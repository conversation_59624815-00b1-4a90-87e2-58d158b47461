# Redis监听器配置指南

## 概述

本文档说明了为规则引擎Redis监听器配置`RedisConnectionFactory`的完整方案，确保Spring Redis监听器能够正常工作。

## 配置架构

### 1. 配置层次结构

```
Apollo配置中心 (ad.user-event.redis)
    ↓
RedisConnectionConfig.java (创建RedisConnectionFactory)
    ↓
RedisListenerConfig.java (使用RedisConnectionFactory)
    ↓
Spring Redis监听器 (rule:update:*, parameter:update:*)
```

### 2. 配置文件

#### 2.1 RedisConnectionConfig.java
**位置**: `event-biz/src/main/java/com/coohua/user/event/biz/rule/config/RedisConnectionConfig.java`

**功能**:
- 读取Apollo配置中心的Redis集群配置
- 创建专门的`RedisConnectionFactory` Bean
- 复用现有的`user-event`命名空间配置

**关键特性**:
```java
@Bean("ruleEngineRedisConnectionFactory")
public RedisConnectionFactory redisConnectionFactory() {
    // 使用Apollo配置的集群节点和连接池参数
    // 创建JedisConnectionFactory
}
```

#### 2.2 RedisListenerConfig.java (已更新)
**位置**: `event-biz/src/main/java/com/coohua/user/event/biz/rule/config/RedisListenerConfig.java`

**更新内容**:
- 注入专门的`ruleEngineRedisConnectionFactory`
- 使用`@Qualifier`注解指定Bean名称
- 保持监听器配置不变

## Apollo配置要求

### 必需的Apollo配置命名空间

在各个应用的启动类中，确保包含以下Apollo配置：

```java
@EnableApolloConfig(value = {
    "application",
    "ad.user-event.redis",  // 必需：Redis集群配置
    // ... 其他配置
})
```

### Redis配置参数

通过Apollo配置中心的`ad.user-event.redis`命名空间提供以下配置：

```properties
# Redis集群节点地址
app.jedis-cluster.user-event.address=redis-node1:6379,redis-node2:6379,redis-node3:6379

# 连接池配置
app.jedis-cluster.user-event.pool.max-total=300
app.jedis-cluster.user-event.pool.max-idle=200
app.jedis-cluster.user-event.pool.min-idle=50
app.jedis-cluster.user-event.pool.max-wait-millis=2000
app.jedis-cluster.user-event.pool.test-on-borrow=false
app.jedis-cluster.user-event.pool.test-on-return=false
app.jedis-cluster.user-event.pool.test-while-idle=true
app.jedis-cluster.user-event.pool.test-on-create=false
```

## 应用配置

### 各模块的Apollo配置

#### event-admin
```java
@EnableApolloConfig(value = {
    "application",
    "ad.user-event.redis",  // ✅ 已配置
    "ap.redis.cluster",
    // ... 其他配置
})
```

#### event-api
```java
@EnableApolloConfig(value = {
    "application", 
    "ad.user-event.redis",  // ✅ 已配置
    "ap.redis.cluster",
    // ... 其他配置
})
```

#### event-risk
```java
@EnableApolloConfig(value = {
    "application",
    "ad.user-event.redis",  // ✅ 已配置
    // ... 其他配置
})
```

## 配置验证

### 1. 启动时验证

应用启动时，检查以下日志：

```
INFO  - 开始创建规则引擎Redis连接工厂
INFO  - Redis集群节点: [redis-node1:6379, redis-node2:6379, redis-node3:6379]
INFO  - Redis连接池配置: maxTotal=300, maxIdle=200, minIdle=50
INFO  - 规则引擎Redis连接工厂创建成功
```

### 2. 监听器验证

发布测试消息验证监听器：

```java
// 使用JedisClusterClient发布测试消息
jedisClusterClient.getResource("test").publish("rule:update:test", "test-rule-id");
jedisClusterClient.getResource("test").publish("parameter:update:test", "test-parameter");
```

预期日志：
```
INFO  - 收到规则更新事件，频道: rule:update:test, 规则ID: test-rule-id
INFO  - 收到参数更新事件，频道: parameter:update:test, 参数名: test-parameter
```

### 3. 连接验证

检查Redis连接是否正常：

```bash
# 检查Redis集群状态
redis-cli -c -h redis-node1 -p 6379 cluster nodes

# 检查应用连接数
redis-cli -c -h redis-node1 -p 6379 info clients
```

## 故障排除

### 常见问题

#### 1. RedisConnectionFactory Bean未找到
**错误**: `No qualifying bean of type 'RedisConnectionFactory'`

**解决方案**:
- 确保`RedisConnectionConfig`类被Spring扫描到
- 检查`@ConditionalOnProperty(name = "rule.engine.enabled")`条件是否满足
- 验证Apollo配置是否正确加载

#### 2. Redis连接失败
**错误**: `Could not get a resource from the pool`

**解决方案**:
- 检查Redis集群节点地址是否正确
- 验证网络连通性
- 检查连接池配置是否合理

#### 3. 监听器不工作
**错误**: 发布消息后没有收到监听事件

**解决方案**:
- 检查频道名称是否匹配（`rule:update:*`, `parameter:update:*`）
- 验证监听器Bean是否正确注册
- 检查Redis连接是否正常

### 调试配置

在`application.properties`中添加调试配置：

```properties
# 启用Redis调试日志
logging.level.org.springframework.data.redis=DEBUG
logging.level.redis.clients.jedis=DEBUG
logging.level.com.coohua.user.event.biz.rule=DEBUG

# 启用规则引擎
rule.engine.enabled=true
```

## 性能考虑

### 连接池配置建议

```properties
# 生产环境建议配置
app.jedis-cluster.user-event.pool.max-total=500
app.jedis-cluster.user-event.pool.max-idle=300
app.jedis-cluster.user-event.pool.min-idle=100
app.jedis-cluster.user-event.pool.max-wait-millis=3000
```

### 监控指标

建议监控以下指标：
- Redis连接池使用率
- 监听器消息处理延迟
- Redis集群节点状态
- 应用内存使用情况

## 总结

通过创建专门的`RedisConnectionConfig`配置类，我们成功地为Spring Redis监听器提供了`RedisConnectionFactory`，同时复用了现有的Apollo配置。这种方案：

1. **复用现有配置**: 使用已有的`ad.user-event.redis`配置
2. **隔离关注点**: 监听器使用Spring Redis，数据操作使用Jedis
3. **配置统一**: 所有Redis相关配置都来自Apollo配置中心
4. **易于维护**: 配置变更只需要在Apollo中修改
