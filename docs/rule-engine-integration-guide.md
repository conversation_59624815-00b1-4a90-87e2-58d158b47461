# 规则引擎整合方案实施指南

## 概述

本文档描述了将 drools-rule-engine 模块整合到现有项目结构中的完整方案。整合后，规则引擎的 RPC 实现将位于 event-api 模块，前端交互功能将位于 event-admin 模块，核心服务将位于 event-biz 模块作为公共包。

## 整合架构

### 模块分布

1. **event-api**: 包含规则引擎的 RPC 实现
   - `UserAdEcpmRemote` 中集成规则引擎调用
   - 提供规则引擎相关的 RPC 接口

2. **event-admin**: 包含规则引擎的前端交互功能
   - `RuleEngineController` 提供规则管理的 REST API
   - 规则配置和管理界面的后端支持

3. **event-biz**: 包含规则引擎的核心服务
   - `RuleEngineService` 核心规则执行服务
   - 规则相关的模型类和服务类
   - Drools 配置类

## 实施步骤

### 1. 数据库准备

执行 `sql/rule_engine_tables.sql` 脚本创建相关表：

```sql
-- 执行以下命令创建表结构
source sql/rule_engine_tables.sql;
```

### 2. 依赖配置

event-biz 模块已添加 Drools 依赖（设置为 optional），无需额外配置。

### 3. 配置文件更新

在 `application.properties` 中添加规则引擎相关配置：

```properties
# 规则引擎配置
rule.engine.enabled=true
rule.engine.cache.enabled=true
rule.engine.cache.ttl=3600
```

### 4. 服务启动

重新启动 event-api 和 event-admin 服务即可。

## 使用方式

### 1. RPC 调用

客户端可以通过现有的 `UserAdEcpmRpc` 接口调用规则引擎：

```java
// 原有方法会自动使用规则引擎（如果可用）
CheckResult result = userAdEcpmRpc.checkOrderBp(appId, userId, orderNo, amount);

// 也可以直接调用规则引擎方法
CheckResult result = userAdEcpmRemote.checkOrderBpByRule(appId, userId, orderNo, amount);
```

### 2. 规则管理

通过 event-admin 提供的 REST API 管理规则：

```bash
# 获取规则列表
GET /rule-engine/rules?appId=1001

# 创建规则
POST /rule-engine/rules
{
  "ruleCode": "new_rule",
  "ruleName": "新规则",
  "description": "规则描述",
  "priority": 10,
  "ruleGroupId": 1,
  "status": 1
}

# 刷新规则引擎
POST /rule-engine/refresh?appId=1001
```

### 3. 规则配置示例

#### 创建规则分组

```json
{
  "groupCode": "withdraw_check_v2",
  "groupName": "提现检查规则组V2",
  "description": "新版本提现审核规则组",
  "appId": 1001,
  "status": 1
}
```

#### 创建规则

```json
{
  "ruleCode": "amount_limit_check",
  "ruleName": "金额限制检查",
  "description": "检查提现金额是否超过限制",
  "priority": 10,
  "ruleGroupId": 1,
  "status": 1,
  "conditions": [
    {
      "fieldName": "totalAmount",
      "operator": "greater_than",
      "fieldValue": "1000",
      "conditionOrder": 1,
      "logicalOperator": "AND"
    }
  ],
  "actions": [
    {
      "actionType": "reject",
      "actionParams": "directRefund=true,reason=提现金额超过限制",
      "actionOrder": 1
    }
  ]
}
```

## 兼容性说明

### 1. 向后兼容

- 原有的 `checkOrderBp` 方法保持不变
- 如果规则引擎不可用，会自动回退到原有逻辑
- 不影响现有客户端的调用

### 2. 渐进式迁移

- 可以通过配置控制是否启用规则引擎
- 支持按应用ID逐步迁移到规则引擎
- 原有逻辑作为兜底保障

### 3. 性能考虑

- 规则引擎使用缓存机制，避免频繁数据库查询
- KieSession 复用，减少创建开销
- 异步日志记录，不影响主流程性能

## 监控和运维

### 1. 日志监控

规则执行日志会记录到 `rule_execution_log` 表，包含：
- 执行时间
- 命中规则
- 执行结果
- 性能指标

### 2. 性能监控

关注以下指标：
- 规则执行时间
- 规则命中率
- 缓存命中率
- 异常率

### 3. 规则热更新

通过调用刷新接口实现规则热更新：

```bash
POST /rule-engine/refresh?appId=1001
```

## 故障排查

### 1. 常见问题

**问题**: 规则引擎不生效
**解决**: 检查 Drools 依赖是否正确加载，查看启动日志

**问题**: 规则执行异常
**解决**: 检查规则语法是否正确，查看 `rule_execution_log` 表

**问题**: 性能问题
**解决**: 检查规则复杂度，优化规则条件，调整缓存配置

### 2. 调试模式

在开发环境可以启用调试模式：

```properties
logging.level.com.coohua.user.event.biz.rule=DEBUG
```

## 扩展功能

### 1. 自定义参数提供者

可以实现自定义的参数提供者，从外部系统获取参数：

```java
@Component
public class CustomParameterProvider implements ParameterProvider {
    @Override
    public Object getParameter(String parameterName, ParameterContext context) {
        // 自定义参数获取逻辑
        return null;
    }
}
```

### 2. 规则模板

支持规则模板功能，快速创建相似规则：

```json
{
  "templateCode": "amount_check_template",
  "templateName": "金额检查模板",
  "parameters": ["maxAmount", "reason"]
}
```

### 3. 规则测试

提供规则测试功能，验证规则逻辑：

```bash
POST /rule-engine/test
{
  "ruleId": 1,
  "testData": {
    "appId": 1001,
    "userId": 12345,
    "amount": 1500
  }
}
```

## 总结

通过本次整合，规则引擎功能已完全集成到现有项目结构中，实现了：

1. **无缝集成**: 不影响现有功能，平滑过渡
2. **模块化设计**: 各模块职责清晰，便于维护
3. **高可用性**: 支持降级和兜底机制
4. **易于扩展**: 支持自定义参数和规则模板
5. **运维友好**: 完善的监控和日志机制

整合完成后，可以逐步将现有的硬编码规则迁移到规则引擎中，实现业务规则的可视化配置和热更新。
