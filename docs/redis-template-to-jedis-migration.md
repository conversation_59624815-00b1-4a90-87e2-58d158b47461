# Redis Template 到 Jedis 迁移文档

## 概述

本文档记录了将规则引擎中基于 `RedisTemplate` 和 `StringRedisTemplate` 的实现改为使用项目统一的 `JedisClusterClient` 的迁移过程。

## 迁移原因

1. **统一技术栈**: 项目中其他模块都使用 `JedisClusterClient`，保持技术栈一致性
2. **性能优化**: Jedis 在某些场景下性能更好，特别是批量操作
3. **减少依赖**: 避免同时依赖 Spring Redis 和 Jedis 两套 Redis 客户端

## 修改的文件

### 1. RedisParameterService.java

**位置**: `event-biz/src/main/java/com/coohua/user/event/biz/rule/service/RedisParameterService.java`

**主要修改**:
- 移除 `RedisTemplate` 和 `StringRedisTemplate` 依赖
- 添加 `@JedisClusterClientRefer(namespace = "user-event")` 注解注入 `JedisClusterClient`
- 修改 `getParameterValue()` 方法中的 Redis 操作:
  - `stringRedisTemplate.opsForValue().get()` → `jedis.get()`
  - `stringRedisTemplate.opsForHash().get()` → `jedis.hget()`
  - `stringRedisTemplate.hasKey()` → `jedis.exists()`
  - `stringRedisTemplate.opsForSet().isMember()` → `jedis.sismember()`
- 修改 `publishParameterUpdateEvent()` 方法:
  - `redisTemplate.convertAndSend()` → `jedis.publish()`

### 2. RuleEngineService.java

**位置**: `event-biz/src/main/java/com/coohua/user/event/biz/rule/service/RuleEngineService.java`

**主要修改**:
- 移除 `RedisTemplate` 依赖
- 添加 `@JedisClusterClientRefer(namespace = "user-event")` 注解注入 `JedisClusterClient`
- 添加相关 import 语句

### 3. RuleService.java

**位置**: `event-biz/src/main/java/com/coohua/user/event/biz/rule/service/RuleService.java`

**主要修改**:
- 移除 `RedisTemplate` 依赖
- 添加 `@JedisClusterClientRefer(namespace = "user-event")` 注解注入 `JedisClusterClient`
- 修改 `publishRuleUpdateEvent()` 方法:
  - `redisTemplate.convertAndSend()` → `jedis.publish()`
- 添加异常处理，避免 Redis 发布失败影响主业务流程

## 技术细节

### Jedis 使用模式

```java
// 获取 Jedis 连接并自动释放
try (Jedis jedis = jedisClusterClient.getResource()) {
    // 执行 Redis 操作
    String value = jedis.get(key);
    jedis.publish(channel, message);
}
```

### 支持的 Redis 命令

| 原 RedisTemplate 操作 | Jedis 操作 | 说明 |
|---------------------|-----------|------|
| `opsForValue().get(key)` | `jedis.get(key)` | 获取字符串值 |
| `opsForHash().get(key, field)` | `jedis.hget(key, field)` | 获取哈希字段值 |
| `hasKey(key)` | `jedis.exists(key)` | 检查键是否存在 |
| `opsForSet().isMember(key, member)` | `jedis.sismember(key, member)` | 检查集合成员 |
| `convertAndSend(channel, message)` | `jedis.publish(channel, message)` | 发布消息 |

### 命名空间配置

使用 `@JedisClusterClientRefer(namespace = "user-event")` 注解，确保使用正确的 Redis 集群配置。

## 保留的组件

### Redis 监听器配置

**文件**: `event-biz/src/main/java/com/coohua/user/event/biz/rule/config/RedisListenerConfig.java`

**保留原因**:
- Spring Redis 的监听器配置已经很成熟且稳定
- 监听器部分不需要频繁操作，性能影响较小
- 避免复杂的 Jedis 订阅实现

### 缓存配置

**文件**: `event-biz/src/main/java/com/coohua/user/event/biz/rule/config/RuleCacheConfig.java`

**保留原因**:
- Spring Cache 抽象层提供了良好的缓存管理
- 与具体的 Redis 客户端实现解耦
- 支持多种缓存后端

## 验证要点

1. **功能验证**:
   - 参数配置的读取是否正常
   - 规则更新事件发布是否成功
   - 参数更新事件发布是否成功

2. **性能验证**:
   - Redis 操作响应时间
   - 连接池使用情况
   - 内存使用情况

3. **异常处理**:
   - Redis 连接失败时的降级处理
   - 发布事件失败时不影响主业务

## 注意事项

1. **连接管理**: 使用 try-with-resources 确保 Jedis 连接正确释放
2. **异常处理**: Redis 操作失败不应影响主业务流程
3. **命名空间**: 确保使用正确的 Redis 集群配置
4. **兼容性**: 保持与现有业务逻辑的兼容性

## 依赖问题解决

### 问题描述
在迁移过程中发现 `event-biz` 模块缺少 Spring Redis 相关依赖，导致监听器和缓存配置类编译错误。

### 解决方案
在 `event-biz/pom.xml` 中添加 Spring Redis 依赖：

```xml
<!-- Spring Redis依赖，用于监听器和缓存配置 -->
<dependency>
    <groupId>org.springframework.boot</groupId>
    <artifactId>spring-boot-starter-data-redis</artifactId>
</dependency>
```

### 依赖传递
由于其他模块（event-admin、event-api、event-job、event-risk）都依赖 `event-biz` 模块，它们会自动获得这个 Redis 依赖，无需单独添加。

## 后续优化建议

1. **批量操作**: 对于需要批量操作的场景，可以使用 Jedis Pipeline 提升性能
2. **监控指标**: 添加 Redis 操作的监控指标
3. **配置外化**: 将 Redis 命名空间等配置外化到配置文件
4. **错误重试**: 对于关键的 Redis 操作添加重试机制
