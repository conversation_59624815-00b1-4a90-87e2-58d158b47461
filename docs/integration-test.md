# 规则引擎整合测试指南

## 测试环境准备

### 1. 数据库准备

```sql
-- 执行建表脚本
source sql/rule_engine_tables.sql;

-- 验证表是否创建成功
SHOW TABLES LIKE 'rule_%';
```

### 2. 应用启动

启动 event-api 和 event-admin 服务，观察启动日志：

```bash
# 启动 event-api
cd event-api
mvn spring-boot:run

# 启动 event-admin  
cd event-admin
mvn spring-boot:run
```

## 功能测试

### 1. 基础功能测试

#### 测试原有接口是否正常

```bash
# 测试原有的 checkOrderBp 接口
curl -X POST "http://localhost:8080/use-event/test/checkOrderBp" \
  -H "Content-Type: application/json" \
  -d '{
    "appId": 1001,
    "userId": 12345,
    "orderNo": "TEST001",
    "amount": 100
  }'
```

预期结果：接口正常返回，不受规则引擎影响

#### 测试规则引擎接口

```bash
# 测试规则引擎专用接口
curl -X POST "http://localhost:8080/use-event/test/checkOrderBpByRule" \
  -H "Content-Type: application/json" \
  -d '{
    "appId": 1001,
    "userId": 12345,
    "orderNo": "TEST001",
    "amount": 100
  }'
```

预期结果：如果没有配置规则，应该返回通过

### 2. 规则管理测试

#### 获取规则列表

```bash
curl "http://localhost:8081/rule-engine/rules?appId=1001"
```

#### 创建规则分组

```bash
curl -X POST "http://localhost:8081/rule-engine/rule-groups" \
  -H "Content-Type: application/json" \
  -d '{
    "groupCode": "test_group",
    "groupName": "测试规则组",
    "description": "用于测试的规则组",
    "appId": 1001,
    "status": 1
  }'
```

#### 创建测试规则

```bash
curl -X POST "http://localhost:8081/rule-engine/rules" \
  -H "Content-Type: application/json" \
  -d '{
    "ruleCode": "test_amount_limit",
    "ruleName": "测试金额限制",
    "description": "测试用的金额限制规则",
    "priority": 10,
    "ruleGroupId": 1,
    "status": 1,
    "conditions": [
      {
        "fieldName": "amount",
        "operator": "greater_than",
        "fieldValue": "500",
        "conditionOrder": 1,
        "logicalOperator": "AND"
      }
    ],
    "actions": [
      {
        "actionType": "reject",
        "actionParams": "directRefund=true,reason=测试金额超过限制",
        "actionOrder": 1
      }
    ]
  }'
```

#### 刷新规则引擎

```bash
curl -X POST "http://localhost:8081/rule-engine/refresh?appId=1001"
```

### 3. 规则执行测试

#### 测试规则生效

```bash
# 测试金额超过限制的情况
curl -X POST "http://localhost:8080/use-event/test/checkOrderBpByRule" \
  -H "Content-Type: application/json" \
  -d '{
    "appId": 1001,
    "userId": 12345,
    "orderNo": "TEST002",
    "amount": 600
  }'
```

预期结果：应该被规则拒绝

```bash
# 测试金额未超过限制的情况
curl -X POST "http://localhost:8080/use-event/test/checkOrderBpByRule" \
  -H "Content-Type: application/json" \
  -d '{
    "appId": 1001,
    "userId": 12345,
    "orderNo": "TEST003",
    "amount": 300
  }'
```

预期结果：应该通过检查

## 性能测试

### 1. 并发测试

使用 JMeter 或 ab 工具进行并发测试：

```bash
# 使用 ab 工具测试
ab -n 1000 -c 10 -p test_data.json -T application/json \
  http://localhost:8080/use-event/test/checkOrderBp
```

### 2. 性能监控

观察以下指标：
- 响应时间
- 内存使用
- CPU 使用率
- 数据库连接数

## 故障测试

### 1. 数据库故障

停止数据库服务，测试应用是否能正常降级：

```bash
# 停止数据库
sudo service mysql stop

# 测试接口
curl -X POST "http://localhost:8080/use-event/test/checkOrderBp" \
  -H "Content-Type: application/json" \
  -d '{
    "appId": 1001,
    "userId": 12345,
    "orderNo": "TEST004",
    "amount": 100
  }'
```

预期结果：应用不崩溃，接口正常返回

### 2. 规则引擎异常

创建错误的规则，测试异常处理：

```bash
curl -X POST "http://localhost:8081/rule-engine/rules" \
  -H "Content-Type: application/json" \
  -d '{
    "ruleCode": "error_rule",
    "ruleName": "错误规则",
    "description": "用于测试异常处理的规则",
    "priority": 5,
    "ruleGroupId": 1,
    "status": 1,
    "conditions": [
      {
        "fieldName": "invalidField",
        "operator": "invalid_operator",
        "fieldValue": "invalid_value",
        "conditionOrder": 1
      }
    ]
  }'
```

## 日志检查

### 1. 应用日志

检查应用启动日志，确认规则引擎组件正常加载：

```bash
tail -f logs/application.log | grep -i "rule\|drools"
```

### 2. 规则执行日志

检查规则执行日志：

```sql
SELECT * FROM rule_execution_log ORDER BY create_time DESC LIMIT 10;
```

### 3. 错误日志

检查是否有异常日志：

```bash
tail -f logs/application.log | grep -i "error\|exception"
```

## 验收标准

### 1. 功能验收

- [ ] 原有接口功能不受影响
- [ ] 规则引擎接口正常工作
- [ ] 规则管理功能正常
- [ ] 规则执行结果正确
- [ ] 异常情况下能正常降级

### 2. 性能验收

- [ ] 接口响应时间增加不超过 50ms
- [ ] 内存使用增加不超过 100MB
- [ ] 并发处理能力不下降

### 3. 稳定性验收

- [ ] 连续运行 24 小时无异常
- [ ] 数据库故障时能正常降级
- [ ] 规则引擎异常时不影响主流程

## 回滚方案

如果测试发现问题，可以通过以下方式回滚：

### 1. 代码回滚

```bash
# 注释掉规则引擎相关代码
# 重新部署应用
```

### 2. 配置回滚

```properties
# 在配置文件中禁用规则引擎
rule.engine.enabled=false
```

### 3. 数据库回滚

```sql
-- 如果需要，可以删除规则引擎相关表
DROP TABLE IF EXISTS rule_execution_log;
DROP TABLE IF EXISTS rule_parameter_redis;
DROP TABLE IF EXISTS rule_action;
DROP TABLE IF EXISTS rule_condition;
DROP TABLE IF EXISTS rule_definition;
DROP TABLE IF EXISTS rule_group;
```

## 总结

通过以上测试步骤，可以全面验证规则引擎整合的正确性和稳定性。测试过程中发现的问题应及时记录和修复，确保整合方案的成功实施。
