package com.coohua.user.event.biz.ap.service;

import com.coohua.user.event.biz.ap.entity.ClickConfig;
import com.coohua.user.event.biz.ap.mapper.ClickConfigMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;

/**
 * <AUTHOR>
 * @since 2021/9/3
 */
@Slf4j
@Service
public class ClickConfigService {

    @Resource
    private ClickConfigMapper clickConfigMapper;

    public Map<Integer,ClickConfig> queryConfigRange(Integer appId){
        if (appId == null){
            appId = -1;
        }
        List<ClickConfig> clickConfigs = clickConfigMapper.queryAllConfig(appId);

        Map<Integer,ClickConfig> resultMap = new HashMap<>();
        clickConfigs.sort(Comparator.comparing(ClickConfig::getExposure));
        clickConfigs.forEach(clickConfig -> {
            resultMap.put(clickConfig.getExposure(),clickConfig);
        });
        return resultMap;
    }

    public Map<Integer,Integer> convertExposureQueryRange(Map<Integer,ClickConfig> configMap){
        Map<Integer,Integer> resultMap = new HashMap<>();
        List<Integer> key1List = new ArrayList<>(configMap.keySet());
        List<Integer> key2List = new ArrayList<>(configMap.keySet());
        for (int i = 0; i < key1List.size();i++){
            if (i > 0) {
                resultMap.put(key1List.get(i), key2List.get(i - 1));
            }else {
                resultMap.put(key1List.get(i), Integer.MIN_VALUE);
            }
        }
        return resultMap;
    }

}
