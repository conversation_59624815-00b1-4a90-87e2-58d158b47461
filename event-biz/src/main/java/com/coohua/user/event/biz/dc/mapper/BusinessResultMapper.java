package com.coohua.user.event.biz.dc.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.coohua.user.event.biz.dc.entity.BusinessResult;
import org.apache.ibatis.annotations.Delete;
import org.apache.ibatis.annotations.Insert;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 *  business_result
 * <AUTHOR> 2020-09-08
 */
@Repository
public interface BusinessResultMapper extends BaseMapper<BusinessResult> {

    @Insert({
            "<script>",
            "INSERT INTO test.business_result_temp (" +
                    "group_name," +
                    "product," +
                    "os," +
                    "ad_slot," +
                    "ad_type," +
                    "ad_source," +
                    "exposure," +
                    "request," +
                    "income," +
                    "constrast," +
                    "zk_people," +
                    "ad_position," +
                    "channel," +
                    "channel_dau," +
                    "logday," +
                    "dau," +
                    "date2," +
                    "click" +
                    ")" +
                    " VALUES " +
                    "<foreach collection='businessResultList'  item='businessResult' separator=','>" +
                    "(" +
                    "#{businessResult.groupName}," +
                    "#{businessResult.product}," +
                    "#{businessResult.os}," +
                    "#{businessResult.adSlot}," +
                    "#{businessResult.adType}," +
                    "#{businessResult.adSource}," +
                    "#{businessResult.exposure}," +
                    "#{businessResult.request}," +
                    "#{businessResult.income}," +
                    "#{businessResult.constrast}," +
                    "#{businessResult.zkPeople}," +
                    "#{businessResult.adPosition}," +
                    "#{businessResult.channel}," +
                    "#{businessResult.channelDau}," +
                    "#{businessResult.logday}," +
                    "#{businessResult.dau}," +
                    "#{businessResult.date2}," +
                    "#{businessResult.click}"+
                    ")",
            "</foreach>",
            "</script>",
    })
    int insertBatch(@Param("businessResultList") List<BusinessResult> businessResultList);

    @Delete({"delete from test.business_result_temp where date2 = #{logDay}"})
    int delByLogDay(@Param("logDay") String logDay);
}
