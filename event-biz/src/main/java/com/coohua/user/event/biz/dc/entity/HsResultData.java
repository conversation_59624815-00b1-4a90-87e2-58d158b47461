package com.coohua.user.event.biz.dc.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import java.util.Date;
import com.baomidou.mybatisplus.annotation.TableId;
import java.io.Serializable;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR>
 * @since 2023-06-28
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value="HsResultData对象", description="")
public class HsResultData implements Serializable {

    private static final long serialVersionUID = 1L;

    private Date logday;

    private String partner;

    private String platformName;

    private Double cost;

    private Double adRevenue;

    private Double withdrawAmount;


}
