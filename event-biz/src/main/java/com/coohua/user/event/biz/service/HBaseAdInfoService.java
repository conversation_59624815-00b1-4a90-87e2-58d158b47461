package com.coohua.user.event.biz.service;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.coohua.user.event.biz.click.entity.VideoAdReportEntitiy;
import com.coohua.user.event.biz.enums.AdPosType;
import com.coohua.user.event.biz.service.bean.UserAdViewBean;
import com.coohua.user.event.biz.service.rsp.BatchQueryResponse;
import com.coohua.user.event.biz.util.HBaseUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.hadoop.hbase.TableName;
import org.apache.hadoop.hbase.TableNotFoundException;
import org.apache.hadoop.hbase.client.*;
import org.apache.hadoop.hbase.io.compress.Compression;
import org.apache.hadoop.hbase.io.encoding.DataBlockEncoding;
import org.apache.hadoop.hbase.util.Bytes;
import org.apache.logging.log4j.util.Strings;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.io.IOException;
import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.IntStream;

/**
 * <AUTHOR>
 * @since 2022/5/9
 */
@Slf4j
@Service
public class HBaseAdInfoService {

    @Resource(name = "linDormConnection")
    private Connection HBaseConnection;

    private static final String USER_OWN_VIEW = "user_own_view";
    private static final String USER_THIRD_VIEW = "user_third_view";
    private static final String QUERY_ID = "user_id";
    private static final Long DEFAULT = 0L;

    private final static  byte[] families = Bytes.toBytes("family");

    public void createTable() throws IOException {
        try (Admin admin = HBaseConnection.getAdmin()) {
            // 建表
            try {
                admin.getDescriptor(TableName.valueOf(USER_OWN_VIEW));
                admin.getDescriptor(TableName.valueOf(USER_THIRD_VIEW));
            } catch (TableNotFoundException te) {
                ColumnFamilyDescriptor familyDescriptor = ColumnFamilyDescriptorBuilder
                        .newBuilder(families)
                        .setCompressionType(Compression.Algorithm.ZSTD)
                        .setDataBlockEncoding(DataBlockEncoding.DIFF)
                        .setTimeToLive(60 * 60 *24 * 30)
                        .build();

                TableDescriptor tableDescriptor = TableDescriptorBuilder.newBuilder(TableName.valueOf(USER_OWN_VIEW))
                        .setColumnFamily(familyDescriptor)
                        .setCompactionEnabled(true)
                        .build();
                TableDescriptor tableDescriptors = TableDescriptorBuilder.newBuilder(TableName.valueOf(USER_THIRD_VIEW))
                        .setColumnFamily(familyDescriptor)
                        .setCompactionEnabled(true)
                        .build();

                admin.createTable(tableDescriptor, HBaseUtils.ONLY_SPLIT_KEYS);
                admin.createTable(tableDescriptors, HBaseUtils.ONLY_SPLIT_KEYS);
            }
        } catch (IOException e) {
            log.error("{} {} 表创建失败:{}", USER_OWN_VIEW,USER_THIRD_VIEW, e);
            throw e;
        }
    }


    private String buildUserKey(Integer appId,String userId){
        return String.format("%s:%s",appId,userId);
    }

    private Map<String,List<VideoAdReportEntitiy>> buildVideoMap(List<String> batchList){
        return batchList.parallelStream()
                .map(s -> JSON.parseObject(s,VideoAdReportEntitiy.class))
                .filter(r -> AdPosType.Rewarded_video.getCode().equals(r.getAdType()))
                .collect(Collectors.groupingBy(
                        videoAdReportEntitiy -> this.buildUserKey(
                                videoAdReportEntitiy.getAppId(),
                                videoAdReportEntitiy.getUserId()
                        )
                ));
    }

    // 我方数据记录
    public void saveUserExposureOwn(List<String> batchList){
        Map<String,UserAdViewBean> userAdViewBeanMap = buildUserAdViewMap(batchList);
        insertOrUpdate(userAdViewBeanMap,USER_OWN_VIEW);
    }

    // 三方数据录入
    public void saveUserRewardThird(List<String> batchList){
        Map<String,UserAdViewBean> userAdViewBeanMap = buildUserAdViewMap(batchList);
        insertOrUpdate(userAdViewBeanMap,USER_THIRD_VIEW);
    }

    public boolean checkFilterCount(Long appId,Long userId){
        return false;
    }

    private Map<String,UserAdViewBean> buildUserAdViewMap(List<String> batchList){
        Map<String,List<VideoAdReportEntitiy>> videoMap = buildVideoMap(batchList);
        if (videoMap == null || videoMap.isEmpty()){
            return new HashMap<>();
        }
        Map<String,UserAdViewBean> userAdViewMap = new HashMap<>();
        for (String queryKey : videoMap.keySet()){
            UserAdViewBean userAdViewBean = new UserAdViewBean();
            userAdViewBean.setQueryKey(queryKey);
            List<VideoAdReportEntitiy> videoAdReportEntities = videoMap.get(queryKey);
            VideoAdReportEntitiy videoAdReportEntitiy = videoAdReportEntities.get(0);
            Map<Long,Long> rsMap = videoAdReportEntities
                    .stream()
                    .filter(r -> Objects.nonNull(r) && Strings.isNotEmpty(r.getAd_id()))
                    .collect(Collectors.groupingBy(r -> Long.valueOf(r.getAd_id()),
                            Collectors.counting()));
            userAdViewBean.setAdWallCount(rsMap);
            userAdViewBean.setUserId(videoAdReportEntitiy.getUserId());
            userAdViewBean.setArpu(videoAdReportEntities.stream()
                    .flatMapToInt(r -> {
                        if (Strings.isNotBlank(r.getPrice())) {
                            return IntStream.of(Integer.parseInt(r.getPrice()));
                        }
                        return IntStream.of(0);
                    }).sum());
            userAdViewMap.put(queryKey,userAdViewBean);
        }
        return userAdViewMap;
    }

    private Map<Long,Long> buildEmptyAdFilterMap(){
        return new HashMap<Long,Long>(){{ put(DEFAULT,DEFAULT); }};
    }

    private Map<Long,Long> addAdFilterMap(Map<Long,Long> sourceMap,Map<Long,Long> targetMap){
        if (sourceMap == null){
            sourceMap = buildEmptyAdFilterMap();
        }
        if (targetMap == null){
            targetMap = buildEmptyAdFilterMap();
        }
        Set<Long> adSet = new HashSet<>(sourceMap.keySet());
        adSet.addAll(targetMap.keySet());
        Map<Long,Long> rsMap = new HashMap<>();
        for (Long adId : adSet){
            rsMap.put(adId, sourceMap.getOrDefault(adId,DEFAULT) + targetMap.getOrDefault(adId, DEFAULT));
        }
        return rsMap;
    }

    private void insertOrUpdate(Map<String, UserAdViewBean> userAdViewBeanMap,String tableName){
        try (Table table = HBaseConnection.getTable(TableName.valueOf(tableName))) {
            Map<String,UserAdViewBean> responseMap = queryResponseMap(userAdViewBeanMap,tableName);
            List<Put> puts = new ArrayList<>();
            userAdViewBeanMap.forEach((key,value) ->{
                UserAdViewBean userAdViewBean = responseMap.getOrDefault(key,new UserAdViewBean(){{
                    setArpu(0);
                    setQueryKey(value.getQueryKey());
                    setUserId(value.getUserId());
                    setAdWallCount(buildEmptyAdFilterMap());
                }});

                UserAdViewBean saveBatchUserAdViewBean = new UserAdViewBean();
                saveBatchUserAdViewBean.setQueryKey(value.getQueryKey());
                saveBatchUserAdViewBean.setUserId(value.getUserId());
                saveBatchUserAdViewBean.setArpu(value.getArpu() + userAdViewBean.getArpu());
                saveBatchUserAdViewBean.setAdWallCount(addAdFilterMap(value.getAdWallCount(),userAdViewBean.getAdWallCount()));

                Put put = new Put(Bytes.toBytes(key));
                put.addColumn(families, Bytes.toBytes(QUERY_ID), Bytes.toBytes(JSONObject.toJSONString(saveBatchUserAdViewBean)));
                puts.add(put);
            });
            table.put(puts);
        } catch (IOException e) {
            log.error("QueryException:",e);
        }
    }

    private Map<String,UserAdViewBean> queryResponseMap(Map<String,UserAdViewBean> userAdViewBeanMap,String tableName){
        try (Table table = HBaseConnection.getTable(TableName.valueOf(tableName))) {
            List<Get> gets = userAdViewBeanMap.keySet()
                    .parallelStream()
                    .map(str->{
                        Get get = new Get(Bytes.toBytes(str));
                        get.addColumn(families, Bytes.toBytes(QUERY_ID));
                        return get;
                    }).collect(Collectors.toList());

            Result[] results = table.get(gets);

            Map<String,UserAdViewBean> responseMap = new HashMap<>();
            for(Result result : results){
                String res = HBaseUtils.getCellValStr(result,QUERY_ID);
                if (StringUtils.isNotEmpty(res)){
                    UserAdViewBean batchQueryResponse = JSON.parseObject(res,UserAdViewBean.class);
                    responseMap.put(batchQueryResponse.getQueryKey(),batchQueryResponse);
                }
            }
            return responseMap;
        } catch (IOException e) {
            log.error("QueryException:",e);
        }
        return new HashMap<>();
    }
}
