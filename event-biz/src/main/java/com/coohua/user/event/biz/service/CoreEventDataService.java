package com.coohua.user.event.biz.service;

import com.coohua.user.event.biz.click.entity.ProductEntity;
import com.coohua.user.event.biz.click.service.ClickHouseService;
import com.coohua.user.event.biz.dc.service.CoreEventResultService;
import com.coohua.user.event.biz.toufang.entity.CoreEventEntity;
import com.coohua.user.event.biz.toufang.entity.UserEventModelEntity;
import com.coohua.user.event.biz.toufang.service.CoreEventService;
import com.coohua.user.event.biz.util.DateUtil;
import com.coohua.user.event.biz.util.Strings;
import lombok.extern.slf4j.Slf4j;
import org.checkerframework.checker.units.qual.A;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2020/9/19
 */
@Slf4j
@Service
public class CoreEventDataService {

    @Autowired
    private CoreEventResultService coreEventResultService;
    @Autowired
    private CoreEventService coreEventService;
    @Autowired
    private ClickHouseService clickHouseService;

    public void doCoreEventProcess(String date){
        List<ProductEntity> productEntityList = clickHouseService.queryAllProduct();
        Map<String,ProductEntity> productEntityMap = productEntityList.parallelStream().collect(Collectors.toMap(ProductEntity::getProduct,pr->pr,(p1,p2)->p1));
        // 从今日起，计算更新前十天的数据..
        if (Strings.noEmpty(date)){
            log.info("[用户核心行为]手动输入参数..只更新{}数据...",date);
            doMain(date,productEntityMap);
            log.info("处理{}的[用户核心行为]数据完成!",date);
            return;
        }

        log.info("开始[用户核心行为]计算....");
        Date now = new Date();
        doMain(DateUtil.dateToString(now),productEntityMap);
        log.info("结束[用户核心行为]计算");
    }


    private void doMain(String logDay,Map<String,ProductEntity> productEntityMap){
        List<CoreEventEntity>  coreEventEntityList = coreEventService.queryCoreEvent(logDay);

        if (coreEventEntityList == null || coreEventEntityList.size() == 0){
            log.error("未查询到当日的数据....");
            return;
        }

        int delcount = coreEventResultService.deleteResult(logDay);
        log.info("成功DEL {} 条 用户核心行为数据...",delcount);

        Date begin = DateUtil.stringToDate(logDay + " 00:00:00",DateUtil.DATETIME_PATTERN);
        Date end = DateUtil.dateIncreaseByDay(begin,1);
        List<UserEventModelEntity> userEventModelEntityList = coreEventService.queryUserEvent(begin,end);

        Map<String,UserEventModelEntity> userEventModelEntityMap = userEventModelEntityList.parallelStream()
                .collect(Collectors.toMap(ue-> ue.getProduct() + ue.getDsp() + ue.getOs(), ue -> ue,(u1,s1) -> u1));

        if (coreEventEntityList.size() > 0){
            int count = coreEventResultService.insertBatch(coreEventEntityList,productEntityMap,userEventModelEntityMap);
            log.info("成功插入 [用户核心行为] {}条...",count);
        }
    }
}
