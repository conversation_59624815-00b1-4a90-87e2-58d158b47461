package com.coohua.user.event.biz.enums;

import com.google.common.base.Joiner;
import com.google.common.base.Splitter;

import java.util.List;
import java.util.Map;

/**
 * (。≖ˇェˇ≖。)
 *
 * @author: zkk DateTime: 2020/8/4 15:13
 */
public class CacheKeyConstants {

    public static final String PKG_USER_SPLIT = "_";

    public static final Joiner JOINER_LINE = Joiner.on(PKG_USER_SPLIT).skipNulls();

    public static final Splitter SPLITTER_LINE = Splitter.on(PKG_USER_SPLIT);

    public static final Long DEFAULT_LONG_EMPTY = -1L;

    /**
     * report lock 防止重复 report 导致的监控数据异常
     */
    private final static String CLICK_REPORT_KEY = "dispense:ocpc:click:%s:%s";
    /**
     * 缓存激活上报
     * @param pkgId
     * @param deviceIdMd5   设备ID Md5值
     * @return
     */
    public final static String getClickReportKey(String pkgId, String deviceIdMd5){
        return String.format(CLICK_REPORT_KEY, pkgId, deviceIdMd5);
    }

    public static void main(String[] args) {
        String join = JOINER_LINE.join("123", "0000");
        System.out.println("join = " + join);
        Iterable<String> split = SPLITTER_LINE.split(join);
        List<String> strings = SPLITTER_LINE.splitToList(join);
        System.out.println("strings = " + strings);
        Map<String, String> split1 = Splitter.on("&").withKeyValueSeparator("=").split("id=123&name[0]=green&name[1]=green");
        split1.forEach((k,v)-> System.out.println( k + "="+v));
    }
}
