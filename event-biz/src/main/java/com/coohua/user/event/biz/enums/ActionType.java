package com.coohua.user.event.biz.enums;

/**
 * <AUTHOR>
 * @since 2022/1/25
 */
public enum ActionType {
    GRAY_BLACK(0,"拉黑"),
    GRAY(1,"拉灰"),
    LIMIT_ECPM(2,"控制输出"),
    GRAY_BLACK_PART(3,"单产品拉黑")
    ;
    public Integer type;
    public String desc;

    ActionType(Integer type, String desc) {
        this.type = type;
        this.desc = desc;
    }

    public Integer getType() {
        return type;
    }

    public void setType(Integer type) {
        this.type = type;
    }

    public String getDesc() {
        return desc;
    }

    public void setDesc(String desc) {
        this.desc = desc;
    }
}
