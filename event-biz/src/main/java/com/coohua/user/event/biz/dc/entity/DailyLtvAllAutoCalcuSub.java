package com.coohua.user.event.biz.dc.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import java.util.Date;
import com.baomidou.mybatisplus.annotation.TableId;
import java.io.Serializable;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR>
 * @since 2021-08-24
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value="DailyLtvAllAutoCalcuSub对象", description="")
public class DailyLtvAllAutoCalcuSub implements Serializable {

    private static final long serialVersionUID = 1L;

    private Date logday;

    private String productGroup;

    private String productName;

    private String product;

    private String os;

    private String period;

    private Double lt;

    private Double ltv;

    private Double platformIncome;

    private Double straightIncome;

    private Double cpa;

    private Long toufangDevice;

    private String activateDevice;

    private Double activateRetain1;

    private Double activateRetain2;

    private Double activateRetain3;

    private Double activateRetain4;

    private Double activateRetain5;

    private Double activateRetain6;

    private Double activateRetain7;

    private Double activateRetain14;

    private Double activateRetain24;

    private Double arpuRetain1;

    private Double arpuRetain2;

    private Double arpuRetain3;

    private Double arpuRetain4;

    private Double arpuRetain5;

    private Double arpuRetain6;

    private Double arpuRetain7;

    private Double arpuRetain25;

    private Double arpuRetain45;

    private Double m;

    private Double cpaSub;

    private Double ltvSub;

    private Double withdrawCost;

    private Double withdraw1;

    private Double withdraw2;

    private Double withdraw3;

    private Double withdraw4;

    private Double withdraw5;

    private Double withdraw6;

    private Double withdraw7;

    private Double withdraw25;

    private Double withdraw45;

    private Double ltvOther;


}
