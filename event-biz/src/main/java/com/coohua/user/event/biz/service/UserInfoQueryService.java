package com.coohua.user.event.biz.service;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.coohua.bp.user.remote.dto.CommonHeaderDTO;
import com.coohua.user.event.biz.ap.vo.ExUserRedisVo;
import com.coohua.user.event.biz.click.entity.ProductEntity;
import com.coohua.user.event.biz.click.entity.RelationshipEntity;
import com.coohua.user.event.biz.click.entity.UserArpuEntity;
import com.coohua.user.event.biz.click.entity.VideoAdReportEntitiy;
import com.coohua.user.event.biz.click.service.ClickHouseService;
import com.coohua.user.event.biz.dc.entity.UserGrayIpEntity;
import com.coohua.user.event.biz.dc.mapper.UserGrayIpMapper;
import com.coohua.user.event.biz.enums.GrayType;
import com.coohua.user.event.biz.service.bean.*;
import com.coohua.user.event.biz.service.rsp.BatchQueryResponse;
import com.coohua.user.event.biz.user.entity.UserMeta;
import com.coohua.user.event.biz.user.mapper.BpUserMapper;
import com.coohua.user.event.biz.util.*;
import com.google.common.collect.ImmutableMap;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.apache.hadoop.hbase.TableName;
import org.apache.hadoop.hbase.client.Connection;
import org.apache.hadoop.hbase.client.Put;
import org.apache.hadoop.hbase.client.Table;
import org.apache.hadoop.hbase.util.Bytes;
import org.apache.hadoop.hbase.util.MD5Hash;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import top.zrbcool.pepper.boot.jediscluster.JedisClusterClient;
import top.zrbcool.pepper.boot.jediscluster.JedisClusterClientRefer;

import javax.annotation.Resource;
import java.util.*;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2022/3/8
 */
@Slf4j
@Service
public class UserInfoQueryService {


    @Resource
    private BpUserMapper bpUserMapper;
    @Autowired
    private ClickHouseService clickHouseService;
    @Resource(name = "hbaseConnection")
    private Connection userHBaseConnection;
    @Resource(name = "linDormConnection")
    private Connection ocpcHadoopConnection;
    @Resource(name = "toutiaoClickConnection")
    private Connection toutiaoClickConnection;
    @Resource
    private UserGrayIpMapper userGrayIpMapper;
    @JedisClusterClientRefer(namespace = "ap-cluster")
    private JedisClusterClient jedisClusterClient;

    @Autowired
    private HBaseUserAdViewService hBaseUserAdViewService;

    public List<SearchUserInfoRsp> queryList(Long userId,Integer appId){
        List<SearchUserInfoRsp> searchUserInfoRspList = new ArrayList<>();

        UserMeta userMeta = bpUserMapper.selectByUserIdAndPkgId(userId,appId);
        if (userMeta == null){
            return new ArrayList<>();
        }

        searchUserInfoRspList.add(new SearchUserInfoRsp(){{
            setAppId(appId);
            setChannel(userMeta.getChannel());
            setCreateTime(DateUtil.dateToStringWithTime(new Date(userMeta.getCreateTime())));
            setNickName(userMeta.getNickName());
            setUserId(userMeta.getUserId());
        }});
        return searchUserInfoRspList;
    }

    public UserInfoRsp queryUserInfo(Long userId,Integer appId){
        UserInfoRsp userInfoRsp = new UserInfoRsp();
        UserMeta userMeta = bpUserMapper.selectByUserIdAndPkgId(userId,appId);
        if (userMeta == null){
            return userInfoRsp;
        }
        ProductEntity productEntity = AppConfig.appIdMap.get(appId.longValue());
        if (productEntity == null){
            return userInfoRsp;
        }

        List<DescDto> descDtos = new ArrayList<>();
        descDtos.add(new DescDto(){{setKey("头像");setValue(userMeta.getPhotoUrl());}});
        descDtos.add(new DescDto(){{setKey("UNION_ID");setValue(userMeta.getUnionId());}});
        descDtos.add(new DescDto(){{setKey("昵称");setValue(userMeta.getNickName());}});
        descDtos.add(new DescDto(){{setKey("渠道");setValue(userMeta.getChannel());}});
        descDtos.add(new DescDto(){{setKey("用户ID");setValue(userMeta.getUserId().toString());}});
        descDtos.add(new DescDto(){{setKey("注册时间");setValue(DateUtil.dateToStringWithTime(new Date(userMeta.getCreateTime())));}});
        userInfoRsp.setUserInfoList(descDtos);

        // 师徒关系
        List<RelationshipEntity> relationshipEntities =  clickHouseService.queryRelationship(productEntity.getProduct(),userId);
        Long countActive = relationshipEntities.stream().filter(r-> Integer.valueOf(1).equals(r.getStatus())).count();
        userInfoRsp.setFriendshipActiveCount(countActive.intValue());
        userInfoRsp.setFriendshipCount(relationshipEntities.size());
        userInfoRsp.setFriendshipList(relationshipEntities.stream().map(relationshipEntity -> {
            FriendShipDto friendShipDto = new FriendShipDto();
            friendShipDto.setFriendId(relationshipEntity.getPrenticeId().toString());
            friendShipDto.setCreateTime(DateUtil.dateToString(relationshipEntity.getCreateTime()));
            friendShipDto.setStatus(relationshipEntity.getStatus());
            return friendShipDto;
        }).collect(Collectors.toList()));

        String pkgs = getUserPkgs(userId, appId);
        if (StringUtils.isNotBlank(pkgs)) {
            userInfoRsp.setInstallPkgs(pkgs);
        }

        return userInfoRsp;
    }

    public String getUserPkgs(Long userId,Integer appId) {
        String pkgs = "";
        // 用户安装包 -- 读HBase缓存
        String key = String.format("cache:app:installed:%d:%d",appId,userId);
        byte[] result = HBaseUtils.searchDataFromHadoop(userHBaseConnection,"cache_record",key);
        if (result != null){
             pkgs = new String(result);
        }
        return pkgs;
    }

    @Autowired
    TFUserService tfUserService;

    public boolean checkUserBlackPkgs(Long userId,Long appId,Integer amount,Set<String> blackPkgs,UserActive userActive){
        int appIdInt = Integer.parseInt(String.valueOf(appId));
        String userPkgs = getUserPkgs(userId, appIdInt);

        if (StringUtils.isNotBlank(userPkgs) && CollectionUtils.isNotEmpty(blackPkgs)) {
            String blackP = blackPkgs.stream().filter(blackPkg -> userPkgs.contains(blackPkg)).findFirst().orElse(null);
            if (Objects.nonNull(blackP)) {
                log.info("存在黑名单包名异常 用户 {} {} {} {} {} 提现 {}  记录日志-拒绝审核",appId,userId,blackP,userActive.getChannel(),userActive.getModel(),amount);
                return true;
            }
        }
        return false;
    }
    public UserArpuRsp queryUserArpu(Long userId,Integer appId){
        UserArpuRsp userArpuRsp = new UserArpuRsp();

        // 总和
        BatchQueryResponse response = hBaseUserAdViewService.singleQueryRsp(Long.valueOf(appId),userId);
        userArpuRsp.setSumVideoCount(response.getVideoExposureCount());
        userArpuRsp.setSumEcpmAll(response.getEcpmSum()/1000d);
        List<VideoAdReportEntitiy> videoAdReportEntitiys = clickHouseService.queryVideoDetailList(appId,userId);
        List<UserArpuEntity> userArpuEntities = clickHouseService.queryAvgVideoArpu(appId,userId);

        userArpuRsp.setVideoArpuDtoList(userArpuEntities.stream().map(userArpuEntity -> {
            VideoArpuDto videoArpuDto = new VideoArpuDto();

            videoArpuDto.setLogday(userArpuEntity.getLogday());
            videoArpuDto.setAvgEcpm(userArpuEntity.getAvgEcpm());
            videoArpuDto.setVideoCount(userArpuEntity.getVideoCount());
            return videoArpuDto;
        }).collect(Collectors.toList()));

        userArpuRsp.setDateList(videoAdReportEntitiys.stream()
                .map(VideoAdReportEntitiy::getLogday)
                .collect(Collectors.toList()));
        userArpuRsp.setArpuList(videoAdReportEntitiys.stream()
                .map(r -> Integer.valueOf(r.getPrice()))
                .collect(Collectors.toList()));

        return userArpuRsp;
    }

    /**
     * 查询拉黑信息
     * @param appId 产品id
     * @param userId 用户id
     * @return grayInfo
     */
    public GrayInfoRsp queryGrayInfoRsp(Integer appId,Long userId,Map<String,String> deviceMap){
        ProductEntity productEntity = AppConfig.appIdMap.get(appId.longValue());
        GrayInfoRsp grayInfoRsp = new GrayInfoRsp();
        if (productEntity == null){
            return grayInfoRsp;
        }
        // 设备信息
        List<DeviceDto> deviceDtos = new ArrayList<>();
        // 日志系统补充
        if (deviceMap.size() > 0){
            for (Map.Entry<String, String> entry : deviceMap.entrySet()) {
                String k = entry.getKey();
                String type = "DEVICE_ID";
                if (k.contains("oaid")){
                    type = "OAID";
                }else if (k.contains("androidId")){
                    type = "ANDROID_ID";
                }else if (k.contains("imei")){
                    type = "IMEI";
                }

                DeviceDto deviceDto = new DeviceDto();
                deviceDto.setType(type);
                deviceDto.setId(entry.getValue());
                deviceDto.setSource("BP_SLS_SYSTEM");
                deviceDtos.add(deviceDto);
            }
        }

        if (deviceMap.size() == 0){
            // 若没查到日志中的设备取埋点BackUp
            deviceMap = clickHouseService.queryDeviceIdOnlyThreeDays(productEntity.getProduct(),userId);
            if(deviceMap.size() > 0) {
                for (Map.Entry<String, String> entry : deviceMap.entrySet()) {
                    String k = entry.getKey();
                    String type = "DEVICE_ID";
                    if (k.contains("oaid")) {
                        type = "OAID";
                    } else if (k.contains("androidId")) {
                        type = "ANDROID_ID";
                    } else if (k.contains("imei")) {
                        type = "IMEI";
                    }

                    DeviceDto deviceDto = new DeviceDto();
                    deviceDto.setType(type);
                    deviceDto.setId(entry.getValue());
                    deviceDto.setSource("EVENT_UPLOAD");
                    deviceDtos.add(deviceDto);
                }
            }
        }
        if (deviceDtos.size() == 0){
            UserMeta userMeta = bpUserMapper.selectByUserIdAndPkgId(userId,appId);
            deviceDtos.add(new DeviceDto(){{
                setType("DEVICE_ID");
                setId(userMeta.getDeviceId());
                setSource("BP_USER");
            }});
            deviceMap.put("DEVICE_ID", userMeta.getDeviceId());
        }
        String dsp;
        List<OcpcInfoDto> ocpcInfoDtoList = new ArrayList<>();
        if (tfUserService.isUseDeviceGui(productEntity.getProduct())) {
            UserActive userActive = tfUserService.querySourceByUserId(productEntity.getProduct(), String.valueOf(userId), Long.valueOf(appId));
            String source;
            if (userActive != null) {
                source = userActive.getSource();
            } else {
                source = "";
            }
            ocpcInfoDtoList.add(new OcpcInfoDto(){{
                setType("设备归因");
                setSource("OCPC_EVENT_HBASE");
                setDsp(source);
            }});
            dsp = source;
        } else {
            // 查SDC-HBase
            String key = String.format("%s@%s",userId,productEntity.getProduct());
            byte[] result = HBaseUtils.searchDataFromHadoop(ocpcHadoopConnection, "userAcitveByUser", key);
            if (result != null){
                OcpcClick click = JSONObject.parseObject(result, OcpcClick.class);
                ocpcInfoDtoList.add(new OcpcInfoDto(){{
                    setType("用户ID归因");
                    setSource("USER_ACTIVE_HBase");
                    setDsp(click.getSource());
                }});
            }
            // 查 GUI_DSP
            dsp = userDsp(deviceMap,productEntity.getId(),productEntity.getProduct());
            ocpcInfoDtoList.add(new OcpcInfoDto(){{
                setType("设备归因");
                setSource("GUI_DSP_HTTP");
                setDsp(dsp);
            }});

        }

        grayInfoRsp.setDeviceList(deviceDtos);
        grayInfoRsp.setOcpcInfoDtoList(ocpcInfoDtoList);
        // OCPC
        OcpcInfoBean ocpcInfoBean = new OcpcInfoBean();
        ocpcInfoBean.setProduct(productEntity.getProduct());
        ocpcInfoBean.setUserId(userId.toString());
        deviceMap.forEach((k,v) -> {
            if (k.contains("oaid")) {
                ocpcInfoBean.setOaid(v);
            } else if (k.contains("androidId")) {
                if (ocpcInfoBean.getImei() == null){
                    ocpcInfoBean.setImei(v);
                }
            } else if (k.contains("imei")) {
                ocpcInfoBean.setImei(v);
            }
        });
        ocpcInfoBean.setOcpcSource(dsp);
        grayInfoRsp.setChangeOcpcInfoData(ocpcInfoBean);
        // -- Gray --
        List<UserGrayIpEntity> allGrayList = new ArrayList<>();

        if (deviceMap.size() > 0) {
            List<UserGrayIpEntity> userGrayDeviceList = userGrayIpMapper.queryList(null, GrayType.DEVICE.getType(), new ArrayList<>(deviceMap.values()));
            if (Lists.noEmpty(userGrayDeviceList)){
                allGrayList.addAll(userGrayDeviceList);
            }
        }
        List<UserGrayIpEntity> userGrayUserList = userGrayIpMapper.queryList(null, GrayType.USER.getType(), Collections.singletonList(userId.toString()));
        if (Lists.noEmpty(userGrayUserList)) {
            allGrayList.addAll(userGrayUserList);
        }
        // 是否可看广告
        String flag  = jedisClusterClient.get(RedisUtil.buildUserReaderAd(appId.toString(),userId.toString()));
        List<GrayDetailDto> grayDetailDtos = allGrayList.stream()
                .map(userGrayIpEntity -> {
                    GrayDetailDto detailDto = new GrayDetailDto();
                    detailDto.setType("禁止登录");
                    detailDto.setId(userGrayIpEntity.getTargetId());
                    detailDto.setRemark(userGrayIpEntity.getRemark());
                    detailDto.setStatus("生效中");
                    detailDto.setCreateTime(DateUtil.dateToStringWithTime(userGrayIpEntity.getCreateTime()));
                    return detailDto;
                }).collect(Collectors.toList());


        if (Strings.noEmpty(flag) && flag.equals("true")) {
            grayDetailDtos.add(new GrayDetailDto() {{
                setType("禁止观看广告");
                setRemark("外部调用禁止");
                setStatus("生效中");
                setId(userId.toString());
                setCreateTime("未知-需查日志");
            }});
        }

        grayInfoRsp.setGrayDetailDtoList(grayDetailDtos);
        return grayInfoRsp;
    }

    @Getter
    @Setter
    @NoArgsConstructor
    @AllArgsConstructor
    public static class OcpcClick {

        /**
         * 渠道来源
         */
        private String source;
    }


    public List<UserWithdrawRsp> queryUserWithdrawRsp(Integer appId,Long userId){
        ProductEntity productEntity = AppConfig.appIdMap.get(appId.longValue());
        if (productEntity == null){
            return new ArrayList<>();
        }

        return clickHouseService.queryWithDraw(productEntity.getProduct(),userId)
                .stream()
                .map(withdrawOrderDetail -> {
                    UserWithdrawRsp rsp = new UserWithdrawRsp();
                    rsp.setOrderNo(withdrawOrderDetail.getOrderNo());
                    rsp.setSubType(withdrawOrderDetail.getSubType());
                    rsp.setStatus(withdrawOrderDetail.getStatus());
                    rsp.setCreateTime(DateUtil.dateToStringWithTime(new Date(withdrawOrderDetail.getCreateTime())));
                    rsp.setAmount(withdrawOrderDetail.getAmount());
                    rsp.setLogday(DateUtil.dateToString(new Date(withdrawOrderDetail.getCreateTime())));
                    return rsp;
                }).collect(Collectors.toList());
    }

    public static String userDsp(Map<String,String> deviceMap, Integer appId, String product){
        try {
            String deviceId="",oaId="",androidId="";

            for (Map.Entry<String, String> entry : deviceMap.entrySet()) {
                String k = entry.getKey();
                String v = entry.getValue();
                if (k.contains("device")){
                    deviceId = v;
                }else if (k.contains("oaid")){
                    oaId = v;
                }else if (k.contains("androidId")){
                    androidId = v;
                }
            }
            Map<String,Object> param = new HashMap<>();
            param.put("product",product);
            param.put("os","android");
            param.put("ocpcDeviceId", MD5Hash.getMD5AsHex(deviceId.getBytes()));
            param.put("SourceDeviceId", deviceId);
            param.put("oaid",oaId);
            param.put("androidId",androidId);
            param.put("appId",appId);

            String result = HttpClients.GET("http://bp-api.shinet-inc.com/dispense/user/event/guiDsp",param);

            if (StringUtils.isNotBlank(result)){
                JSONObject jsonObject = JSONObject.parseObject(result);
                return jsonObject.getString("data");
            }
        }catch (Exception e){
            log.error("Query Er:",e);
        }
        return "NONE";
    }

    private static final String ATTRIBUTE_KEY_FORMATTER ="ck:did:%s:%s:%s";
    public static final String CLICK_CAID_PRE ="ck:caid:%s:%s:%s";

    /**
     * click事件表名
     */
    private static final String CLICK_TABLE_NAME = "ToutiaoClick";
    private static final String CLICK_TABLE_NAME_SDC = "userAcitveByUser";
    private static final String toutiaoClickLong = "ToutiaoClickLong";


    public UserActive queryByHbase(String userId, String product) {
        String rowKey = userId + "@" + product;
        byte[]  hbaseValue = HBaseUtils.searchDataFromHadoop(ocpcHadoopConnection, CLICK_TABLE_NAME_SDC, rowKey);
        if(hbaseValue != null && StringUtils.isNotBlank(new String(hbaseValue))){
            UserActive userActive = JSON.parseObject(hbaseValue,UserActive.class);
            return userActive;
        }
        return null;
    }

    public void changeOcpcFlag(OcpcInfoBean ocpcInfoBean){
        List<String> products = Arrays.asList(ocpcInfoBean.getProduct(),"bd"+ocpcInfoBean.getProduct());
        for (String product : products) {
            String device = ocpcInfoBean.getImei();
            String oaid = ocpcInfoBean.getOaid();
            String userid = ocpcInfoBean.getUserId();
            String dsp = ocpcInfoBean.getOcpcSource();
            Map<String, byte[]> values = ImmutableMap.of(String.format(ATTRIBUTE_KEY_FORMATTER, product, "android", device),
                    JSON.toJSONBytes(new OcpcClick(dsp)));

            HBaseUtils.batchSaveToHadoopWithoutHash(ocpcHadoopConnection, CLICK_TABLE_NAME, values);
            UserActive userActive = queryByHbase(userid, product);
            Date cr = DateUtil.dateIncreaseByHour(new Date(),-12);
            if (userActive != null) {
                String rowKey = userActive.getUserId() + "@" + userActive.getProduct();
                userActive.setSource(dsp);
                cr = DateUtil.dateIncreaseByHour(userActive.getCreateTime(),-12);
                HBaseUtils.saveToHadoop(ocpcHadoopConnection, CLICK_TABLE_NAME_SDC, rowKey, JSON.toJSONBytes(userActive));
            }

            try (Table table = toutiaoClickConnection.getTable(TableName.valueOf(toutiaoClickLong))) {

                String key = String.format(ATTRIBUTE_KEY_FORMATTER, product, "",
                        MD5Hash.getMD5AsHex(device.getBytes()).toUpperCase());

                Put put = new Put(Bytes.toBytes(key));
                Date finalCr = cr;
                put.addColumn(Bytes.toBytes("family"), Bytes.toBytes("qualifier"),
                        Bytes.toBytes(JSONObject.toJSONString(new ToutiaoClick() {{
                    setDsp(dsp);
                    setCreateTime(finalCr);
                }})));

                String keys = String.format(ATTRIBUTE_KEY_FORMATTER, product, "android", MD5Hash.getMD5AsHex(device.getBytes()));
                Put puts = new Put(Bytes.toBytes(keys));
                puts.addColumn(Bytes.toBytes("family"), Bytes.toBytes("qualifier"),
                        Bytes.toBytes(JSONObject.toJSONString(new ToutiaoClick() {{
                    setDsp(dsp);
                    setCreateTime(finalCr);
                }})));
                String keyoa = String.format("ck:android:id:%s:%s:%s", product, "android", device);
                Put put2 = new Put(Bytes.toBytes(keyoa));

                put2.addColumn(Bytes.toBytes("family"), Bytes.toBytes("qualifier"),
                        Bytes.toBytes(JSONObject.toJSONString(new ToutiaoClick() {{
                    setDsp(dsp);
                    setCreateTime(finalCr);
                }})));

                String keyoa2 = String.format("ck:od:%s:%s:%s", product, "android", oaid);
                Put put3 = new Put(Bytes.toBytes(keyoa2));
                put3.addColumn(Bytes.toBytes("family"), Bytes.toBytes("qualifier"),
                        Bytes.toBytes(JSONObject.toJSONString(new ToutiaoClick() {{
                    setDsp(dsp);
                    setCreateTime(finalCr);
                }})));


                table.put(put);
                table.put(put2);
                table.put(put3);
                table.put(puts);
            } catch (Exception e) {

            }
        }
    }
}
