package com.coohua.user.event.biz.dc.service;

import com.coohua.user.event.biz.dc.mapper.TruncateTableMapper;
import com.coohua.user.event.biz.util.DateUtil;
import com.coohua.user.event.biz.util.Strings;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;

/**
 * <AUTHOR>
 * @since 2020/9/9
 */
@Slf4j
@Service
public class CleanResultService {

    @Resource
    private TruncateTableMapper truncateTableMapper;

    public void cleanTable(){
        log.info("开始清空日报结果表.....");
        truncateTableMapper.truncateBusinessResult();
        truncateTableMapper.truncateDailyChannelDevice();
        truncateTableMapper.truncateDailyResult();
        truncateTableMapper.truncateProductUsingTime();
        truncateTableMapper.truncateVideoDetail();
        truncateTableMapper.truncateDailyPrenticeDevice();
        log.info("日报结果表清空完成....");
    }

    public void cleanVideoTemp(){
        log.info("清空视频数据表");
        truncateTableMapper.truncateAdsVideoTemp();
        log.info("清空视频数据表成功!");


        try {
            log.info("开始清空15天前实时数据....");
            Date now = new Date();
            Date before = DateUtil.dateIncreaseByDay(now,-16);
            String logDay = DateUtil.dateToString(before);
            int delAu = truncateTableMapper.deleteRealTimeAuTemp(logDay);
            int delNu = truncateTableMapper.deleteRealTimeNuTemp(logDay);

            log.info("完成删除Au:{},Nu:{}",delAu,delNu);
        }catch (Exception e){

        }
    }

    public void deleteTemp(String date){
        log.info("开始清空实时数据....");
        if (Strings.noEmpty(date)){
            log.info("手动输入参数..只删除{}数据...",date);
            int delAu = truncateTableMapper.deleteRealTimeAuTemp(date);
            int delNu = truncateTableMapper.deleteRealTimeNuTemp(date);

            log.info("完成删除Au:{},Nu:{}",delAu,delNu);
            return;
        }

        Date now = new Date();
        String logDay = DateUtil.dateToString(now);
        int delAu = truncateTableMapper.deleteRealTimeAuTemp(logDay);
        int delNu = truncateTableMapper.deleteRealTimeNuTemp(logDay);

        log.info("完成删除Au:{},Nu:{}",delAu,delNu);
    }


    public void cleanTableBusiness(){
        log.info("开始清空商业结果表.....");
        truncateTableMapper.truncateBusinessTemp();
        log.info("结束清空商业结果表....");
    }
}
