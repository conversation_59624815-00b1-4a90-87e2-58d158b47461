package com.coohua.user.event.biz.service.bean;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import org.apache.commons.lang3.StringUtils;

import java.io.Serializable;
import java.util.Date;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR>
 * @since 2020-07-13
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
public class ToutiaoClick implements Serializable {

    private static final long serialVersionUID = 1L;

    private Long id;

    @ApiModelProperty(value = "xiaoxiaole")
    private String dsp;

    @ApiModelProperty(value = "toutiao")
    private String product;

    @ApiModelProperty(value = "ios android")
    private String os;

    @ApiModelProperty(value = "账户ID")
    private String accountId;

    @ApiModelProperty(value = "ocpcid")
    private String ocpcDeviceId;

    @ApiModelProperty(value = "idfa加md5")
    private String idfa2;

    @ApiModelProperty(value = "时间戳")
    private String ts;

    @ApiModelProperty(value = "账户名称")
    private String accountName;

    private String callbackUrl;

    @ApiModelProperty(value = "创意ID")
    private String cid;

    @ApiModelProperty(value = "广告组ID")
    private String gid;

    @ApiModelProperty(value = "计划ID")
    private String pid;

    @ApiModelProperty(value = "Android Q及更高版本的设备号")
    private String oaid;

    @ApiModelProperty(value = "oaid加md5")
    private String oaid2;

    @ApiModelProperty(value = "计划名称")
    private String aidName;

    @ApiModelProperty(value = "分组名称")
    private String groupName;

    @ApiModelProperty(value = "创意名称")
    private String cidName;

    private Integer activateCount;

    private Date createTime;

    private Date updateTime;

    /**
     *
     对 MAC 去除分隔符之后进行 MD5
     */
    @ApiModelProperty(value = "mac地址")
    private String mac;

    @ApiModelProperty(value = "渠道包")
    private String pkgChannel;

    @ApiModelProperty(value = "广告位编码")
    private String unionSite;

    private String androidId;

    /**
     * 媒体投放系统获取的用户终端的公共IP地址
     */
    private String ip;

    /**
     * 用户代理(User Agent)，一个特殊字符串头，使得服务器能够识别客户使用的操作系统及版本、CPU类型、浏览器及版本、浏览器渲染引擎、浏览器语言、浏览器插件等。
     */
    private String ua;

    /**
     * 手机型号
     */
    private String model;
    @TableField(exist = false)
    private String openId;
    @TableField(exist = false)
    private String wAppId;
    @TableField(exist = false)
    private String caid;
    @TableField(exist = false)
    private String clickId;
    public String concatIpua() {
        if (StringUtils.isAnyBlank(ip, ua, model)) {
            return null;
        }
        return ip + "_" + ua + "_" + model;
    }


    public String concatIpuaMd() {
        if (StringUtils.isAnyBlank(ip, ua)) {
            return null;
        }
        return ip + "_" + ua;
    }
}
