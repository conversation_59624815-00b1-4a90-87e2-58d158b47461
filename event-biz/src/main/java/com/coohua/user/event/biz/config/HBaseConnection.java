package com.coohua.user.event.biz.config;

import lombok.extern.slf4j.Slf4j;
import org.apache.hadoop.hbase.client.Connection;
import org.apache.hadoop.hbase.client.ConnectionFactory;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.apache.hadoop.hbase.HBaseConfiguration;

import java.io.IOException;

/**
 * <AUTHOR>
 * @since 2021/6/25
 */
@Slf4j
@Configuration
public class HBaseConnection {

    @Bean("linDormConnection")
    public Connection init(){
        // 新建一个Configuration
        org.apache.hadoop.conf.Configuration conf = HBaseConfiguration.create();
        // 集群的连接地址(VPC内网地址)在控制台页面的数据库连接界面获得
        conf.set("hbase.zookeeper.quorum", "ld-2ze609kgaqf74l34r-proxy-lindorm.lindorm.rds.aliyuncs.com:30020");
        conf.set("hbase.client.username", "root");
        conf.set("hbase.client.password", "root");
        Connection connection = null;
        try {
            connection = ConnectionFactory.createConnection(conf);
        } catch (IOException e) {
            log.error("Start lindorm ERROR:",e);
        }
        return connection;
    }

    @Bean(name = "toutiaoClickConnection")
    public Connection initToutiaoClickHbase() throws IOException {
        // 新建一个Configuration
        org.apache.hadoop.conf.Configuration conf = HBaseConfiguration.create();
// 集群的连接地址(VPC内网地址)在控制台页面的数据库连接界面获得
        conf.set("hbase.zookeeper.quorum", "ld-2zeo06b341w4czco7-proxy-lindorm.lindorm.rds.aliyuncs.com:30020");
// xml_template.comment.hbaseue.username_password.default
        conf.set("hbase.client.username", "root");
        conf.set("hbase.client.password", "root");
// 如果您直接依赖了阿里云hbase客户端，则无需配置connection.impl参数，如果您依赖了alihbase-connector，则需要配置此参数
//conf.set("hbase.client.connection.impl", AliHBaseUEClusterConnection.class.getName());

        Connection connection = ConnectionFactory.createConnection(conf);
        return connection;
    }

    @Bean(name = "hbaseConnection")
    public Connection inithbaseConnection() throws IOException {
        // 新建一个Configuration
        org.apache.hadoop.conf.Configuration conf = HBaseConfiguration.create();
        conf.set("hbase.zookeeper.quorum", "ld-2ze1vdu9684b75dkc-proxy-lindorm.lindorm.rds.aliyuncs.com:30020");
        conf.set("hbase.client.username", "root");
        conf.set("hbase.client.password", "root");
        Connection connection = ConnectionFactory.createConnection(conf);
        return connection;
    }
}
