package com.coohua.user.event.biz.enums;

/**
 * <AUTHOR>
 * @since 2021/6/23
 */
public enum AdPosType {
    Open_screen(1,"开屏"),
    Information_Flow(2,"信息流"),
    Table_plaque(3,"插屏"),
    Rewarded_video(4,"激励视频"),
    <PERSON>(5,"<PERSON>"),
    Full_screen_video(6,"全屏视频"),
    ;

    Integer code;
    String desc;

    AdPosType(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public Integer getCode() {
        return code;
    }

    public void setCode(Integer code) {
        this.code = code;
    }

    public String getDesc() {
        return desc;
    }

    public void setDesc(String desc) {
        this.desc = desc;
    }
}
