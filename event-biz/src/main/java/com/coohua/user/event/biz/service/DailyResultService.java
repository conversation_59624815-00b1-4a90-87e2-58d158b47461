package com.coohua.user.event.biz.service;

import com.coohua.user.event.biz.dc.mapper.DailyResultMapper;
import com.coohua.user.event.biz.util.DateUtil;
import com.coohua.user.event.biz.util.Strings;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2020/9/7
 */
@Slf4j
@Service
public class DailyResultService {

    @Resource
    private DailyResultMapper dailyResultMapper;

    @Resource(name = "partitionJobTaskPool")
    private ThreadPoolTaskExecutor executor;

    public List<Map<String,Double>> queryDailyAmount(String logDay){
        return  dailyResultMapper.queryDailyAmount(logDay);
    }
}
