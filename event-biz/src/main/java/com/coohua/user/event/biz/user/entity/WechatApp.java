package com.coohua.user.event.biz.user.entity;

import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @since 2020/10/12
 */
@Data
public class WechatApp implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * id
     */
    private Long id;

    /**
     * app_id
     */
    private Long appId;

    /**
     * wechat_app_id
     */
    private String wechatAppId;

    /**
     * wechat_app_secret
     */
    private String wechatAppSecret;

    /**
     * remark
     */
    private String remark;

    /**
     * 一经设定，不可更改n用以区分不同产品马甲包之间的 用户idn当 step=0 时，当前马甲包与step=0 的包 数据共享 否则数据分离
     */
    private Integer step;

    /**
     * create_time
     */
    private Long createTime;

    /**
     * update_time
     */
    private Long updateTime;
}

