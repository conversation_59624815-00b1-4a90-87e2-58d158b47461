package com.coohua.user.event.biz.dc.mapper;

import com.coohua.user.event.biz.dc.entity.HsResultData;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * <p>
 *  Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2023-06-28
 */
public interface HsResultDataMapper extends BaseMapper<HsResultData> {

    @Select({"select * from ads.hs_result_data where logday =#{logday}"})
    List<HsResultData> queryByLogday(@Param("logday")String logday);
}
