package com.coohua.user.event.biz.dc.config;

import com.alibaba.druid.pool.DruidDataSource;
import com.alibaba.druid.spring.boot.autoconfigure.DruidDataSourceBuilder;
import com.baomidou.mybatisplus.extension.plugins.PaginationInterceptor;
import com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean;
import lombok.extern.slf4j.Slf4j;
import org.apache.ibatis.session.SqlSessionFactory;
import org.mybatis.spring.annotation.MapperScan;
import org.mybatis.spring.mapper.MapperScannerConfigurer;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.boot.autoconfigure.condition.ConditionalOnBean;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.io.ClassPathResource;
import org.springframework.core.io.support.PathMatchingResourcePatternResolver;

import javax.sql.DataSource;
import java.sql.SQLException;

@Slf4j
@Configuration
@MapperScan(basePackages = {"com.coohua.user.event.biz.dc.mapper"})
public class DcDataSourceConfig {

    @Bean(name = "datasourceDc")
    @ConfigurationProperties(prefix = "spring.datasource.dc")
    public DataSource dataSource() throws SQLException{
        return DruidDataSourceBuilder.create().build();
    }

    @Bean(name = "datasourceDcSqlSessionFactoryBean")
    @ConditionalOnBean(name = "datasourceDc")
    public SqlSessionFactory sqlSessionFactoryBean(@Qualifier("datasourceDc") DataSource dataSource) throws Exception {
        MybatisSqlSessionFactoryBean mybatisSqlSessionFactoryBean = new MybatisSqlSessionFactoryBean();
        mybatisSqlSessionFactoryBean.setDataSource(dataSource);
        mybatisSqlSessionFactoryBean.setConfigLocation(new ClassPathResource("config/mybatis_config.xml"));
        mybatisSqlSessionFactoryBean.setMapperLocations(new PathMatchingResourcePatternResolver().getResources("classpath*:mapper/dc/*.xml"));
        PaginationInterceptor paginationInterceptor = new PaginationInterceptor();
        paginationInterceptor.setDialectType("mysql");
        paginationInterceptor.setLimit(-1);

        mybatisSqlSessionFactoryBean.setPlugins(paginationInterceptor);
        return mybatisSqlSessionFactoryBean.getObject();
    }

    @Bean(name = "DcMapperScannerConfigurer")
    @ConditionalOnBean(name = "datasourceDcSqlSessionFactoryBean")
    public MapperScannerConfigurer mapperScannerConfigurer(){
        MapperScannerConfigurer configurer = new MapperScannerConfigurer();
        configurer.setBasePackage("com.coohua.user.event.biz.dc.mapper");
        configurer.setSqlSessionFactoryBeanName("datasourceDcSqlSessionFactoryBean");
        return configurer;
    }
}
