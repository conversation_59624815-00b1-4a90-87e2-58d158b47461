package com.coohua.user.event.biz.click.mapper;

import com.coohua.user.event.biz.service.bean.TfPfAdBean;
import com.coohua.user.event.biz.service.bean.TfPfUserBean;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2024/2/2
 */
public interface ClickHouseTfPfMapper {

    @Select({
            " select s1.ad_id            as ad_id,  " +
            "       s1.advertiser_id    as advertiser_id,  " +
            "       s1.app_name         as product_name,  " +
            "       s1.need_return_user as need_return_user,  " +
            "       max(s2.product)     as product  " +
            " from (  " +
            "         select ad_id,  " +
            "                advertiser_id,  " +
            "                app_name,  " +
            "                sum(attribution_convert) as converts,  " +
            "                sum(cost)                as costs,  " +
            "                sum(cpa_bid * 6 * 1.25)  as zh_cj,  " +
            "                6 - converts              as need_return_user  " +
            "         from old_mysql_ads.toutiao_report_tf_new_ad_process  " +
            "         where toDate(ad_create_time) = #{logday}  " +
            "         group by ad_id, advertiser_id, app_name  " +
            "         having converts >= 3  " +
            "            and converts < 6  " +
            "            and zh_cj < costs  " +
            "         ) s1  " +
            "         left join dwd.product_map_dist s2 on s1.app_name = s2.product_name  " +
            " where s2.product_group = '项目一组' and s2.tag !='短视频' ",
            " group by s1.ad_id, s1.advertiser_id, s1.app_name, s1.need_return_user;"
    })
    List<TfPfAdBean> queryTfPfAd(@Param("logday") String logday);


    @Select({"<script>",
            " select k1.plan_id as ad_id, k1.userId as user_id, ifNull(k2.pv, 0) as sp_pv, ifNull(k2.arpu, 0) as arpu " +
                    "from ( " +
                    "         select s1.product as product, s1.userId as userId,s1.plan_id as plan_id" +
                    "         from ( " +
                    "                  select * " +
                    "                  from dwd.user_event_dist " +
                    "                  where plan_id in " ,
                    " <foreach collection='adIds' index='index' item='item' open='(' separator=',' close=')'>#{item}</foreach> ",
                    "                    and toDate(create_time)   &gt;= #{logday} " +
                    "                    and event_type = 0 " +
                    "                  ) s1 " +
                    "                  left join ( " +
                    "             select * " +
                    "             from dwd.user_event_dist " +
                    "             where plan_id in " ,
                    " <foreach collection='adIds' index='index' item='item' open='(' separator=',' close=')'>#{item}</foreach> ",
                    "               and toDate(create_time)   &gt;= #{logday} " +
                    "               and event_type = 25 " +
                    "             ) s2 on s1.product = s2.product and s1.userId = s2.userId " +
                    "         where s2.userId = '' " +
                    "            or s2.userId is null " +
                    "         ) k1 " +
                    "         left join ( " +
                    "    select product, userid, sum(toFloat64OrZero(extend1) / 1000) as arpu, count() as pv " +
                    "    from ods.event_dist " +
                    "    where logday   &gt;= #{logday} " +
                    "      and ad_action = 'exposure' " +
                    "      and ad_type global in (select toString(ad_type) " +
                    "                             from dwd.ad_type_basic_dist " +
                    "                             where type_name in ( " +
                    "                                                 '视频', '全屏视频' " +
                    "                                 )) " +
                    "      and userid global in ( " +
                    "        select userId " +
                    "        from dwd.user_event_dist " +
                    "        where plan_id in  ",
                    " <foreach collection='adIds' index='index' item='item' open='(' separator=',' close=')'>#{item}</foreach> ",
                    "          and toDate(create_time)   &gt;= #{logday} " +
                    "          and event_type = 0 " +
                    "    ) " +
                    "    group by product, userid " +
                    "    ) k2 on k1.product = k2.product and k1.userId = k2.userid",
            "</script>"})
    List<TfPfUserBean> queryTfPfUser(@Param("adIds") List<String> adId,@Param("logday") String logday);
}
