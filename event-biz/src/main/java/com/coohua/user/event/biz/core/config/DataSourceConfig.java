package com.coohua.user.event.biz.core.config;

import com.alibaba.druid.pool.DruidDataSource;
import com.alibaba.druid.spring.boot.autoconfigure.DruidDataSourceBuilder;
import com.baomidou.mybatisplus.extension.plugins.PaginationInterceptor;
import com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean;
import lombok.extern.slf4j.Slf4j;
import org.apache.ibatis.session.SqlSessionFactory;
import org.mybatis.spring.annotation.MapperScan;
import org.mybatis.spring.mapper.MapperScannerConfigurer;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.autoconfigure.condition.ConditionalOnBean;
import org.springframework.boot.autoconfigure.condition.ConditionalOnMissingBean;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;
import org.springframework.core.io.ClassPathResource;
import org.springframework.core.io.support.PathMatchingResourcePatternResolver;
import org.springframework.jdbc.datasource.DataSourceTransactionManager;

import javax.sql.DataSource;
import java.sql.SQLException;

@Slf4j
@Configuration
@MapperScan(basePackages = {"com.coohua.user.event.biz.core.mapper"})
public class DataSourceConfig {

    @Bean(name = "datasourceGoods")
    @ConfigurationProperties(prefix = "spring.datasource")
    public DataSource dataSource() throws SQLException{
        return DruidDataSourceBuilder.create().build();
    }

    @Bean(name = "datasourceGoodsSqlSessionFactoryBean")
    @ConditionalOnBean(name = "datasourceGoods")
    public SqlSessionFactory sqlSessionFactoryBean(@Qualifier("datasourceGoods") DataSource dataSource) throws Exception {
        MybatisSqlSessionFactoryBean mybatisSqlSessionFactoryBean = new MybatisSqlSessionFactoryBean();
        mybatisSqlSessionFactoryBean.setDataSource(dataSource);
        mybatisSqlSessionFactoryBean.setConfigLocation(new ClassPathResource("config/mybatis_config.xml"));
        mybatisSqlSessionFactoryBean.setMapperLocations(new PathMatchingResourcePatternResolver().getResources("classpath*:mapper/core/*.xml"));
        PaginationInterceptor paginationInterceptor = new PaginationInterceptor();
        paginationInterceptor.setDialectType("mysql");
        paginationInterceptor.setLimit(-1);

        mybatisSqlSessionFactoryBean.setPlugins(paginationInterceptor);
        return mybatisSqlSessionFactoryBean.getObject();
    }

    @Bean(name = "goodsMapperScannerConfigurer")
    @ConditionalOnBean(name = "datasourceGoodsSqlSessionFactoryBean")
    public MapperScannerConfigurer mapperScannerConfigurer(){
        MapperScannerConfigurer configurer = new MapperScannerConfigurer();
        configurer.setBasePackage("com.coohua.user.event.biz.core.mapper");
        configurer.setSqlSessionFactoryBeanName("datasourceGoodsSqlSessionFactoryBean");
        return configurer;
    }
}
