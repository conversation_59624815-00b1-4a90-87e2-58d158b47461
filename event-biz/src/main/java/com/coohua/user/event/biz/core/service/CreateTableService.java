package com.coohua.user.event.biz.core.service;

import com.coohua.user.event.biz.core.mapper.CreateTableMapper;
import com.coohua.user.event.biz.util.DateUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;

/**
 * <AUTHOR>
 * @since 2020/9/5
 */
@Slf4j
@Service
public class CreateTableService {

    private static final String tablePerfix = "user_event_log_";
    @Resource
    private CreateTableMapper createTableMapper;

    public void createTable(){
        log.info("开始创建user_event_log_ 表...");
        Date now = new Date();
        int index = 0;
        // 建超.. 避免无表
        while (index < 8){
            String tableName = tablePerfix + DateUtil.dateToString(DateUtil.dateIncreaseByDay(now,index),DateUtil.ISO_DATE_FORMAT);
            log.info("正在创建表:{}",tableName);
            try {
                createTableMapper.createEventLog(tableName);
                log.info("表:{} 创建成功",tableName);
            }catch (Exception e){
                log.error("表:{}创建失败",tableName);
            }
            index ++;
        }
        log.info("结束创建user_event_log_ 表...");
    }
}
