package com.coohua.user.event.biz.dc.service;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.coohua.user.event.biz.click.entity.ProductEntity;
import com.coohua.user.event.biz.click.entity.UserProfileDist;
import com.coohua.user.event.biz.dc.entity.WithdrawOrderDetail;
import com.coohua.user.event.biz.dc.mapper.WithdrawOrderDetailMapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.coohua.user.event.biz.user.entity.UserEntity;
import com.coohua.user.event.biz.user.entity.UserMeta;
import com.coohua.user.event.biz.util.DateUtil;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
* <p>
    * 支付订单 & 付款订单总表 服务实现类
    * </p>
*
* <AUTHOR>
* @since 2020-09-14
*/
@Service
public class WithdrawOrderDetailService{

    @Resource
    private WithdrawOrderDetailMapper withdrawOrderDetailMapper;

    public int saveBatch(ProductEntity entity, List<WithdrawOrderDetail> withdrawOrderDetailList, Map<Long, UserMeta> userEntityMap){
        if (withdrawOrderDetailList == null || withdrawOrderDetailList.size() == 0){
            return 0;
        }
        withdrawOrderDetailList.parallelStream().forEach(withdrawOrderDetail -> {
            withdrawOrderDetail.setId(null);
            withdrawOrderDetail.setProductCn(entity.getProductName());
            withdrawOrderDetail.setProductEn(entity.getProduct());
            withdrawOrderDetail.setProductGroup(entity.getProductGroup());
            UserMeta userEntity = userEntityMap.getOrDefault(withdrawOrderDetail.getUserId(),new UserMeta());
            if (userEntity.getOs()!= null) {
                withdrawOrderDetail.setOs(userEntity.getOs() == 1? "ios":"android");
            }
            withdrawOrderDetail.setDeviceId(userEntity.getDeviceId());
            withdrawOrderDetail.setLogday(DateUtil.dateToString(new Date(withdrawOrderDetail.getCreateTime())));
        });

        return withdrawOrderDetailMapper.saveBatch(withdrawOrderDetailList);
    }


    public void truncateWithdrawOrderDetail(){
        withdrawOrderDetailMapper.truncateWithdrawOrderDetail();
    }

    public int delete(Long start,Long end,String productName){
        return withdrawOrderDetailMapper.delete(start,end,productName);
    }

    public int delete(Long id){
        return withdrawOrderDetailMapper.deleteB(id);
    }

    public int deleteByLogDay(String logDay){
        return withdrawOrderDetailMapper.deleteByLogDay(logDay);
    }

    public List<WithdrawOrderDetail> queryAll(){
        return withdrawOrderDetailMapper.queryAll();
    }
}
