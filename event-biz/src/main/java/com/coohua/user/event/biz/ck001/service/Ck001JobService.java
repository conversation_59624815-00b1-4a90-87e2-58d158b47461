package com.coohua.user.event.biz.ck001.service;

import com.coohua.user.event.biz.ck001.mapper.Ck001JobMapper;
import com.coohua.user.event.biz.click.entity.UserMacIpCheckEntity;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * ck001 sql job
 */
@Service
public class Ck001JobService {
    @Resource
    private Ck001JobMapper ck001JobMapper;

    public List<UserMacIpCheckEntity> queryGdtGroup10UserInfo(List<String> productList, Double rate, Double inc) {
        return ck001JobMapper.queryGdtGroup10UserInfo(productList, rate, inc);
    }

    public List<UserMacIpCheckEntity> querySgmGroup10UserInfo(String group) {
        return ck001JobMapper.querySgmGroup10UserInfo(group);
    }

}
