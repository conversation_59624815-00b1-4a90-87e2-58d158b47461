package com.coohua.user.event.biz.util;

import java.util.Date;

/**
 * (。≖ˇェˇ≖。)
 * redis key 常数类
 * @author: zkk
 * DateTime: 2020/8/7 17:59
 */
public class RedisKeyConstants {

	public static final int EXPIRE_ONE_HOUR = 3600;
	public static final int EXPIRE_HALF_DAYS = 3600 * 12;
	public static final int EXPIRE_ONE_DAYS = 3600 * 24;
	public static final int EXPIRE_THREE_DAYS = 3600 * 24 * 3;
	public static final int EXPIRE_TWO_WEEK = 3600 * 24 * 14;
	public static final int EXPIRE_ONE_MONTH = 3600 * 24 * 30;

	/**
	 * click 事件 mac key
	 */
	public static final String REMOVE_EVENT_LOCK ="REMOVE_EVENT_LOCK";
	public static final String REMOVE_CLIKC_LOCK ="REMOVE_CLICK_LOCK";
	/**
	 * click 事件 mac key
	 */
	public static final String CLICK_MAC_PRE ="ck:mac:%s:%s:%s";
	/**
	 * click 事件 androidId key
	 */
	public static final String CLICK_ANDROID_ID_PRE ="ck:android:id:%s:%s:%s";


	public static final String CLICK_OPEN_ID_PRE ="ck:op:%s:%s";
	/**
	 * click 事件 oaid key
	 */
	public static final String CLICK_OAID_PRE ="ck:od:%s:%s:%s";

	public static final String CLICK_CAID_PRE ="ck:caid:%s:%s:%s";
	/**
	 * click 事件 oaid2 key
	 */
	public static final String CLICK_OAID2_PRE ="ck:od2:%s:%s:%s";
	/**
	 * click 事件 ipua key
	 */
	public static final String CLICK_IPUA_PRE ="ck:ipua:%s:%s:%s";

	public static final String CLICK_UAMD ="ck:uuii:%s:%s:%s";
	/**
	 * click 事件 idfa2 key
	 */
	public static final String CLICK_IDFA2_PRE ="ck:idfa2:%s:%s:%s";
	/**
	 * click事件 deviceId key（idfa和安卓的设备号）
	 */
	public static final String CLICK_DEVICE_PRE ="ck:did:%s:%s:%s";

	public static final String  REPORT_LOCK_KEY= "report:lock:%s:%s:%d";

	/**
	 * 创建ocpc表
	 */
	public static final String CREATE_OCPC_TABLE_LOCK_KEY = "crt_ocpc_table_lock";
	/**
	 * click 事件 androidId key
	 */
	public static final String CLICK_ANDROID_ID_PRE_PKG ="ck:android:id:%s:%s:%s:%s";
	/**
	 * click 事件 mac key
	 */
	public static final String CLICK_MAC_PRE_PKG ="ck:mac:%s:%s:%s:%s";
	/**
	 * click 事件 oaid key
	 */
	public static final String CLICK_OAID_PRE_PKG ="ck:od:%s:%s:%s:%s";
	/**
	 * click 事件 oaid2 key
	 */
	public static final String CLICK_OAID2_PRE_PKG ="ck:od2:%s:%s:%s:%s";
	/**
	 * click 事件 ipua key
	 */
	public static final String CLICK_IPUA_PRE_PKG ="ck:ipua:%s:%s:%s:%s";
	/**
	 * click 事件 idfa2 key
	 */
	public static final String CLICK_IDFA2_PRE_PKG ="ck:idfa2:%s:%s:%s:%s";
	/**
	 * click事件 deviceId key（idfa和安卓的设备号）
	 */
	public static final String CLICK_DEVICE_PRE_PKG ="ck:did:%s:%s:%s:%s";


	/**
	 * click事件 deviceId key（idfa和安卓的设备号）
	 */
	public static final String EVENT_DEVICE_ACTIVE ="event:active:%s:%s:%s:%s";
	public static final String EVENT_OPEN_ACTIVE ="e:op:%s:%s:%s";
	public static final String EVENT_CAID_ACTIVE ="e:caid:%s:%s";
	public static final String EVENT_UID_ACTIVE ="ue:uid:%s:%s:%s";

	public static final String EVENT_COMPENSATE_LOCK = "event2:compensate:lock";
	public static final String VIVO_CLICK_LOCK = "vivo:click:lock";

	public static final String EVENT_ACTIVE_USERID = "event:useractive:%s:%s:%s";
	private static final String CONTINUOUS_WITHDRAW_500AMOUNT_KEY = "withdraw:history:500amount:%s:%s:%s";
	private static final String CONTINUOUS_WITHDRAW_1000AMOUNT_KEY = "withdraw:history:1000amount:%s:%s:%s";
	private static final String USER_INCOME_ABNORMAL_KEY = "user:income:abnormal:%s:%s:%s";
	private static final String USER_INCOME_BAIDU_ABNORMAL_KEY = "user:income:baidu:abnormal:%s:%s:%s";
	private static final String USER_INCOME_BAIDU_ABNORMAL_GROUP5_KEY = "{user:inc:bd:abnormal:g5}:%s:%s:%s";
	public static final String USER_INCOME_BAIDU_ABNORMAL_GROUP5_PREFIX = "{user:inc:bd:abnormal:g5}:";

	private static final String KSDR_HW_GDT_INCOME_ABNORMAL_KEY = "{uinc:ksdr:hw:gdt}:%s:%s";
	public static final String KSDR_HW_GDT_INCOME_ABNORMAL_PREFIX = "{uinc:ksdr:hw:gdt}";
	private static final String USER_INCOME_ABNORMAL_NON_BAIDU_KEY = "{uinc:nbd:abnormal}:%s:%s:%s";
	public static final String NON_BAIDU_INCOME_ABNORMAL_PREFIX = "{uinc:nbd:abnormal}";
	public static final String NON_BAIDU_XIAOMI_KEY = "{uinc:nbd:xm}:%s:%s:%s";
	public static final String NON_BAIDU_XIAOMI_PREFIX = "{uinc:nbd:xm}";
	private static final String USER_INCOME_ABNORMAL_NON_BAIDU_KEY_GROUP5 = "{uinc:nbd:abnormal:group5}:%s:%s:%s";
	public static final String NON_BAIDU_INCOME_ABNORMAL_GROUP5_PREFIX = "{uinc:nbd:abnormal:group5}";
	private static final String USER_INCOME_ABNORMAL_NON_BAIDU_GROUP7_KEY = "{uinc:nbd:g7}:%s:%s:%s";
	public static final String NON_BAIDU_INCOME_ABNORMAL_GROUP7_PREFIX = "{uinc:nbd:g7}";
	/**
	 * 用户回调非百度收入异常
	 */
	private static final String USER_CALL_BACK_INTERCEPT_NON_BAIDU_KEY = "{user:callback:intercept}:%s:%s:%s";
	public static final String USER_CALL_BACK_INTERCEPT_NON_BAIDU_PREFIX = "{user:callback:intercept}";
	private static final String BAIDU_ARPU_INCOME_ABNORMAL_KEY = "{bd:arpu:inc:abnormal}:%s:%s";
	public static final String BAIDU_ARPU_INCOME_ABNORMAL_PREFIX = "{bd:arpu:inc:abnormal}";
	public static final String GDT_GAP_KEY = "{gdt:gap}:%s:%s";
	public static final String GDT_GAP_PREFIX = "{gdt:gap}";
	private static final String EXPOSURE_HIGH_1500_ECPM = "exposure:high:1500ecpm:%s:%s";

	/**
	 * 广点通回调异常
	 */
	private static final String GDT_SERVER_REWARD_CALL_BACK_KEY = "{gdt:server:reward:callback}:%s:%s";
	public static final String GDT_SERVER_REWARD_CALL_BACK_KEY_PREFIX = "{gdt:server:reward:callback}";

	/**
	 * 多个ip的用户
	 */
	public static final String MULTI_IP_COUNT = "{count:slot}:ip:count:%s:%s:%s";
	private static final String MULTI_USER_COUNT = "count:multi:user:%s:%s:%s";

	public static final String BLACK_CAID = "event:black:caid:%s";
	public static final String BLACK_IP = "withdraw:black:ip";
	private static final String USER_IP_KEY = "{user:ip}:%s:%s:%s";
	private static final String EX_IP_KEY = "{user:ex:ip}:%s:%s:%s";
	private static final String GROUP10_SGM_KEY = "{user:sgm:ex}:android:%s:%s";
	public static final String GROUP10_SGM_PREFIX = "{user:sgm:ex}";
	private static final String PRODUCT_CHANNEL_KEY = "event:reward:channel:%s";
	private static final String GROUP10_GDT_KEY = "{user:gdt:ex}:android:%s:%s";
	public static final String GROUP10_GDT_PREFIX = "{user:gdt:ex}";
	private static final String GROUP_SGM_GDT_KEY = "{user:gdt:sgm:ex}:android:%s:%s";;
	public static final String GROUP_SGM_GDT_PREFIX = "{user:gdt:sgm:ex}";
	private static final String GDT_GAP_EX_KEY = "{gdt:gap:ex}:%s:%s";
	public static final String GDT_GAP_EX_PREFIX = "{gdt:gap:ex}";
	private static final String REALTIME_CHECK_MULTI_REQ_ID_KEY = "rcmri:%s:%s";
	private static final String EVENT_DIST_INFO_KEY = "user:dist:info:%s:%s";
	private static final String AD_DATA_EVENT_DIST_INFO_KEY = "user:ad:data:dist:%s:%s";

	public static String getMultiIpCountKey(String os,String product, String userId) {
		return String.format(MULTI_IP_COUNT, os, product, userId);
	}

	public  static String getActiveOpenIdKey( String product,String wappid,String openId){
		return String.format(EVENT_OPEN_ACTIVE,product,wappid,openId);
	}

	public  static String getActiveCaidKey( String product,String caid){
		return String.format(EVENT_CAID_ACTIVE,product,caid);
	}

	public  static String getActiveUidKey( String product,String os,String userId){
		return String.format(EVENT_UID_ACTIVE,product,os,userId);
	}

	public  static String getActiveEventKey( String product,String os,String type,String strId){
		return String.format(EVENT_DEVICE_ACTIVE,product,os,type,strId);
	}

	public static final String KEY_EVENT_FORMATTER = "key:event:%s:%s:%s:%s";

	public static final String KEY_ACTION_EXT_EVENT_LIST_FORMATTER = "key:ext_event_list:%s:%s:%s";

	public static final String SYNC_PKG_TO_PRODUCT_TO_OCPC_KEY = "key:sync:pkgToProduct";

	public static final String SYNC_PRODUCT_TO_OCPC_KEY = "key:sync:product";

	public static final String USER_ACC_ARPU_LOCK_KEY = "key:lock:user_acc_arpu:%s:%s:%s";

	public static final String USER_ACC_NUM_LOCK_KEY = "key:lock:user_acc_num:%s:%s:%s";

	public static final String USER_FIX_BUG_LOCK_KEY = "key:lock:user_fix_bug:%s:%s:%s";

	public static final String USER_ACC_ARPU_FORMATTER = "key:user_acc_arpu:%s:%s:%s";

	public static final String USER_ACC_NUM_FORMATTER = "key:user_acc_num:%s:%s:%s";

	public static final String KUAISHOU_UDS_LOCK_KEY = "key:lock:kuaishou_uds:";

	public static final String TOUTIAO_EXTRA_LOCK_KEY = "key:lock:toutiao_extra:";

	public static final String IOS_CAID_MEMBER_KEY = "ios:caid:member:%s:%s";

	public static final String EXPOSURE_NO_DEVICE = "exposure:no:device:%s:%s";

	public static final String EXPOSURE_HIGH_ECPM = "exposure:high:ecpm:%s:%s";
	public static final String EXPOSURE_HIGH_2000ECPM = "exposure:high:2000ecpm:%s:%s";
	public static final String EXPOSURE_HIGH_3000ECPM = "exposure:high:3000ecpm:%s:%s";

	public static String  getClickAndroidIdKey( String product,String os, String androidId) {
		return String.format(CLICK_ANDROID_ID_PRE,product,os,androidId);
	}

	public static String  getClickOpenKey( String product, String openId) {
		return String.format(CLICK_OPEN_ID_PRE,product,openId);
	}

	public static String  getClickCaidKey(String product, String caid,String os) {
		return String.format(CLICK_CAID_PRE,product,os,caid);
	}

	public static String  getClickMacKey( String product,String os, String mac) {
		return String.format(CLICK_MAC_PRE,product,os,mac);
	}

	public static String getClickOaIdKey(String product, String os,String oaId) {
		return String.format(CLICK_OAID_PRE,product,os,oaId);
	}

	public static String getClickOaId2Key(String product, String os,String oaId2) {
		return String.format(CLICK_OAID2_PRE,product,os,oaId2);
	}

	public static String getClickIpuaKey(String product, String os,String ipua) {
		return String.format(CLICK_IPUA_PRE,product,os,ipua);
	}

	public static String getClickIAKey(String product, String os,String ipua) {
		return String.format(CLICK_UAMD,product,os,ipua);
	}

	public static String getClickIdfa2Key(String product, String os,String idfa2) {
		return String.format(CLICK_IDFA2_PRE,product,os,idfa2);
	}

	public static String getClickDeviceKey(String product, String os,String deviceId) {
		return String.format(CLICK_DEVICE_PRE,product,os,deviceId);
	}


	public static String  getClickAndroidIdKeyByPkgChannel( String product,String os, String androidId,String pkgChannel) {
		return String.format(CLICK_ANDROID_ID_PRE_PKG,product,os,androidId,pkgChannel);
	}

	public static String  getClickMacKeyByPkgChannel( String product,String os, String mac,String pkgChannel) {
		return String.format(CLICK_MAC_PRE_PKG,product,os,mac,pkgChannel);
	}

	public static String getClickOaIdKeyByPkgChannel(String product, String os,String oaId,String pkgChannel) {
		return String.format(CLICK_OAID_PRE_PKG,product,os,oaId,pkgChannel);
	}

	public static String getClickOaId2KeyByPkgChannel(String product, String os,String oaId2,String pkgChannel) {
		return String.format(CLICK_OAID2_PRE_PKG,product,os,oaId2,pkgChannel);
	}

	public static String getClickIpuaKeyByPkgChannel(String product, String os,String ipua,String pkgChannel) {
		return String.format(CLICK_IPUA_PRE_PKG,product,os,ipua,pkgChannel);
	}

	public static String getClickIdfa2KeyByPkgChannel(String product, String os,String idfa2,String pkgChannel) {
		return String.format(CLICK_IDFA2_PRE_PKG,product,os,idfa2,pkgChannel);
	}

	public static String getClickDeviceKeyByPkgChannel(String product, String os,String deviceId,String pkgChannel) {
		return String.format(CLICK_DEVICE_PRE_PKG,product,os,deviceId,pkgChannel);
	}

	public static String getReportLockKey(String product, String userId, int eventType) {
		return String.format(REPORT_LOCK_KEY, product, userId, eventType);
	}

	public static String getUserAccArpuLockKey(String product, String os, String userId) {
		return String.format(USER_ACC_ARPU_LOCK_KEY, product, os, userId);
	}

	public static String getUserAccNumLockKey(String product, String os, String userId) {
		return String.format(USER_ACC_NUM_LOCK_KEY, product, os, userId);
	}

	public static String getUserFixBugLockKey(String product, String os, String userId) {
		return String.format(USER_FIX_BUG_LOCK_KEY, product, os, userId);
	}

	public static String iosCaidCommonKey(String product, String caid) {
		return String.format(IOS_CAID_MEMBER_KEY, product, caid);
	}
	public static String getExposureNoDeviceKey(String product, String userId) {
		return String.format(EXPOSURE_NO_DEVICE, product, userId);
	}
	public static String getExposureHighEcpmKey(String product, String userId) {
		return String.format(EXPOSURE_HIGH_ECPM, product, userId);
	}
	public static String getExposureHigh2000EcpmKey(String product, String userId) {
		return String.format(EXPOSURE_HIGH_2000ECPM, product, userId);
	}
	public static String getExposureHigh3000EcpmKey(String product, String userId) {
		return String.format(EXPOSURE_HIGH_3000ECPM, product, userId);
	}

	public static String getContinuousWithdraw500amountKey(String product, String os, Long userId) {
		return String.format(CONTINUOUS_WITHDRAW_500AMOUNT_KEY, product, os,userId);
	}

	public static String getContinuousWithdraw1000amountKey(String product, String os, Long userId) {
		return String.format(CONTINUOUS_WITHDRAW_1000AMOUNT_KEY, product, os,userId);
	}

    public static String getUserIncomeAbnormalKey(String os, String product, String userIdStr) {
		return String.format(USER_INCOME_ABNORMAL_KEY, os,  product, userIdStr);
    }

	public static String getUserBaiduIncomeAbnormalKey(String android, String product, String userIdStr) {
		return String.format(USER_INCOME_BAIDU_ABNORMAL_KEY, android, product, userIdStr);
	}

	public static String getBaiduIncomeAbnormalGroup5Key(String android, String product, String userIdStr) {
		return String.format(USER_INCOME_BAIDU_ABNORMAL_GROUP5_KEY, android, product, userIdStr);
	}

	public static String getKsdrHwGdtIncomeAbnormalKey(String product, String userIdStr) {
		return String.format(KSDR_HW_GDT_INCOME_ABNORMAL_KEY, product, userIdStr);
	}


	public static String getNonBaiduIncomeAbnormalKey(String android, String product, String userIdStr) {
		return String.format(USER_INCOME_ABNORMAL_NON_BAIDU_KEY, android, product, userIdStr);
	}

	public static String getNonBaiduIncomeAbnormalGroup5Key(String android, String product, String userIdStr) {
		return String.format(USER_INCOME_ABNORMAL_NON_BAIDU_KEY_GROUP5, android, product, userIdStr);
	}

	/**
	 * 用户回调拦截key
	 * @param os
	 * @param product
	 * @param userIdStr
	 * @return
	 */
	public static String getUserCallBackInterceptNonBaiduKey(String os, String product, String userIdStr) {
		return String.format(USER_CALL_BACK_INTERCEPT_NON_BAIDU_KEY, os, product, userIdStr);
	}

	public static String getBdArpuIncAbnormalKey(String product, String userIdStr) {
		return String.format(BAIDU_ARPU_INCOME_ABNORMAL_KEY, product, userIdStr);
	}

	public static String getGdtGapKey(String product, String userIdStr) {
		return String.format(GDT_GAP_KEY, product, userIdStr);
	}

	public static String getNonBaiduIncomeAbnormalGeoup7Key(String android, String product, String userIdStr) {
		return String.format(USER_INCOME_ABNORMAL_NON_BAIDU_GROUP7_KEY, android, product, userIdStr);
	}

	public static String getExposureHigh1500EcpmKey(String product, String userId) {
		return String.format(EXPOSURE_HIGH_1500_ECPM, product, userId);
	}

	public static String getGdtRewardCallBackAbnormalKey(String product, String userId) {
		return String.format(GDT_SERVER_REWARD_CALL_BACK_KEY, product, userId);
	}

	public static String getMultiUserKey(String os, String caid, String product) {
		return String.format(MULTI_USER_COUNT, os, product, caid);
	}

	public static String getBlackCaidKey(String caid) {
		return String.format(BLACK_CAID, caid);
	}

	public static String getUserIpKey(String os, String product, String userId) {
		return String.format(USER_IP_KEY, os, product, userId);
	}

	public static String getExIpKey(String os, String product, String userId) {
		return String.format(EX_IP_KEY, os, product, userId);
	}

	public static String getSgmGroup10Key(String product, String userId) {
		return String.format(GROUP10_SGM_KEY, product, userId);
	}
	public static String getProductChannelKey(String product) {
		return String.format(PRODUCT_CHANNEL_KEY, product);
	}

	public static String getGdtGroup10Key(String product, String s) {
		return String.format(GROUP10_GDT_KEY, product, s);
	}

	public static String getSgmGdtGroupKey(String product, String userId) {
		return String.format(GROUP_SGM_GDT_KEY, product, userId);
	}

	public static String getGdtGapExKey(String product, String userId) {
		return String.format(GDT_GAP_EX_KEY, product, userId);
	}

    public static String buildMultiReqIdKey(String key) {
		Date now = new Date();
		String day = DateUtil.dateToString(now,DateUtil.ISO_DATE_FORMAT);
		return String.format(REALTIME_CHECK_MULTI_REQ_ID_KEY, day, key);
    }

	public static String getEventDistInfoKey(String product, Long userId) {
		return String.format(EVENT_DIST_INFO_KEY, product, userId);
	}
	public static String getAdDataEventDistInfoKey(String product, Long userId) {
		return String.format(AD_DATA_EVENT_DIST_INFO_KEY, product, userId);
	}

	public static String getNonBaiduXiaomiKey(String os, String product, String userIdStr) {
		return String.format(NON_BAIDU_XIAOMI_KEY, os, product, userIdStr);
	}
}
