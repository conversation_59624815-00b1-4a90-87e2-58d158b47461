package com.coohua.user.event.biz.dc.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.coohua.user.event.biz.dc.entity.YdWithdrawMchResultDated;
import org.apache.ibatis.annotations.Select;

/**
 * <p>
 *  Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2021-08-24
 */
public interface YdWithdrawMchResultDatedMapper extends BaseMapper<YdWithdrawMchResultDated> {

    @Select("select wechat_app_id from wechat_order_temp.wechat_config where wechat_id = #{wechatId}")
    String getAppId(Long wechatId);
    

}
