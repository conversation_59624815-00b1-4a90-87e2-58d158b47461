package com.coohua.user.event.biz.toufang.service;

import com.coohua.user.event.biz.click.entity.ProductEntity;
import com.coohua.user.event.biz.click.service.ClickHouseService;
import com.coohua.user.event.biz.core.service.BpToufangResultService;
import com.coohua.user.event.biz.dc.entity.TouFangAllOfflineEntity;
import com.coohua.user.event.biz.dc.entity.TouFangOfflineEntity;
import com.coohua.user.event.biz.dc.mapper.TouFangOfflineMapper;
import com.coohua.user.event.biz.toufang.entity.TouFangEntity;
import com.coohua.user.event.biz.toufang.mapper.TouFangBaseQueryMapper;
import com.coohua.user.event.biz.util.DateUtil;
import com.coohua.user.event.biz.util.Strings;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2020/9/4
 */
@Slf4j
@Service
public class TouFangBaseQueryService {


    @Resource
    private TouFangBaseQueryMapper touFangBaseQueryMapper;
    @Autowired
    private BpToufangResultService bpToufangResultService;
    @Resource
    private TouFangOfflineMapper touFangOfflineMapper;
    @Value("${use_upload_switch}")
    private boolean uploadSwitch;
    @Autowired
    private ClickHouseService clickHouseService;


    public void syToResult(String date){
        log.info("开始投放数据同步...");
        log.info("Param:{}",date);
        Date crashDate =  DateUtil.dateIncreaseByDay(DateUtil.stringToDate(DateUtil.dateToString(new Date())),-1);
        if (Strings.noEmpty(date)) {
            crashDate = DateUtil.stringToDate(date);
        }


        List<ProductEntity> productEntityList = clickHouseService.queryAllProduct();
        Map<String,ProductEntity> nameProductMap = productEntityList.stream().collect(Collectors.toMap(ProductEntity::getProductName,k->k,(k1,k2)->k1));
        String dateDate = DateUtil.dateToString(crashDate);
        log.info("查询日期：{}",dateDate);
        List<TouFangEntity> entities = new ArrayList<>();

        try {
            List<TouFangEntity> touFangEntities = touFangBaseQueryMapper.queryTouFang(dateDate);
            if (touFangEntities != null && touFangEntities.size() > 0) {
                entities.addAll(touFangEntities);
            }

            List<TouFangOfflineEntity> touFangOfflineEntitiesAndroid = touFangOfflineMapper.queryOfflineDataAndroid(dateDate);
            List<TouFangOfflineEntity> touFangOfflineEntitiesIos = touFangOfflineMapper.queryOfflineDataIos(dateDate);

            // FIXME 若数不对 注释掉 使用手传
            List<TouFangAllOfflineEntity> touFangAllOfflineEntities = touFangBaseQueryMapper.queryKuaishouData(dateDate);
            List<TouFangAllOfflineEntity> touFangAllOfflineTecentEntities = touFangBaseQueryMapper.queryTecentData(dateDate);

            if (uploadSwitch){
                // 若打开手动上传 不取库逻辑
                touFangAllOfflineEntities = new ArrayList<>();
                touFangAllOfflineTecentEntities = new ArrayList<>();
            }

            Map<String,TouFangOfflineEntity> offlineMapAndroid = touFangOfflineEntitiesAndroid.parallelStream()
                    .collect(Collectors.toMap(TouFangOfflineEntity::getProductName, t->t,(t1,t2) -> t1));
            Map<String,TouFangOfflineEntity> offlineMapIos = touFangOfflineEntitiesIos.parallelStream()
                    .collect(Collectors.toMap(TouFangOfflineEntity::getProductName, t->t,(t1,t2) -> t1));
            Map<String,TouFangAllOfflineEntity> offlineMapKuaishouAndroid = touFangAllOfflineEntities.parallelStream()
                    .filter(touFangAllOfflineEntity -> "android".equals(touFangAllOfflineEntity.getOs()))
                    .collect(Collectors.toMap(TouFangAllOfflineEntity::getAppName, t->t,(t1,t2) -> t1));

            Map<String,TouFangAllOfflineEntity> offlineMapKuaishouIos = touFangAllOfflineEntities.parallelStream()
                    .filter(touFangAllOfflineEntity -> "ios".equals(touFangAllOfflineEntity.getOs()))
                    .collect(Collectors.toMap(TouFangAllOfflineEntity::getAppName, t->t,(t1,t2) -> t1));
            Map<String,TouFangAllOfflineEntity> offlineMapTecentAndroid = touFangAllOfflineTecentEntities.parallelStream()
                    .filter(touFangAllOfflineEntity -> "android".equals(touFangAllOfflineEntity.getOs()))
                    .collect(Collectors.toMap(TouFangAllOfflineEntity::getAppName, t->t,(t1,t2) -> t1));

            Map<String,TouFangAllOfflineEntity> offlineMapTecentIos = touFangAllOfflineTecentEntities.parallelStream()
                    .filter(touFangAllOfflineEntity -> "ios".equals(touFangAllOfflineEntity.getOs()))
                    .collect(Collectors.toMap(TouFangAllOfflineEntity::getAppName, t->t,(t1,t2) -> t1));

            if (entities.size() > 0){
                entities.forEach(touFangEntity -> {
                    TouFangOfflineEntity touFangOfflineEntityAndroid = offlineMapAndroid.get(touFangEntity.getAppName());
                    TouFangOfflineEntity touFangOfflineEntityIos = offlineMapIos.get(touFangEntity.getAppName());
                    TouFangAllOfflineEntity touFangKuaishouIos = offlineMapKuaishouIos.get(touFangEntity.getAppName());
                    TouFangAllOfflineEntity toufangTecentIos = offlineMapTecentIos.get(touFangEntity.getAppName());
                    touFangEntity.setRebateCostSum(touFangEntity.getRebateCost());
                    touFangEntity.setActivateSum(touFangEntity.getActivate());
                    if ("android".equals(touFangEntity.getOs())) {
                        if (touFangOfflineEntityAndroid != null) {
                            log.info("查询到线下的Android历史数据,Mapping Add....");
                            // 对数增加列
                            touFangEntity.setActivateOther(Long.valueOf(touFangOfflineEntityAndroid.getDeviceNum()));
                            touFangEntity.setRebateCostOther(BigDecimal.valueOf(touFangOfflineEntityAndroid.getToufangAmount()));
                            touFangEntity.setRebateCostSum(touFangEntity.getRebateCost()
                                    .add(BigDecimal.valueOf(touFangOfflineEntityAndroid.getToufangAmount())));

                            touFangEntity.setActivateSum(touFangEntity.getActivate() + touFangOfflineEntityAndroid.getDeviceNum());
                        }

                        TouFangAllOfflineEntity touFangAllOfflineEntity  = offlineMapKuaishouAndroid.get(touFangEntity.getAppName());
                        if (touFangAllOfflineEntity != null){
                            log.info("查询到线下的Kuaishou - Android历史数据,Mapping Add....");
                            if (touFangEntity.getActivateOther() == null){
                                touFangEntity.setActivateOther(0L);
                            }
                            if (touFangEntity.getRebateCostOther() == null){
                                touFangEntity.setRebateCostOther(BigDecimal.ZERO);
                            }
                            if (touFangAllOfflineEntity.getActivate() == null){
                                touFangAllOfflineEntity.setActivate(0L);
                            }
                            touFangEntity.setActivateOther(touFangAllOfflineEntity.getActivate() + touFangEntity.getActivateOther());
                            touFangEntity.setRebateCostOther(touFangEntity.getRebateCostOther().add(touFangAllOfflineEntity.getRebateCost()));
                            touFangEntity.setRebateCostSum(touFangEntity.getRebateCost()
                                    .add(touFangAllOfflineEntity.getRebateCost()));

                            touFangEntity.setActivateSum(touFangEntity.getActivate() + touFangAllOfflineEntity.getActivate());
                        }


                        TouFangAllOfflineEntity touFangAllOfflineTecentEntity  = offlineMapTecentAndroid.get(touFangEntity.getAppName());
                        if (touFangAllOfflineTecentEntity != null){
                            log.info("查询到线下的Tecent - Android历史数据,Mapping Add....");
                            if (touFangEntity.getActivateOther() == null){
                                touFangEntity.setActivateOther(0L);
                            }
                            if (touFangEntity.getRebateCostOther() == null){
                                touFangEntity.setRebateCostOther(BigDecimal.ZERO);
                            }
                            if (touFangAllOfflineTecentEntity.getActivate() == null){
                                touFangAllOfflineTecentEntity.setActivate(0L);
                            }


                            if (touFangEntity.getRebateCostSum() == null){
                                touFangEntity.setRebateCostSum(BigDecimal.ZERO);
                            }

                            if (touFangEntity.getActivateSum() == null){
                                touFangEntity.setActivateSum(0L);
                            }

                            touFangEntity.setActivateOther(touFangAllOfflineTecentEntity.getActivate() + touFangEntity.getActivateOther());
                            touFangEntity.setRebateCostOther(touFangEntity.getRebateCostOther().add(touFangAllOfflineTecentEntity.getRebateCost()));
                            touFangEntity.setRebateCostSum(touFangEntity.getRebateCostSum()
                                    .add(touFangAllOfflineTecentEntity.getRebateCost()));

                            touFangEntity.setActivateSum(touFangEntity.getActivateSum() + touFangAllOfflineTecentEntity.getActivate());
                        }
                    }else if ("ios".equals(touFangEntity.getOs())){
                        if (touFangOfflineEntityIos != null) {
                            log.info("查询到线下的IOS历史数据,Mapping Add....");
                            // 对数增加列
                            touFangEntity.setActivateOther(Long.valueOf(touFangOfflineEntityIos.getDeviceNum()));
                            touFangEntity.setRebateCostOther(BigDecimal.valueOf(touFangOfflineEntityIos.getToufangAmount()));
                            touFangEntity.setRebateCostSum(touFangEntity.getRebateCost()
                                    .add(BigDecimal.valueOf(touFangOfflineEntityIos.getToufangAmount())));

                            touFangEntity.setActivateSum(touFangEntity.getActivate() + touFangOfflineEntityIos.getDeviceNum());
                        }
                        if (touFangKuaishouIos != null){
                            log.info("查询到线下Kuaishou的IOS历史数据,Mapping Add....");
                            if (touFangEntity.getActivateOther() == null){
                                touFangEntity.setActivateOther(0L);
                            }
                            if (touFangEntity.getRebateCostOther() == null){
                                touFangEntity.setRebateCostOther(BigDecimal.ZERO);
                            }

                            if (touFangEntity.getRebateCostSum() == null){
                                touFangEntity.setRebateCostSum(BigDecimal.ZERO);
                            }

                            if (touFangEntity.getActivateSum() == null){
                                touFangEntity.setActivateSum(0L);
                            }

                            // 对数增加列
                            touFangEntity.setActivateOther(touFangEntity.getActivateOther() + touFangKuaishouIos.getActivate());
                            touFangEntity.setRebateCostOther(touFangEntity.getRebateCostOther().add(touFangKuaishouIos.getRebateCost()));
                            touFangEntity.setRebateCostSum(touFangEntity.getRebateCost()
                                    .add(touFangKuaishouIos.getRebateCost()));

                            touFangEntity.setActivateSum(touFangEntity.getActivate() + touFangKuaishouIos.getActivate());
                        }
                        if (toufangTecentIos != null){
                            log.info("查询到线下Tecent的IOS历史数据,Mapping Add....");
                            if (touFangEntity.getActivateOther() == null){
                                touFangEntity.setActivateOther(0L);
                            }
                            if (touFangEntity.getRebateCostOther() == null){
                                touFangEntity.setRebateCostOther(BigDecimal.ZERO);
                            }

                            if (touFangEntity.getRebateCostSum() == null){
                                touFangEntity.setRebateCostSum(BigDecimal.ZERO);
                            }

                            if (touFangEntity.getActivateSum() == null){
                                touFangEntity.setActivateSum(0L);
                            }

                            // 对数增加列
                            touFangEntity.setActivateOther(touFangEntity.getActivateOther() + toufangTecentIos.getActivate());
                            touFangEntity.setRebateCostOther(touFangEntity.getRebateCostOther().add(toufangTecentIos.getRebateCost()));
                            touFangEntity.setRebateCostSum(touFangEntity.getRebateCost()
                                    .add(toufangTecentIos.getRebateCost()));

                            touFangEntity.setActivateSum(touFangEntity.getActivate() + toufangTecentIos.getActivate());
                        }
                    }
                });

                // 先清空当日计算结果
                bpToufangResultService.delResultByDay(dateDate);
                // 重新插入
                bpToufangResultService.saveBatch(entities);

                Date now = new Date();
                List<TouFangAllOfflineEntity> allOfflineEntities = touFangEntities.parallelStream()
                        .map(touFangEntity -> {
                            TouFangAllOfflineEntity entity = new TouFangAllOfflineEntity();
                            entity.setAppName(touFangEntity.getAppName());
                            entity.setActivate(touFangEntity.getActivate());
                            entity.setRebateCost(touFangEntity.getRebateCost());
                            entity.setLogday(DateUtil.stringToDate(touFangEntity.getDataDate()));
                            entity.setOs(touFangEntity.getOs());

                            entity.setAppGroup(touFangEntity.getAppGroup());
                            entity.setCreateTime(now);
                            entity.setUpdateTime(now);
                            entity.setChannelName("toutiao");
                            entity.setCods(touFangEntity.getCods());
                            return entity;
                        }).collect(Collectors.toList());

                touFangOfflineMapper.delByLogDay(dateDate);
                touFangOfflineMapper.insertBatch(allOfflineEntities);


                if (touFangAllOfflineEntities.size() > 0) {
                    touFangOfflineMapper.delByLogDayKuaishou(dateDate);
                    touFangAllOfflineEntities.parallelStream().forEach(touFangAllOfflineEntity -> {
                        Date tx = new Date();
                        touFangAllOfflineEntity.setChannelName("kuaishou");
                        touFangAllOfflineEntity.setOs(touFangAllOfflineEntity.getOs());
                        ProductEntity entity = nameProductMap.get(touFangAllOfflineEntity.getAppName());
                        if (entity != null) {
                            touFangAllOfflineEntity.setAppGroup(entity.getProductGroup());
                        }
                        touFangAllOfflineEntity.setUpdateTime(tx);
                        touFangAllOfflineEntity.setCreateTime(tx);
                    });
                    touFangOfflineMapper.insertBatch(touFangAllOfflineEntities);
                }

                if (touFangAllOfflineTecentEntities.size() > 0){
                    touFangOfflineMapper.delByLogDayTecent(dateDate);
                    touFangAllOfflineTecentEntities.parallelStream().forEach(touFangAllOfflineEntity -> {
                        Date tx = new Date();
                        touFangAllOfflineEntity.setChannelName("guangdiantong");
                        touFangAllOfflineEntity.setOs(touFangAllOfflineEntity.getOs());
                        ProductEntity entity = nameProductMap.get(touFangAllOfflineEntity.getAppName());
                        if (entity != null) {
                            touFangAllOfflineEntity.setAppGroup(entity.getProductGroup());
                        }
                        touFangAllOfflineEntity.setUpdateTime(tx);
                        touFangAllOfflineEntity.setCreateTime(tx);
                    });
                    touFangOfflineMapper.insertBatch(touFangAllOfflineTecentEntities);
                }

                touFangOfflineEntitiesAndroid.addAll(touFangOfflineEntitiesIos);
                List<TouFangAllOfflineEntity> touFangAllOfflineOthers = new ArrayList<>();
                if (touFangOfflineEntitiesAndroid.size() > 0){
                    touFangOfflineMapper.delByLogDayNotOther(dateDate);
                    Date finalCrashDate = crashDate;
                    touFangOfflineEntitiesAndroid.parallelStream().forEach(touFangOfflineEntity -> {
                        Date tx = new Date();
                        TouFangAllOfflineEntity touFangAllOfflineEntity = new TouFangAllOfflineEntity();
                        touFangAllOfflineEntity.setLogday(finalCrashDate);
                        touFangAllOfflineEntity.setAppName(touFangOfflineEntity.getProductName());
                        touFangAllOfflineEntity.setChannelName(touFangOfflineEntity.getChannelName());
                        touFangAllOfflineEntity.setOs(touFangOfflineEntity.getOs());
                        ProductEntity entity = nameProductMap.get(touFangOfflineEntity.getProductName());
                        if (entity != null) {
                            touFangAllOfflineEntity.setAppGroup(entity.getProductGroup());
                        }
                        touFangAllOfflineEntity.setCods(new BigDecimal(touFangOfflineEntity.getToufangAmount()));
                        touFangAllOfflineEntity.setRebateCost(new BigDecimal(touFangOfflineEntity.getToufangAmount()));
                        touFangAllOfflineEntity.setActivate(touFangOfflineEntity.getDeviceNum().longValue());
                        touFangAllOfflineEntity.setUpdateTime(tx);
                        touFangAllOfflineEntity.setCreateTime(tx);
                        touFangAllOfflineOthers.add(touFangAllOfflineEntity);
                    });
                    touFangOfflineMapper.insertBatch(touFangAllOfflineOthers);
                }
            }
        }catch (Exception e){
            log.error("Query EX:",e);
            throw e;
        }
        log.info("结束投放数据同步...");
    }

}
