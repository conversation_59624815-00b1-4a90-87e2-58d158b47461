package com.coohua.user.event.biz.dc.dto;

import com.alibaba.fastjson.JSON;
import com.coohua.user.event.biz.util.Strings;
import lombok.Data;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2022/10/11
 */
@Data
public class RealtimeCsjRulesDto {
    private Integer id;
    private String name;
    private Integer csjRate;
    private Integer uv;
    private Integer pv;
    private Integer avgEcpm;
    private List<ActionLimit> actionTask;
    private String createTime;
    private String updateTime;
    private String channelStart;
    private Integer includeType;
    private Integer modelType;


    public List<String> getChannelStartList(){
        if (Strings.isEmpty(this.channelStart)){
            return new ArrayList<>();
        }
        return Arrays.stream(this.channelStart.split(",")).filter(Strings::noEmpty).collect(Collectors.toList());
    }

    @Data
    public static class ActionLimit{
        private Integer limitPv;
        private Integer limitAvgEcpm;
    }

}
