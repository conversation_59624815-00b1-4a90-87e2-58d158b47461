package com.coohua.user.event.biz.dc.mapper;

import com.coohua.user.event.biz.dc.entity.GrayUserEntity;
import org.apache.ibatis.annotations.Insert;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2021/8/5
 */
public interface GrayUserMapper {

    @Insert({
            "<script>",
            "INSERT INTO ads.gray_user_exposure (" +
                    "logday," +
                    "user_id," +
                    "product," +
                    "product_name," +
                    "product_group," +
                    "os," +
                    "exposure_times," +
                    "config_times," +
                    "create_time," +
                    "update_time" +
                    ")" +
                    "VALUES",
            "<foreach collection='grayUserList'  item='grayUser' separator=','>" ,
            "(" +
                    "#{grayUser.logday}," +
                    "#{grayUser.userId}," +
                    "#{grayUser.product}," +
                    "#{grayUser.productName}," +
                    "#{grayUser.productGroup}," +
                    "#{grayUser.os}," +
                    "#{grayUser.exposureTimes}," +
                    "#{grayUser.configTimes}," +
                    "#{grayUser.createTime}," +
                    "#{grayUser.updateTime}" +
            ")",
            "</foreach>",
            "</script>",
    })
    int batchInsertGrayUser(@Param("grayUserList") List<GrayUserEntity> grayUserEntityList);
}
