package com.coohua.user.event.biz.service.req;

import com.coohua.user.event.biz.dc.entity.CdpAdData;
import lombok.Data;

/**
 * <AUTHOR>
 * @since 2023/6/15
 */
@Data
public class CdpAdDataRequest {

    private String appId;
    private String date;
    private String applicationName;
    private String applicationPackage;
    private Integer activeUserIpu;
    private Integer activeUserMotivationalVideoWatchTimesUserNum;
    private Integer activeUserNum;
    private Integer expend;
    private Integer keyActionCompletedUserNum;
    private Integer motivationalVideoEcpm;
    private Integer motivationalVideoIncome;
    private Integer newUserIpu;
    private Integer newUserMotivationalVideoWatchOneTimesUserNum;
    private Integer newUserNum;
    private Integer totalActiveUserMotivationalVideoWatchOneTimes;
    private Integer totalEcpm;
    private Integer totalIncome;
    private Integer totalNewUserMotivationalVideoWatchTimes;
    private Integer totalWithdrawAmount;


    public CdpAdDataRequest build(CdpAdData cdpAdData){
        CdpAdDataRequest  cdpAdDataRequest = new CdpAdDataRequest();
        cdpAdDataRequest.setAppId(cdpAdData.getAppId());
        cdpAdDataRequest.setDate(cdpAdData.getLogday());
        cdpAdDataRequest.setApplicationName(cdpAdData.getApplicationName());
        cdpAdDataRequest.setApplicationPackage(cdpAdData.getApplicationPackage());
        cdpAdDataRequest.setActiveUserIpu(cdpAdData.getActiveUserLpu());
        cdpAdDataRequest.setActiveUserMotivationalVideoWatchTimesUserNum(cdpAdData.getNewUserMotivationalVideoWatchUserNum());
        cdpAdDataRequest.setActiveUserNum(cdpAdData.getActiveUserNum());
        cdpAdDataRequest.setExpend(cdpAdData.getExpend());
        cdpAdDataRequest.setKeyActionCompletedUserNum(cdpAdData.getKeyActionCompletedNum());
        cdpAdDataRequest.setMotivationalVideoEcpm(cdpAdData.getMotivationalVideoEcpm());
        cdpAdDataRequest.setMotivationalVideoIncome(cdpAdData.getMotivationalVodepIncome());
        cdpAdDataRequest.setNewUserIpu(cdpAdData.getNewUserLpu());
        cdpAdDataRequest.setNewUserMotivationalVideoWatchOneTimesUserNum(cdpAdData.getNewUserMotivationalVideoWatchUserNum());
        cdpAdDataRequest.setNewUserNum(cdpAdData.getNewUserNum());
        cdpAdDataRequest.setTotalActiveUserMotivationalVideoWatchOneTimes(cdpAdData.getTotalActiveUserMotivationalVideoTimes());
        cdpAdDataRequest.setTotalEcpm(cdpAdData.getTotalEcpm());
        cdpAdDataRequest.setTotalIncome(cdpAdData.getTotalIncome());
        cdpAdDataRequest.setTotalWithdrawAmount(cdpAdData.getTotalWithdrawAmount());
        cdpAdDataRequest.setTotalNewUserMotivationalVideoWatchTimes(cdpAdData.getTotalNewUserMotivationalVideoWatchTimes());

        return cdpAdDataRequest;
    }
}
