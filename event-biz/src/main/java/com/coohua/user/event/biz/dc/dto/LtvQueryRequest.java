package com.coohua.user.event.biz.dc.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2021/7/7
 */
@Data
@ApiModel(description = "LTV查询请求")
public class LtvQueryRequest {

    @ApiModelProperty(value = "查询日期",required = true,example = "2021-07-05")
    @NotNull
    private String logday;

    @ApiModelProperty(value = "产品埋点名称",example = "wdfd")
    private String product;

    @ApiModelProperty(value = "查询类型 1-按照素材 2-按照广告主",required = true)
    @NotNull(message = "查询类型不能为空")
    private Integer queryType;

    @ApiModelProperty(value = "广告主id")
    @NotBlank(message = "广告主id不能为空")
    private String advertiserId;

    @ApiModelProperty(value = "投放位置")
    private String toufangSite;

    @ApiModelProperty(value = "投放类型")
    private String toufangType;

    @ApiModelProperty(value = "视频名称")
    private String videoName;
}
