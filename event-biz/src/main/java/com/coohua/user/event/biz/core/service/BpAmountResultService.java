package com.coohua.user.event.biz.core.service;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.coohua.user.event.biz.core.entity.BpAmountResult;
import com.coohua.user.event.biz.core.entity.BpToufangResult;
import com.coohua.user.event.biz.core.mapper.BpAmountResultMapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.coohua.user.event.biz.user.entity.UserInfoEntity;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

/**
* <p>
    * 提现结果表 服务实现类
    * </p>
*
* <AUTHOR>
* @since 2020-09-03
*/
@Slf4j
@Service
public class BpAmountResultService extends ServiceImpl<BpAmountResultMapper, BpAmountResult> {


    public void saveBatch(List<UserInfoEntity> userInfoEntities){
        Date now = new Date();
        List<BpAmountResult> bpAmountResults = userInfoEntities
                .parallelStream()
                .map(userInfoEntity -> {
                    BpAmountResult result = new BpAmountResult();
                    result.setAmount(userInfoEntity.getAmount());
                    result.setAppName(userInfoEntity.getRemark());
                    result.setAppGroup(userInfoEntity.getRemarkGroup());
                    result.setOs(userInfoEntity.getSystem());
                    // 切换
                    if ("一念修仙2".equals(userInfoEntity.getRemark()) && "ios".equals(userInfoEntity.getSystem())){
                        result.setAppName("世外人家2");
                    }
                    result.setPkgId(userInfoEntity.getPkgId());
                    result.setDateStr(userInfoEntity.getDateStr());
                    result.setCreateTime(now);
                    result.setUpdateTime(now);
                    return result;
                })
                .collect(Collectors.toList());
        saveBatch(bpAmountResults,bpAmountResults.size());
    }

    public void delResultByDayAndApp(String logDay){
        QueryWrapper<BpAmountResult> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda()
                .eq(BpAmountResult::getDateStr, logDay);
        remove(queryWrapper);
    }

    public List<BpAmountResult> queryBpAmountResultData(String logDay){
        log.info("查询 bp_toufang_result --{}",logDay);
        return lambdaQuery().eq(BpAmountResult::getDateStr,logDay).list();
    }
}
