package com.coohua.user.event.biz.service.bean;

import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2021/11/22
 */
@Data
public class GlobalBlackUserBean {
    // 拉黑的App
    private List<Integer> targetAppList;

    // 拉黑对象
    private String targetId;

    /**
     * 1-device_id
     * 2-user_id
     * 3-union_id
     */
    private Integer targetType;

    // 是否全局拉黑
    private Boolean isGlobalGary;

    private String reason;

    private Date grayTime;
}
