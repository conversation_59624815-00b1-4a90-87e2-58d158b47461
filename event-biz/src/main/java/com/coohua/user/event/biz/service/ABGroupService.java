package com.coohua.user.event.biz.service;

import com.alibaba.fastjson.JSON;
import com.coohua.user.event.biz.click.entity.UserAvgEcpmEntity;
import com.coohua.user.event.biz.click.service.ClickHouseService;
import com.coohua.user.event.biz.dc.mapper.AbUserEcpmMapper;
import com.coohua.user.event.biz.util.Lists;
import com.coohua.user.event.biz.util.RedisUtil;
import com.ctrip.framework.apollo.spring.annotation.ApolloJsonValue;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;
import top.zrbcool.pepper.boot.jediscluster.JedisClusterClient;
import top.zrbcool.pepper.boot.jediscluster.JedisClusterClientRefer;

import javax.annotation.Resource;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2021/8/18
 */
@Slf4j
@Service
@ConditionalOnProperty(name = "spring.application.name",havingValue = "user-event-job")
public class ABGroupService {

    @Autowired
    private ClickHouseService clickHouseService;

    @JedisClusterClientRefer(namespace = "ap-cluster")
    private JedisClusterClient jedisClusterClient;

    @ApolloJsonValue("${ab.app.ecpm.list}")
    private List<Integer> abAppList;

    @Resource
    private AbUserEcpmMapper abUserEcpmMapper;

    private final static Integer DEFAULT_PAGE = 1000;

    public void queryBaseData(){
        if (Lists.noEmpty(abAppList)){
            abAppList.forEach(this::doAppQuery);
        }
    }

    private void doAppQuery(Integer appId){
        String appIdStr = String.valueOf(appId);

        Integer count = clickHouseService.countYesterdayUser(appIdStr);
        if (count > DEFAULT_PAGE){
            int page = count / DEFAULT_PAGE;
            for(int i = 0;i <= page; i++){
                queryAndInsertToRedis(appIdStr,i);
            }
        }else {
            queryAndInsertToRedis(appIdStr,0);
        }
    }

    private void queryAndInsertToRedis(String appId,Integer page){
        Integer offset = page * DEFAULT_PAGE;
        List<UserAvgEcpmEntity> userAvgEcpmEntityList = clickHouseService.queryYesterdayUserEcpm(appId,offset,DEFAULT_PAGE);
//        String[] result = userAvgEcpmEntityList.stream()
//                .map(userAvgEcpmEntity ->
//                        RedisUtil.buildAbUserKey(appId,userAvgEcpmEntity.getUserId()) + ","
//                                + userAvgEcpmEntity.getAvgEcpm())
//                .collect(Collectors.joining(",")).split(",");
//        jedisClusterClient.mset(result);

//        List<>
    }
}
