package com.coohua.user.event.biz.dc.service;

import com.coohua.user.event.biz.dc.dto.LtvAllQueryResponse;
import com.coohua.user.event.biz.dc.entity.DailyLtvAllAutoCalcuSub;
import com.coohua.user.event.biz.dc.mapper.DailyLtvAllAutoCalcuSubMapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;

import java.util.List;

/**
* <p>
    *  服务实现类
    * </p>
*
* <AUTHOR>
* @since 2021-08-24
*/
@Service
public class DailyLtvAllAutoCalcuSubService extends ServiceImpl<DailyLtvAllAutoCalcuSubMapper, DailyLtvAllAutoCalcuSub> {
    List<LtvAllQueryResponse> queryAll(String logday){
        return this.baseMapper.queryAllSubLtv(logday);
    }
}
