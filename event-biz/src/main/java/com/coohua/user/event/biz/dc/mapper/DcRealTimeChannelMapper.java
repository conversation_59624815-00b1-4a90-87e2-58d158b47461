package com.coohua.user.event.biz.dc.mapper;

import com.coohua.user.event.biz.dc.entity.RealtimeChannelAppVersionTempEntity;
import org.apache.ibatis.annotations.Delete;
import org.apache.ibatis.annotations.Insert;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2020/9/11
 */
public interface DcRealTimeChannelMapper {

    @Delete({"delete from ads.realtime_channel_app_version_temp where logday1 = #{logDay} and product_name = #{product}"})
    int deleteProductByLogDay(@Param("logDay")String logDay, @Param("product")String product);

    @Insert({
            "<script>",
            "INSERT INTO ads.realtime_channel_app_version_temp (" +
                    "logday1," +
                    "logday2," +
                    "product_group," +
                    "product_name," +
                    "os1," +
                    "os2," +
                    "app_version1," +
                    "app_version2," +
                    "channel1," +
                    "channel2," +
                    "device_id1," +
                    "device_id2" +
                    ")" +
                    "VALUES",
            "<foreach collection='realtimeChannelAppVersionTempEntityList'  item='realtimeChannelAppVersionTempEntity' separator=','>" ,
            "(" +
                    "#{realtimeChannelAppVersionTempEntity.logday1}," +
                    "#{realtimeChannelAppVersionTempEntity.logday2}," +
                    "#{realtimeChannelAppVersionTempEntity.productGroup}," +
                    "#{realtimeChannelAppVersionTempEntity.productName}," +
                    "#{realtimeChannelAppVersionTempEntity.os1}," +
                    "#{realtimeChannelAppVersionTempEntity.os2}," +
                    "#{realtimeChannelAppVersionTempEntity.appVersion1}," +
                    "#{realtimeChannelAppVersionTempEntity.appVersion2}," +
                    "#{realtimeChannelAppVersionTempEntity.channel1}," +
                    "#{realtimeChannelAppVersionTempEntity.channel2}," +
                    "#{realtimeChannelAppVersionTempEntity.deviceId1}," +
                    "#{realtimeChannelAppVersionTempEntity.deviceId2}",
            ")",
            "</foreach>",
            "</script>",
    })
    int insertBatch(@Param("realtimeChannelAppVersionTempEntityList") List<RealtimeChannelAppVersionTempEntity> realtimeChannelAppVersionTempEntityList);
}
