package com.coohua.user.event.biz.util;

import com.coohua.user.event.biz.click.entity.ProductEntity;
import com.coohua.user.event.biz.click.service.ClickHouseService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2021/9/25
 */
@Slf4j
@Service
@ConditionalOnProperty(name = "env",havingValue = "pro")
public class AppConfig {

    @Autowired
    private ClickHouseService clickHouseService;

    public static Map<String, ProductEntity> productEnMap = new HashMap<>();
    public static Map<Long, ProductEntity> appIdMap = new HashMap<>();
    public static Set<String> productEnSet = new HashSet<>();

    @PostConstruct
    @Scheduled(cron = "0 0 * * * ?")
    public void refreshProductMap(){
        log.info("START REFRESH PRODUCT MAP...");
        List<ProductEntity> productEntityList = clickHouseService.queryAllProduct();
        productEnMap = productEntityList.stream().collect(Collectors.toMap(ProductEntity::getProduct,l->l,(l1,l2)-> l2));
        appIdMap = productEntityList.stream().collect(Collectors.toMap(p -> Long.valueOf(p.getId()),l->l,(l1,l2)->l2));
        productEnSet = productEntityList.stream().map(ProductEntity::getProduct).collect(Collectors.toSet());
        log.info("COMPLETE REFRESH PRODUCT MAP...");
    }
}
