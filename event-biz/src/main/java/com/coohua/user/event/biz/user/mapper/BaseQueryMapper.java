package com.coohua.user.event.biz.user.mapper;

import com.coohua.user.event.biz.user.entity.AppWithdrawConfEntity;
import com.coohua.user.event.biz.user.entity.UserInfoEntity;
import com.coohua.user.event.biz.user.entity.WechatApp;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2020/9/3
 */
public interface BaseQueryMapper {

    @Select({"select * from bp_user.wechat_app"})
    List<WechatApp> queryAllPkgConf();

    @Select({
            "<script>",
            " SELECT tb1.* ",
            " FROM ( SELECT ",
                    " sum(amount / 100) as amount, ",
                    " wechat_id as pkg_id, ",
                    " sys as system, ",
                    " ct as date_str from (" +
                    " SELECT" +
                    "   amount," +
                    "  t1.wechat_id," +
                    "  CASE t2.os WHEN 0 THEN" +
                    "  'android'" +
                    " WHEN 1 THEN" +
                    "  'IOS'" +
                    " else 'wmin' END  as 'sys'," +
                    " FROM_UNIXTIME(t1.update_time/1000 ,'%Y-%m-%d') as ct" +
                    " FROM" +
                    "  (select * from ${baseTable} where status = 5 and create_time &gt; #{timestamp} and create_time &lt; #{timestampToday})t1 " +
                    " JOIN bp_user.`user_meta` t2 ON t1.user_id = t2.user_id and t1.wechat_id = t2.pkg_id" +
                    " having ct=#{day} "+
                    " )tb group by ct,sys" +
                    " ) tb1",
            "</script>",
    })
    List<UserInfoEntity> queryAmountInfo(@Param(value = "baseTable") String baseTable,
                                         @Param("timestamp") Long timestamp,
                                         @Param("timestampToday") Long timestampToday,
                                         @Param("day") String day);
}
