package com.coohua.user.event.biz.service;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.coohua.bp.user.remote.api.UserRPC;
import com.coohua.bp.user.remote.dto.CommonHeaderDTO;
import com.coohua.bp.user.remote.dto.UserDTO;
import com.coohua.user.event.biz.click.entity.ProductEntity;
import com.coohua.user.event.biz.click.entity.UserScoreEntity;
import com.coohua.user.event.biz.enums.ActiveChannel;
import com.coohua.user.event.biz.enums.GuiyingType;
import com.coohua.user.event.biz.service.bean.OcpcEvent;
import com.coohua.user.event.biz.service.bean.ToutiaoClick;
import com.coohua.user.event.biz.service.bean.UserActive;
import com.coohua.user.event.biz.service.bean.UserEventReq;
import com.coohua.user.event.biz.util.AppConfig;
import com.coohua.user.event.biz.util.HBaseUtils;
import com.coohua.user.event.biz.util.RedisKeyConstants;
import com.coohua.user.event.biz.util.Strings;
import com.ctrip.framework.apollo.spring.annotation.ApolloJsonValue;
import com.weibo.api.motan.config.springsupport.annotation.MotanReferer;
import lombok.Getter;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.apache.hadoop.hbase.TableName;
import org.apache.hadoop.hbase.client.Connection;
import org.apache.hadoop.hbase.client.Get;
import org.apache.hadoop.hbase.client.Result;
import org.apache.hadoop.hbase.client.Table;
import org.apache.hadoop.hbase.util.Bytes;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;


/**
 * <AUTHOR>
 * @since 2021/10/26
 */
@Slf4j
@Service
public class TFUserService {


    /**
     * OCPC HBASE连接
     */
    @Resource(name = "linDormConnection")
    private Connection ocpcHadoopConnection;

    @Resource(name = "toutiaoClickConnection")
    private Connection toutiaoClickConnection;

    /**
     * 用户HBase连接
     */
    @Resource(name = "hbaseConnection")
    private Connection hbaseConnection;

    @MotanReferer(basicReferer = "bp-userBasicRefererConfigBean")
    private UserRPC userRPC;

    @ApolloJsonValue("${use.device.gui.product.list}")
    private List<String> useDeviceGuiProductList;
    @Value("${use.device.gui.product.enable}")
    private Boolean isDeviceGuiEnabled;
    @Value("${use.device.gui.log.enable}")
    public Boolean isDeviceGuiLogEnabled;
    @Value("${enable.device.gui.cyan:false}")
    public Boolean enableDeviceGyCyan;

    /**
     * 归因key
     */
    private static final String ATTRIBUTE_KEY_FORMATTER ="ck:did:%s:%s:%s";

    private static final String UA_KEY = "%s@%s";

    private static final String USER_ACTIVE = "userAcitveByUser";

    private static final String OCPC_EVENT_TABLE = "OcpcEvent";
    private static final String OCPC_EVENT_KEY = "event:active:%s:%s:userId:%s";

    /**
     * 是否使用设备归因
     * 配置的产品列表为空或者包含当前产品，使用设备归因，否则走原来逻辑
     * @param product
     * @return
     */
    public boolean isUseDeviceGui(String product) {
        if (!isDeviceGuiEnabled) return false;

        return CollectionUtils.isEmpty(useDeviceGuiProductList) || useDeviceGuiProductList.contains(product);
    }

    @Getter
    @Setter
    private static class OcpcClick {
        /**
         * 渠道来源
         */
        private String dsp;
    }


    /**
     * click事件表名
     */
    private static final String CLICK_TABLE_NAME = "ToutiaoClick";

    /**
     * 实时判断OCPC
     * @param userScoreEntity 用户分值实体
     * @return TRUE - 非投放用户
     */
    public boolean filterTfUser(UserScoreEntity userScoreEntity){
        try {
            Integer appId = AppConfig.productEnMap.get(userScoreEntity.getProduct()).getId();
            return shouldShieldUser(userScoreEntity.getProduct(),userScoreEntity.getOs(),
                    userScoreEntity.getDeviceId(),
                    appId, Long.valueOf(userScoreEntity.getUserId()));
        }catch (Exception e){
            log.error("Query ex:{}",e.getMessage());
            return true;
        }
    }

    /**
     * 用户检查投放来源
     * @param name productEnName
     * @param os 系统
     * @param deviceId 设备id
     * @param appId appId
     * @param realUserId 真实的用户id
     * @return 是否投放用户 false-投放用户 true-非投放用户
     */
    public boolean shouldShieldUser(String name, String os, String deviceId, Integer appId, Long realUserId) {
        try {

            if (!isOcpcByUserId(appId, realUserId)) {
                return true;
            }

            if (name == null){
                return false;
            }
            if (org.apache.commons.lang3.StringUtils.isBlank(deviceId) || "0".equals(deviceId) || "0000-0000-0000-0000".equals(deviceId)) {
                return false;
            }
            String hbaseKey =  String.format(ATTRIBUTE_KEY_FORMATTER, name, os, deviceId);
            Pair<Boolean, byte[]> result = HBaseUtils.searchDataFromHadoopWithoutHash(ocpcHadoopConnection, CLICK_TABLE_NAME, hbaseKey);

            if (result.getRight() == null) {
                return true;
            }

            OcpcClick click = JSONObject.parseObject(result.getRight(), OcpcClick.class);
            String dsp = click.dsp;
            boolean isOcpc = "toutiao".equals(dsp) || "kuaishou".equals(dsp) || "guangdiantong".equals(dsp);
            return !isOcpc;
        }catch (Exception e){
            log.error("查询用户投放/非投放检查异常...", e);
            return false;
        }
    }

    public String queryByCaid(String product,String caid){
        String queryKey = String.format(UA_KEY,caid, product);
        byte[] bytes  = HBaseUtils.searchDataFromHadoop(ocpcHadoopConnection,USER_ACTIVE,queryKey);
        if (bytes != null){
            return Bytes.toString(bytes);
        }
        return null;
    }

    public Boolean isOcpc(String deviceId,String imei,String oaid,String product){
        UserEventReq userEventReq = new UserEventReq();
        userEventReq.setProduct(product);
        userEventReq.setOs("android");
        userEventReq.setOaid(oaid);
        userEventReq.setOaid2(imei);
        userEventReq.setAndroidId(imei);
        userEventReq.setOcpcDeviceId(deviceId);
        ToutiaoClick toutiaoClick = getHbaseClickByDevice(userEventReq,"ToutiaoClickLong",toutiaoClickConnection);
        if (toutiaoClick != null){
            if (Strings.isEmpty(toutiaoClick.getDsp())){
                return false;
            }else {
                return !toutiaoClick.getDsp().startsWith("INNER");
            }
        }
        return false;

    }

    private ToutiaoClick getHbaseClickByDevice(UserEventReq request, String tableName, Connection connection) {
        ToutiaoClick click = null;
        try (Table table = connection.getTable(TableName.valueOf(tableName))) {
            String product = request.getProduct();
            if (StringUtils.isEmpty(product)) {
                log.warn("hbaseGetClick product is null ! {}", JSONObject.toJSONString(request));
                return click;
            }

            /**
             * 先按设备号查询
             */
            if (StringUtils.isNotBlank(request.getOcpcDeviceId())) {
                Get get = new Get(Bytes.toBytes(RedisKeyConstants.getClickDeviceKey(product, request.getOs(), request.getOcpcDeviceId())));
                Result res = table.get(get);
                String s = HBaseUtils.getCellValStr(res);
                if (StringUtils.isNotBlank(s)) {
                    click = JSONObject.parseObject(s, ToutiaoClick.class);
                }
            }

            /**
             * click不存在再查oaId
             */
            if (click == null && StringUtils.isNotBlank(request.getOaid())) {
                Get get = new Get(Bytes.toBytes(RedisKeyConstants.getClickOaIdKey(product, request.getOs(), request.getOaid())));
                Result res = table.get(get);
                String s = HBaseUtils.getCellValStr(res);
                if (StringUtils.isNotBlank(s)) {
                    click = JSONObject.parseObject(s, ToutiaoClick.class);
                }
            }

            if (click == null && StringUtils.isNotBlank(request.getOaid2())) {
                Get get = new Get(Bytes.toBytes(RedisKeyConstants.getClickOaId2Key(product, request.getOs(), request.getOaid2())));
                Result res = table.get(get);
                String s = HBaseUtils.getCellValStr(res);
                if (StringUtils.isNotBlank(s)) {
                    click = JSONObject.parseObject(s, ToutiaoClick.class);
                }
            }

            /**
             * androidId查
             */
            if (click == null && StringUtils.isNotBlank(request.getAndroidId())) {
                Get get = new Get(Bytes.toBytes(RedisKeyConstants.getClickAndroidIdKey(product, request.getOs(), request.getAndroidId())));
                Result res = table.get(get);
                String s = HBaseUtils.getCellValStr(res);
                if (StringUtils.isNotBlank(s)) {
                    click = JSONObject.parseObject(s, ToutiaoClick.class);
                    log.info("androidId归因成功 " + product);
                }
            }
        } catch (Exception e) {
            log.error("hbaseGetClick error ", e);
        }
        return click;
    }

    /**
     * 用户渠道信息表前缀
     */
    private static final String USER_CHANNEL_TABLE_PREFIX = "user_channel_";

    private boolean isOcpcByUserId(Integer appId, Long realUserId) {

        if (realUserId == null) {
            return true;
        }

        String activeChannelTableName = USER_CHANNEL_TABLE_PREFIX + appId;
        byte[] result = HBaseUtils.searchDataFromHadoop(hbaseConnection, activeChannelTableName, realUserId.toString());
        if (result == null) {
            return true;
        }

        String content = Bytes.toString(result);
        String channel = org.apache.commons.lang3.StringUtils.substringBefore(content, ",");
        return "toutiao".equals(channel) || "kuaishou".equals(channel) || "guangdiantong".equals(channel);
    }

    private static final Integer BATCH_SIZE = 1000;

    public List<UserScoreEntity> filterOCPCUser(List<UserScoreEntity> userScoreEntityList){
        if (userScoreEntityList.size() < BATCH_SIZE){
            return batchQuery(userScoreEntityList);
        }else {
            List<UserScoreEntity> userScoreEntities =  new ArrayList<>();
            for (int i =0 ; i <= userScoreEntityList.size()/BATCH_SIZE ;i++){
                userScoreEntities.addAll(batchQuery(userScoreEntityList.stream()
                        .skip(i*BATCH_SIZE).limit(BATCH_SIZE).collect(Collectors.toList())));
            }
            return userScoreEntities;
        }
    }

    private List<UserScoreEntity> batchQuery(List<UserScoreEntity> userScoreEntityList){
        // 投放用户channel 过滤
        /*
        List<UserScoreEntity> filterUserList = batchQueryUser(userScoreEntityList);
        filterUserList = filterUserList.stream().filter(r -> !(
                StringUtils.isBlank(r.getDeviceId()) || "0".equals(r.getDeviceId()) || "0000-0000-0000-0000".equals(r.getDeviceId())
                )).collect(Collectors.toList());
        // 投放用户click 过滤
        filterUserList = batchQueryClick(filterUserList);
         */
        // 投放用户-UserActive过滤 -- 不再看Click + channel 的组和
        return batchQueryUserActive(userScoreEntityList);
    }

    public List<UserActive> queryUserActiveInfo(String product,List<String> userIdList){
        List<String> rowKeyList = userIdList.stream()
                .map(r ->  String.format(UA_KEY,r, product))
                .collect(Collectors.toList());
        List<UserActive> tfList = new ArrayList<>();

        Map<String,String> resultMap  = HBaseUtils.searchDataFromHadoopList(ocpcHadoopConnection,USER_ACTIVE,rowKeyList);
        for (String userId: userIdList) {
            String key = HBaseUtils.hashRowKeyByLastCharacter(String.format(UA_KEY, userId, product));
            String result = resultMap.get(key);
            if (Strings.noEmpty(result)) {
                UserActive userActive = JSONObject.parseObject(result, UserActive.class);
                tfList.add(userActive);
            }
        }
        return tfList;
    }

    public List<String> querytfUser(String product,List<String> userIdList,String trace){
        List<String> rowKeyList = userIdList.stream()
                .map(r ->  String.format(UA_KEY,r, product))
                .collect(Collectors.toList());

        List<String> tfList = new ArrayList<>();
        Map<String,String> resultMap  = HBaseUtils.searchDataFromHadoopList(ocpcHadoopConnection,USER_ACTIVE,rowKeyList);
        for (String userId: userIdList){
            String key = HBaseUtils.hashRowKeyByLastCharacter(String.format(UA_KEY,userId, product));
            String result = resultMap.get(key);
            if (Strings.noEmpty(result)){
                UserActive userActive = JSONObject.parseObject(result, UserActive.class);
                ActiveChannel activeChannel = ActiveChannel.getByDesc(userActive.getSource());
                if (ActiveChannel.isOCPCChanel(activeChannel)){
                    tfList.add(userId);
                }
            }
        }

        if (tfList.size() > 0) {
            log.info("{} ==> Find For {},Exist {} TF USER", trace, userIdList.size(), tfList.size());
        }
        return tfList;
    }

    /**
     * 查询非投放用户列表
     * @param product 产品埋点名称
     * @param userIdList 用户列表
     * @return 非投放用户列表
     */
    public List<String> queryNtfUser(String product,List<String> userIdList,String trace){
        List<String> rowKeyList = userIdList.stream()
                .map(r ->  String.format(UA_KEY,r, product))
                .collect(Collectors.toList());

        List<String> noTfList = new ArrayList<>();
        Map<String,String> resultMap  = HBaseUtils.searchDataFromHadoopList(ocpcHadoopConnection,USER_ACTIVE,rowKeyList);
        List<String> notExist = new ArrayList<>();
        for (String userId: userIdList){
            String key = HBaseUtils.hashRowKeyByLastCharacter(String.format(UA_KEY,userId, product));
            String result = resultMap.get(key);
            if (Strings.noEmpty(result)){
                UserActive userActive = JSONObject.parseObject(result,UserActive.class);
                ActiveChannel activeChannel = ActiveChannel.getByDesc(userActive.getSource());
                if (!ActiveChannel.isOCPCChanel(activeChannel)){
                    noTfList.add(userId);
                }
            }else {
                notExist.add(key);
            }
        }
        if (notExist.size() > 0) {
            log.warn("{} ==> {} Not Find in UserActive.. Skip", trace, JSON.toJSONString(notExist));
        }
        if (noTfList.size() > 0) {
            log.info("{} ==> Find For {},Exist {} NTF USER", trace, userIdList.size(), noTfList.size());
        }
        return noTfList;
    }

    /**
     * 批量查询用户设备归因
     * @param product
     * @param userIdList
     * @return
     */
    public List<OcpcEvent> queryUserDeviceGuiBatch(String product, List<String> userIdList , String os, Integer appId) {
        List<String> rowKeyList = userIdList.stream()
                .map(userId ->  String.format(OCPC_EVENT_KEY, product, os, userId))
                .collect(Collectors.toList());
        List<OcpcEvent> ocpcEventList = new ArrayList<>();

        Map<String,String> resultMap  = HBaseUtils.searchDataFromHadoopListWithNoHashRowKey(ocpcHadoopConnection, OCPC_EVENT_TABLE, rowKeyList);
        for (String userId: userIdList){
            String key = String.format(OCPC_EVENT_KEY, product, os, userId);
            String result = resultMap.get(key);
            if (Strings.noEmpty(result)){
                OcpcEvent ocpcEvent = JSONObject.parseObject(result, OcpcEvent.class);
                if (ocpcEvent.getEventType().equals(0) && !GuiyingType.packageGy.value.equals(ocpcEvent.getGyType())) {
                    ocpcEventList.add(ocpcEvent);
                }
            }
        }
        return ocpcEventList;
    }

    public UserActive queryUserActiveIfNoFundQueryRpc(String product, String userId,Long appId){
        String key1 = String.format(UA_KEY,userId, appId);
        String key2 = String.format(UA_KEY,userId, product);
        List<String> rowKeyList = new ArrayList<>();
        rowKeyList.add(key1);
        rowKeyList.add(key2);

        Map<String,String> resultMap  = HBaseUtils.searchDataFromHadoopList(ocpcHadoopConnection,USER_ACTIVE,rowKeyList);
        for (String key: rowKeyList) {
            String hashKey = HBaseUtils.hashRowKeyByLastCharacter(key);
            String result = resultMap.get(hashKey);
            if (Strings.noEmpty(result)) {
                return JSONObject.parseObject(result, UserActive.class);
            }
        }
        log.info("QueryBy Hbase notFund Use Rpc {} {}",appId,userId);
        return queryByRpc(appId,userId);
    }

//    @PostConstruct
    public void test() {
        UserActive userActive = queryUserDeviceGui("msyq", "2239693055", 1L);
//        UserActive userActive2 = queryUserDeviceGui("wgmx", "2290885688", 1L);
        System.out.println();
    }

    /**
     * 查询用户设备归因
     * @param product
     * @param userId
     * @param appId
     * @return
     */
    public static final List<String> cyGyTypeList = Arrays.asList("ip", "ipua", "iunod", "ipmd", "mac", "openid","sdkNoClick");
    public UserActive queryUserDeviceGui(String product, String userId, Long appId) {
//        UserActive userActive = queryUserActiveIfNoFundQueryRpc(product, userId, appId);
        UserActive userActive = queryQueryRpcIfNoFundUserActive(product, userId, appId);
        if (userActive == null) return null;

        String key = String.format(OCPC_EVENT_KEY, product, userActive.getOs(), userId);
        byte[] bytes = HBaseUtils.searchDataFromHadoopWithNoHashRowKey(ocpcHadoopConnection,OCPC_EVENT_TABLE, key);

        /**
         * 转为UserActive-兼容旧代码逻辑
         */
        if (bytes != null){
            OcpcEvent ocpcEvent = JSONObject.parseObject(Bytes.toString(bytes), OcpcEvent.class);
            UserActive userActiveRes = convertToUserActive(ocpcEvent);
            userActiveRes.setChannel(userActive.getChannel())
                    .setIdfa(userActive.getIdfa())
                    .setGyCaid(ocpcEvent.getCaid())
                    .setIp(ocpcEvent.getIp())
                    .setCaid(userActive.getCaid())
                    .setImei(userActive.getImei())
                    .setModel(userActive.getModel())
                    .setCreateTime(userActive.getCreateTime())
                    .setDeviceGui(true);
            // 0为归因用户
            userActiveRes.setOcpc(ocpcEvent.getEventType().equals(0) && !GuiyingType.packageGy.value.equals(ocpcEvent.getGyType()));

            if (enableDeviceGyCyan && cyGyTypeList.contains(ocpcEvent.getGyType())){
                userActiveRes.setOcpc(false);
                log.info("非精准归因从严: {}", userActiveRes);
            }

            return userActiveRes;
        }

        userActive.setDeviceGui(true);
        userActive.setOcpc(false);

        return userActive;
    }

    /**
     * 查询用户设备归因
     *
     * @param product 产品
     * @param userId  用户ID
     * @param appId   产品ID
     * @return userActive
     */
    public UserActive querySourceByUserId(String product, String userId, Long appId) {
        UserActive userActive = queryQueryRpcIfNoFundUserActive(product, userId, appId);
        if (userActive == null) {
            return new UserActive();
        }
        String key = String.format(OCPC_EVENT_KEY, product, userActive.getOs(), userId);
        byte[] bytes = HBaseUtils.searchDataFromHadoopWithNoHashRowKey(ocpcHadoopConnection, OCPC_EVENT_TABLE, key);

        if (bytes != null) {
            OcpcEvent ocpcEvent = JSONObject.parseObject(Bytes.toString(bytes), OcpcEvent.class);
            if (StringUtils.isEmpty(ocpcEvent.getGyType()) || GuiyingType.packageGy.value.equals(ocpcEvent.getGyType())) {
                userActive.setSource("自然量");
            }
        }
        return userActive;
    }

    private UserActive queryQueryRpcIfNoFundUserActive(String product, String userId, Long appId) {
        UserActive userActive = queryByRpc(appId, userId);
        if (userActive == null) {
            log.info("当前用户无注册信息 {} {} {}", product, appId, userId);
            return null;
        }
        String key1 = String.format(UA_KEY,userId, appId);
        String key2 = String.format(UA_KEY,userId, product);
        List<String> rowKeyList = new ArrayList<>();
        rowKeyList.add(key1);
        rowKeyList.add(key2);

        Map<String,String> resultMap  = HBaseUtils.searchDataFromHadoopList(ocpcHadoopConnection,USER_ACTIVE,rowKeyList);
        for (String key: rowKeyList) {
            String hashKey = HBaseUtils.hashRowKeyByLastCharacter(key);
            String result = resultMap.get(hashKey);
            if (Strings.noEmpty(result)) {
                return JSONObject.parseObject(result, UserActive.class);
            }
        }
        log.info("QueryBy UserActive Hbase notFund {} {}",appId,userId);

        return null;
    }

    private UserActive convertToUserActive(OcpcEvent ocpcEvent) {
        return new UserActive().setUserId(ocpcEvent.getUserId()).setProduct(ocpcEvent.getProduct()).setOs(ocpcEvent.getOs()).setSource(ocpcEvent.getDsp())
                .setOaid(ocpcEvent.getOaid()).setCaid(ocpcEvent.getCaid()).setGyType(ocpcEvent.getGyType()).setAccountId(ocpcEvent.getAccountId())
                .setAccountId(ocpcEvent.getAccountId()).setGyModel(ocpcEvent.getModel()).setCreateTime(ocpcEvent.getCreateTime());
    }

    public UserActive queryByRpc(Long appId,String userId){
        CommonHeaderDTO commonHeaderDTO = new CommonHeaderDTO();
        commonHeaderDTO.setAppId(appId.toString());
        UserDTO userDTO = userRPC.getUserInfo(commonHeaderDTO, Long.valueOf(userId));

        if (userDTO != null){
            UserActive userActive = new UserActive();
            userActive.setUserId(userId);
            userActive.setSource("自然量");
            userActive.setCreateTime(new Date(userDTO.getCreateTime()));
            userActive.setChannel(userDTO.getChannel());
            userActive.setOs(userDTO.getOs() == 0 ?"android":"ios");
            return userActive;
        }
        return null;
    }

    private List<UserScoreEntity> batchQueryUser(List<UserScoreEntity> userScoreEntityList){
        Map<String,List<UserScoreEntity>> userMap = userScoreEntityList.stream()
                .collect(Collectors.groupingBy(UserScoreEntity::getProduct));

        List<UserScoreEntity> filterList = new ArrayList<>();
        userMap.forEach((product,lUserEntity) ->{
            Integer appId = AppConfig.productEnMap.getOrDefault(product,new ProductEntity(){{
                setId(0);
            }}).getId();
            String activeChannelTableName = USER_CHANNEL_TABLE_PREFIX + appId;
            List<String> userIdList = lUserEntity.stream().map(UserScoreEntity::getUserId).collect(Collectors.toList());
            Map<String,String> resultMap = HBaseUtils.searchDataFromHadoopList(hbaseConnection, activeChannelTableName, userIdList);
            for (UserScoreEntity entity : lUserEntity){
                String channel  = resultMap.get(entity.getUserId());
                if (Strings.isEmpty(channel)){
                    filterList.add(entity);
                }
                channel = org.apache.commons.lang3.StringUtils.substringBefore(channel, ",");
                if ("toutiao".equals(channel) || "kuaishou".equals(channel) || "guangdiantong".equals(channel)){
                    log.info("[User]: {}-{} 判定为投放..不拉黑",entity.getProduct(),entity.getUserId());
                }else {
                    filterList.add(entity);
                }
            }
        });

        return filterList;
    }

    private List<UserScoreEntity> batchQueryUserActive(List<UserScoreEntity> userScoreEntityList){

        // 切换为设备归因-未配置则所有产品走设备归因
        if (CollectionUtils.isEmpty(useDeviceGuiProductList)) {
            try {
                List<OcpcEvent> ocpcEventList = queryUserDeviceGuiBatch(userScoreEntityList);
                // 过滤userScoreEntityList在ocpcEventList中的用户
                List<String> ocpcUserIdList = ocpcEventList.stream().map(OcpcEvent::getUserId).collect(Collectors.toList());
                userScoreEntityList = userScoreEntityList.stream().filter(f -> !ocpcUserIdList.contains(f.getUserId())).collect(Collectors.toList());
                return userScoreEntityList;
            } catch (Exception e) {
                log.error("batchQueryUserActive查询用户设备归因失败 {}",userScoreEntityList , e);
            }
        }

        List<UserScoreEntity> filterClickList = new ArrayList<>();
        List<String> rowKeyList = userScoreEntityList.stream()
                .map(r ->  String.format(UA_KEY,r.getUserId(), r.getProduct()))
                .collect(Collectors.toList());

        Map<String,String> resultMap  = HBaseUtils.searchDataFromHadoopList(ocpcHadoopConnection,USER_ACTIVE,rowKeyList);
        for (UserScoreEntity entity : userScoreEntityList){
            String key = HBaseUtils.hashRowKeyByLastCharacter(String.format(UA_KEY,entity.getUserId(), entity.getProduct()));
            String result = resultMap.get(key);
            if (Strings.isEmpty(result)){
                log.info("[UserActive]: {}-{} 未查询到..不拉黑",entity.getProduct(),entity.getUserId());
//                filterClickList.add(entity);
            }else {
                UserActive userActive = JSONObject.parseObject(result, UserActive.class);
                String source = userActive.getSource();
                ActiveChannel activeChannel = ActiveChannel.getByDesc(source);
                boolean isOcpc = ActiveChannel.isOCPCChanel(activeChannel);
                if (isOcpc) {
                    log.info("[UserActive]: {}-{}-{} 判定为投放..不拉黑",entity.getProduct(),entity.getUserId(),activeChannel.getChannel());
                } else {
                    filterClickList.add(entity);
                }
            }
        }
        return filterClickList;
    }

    public List<OcpcEvent> queryUserDeviceGuiBatch(List<UserScoreEntity> userScoreEntityList) {
        List<OcpcEvent> ocpcEventList = new ArrayList<>();
        List<String> rowKeyList = userScoreEntityList.stream()
                .map(r ->  String.format(OCPC_EVENT_KEY, r.getProduct(), r.getOs(), r.getUserId()))
                .collect(Collectors.toList());

        Map<String,String> resultMap  = HBaseUtils.searchDataFromHadoopListWithNoHashRowKey(ocpcHadoopConnection,OCPC_EVENT_TABLE,rowKeyList);
        for (UserScoreEntity entity : userScoreEntityList){
            String key = String.format(OCPC_EVENT_KEY, entity.getProduct(), entity.getOs(), entity.getUserId());
            String result = resultMap.get(key);
            if (Strings.noEmpty(result)){
                OcpcEvent ocpcEvent = JSONObject.parseObject(result, OcpcEvent.class);
                // 0 为归因用户
                if (ocpcEvent.getEventType().equals(0) && !GuiyingType.packageGy.value.equals(ocpcEvent.getGyType())) {
                    ocpcEventList.add(ocpcEvent);
                }
            }
        }

        return ocpcEventList;
    }

    private List<UserScoreEntity> batchQueryClick(List<UserScoreEntity> userScoreEntityList){
        List<UserScoreEntity> filterClickList = new ArrayList<>();
        List<String> rowKeyList = userScoreEntityList.stream()
                .map(r ->  String.format(ATTRIBUTE_KEY_FORMATTER, r.getProduct(), r.getOs(), r.getDeviceId()))
                .collect(Collectors.toList());

        Map<String,String> resultMap = HBaseUtils.searchDataFromHadoopWithoutHash(ocpcHadoopConnection, CLICK_TABLE_NAME, rowKeyList);

        for (UserScoreEntity entity : userScoreEntityList){
            String key = String.format(ATTRIBUTE_KEY_FORMATTER, entity.getProduct(), entity.getOs(), entity.getDeviceId());
            String result = resultMap.get(key);
            if (Strings.isEmpty(result)){
                filterClickList.add(entity);
            }else {
                OcpcClick click = JSONObject.parseObject(result, OcpcClick.class);
                String dsp = click.dsp;
                boolean isOcpc = "toutiao".equals(dsp) || "kuaishou".equals(dsp) || "guangdiantong".equals(dsp);
                if (isOcpc) {
                    log.info("[Click]: {}-{} 判定为投放..不拉黑",entity.getProduct(),entity.getUserId());
                } else {
                    filterClickList.add(entity);
                }
            }
        }
        return filterClickList;
    }

}
