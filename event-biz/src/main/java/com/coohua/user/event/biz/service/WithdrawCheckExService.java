package com.coohua.user.event.biz.service;

import com.alibaba.fastjson.JSON;
import com.coohua.user.event.biz.click.entity.WithdrawExChannelAndModel;
import com.coohua.user.event.biz.click.service.ClickHouseService;
import com.coohua.user.event.biz.service.bean.UserActive;
import com.coohua.user.event.biz.util.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.joda.time.LocalDateTime;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;
import top.zrbcool.pepper.boot.jediscluster.JedisClusterClient;
import top.zrbcool.pepper.boot.jediscluster.JedisClusterClientRefer;

import javax.annotation.PostConstruct;
import java.util.*;
import java.util.Objects;
import java.util.stream.Collectors;

import static java.util.stream.Collectors.*;


/**
 * <AUTHOR>
 * @since 2023/9/20
 */
@Slf4j
@Service
public class WithdrawCheckExService {

    @Autowired
    private ClickHouseService clickHouseService;
    @JedisClusterClientRefer(namespace = "user-event")
    private JedisClusterClient userEventJedisClusterClient;

    private static final String REDIS_COUNT = "{count:slot}:event:count:%s:%s";

    private static final String BIG_EX_CHANNEL = "user:withdraw:big:channel:";

    private List<WithdrawExChannelAndModel> exBigChannels = new ArrayList<>();

    // 分时大额提现
    private static final String BIG_EX_HOUR_CHANNEL = "user:withdraw:hour:big:channel:";

    private List<WithdrawExChannelAndModel> exBigHourChannels = new ArrayList<>();

    // 分时自然量提现
    //private static final String BIG_EX_NATURE_HOUR_CHANNEL = "user:withdraw:hour:nature:big:channel:";

    //private List<WithdrawExChannelAndModel> exNatureBigHourChannels = new ArrayList<>();

    // 分时自然量提现
    //private static final String BIG_EX_STOP_HOUR_CHANNEL = "user:withdraw:hour:stop:big:channel:";

    //public static Map<String,Set<String>> stopChannelMap = new HashMap<>();



    //渠道和机型限制
    private static final String EX_CHANNEL_MODEL = "user:withdraw:ex:model:";
    private List<WithdrawExChannelAndModel> exChannelModels = new ArrayList<>();

    //渠道和oa限制
    private static final String EX_CHANNEL_OA = "user:withdraw:ex:oa:";
    private List<WithdrawExChannelAndModel> exChannelOas = new ArrayList<>();


    public void refreshToRedisExChannel(){
        log.info("==> Start Refresh Withdraw Big Channel ..");
        List<WithdrawExChannelAndModel> exBigChannels =  clickHouseService.queryWithdrawBigExChannel();
        if (Lists.noEmpty(exBigChannels)){
            String result = JSON.toJSONString(exBigChannels);
            log.info("==> Withdraw Current Big Ex Channel:{}", result);
            userEventJedisClusterClient.setex(BIG_EX_CHANNEL+ DateUtil.dateToString(new Date()), RedisKeyConstants.EXPIRE_ONE_DAYS, result);
        }
        log.info("==> End Refresh Withdraw Big Channel ..");
    }

    public void refreshToRedisHourExChannel(){
        log.info("==> Start Refresh Withdraw hour Big Channel ..");
        List<WithdrawExChannelAndModel> exBigHourChannels =  clickHouseService.queryWithdrawBigHourExChannel();
        if (Lists.noEmpty(exBigHourChannels)){
            exBigHourChannels = exBigHourChannels.stream().peek(one -> {
                Date houDate = DateUtil.stringToDate(one.getHourStr() + ":00:00", DateUtil.COMMON_TIME_FORMAT);
                one.setHourTime(new Date(houDate.getTime() + 3600 * 1000 * 25));
            }).filter(f -> f.getHourTime().compareTo(new Date()) >= 0 && StringUtils.isNotBlank(f.getUserIds())).collect(Collectors.toList());

            exBigHourChannels.stream().forEach(ex ->{
                Date beginTime = DateUtil.stringToDate(ex.getHourStr() + ":00:00", DateUtil.COMMON_TIME_FORMAT);
                Date endTime = LocalDateTime.fromDateFields(beginTime).plusHours(1).toDate();
                List<UserActive> userActives = clickHouseService.queryActiveUserByChannel(ex.getProduct(), ex.getChannel(), beginTime, endTime);
                List<Long> collect = userActives.stream().map(UserActive::getUserId).filter(StringUtils::isNotBlank).map(Long::parseLong).collect(Collectors.toList());
                ex.setUserIds(JSON.toJSONString(collect));
            });
            String result = JSON.toJSONString(exBigHourChannels);
            log.info("==> Withdraw Current hour Big Ex Channel:{}", result);
            userEventJedisClusterClient.setex(BIG_EX_HOUR_CHANNEL+ DateUtil.dateToString(new Date()), RedisKeyConstants.EXPIRE_ONE_DAYS, result);
        }
        log.info("==> End Refresh Withdraw hour Channel ..");
    }

    @PostConstruct
    @Scheduled(cron = "0 0/2 * * * ?")
    public void refreshExChannel(){
        log.info("==> Start Refresh Withdraw Big Channel Config ..");
        String result = userEventJedisClusterClient.get(BIG_EX_CHANNEL+ DateUtil.dateToString(new Date()));
        if (Strings.noEmpty(result)){
            List<WithdrawExChannelAndModel> res = JSON.parseArray(result,WithdrawExChannelAndModel.class);
            log.info("==> Withdraw Current get Big Ex Channel:{}", res);
            exBigChannels = res;
        }else {
            exBigChannels = new ArrayList<>();
        }

        String hourResult = userEventJedisClusterClient.get(BIG_EX_HOUR_CHANNEL+ DateUtil.dateToString(new Date()));
        if (Strings.noEmpty(hourResult)){

            List<WithdrawExChannelAndModel> res = JSON.parseArray(hourResult, WithdrawExChannelAndModel.class);

            log.info("==> Withdraw Current get Big Ex hour Channel:{}", res);
            exBigHourChannels = res;
        }else {
            exBigHourChannels = new ArrayList<>();
        }

        /*String stopResult = userEventJedisClusterClient.get(BIG_EX_STOP_HOUR_CHANNEL+ DateUtil.dateToString(new Date()));
        if (Strings.noEmpty(stopResult)){

            List<WithdrawExChannelAndModel> res = JSON.parseArray(stopResult, WithdrawExChannelAndModel.class);

            log.info("==> Withdraw Current get stopBig Ex hour Channel:{}", res);

            stopChannelMap = res.stream().collect(groupingBy(r -> r.getProduct() + ":" + r.getChannel(), collectingAndThen(toList(), list -> list.stream().map(WithdrawExChannelAndModel::getDate).map(DateUtil::dateToString).collect(toSet()))));
        }else {
            stopChannelMap = new HashMap<>();
        }

        String hourNatureResult = userEventJedisClusterClient.get(BIG_EX_NATURE_HOUR_CHANNEL+ DateUtil.dateToString(new Date()));
        if (Strings.noEmpty(hourNatureResult)){

            List<WithdrawExChannelAndModel> res = JSON.parseArray(hourNatureResult, WithdrawExChannelAndModel.class);

            log.info("==> Withdraw Current get Big Ex NatureHour Channel:{}", res);
            exNatureBigHourChannels = res;
        }else {
            exNatureBigHourChannels = new ArrayList<>();
        }*/

        String modelResult = userEventJedisClusterClient.get(EX_CHANNEL_MODEL+ DateUtil.dateToString(new Date()));
        if (Strings.noEmpty(modelResult)){
            List<WithdrawExChannelAndModel> modelRes = JSON.parseArray(modelResult,WithdrawExChannelAndModel.class);
            log.info("==> Withdraw Current get Ex Model:{}", modelRes);
            exChannelModels = modelRes;
        }else {
            exChannelModels = new ArrayList<>();
        }

        String oaResult = userEventJedisClusterClient.get(EX_CHANNEL_OA+ DateUtil.dateToString(new Date()));
        if (Strings.noEmpty(oaResult)){
            List<WithdrawExChannelAndModel> oaRes = JSON.parseArray(oaResult,WithdrawExChannelAndModel.class);
            log.info("==> Withdraw Current get Ex Oa:{}", oaRes);
            exChannelOas = oaRes;
        }else {
            exChannelOas = new ArrayList<>();
        }
        log.info("==> End Refresh Withdraw Big Channel Config ..");
    }

    public boolean isExChannel(String product,String channel){
        return exBigChannels.stream().anyMatch(r -> r.getProduct().equals(product) && r.getChannel().equals(channel));
    }

    public boolean isExHourChannel(String product,String channel,String userId){

        return exBigHourChannels.stream().anyMatch(r -> r.getProduct().equals(product) && r.getChannel().equals(channel) &&  r.getUserIds().contains(userId));
    }

    /*public boolean isExNatureHourChannel(String product,String channel,String userId){

        return exNatureBigHourChannels.stream().anyMatch(r -> r.getProduct().equals(product) && r.getChannel().equals(channel) &&  r.getUserIds().contains(userId));
    }*/

    //渠道和机型限制
    public void refreshToRedisExChannelAndModel(){
        log.info("==> Start Refresh Withdraw ChannelModel ..");
        List<WithdrawExChannelAndModel> exChannelModels =  clickHouseService.queryWithdrawExChannelModel();
        if (Lists.noEmpty(exChannelModels)){
            String result = JSON.toJSONString(exChannelModels);
            log.info("==> Withdraw Current Ex ChannelModel:{}", result);
            userEventJedisClusterClient.setex(EX_CHANNEL_MODEL+ DateUtil.dateToString(new Date()), RedisKeyConstants.EXPIRE_ONE_DAYS, result);
        }
        log.info("==> End Refresh Withdraw ChannelModel ..");
    }

    //渠道和oa限制
    public void refreshToRedisExChannelAndOa(){
        log.info("==> Start Refresh Withdraw ChannelOa ..");
        List<WithdrawExChannelAndModel> exChannelModels =  clickHouseService.queryWithdrawExChannelOa();
        if (Lists.noEmpty(exChannelModels)){
            String result = JSON.toJSONString(exChannelModels);
            log.info("==> Withdraw Current Ex ChannelOa:{}", result);
            userEventJedisClusterClient.setex(EX_CHANNEL_OA+ DateUtil.dateToString(new Date()),RedisKeyConstants.EXPIRE_ONE_DAYS,result);
        }
        log.info("==> End Refresh Withdraw ChannelOa ..");
    }

    public boolean isExChannelAndModel(String product,String channel,String model,Long userId) {
        if (CollectionUtils.isEmpty(exChannelModels)) {
            return false;
        } else {
            if (exChannelModels.stream().anyMatch(r -> Objects.equals(r.getProduct(), product) && Objects.equals(r.getChannel(), channel) && Objects.equals(r.getModel(), model))) {
                String key = String.format(REDIS_COUNT, product, userId);
                String result = userEventJedisClusterClient.get(key);
                if (StringUtils.isNotEmpty(result)) {
                    if (Integer.parseInt(result) < 1) {
                        return true;
                    }
                } else {
                    return true;
                }
            }
            return false;
        }
    }
    public boolean isExChannelAndOs(String product,String channel,String oaid){
        if (CollectionUtils.isEmpty(exChannelOas) || StringUtils.isBlank(oaid) || oaid.length() <8) {
            return false;
        } else {
            String oaid1 = oaid.substring(0, 3) + oaid.substring(oaid.length() - 5, oaid.length());
            return exChannelOas.stream().anyMatch(r -> Objects.equals(r.getProduct(),product) && Objects.equals(r.getChannel(),channel) && Objects.equals(r.getOa(),oaid1) );
        }
    }



    /*public void refreshToRedisHourNatureExChannel(){
        log.info("==> Start Refresh Withdraw hour NatureBig Channel ..");
        List<WithdrawExChannelAndModel> exBigHourChannels =  clickHouseService.queryWithdrawBigHourNatureExChannel();
        if (Lists.noEmpty(exBigHourChannels)){
            exBigHourChannels = exBigHourChannels.stream().filter(f -> Objects.nonNull(f.getChannel()) && Objects.nonNull(f.getHourStr())).peek(one -> {
                Date houDate = DateUtil.stringToDate(one.getHourStr() + ":00:00", DateUtil.COMMON_TIME_FORMAT);
                one.setHourTime(new Date(houDate.getTime() + 3600 * 1000 * 25));
            }).filter(f -> f.getHourTime().compareTo(new Date()) >= 0 && StringUtils.isNotBlank(f.getUserIds())).collect(Collectors.toList());

            exBigHourChannels.stream().forEach(ex ->{
                Date beginTime = DateUtil.stringToDate(ex.getHourStr() + ":00:00", DateUtil.COMMON_TIME_FORMAT);
                Date endTime = LocalDateTime.fromDateFields(beginTime).plusHours(1).toDate();
                List<UserActive> userActives = clickHouseService.queryActiveUserByChannel(ex.getProduct(), ex.getChannel(), beginTime, endTime);
                List<Long> collect = userActives.stream().filter(f -> Objects.isNull(f.getAccountId())).map(UserActive::getUserId).filter(StringUtils::isNotBlank).map(Long::parseLong).collect(Collectors.toList());
                ex.setUserIds(JSON.toJSONString(collect));
            });
            String result = JSON.toJSONString(exBigHourChannels);
            log.info("==> Withdraw Current hour NatureBig Ex Channel:{}", result);
            userEventJedisClusterClient.setex(BIG_EX_NATURE_HOUR_CHANNEL+ DateUtil.dateToString(new Date()), RedisKeyConstants.EXPIRE_ONE_DAYS, result);
        }
        log.info("==> End Refresh Withdraw NatureBig hour Channel ..");
    }


    public void refreshToRedisHourStopExChannel(){
        log.info("==> Start Refresh Withdraw hour StopBig Channel ..");
        List<WithdrawExChannelAndModel> exBigHourChannels =  clickHouseService.queryWithdrawBigHourStopExChannel();
        if (Lists.noEmpty(exBigHourChannels)){
            exBigHourChannels = exBigHourChannels.stream().filter(f -> Objects.nonNull(f.getChannel()) && Objects.nonNull(f.getHourStr())).peek(one -> {
                Date date = DateUtil.stringToDate(one.getHourStr().substring(0,10));
                one.setDate(date);
            }).collect(Collectors.toList());

            String result = JSON.toJSONString(exBigHourChannels);
            log.info("==> Withdraw Current hour StopBig Ex Channel:{}", result);
            userEventJedisClusterClient.setex(BIG_EX_STOP_HOUR_CHANNEL+ DateUtil.dateToString(new Date()), RedisKeyConstants.EXPIRE_THREE_DAYS, result);
        }
        log.info("==> End Refresh Withdraw StopBig hour Channel ..");
    }*/
}
