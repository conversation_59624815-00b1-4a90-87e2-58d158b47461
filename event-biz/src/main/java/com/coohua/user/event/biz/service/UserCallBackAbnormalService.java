package com.coohua.user.event.biz.service;

import com.coohua.user.event.biz.click.entity.UserMacIpCheckEntity;
import com.coohua.user.event.biz.click.service.ClickHouseService;
import com.coohua.user.event.biz.util.RedisKeyConstants;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import java.util.ArrayList;
import java.util.List;

@Slf4j
@Service
public class UserCallBackAbnormalService {

    @Autowired
    private ClickHouseService clickHouseService;
    @Autowired
    private UserIncomeAbnormalService userIncomeAbnormalService;


    public void userCallBackAbnormal() {
        List<UserMacIpCheckEntity> userList = clickHouseService.queryUserCallBackAbnormaList();
        List<String> userKeyList = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(userList)) {
            for (UserMacIpCheckEntity userEntity : userList) {
                if (StringUtils.isBlank(userEntity.getUserIdStr())) {
                    continue;
                }
                if (StringUtils.isBlank(userEntity.getProduct())) {
                    continue;
                }
                String key = RedisKeyConstants.getGdtRewardCallBackAbnormalKey(userEntity.getProduct(), userEntity.getUserIdStr());
                userKeyList.add(key);
            }
            userIncomeAbnormalService.batchSaveToRedis(userKeyList, RedisKeyConstants.GDT_SERVER_REWARD_CALL_BACK_KEY_PREFIX, 900);
        }
    }

}
