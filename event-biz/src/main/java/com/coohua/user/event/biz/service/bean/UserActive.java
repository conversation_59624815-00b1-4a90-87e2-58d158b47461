package com.coohua.user.event.biz.service.bean;

import cn.hutool.core.date.DateUtil;
import lombok.Data;
import lombok.experimental.Accessors;
import org.apache.commons.lang3.StringUtils;

import java.util.Date;
import java.util.Objects;

/**
 * <AUTHOR>
 * @since 2023/7/11
 */
@Accessors(chain = true)
@Data
public class UserActive {
    private String userId;
    private String product;
    private String os;
    private String source;
    private String channel;
    private String model;
    private String gyModel;
    private String oaid;
    private String imei;
    private String caid;
    private String gyCaid;
    private String idfa;
    private String gyType;
    private String accountId;
    private String ip;
    private Date createTime;
    private boolean isDeviceGui = false;
    private boolean isOcpc = false;


    //反作弊自然量判断从严
    public boolean isZiRan() {
        // 设备归因-兼容旧逻辑
        if (isDeviceGui) {
            return !this.isOcpc;
        }

        if (Objects.equals(source, "自然量") || Objects.isNull(gyType) || StringUtils.equalsAny(gyType, "mac", "ip", "ipmd")) {
            return true;
        }
        if (StringUtils.isBlank(accountId) && StringUtils.equalsAny(source, "自然ALIYUN量", "yingyongbao", "xiaomi", "vivo",
                "oppo", "neilaxin", "huawei", "INNER_OLD_PULL")) {
            return true;
        }
        if (Objects.equals(os, "ios") && !StringUtils.equalsAny(channel, "AppStore", "ksmin", "wmin", "byte")) {
            return true;
        }
        return false;
    }

    public boolean isIos() {
        return StringUtils.isNotBlank(os) && "ios".equalsIgnoreCase(os);
    }

    public boolean isAndroid() {
        return StringUtils.isNotBlank(os) && "android".equalsIgnoreCase(os);
    }

    public boolean isTodayUser() {
        if (createTime == null) return false;

        return DateUtil.isSameDay(createTime, new Date());
    }

    public boolean isPastOneDaysUser() {
        if (createTime == null) return false;

        return DateUtil.isSameDay(createTime, new Date()) || DateUtil.isSameDay(createTime, DateUtil.yesterday());
    }

    public boolean isWithinDays(int days) {
        if (createTime == null) return false;

        Date daysAgo = DateUtil.offsetDay(new Date(), -days);
        return createTime.after(DateUtil.beginOfDay(daysAgo));
    }
}
