package com.coohua.user.event.biz.service;

import com.aliyun.oss.OSS;
import com.aliyun.oss.model.PutObjectRequest;
import com.aliyun.oss.model.PutObjectResult;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.io.InputStream;
import java.util.HashMap;

@Slf4j
@Service
public class OssService {

    @Resource(name = "eventManagerOss")
    private OSS eventOss;

    public static  final String OSS_URL ="https://video-manager.shinet.cn";

    public PutObjectResult uploadFile(String bucket, String objectName, InputStream file) {
        PutObjectRequest putObjectRequest = new PutObjectRequest(bucket, objectName, file);
        HashMap<String, String> map = new HashMap<>();
        map.put("Content-Disposition", "attachment;");
        putObjectRequest.setHeaders(map);
        PutObjectResult putObjectResult = eventOss.putObject(putObjectRequest);
        return putObjectResult;
    }
}
