package com.coohua.user.event.biz.user.service;

import com.coohua.user.event.biz.click.entity.ProductEntity;
import com.coohua.user.event.biz.dc.entity.UserGrayIpEntity;
import com.coohua.user.event.biz.dc.mapper.UserGrayIpMapper;
import com.coohua.user.event.biz.enums.GrayType;
import com.coohua.user.event.biz.user.entity.UserCancelEntity;
import com.coohua.user.event.biz.user.mapper.UserCancelMapper;
import com.coohua.user.event.biz.util.AppConfig;
import com.coohua.user.event.biz.util.DateUtil;
import com.coohua.user.event.biz.util.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;
import java.util.regex.Pattern;

/**
 * <AUTHOR>
 * @since 2022/2/24
 */
@Slf4j
@Service
public class UserCancelSycnService {

    @Resource
    private UserCancelMapper userCancelMapper;
    @Resource
    private UserGrayIpMapper userGrayIpMapper;

    public void trans(){
        // 65 MIN
        Date indexTime = DateUtil.dateIncreaseBySeconds(new Date(),- 60 * 65);
        List<UserCancelEntity> cancelEntityList = userCancelMapper.queryPackageAndDevice(indexTime);

        if (Lists.noEmpty(cancelEntityList)){
            Integer index = 0;
            for (UserCancelEntity pr : cancelEntityList) {
                index ++;
                GrayType grayType = GrayType.DEVICE;
                if (isInteger(pr.getUserId()) && pr.getUserId().length() < 12) {
                    grayType = GrayType.USER;
                }

                if (StringUtils.isNotBlank(pr.getCDesc()) && pr.getCDesc().contains("外部调用-用户注销")) continue;

                Integer count = userGrayIpMapper.countExist(null, grayType.getType(), pr.getUserId());
                if (count == 0) {
                    log.info("[{}/{}] SAVE USER SYSTEM INFO {} {}",index,cancelEntityList.size(), grayType.getDesc(), pr.getUserId());
                    ProductEntity productEntity = AppConfig.appIdMap.getOrDefault(pr.getAppId(), new ProductEntity());
                    GrayType finalGrayType = grayType;
                    userGrayIpMapper.insert(new UserGrayIpEntity() {{
                        setProduct(productEntity.getProduct());
                        setTargetType(finalGrayType.getType());
                        setTargetId(pr.getUserId());
                        setRemark(pr.getCDesc());
                        setCreateTime(pr.getCreateTime());
                        setUpdateTime(pr.getUpdateTime());
                    }});
                }
            }
        }
    }

    private static boolean isInteger(String str) {
        Pattern pattern = Pattern.compile("^[-\\ ]?[\\d]*$");
        return pattern.matcher(str).matches();
    }

}
