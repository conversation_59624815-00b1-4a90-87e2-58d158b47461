package com.coohua.user.event.biz.core.mapper;

import com.coohua.user.event.biz.core.entity.UserEventCount;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;
import java.util.Map;

/**
 * <p>
 *  Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2020-09-28
 */
public interface UserEventCountMapper extends BaseMapper<UserEventCount> {


	/**
	 * 查询打点日志小于countLog的用户
	 * @param userIdList
	 * @param countLog
	 * @return
	 */
	@Select({
			"<script>",
			"select user_id,app_id,ad_data+app_click+app_news_view+app_page_view+app_share+start_up as num from `ads_biz`.`user_event_count`" +
					" where app_id= #{appId} and  user_id in  " +
					" <foreach collection='ids' index='index' item='item' open='(' separator=',' close=')'>#{item} </foreach> "+
					" and ad_data+app_click+app_news_view+app_page_view+app_share+start_up &gt;= #{countLog}",
			"</script>"
	})
	List<Map> listCountByUserAndApp(@Param("ids") List<Long> userIdList, @Param("appId")Long appId, @Param("countLog")Integer countLog);

}
