package com.coohua.user.event.biz.toufang.entity;

import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 * @since 2020/9/19
 */
@Data
public class CoreEventEntity {
    /**
     * 主键
     */
    private Integer id;

    /**
     * toutiao
     */
    private String dsp;

    /**
     * xiaoxiaole
     */
    private String product;

    /**
     * 0: android 1: ios
     */
    private String os;

    /**
     * clickid
     */
    private Long clickId;

    /**
     * usereventid
     */
    private Long userEventId;

    /**
     * 创意id
     */
    private String cid;

    /**
     * 创意名称
     */
    private String cidName;

    /**
     * 广告组id
     */
    private String groupId;

    /**
     * 广告组名称
     */
    private String groupName;

    /**
     * 计划id
     */
    private String adId;

    /**
     * 计划名称
     */
    private String aidName;

    /**
     * 账户id
     */
    private String accountId;

    /**
     * 账户名称
     */
    private String accountName;

    /**
     * 设备id
     */
    private String deviceId;

    /**
     * oaid
     */
    private String oaId;

    /**
     * 记录时间
     */
    private Date logDate;

    /**
     * 小时
     */
    private Integer logHour;

    /**
     * 核心行为1
     */
    private Integer event1Type;

    /**
     * 核心行为2
     */
    private Integer event2Type;

    /**
     * 核心行为3
     */
    private Integer event3Type;

    /**
     * 核心行为4
     */
    private Integer event4Type;

    /**
     * 核心行为5
     */
    private Integer event5Type;

    /**
     * 删除标识
     */
    private Integer delFlag;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;
}
