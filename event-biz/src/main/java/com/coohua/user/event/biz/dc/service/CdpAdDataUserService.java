package com.coohua.user.event.biz.dc.service;

import com.coohua.user.event.biz.dc.entity.CdpAdDataUser;
import com.coohua.user.event.biz.dc.mapper.CdpAdDataUserMapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;

import java.util.List;

/**
* <p>
    *  服务实现类
    * </p>
*
* <AUTHOR>
* @since 2023-06-15
*/
@Service
public class CdpAdDataUserService extends ServiceImpl<CdpAdDataUserMapper, CdpAdDataUser> {

    public List<CdpAdDataUser> queryByLogday(String logday){
        return baseMapper.queryByLogday(logday);
    }

}
