package com.coohua.user.event.biz.dc.service;

import com.coohua.user.event.biz.dc.entity.DailyResultEntity;
import com.coohua.user.event.biz.dc.entity.DailyResultHisEntity;
import com.coohua.user.event.biz.dc.mapper.DcHistoryMapper;
import com.coohua.user.event.biz.util.DateUtil;
import com.coohua.user.event.biz.util.Strings;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2020/9/4
 */
@Slf4j
@Service
public class DcHistoryDataService {

    @Resource
    private DcHistoryMapper dcHistoryMapper;

    public void synchDate(){
        log.info("历史数据同步开始...");
        Date beginDate = DateUtil.stringToDate("2020-07-01");
        Date endDate = DateUtil.stringToDate("2020-08-24");
        while (beginDate.before(endDate)){
            synchDataHis(DateUtil.dateToString(beginDate));
            beginDate = DateUtil.dateIncreaseByDay(beginDate,1);
        }

        beginDate = DateUtil.stringToDate("2020-08-24");
        endDate = DateUtil.stringToDate("2020-09-01");
        while (beginDate.before(endDate)){
            synchData(DateUtil.dateToString(beginDate));
            beginDate = DateUtil.dateIncreaseByDay(beginDate,1);
        }
        log.info("历史数据同步结束...");
    }


    public void synchData(String date){

        log.info("查询的目标日期为:{}",date);
        List<DailyResultHisEntity> dailyResultHisEntityList = dcHistoryMapper.queryHistory(date);
        if (dailyResultHisEntityList == null || dailyResultHisEntityList.size() == 0){
            log.warn("为查询到当日历史数据...不做处理");
            return;
        }

        for (DailyResultHisEntity dailyResultHisEntity : dailyResultHisEntityList){
            DailyResultEntity entity = new DailyResultEntity();
            BeanUtils.copyProperties(dailyResultHisEntity,entity);
            entity.setLogday(dailyResultHisEntity.getLogday1());
            entity.setLogday1(dailyResultHisEntity.getLogday());
            if (dcHistoryMapper.countDcData(date,dailyResultHisEntity.getProduct(),dailyResultHisEntity.getOs()) > 0){
                dcHistoryMapper.delete(date,dailyResultHisEntity.getProduct(),dailyResultHisEntity.getOs());
            }
            dcHistoryMapper.insertToDcData(entity);

        }
        log.info("日期{}已完成同步{}条",date,dailyResultHisEntityList.size());
    }

    public void synchDataHis(String date){

        log.info("查询的目标日期为:{}",date);
        List<DailyResultHisEntity> dailyResultHisEntityList = dcHistoryMapper.queryHistoryHis(date);
        if (dailyResultHisEntityList == null || dailyResultHisEntityList.size() == 0){
            log.warn("为查询到当日历史数据...不做处理");
            return;
        }

        for (DailyResultHisEntity dailyResultHisEntity : dailyResultHisEntityList){
            DailyResultEntity entity = new DailyResultEntity();
            BeanUtils.copyProperties(dailyResultHisEntity,entity);
            entity.setLogday(dailyResultHisEntity.getLogday1());
            entity.setLogday1(dailyResultHisEntity.getLogday());
            if (dcHistoryMapper.countDcData(date,dailyResultHisEntity.getProduct(),dailyResultHisEntity.getOs()) > 0){
                dcHistoryMapper.delete(date,dailyResultHisEntity.getProduct(),dailyResultHisEntity.getOs());
            }
            dcHistoryMapper.insertToDcData(entity);

        }
        log.info("日期{}已完成同步{}条",date,dailyResultHisEntityList.size());
    }

}
