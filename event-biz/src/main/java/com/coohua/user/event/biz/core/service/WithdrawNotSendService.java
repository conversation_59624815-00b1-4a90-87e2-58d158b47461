package com.coohua.user.event.biz.core.service;

import com.baomidou.mybatisplus.extension.service.additional.query.impl.LambdaQueryChainWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.coohua.user.event.biz.click.entity.ProductEntity;
import com.coohua.user.event.biz.core.entity.WithdrawNotSend;
import com.coohua.user.event.biz.core.mapper.WithdrawNotSendMapper;
import com.coohua.user.event.biz.dc.dto.DelayOrderResponse;
import com.coohua.user.event.biz.dc.dto.Pages;
import com.coohua.user.event.biz.service.TFUserService;
import com.coohua.user.event.biz.service.bean.UserActive;
import com.coohua.user.event.biz.util.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

/**
* <p>
    *  服务实现类
    * </p>
*
* <AUTHOR>
* @since 2023-09-23
*/
@Slf4j
@Service
public class WithdrawNotSendService extends ServiceImpl<WithdrawNotSendMapper, WithdrawNotSend> {

    @Autowired
    private TFUserService tfUserService;

    public Pages<DelayOrderResponse> queryList(String product,Long userId,String logday,Integer canSend,Pages<DelayOrderResponse> pages,Integer queryType){

        LambdaQueryChainWrapper<WithdrawNotSend> queryChainWrapper = this.lambdaQuery()
                .eq(Strings.noEmpty(product),WithdrawNotSend::getAppId,product)
                .eq(userId != null && userId != 0L,WithdrawNotSend::getUserId,userId)
                .eq(WithdrawNotSend::getIsRefund,0)
                .eq(canSend != null,WithdrawNotSend::getCanSend,canSend);

        if (Strings.isEmpty(logday)){
            logday = DateUtil.dateToString(new Date());
        }

        if (queryType != null){
            if (queryType == 1){
                queryChainWrapper.likeRight(WithdrawNotSend::getRemark,"内拉新检测异常");
            }else if (queryType == 2){
                queryChainWrapper.likeRight(WithdrawNotSend::getRemark,"处于拒绝渠道");
            }else if (queryType == 3){
                queryChainWrapper.likeRight(WithdrawNotSend::getRemark,"项目七组");
            }else if (queryType == 4){
                queryChainWrapper.likeRight(WithdrawNotSend::getRemark,"累积提现过高");
            }else if (queryType == 5){
                queryChainWrapper.likeRight(WithdrawNotSend::getRemark,"服务回调次数过低拦截");
            }
        }

        String start = logday + " 00:00:00";
        String end = logday + " 23:59:59";
        queryChainWrapper
                .ge(WithdrawNotSend::getCreateTime,start)
                .le(WithdrawNotSend::getCreateTime,end)
        .orderByDesc(WithdrawNotSend::getCreateTime);

        Integer count = queryChainWrapper.count();
        pages.setCount(count);
        queryChainWrapper.last(String.format(" limit %s,%s",(pages.getPageNo()-1)*pages.getPageSize(),pages.getPageSize()));

        List<DelayOrderResponse> delayOrderResponses = queryChainWrapper.list()
                .stream()
                .map(r ->{
                    DelayOrderResponse response = new DelayOrderResponse();
                    BeanUtils.copyProperties(r,response);
                    response.setCreateTime(DateUtil.dateToStringWithTime(r.getCreateTime()));
                    response.setUpdateTime(DateUtil.dateToStringWithTime(r.getUpdateTime()));
                    return response;
                }).collect(Collectors.toList());
        pages.setItems(delayOrderResponses);
        return pages;
    }

    public void createIfNotExist(Long appId,Long userId,Integer amount,String orderNo,String reason,boolean isDirectRefund){
        WithdrawNotSend withdrawNotSend = lambdaQuery()
                .eq(WithdrawNotSend::getAppId,appId)
                .eq(WithdrawNotSend::getUserId,userId)
                .eq(WithdrawNotSend::getOrderNo,orderNo)
                .last("limit 1")
                .one();
        if (Objects.isNull(withdrawNotSend)) {
            WithdrawNotSend record = new WithdrawNotSend();
            ProductEntity productEntity = AppConfig.appIdMap.get(appId);
            if (productEntity != null) {
                record.setProduct(productEntity.getProduct());
                record.setProductName(productEntity.getProductName());
                UserActive userActive = tfUserService.queryUserActiveIfNoFundQueryRpc(productEntity.getProduct(), userId.toString(), appId);
                if (userActive != null) {
                    record.setRegisterChannel(userActive.getChannel());
                } else {
                    record.setRegisterChannel("未查询到注册渠道");
                }
            }
            record.setAppId(appId.intValue());
            record.setAmount(amount);
            record.setCanSend(0);
            record.setUserId(userId);
            record.setOrderNo(orderNo);

            record.setRemark(reason);
            record.setIsRefund(isDirectRefund ? 1 : 0);
            Date now = new Date();
            record.setCreateTime(now);
            record.setUpdateTime(now);
            save(record);
        } else {
            log.info("修改reason {} {} ",userId,reason);
            this.lambdaUpdate().set(WithdrawNotSend::getRemark, reason).set(WithdrawNotSend::getUpdateTime, new Date()).eq(WithdrawNotSend::getId, withdrawNotSend.getId()).update();
        }
    }

    // 确认订单是否可发
    public boolean queryOrderCanSend(Long appId,Long userId,String orderNo){
        return lambdaQuery()
                .eq(WithdrawNotSend::getAppId,appId)
                .eq(WithdrawNotSend::getUserId,userId)
                .eq(WithdrawNotSend::getOrderNo,orderNo)
                .eq(WithdrawNotSend::getCanSend,1)
                .count() > 0;
    }

    public WithdrawNotSend queryOrderByOrderNo(Long appId,Long userId,String orderNo){
        List<WithdrawNotSend> queryList =  lambdaQuery()
                .eq(WithdrawNotSend::getAppId,appId)
                .eq(WithdrawNotSend::getUserId,userId)
                .eq(WithdrawNotSend::getOrderNo,orderNo)
                .list();
        if (Lists.noEmpty(queryList) && queryList.size() > 0){
            return queryList.get(0);
        }
        return null;
    }


    public void cleanBefore15DaysOrder(){
        Date now = new Date();
        Date before15 = DateUtil.dateIncreaseByDay(now,-15);
        log.info("清除十五天之前 {} 的提现拒绝记录..",DateUtil.dateToString(before15));
        String start = DateUtil.dateToString(before15) + " 00:00:00";
        String end = DateUtil.dateToString(before15) + " 23:59:59";

        List<Integer> withdrawNotSends = lambdaQuery().ge(WithdrawNotSend::getCreateTime,start)
                .le(WithdrawNotSend::getCreateTime,end)
                .select(WithdrawNotSend::getId)
                .list()
                .stream()
                .map(WithdrawNotSend::getId)
                .collect(Collectors.toList());
        // 按照主键删除
        if (withdrawNotSends.size() > 0) {
            this.baseMapper.deleteBatchIds(withdrawNotSends);
        }
        log.info("成功清除十五天之前 {} 的提现拒绝记录 {} 条..",DateUtil.dateToString(before15),withdrawNotSends.size());
    }


    public boolean updateOrderToCanSend(String orderNo, boolean forcePass){
        List<WithdrawNotSend> queryList =  lambdaQuery()
                .eq(WithdrawNotSend::getOrderNo,orderNo)
                .list();

        WithdrawNotSend withdrawNotSend = null;
        if (Lists.noEmpty(queryList) && queryList.size() > 0){
            withdrawNotSend = queryList.get(0);
        }
        if (withdrawNotSend == null){
            log.info("未查询到订单号 {}",orderNo);
            return false;
        }
        if (!forcePass) {
            // 强制放行不校验原因
            String remark = withdrawNotSend.getRemark();
            if (StringUtils.isNotBlank(remark) && (remark.contains("中台拦截") || !remark.startsWith("项目"))) {
                //中台拦截的不允许直接解开
                log.info("中台拦截订单号 {}",orderNo);
                return false;
            }else {
                log.info("放行 {} {} ",orderNo,remark);
            }
        }

        withdrawNotSend.setCanSend(1);
        withdrawNotSend.setUpdateTime(new Date());
        return updateById(withdrawNotSend);
    }

    public WithdrawNotSend queryByOrderNo(String orderNo) {
        List<WithdrawNotSend> queryList = lambdaQuery()
                .eq(WithdrawNotSend::getOrderNo, orderNo)
                .list();
        WithdrawNotSend withdrawNotSend = null;
        if (Lists.noEmpty(queryList) && queryList.size() > 0){
            withdrawNotSend = queryList.get(0);
        }
        return withdrawNotSend;
    }
}
