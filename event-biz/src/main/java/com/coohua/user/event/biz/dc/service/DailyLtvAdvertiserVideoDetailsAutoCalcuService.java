package com.coohua.user.event.biz.dc.service;

import com.coohua.user.event.biz.dc.dto.LtvQueryRequest;
import com.coohua.user.event.biz.dc.entity.DailyLtvAdvertiserVideoDetailsAutoCalcu;
import com.coohua.user.event.biz.dc.mapper.DailyLtvAdvertiserVideoDetailsAutoCalcuMapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;

/**
* <p>
    *  服务实现类
    * </p>
*
* <AUTHOR>
* @since 2021-07-07
*/
@Service
public class DailyLtvAdvertiserVideoDetailsAutoCalcuService extends ServiceImpl<DailyLtvAdvertiserVideoDetailsAutoCalcuMapper, DailyLtvAdvertiserVideoDetailsAutoCalcu> {

    public DailyLtvAdvertiserVideoDetailsAutoCalcu queryByAdvertiserId(LtvQueryRequest request){
        return this.baseMapper.selectByAdvertiserId(request.getLogday(),request.getProduct(),request.getAdvertiserId(),request.getVideoName());
    }

    public DailyLtvAdvertiserVideoDetailsAutoCalcu queryByAdvertiserIdAndToufangType(LtvQueryRequest request){
        return this.baseMapper.selectByAdvertiserIdAndTouFangType(request.getLogday(),request.getProduct(),
                request.getAdvertiserId(),
                request.getToufangSite() + request.getToufangType(),
                request.getVideoName());
    }
}
