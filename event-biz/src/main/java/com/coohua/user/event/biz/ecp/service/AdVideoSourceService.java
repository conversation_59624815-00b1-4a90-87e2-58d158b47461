package com.coohua.user.event.biz.ecp.service;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.coohua.user.event.biz.ecp.entity.AdVideoSource;
import com.coohua.user.event.biz.ecp.mapper.AdVideoSourceMapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;

/**
* <p>
    * 视频源数据 服务实现类
    * </p>
*
* <AUTHOR>
* @since 2020-11-05
*/
@Slf4j
@Service
public class AdVideoSourceService extends ServiceImpl<AdVideoSourceMapper, AdVideoSource> {


    public Integer queryVideoIdMax(){
        QueryWrapper<AdVideoSource> queryWrapper = new QueryWrapper<>();
        AdVideoSource adVideoSource = getOne(queryWrapper.lambda().orderByDesc(AdVideoSource::getVideoId).last(" limit 1"));
        return adVideoSource == null ? 0 : adVideoSource.getVideoId();
    }

    @Transactional
    public int insertVideoInfo(List<AdVideoSource> insertBatch){
        boolean save = saveBatch(insertBatch,insertBatch.size());
        if (!save){
            throw new RuntimeException("视频数据保存异常！");
        }
        return insertBatch.size();
    }

}
