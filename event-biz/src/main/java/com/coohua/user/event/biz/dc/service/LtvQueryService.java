package com.coohua.user.event.biz.dc.service;

import com.coohua.user.event.biz.dc.dto.LtvAllQueryResponse;
import com.coohua.user.event.biz.dc.dto.LtvQueryRequest;
import com.coohua.user.event.biz.dc.dto.LtvQueryResponse;
import com.coohua.user.event.biz.dc.dto.QueryRosRequest;
import com.coohua.user.event.biz.dc.entity.RosEntity;
import com.coohua.user.event.biz.dc.entity.DailyLtvAdvertiserVideoDetailsAutoCalcu;
import com.coohua.user.event.biz.dc.mapper.RosMapper;
import com.coohua.user.event.biz.util.DateUtil;
import com.coohua.user.event.biz.util.Lists;
import com.coohua.user.event.biz.util.Strings;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;

/**
 * <AUTHOR>
 * @since 2021/7/7
 */
@Slf4j
@Service
public class LtvQueryService {

    @Autowired
    private DailyLtvAdvertiserVideoDetailsAutoCalcuService dailyLtvAdvertiserVideoDetailsAutoCalcuService;
    @Autowired
    private DailyLtvAllAutoCalcuSubService dailyLtvAllAutoCalcuSubService;
    @Resource
    private RosMapper rosMapper;

    public LtvQueryResponse queryLtvInfo(LtvQueryRequest request){
        LtvQueryResponse response = new LtvQueryResponse();
        DailyLtvAdvertiserVideoDetailsAutoCalcu dailyLtvAdvertiserVideoDetailsAutoCalcu = null;
        if (request.getQueryType() == 2) {
            dailyLtvAdvertiserVideoDetailsAutoCalcu = dailyLtvAdvertiserVideoDetailsAutoCalcuService.queryByAdvertiserIdAndToufangType(request);
        }else if (request.getQueryType() == 1){
             dailyLtvAdvertiserVideoDetailsAutoCalcu = dailyLtvAdvertiserVideoDetailsAutoCalcuService.queryByAdvertiserId(request);
        }
        if (dailyLtvAdvertiserVideoDetailsAutoCalcu == null){
            return response;
        }

        response.setLtv(dailyLtvAdvertiserVideoDetailsAutoCalcu.getLtv());
        response.setActiveCount(dailyLtvAdvertiserVideoDetailsAutoCalcu.getToufangDevice().intValue());
        if (dailyLtvAdvertiserVideoDetailsAutoCalcu.getToufangDevice() > 0) {
            if (dailyLtvAdvertiserVideoDetailsAutoCalcu.getLtvOther() == null){
                dailyLtvAdvertiserVideoDetailsAutoCalcu.setLtvOther(0d);
            }
            response.setRosLtv((response.getActiveCount() * response.getLtv() + dailyLtvAdvertiserVideoDetailsAutoCalcu.getLtvOther()) / response.getActiveCount());
        }
        response.setAdvertiserId(dailyLtvAdvertiserVideoDetailsAutoCalcu.getAdvertiserId());
        response.setVideoName(dailyLtvAdvertiserVideoDetailsAutoCalcu.getVideoName());
        return response;
    }

    public List<LtvAllQueryResponse> queryLtvAllQueryResponse(String logday){
        return dailyLtvAllAutoCalcuSubService.queryAll(logday);
    }

    public List<RosEntity> queryBaiduFeedRos(QueryRosRequest request){
        if (Lists.isEmpty(request.getAdvertiserIds())){
            return new ArrayList<>();
        }

        if (Strings.isEmpty(request.getLogday())){
            request.setLogday(DateUtil.dateToString(new Date()));
        }

        return rosMapper.queryBaiduRos(request.getLogday(),request.getAdvertiserIds());
    }

    public List<RosEntity> queryToutiaoRos(QueryRosRequest request){
        if (Lists.isEmpty(request.getCampaignIds())){
            return new ArrayList<>();
        }

        if (Strings.isEmpty(request.getLogday())){
            request.setLogday(DateUtil.dateToString(new Date()));
        }

        return rosMapper.queryToutiaoRos(request.getLogday(),request.getCampaignIds());
    }
}
