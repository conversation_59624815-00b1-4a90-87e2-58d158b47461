package com.coohua.user.event.biz.dc.mapper;

import com.coohua.user.event.biz.dc.entity.HistoryIncomeEntity;
import com.coohua.user.event.biz.dc.entity.HistoryResult1Entity;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2020/9/7
 */
public interface HistoryIncomeMapper {


    @Select({"select * from ads.history_income "})
    List<HistoryIncomeEntity> selectByLogDay(@Param("logDay")String logDay);

    @Update({" update ads.history_result1 set platform_income = #{platformIncome}, ",
            " straight_income=#{straightIncome},total_income = #{totalIncome} ",
            " where logday= #{logday} and product = #{product} and os=#{os} "})
    int updateHistoryResult1(HistoryResult1Entity historyResult1Entity);
}
