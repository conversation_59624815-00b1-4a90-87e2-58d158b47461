package com.coohua.user.event.biz.dc.mapper;

import com.coohua.user.event.biz.dc.entity.RealtimeRule;
import org.apache.ibatis.annotations.Insert;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2022/1/22
 */
public interface RealtimeRuleMapper {

    @Select("select * from `user-event`.realtime_rule where state = 1")
    List<RealtimeRule> queryAllConfig();

    @Select("select * from `user-event`.realtime_rule where id = #{id}")
    RealtimeRule queryById(@Param("id") Integer id);

    @Insert({"INSERT INTO `user-event`.`realtime_rule` (`id`, `channels`, `models`, `ips`, `product`, " +
            "`ocpc_type`, `state`, `create_time`, `update_time`, `action_type`, `action_rule`, " +
            "`rule_name`, `app_id`, `product_name`, `level`, `brands`,`sources`,`new_user_type`)" +
            " VALUES (#{id}, #{channels}, #{models}, #{ips}, #{product}, #{ocpcType}, #{state}, #{createTime}," +
            " #{updateTime}, #{actionType}, #{actionRule}, #{ruleName}, #{appId}, #{productName}, #{level}, #{brands},#{sources},#{newUserType});"})
    int insert(RealtimeRule realtimeRule);

    @Update({
            "UPDATE `user-event`.`realtime_rule` SET " +
                    "`channels`=#{channels}, " +
                    "`models`=#{models}, " +
                    "`ips`=#{ips}, " +
                    "`product`=#{product}, " +
                    "`ocpc_type`=#{ocpcType}, " +
                    "`state`=#{state}, " +
                    "`create_time`=#{createTime}, " +
                    "`update_time`=#{updateTime}, " +
                    "`action_type`=#{actionType}, " +
                    "`action_rule`= #{actionRule}, " +
                    "`rule_name`= #{ruleName}, " +
                    "`app_id`=#{appId}, " +
                    "`product_name`=#{productName}, " +
                    "`level`=#{level}, " +
                    "`sources`=#{sources}, " +
                    "`new_user_type`=#{newUserType}, " +
                    "`brands`=#{brands} " +
            "WHERE (`id`=#{id});"
    })
    int update(RealtimeRule realtimeRule);

    @Update({"update `user-event`.realtime_rule set state = #{state} where id =#{id}"})
    int switchFlag(@Param("id") Integer id,@Param("state") Integer state);

    @Select({"<script>",
            "select * from `user-event`.realtime_rule",
            "<if test ='null != product'>" ,
            "  where app_id = #{product} " ,
            "</if>" ,
            "limit #{offset},#{limit}" ,
            "</script>",
    })
    List<RealtimeRule> queryList(@Param("product") String product,@Param("offset")Integer offset,@Param("limit")Integer limit);

    @Select({"<script>",
            "select count(1) from `user-event`.realtime_rule",
            "<if test ='null != product'>" ,
            "  where app_id = #{product} " ,
            "</if>" ,
            "</script>",
    })
    Integer queryCount(@Param("product") String product);
}
