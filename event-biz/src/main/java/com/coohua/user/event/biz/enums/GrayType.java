package com.coohua.user.event.biz.enums;

/**
 * <AUTHOR>
 * @since 2021/8/23
 */
public enum  GrayType {
    DEVICE(1,"DEVICE"),
    USER(2,"USER"),
    UNION(3,"UNION")
    ;

    private Integer type;
    private String desc;

    GrayType(Integer type, String desc) {
        this.type = type;
        this.desc = desc;
    }

    public Integer getType() {
        return type;
    }

    public void setType(Integer type) {
        this.type = type;
    }

    public String getDesc() {
        return desc;
    }

    public void setDesc(String desc) {
        this.desc = desc;
    }


    public static GrayType get(Integer type){
        for (GrayType grayType : GrayType.values()){
            if (grayType.getType().equals(type)){
                return grayType;
            }
        }
        return null;
    }
}
