package com.coohua.user.event.biz.core.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import java.util.Date;
import com.baomidou.mybatisplus.annotation.TableId;
import java.io.Serializable;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * 设备激活数据表
 * </p>
 *
 * <AUTHOR>
 * @since 2020-09-07
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value="BpChannelDeviceResult对象", description="设备激活数据表")
public class BpChannelDeviceResult implements Serializable {

    private static final long serialVersionUID = 1L;

    private String productGroup;

    private String productName;

    private String productChannel;

    private Integer activateDevice;

    private Integer activate1Device;

    private Integer activate2Device;

    private Integer activate3Device;

    private Integer activate4Device;

    private Integer activate5Device;

    private Integer activate6Device;

    private Integer activate7Device;

    private Integer activate14Device;

    private Integer activate30Device;

    private Date logday;


}
