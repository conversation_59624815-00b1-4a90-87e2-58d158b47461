package com.coohua.user.event.biz.user.mapper;

import com.coohua.user.event.biz.dc.entity.WithdrawOrderDetail;
import com.coohua.user.event.biz.user.entity.OsBean;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2020/9/14
 */
public interface BpMallMapper {

    @Select({"select * from ${tableName} where id >= #{idMin} limit #{page}"})
    List<WithdrawOrderDetail> queryOrderList(@Param("tableName")String tableName,@Param("idMin")Long idMin,@Param("page")Long page);

    @Select({"select count(1) from ${tableName} where create_time > #{createTime}"})
    Long countOrderList(@Param("tableName")String tableName,@Param("createTime") Long creatTime);

    @Select({"select id from ${tableName} where create_time > #{createTime} order by create_time asc limit 1"})
    Long selectMinId(@Param("tableName")String tableName,@Param("createTime") Long creatTime);

    @Select({"select id from ${tableName} where create_time <= #{createTime} order by create_time desc limit 1"})
    Long selectMaxId(@Param("tableName")String tableName,@Param("createTime") Long creatTime);

    @Select({"select id from ${tableName} where user_id =#{userId} and order_no =#{orderNo} limit 1"})
    WithdrawOrderDetail queryByOrderNoAndUserID(@Param("tableName")String tableName,@Param("userId") Long userId,@Param("orderNo") String orderNo);

    @Select({"<script>",
            "select * from ${tableName} where  ",
            "order_no in ",
            "<foreach collection='orderNoList' index='index' item='item' open='(' separator=',' close=')'>#{item}</foreach>",
            "</script>"})
    List<WithdrawOrderDetail> queryByOrderNoListAndUserID(@Param("tableName")String tableName, @Param("orderNoList") List<String> orderNoList);


    @Select({"<script>",
            "select order_no,os from ${tableName} where  " +
                    " app_id = #{appId} and os is not null and order_no in ",
            "<foreach collection='idList' index='index' item='item' open='(' separator=',' close=')'>#{item}</foreach>",
            "</script>"})
    List<OsBean> queryOsList(@Param("tableName")String tableName,@Param("idList")List<Long> idList, @Param("appId")Integer appId);
}
