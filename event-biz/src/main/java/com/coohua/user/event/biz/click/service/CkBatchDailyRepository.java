package com.coohua.user.event.biz.click.service;

import com.coohua.user.event.biz.click.entity.ActiveToConvertAdv;
import com.coohua.user.event.biz.dc.entity.WithdrawOrderDetail;
import com.coohua.user.event.biz.util.DateUtil;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.tomcat.jni.Time;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.stereotype.Component;
import ru.yandex.clickhouse.ClickHouseDataSource;
import ru.yandex.clickhouse.settings.ClickHouseProperties;

import javax.annotation.PreDestroy;
import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.SQLException;
import java.util.Date;
import java.util.List;
import java.util.concurrent.TimeUnit;

/**
 * 暂时不支持并发
 */
@Slf4j
@Component
public class CkBatchDailyRepository implements InitializingBean {

    private static final String USER = "default";
    private static final String PASSWORD = "Phw7a7A4";
    private static final String URL = "************************************";
    private ClickHouseDataSource dataSource;
    private Connection connection;
    private PreparedStatement ps;
    public int batchCounter = 0;
    private static final String INSERT_WITHDRAW = "INSERT INTO dwd.withdraw_s2_local (logday, product, product_name, product_group, order_no, user_id, os, device_id_bp,sub_type, status, title, channel, amount, check_auth, extra, create_time, update_time,wechat_id, withdraw_type) values (?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)";

    private static final String DROP_PARTITION = "ALTER TABLE dwd.withdraw_s2_local DROP PARTITION '%s'";
    private static final String OPTIMIZE_TABLE = "optimize table dwd.withdraw_s2_local partition '%s'";
    private static final String DROP_OLD_DATA = "alter table dwd.withdraw_s2_local drop partition '%s'";
    private static final String DROP_PARTITION_LOCAL = "ALTER TABLE dwd.withdraw_local on cluster 'cluster-1' DROP PARTITION '%s'";

    private static final String INSERT_INTO_DIST = "insert into dwd.withdraw_dist select * from dwd.withdraw_s2_local where logday = '%s'";
    @PreDestroy
    public void destroy() {
        if (ps != null) {
            try {
                ps.close();
            } catch (SQLException throwables) {
                log.error("", throwables);
            }
        }

        if (connection != null) {
            try {
                connection.close();
            } catch (SQLException throwables) {
                log.error("", throwables);
            }
        }

    }

    @Override
    public void afterPropertiesSet() throws Exception {
        Class.forName("ru.yandex.clickhouse.ClickHouseDriver");
        ClickHouseProperties properties = new ClickHouseProperties();
        properties.setUser(USER);
        properties.setPassword(PASSWORD);
        dataSource = new ClickHouseDataSource(URL, properties);
    }

    public void batchSave(List<WithdrawOrderDetail> withdrawOrderDetails) throws SQLException {
        if (connection == null) {
            connection = dataSource.getConnection();
        }
        if (ps == null) {
            ps = connection.prepareStatement(INSERT_WITHDRAW);
        }
        for (WithdrawOrderDetail withdrawOrderDetail : withdrawOrderDetails) {
            addToBatch(withdrawOrderDetail);
        }
        long begin = System.currentTimeMillis();
        ps.executeBatch();
        log.info("[CK-Daily]保存数据成功! batchSize:{}, cost:{}mms", batchCounter, System.currentTimeMillis() - begin);
        batchCounter = 0;
    }

    public synchronized void  executeBatch() throws SQLException {
        if (ps != null) {
            long begin = System.currentTimeMillis();
            if (batchCounter > 0) {
                ps.executeBatch();
            }
            log.info("[CK-Daily]保存数据成功! batchSize:{}, cost:{}mms", batchCounter, System.currentTimeMillis() - begin);
            batchCounter = 0;
        }
    }

    @SneakyThrows
    public synchronized void dropPartition(String logday){
        if (connection == null) {
            connection = dataSource.getConnection();
        }
        PreparedStatement psxDrop = connection.prepareStatement(String.format(DROP_PARTITION_LOCAL,logday));
        psxDrop.execute();
        log.info("DROP PARTITION withdraw_local {} SUCCESS",logday);
        TimeUnit.SECONDS.sleep(10);
        psxDrop.close();
    }

    @SneakyThrows
    public synchronized void insertAdvToCk(List<ActiveToConvertAdv> convertAdvs){
        if (connection == null) {
            connection = dataSource.getConnection();
        }
        PreparedStatement tps = connection.prepareStatement("insert into dwd.active_to_convert_adv(adv_id, dsp) values (?,?)");

        for (ActiveToConvertAdv activeToConvertAdv:convertAdvs){
            int colIndex = 1;
            tps.setString(colIndex++, activeToConvertAdv.getAdvId());
            tps.setString(colIndex++, activeToConvertAdv.getDsp());
            tps.addBatch();
        }
        tps.executeBatch();
        tps.close();
        TimeUnit.SECONDS.sleep(1);
        PreparedStatement optimize = connection.prepareStatement("optimize table dwd.active_to_convert_adv");
        optimize.execute();
        log.info("optimize active_to_convert_adv over");
        TimeUnit.SECONDS.sleep(1);
        optimize.close();
    }

    public synchronized void insertIntoDist(String logday){
        try {
            log.info("transferDataToCkDaily: step3");
            if (connection == null) {
                connection = dataSource.getConnection();
            }
            // drop history data
            Date begin = DateUtil.dateIncreaseByDay(DateUtil.stringToDate(logday),-4);
            PreparedStatement dropOldData = connection.prepareStatement(String.format(DROP_OLD_DATA,DateUtil.dateToString(begin)));
            dropOldData.execute();
            log.info("drop withdraw_s2_local partition:{} over",DateUtil.dateToString(begin));
            dropOldData.close();
            // optimize table
            PreparedStatement optimize = connection.prepareStatement(String.format(OPTIMIZE_TABLE,logday));
            optimize.execute();
            log.info("optimize withdraw_s2_local partition:{} over",logday);
            TimeUnit.SECONDS.sleep(10);
            optimize.close();
            PreparedStatement psxDrop = connection.prepareStatement(String.format(DROP_PARTITION_LOCAL,logday));
            psxDrop.execute();
            log.info("DROP PARTITION withdraw_local {} SUCCESS",logday);
            TimeUnit.SECONDS.sleep(10);
            psxDrop.close();
            PreparedStatement psx = connection.prepareStatement(String.format(INSERT_INTO_DIST,logday));
            psx.execute();
            log.info("INSERT PARTITION {} SUCCESS",logday);
            TimeUnit.SECONDS.sleep(20);
            psx.close();
        }catch (Exception e){
            log.info("ex:",e);
        }

    }

    public synchronized  void addToBatch(WithdrawOrderDetail withdrawOrderDetail) throws SQLException {
        if (connection == null) {
            connection = dataSource.getConnection();
        }
        if (ps == null) {
            ps = connection.prepareStatement(INSERT_WITHDRAW);
        }
        int colIndex = 1;
        ps.setDate(colIndex++, withdrawOrderDetail.getLogdayCk());
        ps.setString(colIndex++, withdrawOrderDetail.getProductEn());
        ps.setString(colIndex++, withdrawOrderDetail.getProductCn());
        ps.setString(colIndex++, withdrawOrderDetail.getProductGroup());
        ps.setString(colIndex++, withdrawOrderDetail.getOrderNo());
        ps.setLong(colIndex++, withdrawOrderDetail.getUserId());
        ps.setString(colIndex++,withdrawOrderDetail.getOs());
        ps.setString(colIndex++,withdrawOrderDetail.getDeviceId());
        ps.setInt(colIndex++,withdrawOrderDetail.getSubType());
        ps.setInt(colIndex++,withdrawOrderDetail.getStatus());
        ps.setString(colIndex++,withdrawOrderDetail.getTitle());
        ps.setInt(colIndex++,withdrawOrderDetail.getChannel());
        ps.setInt(colIndex++,withdrawOrderDetail.getAmount());
        ps.setInt(colIndex++,withdrawOrderDetail.getCheckAuth());
        ps.setString(colIndex++,withdrawOrderDetail.getExtra());
        ps.setLong(colIndex++,withdrawOrderDetail.getCreateTime());
        ps.setLong(colIndex++,withdrawOrderDetail.getUpdateTime());
        ps.setLong(colIndex++,withdrawOrderDetail.getWechatId());
        ps.setInt(colIndex++,withdrawOrderDetail.getWithdrawType());
        ps.addBatch();
        batchCounter++;
    }

}
