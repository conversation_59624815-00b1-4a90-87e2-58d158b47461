package com.coohua.user.event.biz.click.service;

import com.coohua.user.event.biz.dc.entity.WithdrawOrderDetail;
import com.coohua.user.event.biz.util.DateUtil;
import lombok.extern.slf4j.Slf4j;
import org.checkerframework.checker.units.qual.C;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.stereotype.Component;
import ru.yandex.clickhouse.ClickHouseDataSource;
import ru.yandex.clickhouse.settings.ClickHouseProperties;

import javax.annotation.PreDestroy;
import java.sql.Connection;
import java.sql.Date;
import java.sql.PreparedStatement;
import java.sql.SQLException;
import java.util.Collections;
import java.util.List;
import java.util.concurrent.TimeUnit;

/**
 * 暂时不支持并发
 */
@Slf4j
@Component
public class CkBatchRepository implements InitializingBean {

    private static final String USER = "default";
    private static final String PASSWORD = "Phw7a7A4";
    private static final String URL = "************************************";
    private ClickHouseDataSource dataSource;
    private Connection connection;
    private PreparedStatement ps;
    public int batchCounter = 0;
    private static final String INSERT_WITHDRAW = "INSERT INTO dwd.withdraw_s1_local (logday, product, product_name, product_group, order_no, user_id, os, device_id_bp,sub_type, status, title, channel, amount, check_auth, extra, create_time, update_time,wechat_id, withdraw_type) values (?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)";

    @PreDestroy
    public void destroy() {
        if (ps != null) {
            try {
                ps.close();
            } catch (SQLException throwables) {
                log.error("", throwables);
            }
        }

        if (connection != null) {
            try {
                connection.close();
            } catch (SQLException throwables) {
                log.error("", throwables);
            }
        }

    }

    @Override
    public void afterPropertiesSet() throws Exception {
        Class.forName("ru.yandex.clickhouse.ClickHouseDriver");
        ClickHouseProperties properties = new ClickHouseProperties();
        properties.setUser(USER);
        properties.setPassword(PASSWORD);
        dataSource = new ClickHouseDataSource(URL, properties);
    }

    public void batchSave(List<WithdrawOrderDetail> withdrawOrderDetails) throws SQLException {
        if (connection == null) {
            connection = dataSource.getConnection();
        }
        if (ps == null) {
            ps = connection.prepareStatement(INSERT_WITHDRAW);
        }
        for (WithdrawOrderDetail withdrawOrderDetail : withdrawOrderDetails) {
            addToBatch(withdrawOrderDetail);
        }
        long begin = System.currentTimeMillis();
        ps.executeBatch();
        log.info("[CK]保存数据成功! batchSize:{}, cost:{}mms", batchCounter, System.currentTimeMillis() - begin);
        batchCounter = 0;
    }

    public void executeBatch() throws SQLException {
        if (ps != null) {
            long begin = System.currentTimeMillis();
            ps.executeBatch();
            log.info("[CK]保存数据成功! batchSize:{}, cost:{}mms", batchCounter, System.currentTimeMillis() - begin);
            batchCounter = 0;
        }
    }

    public synchronized  void addToBatch(WithdrawOrderDetail withdrawOrderDetail) throws SQLException {
        if (connection == null) {
            connection = dataSource.getConnection();
        }
        if (ps == null) {
            ps = connection.prepareStatement(INSERT_WITHDRAW);
        }
        int colIndex = 1;
        ps.setDate(colIndex++, withdrawOrderDetail.getLogdayCk());
        ps.setString(colIndex++, withdrawOrderDetail.getProductEn());
        ps.setString(colIndex++, withdrawOrderDetail.getProductCn());
        ps.setString(colIndex++, withdrawOrderDetail.getProductGroup());
        ps.setString(colIndex++, withdrawOrderDetail.getOrderNo());
        ps.setLong(colIndex++, withdrawOrderDetail.getUserId());
        ps.setString(colIndex++,withdrawOrderDetail.getOs());
        ps.setString(colIndex++,withdrawOrderDetail.getDeviceId());
        ps.setInt(colIndex++,withdrawOrderDetail.getSubType());
        ps.setInt(colIndex++,withdrawOrderDetail.getStatus());
        ps.setString(colIndex++,withdrawOrderDetail.getTitle());
        ps.setInt(colIndex++,withdrawOrderDetail.getChannel());
        ps.setInt(colIndex++,withdrawOrderDetail.getAmount());
        ps.setInt(colIndex++,withdrawOrderDetail.getCheckAuth());
        ps.setString(colIndex++,withdrawOrderDetail.getExtra());
        ps.setLong(colIndex++,withdrawOrderDetail.getCreateTime());
        ps.setLong(colIndex++,withdrawOrderDetail.getUpdateTime());
        ps.setLong(colIndex++,withdrawOrderDetail.getWechatId());
        ps.setInt(colIndex++,withdrawOrderDetail.getWithdrawType());
        ps.addBatch();
        batchCounter++;
    }

}
