package com.coohua.user.event.biz.util;

import com.coohua.user.event.biz.service.OssService;
import com.weibo.api.motan.config.springsupport.util.SpringBeanUtil;
import lombok.extern.slf4j.Slf4j;

import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;
import java.nio.file.Files;
import java.text.SimpleDateFormat;
import java.util.Date;

@Slf4j
public class UploadFileToOSSUtil {

    public static final String EXCEL_PATH = "tempExcelCanDelete/";

    public static String uploadFileToOss(String bucket, File file, String path) {
        try {
            String suffix = new SimpleDateFormat("yyMMddHHmmssSSS").format(new Date());
            String innerPath = path + suffix + file.getName();
            OssService ossService = SpringContextHolder.getBean(OssService.class);
            ossService.uploadFile(bucket, innerPath, Files.newInputStream(file.toPath()));
            return OssService.OSS_URL + "/" + innerPath;
        } catch (IOException e) {
            log.error("上传文件失败！", e);
            throw new IllegalArgumentException("上传文件失败！请重试");
        }
    }
}
