package com.coohua.user.event.biz.user.service;

import com.coohua.user.event.biz.click.entity.ProductEntity;
import com.coohua.user.event.biz.click.service.ClickHouseService;
import com.coohua.user.event.biz.core.service.BpAmountResultService;
import com.coohua.user.event.biz.user.entity.UserInfoEntity;
import com.coohua.user.event.biz.user.mapper.BaseQueryMapper;
import com.coohua.user.event.biz.util.AppDrawConf;
import com.coohua.user.event.biz.util.DateUtil;
import com.coohua.user.event.biz.util.Lists;
import com.coohua.user.event.biz.util.Strings;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2020/9/3
 */
@Slf4j
@Service
public class BaseQueryService {

    @Resource
    private BaseQueryMapper baseQueryMapper;

    @Autowired
    private BpAmountResultService bpAmountResultService;

    @Autowired
    private ClickHouseService clickHouseService;

    private List<UserInfoEntity> queryAmount(String tableName,Date crashDate){
        try {
            log.info("查询表：{}",tableName);
            String currentDay = DateUtil.dateToString(crashDate);
            Long today = DateUtil.dateIncreaseByDay(crashDate,+1).getTime();
            Long before2Day = DateUtil.dateIncreaseByDay(crashDate,-2).getTime();
            return baseQueryMapper.queryAmountInfo(tableName,before2Day,today,currentDay);
        }catch (Exception e){
            log.error("表{}异常",tableName);
        }
        return new ArrayList<>();
    }

//    @Scheduled(cron = "30 41 1 * * ?")
    public void tes(){
        syToResult("2022-11-08");
    }

    public static void main(String[] args) {
        Date crashDate =  DateUtil.dateIncreaseByDay(DateUtil.stringToDate("2025-02-21"),-1);
        System.out.println(DateUtil.dateToString(crashDate, DateUtil.DATETIME_PATTERN));
        Long today = DateUtil.dateIncreaseByDay(crashDate,+1).getTime();
        Long before2Day = DateUtil.dateIncreaseByDay(crashDate,-2).getTime();
        System.out.println(DateUtil.dateToString(new Date(today), DateUtil.DATETIME_PATTERN));
        System.out.println(DateUtil.dateToString(new Date(before2Day), DateUtil.DATETIME_PATTERN));
        String currentDay = DateUtil.dateToString(crashDate);
        System.out.println(before2Day);
        System.out.println(today);
        System.out.println(currentDay);
    }

    public void syToResult(String date){
        log.info("开始提现数据同步...");
        log.info("Param:{}",date);
        Date crashDate =  DateUtil.dateIncreaseByDay(DateUtil.stringToDate(DateUtil.dateToString(new Date())),-1);
        if (Strings.noEmpty(date)) {
            crashDate = DateUtil.stringToDate(date);
        }

        List<UserInfoEntity> userInfoEntityList = new ArrayList<>();

        String currentDay = DateUtil.dateToString(crashDate);
        log.info("查询日期{}",currentDay);

        List<ProductEntity> productEntityList = clickHouseService.queryAllProduct();

        for (ProductEntity productEntity :productEntityList){
            try {
                List<String> tableNameList = AppDrawConf.appWithdrawTableMap.get(productEntity.getId());
                if ("zhuzhushijie".equals(productEntity.getProduct())){
                    tableNameList = Arrays.asList("bp_mall.withdraw_order_12");
                }
                if (tableNameList == null || tableNameList.size() == 0){
                    log.error("提现配置异常....");
                    continue;
                }

                for (String tableName : tableNameList){
                    List<UserInfoEntity> entities = queryAmount(tableName, crashDate);
                    if (entities.size() > 0) {
                        entities.forEach(userInfoEntity -> {
                            userInfoEntity.setRemark(productEntity.getProductName());
                            userInfoEntity.setRemarkGroup(productEntity.getProductGroup());
                            userInfoEntity.setRemarkId(productEntity.getId());
                        });
                        userInfoEntityList.addAll(entities);
                    }
                }
            }catch (Exception e){
                log.error("Er:",e);
            }
        }

        if (userInfoEntityList.size() == 0){
            log.warn("昨日未查到提现数据...");
            return;
        }

        // 合并求和
        Map<Integer,List<UserInfoEntity>> resMap = userInfoEntityList.parallelStream()
                .collect(Collectors.groupingBy(UserInfoEntity::getRemarkId));
        Set<Integer> appKeySet = resMap.keySet();
        List<UserInfoEntity> resultList = new ArrayList<>();

        appKeySet.forEach(appId ->{
            List<UserInfoEntity> entities = resMap.get(appId);
            if (Lists.isEmpty(entities)){
                return;
            }
            UserInfoEntity entityUser = entities.get(0);
            Map<String,List<UserInfoEntity>> entityMap = entities.parallelStream().collect(Collectors.groupingBy(UserInfoEntity::getSystem));

            entityMap.keySet().forEach(system -> {
                List<UserInfoEntity> systemList = entityMap.get(system);
                UserInfoEntity entity = systemList.get(0);
                resultList.add(new UserInfoEntity(){{
                    setDateStr(entity.getDateStr());
                    setPkgId(entity.getPkgId());
                    setRemark(entity.getRemark());
                    setRemarkGroup(entity.getRemarkGroup());
                    setSystem(entity.getSystem().toLowerCase());
                    BigDecimal bigDecimal = BigDecimal.ZERO;
                    for (UserInfoEntity userInfoEntity:systemList){
                        bigDecimal = bigDecimal.add(userInfoEntity.getAmount());
                    }
                    setAmount(bigDecimal);
                }});
            });

        });
        // del AlmostComplete
        bpAmountResultService.delResultByDayAndApp(currentDay);
        // saveBatch
        bpAmountResultService.saveBatch(resultList);
        log.info("结束提现数据同步...");
    }

}
