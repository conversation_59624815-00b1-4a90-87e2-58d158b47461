package com.coohua.user.event.biz.dc.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.util.Date;

@Data
@TableName("wechat_order_temp.yd_withdraw_mch_result_date_d")
public class YdWithdrawMchResultDated {
    private Long id;
    private String logday;
    private String product;
    private String productName;
    private String productGroup;
    private String orderNo;
    private String batchId;
    private Long userId;
    private String os;
    private Short status;
    private String title;
    private Long channel;
    private Long amount;
    private Long checkAuth;
    private String mchId;
    private Short withdrawType;
    private Long wechatId;
    private String openId;
    private String unionId;
    private Date createTime;
    private Date updateTime;

}
