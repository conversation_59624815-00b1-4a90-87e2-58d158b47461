package com.coohua.user.event.biz.click.mapper;

import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2021/12/28
 */
public interface ClickHouseAdsMapper {


    @Select({
            "<script>",
            "select uniqExact(user_id) as user_count from ads.relationship_user_retain where user_id in ",
            "<foreach collection='ids' index='index' item='item' open='(' separator=',' close=')'>#{item}</foreach>",
            " and app_id = #{appId}",
            "</script>",
    })
    Integer countRetainUser(@Param("ids") List<Long> userList, @Param("appId") Integer appId);
}
