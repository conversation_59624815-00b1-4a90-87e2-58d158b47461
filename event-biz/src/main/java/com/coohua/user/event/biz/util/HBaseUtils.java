package com.coohua.user.event.biz.util;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.apache.hadoop.hbase.Cell;
import org.apache.hadoop.hbase.TableName;
import org.apache.hadoop.hbase.client.*;
import org.apache.hadoop.hbase.util.Bytes;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static com.coohua.user.event.biz.util.HBaseParameterEnum.DEFAULT_COLUMN;
import static com.coohua.user.event.biz.util.HBaseParameterEnum.FAMILY;
import static java.util.stream.Collectors.toList;

@Slf4j
public class HBaseUtils {

    public static boolean saveToHadoop(Connection connection, String tableName, String rowKey, byte[] content) {

        try (Table table = connection.getTable(TableName.valueOf(tableName))) {

            String processedRowKey = hashRowKeyByLastCharacter(rowKey);

            Put put = new Put(Bytes.toBytes(processedRowKey));
            put.addColumn(HBaseParameterEnum.FAMILY.getBinaryContent(), HBaseParameterEnum.DEFAULT_COLUMN.getBinaryContent(),
                    content);

            table.put(put);

            return true;
        } catch (Exception e) {
            log.error("Hadoop表存储失败 :", e);
            return false;
        }
    }

    public static boolean batchSaveToHadoopWithoutHash(Connection connection, String tableName, Map<String, byte[]> rows) {

        List<Put> saveRequest = rows.entrySet().stream()
                .map(entry -> {
                    Put put = new Put(Bytes.toBytes(entry.getKey()));
                    put.addColumn(HBaseParameterEnum.FAMILY.getBinaryContent(), HBaseParameterEnum.DEFAULT_COLUMN.getBinaryContent(),
                            entry.getValue());
                    return put;
                }).collect(toList());

        try (Table table = connection.getTable(TableName.valueOf(tableName))) {

            table.put(saveRequest);

            return true;
        } catch (Exception e) {
            log.error("Hadoop表存储失败 :", e);
            return false;
        }
    }

    public static String getCellValStr(Result res,String colName){
        List<Cell> cells = res.listCells();
        if(cells!=null && cells.size()>0){
            byte[] val = res.getValue(Bytes.toBytes("family"),Bytes.toBytes(colName));
            return Bytes.toString(val);
        }
        return null;
    }

    public static String getCellValStr(Result res){
        List<Cell> cells = res.listCells();
        if(cells!=null && cells.size()>0){
            byte[] val = res.getValue(Bytes.toBytes("family"),Bytes.toBytes("qualifier"));
            return Bytes.toString(val);
        }
        return null;
    }

    /**
     * 本工程唯一指定splitKey
     */
    public static final byte[][] ONLY_SPLIT_KEYS = {Bytes.toBytes("0"), Bytes.toBytes("1"), Bytes.toBytes("2"),
            Bytes.toBytes("3"), Bytes.toBytes("4"), Bytes.toBytes("5"), Bytes.toBytes("6"),
            Bytes.toBytes("7"), Bytes.toBytes("8"), Bytes.toBytes("9")};

    /**
     * rowKey前缀数组
     */
    private static final String[] ROW_KEY_PREFIX_ARRAY = {"0", "1", "2", "3", "4", "5", "6", "7", "8", "9"};

    /**
     * char常量-0
     */
    public static final char CHAR_ZERO = '0';

    /**
     * char常量-9
     */
    public static final char CHAR_NINE = '9';
    /**
     * 根据rowKey最后一位来散列rowKey：根据ASCII码加上0-9前缀
     *
     * @param rowKey rowKey
     * @return 散列后的rowKey
     */
    public static String hashRowKeyByLastCharacter(String rowKey) {
        if(StringUtils.isBlank(rowKey)){
            return "";
        }
        char lastCharacter = rowKey.charAt(rowKey.length() - 1);
        int prefixIndex;
        if (lastCharacter > CHAR_NINE || lastCharacter < CHAR_ZERO) {
            prefixIndex = lastCharacter % 10;
        } else {
            prefixIndex = lastCharacter - CHAR_ZERO;
        }

        return ROW_KEY_PREFIX_ARRAY[prefixIndex] + rowKey;
    }

    /**
     * 指定表名从Hadoop中检索特定数据
     *
     * @param connection 连接
     * @param tableName  表名
     * @param rowKey     行key
     * @return 数据检索结果
     */
    public static byte[] searchDataFromHadoop(Connection connection, String tableName, String rowKey) {

        try (Table table = connection.getTable(TableName.valueOf(tableName))) {

            String processedRowKey = hashRowKeyByLastCharacter(rowKey);

            Get get = new Get(Bytes.toBytes(processedRowKey));

            Result result = table.get(get);

            return result.getValue(FAMILY.getBinaryContent(), DEFAULT_COLUMN.getBinaryContent());

        } catch (Exception e) {
            log.warn("Hadoop表检索失败:{}", e.getMessage());
            return null;
        }
    }

    public static byte[] searchDataFromHadoopWithNoHashRowKey(Connection connection, String tableName, String rowKey) {

        try (Table table = connection.getTable(TableName.valueOf(tableName))) {


            Get get = new Get(Bytes.toBytes(rowKey));

            Result result = table.get(get);

            return result.getValue(FAMILY.getBinaryContent(), DEFAULT_COLUMN.getBinaryContent());

        } catch (Exception e) {
            log.warn("Hadoop表检索失败:{}", e.getMessage());
            return null;
        }
    }


    public static Map<String,String> searchDataFromHadoopList(Connection connection, String tableName, List<String> rowKey) {
        try (Table table = connection.getTable(TableName.valueOf(tableName))) {

            List<String> processedRowKeyList = rowKey.stream().map(HBaseUtils::hashRowKeyByLastCharacter).collect(Collectors.toList());
            List<Get> getList = processedRowKeyList.stream().map(r -> new Get(Bytes.toBytes(r))).collect(Collectors.toList());
            Result[] result = table.get(getList);

            return Arrays.stream(result)
                    .collect(Collectors.toMap(
                            rs-> Bytes.toString(rs.getRow()),
                            rs -> {
                                byte[] r = rs.getValue(FAMILY.getBinaryContent(), DEFAULT_COLUMN.getBinaryContent());
                                if (r == null){
                                    return Strings.GLOBAL_EMPTY;
                                }else {
                                    return Bytes.toString(r);
                                }
                            },
                            (r1, r2)->r2));
        } catch (Exception e) {
            log.warn("Hadoop表检索失败:{}", e.getMessage());
            return Collections.emptyMap();
        }
    }
    public static Map<String,String> searchDataFromHadoopListWithNoHashRowKey(Connection connection, String tableName, List<String> rowKey) {
        try (Table table = connection.getTable(TableName.valueOf(tableName))) {

            List<Get> getList = rowKey.stream().map(r -> new Get(Bytes.toBytes(r))).collect(Collectors.toList());
            Result[] result = table.get(getList);

            return Arrays.stream(result)
                    .collect(Collectors.toMap(
                            rs-> Bytes.toString(rs.getRow()),
                            rs -> {
                                byte[] r = rs.getValue(FAMILY.getBinaryContent(), DEFAULT_COLUMN.getBinaryContent());
                                if (r == null){
                                    return Strings.GLOBAL_EMPTY;
                                }else {
                                    return Bytes.toString(r);
                                }
                            },
                            (r1, r2)->r2));
        } catch (Exception e) {
            log.warn("Hadoop表检索失败:{}", e.getMessage());
            return Collections.emptyMap();
        }
    }
    /**
     * 指定表名从Hadoop中检索特定数据(无分片版本)
     *
     * @param connection 连接
     * @param tableName  表名
     * @param rowKey     行key
     * @return 数据检索结果
     */
    public static Pair<Boolean, byte[]> searchDataFromHadoopWithoutHash(Connection connection, String tableName, String rowKey) {

        try (Table table = connection.getTable(TableName.valueOf(tableName))) {

            Get get = new Get(Bytes.toBytes(rowKey));

            Result result = table.get(get);

            return Pair.of(true, result.getValue(HBaseParameterEnum.FAMILY.getBinaryContent(),
                    HBaseParameterEnum.DEFAULT_COLUMN.getBinaryContent()));

        } catch (Exception e) {
            log.warn("Hadoop表检索失败:{}", e.getMessage());
            return Pair.of(false, null);
        }
    }

    public static Pair<Boolean, byte[]> searchDataFromHadoopUserId(Connection connection, String tableName, String rowKey) {

        try (Table table = connection.getTable(TableName.valueOf(tableName))) {

            Get get = new Get(Bytes.toBytes(rowKey));
            get.addColumn(Bytes.toBytes("family"), Bytes.toBytes("user_id"));
            Result result = table.get(get);

            return Pair.of(true, result.getValue(Bytes.toBytes("family"), Bytes.toBytes("user_id")));

        } catch (Exception e) {
            log.warn("Hadoop表检索失败:{}", e.getMessage());
            return Pair.of(false, null);
        }
    }

    public static Map<String,String> searchDataFromHadoopWithoutHash(Connection connection, String tableName, List<String> rowKey){
        try (Table table = connection.getTable(TableName.valueOf(tableName))) {

            List<Get> getList = rowKey.stream().map(r -> new Get(Bytes.toBytes(r))).collect(Collectors.toList());
            Result[] result = table.get(getList);

            return Arrays.stream(result).collect(Collectors.toMap(
                    rs -> Bytes.toString(rs.getRow()),
                    rs -> {
                        byte[] r = rs.getValue(HBaseParameterEnum.FAMILY.getBinaryContent(),
                                HBaseParameterEnum.DEFAULT_COLUMN.getBinaryContent());
                        if (r == null){
                            return Strings.GLOBAL_EMPTY;
                        }else {
                            return Bytes.toString(r);
                        }
                    },
                    (r1,r2) -> r1
            ));
        } catch (Exception e) {
            log.warn("Hadoop表检索失败:{}", e.getMessage());
            return Collections.emptyMap();
        }
    }

    public static boolean batchDeleteFromHbase(Connection connection, String tableName, Stream<String> rowKeyStream,
                                               Function<String, String> keyProcessor) {

        List<Delete> deleteActions = rowKeyStream
                .map(keyProcessor)
                .map(Bytes::toBytes)
                .map(Delete::new)
                .collect(toList());

        try (Table table = connection.getTable(TableName.valueOf(tableName))) {

            table.delete(deleteActions);

            return true;

        } catch (Exception e) {
            log.error("Hadoop表数据删除失败 ", e);
            return false;
        }
    }

    public static boolean saveToHadoopBatch(Connection connection, String tableName, Map<String,byte[]> targetContent) {

        try (Table table = connection.getTable(TableName.valueOf(tableName))) {
            List<Put> puts = new ArrayList<>();
            targetContent.forEach((processedRowKey, content) -> {
                Put put = new Put(Bytes.toBytes(processedRowKey));
                put.addColumn(FAMILY.getBinaryContent(), DEFAULT_COLUMN.getBinaryContent(), content);
                puts.add(put);
            });

            table.put(puts);
            return true;
        } catch (Exception e) {
            log.error("Hadoop表存储失败 ", e);
            return false;
        }
    }
}
