package com.coohua.user.event.biz.ap.mapper;

import com.coohua.user.event.biz.ap.entity.ClickConfig;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2021/9/3
 */
public interface ClickConfigMapper {

    @Select("select * from `bp-ap`.click_config where del_flag = 1 and app_id = #{appId}")
    List<ClickConfig> queryAllConfig(@Param("appId")Integer appId);
}
