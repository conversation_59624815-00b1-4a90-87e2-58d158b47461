package com.coohua.user.event.biz.service;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.coohua.user.event.biz.click.entity.VideoAdReportEntitiy;
import com.coohua.user.event.biz.enums.AdPosType;
import com.coohua.user.event.biz.service.rsp.BatchQueryResponse;
import com.coohua.user.event.biz.util.DateUtil;
import com.coohua.user.event.biz.util.HBaseUtils;
import com.coohua.user.event.biz.util.Lists;
import com.google.common.collect.Maps;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.apache.hadoop.hbase.TableName;
import org.apache.hadoop.hbase.TableNotFoundException;
import org.apache.hadoop.hbase.client.*;
import org.apache.hadoop.hbase.io.compress.Compression;
import org.apache.hadoop.hbase.io.encoding.DataBlockEncoding;
import org.apache.hadoop.hbase.util.Bytes;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.io.IOException;
import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * <AUTHOR>
 * @since 2021/6/25
 */
@Slf4j
@Service
public class HBaseUserAdViewService {

    @Resource(name = "linDormConnection")
    private Connection HBaseConnection;

    private final static String TABLE_NAME = "ad_ecpm_point";
    private final static  byte[] families = Bytes.toBytes("family");

    @PostConstruct
    public void createTable() throws IOException {
        try (Admin admin = HBaseConnection.getAdmin()) {
            // 建表
            try {
//                admin.disableTable(TableName.valueOf(TABLE_NAME));
//                admin.deleteTable(TableName.valueOf(TABLE_NAME));
                admin.getDescriptor(TableName.valueOf(TABLE_NAME));
            } catch (TableNotFoundException te) {
                ColumnFamilyDescriptor familyDescriptor = ColumnFamilyDescriptorBuilder
                        .newBuilder(families)
                        .setCompressionType(Compression.Algorithm.ZSTD)
                        .setDataBlockEncoding(DataBlockEncoding.DIFF)
                        .build();

                TableDescriptor tableDescriptor = TableDescriptorBuilder.newBuilder(TableName.valueOf(TABLE_NAME))
                        .setColumnFamily(familyDescriptor)
                        .setCompactionEnabled(true)
                        .build();

                admin.createTable(tableDescriptor, HBaseUtils.ONLY_SPLIT_KEYS);
            }
        } catch (IOException e) {
            log.error("{}表创建失败:{}", TABLE_NAME, e);
            throw e;
        }
    }

    public static final String AD_ECPM_POINT_TODAY = "ad_ecpm_point_today";

    private static final String ECPM_TODAY_KEY = "ecpm:sum:%s:%s:%s:%s";

    @PostConstruct
    public void createTableAdEcpmPointToday() throws IOException {
        try (Admin admin = HBaseConnection.getAdmin()) {
            // 建表
            try {
                admin.getDescriptor(TableName.valueOf(AD_ECPM_POINT_TODAY));
            } catch (TableNotFoundException te) {
                ColumnFamilyDescriptor familyDescriptor = ColumnFamilyDescriptorBuilder
                        .newBuilder(families)
                        .setCompressionType(Compression.Algorithm.ZSTD)
                        .setDataBlockEncoding(DataBlockEncoding.DIFF)
                        .setTimeToLive(60 * 60 * 24 * 3) //存三天
                        .build();

                TableDescriptor tableDescriptor = TableDescriptorBuilder.newBuilder(TableName.valueOf(AD_ECPM_POINT_TODAY))
                        .setColumnFamily(familyDescriptor)
                        .setCompactionEnabled(true)
                        .build();

                admin.createTable(tableDescriptor, HBaseUtils.ONLY_SPLIT_KEYS);
            }
        } catch (IOException e) {
            log.error("{}表创建失败:{}", AD_ECPM_POINT_TODAY, e);
            throw e;
        }
    }

    private String buildAdEcpmPointTodayKey(Integer appId, String userId, String os) {
        Date now = new Date();
        String day = DateUtil.formatDateForyyyyMMdd(now);
        return HBaseUtils.hashRowKeyByLastCharacter(String.format(ECPM_TODAY_KEY, day, appId, os, userId));
    }

    public void batchInsert(List<String> videoAdReportStringList){
        List<VideoAdReportEntitiy> list = videoAdReportStringList.parallelStream()
                .map(s -> JSON.parseObject(s, VideoAdReportEntitiy.class))
                .filter(r -> AdPosType.Rewarded_video.getCode().equals(r.getAdType()))
                .collect(Collectors.toList());

        Map<String,List<VideoAdReportEntitiy>> videoMap  = list.stream().collect(Collectors.groupingBy(
                        videoAdReportEntitiy -> this.buildEcpmSumKey(
                                videoAdReportEntitiy.getAppId(),
                                videoAdReportEntitiy.getUserId(),
                                videoAdReportEntitiy.getOs()
                        )
                ));

        Map<String,List<VideoAdReportEntitiy>> todayMap  = list.stream().collect(Collectors.groupingBy(
                videoAdReportEntitiy -> this.buildAdEcpmPointTodayKey(
                        videoAdReportEntitiy.getAppId(),
                        videoAdReportEntitiy.getUserId(),
                        videoAdReportEntitiy.getOs()
                )
        ));
        Map<String,BatchQueryResponse> responseMap = new HashMap<>();
        videoMap.forEach((k,v) ->{
            int ecpm = 0;
            Integer videoCount = 0;
            Long userId = 0L;
            for(VideoAdReportEntitiy reportEntitiy : v){
                ecpm = ecpm + Integer.parseInt(reportEntitiy.getPrice());
                videoCount ++;
                userId = Long.valueOf(reportEntitiy.getUserId());
            }
            BatchQueryResponse response = new BatchQueryResponse();
            response.setQueryKey(k);
            response.setUserId(userId);
            response.setEcpmSum(ecpm);
            response.setVideoExposureCount(videoCount);
            responseMap.put(k,response);
        });
        this.insertOrUpdate(responseMap, TABLE_NAME);

        responseMap.clear();

        todayMap.forEach((k,v) ->{
            int ecpm = 0;
            Integer videoCount = 0;
            Long userId = 0L;
            for(VideoAdReportEntitiy reportEntitiy : v){
                ecpm = ecpm + Integer.parseInt(reportEntitiy.getPrice());
                videoCount ++;
                userId = Long.valueOf(reportEntitiy.getUserId());
            }
            BatchQueryResponse response = new BatchQueryResponse();
            response.setQueryKey(k);
            response.setUserId(userId);
            response.setEcpmSum(ecpm);
            response.setVideoExposureCount(videoCount);
            responseMap.put(k,response);
        });
        this.insertOrUpdate(responseMap, AD_ECPM_POINT_TODAY);
    }

    /**
     * HBase 客户端无缓存，get -> put 效率高于 increment
     * （由于客户端没有缓存,所以无法批量提交）
     * @param batchQueryResponseMap
     */
    private void insertOrUpdate(Map<String,BatchQueryResponse> batchQueryResponseMap, String tableName){
        try (Table table = HBaseConnection.getTable(TableName.valueOf(tableName))) {
            Map<String,BatchQueryResponse> responseMap = queryResponseMap(batchQueryResponseMap, tableName);
            List<Put> puts = new ArrayList<>();
            batchQueryResponseMap.forEach((key,value) ->{
                BatchQueryResponse batchQueryResponse = responseMap.getOrDefault(key,new BatchQueryResponse(){{
                    setVideoExposureCount(0);
                    setEcpmSum(0);
                    setUserId(value.getUserId());
                    setQueryKey(value.getQueryKey());
                }});

                BatchQueryResponse saveBatchQueryResponse = new BatchQueryResponse();
                saveBatchQueryResponse.setQueryKey(value.getQueryKey());
                saveBatchQueryResponse.setUserId(value.getUserId());
                saveBatchQueryResponse.setEcpmSum(value.getEcpmSum() + batchQueryResponse.getEcpmSum());
                saveBatchQueryResponse.setVideoExposureCount(value.getVideoExposureCount()+batchQueryResponse.getVideoExposureCount());

                Put put = new Put(Bytes.toBytes(key));
                put.addColumn(families, Bytes.toBytes(QUERY_ID), Bytes.toBytes(JSONObject.toJSONString(saveBatchQueryResponse)));
                puts.add(put);
            });
            table.put(puts);
        } catch (IOException e) {
            log.error("QueryException:",e);
        }
    }

    private Map<String,BatchQueryResponse> queryResponseMap(Map<String,BatchQueryResponse> batchQueryResponseMap, String tableName){
        try (Table table = HBaseConnection.getTable(TableName.valueOf(tableName))) {
            // 先查询...
            List<Get> gets = batchQueryResponseMap.keySet()
                    .parallelStream()
                    .map(str->{
                        Get get = new Get(Bytes.toBytes(str));
                        get.addColumn(families, Bytes.toBytes(QUERY_ID));
                        return get;
                    }).collect(Collectors.toList());

            Result[] results = table.get(gets);

            Map<String,BatchQueryResponse> responseMap = new HashMap<>();
            for(Result result : results){
                String res = HBaseUtils.getCellValStr(result,QUERY_ID);
                if (StringUtils.isNotEmpty(res)){
                    BatchQueryResponse batchQueryResponse = JSON.parseObject(res,BatchQueryResponse.class);
                    responseMap.put(batchQueryResponse.getQueryKey(),batchQueryResponse);
                }
            }
            return responseMap;
        } catch (IOException e) {
            log.error("QueryException:",e);
        }
        return new HashMap<>();
    }

    public boolean isHs300ExUser(Long appId,Long user){
        String key = String.format("%s:%s",appId,user);
        Pair<Boolean, byte[]> result =  HBaseUtils.searchDataFromHadoopWithoutHash(HBaseConnection,"hs_user_score",key);
        return result.getRight() != null;
    }

    public Integer isHs300ExUserScore(Integer appId,Long user){
        try {
            String key = String.format("%s:%s", appId, user);
            Pair<Boolean, byte[]> result = HBaseUtils.searchDataFromHadoopUserId(HBaseConnection, "hs_user_score", key);
            if (result.getRight() != null) {
                ByteUserDevice byteUserDevice = JSONObject.parseObject(result.getRight(), ByteUserDevice.class);
                if (byteUserDevice != null) {
                    return byteUserDevice.getScore();
                }
            }
        }catch (Exception e){
            log.warn("Query Err:",e);
        }
        return 0;
    }

    @Data
    private static class ByteUserDevice{
        private Integer score;
    }

    public Integer singleQuery(Long appId,Long userId){
        Map<String,BatchQueryResponse> requestParam = Stream.of("android","ios")
                .collect(Collectors.toMap(os -> this.buildEcpmSumKey(appId.intValue(), String.valueOf(userId),os),
                os->{
                    BatchQueryResponse response = new BatchQueryResponse();
                    response.setQueryKey(buildEcpmSumKey(appId.intValue(), String.valueOf(userId),os));
                    response.setUserId(userId);
                    return response;
                },(u1,u2) -> u1));
        Map<String,BatchQueryResponse> queryResponseMap = this.queryResponseMap(requestParam, TABLE_NAME);
        if (queryResponseMap.isEmpty()){
            return 0;
        }

        Integer ecpm = 0;
        for (String key : queryResponseMap.keySet()){
            ecpm += queryResponseMap.get(key).getEcpmSum();
        }
        return ecpm;
    }

    public BatchQueryResponse singleQueryRsp(Long appId,Long userId){
        Map<String,BatchQueryResponse> requestParam = Stream.of("android","ios")
                .collect(Collectors.toMap(os -> this.buildEcpmSumKey(appId.intValue(), String.valueOf(userId),os),
                        os->{
                            BatchQueryResponse response = new BatchQueryResponse();
                            response.setQueryKey(buildEcpmSumKey(appId.intValue(), String.valueOf(userId),os));
                            response.setUserId(userId);
                            return response;
                        },(u1,u2) -> u1));
        Map<String,BatchQueryResponse> queryResponseMap = this.queryResponseMap(requestParam, TABLE_NAME);

        BatchQueryResponse batchQueryResponse = new BatchQueryResponse();
        batchQueryResponse.setEcpmSum(0);
        batchQueryResponse.setVideoExposureCount(0);
        if (queryResponseMap.isEmpty()){
            return batchQueryResponse;
        }

        Integer ecpm = 0;
        Integer videoCount = 0;
        for (String key : queryResponseMap.keySet()){
            ecpm += queryResponseMap.get(key).getEcpmSum();
            videoCount += queryResponseMap.get(key).getVideoExposureCount();
        }

        batchQueryResponse.setEcpmSum(ecpm);
        batchQueryResponse.setVideoExposureCount(videoCount);
        return batchQueryResponse;
    }

    public Map<Long, BatchQueryResponse> batchQuery(List<Long> userIdList,String os,Integer appId){
        Map<Long, BatchQueryResponse> responseMap =  Maps.newHashMap();
        if (Lists.isEmpty(userIdList)){
            return responseMap;
        }
        Map<String,BatchQueryResponse> requestParam = userIdList.parallelStream().collect(Collectors.toMap(userId->
                this.buildEcpmSumKey(appId, String.valueOf(userId),os),
                userId->{
                    BatchQueryResponse response = new BatchQueryResponse();
                    response.setQueryKey(buildEcpmSumKey(appId, String.valueOf(userId),os));
                    response.setUserId(userId);
                    return response;
                },(u1,u2) -> u1));
        Map<String,BatchQueryResponse> queryResponseMap = this.queryResponseMap(requestParam, TABLE_NAME);
        queryResponseMap.forEach((key,value) ->{
            responseMap.put(value.getUserId(),new BatchQueryResponse(){{
                setUserId(value.getUserId());
                setEcpmSum(value.getEcpmSum());
                setVideoExposureCount(value.getVideoExposureCount());
            }});
        });
        return responseMap;
    }


    private static final String ECPM_KEY ="ecpm:sum:%s:%s:%s";
    private static final String QUERY_ID = "userId";

    private String buildEcpmSumKey(Integer appId,String userId,String os){
        return HBaseUtils.hashRowKeyByLastCharacter(String.format(ECPM_KEY,appId,os,userId));
    }
}
