package com.coohua.user.event.biz.util;

import javax.crypto.Cipher;
import java.io.ByteArrayOutputStream;
import java.security.*;
import java.security.interfaces.RSAPrivateKey;
import java.security.interfaces.RSAPublicKey;
import java.security.spec.PKCS8EncodedKeySpec;
import java.security.spec.X509EncodedKeySpec;
import java.util.HashMap;
import java.util.Map;

/**
 * <p>
 * RSA公钥/私钥/签名工具包
 * </p>
 * <p>
 * 罗纳德·李维斯特（Ron [R]ivest）、阿迪·萨莫尔（Adi [S]hamir）和伦纳德·阿德曼（Leonard [A]dleman）
 * </p>
 * <p>
 * 字符串格式的密钥在未在特殊说明情况下都为BASE64编码格式<br/>
 * 由于非对称加密速度极其缓慢，一般文件不使用它来加密而是使用对称加密，<br/>
 * 非对称加密算法可以用来对对称加密的密钥加密，这样保证密钥的安全也就保证了数据的安全
 * </p>
 *
 * <AUTHOR> http://blog.csdn.net/centralperk/article/details/8538697
 * @date 2012-4-26
 * @version 1.0
 */
public class RSAUtils {
    public static String DEFAULT_RSA_PUBLIC_KEY_BASE64 = "MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQCFnqsHV+s0xKA6LeAvM2Q1fesA+4gUiuMk4KseWSSrdiL19Y495wSwcRaOVA6LcXh8fmYxwzVrN7hfN6zvYfstt/mi1rVs6eGNu+6Yk5ezWZra2QLxpmMHFcovjKzrFOwlUv7SwbkT7/nnS96cF/1Wq8bYCBwD/IyiCb0KfRdoqwIDAQAB";

    public static String DEFAULT_RSA_PRIVATE_KEY_BASE64 = "MIICeAIBADANBgkqhkiG9w0BAQEFAASCAmIwggJeAgEAAoGBAIWeqwdX6zTEoDot4C8zZDV96wD7iBSK4yTgqx5ZJKt2IvX1jj3nBLBxFo5UDotxeHx+ZjHDNWs3uF83rO9h+y23+aLWtWzp4Y277piTl7NZmtrZAvGmYwcVyi+MrOsU7CVS/tLBuRPv+edL3pwX/VarxtgIHAP8jKIJvQp9F2irAgMBAAECgYEAgktgdv6yang5zcGiVCSG2Op6WDDuVym/qNwzwJooedHu8WyLUa2OEweTeSJaNHtng+41dNznoC9rZ94AkkU3CcqaIzvy3ru2C+yp+mliLyDBAhEuvYMpUA1npnndi5gx+Sto9YBfhn1QPIHv75nOa3aOTf8grZ8+7d9+BN/2WHECQQDDTLJ900Je6393m+UxN/q99Y3SipvMbSBhdUopOCkxIJeaLulNkk8YQOcAvmbooxuxa4AVrpX9ccnmFxJB0ZhvAkEAryZTZ4M1PoSL5YUrqNO2pC+KZxSqi1BRL5oUAL42ICZF/K3oBbSCeEaLtqp+IhW7kigdNPyMg301Uoqn9CW5hQJBALd0Qm7DliMH4LMAdCjpxtXN5i0SGwvKrpId1U4m/TqyvPYOfwVpHHdR8CzYrl51aTweGTFE0IxE6T0ECxhcxG0CQQCR+qF9HrBb4OEwdOPEnGAXS8BQ1bqmzlQ8FnEhhWdbOEsQpGlDiAVN9Y4wurzR78cbs+9T4EHEvbCZkRMWu/J5AkB54yW7+/qnIL1DcHnNps+ZwIiBJHL4dApFVvlEHybslPHb0x9VHy6n/Kj6Dk+5dWN/nQPDzeGuLYGkQAMHQIuZ";

    /**
     * 加密算法RSA
     */
    public static final String KEY_ALGORITHM = "RSA";

    /**
     * 签名算法
     */
    public static final String SIGNATURE_ALGORITHM = "MD5withRSA";

    /**
     * 获取公钥的key
     */
    private static final String PUBLIC_KEY = "RSAPublicKey";

    /**
     * 获取私钥的key
     */
    private static final String PRIVATE_KEY = "RSAPrivateKey";

    /**
     * RSA最大加密明文大小
     */
    private static final int MAX_ENCRYPT_BLOCK = 117;

    /**
     * RSA最大解密密文大小
     */
    private static final int MAX_DECRYPT_BLOCK = 128;

    /**
     * <p>
     * 生成密钥对(公钥和私钥)
     * </p>
     *
     * @return
     * @throws Exception
     */
    public static Map<String, Object> genKeyPair() throws Exception {
        KeyPairGenerator keyPairGen = KeyPairGenerator.getInstance(KEY_ALGORITHM);
        keyPairGen.initialize(1024);
        KeyPair keyPair = keyPairGen.generateKeyPair();
        RSAPublicKey publicKey = (RSAPublicKey) keyPair.getPublic();
        RSAPrivateKey privateKey = (RSAPrivateKey) keyPair.getPrivate();
        Map<String, Object> keyMap = new HashMap<String, Object>(2);
        keyMap.put(PUBLIC_KEY, publicKey);
        keyMap.put(PRIVATE_KEY, privateKey);

        return keyMap;
    }

    /**
     * <p>
     * 用私钥对信息生成数字签名
     * </p>
     *
     * @param data 已加密数据
     * @param privateKey 私钥(BASE64编码)
     *
     * @return
     * @throws Exception
     */
    public static String sign(byte[] data, String privateKey) throws Exception {
        byte[] keyBytes = Base64Util.base64ToByteArray(privateKey);
        PKCS8EncodedKeySpec pkcs8KeySpec = new PKCS8EncodedKeySpec(keyBytes);
        KeyFactory keyFactory = KeyFactory.getInstance(KEY_ALGORITHM);
        PrivateKey privateK = keyFactory.generatePrivate(pkcs8KeySpec);
        Signature signature = Signature.getInstance(SIGNATURE_ALGORITHM);
        signature.initSign(privateK);
        signature.update(data);
        return Base64Util.byteArrayToBase64(signature.sign());
    }

    /**
     * <p>
     * 校验数字签名
     * </p>
     *
     * @param data 已加密数据
     * @param publicKey 公钥(BASE64编码)
     * @param sign 数字签名
     *
     * @return
     * @throws Exception
     *
     */
    public static boolean verify(byte[] data, String publicKey, String sign)
            throws Exception {
        byte[] keyBytes = Base64Util.base64ToByteArray(publicKey);
        X509EncodedKeySpec keySpec = new X509EncodedKeySpec(keyBytes);
        KeyFactory keyFactory = KeyFactory.getInstance(KEY_ALGORITHM);
        PublicKey publicK = keyFactory.generatePublic(keySpec);
        Signature signature = Signature.getInstance(SIGNATURE_ALGORITHM);
        signature.initVerify(publicK);
        signature.update(data);
        return signature.verify(Base64Util.base64ToByteArray(sign));
    }

    /**
     * <P>
     * 私钥解密
     * </p>
     *
     * @param encryptedData 已加密数据
     * @param privateKey 私钥(BASE64编码)
     * @return
     * @throws Exception
     */
    public static byte[] decryptByPrivateKey(byte[] encryptedData, String privateKey)
            throws Exception {
        byte[] keyBytes = Base64Util.base64ToByteArray(privateKey);
        PKCS8EncodedKeySpec pkcs8KeySpec = new PKCS8EncodedKeySpec(keyBytes);
        KeyFactory keyFactory = KeyFactory.getInstance(KEY_ALGORITHM);
        Key privateK = keyFactory.generatePrivate(pkcs8KeySpec);
        Cipher cipher = Cipher.getInstance(keyFactory.getAlgorithm());
        cipher.init(Cipher.DECRYPT_MODE, privateK);
        int inputLen = encryptedData.length;
        ByteArrayOutputStream out = new ByteArrayOutputStream();
        int offSet = 0;
        byte[] cache;
        int i = 0;
        // 对数据分段解密
        while (inputLen - offSet > 0) {
            if (inputLen - offSet > MAX_DECRYPT_BLOCK) {
                cache = cipher.doFinal(encryptedData, offSet, MAX_DECRYPT_BLOCK);
            } else {
                cache = cipher.doFinal(encryptedData, offSet, inputLen - offSet);
            }
            out.write(cache, 0, cache.length);
            i++;
            offSet = i * MAX_DECRYPT_BLOCK;
        }
        byte[] decryptedData = out.toByteArray();
        out.close();
        return decryptedData;
    }

    /**
     * <p>
     * 公钥解密
     * </p>
     *
     * @param encryptedData 已加密数据
     * @param publicKey 公钥(BASE64编码)
     * @return
     * @throws Exception
     */
    public static byte[] decryptByPublicKey(byte[] encryptedData, String publicKey)
            throws Exception {
        byte[] keyBytes = Base64Util.base64ToByteArray(publicKey);
        X509EncodedKeySpec x509KeySpec = new X509EncodedKeySpec(keyBytes);
        KeyFactory keyFactory = KeyFactory.getInstance(KEY_ALGORITHM);
        Key publicK = keyFactory.generatePublic(x509KeySpec);
        Cipher cipher = Cipher.getInstance(keyFactory.getAlgorithm());
        cipher.init(Cipher.DECRYPT_MODE, publicK);
        int inputLen = encryptedData.length;
        ByteArrayOutputStream out = new ByteArrayOutputStream();
        int offSet = 0;
        byte[] cache;
        int i = 0;
        // 对数据分段解密
        while (inputLen - offSet > 0) {
            if (inputLen - offSet > MAX_DECRYPT_BLOCK) {
                cache = cipher.doFinal(encryptedData, offSet, MAX_DECRYPT_BLOCK);
            } else {
                cache = cipher.doFinal(encryptedData, offSet, inputLen - offSet);
            }
            out.write(cache, 0, cache.length);
            i++;
            offSet = i * MAX_DECRYPT_BLOCK;
        }
        byte[] decryptedData = out.toByteArray();
        out.close();
        return decryptedData;
    }

    /**
     * <p>
     * 公钥加密
     * </p>
     *
     * @param data 源数据
     * @param publicKey 公钥(BASE64编码)
     * @return
     * @throws Exception
     */
    public static byte[] encryptByPublicKey(byte[] data, String publicKey)
            throws Exception {
        byte[] keyBytes = Base64Util.base64ToByteArray(publicKey);
        X509EncodedKeySpec x509KeySpec = new X509EncodedKeySpec(keyBytes);
        KeyFactory keyFactory = KeyFactory.getInstance(KEY_ALGORITHM);
        Key publicK = keyFactory.generatePublic(x509KeySpec);
        // 对数据加密
        Cipher cipher = Cipher.getInstance(keyFactory.getAlgorithm());
        cipher.init(Cipher.ENCRYPT_MODE, publicK);
        int inputLen = data.length;
        ByteArrayOutputStream out = new ByteArrayOutputStream();
        int offSet = 0;
        byte[] cache;
        int i = 0;
        // 对数据分段加密
        while (inputLen - offSet > 0) {
            if (inputLen - offSet > MAX_ENCRYPT_BLOCK) {
                cache = cipher.doFinal(data, offSet, MAX_ENCRYPT_BLOCK);
            } else {
                cache = cipher.doFinal(data, offSet, inputLen - offSet);
            }
            out.write(cache, 0, cache.length);
            i++;
            offSet = i * MAX_ENCRYPT_BLOCK;
        }
        byte[] encryptedData = out.toByteArray();
        out.close();
        return encryptedData;
    }

    static ThreadLocal<Cipher> threadLocal;
    static Key privateK;
    static {
        try {
            String PRIVATE_KEY = "MIICdgIBADANBgkqhkiG9w0BAQEFAASCAmAwggJcAgEAAoGBAJ6lEe2UwnoluOp5JhpBO0GLXB9tfnT73VJZzsmcWEBCg4EYyFiL7MbnXChwGcjsOwEKCTa6feKsQ7effzOBg0vQFtGDeWMuri7KfDW+RFNGJimFUjlP8smT/GHGF4Na0t6PeMdjnPrgoYXWxNab4BKJcnH6otmwVePpZI7umwRXAgMBAAECgYB0OOIld3HyImiR/icX3OoYXowiygh2k33Uss53jP5qNVA553n+xKO2JBCz30xEkiu32y5c1csboLALeeGqThts6Ib1FtgW0xE2jtWi7l2ryBbLZRIqXp6cDrwx/FaWBwLh9Okijc8q3OKF5e6gf6qN8bhpmAoiqQyUZzh+sOqn4QJBAOmgSoXt3ahC6NAEiXUQK5uCmuLLnKCHaoSl1u9FrURX/wqVlGATsCpbOUHlj1iIJ6Nz6rGReJKLx9b3eEYAc4UCQQCt1n6kPQaQYQb5p+3Iw2dXs68TUkE6AOi0I19kIs5byoct+xFy/LyabyYVAg0nQm6DFXA6OZxkJ5UhepmrQzkrAkEAwSTwl3rIhsQSP2+QyIP+2UkHjbpxmdF6QYHtiNEc5uStCG4TRb7hJJNYad9EPBgMVTiO7hBrExSWuWTMPWtAFQJAUbHe3DJttkZS79rSbaofE+E2ot7iFm887QM+niGZVxvrwVkfTymlPPKFNBQ9uDiJFQNrNYHDlHHhrNUgCJNT9wJAO7fgkSZbkr2uoWI4LpBxIgw7iKJc2SgxhTkDpSYPN1sCBhnFJEHV1z06qAUHy0leyvawxZMJHVL2AAVf00DX1Q==";
            byte[] keyBytes = Base64Util.base64ToByteArray(PRIVATE_KEY);
            PKCS8EncodedKeySpec pkcs8KeySpec = new PKCS8EncodedKeySpec(keyBytes);
            KeyFactory keyFactory = KeyFactory.getInstance(KEY_ALGORITHM);
            privateK = keyFactory.generatePrivate(pkcs8KeySpec);
            threadLocal = ThreadLocal.withInitial(() ->{
                try {
                    Cipher cipher = Cipher.getInstance(keyFactory.getAlgorithm());
                    cipher.init(Cipher.ENCRYPT_MODE, privateK);
                    return cipher;
                }catch (Exception e){
                    return null;
                }
            });
        }catch (Exception e){
            System.err.println("Init Error");
        }

    }
    /**
     * <p>
     * 私钥加密
     * </p>
     *
     * @param data 源数据
     * @return
     * @throws Exception
     */
    public static byte[] encryptByPrivateKey(byte[] data)
            throws Exception {
        Cipher cipher = threadLocal.get();
        int inputLen = data.length;
        ByteArrayOutputStream out = new ByteArrayOutputStream();
        int offSet = 0;
        byte[] cache;
        int i = 0;
        // 对数据分段加密
        while (inputLen - offSet > 0) {
            if (inputLen - offSet > MAX_ENCRYPT_BLOCK) {
                cache = cipher.doFinal(data, offSet, MAX_ENCRYPT_BLOCK);
            } else {
                cache = cipher.doFinal(data, offSet, inputLen - offSet);
            }
            out.write(cache, 0, cache.length);
            i++;
            offSet = i * MAX_ENCRYPT_BLOCK;
        }
        byte[] encryptedData = out.toByteArray();
        out.close();
        return encryptedData;
    }

    /**
     * <p>
     * 获取私钥
     * </p>
     *
     * @param keyMap 密钥对
     * @return
     * @throws Exception
     */
    public static String getPrivateKey(Map<String, Object> keyMap)
            throws Exception {
        Key key = (Key) keyMap.get(PRIVATE_KEY);
        return Base64Util.byteArrayToBase64(key.getEncoded());
    }

    /**
     * <p>
     * 获取公钥
     * </p>
     *
     * @param keyMap 密钥对
     * @return
     * @throws Exception
     */
    public static String getPublicKey(Map<String, Object> keyMap)
            throws Exception {
        Key key = (Key) keyMap.get(PUBLIC_KEY);
        return Base64Util.byteArrayToBase64(key.getEncoded());
    }

    //=================  test ==========================
    static String publicKey;
    static String privateKey;

    static {
        try {
            Map<String, Object> keyMap = RSAUtils.genKeyPair();
            publicKey = RSAUtils.getPublicKey(keyMap);
            privateKey = RSAUtils.getPrivateKey(keyMap);
//            System.err.println("公钥: \n\r" + publicKey);
//            System.err.println("私钥： \n\r" + privateKey);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    public static void main(String[] args) throws Exception {
//        test();
//        testSign();
        Map<String, Object> pair = genKeyPair();
        String pkey = RSAUtils.getPublicKey(pair);
        String priKey = RSAUtils.getPrivateKey(pair);
        System.err.println("公钥: \n\r" + pkey);
        System.err.println("私钥： \n\r" + priKey);
    }

    public static String encryptByPrivateKeyToBase64(String msg) throws Exception {
        return Base64Util.byteArrayToBase64(encryptByPrivateKey(msg.getBytes()));
    }

    public static String decryptByPublicKeyFormBase64(String meg, String publicKey) throws Exception{
        return new String(decryptByPublicKey(Base64Util.base64ToByteArray(meg), publicKey));
    }
}