package com.coohua.user.event.biz.core.dto.rsp;

import lombok.Data;

/**
 * <AUTHOR>
 * @since 2021/5/26
 */
@Data
public class ValidResponse {
    private Boolean isValid = Boolean.FALSE;
    private String sign;

    public ValidResponse() {
    }

    public ValidResponse(String sign) {
        this.sign = sign;
    }

    public ValidResponse(Boolean isValid, String sign) {
        this.isValid = isValid;
        this.sign = sign;
    }

    public ValidResponse(Boolean isValid) {
        this.isValid = isValid;
    }
}
