package com.coohua.user.event.biz.dc.entity;

import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 * @since 2020/9/19
 */
@Data
public class CoreEventResultEntity {
    /**
     * logday
     */
    private Date logday;

    /**
     * product_group
     */
    private String productGroup;

    /**
     * product_en
     */
    private String productEn;

    /**
     * product_cn
     */
    private String productCn;

    /**
     * os
     */
    private String os;

    /**
     * dsp
     */
    private String dsp;

    /**
     * account_id
     */
    private String accountId;

    /**
     * cid
     */
    private String cid;

    /**
     * gid
     */
    private String gid;

    /**
     * pid
     */
    private String pid;

    /**
     * count_active
     */
    private Integer countActive;

    /**
     * event_type1
     */
    private Integer eventType1;

    /**
     * event_type2
     */
    private Integer eventType2;

    /**
     * event_type3
     */
    private Integer eventType3;

    /**
     * event_type4
     */
    private Integer eventType4;

    /**
     * event_type5
     */
    private Integer eventType5;
}
