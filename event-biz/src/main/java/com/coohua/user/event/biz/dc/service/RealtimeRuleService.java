package com.coohua.user.event.biz.dc.service;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.coohua.user.event.biz.click.entity.ProductEntity;
import com.coohua.user.event.biz.dc.dto.ActionRuleDto;
import com.coohua.user.event.biz.dc.dto.Pages;
import com.coohua.user.event.biz.dc.dto.RealtimeRuleRequest;
import com.coohua.user.event.biz.dc.dto.RealtimeRuleResponse;
import com.coohua.user.event.biz.dc.entity.RealtimeRule;
import com.coohua.user.event.biz.dc.mapper.RealtimeRuleMapper;
import com.coohua.user.event.biz.enums.ActionDoType;
import com.coohua.user.event.biz.enums.ActionType;
import com.coohua.user.event.biz.util.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2022/1/26
 */
@Slf4j
@Service
public class RealtimeRuleService {

    @Resource
    private RealtimeRuleMapper realtimeRuleMapper;

    private List<RealtimeRule> allGrayRules;

    public Map<Integer, RealtimeRule> realtimeGrayRuleMap;
    public Map<Integer, RealtimeRule> realtimeEcpmMap;

    @PostConstruct
    @Scheduled(cron = "0 5/10 * * * ?")
    public void refreshMap() {
        this.allGrayRules = realtimeRuleMapper.queryAllConfig();
        this.realtimeGrayRuleMap = allGrayRules
                .stream()
                .filter(r-> ActionType.GRAY_BLACK.type.equals(r.getActionType()))
                .collect(Collectors.toMap(RealtimeRule::getId, k->k));
        this.realtimeEcpmMap = allGrayRules
                .stream()
                .filter(r -> ActionType.LIMIT_ECPM.type.equals(r.getActionType()))
                .collect(Collectors.toMap(RealtimeRule::getId, k->k));
    }

    public List<RealtimeRule> queryBlackRealTimeRules(){
        return allGrayRules
                .stream()
                .filter(r -> ActionDoType.RULE_CHECK.getType().equals(r.getActionDto().getType()))
                .collect(Collectors.toList());
    }

    public List<ActionRuleDto> queryEcpmConfig(){
        return allGrayRules
                .stream()
                .map(RealtimeRule::getActionDto)
                .filter(r -> ActionDoType.ECPM_CHECK.getType().equals(r.getType()))
                .collect(Collectors.toList());
    }

    private void checkParam(RealtimeRuleRequest realtimeRuleRequest){
        if (ActionDoType.RULE_CHECK.getType().equals(realtimeRuleRequest.getType())){
            if (Strings.isEmpty(realtimeRuleRequest.getBrands())
                    && Strings.isEmpty(realtimeRuleRequest.getChannels())
                    && Strings.isEmpty(realtimeRuleRequest.getIps())
                    && Strings.isEmpty(realtimeRuleRequest.getSources())
                    && Strings.isEmpty(realtimeRuleRequest.getModels())
            ){
                throw new RuntimeException("至少填写一个筛选条件");
            }
        }
    }

    public void insert(RealtimeRuleRequest realtimeRuleRequest){
        checkParam(realtimeRuleRequest);
        RealtimeRule realtimeRule = convertToRealtime(realtimeRuleRequest,true);
        realtimeRuleMapper.insert(realtimeRule);
    }

    private RealtimeRule convertToRealtime(RealtimeRuleRequest realtimeRuleRequest,Boolean insert){
        RealtimeRule realtimeRule = new RealtimeRule();
        Date now = new Date();
        if (insert) {
            realtimeRule.setId(realtimeRuleRequest.getId());
            realtimeRule.setCreateTime(now);
        }
        ProductEntity productEntity = AppConfig.appIdMap.getOrDefault(realtimeRuleRequest.getAppId(),new ProductEntity());
        realtimeRule.setProduct(productEntity.getProduct());
        realtimeRule.setState(realtimeRuleRequest.getState());
        realtimeRule.setOcpcType(realtimeRuleRequest.getOcpcType());
        realtimeRule.setChannels(realtimeRuleRequest.getChannels());
        realtimeRule.setModels(realtimeRuleRequest.getModels());
        realtimeRule.setIps(realtimeRuleRequest.getIps());
        ActionRuleDto actionRuleDto = realtimeRule.getActionDto();
        actionRuleDto.setAppId(productEntity.getId());
        actionRuleDto.setMaxEcpm(realtimeRuleRequest.getMaxEcpm());
        actionRuleDto.setMinEcpm(realtimeRuleRequest.getMinEcpm());
        actionRuleDto.setProduct(productEntity.getProduct());
        actionRuleDto.setRewardLimit(realtimeRuleRequest.getRewardLimit());
        actionRuleDto.setType(realtimeRuleRequest.getType());
        actionRuleDto.setVideoLimit(realtimeRuleRequest.getVideoLimit());
        actionRuleDto.setVideoTimes(realtimeRuleRequest.getVideoTimes());
        actionRuleDto.setWithdrawLimit(realtimeRuleRequest.getWithdrawLimit());
        if (Lists.isEmpty(realtimeRuleRequest.getPlatformLimit())){
            realtimeRuleRequest.setPlatformLimit(Collections.singletonList(0));
        }
        actionRuleDto.setPlatformLimit(realtimeRuleRequest.getPlatformLimit());
        if (Lists.isEmpty(realtimeRuleRequest.getPlatformSkipList())){
            realtimeRuleRequest.setPlatformSkipList(new ArrayList<>());
        }
        actionRuleDto.setPlatformLimitSkip(realtimeRuleRequest.getPlatformSkipList().stream()
                .map(AdTypeConvert::convertToInt).collect(Collectors.toList()));
        realtimeRule.setActionRule(JSON.toJSONString(actionRuleDto));
        realtimeRule.setActionType(realtimeRuleRequest.getActionType());
        realtimeRule.setAppId(productEntity.getId());
        realtimeRule.setProductName(productEntity.getProductName());
        realtimeRule.setRuleName(realtimeRuleRequest.getRuleName());
        realtimeRule.setLevel(realtimeRuleRequest.getLevel());
        realtimeRule.setBrands(realtimeRuleRequest.getBrands());
        realtimeRule.setSources(realtimeRuleRequest.getSources());
        realtimeRule.setNewUserType(realtimeRuleRequest.getNewUserType());
        realtimeRule.setUpdateTime(now);
        if (productEntity.getId() == null){
            realtimeRule.setState(0);
        }

        return realtimeRule;
    }




    private RealtimeRuleResponse convertToRealtime(RealtimeRule realtimeRule){
        RealtimeRuleResponse realtimeRuleResponse = new RealtimeRuleResponse();
        realtimeRuleResponse.setId(realtimeRule.getId());
        realtimeRuleResponse.setProduct(realtimeRule.getProduct());
        realtimeRuleResponse.setState(realtimeRule.getState());
        realtimeRuleResponse.setOcpcType(realtimeRule.getOcpcType());
        realtimeRuleResponse.setChannels(realtimeRule.getChannels());
        realtimeRuleResponse.setModels(realtimeRule.getModels());
        realtimeRuleResponse.setIps(realtimeRule.getIps());
        realtimeRuleResponse.setCreateTime(DateUtil.dateToStringWithTime(realtimeRule.getCreateTime()));
        realtimeRuleResponse.setUpdateTime(DateUtil.dateToStringWithTime(realtimeRule.getUpdateTime()));
        realtimeRuleResponse.setRuleName(realtimeRule.getRuleName());
        realtimeRuleResponse.setAppId(realtimeRule.getAppId());
        realtimeRuleResponse.setProductName(realtimeRule.getProductName());
        realtimeRuleResponse.setBrands(realtimeRule.getBrands());
        realtimeRuleResponse.setSources(realtimeRule.getSources());
        realtimeRuleResponse.setLevel(realtimeRule.getLevel());
        realtimeRuleResponse.setNewUserType(realtimeRule.getNewUserType());
        realtimeRuleResponse.setActionType(realtimeRule.getActionType());

        ActionRuleDto dto = realtimeRule.getActionDto();
        realtimeRuleResponse.setType(dto.getType());
        realtimeRuleResponse.setMinEcpm(dto.getMinEcpm());
        realtimeRuleResponse.setMaxEcpm(dto.getMaxEcpm());
        realtimeRuleResponse.setVideoTimes(dto.getVideoTimes());
        realtimeRuleResponse.setVideoLimit(dto.getVideoLimit() == null ? 2800:dto.getVideoLimit());
        realtimeRuleResponse.setWithdrawLimit(dto.getWithdrawLimit() == null ? 100:dto.getWithdrawLimit());
        realtimeRuleResponse.setRewardLimit(dto.getRewardLimit() == null ? 100:dto.getRewardLimit());
        if (Lists.isEmpty(dto.getPlatformLimit())){
            dto.setPlatformLimit(Collections.singletonList(0));
        }
        realtimeRuleResponse.setPlatformLimit(dto.getPlatformLimit());
        if (Lists.isEmpty(dto.getPlatformLimitSkip())){
            dto.setPlatformLimitSkip(new ArrayList<>());
        }
        realtimeRuleResponse.setPlatformSkipList(dto.getPlatformLimitSkip().stream()
                .map(AdTypeConvert::convertToStr).collect(Collectors.toList()));
        return realtimeRuleResponse;
    }

    public void update(RealtimeRuleRequest realtimeRuleRequest){
        checkParam(realtimeRuleRequest);
        RealtimeRule realtimeRuleRecord = realtimeRuleMapper.queryById(realtimeRuleRequest.getId());
        Optional.ofNullable(realtimeRuleRecord).orElseThrow(() -> new RuntimeException("未找到的记录"));

        RealtimeRule realtimeRule = convertToRealtime(realtimeRuleRequest,false);
        realtimeRule.setId(realtimeRuleRecord.getId());
        realtimeRule.setCreateTime(realtimeRuleRecord.getCreateTime());
        realtimeRuleMapper.update(realtimeRule);
    }

    public void switchFlag(Integer id,Integer state){
        int ret = realtimeRuleMapper.switchFlag(id,state);
    }

    public Pages<RealtimeRuleResponse> queryList(String product, Pages<RealtimeRuleResponse> page){
        if (Strings.isEmpty(product)){
            product = null;
        }
        int from = (page.getPageNo() - 1) * page.getPageSize();
        List<RealtimeRule> realtimeRules = realtimeRuleMapper.queryList(product,from,page.getPageSize());

        page.setItems(realtimeRules.stream().map(this::convertToRealtime).collect(Collectors.toList()));
        page.setCount(realtimeRuleMapper.queryCount(product));
        return page;
    }
}
