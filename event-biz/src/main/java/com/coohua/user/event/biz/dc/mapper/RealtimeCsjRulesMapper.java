package com.coohua.user.event.biz.dc.mapper;

import com.coohua.user.event.biz.dc.entity.RealtimeCsjRules;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;

import java.util.List;

/**
 * <p>
 *  Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2022-10-11
 */
public interface RealtimeCsjRulesMapper{

    @Update({"update `user-event`.realtime_csj_rules set " +
            " name = #{entity.name}," +
            " csj_rate = #{entity.csj_rate}," +
            " uv = #{entity.uv}," +
            " pv = #{entity.pv}," +
            " avg_ecpm = #{entity.avgEcpm}," +
            " action_task = #{entity.actionTask}," +
            " channel_start = #{entity.channelStart}," +
            " include_type = #{entity.includeType}," +
            " model_type = #{entity.modelType}," +
            " update_time = #{entity.updateTime}" +
            " where id #{entity.id}"})
    Integer updateRules(@Param("entity") RealtimeCsjRules realtimeCsjRules);

    @Select("select * from `user-event`.realtime_csj_rules")
    List<RealtimeCsjRules> queryList();
}
