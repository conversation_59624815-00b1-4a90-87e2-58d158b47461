package com.coohua.user.event.biz.service.bean;

import lombok.Data;

/**
 * <AUTHOR>
 * @since 2022/6/16
 */
@Data
public class UserEventCountHBaseBean {
    private String queryKey;
    private String product;
    private Long userId;
//    private String os;
    private Long eventCount;
    private Long adDataCount;
    private Long appUseCount;
    private Long appClickCount;
    private Long appStatusCount;
    private Long startUpCount;


    public static UserEventCountHBaseBean buildDefault(String product,Long userId){
        return new UserEventCountHBaseBean(){{
            Long defaultValue = 0L;
            setProduct(product);
            setUserId(userId);
            setEventCount(defaultValue);
            setAdDataCount(defaultValue);
            setAppClickCount(defaultValue);
            setAppStatusCount(defaultValue);
            setStartUpCount(defaultValue);
            setAppUseCount(defaultValue);
        }};
    }
}
