package com.coohua.user.event.biz.dc.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 异常用户激活回传拦截
 *
 * @TableName user_call_back_intercept
 */
@TableName(value = "user_call_back_intercept")
@Data
public class UserCallBackIntercept implements Serializable {
    /**
     *
     */
    @TableId(type = IdType.AUTO)
    private Long id;
    private String userId;
    private String os;

    @TableField(exist = false)
    private String productName;
    /**
     *
     */
    private String product;

    /**
     * 渠道
     */
    private String channel;

    /**
     * 来源
     */
    private String source;

    /**
     * 账户id
     */
    private String accountId;

    /**
     *
     */
    private String remark;

    /**
     *
     */
    private LocalDateTime createTime;

    /**
     *
     */
    private LocalDateTime updateTime;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;
}