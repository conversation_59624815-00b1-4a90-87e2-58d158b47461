package com.coohua.user.event.biz.ap.service;

import com.coohua.user.event.biz.ap.entity.AdInfoEntity;
import com.coohua.user.event.biz.ap.mapper.AdInfoMapper;
import com.coohua.user.event.biz.ap.mapper.ConfigMapper;
import com.coohua.user.event.biz.enums.AdPosType;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2021/8/2
 */
@Slf4j
@Service
public class BpApService {

    private static Map<Integer,Integer> adIdMap;

    private static String configStr;

    @Resource
    private AdInfoMapper adInfoMapper;
    @Resource
    private ConfigMapper configMapper;

    @PostConstruct
    @Scheduled(cron = "30 0/5 * * * ? ")
    public void refreshAdIdMap(){
        adIdMap = adInfoMapper.selectAllAdInfo().stream()
                .collect(Collectors.toMap(AdInfoEntity::getId,AdInfoEntity::getType,(r1,r2) -> r1));
        configStr = configMapper.selectKafKaConfig();
    }

    public boolean isSendToKafka(Integer appId){
        return configStr.contains(appId.toString());
    }

    public Integer getAdType(Integer adId){
        return basicAdType(adIdMap.getOrDefault(adId,0));
    }

    private Integer basicAdType(Integer adType){
        String adTypeStr = adType.toString();

        if (adTypeStr.startsWith("1015")
                || adTypeStr.startsWith("1008")
                || adTypeStr.startsWith("1018")){
            return AdPosType.Rewarded_video.getCode();
        }else if (adTypeStr.startsWith("1061")
                || adTypeStr.startsWith("1062")
                || adTypeStr.startsWith("1073")
                || adTypeStr.startsWith("1081")){
            return AdPosType.Table_plaque.getCode();
        }
        return 0;
    }

}
