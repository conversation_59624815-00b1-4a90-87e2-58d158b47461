package com.coohua.user.event.biz.core.service;

import com.alibaba.fastjson.JSON;
import com.coohua.bp.user.remote.dto.UserDTO;
import com.coohua.user.event.biz.core.dto.req.UploadEventReq;
import com.coohua.user.event.biz.core.entity.UserEvent;
import com.coohua.user.event.biz.core.entity.UserStatType;
import com.coohua.user.event.biz.core.mapper.UserEventMapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.coohua.user.event.biz.util.DateUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;

/**
* <p>
    *  服务实现类
    * </p>
*
* <AUTHOR>
* @since 2020-09-02
*/
@Slf4j
@Service
public class UserEventService extends ServiceImpl<UserEventMapper, UserEvent> {

    public void solveUserEvent(UserDTO userDTO, UploadEventReq req){
        Integer appId = Integer.valueOf(req.getAppId());
        Date now = new Date();

        if (userDTO != null) {
            Date userCreateTime = new Date(userDTO.getCreateTime());
            int dayBetween = DateUtil.daysBetween(userCreateTime, now);
            if (dayBetween == 1) {
                saveStat(appId, req.getChannel(), req.getIntOs(), userDTO.getId(), UserStatType.dayOpen2, req);
            } else if (dayBetween == 2) {
                saveStat(appId, req.getChannel(), req.getIntOs(), userDTO.getId(), UserStatType.dayOpen3, req);
            } else if (dayBetween == 6) {
                saveStat(appId, req.getChannel(), req.getIntOs(), userDTO.getId(), UserStatType.dayOpen7, req);
            }
        }else {
            saveStat(appId, req.getChannel(), req.getIntOs(), req.getUserId(), UserStatType.register, req);
        }

        UserEvent userEvent = recentDay(appId,req.getUserId());
        if (userEvent != null){
            if (!userEvent.getDateStr().equals(DateUtil.dateToString(now))){
                int resetDayBetween = DateUtil.daysBetween(userEvent.getCreateTime(), now);
                if (resetDayBetween == 1) {
                    saveStat(appId, req.getChannel(), req.getIntOs(), req.getUserId(), UserStatType.activeDayOpen2, req);
                }
            }
        }else {
            saveStat(appId, req.getChannel(), req.getIntOs(), req.getUserId(), UserStatType.active, req);
        }
    }


    private UserEvent recentDay(Integer appId,Long userId){
        List<UserEvent> userEventList = lambdaQuery()
                .eq(UserEvent::getAppId,appId)
                .eq(UserEvent::getUserId,userId)
                .orderByDesc(UserEvent::getCreateTime)
                .last(" limit 1").list();
        if (userEventList == null || userEventList.size() == 0){
            return null;
        }

        return userEventList.get(0);
    }

    private void saveStat(Integer appId,String channel,Integer os, Long id, UserStatType userStatType,UploadEventReq req){
        Date now = new Date();
        String dateStr = DateUtil.dateToString(now);
        UserEvent userEvent = new UserEvent();
        userEvent.setAppId(appId);
        userEvent.setUserId(id);
        userEvent.setChannel(channel);
        userEvent.setDateStr(dateStr);
        userEvent.setIntOs(os);
        userEvent.setCreateTime(now);
        userEvent.setUpdateTime(now);
        userEvent.setUserStat(userStatType.name());
        userEvent.setDeviceId(req.getDeviceId());
        userEvent.setRequestJson(JSON.toJSONString(req));
        save(userEvent);
    }
}
