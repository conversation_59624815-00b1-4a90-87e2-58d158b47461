package com.coohua.user.event.biz.user.service;

import cn.hutool.core.thread.ThreadUtil;
import com.aliyun.openservices.shade.com.google.common.util.concurrent.RateLimiter;
import com.coohua.user.event.biz.enums.GrayType;
import com.coohua.user.event.biz.user.entity.UserEntity;
import com.coohua.user.event.biz.user.entity.UserMeta;
import com.coohua.user.event.biz.user.mapper.BpUserMapper;
import com.pepper.metrics.core.ThreadFactory;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.sql.Connection;
import java.sql.DriverManager;
import java.sql.PreparedStatement;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.*;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * <AUTHOR>
 * @since 2020/9/17
 */
@Slf4j
@Service
public class BpUserService {

    // bp-user连接信息
    private static final String JDBC_URL = "*****************************************************************************************************************************************************";
    private static final String USERNAME = "bp_user";
    private static final String PASSWORD = "M7rh2o7xxwtsy";


    @Resource
    private BpUserMapper bpUserMapper;

    public List<UserEntity> queryUserById(List<Long> userIdList){
        return bpUserMapper.queryUserInfoByIdBatch(userIdList);
    }

    public List<UserMeta> queryUserByIds(List<Long> userIdList,List<Long> pkgId){
        return bpUserMapper.queryUserInfoByIdBatchs(userIdList,pkgId);
    }

    public int doUnGaryUser(List<Long> userIdList) {
        return bpUserMapper.deleteUserGrayByUserIdList(userIdList);
    }

    public void unGrayUser() {
        ThreadPoolExecutor threadPoolExecutor = new ThreadPoolExecutor(Runtime.getRuntime().availableProcessors(), Runtime.getRuntime().availableProcessors() * 2, 100, TimeUnit.SECONDS,
                new ArrayBlockingQueue<>(50), new ThreadFactory("UserUnGary-"), new ThreadPoolExecutor.CallerRunsPolicy());
        AtomicInteger releaseSize = new AtomicInteger();

        List<Long> userIsList = bpUserMapper.queryUserIdList();

        List<Future> futures = new ArrayList<>();
        AtomicInteger processedBatches = new AtomicInteger();

        final int BATCH_SIZE = 1000;
        for (int i = 0; i < userIsList.size(); i += BATCH_SIZE) {
            MAX_CAN_SEND_UPDATE.acquire();
            releaseSize.getAndIncrement();

            int end = Math.min(i + BATCH_SIZE, userIsList.size());
            List<Long> batch = userIsList.subList(i, end);
            Future<?> submit = threadPoolExecutor.submit(() -> {
                try (Connection conn = DriverManager.getConnection(JDBC_URL, USERNAME, PASSWORD)) {
                    conn.setAutoCommit(false);
                    long start = System.currentTimeMillis();

                    int deleted = batchDeleteUsers(conn, batch);

                    conn.commit();
                    System.out.printf("批次 %d: 删除 %d 条, 用时 %dms%n",
                            processedBatches.incrementAndGet(), deleted,
                            System.currentTimeMillis() - start);
                } catch (SQLException e) {
                    System.err.println("处理批次失败: " + e.getMessage());
                }
            });
            futures.add(submit);

        }

        for (Future future : futures) {
            try {
                future.get();
            }  catch (Exception e) {
                System.out.println("本批执行失败");
            }
        }

        System.out.println("done doUnGaryUser");
    }

    private RateLimiter MAX_CAN_SEND_UPDATE = RateLimiter.create(30);

    private int batchDeleteUsers(Connection conn, List<Long> userIds) {
        if (userIds == null || userIds.isEmpty()) {
            return 0;
        }

        try (PreparedStatement pstmt = conn.prepareStatement(
                "delete from bp_user.user_gray where user_id = ?")) {

            // 批处理设置
            for (Long userId : userIds) {
                pstmt.setLong(1, userId);
                pstmt.addBatch();
            }

            // 执行批处理
            int[] results = pstmt.executeBatch();

            // 计算总删除行数
            int total = 0;
            for (int r : results) {
                if (r >= 0) total += r;
            }
            return total;
        } catch (SQLException e) {
            System.out.println("删除失败");
        }
        return 0;
    }

}
