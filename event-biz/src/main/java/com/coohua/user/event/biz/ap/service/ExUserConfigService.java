package com.coohua.user.event.biz.ap.service;

import com.coohua.user.event.biz.ap.entity.ExUserConfig;
import com.coohua.user.event.biz.ap.mapper.ExUserConfigMapper;
import com.coohua.user.event.biz.ap.vo.ExUserConfigVo;
import com.coohua.user.event.biz.util.Strings;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2021/11/1
 */
@Slf4j
@Service
public class ExUserConfigService {

    @Resource
    private ExUserConfigMapper exUserConfigMapper;

    public Map<Integer, List<ExUserConfigVo>> queryConfigMap(){
        List<ExUserConfig> exUserConfigs = exUserConfigMapper.queryAllActiveConfig();
        return exUserConfigs.stream().map(exUserConfig -> {
            ExUserConfigVo vo = new ExUserConfigVo();
            vo.setRuleId(exUserConfig.getId());
            vo.setProduct(exUserConfig.getProduct());
            vo.setRewardLimit(exUserConfig.getScoreRate());
            vo.setWithdrawLimit(exUserConfig.getWithdrawRate());
            vo.setVideoLimit(exUserConfig.getVideoLimit());
            vo.setChannelList(convertToList(exUserConfig.getChannel()));
            vo.setModelList(convertToList(exUserConfig.getModel()));
            vo.setPhoneList(convertToList(exUserConfig.getPhone()));
            vo.setIpList(convertToList(exUserConfig.getIp()));
            vo.setOcpcType(exUserConfig.getOcpcType());
            vo.setOcpcChannel(exUserConfig.getOcpcChannel() == null ? 3:exUserConfig.getOcpcChannel());
            vo.setRuleLevel(exUserConfig.getRuleLevel());
            return vo;
        }).collect(Collectors.groupingBy(ExUserConfigVo::getProduct));
    }

    private List<String> convertToList(String target){
        if (Strings.noEmpty(target)){
            target = target.trim();
            if (Strings.noEmpty(target)) {
                return Arrays.stream(target.split(",")).filter(Strings::noEmpty).collect(Collectors.toList());
            }
        }
        return new ArrayList<>();
    }
}
