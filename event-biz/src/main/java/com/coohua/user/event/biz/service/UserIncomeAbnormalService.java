package com.coohua.user.event.biz.service;

import com.alibaba.fastjson.JSONArray;
import com.coohua.user.event.biz.ck001.service.Ck001JobService;
import com.coohua.user.event.biz.click.entity.UserMacIpCheckEntity;
import com.coohua.user.event.biz.click.service.ClickHouseService;
import com.coohua.user.event.biz.dc.entity.UserCallBackIntercept;
import com.coohua.user.event.biz.dc.service.UserCallBackInterceptService;
import com.coohua.user.event.biz.util.DateUtil;
import com.coohua.user.event.biz.util.DingTalkPushUtils;
import com.coohua.user.event.biz.util.RedisKeyConstants;
import com.ctrip.framework.apollo.spring.annotation.ApolloJsonValue;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import redis.clients.jedis.Jedis;
import redis.clients.jedis.Pipeline;
import top.zrbcool.pepper.boot.jediscluster.JedisClusterClient;
import top.zrbcool.pepper.boot.jediscluster.JedisClusterClientRefer;

import javax.annotation.Resource;
import java.io.File;
import java.io.IOException;
import java.nio.file.Files;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;

@Service
@Slf4j
public class UserIncomeAbnormalService {
    @JedisClusterClientRefer(namespace = "user-event")
    private JedisClusterClient userEventJedisClusterClient;

    @ApolloJsonValue("${group10.gdt.check.ex.product:[]}")
    private List<String> group10GdtExProduct;

    @Autowired
    private ClickHouseService clickHouseService;
    @Resource
    private Ck001JobService ck001JobService;
    @Autowired
    private UserInfoQueryService userInfoQueryService;
    @Resource
    private UserCallBackInterceptService userCallBackInterceptService;
    @Resource
    private OssService ossService;



    public void refreshUserIncomeAbnormalIos() {
        List<UserMacIpCheckEntity> iosIncomeAbnormalUserList = clickHouseService.queryIosIncomeAbnormalUserInfo();

        if (CollectionUtils.isEmpty(iosIncomeAbnormalUserList)) return;

        for (UserMacIpCheckEntity userEntity : iosIncomeAbnormalUserList) {
            if (StringUtils.isBlank(userEntity.getUserIdStr())) {
                continue;
            }
            if (StringUtils.isBlank(userEntity.getProduct())) {
                continue;
            }
            String key = RedisKeyConstants.getUserIncomeAbnormalKey("ios", userEntity.getProduct(), userEntity.getUserIdStr());
            if (StringUtils.isNotBlank(userEventJedisClusterClient.get(key))) {
                continue;
            }
            userEventJedisClusterClient.setex(key, 3600, String.valueOf(System.currentTimeMillis()));

        }
    }

    public void userAndroidBaiduIncomeAbnormalJob() {
        List<UserMacIpCheckEntity> userInfoList = clickHouseService.queryAndroidBaiduIncomeAbnormalUserInfo();

        if (CollectionUtils.isNotEmpty(userInfoList)) {
            for (UserMacIpCheckEntity userEntity : userInfoList) {
                if (StringUtils.isBlank(userEntity.getUserIdStr())) {
                    continue;
                }
                if (StringUtils.isBlank(userEntity.getProduct())) {
                    continue;
                }
                String key = RedisKeyConstants.getUserBaiduIncomeAbnormalKey("android", userEntity.getProduct(), userEntity.getUserIdStr());
                if (StringUtils.isNotBlank(userEventJedisClusterClient.get(key))) {
                    continue;
                }
                userEventJedisClusterClient.setex(key, 3600, String.valueOf(System.currentTimeMillis()));
            }
        }

        //五组 ANDROID百度收入占比异常
        List<UserMacIpCheckEntity> userInfoListForGroup5 = clickHouseService.queryBaiduIncomeAbnormalGroup5UserInfo();
        List<String> group5KeyList = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(userInfoListForGroup5)) {
            for (UserMacIpCheckEntity userEntity : userInfoListForGroup5) {
                if (StringUtils.isBlank(userEntity.getUserIdStr())) {
                    continue;
                }
                if (StringUtils.isBlank(userEntity.getProduct())) {
                    continue;
                }
                String key = RedisKeyConstants.getBaiduIncomeAbnormalGroup5Key("android", userEntity.getProduct(), userEntity.getUserIdStr());
                group5KeyList.add(key);
            }
            batchSaveToRedis(group5KeyList, RedisKeyConstants.USER_INCOME_BAIDU_ABNORMAL_GROUP5_PREFIX, 3600);
        }

        // 安卓百度买量用户百度收入占比异常 arpu>30 inc> 97
        List<UserMacIpCheckEntity> userInfoList1 = clickHouseService.queryBdArpuIncAbnormalUserInfo();
        List<String> keyList = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(userInfoList1)) {
            for (UserMacIpCheckEntity userEntity : userInfoList1) {
                if (StringUtils.isBlank(userEntity.getUserIdStr())) {
                    continue;
                }
                if (StringUtils.isBlank(userEntity.getProduct())) {
                    continue;
                }
                String key = RedisKeyConstants.getBdArpuIncAbnormalKey(userEntity.getProduct(), userEntity.getUserIdStr());
                keyList.add(key);
            }
            batchSaveToRedis(keyList, RedisKeyConstants.BAIDU_ARPU_INCOME_ABNORMAL_PREFIX, 3600);
        }
    }

    public void userIncomeAbnormalJob() {
        // 快手达人华为渠道，广点通收入异常
        List<UserMacIpCheckEntity> userInfoList = clickHouseService.queryKsdrHwGdtIncomeAbnormalUserInfo();
        List<String> keyList = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(userInfoList)) {
            for (UserMacIpCheckEntity userEntity : userInfoList) {
                if (StringUtils.isBlank(userEntity.getUserIdStr())) {
                    continue;
                }
                if (StringUtils.isBlank(userEntity.getProduct())) {
                    continue;
                }
                String key = RedisKeyConstants.getKsdrHwGdtIncomeAbnormalKey(userEntity.getProduct(), userEntity.getUserIdStr());
                keyList.add(key);
            }
            batchSaveToRedis(keyList, RedisKeyConstants.KSDR_HW_GDT_INCOME_ABNORMAL_PREFIX, 3600);
        }

        List<String> keyList1;
        List<UserMacIpCheckEntity> userInfoList1 = queryGdtGapUserInfo();
        keyList1 = buildGdtGapKeyList(userInfoList1);
        batchSaveToRedis(keyList1, RedisKeyConstants.GDT_GAP_PREFIX, 3600);

    }

    private List<String> buildSgmGroup10KeyList(List<UserMacIpCheckEntity> userInfoList2) {
        List<String> keyList = new ArrayList<>();
        for (UserMacIpCheckEntity userEntity : userInfoList2) {
            if (StringUtils.isBlank(userEntity.getUserIdStr())) {
                continue;
            }
            if (StringUtils.isBlank(userEntity.getProduct())) {
                continue;
            }
            List<String> strings = JSONArray.parseArray(userEntity.getUserIdStr(), String.class);
            List<String> list = strings.stream().map(e -> RedisKeyConstants.getSgmGroup10Key(userEntity.getProduct(), e)).collect(Collectors.toList());
            keyList.addAll(list);
        }
        return keyList;
    }

    private List<UserMacIpCheckEntity> querySgmGroup10UserInfo(String group) {
        List<UserMacIpCheckEntity> userInfoList = ck001JobService.querySgmGroup10UserInfo(group);

        if (CollectionUtils.isEmpty(userInfoList)) {
            log.info("querySgmGroup10UserInfo userInfoList is empty");
            userInfoList = new ArrayList<>();
        }

        return userInfoList;
    }

    private List<String> buildGdtGapKeyList(List<UserMacIpCheckEntity> userInfoList) {
        List<String> keyList = new ArrayList<>();
        for (UserMacIpCheckEntity userEntity : userInfoList) {
            if (StringUtils.isBlank(userEntity.getUserIdStr())) {
                continue;
            }
            if (StringUtils.isBlank(userEntity.getProduct())) {
                continue;
            }
            String key = RedisKeyConstants.getGdtGapKey(userEntity.getProduct(), userEntity.getUserIdStr());
            keyList.add(key);
        }
        return keyList;
    }

    /**
     * gdt gap 用户
     * @return
     */
    private List<UserMacIpCheckEntity> queryGdtGapUserInfo() {
        List<UserMacIpCheckEntity> userInfoList = clickHouseService.queryGdtGapAbnormalUserInfo();

        if (CollectionUtils.isEmpty(userInfoList)) {
            log.info("queryGdtGapUserInfo userInfoList is empty");
            userInfoList = new ArrayList<>();
        }

        return userInfoList;
    }

    public void batchSaveToRedis(List<String> list, String PRE_FIX, Integer expireTime) {
        if (CollectionUtils.isEmpty(list)) {
            log.info("batchSaveToRedis list is empty");
            return;
        }
        if (expireTime == null) {
            expireTime = RedisKeyConstants.EXPIRE_THREE_DAYS;
        }
        int batchSize = 1000; // 每批次写入 1000 条
        for (int i = 0; i < list.size(); i += batchSize) {
            List<String> batch = list.subList(i, Math.min(i + batchSize, list.size()));
            try (Jedis jedis = userEventJedisClusterClient.getResource(PRE_FIX)) {
                Pipeline pipeline = jedis.pipelined();
                Integer finalExpireTime = expireTime;
                batch.forEach(key -> {
                    pipeline.setex(key, finalExpireTime, String.valueOf(System.currentTimeMillis()));
                });
                pipeline.sync();
            } catch (Exception e) {
                log.error("Batch SaveEx:", e);
            }
        }
    }

    public void checkNonBaiduAndroidIncomeAbnormalJob() {
        List<UserMacIpCheckEntity> userInfoList = clickHouseService.queryNonBaiduAndroidIncomeAbnormalUserInfo();

        if (CollectionUtils.isEmpty(userInfoList)) {
            log.info("checkNonBaiduAndroidIncomeAbnormalJob userInfoList is empty");
            return;
        }

        List<String> keyList1 = new ArrayList<>();
        List<String> keyList2 = new ArrayList<>();
        List<String> keyList3 = new ArrayList<>();
        List<String> keyList4 = new ArrayList<>();
        for (UserMacIpCheckEntity userEntity : userInfoList) {
            if (StringUtils.isBlank(userEntity.getUserIdStr())) {
                continue;
            }
            if (StringUtils.isBlank(userEntity.getProduct())) {
                continue;
            }
//            if (userEntity.getPv() > 50) {
//                String key = RedisKeyConstants.getNonBaiduIncomeAbnormalGeoup7Key("android", userEntity.getProduct(), userEntity.getUserIdStr());
//                keyList1.add(key);
//            }
            if (userEntity.getPv() > 100) {
                String key3 = RedisKeyConstants.getNonBaiduIncomeAbnormalGroup5Key("android", userEntity.getProduct(), userEntity.getUserIdStr());
                keyList3.add(key3);
            }
            String key = RedisKeyConstants.getNonBaiduIncomeAbnormalKey("android", userEntity.getProduct(), userEntity.getUserIdStr());
            keyList2.add(key);
            // 小米单独记录
            if (StringUtils.isNotBlank(userEntity.getManufacturer()) && "XIAOMI".equals(userEntity.getManufacturer())) {
                String nonBaiduXiaomiKey = RedisKeyConstants.getNonBaiduXiaomiKey("android", userEntity.getProduct(), userEntity.getUserIdStr());
                keyList4.add(nonBaiduXiaomiKey);
            }
        }
        batchSaveToRedis(keyList1, RedisKeyConstants.NON_BAIDU_INCOME_ABNORMAL_GROUP7_PREFIX, 3600);
        batchSaveToRedis(keyList2, RedisKeyConstants.NON_BAIDU_INCOME_ABNORMAL_PREFIX, 3600);
        batchSaveToRedis(keyList3, RedisKeyConstants.NON_BAIDU_INCOME_ABNORMAL_GROUP5_PREFIX, 3600);
        batchSaveToRedis(keyList4, RedisKeyConstants.NON_BAIDU_XIAOMI_PREFIX, 3600);
    }

    public void userCallBackInterceptRefreshJob() {
        List<UserMacIpCheckEntity> userInfoList = clickHouseService.queryUserCallBackInterceptNonBaiduUserInfo();

        if (CollectionUtils.isEmpty(userInfoList)) {
            log.info("userCallBackInterceptRefreshJob userInfoList is empty");
            return;
        }
        List<String> inceptUserList = new ArrayList<>();
        for (UserMacIpCheckEntity userEntity : userInfoList) {
            if (StringUtils.isBlank(userEntity.getUserIdStr())) {
                continue;
            }
            if (StringUtils.isBlank(userEntity.getProduct())) {
                continue;
            }
            String key = RedisKeyConstants.getUserCallBackInterceptNonBaiduKey(userEntity.getOs(), userEntity.getProduct(), userEntity.getUserIdStr());
            inceptUserList.add(key);
        }
        batchSaveToRedis(inceptUserList, RedisKeyConstants.USER_CALL_BACK_INTERCEPT_NON_BAIDU_PREFIX, 3600);
    }

    public boolean checkUserCallBackInterceptJob() throws IOException {
        String todayStr = DateUtil.dateToString(new Date()) + " 00:00:00";
        List<UserCallBackIntercept> list = userCallBackInterceptService.selectInterceptUserList(todayStr);

        if (CollectionUtils.isEmpty(list)) {
            return false;
        }

        String title = "测试-异常用户回调拦截";
        String msg = "今日异常用户回调拦截：" + list.size() + "人";
        String accessToken = "92f964966b1c0caf72ca2855e4e8382153335e5808e24516221778e0000b4dfe";

        File file = null;
        String downloadUrl = "";
        try {
            String path = "/data/tempCanDelete";
            String fileName = "异常用户回调拦截";
            // 数据构建excel表格，传送阿里云存储，钉钉发送下载链接
            file = userCallBackInterceptService.exportExcel(path, fileName, list);

            String suffix = new SimpleDateFormat("yyMMddHHmmssSSS").format(new Date());
            String innerPath = "tempExcelCanDelete/" + suffix + file.getName();
            ossService.uploadFile("coohua-video-manager", innerPath, Files.newInputStream(file.toPath()));

            downloadUrl = OssService.OSS_URL + "/" + innerPath;
        } catch (Exception e) {
            throw e;
        } finally {
            if (file != null) {
                file.delete();
            }
        }

        DingTalkPushUtils.sendActionCardMsg(accessToken, title, msg, downloadUrl, null);

        return true;
    }

    public void Group10GdtExUserJob(String param) {
        Double rate = 0.95;
        Double inc = 20.00;
        if (StringUtils.isNotBlank(param)) {
            String[] params = param.split(",");
            rate = Double.valueOf(params[0]);
            inc = Double.valueOf(params[1]);
        }
        List<String> keyList;
        List<UserMacIpCheckEntity> userInfoList = queryGdtGroup10UserInfo(rate, inc);
        keyList = buildGdtGroup10KeyList(userInfoList);
        batchSaveToRedis(keyList, RedisKeyConstants.GROUP10_GDT_PREFIX, 3600);
    }

    private List<String> buildGdtGroup10KeyList(List<UserMacIpCheckEntity> userInfoList2) {
        List<String> keyList = new ArrayList<>();
        for (UserMacIpCheckEntity userEntity : userInfoList2) {
            if (StringUtils.isBlank(userEntity.getUserIdStr())) {
                continue;
            }
            if (StringUtils.isBlank(userEntity.getProduct())) {
                continue;
            }
            List<String> strings = JSONArray.parseArray(userEntity.getUserIdStr(), String.class);
            List<String> list = strings.stream().map(e -> RedisKeyConstants.getGdtGroup10Key(userEntity.getProduct(), e)).collect(Collectors.toList());
            keyList.addAll(list);
        }
        return keyList;
    }

    private List<UserMacIpCheckEntity> queryGdtGroup10UserInfo(Double rate, Double inc) {
        List<UserMacIpCheckEntity> userInfoList = ck001JobService.queryGdtGroup10UserInfo(group10GdtExProduct, rate, inc);

        if (CollectionUtils.isEmpty(userInfoList)) {
            log.info("queryGdtGroup10UserInfo userInfoList is empty");
            userInfoList = new ArrayList<>();
        }

        return userInfoList;
    }

    public void GroupSgmGdtExUserJob(String param) {
        // 项目一组 特殊
//        List<String> keyList;
//        List<UserMacIpCheckEntity> userInfoList = querySgmGdtGroupUserInfo();
//        keyList = buildSgmGdtGroupKeyList(userInfoList);
//        batchSaveToRedis(keyList, RedisKeyConstants.GROUP_SGM_GDT_PREFIX, 60 * 15);

        Set<String> queryGroup = new HashSet<>();
        queryGroup.add("项目十组");
        if (StringUtils.isNotBlank(param)) {
            String[] params = param.split(",");
            queryGroup.addAll(Arrays.asList(params));
        }
        for (String group : queryGroup) {
            List<String> keyList2;
            List<UserMacIpCheckEntity> userInfoList2 = querySgmGroup10UserInfo(group);
            keyList2 = buildSgmGroup10KeyList(userInfoList2);
            batchSaveToRedis(keyList2, RedisKeyConstants.GROUP10_SGM_PREFIX, 3600);
        }

    }

    private List<String> buildSgmGdtGroupKeyList(List<UserMacIpCheckEntity> userInfoList) {
        List<String> keyList = new ArrayList<>();
        for (UserMacIpCheckEntity userEntity : userInfoList) {
            if (StringUtils.isBlank(userEntity.getUserIdStr())) {
                continue;
            }
            if (StringUtils.isBlank(userEntity.getProduct())) {
                continue;
            }
            List<String> strings = JSONArray.parseArray(userEntity.getUserIdStr(), String.class);
            List<String> list = strings.stream().map(e -> RedisKeyConstants.getSgmGdtGroupKey(userEntity.getProduct(), e)).collect(Collectors.toList());
            keyList.addAll(list);
        }
        return keyList;
    }

    private List<UserMacIpCheckEntity> querySgmGdtGroupUserInfo() {
        List<UserMacIpCheckEntity> userInfoList = clickHouseService.querySgmGdtGroupUserInfo();

        if (CollectionUtils.isEmpty(userInfoList)) {
            log.info("querySgmGdtGroupUserInfo userInfoList is empty");
            userInfoList = new ArrayList<>();
        }

        return userInfoList;
    }

    public void GdtGapExUserJob(String param) {
        List<String> keyList;
        List<UserMacIpCheckEntity> userInfoList = queryGdtGapExUserInfo();
        keyList = buildGdtGapExUserKeyList(userInfoList);
        batchSaveToRedis(keyList, RedisKeyConstants.GDT_GAP_EX_PREFIX, 3600);
    }

    private List<String> buildGdtGapExUserKeyList(List<UserMacIpCheckEntity> userInfoList) {
        List<String> keyList = new ArrayList<>();
        for (UserMacIpCheckEntity userEntity : userInfoList) {
            if (StringUtils.isBlank(userEntity.getUserIdStr())) {
                continue;
            }
            if (StringUtils.isBlank(userEntity.getProduct())) {
                continue;
            }
            List<String> strings = JSONArray.parseArray(userEntity.getUserIdStr(), String.class);
            List<String> list = strings.stream().map(e -> RedisKeyConstants.getGdtGapExKey(userEntity.getProduct(), e)).collect(Collectors.toList());
            keyList.addAll(list);
        }
        return keyList;
    }

    private List<UserMacIpCheckEntity> queryGdtGapExUserInfo() {
        List<UserMacIpCheckEntity> userInfoList = clickHouseService.queryGdtGapExUserInfo();

        if (CollectionUtils.isEmpty(userInfoList)) {
            log.info("queryGdtGapExUserInfo userInfoList is empty");
            userInfoList = new ArrayList<>();
        }

        return userInfoList;
    }
}
