package com.coohua.user.event.biz.core.entity;

import java.math.BigDecimal;
import com.baomidou.mybatisplus.annotation.IdType;
import java.util.Date;
import com.baomidou.mybatisplus.annotation.TableId;
import java.io.Serializable;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR>
 * @since 2020-09-07
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value="BpToufangResult对象", description="")
public class BpToufangResult implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    private String appName;
    private String appGroup;

    private BigDecimal cods;

    private BigDecimal rebateCost;

    private Long activate;

    private BigDecimal rebateCostOther;

    private Long activateOther;

    private BigDecimal rebateCostSum;

    private Long activateSum;

    private String dateStr;

    private String os;

    private Date createTime;

    private Date updateTime;


}
