package com.coohua.user.event.biz.core.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import java.io.Serializable;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR>
 * @since 2020-09-28
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value="UserEventCount对象", description="")
public class UserEventCount implements Serializable {

    private static final long serialVersionUID = 1L;

    private Long userId;

    private String product;

    private String productName;

    private Integer appId;

    private Integer adData;

    private Integer appClick;

    private Integer appNewsView;

    private Integer appPageView;

    private Integer appShare;

    private Integer startUp;


}
