package com.coohua.user.event.biz.dc.entity;

import java.io.Serializable;
import lombok.Data;
import java.util.Date;
import java.util.List;

/**
 *  business_result
 * <AUTHOR> 2020-09-08
 */
@Data
public class BusinessResult implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 项目组
     */
    private String groupName;

    /**
     * 产品
     */
    private String product;

    /**
     * 平台
     */
    private String os;

    /**
     * 子分类
     */
    private String adSlot;

    /**
     * 广告类型
     */
    private String adType;

    /**
     * 广告来源
     */
    private String adSource;

    /**
     * 曝光
     */
    private Long exposure;

    /**
     * request
     */
    private Long request;

    /**
     * income
     */
    private Double income;

    /**
     * 数据方
     */
    private String constrast;

    /**
     * 点击直客人数
     */
    private Long zkPeople;

    /**
     * 广告位置
     */
    private String adPosition;

    /**
     * 渠道
     */
    private String channel;

    /**
     * 渠道dau
     */
    private Integer channelDau;

    /**
     * yymmdd日期
     */
    private String logday;

    /**
     * dau
     */
    private Long dau;

    /**
     * yy-mm-dd日期
     */
    private String date2;

    /**
     * click
     */
    private Long click;
}
