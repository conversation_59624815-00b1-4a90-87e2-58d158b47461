package com.coohua.user.event.biz.click.mapper;

import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

/**
 * <AUTHOR>
 * @since 2021/11/19
 */
public interface ClickHouseRiskMapper {

    @Select("")
    String selectChannelOCPC(@Param("product")String product,@Param("os") String os);

    @Select(" select groupUniqArray(concat(device_id,'|',userid)) as result_array from " +
            "  ods.event_dist where product =#{product} and os = #{os} " +
            " and ( " +
            "    (channel like '%huawei%' and manufacturer not in ('HUAWEI', 'HONOR')) or " +
            "    (channel like '%oppo%' and manufacturer != 'OPPO') or " +
            "    (channel like '%vivo%' and manufacturer != 'vivo') " +
            " ) and device_id != '' and match(device_id, '^00000.*') = 0 " +
            " and logday = today()")
    String selectChannelMa(@Param("product")String product,@Param("os") String os);
}
