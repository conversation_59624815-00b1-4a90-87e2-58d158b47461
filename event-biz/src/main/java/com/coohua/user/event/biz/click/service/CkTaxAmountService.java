package com.coohua.user.event.biz.click.service;

import cn.hutool.core.thread.NamedThreadFactory;
import com.aliyun.openservices.shade.com.google.common.collect.Lists;
import com.coohua.user.event.biz.click.entity.OrderAmount;
import com.coohua.user.event.biz.click.entity.WithdrawMchDV;
import com.coohua.user.event.biz.click.mapper.ClickHouseOdsMapper;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.io.File;
import java.io.FileInputStream;
import java.io.FileOutputStream;
import java.io.IOException;
import java.sql.SQLException;
import java.util.*;
import java.util.concurrent.ArrayBlockingQueue;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;
import java.util.stream.IntStream;


@Component
@Slf4j
public class CkTaxAmountService {

    @Autowired
    private ClickHouseOdsMapper clickHouseOdsMapper;
    @Autowired
    private Ck004TaxAmountRepository ck004TaxAmountRepository;
    public static final ThreadPoolExecutor TAX_AMOUNT = new ThreadPoolExecutor(Runtime.getRuntime().availableProcessors(), Runtime.getRuntime().availableProcessors()*2,60L, TimeUnit.SECONDS,
            new ArrayBlockingQueue<>(500), new NamedThreadFactory("tax_amount",false),new ThreadPoolExecutor.CallerRunsPolicy());

    public static class AccumulationResult {
        private final List<WithdrawMchDV> selectedWithdrawMchDVS;
        private final long totalAmount;
        private final boolean exactMatch;

        public AccumulationResult(List<WithdrawMchDV> selectedWithdrawMchDVS, long totalAmount, boolean exactMatch) {
            this.selectedWithdrawMchDVS = selectedWithdrawMchDVS;
            this.totalAmount = totalAmount;
            this.exactMatch = exactMatch;
        }

        public List<WithdrawMchDV> getSelectedOrders() {
            return Collections.unmodifiableList(selectedWithdrawMchDVS);
        }

        public long getTotalAmount() {
            return totalAmount;
        }

        public boolean isExactMatch() {
            return exactMatch;
        }

        @Override
        public String toString() {
            return "AccumulationResult{" +
                    "selectedWithdrawMchDVS=" + selectedWithdrawMchDVS +
                    ", totalAmount=" + totalAmount +
                    ", exactMatch=" + exactMatch +
                    '}';
        }
    }

    /**
     * 从订单列表中筛选出累计金额最接近目标值的订单
     *
     * @param withdrawMchDVS       原始订单列表
     * @param targetAmount 目标金额
     * @param parallel     是否使用并行处理
     * @return 筛选结果对象
     */
    public static AccumulationResult accumulateOrders(List<WithdrawMchDV> withdrawMchDVS, long targetAmount, boolean parallel) {
        // 防御性拷贝
        List<WithdrawMchDV> withdrawMchDVCopy = new ArrayList<>(withdrawMchDVS);

        // 1. 按金额从小到大排序
        if (parallel) {
            withdrawMchDVCopy = withdrawMchDVCopy.parallelStream()
                    .sorted(Comparator.comparingLong(WithdrawMchDV::getAmount))
                    .collect(Collectors.toList());
        } else {
            withdrawMchDVCopy.sort(Comparator.comparingLong(WithdrawMchDV::getAmount));
        }

        // 2. 先尝试贪心算法（从小到大）
        long start = System.currentTimeMillis();
        AccumulationResult greedyResult = tryGreedyApproach(withdrawMchDVCopy, targetAmount);
        if (greedyResult.isExactMatch()) {
            return greedyResult;
        }
        long alTime = System.currentTimeMillis() - start;

        // 3. 如果贪心算法不成功，尝试动态规划或回溯算法
        if (withdrawMchDVCopy.size() <= 30) {
            // 小数据量使用回溯算法
            return findBestCombinationBacktrack(withdrawMchDVCopy, targetAmount);
        } else {
            // 大数据量使用动态规划
            return findBestCombinationDP2(withdrawMchDVCopy, targetAmount);
        }
    }

    /**
     * 贪心算法尝试从小到大累计
     */
    private static AccumulationResult tryGreedyApproach(List<WithdrawMchDV> sortedWithdrawMchDVS, long targetAmount) {
        List<WithdrawMchDV> selected = new ArrayList<>();
        long accumulated = 0;

        for (WithdrawMchDV withdrawMchDV : sortedWithdrawMchDVS) {
            if (accumulated + withdrawMchDV.getAmount() <= targetAmount) {
                selected.add(withdrawMchDV);
                accumulated += withdrawMchDV.getAmount();

                if (accumulated == targetAmount) {
                    return new AccumulationResult(selected, accumulated, true);
                }
            }
        }

        return new AccumulationResult(selected, accumulated, false);
    }

    /**
     * 回溯算法寻找最佳组合（适合小数据量）
     */
    private static AccumulationResult findBestCombinationBacktrack(List<WithdrawMchDV> withdrawMchDVS, long targetAmount) {
        List<WithdrawMchDV> bestCombination = new ArrayList<>();
        long[] bestInfo = {0, Long.MAX_VALUE}; // [bestAmount, bestDifference]

        backtrack(withdrawMchDVS, 0, targetAmount, new ArrayList<>(), 0, bestCombination, bestInfo);

        return new AccumulationResult(bestCombination, bestInfo[0], bestInfo[1] == 0);
    }

    private static void backtrack(List<WithdrawMchDV> withdrawMchDVS, int start, long target,
                                  List<WithdrawMchDV> current, long currentAmount,
                                  List<WithdrawMchDV> bestCombination, long[] bestInfo) {
        // 计算当前差值
        long diff = Math.abs(currentAmount - target);

        // 更新最佳组合
        if (diff < bestInfo[1] || (diff == bestInfo[1] && currentAmount > bestInfo[0])) {
            bestCombination.clear();
            bestCombination.addAll(current);
            bestInfo[0] = currentAmount;
            bestInfo[1] = diff;
        }

        // 找到精确匹配，提前终止
        if (bestInfo[1] == 0) {
            return;
        }

        // 剪枝条件
        if (currentAmount >= target + bestInfo[1]) {
            return;
        }

        // 继续搜索
        for (int i = start; i < withdrawMchDVS.size(); i++) {
            WithdrawMchDV withdrawMchDV = withdrawMchDVS.get(i);
            long newAmount = currentAmount + withdrawMchDV.getAmount();

            // 提前剪枝
            if (newAmount > target + bestInfo[1]) {
                continue;
            }

            current.add(withdrawMchDV);
            backtrack(withdrawMchDVS, i + 1, target, current, newAmount, bestCombination, bestInfo);
            current.remove(current.size() - 1);

            // 如果已经找到精确匹配，提前终止
            if (bestInfo[1] == 0) {
                return;
            }
        }
    }

    /**
     * 动态规划寻找最佳组合（适合大数据量）
     */
    private static AccumulationResult findBestCombinationDP(List<WithdrawMchDV> withdrawMchDVS, long targetAmount) {
        // 使用Map来存储可能达到的金额和对应的订单组合
        Map<Long, List<WithdrawMchDV>> dp = new HashMap<>();
        dp.put(0L, new ArrayList<>());

        long closestAmount = 0;
        long minDiff = Long.MAX_VALUE;
        List<WithdrawMchDV> bestCombination = new ArrayList<>();

        for (WithdrawMchDV withdrawMchDV : withdrawMchDVS) {
            Map<Long, List<WithdrawMchDV>> newEntries = new HashMap<>();

            for (Map.Entry<Long, List<WithdrawMchDV>> entry : dp.entrySet()) {
                long newAmount = entry.getKey() + withdrawMchDV.getAmount();
                List<WithdrawMchDV> newCombination = new ArrayList<>(entry.getValue());
                newCombination.add(withdrawMchDV);

                // 计算差值
                long diff = Math.abs(newAmount - targetAmount);

                // 更新最佳组合
                if (diff < minDiff || (diff == minDiff && newAmount > closestAmount)) {
                    closestAmount = newAmount;
                    minDiff = diff;
                    bestCombination = newCombination;

                    // 找到精确匹配，提前终止
                    if (minDiff == 0) {
                        return new AccumulationResult(bestCombination, closestAmount, true);
                    }
                }

                // 只保留比当前最佳组合更好的或相等的可能性
                if (newAmount <= targetAmount + minDiff) {
                    newEntries.put(newAmount, newCombination);
                }
            }

            // 合并到DP表
            for (Map.Entry<Long, List<WithdrawMchDV>> entry : newEntries.entrySet()) {
                if (!dp.containsKey(entry.getKey()) ||
                        entry.getValue().size() < dp.get(entry.getKey()).size()) {
                    dp.put(entry.getKey(), entry.getValue());
                }
            }

            // 定期清理DP表，移除不可能成为最佳解的项
            if (dp.size() > 10000) {
                long finalMinDiff = minDiff;
                dp.entrySet().removeIf(e -> e.getKey() > targetAmount + finalMinDiff);
            }
        }

        return new AccumulationResult(bestCombination, closestAmount, minDiff == 0);
    }

    /**
     * 动态规划寻找最佳组合（优化版）
     * 优化点：DP表中不存储完整的订单列表，只存储上一个节点（订单），以减少内存和CPU开销。
     * 在找到最佳金额后，通过回溯重构订单列表。
     */
    /**
     * 动态规划寻找最佳组合（修正并优化版）
     * 修正了原优化版中DP计算与最优解寻找耦合导致的逻辑错误。
     *
     * 步骤:
     * 1. 构建完整的可达金额路径图 (pathMap)，只记录路径，不判断优劣。
     * 2. 遍历路径图，从所有可达金额中找出最接近目标的金额。
     * 3. 根据找到的最佳金额，回溯路径图以重构订单列表。
     */
    private static AccumulationResult findBestCombinationDP2(List<WithdrawMchDV> orders, long targetAmount) {
        // DP表：Key为可达到的金额，Value为实现该金额最后添加的那个订单。
        Map<Long, WithdrawMchDV> pathMap = new HashMap<>();
        pathMap.put(0L, null); // 初始状态，金额0不需要任何订单

        // 步骤1: 构建完整的可达金额路径图
        for (WithdrawMchDV order : orders) {
            long orderAmount = order.getAmount();
            // 必须使用 new ArrayList<>(pathMap.keySet()) 来创建一个快照
            // 否则在遍历时修改 pathMap 会导致 ConcurrentModificationException
            for (Long currentSum : new ArrayList<>(pathMap.keySet())) {
                long newSum = currentSum + orderAmount;
                // 如果这个金额是第一次达到，就记录下来路径
                if (!pathMap.containsKey(newSum)) {
                    pathMap.put(newSum, order);
                }
            }
        }

        // 步骤2: 从所有可达金额中找出最接近目标的金额
        long bestAmount = 0;
        // 初始的最小差值应该是目标本身（对应金额为0时）
        long minDiff = targetAmount;

        for (long amount : pathMap.keySet()) {
            long diff = Math.abs(amount - targetAmount);
            if (diff < minDiff) {
                minDiff = diff;
                bestAmount = amount;
            } else if (diff == minDiff && amount > bestAmount) {
                // 如果差值相同，我们倾向于选择总金额更大的那个
                bestAmount = amount;
            }
        }

        // 步骤3: 回溯重构最佳订单列表
        List<WithdrawMchDV> bestCombination = new ArrayList<>();
        long amountToTrace = bestAmount;

        while (amountToTrace > 0 && pathMap.get(amountToTrace) != null) {
            WithdrawMchDV order = pathMap.get(amountToTrace);
            bestCombination.add(order);
            amountToTrace -= order.getAmount();
        }
        Collections.reverse(bestCombination); // 恢复原始添加顺序

        return new AccumulationResult(bestCombination, bestAmount, minDiff == 0);
    }

    private static AccumulationResult findBestCombinationMidParallel(List<WithdrawMchDV> withdrawMchDVS, long targetAmount) {
        int n = withdrawMchDVS.size();
        List<WithdrawMchDV> leftList = withdrawMchDVS.subList(0, n / 2);
        List<WithdrawMchDV> rightList = withdrawMchDVS.subList(n / 2, n);

        // 并行枚举左右所有组合
        List<Pair> leftSums = generateAllCombinations(leftList);
        List<Pair> rightSums = generateAllCombinations(rightList);

        // 按金额排序方便二分查找
        rightSums.sort(Comparator.comparingLong(p -> p.sum));

        long bestDiff = Long.MAX_VALUE;
        long bestSum = 0;
        List<WithdrawMchDV> bestCombination = new ArrayList<>();

        for (Pair left : leftSums) {
            long remaining = targetAmount - left.sum;
            int idx = binarySearchClosest(rightSums, remaining);

            for (int i = Math.max(0, idx - 2); i <= Math.min(rightSums.size() - 1, idx + 2); i++) {
                Pair right = rightSums.get(i);
                long total = left.sum + right.sum;
                long diff = Math.abs(total - targetAmount);

                if (diff < bestDiff || (diff == bestDiff && total > bestSum)) {
                    bestDiff = diff;
                    bestSum = total;
                    bestCombination = new ArrayList<>();
                    bestCombination.addAll(left.combination);
                    bestCombination.addAll(right.combination);

                    if (diff == 0) {
                        return new AccumulationResult(bestCombination, bestSum, true);
                    }
                }
            }
        }

        return new AccumulationResult(bestCombination, bestSum, bestDiff == 0);
    }

    // 存储金额和组合的辅助类
    static class Pair {
        long sum;
        List<WithdrawMchDV> combination;

        Pair(long sum, List<WithdrawMchDV> combination) {
            this.sum = sum;
            this.combination = combination;
        }
    }

    // 生成所有子集组合及其金额（并行）
    private static List<Pair> generateAllCombinations(List<WithdrawMchDV> list) {
        int n = list.size();
        int total = 1 << n; // 2^n 种组合
        return IntStream.range(0, total).parallel()
                .mapToObj(i -> {
                    long sum = 0;
                    List<WithdrawMchDV> combo = new ArrayList<>();
                    for (int j = 0; j < n; j++) {
                        if ((i & (1 << j)) != 0) {
                            combo.add(list.get(j));
                            sum += list.get(j).getAmount();
                        }
                    }
                    return new Pair(sum, combo);
                })
                .collect(Collectors.toList());
    }

    // 在有序列表中二分查找最接近目标值的索引
    private static int binarySearchClosest(List<Pair> sortedPairs, long target) {
        int low = 0, high = sortedPairs.size() - 1;
        while (low < high) {
            int mid = (low + high) / 2;
            if (sortedPairs.get(mid).sum < target) {
                low = mid + 1;
            } else {
                high = mid;
            }
        }
        return low;
    }


//    @PostConstruct
    public void taxAmount() throws SQLException {

//        List<String> list = Arrays.asList("开心农场主2,治愈田园生活,青青草原3,拼词大赢家2,天天点点消2,梦幻田园,江南花园,兔子吃萝卜,开心成语2,红颜九州,筑梦长安生活".split(","));

        List<WithdrawMchDV> ordersByProduct = clickHouseOdsMapper.findOrdersByProduct("");
        Map<String, List<WithdrawMchDV>> mapByProduct = ordersByProduct.stream()
                .collect(Collectors.groupingBy(WithdrawMchDV::getProductName));
        List<OrderAmount> orderAmounts = clickHouseOdsMapper.taxAmountByMonth();
        List<CompletableFuture> redisTaskList = new ArrayList<>();
        for (OrderAmount orderAmount : orderAmounts) {
            CompletableFuture<Void> redisConnectionTask = CompletableFuture.runAsync(() -> {

//            if (list.contains(orderAmount.getProductName())){
//                continue;
//            }
            long start = System.currentTimeMillis();
            log.info(orderAmount.getProductName() +" 产品正在运行中。。。");
            long target = orderAmount.getYdAmount();
            AccumulationResult parallelResult = accumulateOrders(mapByProduct.get(orderAmount.getProductName()), target, true);
            long alTime = System.currentTimeMillis() - start;
//            writeOrdersToExcel(parallelResult, "/Users/<USER>/Desktop/202409订单内容.xlsx",orderAmount.getProductName());
            for (List<WithdrawMchDV> orders : Lists.partition(parallelResult.getSelectedOrders(), 2000)) {
                try {
                    ck004TaxAmountRepository.batchSave(orders);
                } catch (SQLException e) {
                    throw new RuntimeException(e);
                }
            }
            log.info("产品为: {}, 目标金额: {}, 实际累计金额: {}, 是否精确匹配:{}, 选中订单数: {}, 算法使用时间:{} 运行时间:{}", orderAmount.getProductName(),target,parallelResult.getTotalAmount(),parallelResult.isExactMatch(),parallelResult.getSelectedOrders().size(), alTime,(System.currentTimeMillis() - start));
//            clickHouseOdsMapper.insertOrders(orderAmount.getProductName(), parallelResult.getSelectedOrders());
            // 测试串行处理
//        start = System.currentTimeMillis();
//        AccumulationResult sequentialResult = accumulateOrders(orders, target, false);
//        System.out.println("\n串行处理结果 (" + (System.currentTimeMillis() - start) + "ms):");
//        System.out.println("目标金额: " + target);
//        System.out.println("实际累计金额: " + sequentialResult.getTotalAmount());
//        System.out.println("是否精确匹配: " + sequentialResult.isExactMatch());
//        System.out.println("选中订单数: " + sequentialResult.getSelectedOrders().size());
            }, TAX_AMOUNT);
            redisTaskList.add(redisConnectionTask);
        }
        CompletableFuture.allOf(redisTaskList.toArray(new CompletableFuture[0])).join();

    }


    public static void writeOrdersToExcel(AccumulationResult result, String filePath, String productName) {
        Workbook workbook;
        File file = new File(filePath);
        boolean fileExists = file.exists();

        try {
            // 1. 加载已有文件或新建 Workbook
            if (fileExists) {
                try (FileInputStream fis = new FileInputStream(file)) {
                    workbook = new XSSFWorkbook(fis);
                }
            } else {
                workbook = new XSSFWorkbook();
            }

            // 2. 创建新的 sheet，确保 sheetName 唯一
            String baseSheetName = productName;
            String sheetName = getUniqueSheetName(workbook, baseSheetName);
            Sheet sheet = workbook.createSheet(sheetName);

            // 3. 写标题行
            Row header = sheet.createRow(0);
            header.createCell(0).setCellValue("OrderNo");
            header.createCell(1).setCellValue("ProductName");
            header.createCell(2).setCellValue("Amount");

            List<WithdrawMchDV> withdrawMchDVS = result.getSelectedOrders();

            // 4. 写数据
            for (int i = 0; i < withdrawMchDVS.size(); i++) {
                WithdrawMchDV withdrawMchDV = withdrawMchDVS.get(i);
                Row row = sheet.createRow(i + 1);
                row.createCell(0).setCellValue(withdrawMchDV.getOrderNo());
                row.createCell(1).setCellValue(withdrawMchDV.getProductName());
                row.createCell(2).setCellValue(withdrawMchDV.getAmount());
            }

            // 5. 自动列宽
            for (int i = 0; i < 3; i++) {
                sheet.autoSizeColumn(i);
            }

            // 6. 写回文件（用 FileOutputStream 覆盖整个文件，但保留已有 sheet）
            try (FileOutputStream fileOut = new FileOutputStream(file)) {
                workbook.write(fileOut);
                System.out.println("Appended sheet: " + sheetName);
            }

            workbook.close();

        } catch (IOException e) {
            e.printStackTrace();
        }
    }

    // 工具方法：防止 Sheet 重名
    private static String getUniqueSheetName(Workbook workbook, String baseName) {
        String name = baseName;
        int index = 1;
        while (workbook.getSheet(name) != null) {
            name = baseName + "_" + index;
            index++;
        }
        return name;
    }



}
