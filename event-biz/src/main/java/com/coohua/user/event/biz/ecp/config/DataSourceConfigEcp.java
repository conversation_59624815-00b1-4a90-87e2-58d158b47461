package com.coohua.user.event.biz.ecp.config;

import com.alibaba.druid.spring.boot.autoconfigure.DruidDataSourceBuilder;
import com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean;
import lombok.extern.slf4j.Slf4j;
import org.apache.ibatis.session.SqlSessionFactory;
import org.mybatis.spring.annotation.MapperScan;
import org.mybatis.spring.mapper.MapperScannerConfigurer;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.boot.autoconfigure.condition.ConditionalOnBean;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.io.ClassPathResource;
import org.springframework.core.io.support.PathMatchingResourcePatternResolver;

import javax.sql.DataSource;
import java.sql.SQLException;

/**
 * <AUTHOR>
 * @since 2020/11/5
 */
@Slf4j
@Configuration
@MapperScan(basePackages = {"com.coohua.user.event.biz.ecp.mapper"})
public class DataSourceConfigEcp {

    @Bean(name = "datasourceEcp")
    @ConfigurationProperties(prefix = "spring.datasource.ecp")
    public DataSource dataSource() throws SQLException {
        return DruidDataSourceBuilder.create().build();
    }

    @Bean(name = "datasourceEcpSqlSessionFactoryBean")
    @ConditionalOnBean(name = "datasourceEcp")
    public SqlSessionFactory sqlSessionFactoryBean(@Qualifier("datasourceEcp") DataSource dataSource) throws Exception {
        MybatisSqlSessionFactoryBean mybatisSqlSessionFactoryBean = new MybatisSqlSessionFactoryBean();
        mybatisSqlSessionFactoryBean.setDataSource(dataSource);
        mybatisSqlSessionFactoryBean.setConfigLocation(new ClassPathResource("config/mybatis_config.xml"));
        mybatisSqlSessionFactoryBean.setMapperLocations(new PathMatchingResourcePatternResolver().getResources("classpath*:mapper/ecp/*.xml"));
        return mybatisSqlSessionFactoryBean.getObject();
    }

    @Bean(name = "ecpMapperScannerConfigurer")
    @ConditionalOnBean(name = "datasourceEcpSqlSessionFactoryBean")
    public MapperScannerConfigurer mapperScannerConfigurer(){
        MapperScannerConfigurer configurer = new MapperScannerConfigurer();
        configurer.setBasePackage("com.coohua.user.event.biz.ecp.mapper");
        configurer.setSqlSessionFactoryBeanName("datasourceEcpSqlSessionFactoryBean");
        return configurer;
    }
}
