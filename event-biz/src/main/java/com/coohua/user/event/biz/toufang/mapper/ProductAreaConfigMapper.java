package com.coohua.user.event.biz.toufang.mapper;

import com.coohua.user.event.biz.toufang.entity.ProductLockConfig;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2022/2/16
 */
public interface ProductAreaConfigMapper {

    @Select("select * from product_lock_config where state_flag = 1 and del_flag = 0")
    List<ProductLockConfig> queryAllConfig();
}
