package com.coohua.user.event.biz.cache;

import com.coohua.user.event.biz.enums.CacheKeyConstants;
import com.github.benmanes.caffeine.cache.LoadingCache;
import com.github.benmanes.caffeine.cache.stats.CacheStats;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;

/**
 * (。≖ˇェˇ≖。)
 * 封装缓存常用 方法
 * @author: zkk
 * DateTime: 2020/9/4 15:39
 */
@Slf4j
@Service
@ConditionalOnProperty(name = "spring.application.name",havingValue = "user-event")
public class UserEventLogCacheService {

	/**
	 * 缓存用户创建时间 6小时过期
	 */
	@Resource(name = "userCreateTimeCache")
	private LoadingCache<String, Long> userCreateTimeCache;

	@Scheduled(cron = "0 0/1 * * * ?")
	public void logRecordStats() {
		CacheStats userCache = userCreateTimeCache.stats();
		log.info("用戶缓存使用情況  hitCount= {}, loadCount={}, hitRate={} missRate={}", userCache.loadCount(),userCache.hitCount(),userCache.hitRate(),userCache.missRate());
	}

	/**
	 * 根据 appId 和userId信息获取用户创建时间
	 ** @param userId
	 * @return
	 */
	public Long getUserCreateTime(String appId, String userId) {
		if(StringUtils.isEmpty(appId) || StringUtils.isEmpty(userId)){
			return null;
		}
		Long createTime = userCreateTimeCache.get(CacheKeyConstants.JOINER_LINE.join(appId, userId));
		return createTime;
	}
}
