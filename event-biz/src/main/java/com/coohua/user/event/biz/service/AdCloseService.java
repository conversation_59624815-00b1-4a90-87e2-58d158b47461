package com.coohua.user.event.biz.service;

import com.alibaba.fastjson.JSON;
import com.coohua.user.event.biz.click.entity.ProductEntity;
import com.coohua.user.event.biz.util.AppConfig;
import com.coohua.user.event.biz.util.DateUtil;
import com.coohua.user.event.biz.util.DingTalkPushUtils;
import com.coohua.user.event.biz.util.Strings;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import top.zrbcool.pepper.boot.jediscluster.JedisClusterClient;
import top.zrbcool.pepper.boot.jediscluster.JedisClusterClientRefer;

import javax.annotation.PostConstruct;
import java.util.*;

/**
 * <AUTHOR>
 * @since 2022/11/3
 */
@Slf4j
@Service
public class AdCloseService {

    @JedisClusterClientRefer(namespace = "ap-cluster")
    private JedisClusterClient apJedisClusterClient;

    // 检测是否影响过多用户
    public void checkDisablePlatformCount(){
        for (ProductEntity productEntity : AppConfig.productEnMap.values()){
            String value = apJedisClusterClient.get(buildSkipProductDailyCount(productEntity.getId()));
            if (Strings.noEmpty(value)){
                int count = Integer.parseInt(value);
                if (count > 1000){
                    DingTalkPushUtils.sendMsg(String.format("产品 %s 实时规则设置跳过平台广告已影响超过 %s 人",productEntity.getProductName(),count));
                }
            }
        }
    }

    public long deleteSkipPlatform(Integer appId, String userId){
        String skipAd = buildSkipAdSourceList(appId, userId);
        String skipAdBidding = buildSkipBiddingAdSourceList(appId, userId);
        Long del = apJedisClusterClient.del(skipAd);
        Long del1 = apJedisClusterClient.del(skipAdBidding);
        long l1 = del == null ? 0L : del;
        long l2 = del1 == null ? 0L : del1;
        return l1 + l2;
    }

    public void setSkipPlatform(Integer appId, String userId, List<Integer> platformList,Integer timeout){
        String skipAd = buildSkipAdSourceList(appId, userId);
        String skipAdBidding = buildSkipBiddingAdSourceList(appId, userId);

        // 只算首次
        String rs = apJedisClusterClient.getSet(skipAd, JSON.toJSONString(platformList));
        if (Strings.noEmpty(rs)) {
            try {
                // 检查是否需要追加
                List<Integer> resultList = JSON.parseArray(rs,Integer.class);
                List<Integer> notInList = new ArrayList<>();
                for (Integer code : platformList){
                    if (!resultList.contains(code)){
                        notInList.add(code);
                    }
                }
                if (notInList.size() > 0){
                    platformList.addAll(resultList);
                    Set<Integer> newRs = new HashSet<>(platformList);
                    apJedisClusterClient.set(skipAd,JSON.toJSONString(newRs));
                    apJedisClusterClient.set(skipAdBidding, JSON.toJSONString(newRs));
                    apJedisClusterClient.expire(skipAd, timeout);
                    apJedisClusterClient.expire(skipAdBidding, timeout);
                    log.info(">>> appId {} user {} has set skip {}",appId,userId,JSON.toJSONString(newRs));
                }
            }catch (Exception e){
                log.error("Append Csj Er:",e);
            }

            return;
        }
        apJedisClusterClient.set(skipAdBidding, JSON.toJSONString(platformList));

        apJedisClusterClient.expire(skipAd, timeout);
        apJedisClusterClient.expire(skipAdBidding, timeout);

        log.info(">>> appId {} user {} has set skip {}",appId,userId,JSON.toJSONString(platformList));
        // 产品统计今日设置数量
        String key = buildSkipProductDailyCount(appId);
        apJedisClusterClient.incr(key);
        // 记录两天
        apJedisClusterClient.expire(key, 60 * 60 * 24 * 2);
    }

    /**
     * 设置用户广告不返回穿山甲平台
     * @param appId appId
     * @param userId 用户ID
     * @param timeout 生效时间 单位s/秒
     */
    public void setNoCsjAd(Integer appId,String userId,Integer timeout){
        setSkipPlatform(appId,userId, new ArrayList<Integer>(){{add(1);}},timeout);
    }


    /**
     * 设置用户广告不返回广点通平台
     * @param appId appId
     * @param userId 用户ID
     * @param timeout 生效时间 单位s/秒
     */
    public void setNoGdtAd(Integer appId,String userId,Integer timeout){
        setSkipPlatform(appId,userId, Collections.singletonList(2),timeout);
    }

    private static String buildSkipAdSourceList(Integer app,String userId){
        return String.format("ad:skip:list:%s:%s",app,userId);
    }

    private static String buildSkipBiddingAdSourceList(Integer app,String userId){
        return String.format("ad:skip:bidding:list:%s:%s",app,userId);
    }

    private static String buildSkipProductDailyCount(Integer app){
        Date now = new Date();
        String dayStr = DateUtil.dateToString(now,DateUtil.ISO_DATE_FORMAT);
        return String.format("ad:skip:app:%s:%s",app,dayStr);
    }

}
