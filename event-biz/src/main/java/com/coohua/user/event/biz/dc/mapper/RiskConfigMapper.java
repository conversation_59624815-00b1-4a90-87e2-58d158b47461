package com.coohua.user.event.biz.dc.mapper;

import com.coohua.user.event.biz.dc.entity.RiskConfigEntity;
import org.apache.ibatis.annotations.Insert;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2021/11/6
 */
public interface RiskConfigMapper {

    @Insert({
            "<script>",
            "INSERT INTO `user-event`.risk_config (" +
                    "rule_id," +
                    "score," +
                    "product," +
                    "os," +
                    "create_time," +
                    "update_time" +
                    ")" +
                    "VALUES",
            "<foreach collection='entityList'  item='entity' separator=','>" ,
            "(" +
                    "#{entity.ruleId}," +
                    "#{entity.score}," +
                    "#{entity.product}," +
                    "#{entity.os}," +
                    "#{entity.createTime}," +
                    "#{entity.updateTime}" +
                    ")",
            "</foreach>",
            "</script>",
    })
    Integer batchInsert(@Param("entityList") List<RiskConfigEntity> entityList);

    @Update({"update `user-event`.risk_config set score = #{entity.score},update_time = #{entity.updateTime} where id = #{entity.id}"})
    Integer updateConfig(@Param("entity") RiskConfigEntity entity);

    RiskConfigEntity getById(@Param("id") Integer id);

    @Select({"select * from `user-event`.risk_config where product=#{product} and os =#{os}"})
    List<RiskConfigEntity> queryProductAndOs(@Param("product") Integer product,@Param("os") Integer os);
}
