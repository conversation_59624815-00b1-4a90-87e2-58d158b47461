package com.coohua.user.event.biz.core.service;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.coohua.user.event.biz.core.entity.BpToufangResult;
import com.coohua.user.event.biz.core.mapper.BpToufangResultMapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.coohua.user.event.biz.toufang.entity.TouFangEntity;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

/**
* <p>
    *  服务实现类
    * </p>
*
* <AUTHOR>
* @since 2020-09-03
*/
@Slf4j
@Service
public class BpToufangResultService extends ServiceImpl<BpToufangResultMapper, BpToufangResult> {


    public void saveBatch(List<TouFangEntity> touFangEntityList){

        List<BpToufangResult> bpToufangResults = touFangEntityList.stream()
                .map(touFangEntity -> {
                    BpToufangResult result = new BpToufangResult();
                    result.setDateStr(touFangEntity.getDataDate());
                    result.setCods(touFangEntity.getCods());
                    result.setRebateCost(touFangEntity.getRebateCost());
                    result.setRebateCostOther(touFangEntity.getRebateCostOther());
                    result.setRebateCostSum(touFangEntity.getRebateCostSum());
                    result.setAppGroup(touFangEntity.getAppGroup());
                    result.setAppName(touFangEntity.getAppName());
                    result.setOs(touFangEntity.getOs() == null ? touFangEntity.getOs():touFangEntity.getOs().toLowerCase());
                    result.setActivate(touFangEntity.getActivate());
                    result.setActivateOther(touFangEntity.getActivateOther());
                    result.setActivateSum(touFangEntity.getActivateSum());
                    result.setCreateTime(new Date());
                    result.setUpdateTime(new Date());
                    return result;
                })
                .collect(Collectors.toList());

        saveBatch(bpToufangResults);
    }

    public void delResultByDay(String logDay){
        QueryWrapper<BpToufangResult> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().eq(BpToufangResult::getDateStr, logDay);
        remove(queryWrapper);
    }

    public List<BpToufangResult> queryTouFangResultData(String logDay){
        log.info("查询 bp_toufang_result --{}",logDay);
        return lambdaQuery().eq(BpToufangResult::getDateStr,logDay).list();
    }

}
