package com.coohua.user.event.biz.dc.service;

import com.coohua.user.event.biz.dc.entity.TouFangAllOfflineEntity;
import com.coohua.user.event.biz.dc.entity.TouFangOfflineEntity;
import com.coohua.user.event.biz.dc.mapper.TouFangOfflineMapper;
import com.coohua.user.event.biz.toufang.entity.TouFangEntity;
import com.coohua.user.event.biz.toufang.mapper.TouFangBaseQueryMapper;
import com.coohua.user.event.biz.util.DateUtil;
import com.coohua.user.event.biz.util.Strings;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2020/9/8
 */
@Slf4j
@Service
public class TouFangOfflineService {

    @Resource
    private TouFangBaseQueryMapper touFangBaseQueryMapper;
    @Resource
    private TouFangOfflineMapper touFangOfflineMapper;

    public void sychToufangDataOffline(String date){
        log.info("开始投放离线数据同步...");
        log.info("Param:{}",date);
        Date crashDate =  DateUtil.dateIncreaseByDay(DateUtil.stringToDate(DateUtil.dateToString(new Date())),-1);
        if (Strings.noEmpty(date)) {
            crashDate = DateUtil.stringToDate(date);
        }

        String dateDate = DateUtil.dateToString(crashDate);
        log.info("查询日期：{}",dateDate);

        List<TouFangOfflineEntity>  list = touFangOfflineMapper.queryOfflineDataByDate(dateDate);
        if (list.size() > 0){
            Date now = new Date();
            List<TouFangAllOfflineEntity> allOfflineEntities = list.parallelStream()
                    .map(touFangEntity -> {
                        TouFangAllOfflineEntity entity = new TouFangAllOfflineEntity();
                        entity.setAppName(touFangEntity.getProductName());
                        entity.setActivate(Long.valueOf(touFangEntity.getDeviceNum()));
                        entity.setRebateCost(BigDecimal.valueOf(touFangEntity.getToufangAmount()));
                        entity.setLogday(DateUtil.stringToDate(touFangEntity.getLogday()));
                        entity.setOs(touFangEntity.getOs());

                        entity.setCreateTime(now);
                        entity.setUpdateTime(now);
                        entity.setChannelName(touFangEntity.getChannelName());
//                        entity.setCods();
                        return entity;
                    }).collect(Collectors.toList());

            touFangOfflineMapper.delByLogDayNotToutiao(dateDate);
            touFangOfflineMapper.insertBatch(allOfflineEntities);
        }
        log.info("结束投放离线数据同步...");
    }
}
