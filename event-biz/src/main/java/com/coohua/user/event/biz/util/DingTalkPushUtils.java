package com.coohua.user.event.biz.util;

import com.alibaba.fastjson.JSONObject;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2020/8/5
 */
@Slf4j
public class DingTalkPushUtils {
    private final static String dingTalkUrl = "https://oapi.dingtalk.com/robot/send?access_token=c4211f8a23029844ab92a67679b804d52efa0cc138bb7a719d36eb4c48bd93d2";
    private final static String dingTalkUrl_fzb = "https://oapi.dingtalk.com/robot/send?access_token=85be67fb0d8604102edc0a59012a836aaa960346e6bd34edd47004f33c4f5a7e";
    private static final String DINGTALKURL_PRE = "https://oapi.dingtalk.com/robot/send?access_token=%s";
    public static final String callBackInterceptToken = "92f964966b1c0caf72ca2855e4e8382153335e5808e24516221778e0000b4dfe";
    public static void jobErr(String jobName){
        Map<String,Object> header = new HashMap<>();
        header.put("Content-Type","application/json");
        JSONObject jsonObject = new JSONObject();
        JSONObject content = new JSONObject();
        JSONObject atMobiles = new JSONObject();
        atMobiles.put("atMobiles", new String[]{"15175062197"});
        jsonObject.put("msgtype", "text");
        content.put("content",
                "[Alert]数据调度异常！ \n\n" +
                        "- 任务 " + jobName + " 调度异常，请注意查看！ \n" +
                        "@15175062197"
        );
        jsonObject.put("at", atMobiles);
        jsonObject.put("text", content);
        HttpClients.POST(dingTalkUrl,header,jsonObject.toJSONString());
    }

    public static void pushMessage(String msg){
        Map<String,Object> header = new HashMap<>();
        header.put("Content-Type","application/json");
        JSONObject jsonObject = new JSONObject();
        JSONObject content = new JSONObject();
        JSONObject atMobiles = new JSONObject();
        atMobiles.put("atMobiles", new String[]{"17301209152"});
        jsonObject.put("msgtype", "text");
        content.put("content",
                "[Alert]调度异常！ \n\n" +
                        "- " + msg + "\n" +
                        "@17301209152"
        );
        jsonObject.put("at", atMobiles);
        jsonObject.put("text", content);
        HttpClients.POST(dingTalkUrl,header,jsonObject.toJSONString());
    }


    public static void sendMessage(String product,String remark,Integer count,String desc){
        Map<String,Object> header = new HashMap<>();
        header.put("Content-Type","application/json");
        JSONObject jsonObject = new JSONObject();
        JSONObject content = new JSONObject();
        JSONObject atMobiles = new JSONObject();
        atMobiles.put("atMobiles", new String[]{"17301209152"});
        jsonObject.put("msgtype", "text");
        content.put("content",
                "[Alert]查询到异常用户过多！ \n\n" +
                        ">> 产品 :" + product + " 当前发现 "+ remark +" 共" +count+"个"+desc+",请注意查看！ \n" +
                        "@17301209152"
        );
        jsonObject.put("at", atMobiles);
        jsonObject.put("text", content);
        HttpClients.POST(dingTalkUrl,header,jsonObject.toJSONString());
    }

    public static void sendMessage(String contentStr,Double rate){
        Map<String,Object> header = new HashMap<>();
        header.put("Content-Type","application/json");
        JSONObject jsonObject = new JSONObject();
        JSONObject content = new JSONObject();
        JSONObject atMobiles = new JSONObject();
        atMobiles.put("atMobiles", new String[]{"17301209152"});
        jsonObject.put("msgtype", "markdown");
        content.put("title","[Alert] AdPos Exception");
        String context = "## 今日累计拉黑用户过多,占比超过"+ rate +"%！ \n\n" +
                contentStr +"\n\n";
        content.put("text",context +" ##### @17301209152");
        jsonObject.put("at", atMobiles);
        jsonObject.put("markdown", content);
        HttpClients.POST(dingTalkUrl,header,jsonObject.toJSONString());
    }

    public static void sendMessage(String contentStr,Integer rate){
        Map<String,Object> header = new HashMap<>();
        header.put("Content-Type","application/json");
        JSONObject jsonObject = new JSONObject();
        JSONObject content = new JSONObject();
        JSONObject atMobiles = new JSONObject();
        atMobiles.put("atMobiles", new String[]{"18311025152"});
        jsonObject.put("msgtype", "markdown");
        content.put("title","[Alert] AdPos Exception");
        String context = "## 今日累计拉黑用户过多,超过"+ rate +"个 \n\n" +
                contentStr +"\n\n";
        content.put("text",context +" ##### @18311025152");
        jsonObject.put("at", atMobiles);
        jsonObject.put("markdown", content);
        HttpClients.POST(dingTalkUrl_fzb,header,jsonObject.toJSONString());
    }


    public static void sendMessageGrayUser(String contentStr,Integer max){
        Map<String,Object> header = new HashMap<>();
        header.put("Content-Type","application/json");
        JSONObject jsonObject = new JSONObject();
        JSONObject content = new JSONObject();
        JSONObject atMobiles = new JSONObject();
        atMobiles.put("atMobiles", new String[]{"17301209152","18141911769"});
        jsonObject.put("msgtype", "markdown");
        content.put("title","[Alert] AdPos Exception");
        String context = "## 今日累计拉灰用户过多,超过"+ max +"个 \n\n" +
                contentStr +"\n\n";
        content.put("text",context +" ##### @17301209152 @18141911769");
        jsonObject.put("at", atMobiles);
        jsonObject.put("markdown", content);
        HttpClients.POST(dingTalkUrl,header,jsonObject.toJSONString());
    }


    public static void sendMsg(String contentStr){
        Map<String,Object> header = new HashMap<>();
        header.put("Content-Type","application/json");
        JSONObject jsonObject = new JSONObject();
        JSONObject content = new JSONObject();
        JSONObject atMobiles = new JSONObject();
        atMobiles.put("atMobiles", new String[]{"17301209152"});
        jsonObject.put("msgtype", "markdown");
        content.put("title","[Alert] Alert Msg");
        content.put("text", contentStr +" ##### @17301209152");
        jsonObject.put("at", atMobiles);
        jsonObject.put("markdown", content);
        HttpClients.POST(dingTalkUrl,header,jsonObject.toJSONString());
    }

    public static void sendActionCardMsg(String accessToken, String title, String msg, String downLoadUrl, Collection<String> phoneArray) {
        String url = String.format(DINGTALKURL_PRE, accessToken);
        Map<String, Object> header = new HashMap<>();
        header.put("Content-Type", "application/json");
        header.put("Charset", "UTF-8");
        JSONObject param = new JSONObject();
        JSONObject actionCard = new JSONObject();
        JSONObject bntContent = new JSONObject();
        if (StringUtils.isNotBlank(downLoadUrl)) {
            List<JSONObject> btnsContent = new ArrayList<>();
            bntContent.put("title", "下载详情");
            bntContent.put("actionURL", downLoadUrl);
            btnsContent.add(bntContent);
            actionCard.put("btns", btnsContent.toArray());
        }
//        String s = "## **%s**\n\r%s";
//        String sendMsg = String.format(s, title, msg);
        if (CollectionUtils.isNotEmpty(phoneArray)) {
            String atPhones = phoneArray.stream().filter(k -> StringUtils.isNotBlank(k)).map(k -> "@" + k).collect(Collectors.joining());
//            sendMsg += "\n\r### " + atPhones;
            param.put("at", new JSONObject()
                    .fluentPut("atMobiles", phoneArray)
                    .fluentPut("isAtAll", false));
        }

        actionCard.put("title", title);
        actionCard.put("text", msg);
        actionCard.put("btnOrientation", "0");
        actionCard.put("hideAvatar", "0");

        param.put("msgtype", "actionCard");
        param.put("actionCard", actionCard);


        HttpClients.POST(url, header, param.toJSONString());
    }


    public static void sendMsgReleaseMall(String context){
        Map<String,Object> header = new HashMap<>();
        header.put("Content-Type","application/json");
        JSONObject jsonObject = new JSONObject();
        JSONObject content = new JSONObject();
        JSONObject atMobiles = new JSONObject();
        //atMobiles.put("atMobiles", new String[]{"17301209152","18141911769"});
        jsonObject.put("msgtype", "markdown");
        content.put("title","误拦截订单释放通知");
        content.put("text",context);
        jsonObject.put("at", atMobiles);
        jsonObject.put("markdown", content);
        String format = String.format(DINGTALKURL_PRE, "665c77a35481d419608035348226d5538b81608352ee051761ffeccfa0a8efa8");
        HttpClients.POST(format,header,jsonObject.toJSONString());
    }


}
