package com.coohua.user.event.biz.util;

/**
 * <AUTHOR>
 * @since 2023/3/30
 */
public class AdTypeConvert {

    public static Integer convertToInt(String r){
        switch (r){
            case "穿山甲":return 1;
            case "广点通":return 2;
            case "快手":return 3;
            case "百度":return 4;
            case "VIVO":return 5;
            case "OPPO":return 6;
            case "小米":return 7;
            case "华为":return 8;
            default: return 0;
        }
    }

    public static String convertToStr(Integer r){
        switch (r){
            case 1:return "穿山甲";
            case 2:return "广点通";
            case 3:return "快手";
            case 4:return "百度";
            case 5:return "VIVO";
            case 6:return "OPPO";
            case 7:return "小米";
            case 8:return "华为";
            default: return "";
        }
    }
}
