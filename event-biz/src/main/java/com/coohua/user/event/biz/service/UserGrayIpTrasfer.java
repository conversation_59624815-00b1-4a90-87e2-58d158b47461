package com.coohua.user.event.biz.service;

import com.alibaba.fastjson.JSON;
import com.coohua.user.event.biz.dc.entity.UserGrayIpEntity;
import com.coohua.user.event.biz.dc.mapper.UserGrayIpMapper;
import com.coohua.user.event.biz.enums.GrayType;
import com.coohua.user.event.biz.service.bean.GlobalBlackUserBean;
import com.coohua.user.event.biz.util.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.hadoop.hbase.TableName;
import org.apache.hadoop.hbase.client.Connection;
import org.apache.hadoop.hbase.client.Put;
import org.apache.hadoop.hbase.client.Table;
import org.apache.hadoop.hbase.util.Bytes;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.function.UnaryOperator;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2021/11/22
 */
@Slf4j
@Service
public class UserGrayIpTrasfer {

    @Resource
    private UserGrayIpMapper userGrayIpMapper;
    @Autowired
    private UserDeviceCheckService userDeviceCheckService;
    // 一次一万条
    private final static Integer BATCH = 10000;

    public void transfer(){
        log.info("开始数据迁移....");
        Integer maxId = userGrayIpMapper.selectMaxId();
        if (maxId > BATCH){
            int size = maxId / BATCH;
            int mixId = 0;
            for (int i= 0;i<= size;i++){
                List<UserGrayIpEntity> userGrayIpEntityList = userGrayIpMapper.queryBatch(mixId,BATCH);
                solve(userGrayIpEntityList);
                mixId += BATCH;
                log.info("正在处理第{}",mixId);
            }
        }else {
            List<UserGrayIpEntity> userGrayIpEntityList = userGrayIpMapper.queryBatch(0,BATCH);
            solve(userGrayIpEntityList);
        }
        log.info("结束数据迁移");
    }

    private void solve2(List<UserGrayIpEntity> userGrayIpEntities){
        if (Lists.noEmpty(userGrayIpEntities)){
            List<String> device = userGrayIpEntities.stream()
                    .filter(r -> GrayType.DEVICE.getType().equals(r.getTargetType()))
                    .map(r -> r.getTargetId())
                    .collect(Collectors.toList());
            userDeviceCheckService.saveGrayDevice(device);
        }
    }

    private void solve(List<UserGrayIpEntity> userGrayIpEntities){
        if (Lists.noEmpty(userGrayIpEntities)){
            Map<Integer,List<UserGrayIpEntity>> grayMap = userGrayIpEntities.stream().collect(Collectors.groupingBy(UserGrayIpEntity::getTargetType));
            grayMap.forEach((targetType,resList) ->{
                GrayType grayType = GrayType.get(targetType);
                if (grayType == null){
                    return;
                }
                Map<String, GlobalBlackUserBean> insertMap = resList.stream().collect(
                        Collectors.toMap(UserGrayIpEntity::getTargetId,userGrayIpEntity -> {
                            GlobalBlackUserBean userBean = new GlobalBlackUserBean();
                            userBean.setTargetType(grayType.getType());
                            userBean.setTargetId(userGrayIpEntity.getTargetId());
                            userBean.setIsGlobalGary(true);
                            userBean.setReason(userGrayIpEntity.getRemark());
                            userBean.setGrayTime(userGrayIpEntity.getCreateTime());
                            userBean.setTargetAppList(new ArrayList<>());
                            return userBean;
                        },(r1,r2)->r2)
                );
                switch (grayType){
                    case DEVICE:
                        this.saveBatch(insertMap, GLOBAL_DEVICE_ID_KEY_MAPPER);
                        break;
                    case UNION:
                        this.saveBatch(insertMap,GLOBAL_UNION_ID_KEY_MAPPER);
                        break;
                    case USER:
                        this.saveBatch(insertMap,GLOBAL_USER_ID_KEY_MAPPER);
                        break;
                    default:
                        break;
                }
            });
        }
    }


    /**
     * 全局黑名单表名
     */
    private static final String GLOBAL_BLACK_LIST_TABLE_NAME = "GlobalBlackUser";

    /**
     * userId黑名单key生成器
     */
    public static final UnaryOperator<String> GLOBAL_USER_ID_KEY_MAPPER = userId -> "user:" + userId;

    /**
     * deviceId黑名单key生成器
     */
    public static final UnaryOperator<String> GLOBAL_DEVICE_ID_KEY_MAPPER = deviceId -> "device:" + deviceId;

    /**
     * unionId黑名单key生成器
     */
    public static final UnaryOperator<String> GLOBAL_UNION_ID_KEY_MAPPER = unionId -> "union:" + unionId;

    /**
     * hbase连接
     */
    @Resource()
    private Connection hbaseConnection;


    public void saveBatch(Map<String, GlobalBlackUserBean> resMap, UnaryOperator<String> blackListKeyMapper){
        try (Table table = hbaseConnection.getTable(TableName.valueOf(GLOBAL_BLACK_LIST_TABLE_NAME))) {
            List<Put> puts = resMap.keySet().stream()
                    .map(targetId -> {
                        String rowKey = blackListKeyMapper.apply(targetId);
                        Put put = new Put(Bytes.toBytes(rowKey));
                        put.addColumn(Bytes.toBytes("family"), Bytes.toBytes("qualifier"), Bytes.toBytes(JSON.toJSONString(resMap.get(targetId))));
                        return put;
                    })
                    .collect(Collectors.toList());

            table.put(puts);
            log.info("本次成功存储：{} 个",resMap.size());
        }catch (Exception e){
            log.error("Save Err:",e);
        }
    }

}
