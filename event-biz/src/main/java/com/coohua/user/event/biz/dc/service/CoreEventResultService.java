package com.coohua.user.event.biz.dc.service;

import com.coohua.user.event.biz.click.entity.ProductEntity;
import com.coohua.user.event.biz.dc.entity.CoreEventResultEntity;
import com.coohua.user.event.biz.dc.mapper.CoreEventResultMapper;
import com.coohua.user.event.biz.toufang.entity.CoreEventEntity;
import com.coohua.user.event.biz.toufang.entity.UserEventModelEntity;
import com.coohua.user.event.biz.util.DateUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2020/9/19
 */
@Slf4j
@Service
public class CoreEventResultService {


    @Resource
    private CoreEventResultMapper coreEventResultMapper;

    public int insertBatch(List<CoreEventEntity> coreEventEntityList,
                           Map<String,ProductEntity> productEntityMap,
                           Map<String, UserEventModelEntity> userEventModelEntityMap){

        if (coreEventEntityList == null || coreEventEntityList.size() ==0){
            return 0;

        }

        List<CoreEventResultEntity> coreEventResultEntityList = coreEventEntityList.parallelStream()
                .map(coreEventEntity -> {
                    CoreEventResultEntity eventResultEntity = new CoreEventResultEntity();
                    eventResultEntity.setLogday(coreEventEntity.getLogDate());
                    eventResultEntity.setOs(coreEventEntity.getOs());
                    ProductEntity productEntity = productEntityMap.getOrDefault(coreEventEntity.getProduct(),new ProductEntity());
                    eventResultEntity.setProductCn(productEntity.getProductName());
                    eventResultEntity.setProductEn(coreEventEntity.getProduct());
                    eventResultEntity.setProductGroup(productEntity.getProductGroup());
                    eventResultEntity.setGid(coreEventEntity.getGroupId());
                    eventResultEntity.setCid(coreEventEntity.getCid());
                    eventResultEntity.setPid(coreEventEntity.getAdId());


                    eventResultEntity.setAccountId(coreEventEntity.getAccountId());
                    eventResultEntity.setDsp(coreEventEntity.getDsp());
                    String key = coreEventEntity.getProduct() + coreEventEntity.getDsp() + coreEventEntity.getOs();

                    UserEventModelEntity userEventModelEntity = userEventModelEntityMap.getOrDefault(key,new UserEventModelEntity(){{
                        setCountDeviceId(0);
                    }});

                    eventResultEntity.setCountActive(userEventModelEntity.getCountDeviceId());
                    eventResultEntity.setEventType1(coreEventEntity.getEvent1Type());
                    eventResultEntity.setEventType2(coreEventEntity.getEvent2Type());
                    eventResultEntity.setEventType3(coreEventEntity.getEvent3Type());
                    eventResultEntity.setEventType4(coreEventEntity.getEvent4Type());
                    eventResultEntity.setEventType5(coreEventEntity.getEvent5Type());

                    return eventResultEntity;
                }).collect(Collectors.toList());

        return coreEventResultMapper.insertBatch(coreEventResultEntityList);
    }

    public int deleteResult(String logDay){
        return coreEventResultMapper.delete(logDay);
    }

}
