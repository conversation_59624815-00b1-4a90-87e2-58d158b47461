package com.coohua.user.event.biz.dc.service;

import com.coohua.user.event.biz.dc.entity.CdpAdData;
import com.coohua.user.event.biz.dc.mapper.CdpAdDataMapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;

import java.util.List;

/**
* <p>
    *  服务实现类
    * </p>
*
* <AUTHOR>
* @since 2023-06-15
*/
@Service
public class CdpAdDataService extends ServiceImpl<CdpAdDataMapper, CdpAdData> {


    public List<CdpAdData> queryByLogday(String logday){
        return baseMapper.queryByLogday(logday);
    }
}
