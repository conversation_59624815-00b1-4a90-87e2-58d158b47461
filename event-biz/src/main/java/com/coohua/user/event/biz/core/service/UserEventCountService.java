package com.coohua.user.event.biz.core.service;

import com.coohua.user.event.biz.core.entity.UserEventCount;
import com.coohua.user.event.biz.core.mapper.UserEventCountMapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;


/**
* <p>
    *  服务实现类
    * </p>
*
* <AUTHOR>
* @since 2020-09-28
*/
@Service
public class UserEventCountService extends ServiceImpl<UserEventCountMapper, UserEventCount> {
	@Autowired
	UserEventCountMapper userEventCountMapper;
}
