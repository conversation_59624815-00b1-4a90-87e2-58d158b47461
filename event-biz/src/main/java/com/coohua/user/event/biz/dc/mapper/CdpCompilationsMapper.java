package com.coohua.user.event.biz.dc.mapper;

import com.coohua.user.event.biz.dc.entity.CdpAdData;
import com.coohua.user.event.biz.dc.entity.CdpCompilations;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * <p>
 *  Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2023-06-15
 */
public interface CdpCompilationsMapper extends BaseMapper<CdpCompilations> {

    @Select({"select * from ads.cdp_compilations where logday =#{logday}"})
    List<CdpCompilations> queryByLogday(@Param("logday")String logday);
}
