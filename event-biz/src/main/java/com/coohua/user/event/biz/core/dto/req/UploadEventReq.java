package com.coohua.user.event.biz.core.dto.req;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @since 2020/9/2
 */
@Data
public class UploadEventReq {

    private String oaid;
    @ApiModelProperty(required = true)
    private Long userId;
    @ApiModelProperty(required = true)
    private String deviceId;
    private String brand;
    private String gps;
    private String bs;
    private String appVersion;
    @ApiModelProperty(required = true)
    private String os;
    @ApiModelProperty(required = true)
    private String channel;
    private String romVersion;
    private String osVersion;
    private String accessKey;
    private String wechatId;
    private String pkgId;
    @ApiModelProperty(required = true)
    private String appId;

    public Integer getIntOs(){
        return os.equals("android") ? 0 : 1;
    }
}
