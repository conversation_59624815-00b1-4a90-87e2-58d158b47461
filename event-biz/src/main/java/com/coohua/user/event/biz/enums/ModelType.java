package com.coohua.user.event.biz.enums;

/**
 * <AUTHOR>
 * @since 2022/10/11
 */
public enum ModelType {
    NOT_NEED(0,"不需要机型维度"),
    NEED(1,"需要机型维度")
    ;
    private Integer code;
    private String desc;

    ModelType(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public Integer getCode() {
        return code;
    }

    public void setCode(Integer code) {
        this.code = code;
    }

    public String getDesc() {
        return desc;
    }

    public void setDesc(String desc) {
        this.desc = desc;
    }
}
