package com.coohua.user.event.biz.service;

import com.alibaba.fastjson.JSON;
import com.coohua.user.event.biz.click.entity.*;
import com.coohua.user.event.biz.click.mapper.ClickHouseOdsMapper;
import com.coohua.user.event.biz.click.service.ClickHouseService;
import com.coohua.user.event.biz.dc.dto.RealtimeCsjRulesDto;
import com.coohua.user.event.biz.dc.service.RealtimeCsjRuleService;
import com.coohua.user.event.biz.enums.GrayType;
import com.coohua.user.event.biz.util.AppConfig;
import com.coohua.user.event.biz.util.DingTalkPushUtils;
import com.coohua.user.event.biz.util.Lists;
import com.ctrip.framework.apollo.spring.annotation.ApolloJsonValue;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.math3.stat.descriptive.summary.Product;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2022/10/11
 */
@Slf4j
@Service
public class UserEcpmCheckService {

    @Autowired
    private UserGrayService userGrayService;
    @Autowired
    private RealtimeCsjRuleService realtimeCsjRuleService;
    @Autowired
    private ClickHouseService clickHouseService;
    @Autowired
    private AdCloseService adCloseService;
    @ApolloJsonValue("${join.call_back.check:[657,786,775,768]}")
    private List<Integer> joinCheckAppList;

    /**
     * 渠道-机型聚集型ECPM检查
     */
    public void checkEcpm(){
        if (checkRules()){
            return;
        }
        List<RealtimeCsjRulesDto> realtimeCsjRulesDtos = realtimeCsjRuleService.queryRealTimeRules();
        realtimeCsjRulesDtos.forEach(this::doCheckAndGray);

        // 广点通偏门
        RealtimeCsjRulesDto realtimeCsjRulesDto = new RealtimeCsjRulesDto();
        List<RealtimeCsjRulesDto.ActionLimit> actionLimitList = new ArrayList<>();
        actionLimitList.add(new RealtimeCsjRulesDto.ActionLimit(){{
            setLimitPv(20);
            setLimitAvgEcpm(1500);
        }});
        realtimeCsjRulesDto.setActionTask(actionLimitList);
        // 活跃补充
        List<UserCsjEcpmExEntity> userCsjEcpmExEntitiesGdt = clickHouseService.queryExChannelAndMGdt(95,100,5,1500);
        if (Lists.noEmpty(userCsjEcpmExEntitiesGdt)){
            userCsjEcpmExEntitiesGdt.forEach(r -> doCheckAndGray(r,realtimeCsjRulesDto));
        }
    }

    // 检查广点通回调 5min check一次
    public void checkGdtCallBack(){
        if (Lists.noEmpty(joinCheckAppList)) {
            log.info("===> 开始广点通GAP检测....");
            List<UserCallBackBean> gapUserIdList = clickHouseService.queryUserGdtGap(joinCheckAppList);
            log.info("===> 本批次获取到 {} 需要关停广点通广告",gapUserIdList.size());
            if (Lists.noEmpty(gapUserIdList)) {
                if (gapUserIdList.size() > 1000){
                    log.warn("超过1k不处理...");
                    return;
                }
                gapUserIdList.forEach(gapUser -> {
                    // 关24h黑屋 不给看广点通
                    adCloseService.setNoGdtAd(gapUser.getAppId(), gapUser.getUserId(), 60 * 60 * 24);
                    log.info(">>> Success Set SkipGdt {} {}", gapUser.getAppId(), gapUser.getUserId());
                });
            }
            log.info("===> 完成广点通GAP检测....");
        }
    }
    @Resource
    private ClickHouseOdsMapper clickHouseOdsMapper;

    public long delSkipUser(){
        List<UserMacIpCheckEntity> list = clickHouseOdsMapper.queryDelUser();

        long count = 0;
        for (UserMacIpCheckEntity userMacIpCheckEntity : list) {
            List<String> userIds = JSON.parseArray(userMacIpCheckEntity.getUserIdStr(),String.class);
            ProductEntity productEntity = AppConfig.productEnMap.get(userMacIpCheckEntity.getProduct());
            for (String userId : userIds) {
                long l = adCloseService.deleteSkipPlatform(productEntity.getId(), userId);
                count += l;
            }
            log.info("成功删除 {} 条记录",count);
        }
        return count;
    }

    public void checkEcpmDr(){
        List<UserEcpmCheckBean> queryExChannelProduct = clickHouseService.queryExDrChannel();
        if (Lists.noEmpty(queryExChannelProduct)){
            queryExChannelProduct.forEach(userEcpmCheckBean -> {
                UserMacIpCheckEntity queryUser = clickHouseService.queryExDrUser(userEcpmCheckBean.getProduct(),userEcpmCheckBean.getAvgEcpm());
                if (queryUser != null){
                    List<String> userIds = JSON.parseArray(queryUser.getUserIdStr(),String.class);
                    ProductEntity productEntity = AppConfig.productEnMap.get(queryUser.getProduct());
                    if (productEntity == null){
                        return;
                    }
                    for (String userId : userIds) {
                        // 关三天小黑屋 不给看穿山甲
                        adCloseService.setNoCsjAd(productEntity.getId(), userId, 60*60*24*3);
                        log.info(">>> Success Set SkipCsj {} {}",productEntity.getId(),userId);
                    }
                }
            });
        }
    }

    private boolean checkRules(){
        Integer count = clickHouseService.countAlreadyGrayUser();
        if (count > 1000) {
            DingTalkPushUtils.sendMessageGrayUser("ECPM集中-实时规则拉黑" + count + "个", 2000);
        }
        return count >= 1000;
    }

    private void doCheckAndGray(RealtimeCsjRulesDto realtimeCsjRulesDto){
        try {
            // Check 该规则下异常的产品-渠道-品牌
            List<UserCsjEcpmExEntity> userCsjEcpmExEntities = clickHouseService.queryExChannelAndM(
                    realtimeCsjRulesDto.getCsjRate(),
                    realtimeCsjRulesDto.getPv(),
                    realtimeCsjRulesDto.getUv(),
                    realtimeCsjRulesDto.getAvgEcpm(),
                    realtimeCsjRulesDto.getModelType(),
                    realtimeCsjRulesDto.getIncludeType(),
                    realtimeCsjRulesDto.getChannelStartList()
            );
            // 依次检查相关产品-渠道-品牌的用户并拉黑
            if (Lists.noEmpty(userCsjEcpmExEntities)){
                userCsjEcpmExEntities.forEach(r -> doCheckAndGray(r,realtimeCsjRulesDto));
            }
        }catch (Exception e){
            log.error("Solve Realtime Csj Rules Ex:",e);
        }

    }

    private void doCheckAndGray(UserCsjEcpmExEntity userCsjEcpmExEntity,RealtimeCsjRulesDto realtimeCsjRulesDto){
        if ("dwsj2".equals(userCsjEcpmExEntity.getProduct())){
            log.info("动物世界2 不处理");
            return;
        }

        List<RealtimeCsjRulesDto.ActionLimit> actionLimitList = realtimeCsjRulesDto.getActionTask();

        List<UserMacIpCheckEntity> userMacIpCheckEntities = clickHouseService.queryExChannelAndMUser(
                userCsjEcpmExEntity.getProduct(),
                actionLimitList,
                userCsjEcpmExEntity.getExChannelList(),
                userCsjEcpmExEntity.getExManufacturerList(),
                userCsjEcpmExEntity.getExModelList()
        );

        String remark = String.format("ECPM集中%s过高-%s",userCsjEcpmExEntity.getExType(),userCsjEcpmExEntity.getProduct());
        if (Lists.noEmpty(userMacIpCheckEntities)){
            userMacIpCheckEntities.forEach(userMacIpCheckEntity -> {
                List<String> userIdList = JSON.parseArray(userMacIpCheckEntity.getUserIdStr(),String.class);
                List<String> deviceIdList = JSON.parseArray(userMacIpCheckEntity.getDeviceIdStr(),String.class);
                // 执行全局拉黑
                userGrayService.grayUser(GrayType.USER,userIdList,remark,userCsjEcpmExEntity.getProduct(),"android",false,true);
                userGrayService.grayUser(GrayType.DEVICE,deviceIdList,remark,userCsjEcpmExEntity.getProduct(),"android",false,true);
            });
        }
    }
}
