package com.coohua.user.event.biz.config;

import com.aliyun.oss.OSS;
import com.aliyun.oss.OSSClientBuilder;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

@Configuration
public class OssConfiguration {

    @Value("${ali.oss.event.endpoint:oss-cn-beijing-internal.aliyuncs.com}")
    private String eventEndpoint;

    @Value("${ali.oss.event.accessKeyId:LTAI4G7Lq5677x5MSCmUL9VW}")
    private String eventAccessKeyId;

    @Value("${ali.oss.event.accessKeySecret:******************************}")
    private String eventAccessKeySecret;



    @Bean(name = "eventManagerOss")
    public OSS ossClientFactory() {
        return new OSSClientBuilder().build(eventEndpoint, eventAccessKeyId, eventAccessKeySecret);
    }
}
