package com.coohua.user.event.biz.click.service;

import com.coohua.user.event.biz.click.entity.LBSBean;
import com.coohua.user.event.biz.click.entity.VideoAdReportEntitiy;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.stereotype.Component;
import ru.yandex.clickhouse.ClickHouseDataSource;
import ru.yandex.clickhouse.settings.ClickHouseProperties;

import javax.annotation.PreDestroy;
import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.SQLException;
import java.util.List;

/**
 * 暂时不支持并发
 */
@Slf4j
@Component
public class CkLbsInfoRepository implements InitializingBean {

    private static final String USER = "default";
    private static final String PASSWORD = "Phw7a7A4";
    private static final String URL = "*********************************";
    private ClickHouseDataSource dataSource;
    private Connection connection;
    private PreparedStatement ps;
    public int batchCounter = 0;
    private static final String INSERT_LBS = "insert into ods.lbs_detail_local (logday, product, user_id, device_id, channel, app_version, imei, oaid, android_id, mac, brand, model, ip, city, create_time)" +
            " values (?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)";

    @PreDestroy
    public void destroy() {
        if (ps != null) {
            try {
                ps.close();
            } catch (SQLException throwables) {
                log.error("", throwables);
            }
        }

        if (connection != null) {
            try {
                connection.close();
            } catch (SQLException throwables) {
                log.error("", throwables);
            }
        }

    }

    @Override
    public void afterPropertiesSet() throws Exception {
        Class.forName("ru.yandex.clickhouse.ClickHouseDriver");
        ClickHouseProperties properties = new ClickHouseProperties();
        properties.setUser(USER);
        properties.setPassword(PASSWORD);
        dataSource = new ClickHouseDataSource(URL, properties);
    }

    public void batchSave(List<LBSBean> lbsBeans) throws SQLException {
        if (connection == null) {
            connection = dataSource.getConnection();
        }
        if (ps == null) {
            ps = connection.prepareStatement(INSERT_LBS);
        }
        for (LBSBean lbsBean : lbsBeans) {
            addToBatch(lbsBean);
        }
        long begin = System.currentTimeMillis();
        ps.executeBatch();
        log.info("[CK]保存数据成功! batchSize:{}, cost:{}mms", batchCounter, System.currentTimeMillis() - begin);
        batchCounter = 0;
    }

    public synchronized void executeBatch() throws SQLException {
        if (ps != null) {
            long begin = System.currentTimeMillis();
            ps.executeBatch();
            log.info("[CK]保存数据成功! batchSize:{}, cost:{}mms", batchCounter, System.currentTimeMillis() - begin);
            batchCounter = 0;
        }
    }

    public synchronized  void addToBatch(LBSBean lbsBean) throws SQLException {
        if (connection == null) {
            connection = dataSource.getConnection();
        }
        if (ps == null) {
            ps = connection.prepareStatement(INSERT_LBS);
        }
        int colIndex = 1;
        ps.setDate(colIndex++, lbsBean.getLogday());
        ps.setString(colIndex++, lbsBean.getProduct());
        ps.setLong(colIndex++, lbsBean.getUserId());
        ps.setString(colIndex++, lbsBean.getDeviceId());
        ps.setString(colIndex++, lbsBean.getChannel());
        ps.setString(colIndex++, lbsBean.getAppVersion());
        ps.setString(colIndex++, lbsBean.getImei());
        ps.setString(colIndex++, lbsBean.getOaid());
        ps.setString(colIndex++, lbsBean.getAndroidId());
        ps.setString(colIndex++, lbsBean.getMac());
        ps.setString(colIndex++, lbsBean.getBrand());
        ps.setString(colIndex++, lbsBean.getModel());
        ps.setString(colIndex++, lbsBean.getIp());
        ps.setString(colIndex++, lbsBean.getCity());
        ps.setTimestamp(colIndex++, lbsBean.getCrTime());

        ps.addBatch();
        batchCounter++;
    }

}
