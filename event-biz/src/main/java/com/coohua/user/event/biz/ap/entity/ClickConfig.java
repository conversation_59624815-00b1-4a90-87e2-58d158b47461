package com.coohua.user.event.biz.ap.entity;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 * @since 2021/9/3
 */
@Data
public class ClickConfig implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * id
     */
    private Integer id;

    /**
     * 生效的app_id
     */
    private Integer appId;

    /**
     * 曝光次数挡位
     */
    private Integer exposure;

    /**
     * click_a
     */
    private Integer clickA;

    /**
     * rate_a
     */
    private Double rateA;

    /**
     * click_b
     */
    private Integer clickB;

    /**
     * rate_b
     */
    private Double rateB;

    /**
     * del_flag
     */
    private Integer delFlag;

    /**
     * create_time
     */
    private Date createTime;

    /**
     * update_time
     */
    private Date updateTime;

    public ClickConfig() {}
}