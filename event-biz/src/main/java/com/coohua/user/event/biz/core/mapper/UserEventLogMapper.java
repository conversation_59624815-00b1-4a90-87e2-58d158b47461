package com.coohua.user.event.biz.core.mapper;

import com.coohua.user.event.biz.core.dto.UserEventLogEntity;
import com.coohua.user.event.biz.core.entity.UserEventLog;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Insert;
import org.apache.ibatis.annotations.Param;

/**
 * <p>
 *  Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2020-09-04
 */
public interface UserEventLogMapper extends BaseMapper<UserEventLog> {

    @Insert({
            "<script>",
            "INSERT INTO ${tableName} ",
                    "(app_id,user_id,device_id,channel,int_os,user_create_time,pkg_id)",
            "VALUES (#{appId},#{userId},#{deviceId},#{channel},#{intOs},#{userCreateTime},#{pkgId})",
            "</script>",
    })
    int insertEvent(UserEventLogEntity eventLogEntity);
}
