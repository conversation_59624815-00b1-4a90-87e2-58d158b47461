package com.coohua.user.event.biz.util;

import java.util.Arrays;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @since 2021/8/18
 */
public class RedisUtil {

    public final static Map<String,String> rangeMap = new HashMap<String,String>(){{
        put("0-20","20:");
        put("21-40","40:");
        put("41-60","60:");
        put("61-80","80:");
        put("81-100","100:");
        put("101-120","120:");
        put("121-140","140:");
        put("141-160","160:");
        put("161-180","180:");
        put("181-200","200:");
        put("201-250","250:");
        put("251-300","300:");
        put("301-350","350:");
        put("351-400","400:");
        put("401-450","450:");
        put("451-500","500:");
        put("501-600","600:");
        put("601-700","700:");
        put("701-800","800:");
        put("801-900","900:");
        put("901-1000","1000:");
        put("1001-1500","1500:");
        put("1501-5000","5000:");
    }};

    public static String buildExposureVideo(Long userId, Integer appId){
        Date now = new Date();
        String day = DateUtil.dateToString(now,DateUtil.ISO_DATE_FORMAT);
        return "user:exposure:video:time:" + day + ":" + appId + ":" + userId;
    }

    public static String buildUserReaderAd(String appId,String userId){
        return "ad:user:filter:" + appId + ":" + userId;
    }

    public static String buildAbUserKey(String appId,String userId){
        return "{ab:user}:ecpm:avg:" + appId + ":" + userId;
    }

    public static String[] buildAbUserGroupKeyAll(String appId,Double ecpm){
        String perFix = "ab:user:ecpm:group:" + appId + ":";
        for (Map.Entry<String, String> entry : rangeMap.entrySet()) {
            String k = entry.getKey();
            String v = entry.getValue();
            String[] range = k.split("-");
            if (ecpm >= Integer.parseInt(range[0]) && Integer.parseInt(range[1]) >= ecpm) {
                perFix += v;
                break;
            }
        }
        return new String[]{perFix + 1,perFix + 2};
    }

    public static String buildUserExConfigKey(Integer appId,String userId){
        return "{risk:slot}:user:risk:ex:config:" + appId + ":" + userId;
    }

    public static String buildUserRiskKey(String userId,Integer appId){
        return "{risk:slot}:user:risk:level:" + appId + ":" + userId;
    }

    public static String buildUserRiskDailyKey(String userId,Integer appId){
        return "{risk:slot}:user:risk:daily:level:" + appId + ":" + userId;
    }
    public static String buildUserRiskFailCheckKey(String userId,Integer appId){
        return "{risk:slot}:user:risk:check:fail:" + appId + ":" + userId;
    }

    public static String buildGrayedIpKey(String ip){
        return "user:grayed:ip:" + ip;
    }


    public static void main(String[] args) {
        System.out.println(Arrays.toString(buildAbUserGroupKeyAll("395", 59d)));
    }
}
