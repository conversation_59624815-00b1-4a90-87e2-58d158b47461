package com.coohua.user.event.biz.user.service;

import com.coohua.user.event.biz.dc.entity.WithdrawOrderDetail;
import com.coohua.user.event.biz.user.entity.OsBean;
import com.coohua.user.event.biz.user.mapper.BpMallMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2020/9/14
 */
@Service
@Slf4j
public class BpMallService {

    @Resource
    private BpMallMapper bpMallMapper;


    public List<WithdrawOrderDetail> queryOrderList(String tableName, Long idMin,Long page){
        return bpMallMapper.queryOrderList(tableName,idMin,page);
    }

    public WithdrawOrderDetail queryOrder(String tableName, Long userId,String orderNo){
        return bpMallMapper.queryByOrderNoAndUserID(tableName,userId,orderNo);
    }

    public List<WithdrawOrderDetail> queryOrder(String tableName,List<String> orderNoList){
        return bpMallMapper.queryByOrderNoListAndUserID(tableName, orderNoList);
    }

    public Long countOrderList(String tableName, Long creatTime){
        return bpMallMapper.countOrderList(tableName,creatTime);
    }

    public Long selectMinId(String tableName, Long creatTime){
        return bpMallMapper.selectMinId(tableName,creatTime);
    }

    public Long selectMaxId(String tableName, Long creatTime){
        return bpMallMapper.selectMaxId(tableName,creatTime);
    }

    public List<OsBean> queryOsBeanList(String tableName, List<Long> orderIdList, Integer appId){
        return bpMallMapper.queryOsList(tableName,orderIdList,appId);
    }
}
