package com.coohua.user.event.biz.dc.mapper;

import com.coohua.user.event.biz.dc.entity.UserGrayIpEntity;
import org.apache.ibatis.annotations.*;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2021/8/19
 */
public interface UserGrayIpMapper {

    @Insert({"INSERT INTO `user-event`.user_gray_ip (" +
            "product," +
            "os," +
            "target_type," +
            "target_id," +
            "ip," +
            "remark," +
            "create_time," +
            "update_time)" +
            "VALUES(#{product}," +
            "#{os}," +
            "#{targetType}," +
            "#{targetId}," +
            "#{ip}," +
            "#{remark}," +
            "#{createTime}," +
            "#{updateTime})"})
    Integer insert(UserGrayIpEntity userGrayIpEntity);

    @Insert({"INSERT INTO `user-event`.user_gray_ip_del_record (" +
            "product," +
            "os," +
            "target_type," +
            "target_id," +
            "ip," +
            "remark," +
            "create_time," +
            "update_time)" +
            "VALUES(#{product}," +
            "#{os}," +
            "#{targetType}," +
            "#{targetId}," +
            "#{ip}," +
            "#{remark}," +
            "#{createTime}," +
            "#{updateTime})"})
    Integer insertIntoUserGrayIpDelRecord(UserGrayIpEntity userGrayIpEntity);
    
    
    @Update({"UPDATE `user-event`.`user_gray_ip` " +
            "SET `product` = #{product}, " +
            " `os` = #{os}, " +
            " `target_type` = #{targetType}, " +
            " `target_id` = #{targetId}, " +
            " `ip` = #{ip}, " +
            " `remark` = #{remark}, " +
            " `update_time` = #{updateTime} " +
            "WHERE (`id` = #{id});"
    })
    Integer update(UserGrayIpEntity userGrayIpEntity);

    @Select({"<script>",
            " select count(1) from `user-event`.user_gray_ip where target_type = #{type} and target_id =#{targetId} ",
            "<if test ='null != product'>" ,
            "  and product = #{product} " ,
            "</if>" ,
            "</script>",
    })
    Integer countExist(@Param("product") String product,
                       @Param("type") Integer type,
                       @Param("targetId") String targetId);


    @Select({"<script>",
            " select * from `user-event`.user_gray_ip where target_type = #{type} and target_id =#{targetId} ",
            "<if test ='null != product'>" ,
            "  and product = #{product} " ,
            "</if>" ,
            "limit 1" ,
            "</script>",
    })
    UserGrayIpEntity selectOne(@Param("product") String product,
                       @Param("type") Integer type,
                       @Param("targetId") String targetId);

    @Select({"<script>",
            " select * from `user-event`.user_gray_ip where target_type = #{type} and target_id in ",
            "<foreach collection='targetIds' index='index' item='item' open='(' separator=',' close=')'>#{item}</foreach>",
            "<if test ='null != product'>" ,
            "  and product = #{product} " ,
            "</if>" ,
            "</script>",
    })
    List<UserGrayIpEntity> queryList(@Param("product") String product,
                                      @Param("type") Integer type,
                                      @Param("targetIds") List<String> targetIds);

    @Delete({"<script>",
            " delete from `user-event`.user_gray_ip where id in ",
            "<foreach collection='targetIds' index='index' item='item' open='(' separator=',' close=')'>#{item}</foreach>",
            "</script>",
    })
    int delByIds(@Param("targetIds") List<Integer> targetIds);

    @Delete({"<script>",
            "delete from `user-event`.user_gray_ip where target_type = #{type} and target_id =#{targetId} ",
            "<if test ='null != product'>" ,
            "  and product = #{product} " ,
            "</if>" ,
            "</script>",
    })
    Integer delete(@Param("product") String product,
                   @Param("type") Integer type,
                   @Param("targetId") String targetId);

    @Select("select target_id from `user-event`.user_gray_ip where target_type = #{type} and os ='ios' and remark = '火山SDK检测900-非投放用户拉黑' and create_time > '2021-10-26 00:00:00'")
    List<String> selectReset(@Param("type")Integer type);

    @Select("select max(id) from `user-event`.user_gray_ip")
    Integer selectMaxId();

    @Select("select * from `user-event`.user_gray_ip where id > #{minId} limit #{batchSize}")
    List<UserGrayIpEntity> queryBatch(@Param("minId") Integer minId,@Param("batchSize") Integer batchSize);

    @Select("select * from `user-event`.user_gray_ip where create_time < #{crTime}")
    List<UserGrayIpEntity> queryBatchLessThanCreateTime(@Param("crTime") Date createTime);

    @Select("select * from `user-event`.user_gray_ip where create_time between #{begin} and #{end}")
    List<UserGrayIpEntity> queryBatchLess(@Param("begin") String begin,@Param("end") String end);

    @Select("select target_id, target_type\n" +
            "from `user-event`.user_gray_ip\n" +
            "where (remark like '%单产品不同用户req_id重复-异常拉黑%')\n" +
            "  and update_time > '2025-07-31 00:00:00' and update_time <= '2025-08-01 00:00:00';")
    List<UserGrayIpEntity> selctGaryUser();
}
