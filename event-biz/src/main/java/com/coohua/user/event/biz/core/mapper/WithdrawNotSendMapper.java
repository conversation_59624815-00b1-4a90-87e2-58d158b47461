package com.coohua.user.event.biz.core.mapper;

import com.coohua.user.event.biz.core.entity.WithdrawNotSend;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;

import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * <p>
 *  Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2023-09-23
 */
public interface WithdrawNotSendMapper extends BaseMapper<WithdrawNotSend> {


    @Select("select product, count(1) num\n" +
            "from `user-event`.withdraw_not_send\n" +
            "where remark = '校验失败:规则88-拒绝提现'\n" +
            "  and create_time > #{today}\n" +
            "group by product\n" +
            "having num > #{minInterceptNum}\n" +
            "order by num desc")
    List<Map<String, Integer>> selectRule88Count(@Param("minInterceptNum") Integer minInterceptNum, @Param("today") Date today);

    @Update({"update `user-event`.withdraw_not_send " +
            "set can_send = 1, update_time = now() " +
            "WHERE app_id = #{appId} and user_id = #{userId} and order_no = #{orderNo};"
    })
    void updateCanSend(@Param("appId") Integer appId, @Param("userId") Long userId, @Param("orderNo") String orderNo);

    @Select("select distinct product,app_id from `user-event`.withdraw_not_send")
    List<WithdrawNotSend> getProductAndAppId();
}
