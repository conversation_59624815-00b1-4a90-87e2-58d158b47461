package com.coohua.user.event.biz.dc.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @since 2021/7/7
 */
@Data
@ApiModel(description = "Ltv查询返回结果")
public class LtvQueryResponse {

    @ApiModelProperty(value = "激活设备数")
    private Integer activeCount;

    @ApiModelProperty(value = "ROS LTV")
    private Double rosLtv;

    @ApiModelProperty(value = "LifeTimeValue")
    private Double ltv;

    @ApiModelProperty(value = "视频名称")
    private String videoName;

    @ApiModelProperty(value = "广告主id")
    private String advertiserId;
}
