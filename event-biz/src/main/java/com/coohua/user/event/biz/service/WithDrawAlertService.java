package com.coohua.user.event.biz.service;

import com.coohua.user.event.biz.core.mapper.WithdrawNotSendMapper;
import com.coohua.user.event.biz.util.AppConfig;
import com.coohua.user.event.biz.util.DateUtil;
import com.coohua.user.event.biz.util.DingTalkPushUtils;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Service
public class WithDrawAlertService {

    @Resource
    private WithdrawNotSendMapper withdrawNotSendMapper;

    public void withDrawRule88Alert(Integer minInterceptNum) {
        // 获取今日0点时间
        Date today = DateUtil.stringToDate(DateUtil.dateToString(new Date()) + " 00:00:00",DateUtil.COMMON_TIME_FORMAT);
        List<Map<String, Integer>> list = withdrawNotSendMapper.selectRule88Count(minInterceptNum, today);

        if (CollectionUtils.isEmpty(list)) return;

        ;
        String alert = "规则88提现拦截订单\n\n今日拦截:\n\n";
        alert += list.stream()
                .map(r ->
                        String.format("产品:%s(%s),今日拦截:%s单"
                                , AppConfig.productEnMap.get(r.get("product")).getProductName(), r.get("product"), r.get("num"))
                )
                .collect(Collectors.joining(";\n\n"));

        String title = "[Alert] 规则88提现拦截订单";
        String token = "7d532c9c5ddbe8d7477d95b9815bc445e3b379c74a72bd1ea4d10bdcb06aef72";

        DingTalkPushUtils.sendActionCardMsg(token, title, alert, null, null);
    }
}
