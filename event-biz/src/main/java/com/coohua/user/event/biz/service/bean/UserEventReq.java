package com.coohua.user.event.biz.service.bean;

import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;


@Data
@Slf4j
public class UserEventReq {
    private Integer appId; //应用id
    private String userId;
    private String product;//xiaoxiaole
    private String os;//android || ios
    private String ocpcDeviceId;//md5 imei  ios idfa 没做
    private String mac;//客户端传输原值
    private String androidId;//安卓id原值的md5，32位
    private String oaid;// and头条roid 拿的
    private String oaid2;// md5(oaid) 不是回传过来的，是接收到oaid后计算的
    private String idfa2;// md5(idfa) 不是回传过来的，是接收到idfa后计算的
    private int eventType; //回传事件 定义 0 激活

    private String clickId;
    /**
     * 渠道包标识
     */
    private String pkgChannel;
    private String pkg_channel;

    private String SourceDeviceId;
    private String SourceOaid;

    private Integer actionType;

    /**
     * 行为计数；采用String是为了兼容整数、小数及未来可能扩展的其他类型
     */
    private String actionNumber;
    private Boolean actionDefault;

    /**
     * 升级版事件类型及相应值回传字段，将满足条件的事件及值全部回传
     */
    private String actionValues;

    /**
     * 数据累积到当前值所花费的时间：单位为分钟
     */
    private Integer accumulateDuration;

    private Boolean isSaveAction = true;

    private Integer payAmount;//微信小游戏 付费 单位分
    private String ip;
    private String ua;
    private String model;
    private String openId;
    private String wAppId;
    private String caid;
    private String caidVersion;

    private String guiType;
    public String concatIpua() {
        if (StringUtils.isAnyBlank(ip, ua, model)) {
            return null;
        }
        if ("null".equals(ip) || "null".equals(ua) || "null".equals(model)) {
            return null;
        }
        return ip + "_" + ua + "_" + model;
    }

    public String concatIpuaMd() {
        if (StringUtils.isAnyBlank(ip, ua)) {
            return null;
        }
        if ("null".equals(ip) || "null".equals(ua)) {
            return null;
        }
        return ip + "_" + ua;
    }






}
