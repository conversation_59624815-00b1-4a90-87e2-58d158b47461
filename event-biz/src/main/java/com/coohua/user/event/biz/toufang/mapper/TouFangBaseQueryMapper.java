package com.coohua.user.event.biz.toufang.mapper;

import com.coohua.user.event.biz.dc.entity.TouFangAllOfflineEntity;
import com.coohua.user.event.biz.toufang.entity.TouFangEntity;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2020/9/4
 */
public interface TouFangBaseQueryMapper {

    @Select({
            "<script>",
            "SELECT sum(cost) AS cods,sum(rebate_cost) rebate_cost,sum(active) activate,app_name," +
                    " (CASE project_group  " +
                    " WHEN '1组' THEN '项目一组' " +
                    " WHEN '2组' THEN '项目二组' " +
                    " WHEN '3组' THEN '项目三组' " +
                    " WHEN '4组' THEN '项目四组' " +
                    " WHEN '5组' THEN '项目五组' " +
                    " WHEN '6组' THEN '项目六组' " +
                    " WHEN '7组' THEN '项目七组' " +
                    " WHEN '8组' THEN '项目八组' " +
                    " WHEN '9组' THEN '项目九组' " +
                    " ELSE NULL END " +
                    ") AS app_group," ,
                    " data_date,os FROM toutiao_report_ideaday WHERE " ,
                    " data_date = #{date} " ,
                    " GROUP BY app_name,project_group,data_date,os",
            "</script>",
    })
    List<TouFangEntity> queryTouFang(@Param("date")String queryDate);




    @Select({"SELECT " +
            " DATE_FORMAT(stat_date,'%Y-%m-%d') as logday," +
            " krdu.os as os, " +
            " sum(rebate_cost) as rebate_cost, " +
            " sum(activation) as activate, " +
            " sum(charge) as cods, " +
            " prc.product_id as app_id, " +
            " prc.product_name as app_name "+
            "FROM " +
            " kuaishou_report_data_unit krdu " +
            "LEFT JOIN ( " +
            " SELECT " +
            "  akad.product_id, " +
            "  akad.product_name, " +
            "  aka.advertiser_id " +
            " FROM " +
            "  auth_kuaishou_app aka " +
            " LEFT JOIN auth_kuaishou_advertiser akad ON akad.customer_id = aka.id " +
            " WHERE " +
            "  aka.advertiser_id IS NOT NULL " +
            " AND product_id IS NOT NULL " +
            " GROUP BY " +
            "  aka.advertiser_id, " +
            "  product_id " +
            ") AS prc ON krdu.advertiser_id = prc.advertiser_id " +
            "where stat_date = #{dateStr} and charge > 0 and os != '' and os is not null " +
            "GROUP BY " +
            " krdu.stat_date," +
            " krdu.os, " +
            " prc.product_id "})
    List<TouFangAllOfflineEntity> queryKuaishouData(@Param("dateStr")String dateStr);


    @Select({"SELECT " +
            " DATE_FORMAT(date, '%Y-%m-%d') as logday, " +
            " sum(IFNULL(cost,0) / 100) AS cods, " +
            " sum(IFNULL(rebate_cost,0) / 100) AS rebate_cost, " +
            " sum(IFNULL(activated_count,0)) AS activate, " +
            " app_name,os " +
            " FROM " +
            " tencent_report_data_main " +
            " WHERE " +
            " date =  #{dateStr} " +
            " GROUP BY date,app_name,os"})
    List<TouFangAllOfflineEntity> queryTecentData(@Param("dateStr")String dateStr);

}
