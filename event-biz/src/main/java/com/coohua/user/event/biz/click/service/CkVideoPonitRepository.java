package com.coohua.user.event.biz.click.service;

import com.coohua.user.event.biz.click.entity.VideoAdReportEntitiy;
import com.coohua.user.event.biz.dc.entity.WithdrawOrderDetail;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.stereotype.Component;
import ru.yandex.clickhouse.ClickHouseDataSource;
import ru.yandex.clickhouse.settings.ClickHouseProperties;

import javax.annotation.PreDestroy;
import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.SQLException;
import java.util.List;

/**
 * 暂时不支持并发
 */
@Slf4j
@Component
public class CkVideoPonitRepository implements InitializingBean {

    private static final String USER = "default";
    private static final String PASSWORD = "Phw7a7A4";
    private static final String URL = "*********************************";
    private ClickHouseDataSource dataSource;
    private Connection connection;
    private PreparedStatement ps;
    public int batchCounter = 0;
    private static final String INSERT_AD_POINT = "INSERT INTO ods.ad_point_local (logday,dsp,price,app_id,time,call_time,device_id,user_id,os,channel,ad_id,uri) values (?,?,?,?,?,?,?,?,?,?,?,?)";

    @PreDestroy
    public void destroy() {
        if (ps != null) {
            try {
                ps.close();
            } catch (SQLException throwables) {
                log.error("", throwables);
            }
        }

        if (connection != null) {
            try {
                connection.close();
            } catch (SQLException throwables) {
                log.error("", throwables);
            }
        }

    }

    @Override
    public void afterPropertiesSet() throws Exception {
        Class.forName("ru.yandex.clickhouse.ClickHouseDriver");
        ClickHouseProperties properties = new ClickHouseProperties();
        properties.setUser(USER);
        properties.setPassword(PASSWORD);
        dataSource = new ClickHouseDataSource(URL, properties);
    }

    public void batchSave(List<VideoAdReportEntitiy> videoAdReportEntitiys) throws SQLException {
        if (connection == null) {
            connection = dataSource.getConnection();
        }
        if (ps == null) {
            ps = connection.prepareStatement(INSERT_AD_POINT);
        }
        for (VideoAdReportEntitiy videoAdReportEntitiy : videoAdReportEntitiys) {
            addToBatch(videoAdReportEntitiy);
        }
        long begin = System.currentTimeMillis();
        ps.executeBatch();
        log.info("[CK]保存数据成功! batchSize:{}, cost:{}mms", batchCounter, System.currentTimeMillis() - begin);
        batchCounter = 0;
    }

    public synchronized void executeBatch() throws SQLException {
        if (ps != null) {
            long begin = System.currentTimeMillis();
            ps.executeBatch();
            log.info("[CK]保存数据成功! batchSize:{}, cost:{}mms", batchCounter, System.currentTimeMillis() - begin);
            batchCounter = 0;
        }
    }

    public synchronized  void addToBatch(VideoAdReportEntitiy videoAdReportEntitiy) throws SQLException {
        if (connection == null) {
            connection = dataSource.getConnection();
        }
        if (ps == null) {
            ps = connection.prepareStatement(INSERT_AD_POINT);
        }
        int colIndex = 1;
        ps.setDate(colIndex++, videoAdReportEntitiy.getCkLogDay());
        ps.setString(colIndex++, videoAdReportEntitiy.getDsp());
        ps.setString(colIndex++, videoAdReportEntitiy.getPrice());
        ps.setInt(colIndex++,videoAdReportEntitiy.getAppId());
        ps.setLong(colIndex++, videoAdReportEntitiy.getTimestamp());
        ps.setTimestamp(colIndex++, videoAdReportEntitiy.getCallTime());
        ps.setString(colIndex++, videoAdReportEntitiy.getDeviceId());
        ps.setString(colIndex++, videoAdReportEntitiy.getUserId());
        ps.setString(colIndex++, videoAdReportEntitiy.getOs());
        ps.setString(colIndex++,videoAdReportEntitiy.getChannel());
        ps.setString(colIndex++,videoAdReportEntitiy.getAd_id());
        ps.setString(colIndex++,videoAdReportEntitiy.getUri());
        ps.addBatch();
        batchCounter++;
    }

}
