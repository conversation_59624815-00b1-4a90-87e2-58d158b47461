package com.coohua.user.event.biz.dc.service;

import com.alibaba.fastjson.JSON;
import com.coohua.user.event.biz.dc.dto.RealtimeCsjRulesDto;
import com.coohua.user.event.biz.dc.entity.RealtimeCsjRules;
import com.coohua.user.event.biz.dc.entity.RealtimeRule;
import com.coohua.user.event.biz.dc.mapper.RealtimeCsjRulesMapper;
import com.coohua.user.event.biz.util.DateUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2022/10/11
 */
@Slf4j
@Service
public class RealtimeCsjRuleService {

    @Resource
    private RealtimeCsjRulesMapper realtimeCsjRulesMapper;

    public List<RealtimeCsjRulesDto> queryRealTimeRules(){
        List<RealtimeCsjRules> realtimeCsjRules = realtimeCsjRulesMapper.queryList();
        return realtimeCsjRules.stream()
                .map(this::convert)
                .collect(Collectors.toList());
    }

    private RealtimeCsjRulesDto convert(RealtimeCsjRules realtimeCsjRules){
        RealtimeCsjRulesDto dto = new RealtimeCsjRulesDto();
        dto.setId(realtimeCsjRules.getId());
        dto.setName(realtimeCsjRules.getName());
        dto.setCsjRate(realtimeCsjRules.getCsjRate());
        dto.setPv(realtimeCsjRules.getPv());
        dto.setUv(realtimeCsjRules.getUv());
        dto.setAvgEcpm(realtimeCsjRules.getAvgEcpm());
        dto.setIncludeType(realtimeCsjRules.getIncludeType());
        dto.setModelType(realtimeCsjRules.getModelType());
        dto.setChannelStart(realtimeCsjRules.getChannelStart());
        dto.setCreateTime(DateUtil.dateToStringWithTime(realtimeCsjRules.getCreateTime()));
        dto.setUpdateTime(DateUtil.dateToStringWithTime(realtimeCsjRules.getUpdateTime()));
        dto.setActionTask(JSON.parseArray(realtimeCsjRules.getActionTask(), RealtimeCsjRulesDto.ActionLimit.class));
        return dto;
    }

    private RealtimeCsjRules convert(RealtimeCsjRulesDto realtimeCsjRulesDto){
        RealtimeCsjRules realtimeCsjRules = new RealtimeCsjRules();
        realtimeCsjRules.setId(realtimeCsjRulesDto.getId());
        realtimeCsjRules.setName(realtimeCsjRulesDto.getName());
        realtimeCsjRules.setCsjRate(realtimeCsjRulesDto.getCsjRate());
        realtimeCsjRules.setPv(realtimeCsjRulesDto.getPv());
        realtimeCsjRules.setUv(realtimeCsjRulesDto.getUv());
        realtimeCsjRules.setAvgEcpm(realtimeCsjRulesDto.getAvgEcpm());
        realtimeCsjRules.setUpdateTime(new Date());
        realtimeCsjRules.setIncludeType(realtimeCsjRulesDto.getIncludeType());
        realtimeCsjRules.setModelType(realtimeCsjRulesDto.getModelType());
        realtimeCsjRules.setChannelStart(realtimeCsjRulesDto.getChannelStart());
        realtimeCsjRules.setActionTask(JSON.toJSONString(realtimeCsjRulesDto.getActionTask()));
        return realtimeCsjRules;
    }

    public void updateRealtimeRules(RealtimeCsjRulesDto realtimeCsjRulesDto){
        RealtimeCsjRules realtimeCsjRules = convert(realtimeCsjRulesDto);
        Integer count = realtimeCsjRulesMapper.updateRules(realtimeCsjRules);
        if (count > 0){
            log.info("已更新配置 {} ",realtimeCsjRulesDto.getId());
        }
    }
}
