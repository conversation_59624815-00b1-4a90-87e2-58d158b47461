package com.coohua.user.event.biz.dc.service;

import com.coohua.user.event.biz.dc.entity.RiskConfigEntity;
import com.coohua.user.event.biz.dc.mapper.RiskConfigMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2021/11/6
 */
@Slf4j
@Service
public class RiskConfigService {

    @Resource
    private RiskConfigMapper riskConfigMapper;

    public void saveOrUpdate(Integer product, Integer os,List<RiskConfigEntity> riskConfigEntities){
        // 查询所有配置
        Map<Integer,RiskConfigEntity> alreadyMap = this.queryConfig(product,os);
        List<RiskConfigEntity> insertList = new ArrayList<>();
        List<RiskConfigEntity> updateList = new ArrayList<>();

        for (RiskConfigEntity entity: riskConfigEntities){
            RiskConfigEntity alEntity = alreadyMap.get(entity.getRuleId());
            if (alEntity != null){
                if (!alEntity.getScore().equals(entity.getScore())) {
                    alEntity.setScore(entity.getScore());
                    alEntity.setUpdateTime(entity.getUpdateTime());
                    updateList.add(alEntity);
                }
            }else {
                insertList.add(entity);
            }
        }
        if (insertList.size() > 0){
            Integer count = riskConfigMapper.batchInsert(insertList);
            log.info("本次新写入 {}个",count);
        }
        if (updateList.size() > 0){
            updateConfig(updateList);
        }
    }

    private void updateConfig(List<RiskConfigEntity> updateList){
        updateList.forEach(riskConfigMapper::updateConfig);
    }

    public Map<Integer,RiskConfigEntity> queryConfig(Integer product, Integer os){
        return riskConfigMapper.queryProductAndOs(product,os)
                .stream()
                .collect(Collectors.toMap(RiskConfigEntity::getRuleId,r->r,(r1,r2) -> r2));
    }
}
