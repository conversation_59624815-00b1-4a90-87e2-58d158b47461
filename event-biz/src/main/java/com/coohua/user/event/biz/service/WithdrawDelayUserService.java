package com.coohua.user.event.biz.service;

import com.alibaba.fastjson.JSON;
import com.coohua.user.event.biz.click.entity.SkipAdEntity;
import com.coohua.user.event.biz.click.entity.WithdrawExChannelAndModel;
import com.coohua.user.event.biz.click.service.ClickHouseService;
import com.coohua.user.event.biz.util.DateUtil;
import com.coohua.user.event.biz.util.Lists;
import com.coohua.user.event.biz.util.RedisKeyConstants;
import com.coohua.user.event.biz.util.Strings;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;
import top.zrbcool.pepper.boot.jediscluster.JedisClusterClient;
import top.zrbcool.pepper.boot.jediscluster.JedisClusterClientRefer;

import javax.annotation.PostConstruct;
import java.util.*;


/**
 * <AUTHOR>
 * @since 2023/9/20
 */
@Slf4j
@Service
public class WithdrawDelayUserService {

    @Autowired
    private ClickHouseService clickHouseService;
    @JedisClusterClientRefer(namespace = "user-event")
    private JedisClusterClient userEventJedisClusterClient;

    private static final String WITHDRAW_DELAY_USER = "user:withdraw:delay:userid:";

    private List<SkipAdEntity> withdrawDelayUserIds = new ArrayList<>();


    public void refreshToRedisWithdrawDelayUser(){
        log.info("==> Start Refresh Withdraw CsjDelayUser ..");
        List<SkipAdEntity> csjIncomeDelayUser =  clickHouseService.queryCsjIncomeDelayUser();
        if (Lists.noEmpty(csjIncomeDelayUser)){
            String result = JSON.toJSONString(csjIncomeDelayUser);
            log.info("==> Withdraw Current Withdraw CsjDelayUser:{}", result);
            userEventJedisClusterClient.setex(WITHDRAW_DELAY_USER+ DateUtil.dateToString(new Date()), RedisKeyConstants.EXPIRE_ONE_DAYS, result);
        }
        log.info("==> End Refresh Withdraw CsjDelayUser ..");
    }


    /*@PostConstruct
    @Scheduled(cron = "0 0/1 * * * ?")
    public void refreshExChannel(){
        log.info("==> Start Refresh Withdraw CsjDelayUser Config ..");
        String result = userEventJedisClusterClient.get(WITHDRAW_DELAY_USER+ DateUtil.dateToString(new Date()));
        if (Strings.noEmpty(result)){
            List<SkipAdEntity> res = JSON.parseArray(result,SkipAdEntity.class);
            log.info("==> Withdraw Current get Withdraw CsjDelayUser:{}", res);
            withdrawDelayUserIds = res;
        }else {
            withdrawDelayUserIds = new ArrayList<>();
        }
        log.info("==> End Refresh Withdraw DelayUser Config ..");
    }

    public boolean isDelayUser(String product,String userId){
        if (CollectionUtils.isEmpty(withdrawDelayUserIds)) {
            return false;
        }
        return withdrawDelayUserIds.stream().anyMatch(r -> Objects.equals(r.getProduct(),product) && Objects.equals(r.getUserid(),userId));
    }*/


}
