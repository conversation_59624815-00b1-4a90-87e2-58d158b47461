package com.coohua.user.event.biz.cache;

import com.coohua.bp.account.remote.api.AccountRPC;
import com.coohua.bp.account.remote.dto.AccountDTO;
import com.coohua.bp.user.remote.api.AppRPC;
import com.coohua.bp.user.remote.dto.WechatAppDTO;
import com.coohua.user.event.biz.enums.CacheKeyConstants;
import com.github.benmanes.caffeine.cache.CacheLoader;
import com.github.benmanes.caffeine.cache.Caffeine;
import com.github.benmanes.caffeine.cache.LoadingCache;
import com.github.benmanes.caffeine.cache.stats.CacheStats;
import com.weibo.api.motan.config.springsupport.annotation.MotanReferer;
import lombok.NonNull;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.context.annotation.Bean;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import javax.annotation.PostConstruct;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * (。≖ˇェˇ≖。)
 *
 * @author: zkk
 * DateTime: 2020/9/4 15:16
 */

@Component
@Slf4j
@ConditionalOnProperty(name = "spring.application.name",havingValue = "user-event")
public class UserCreateTimeCacheComponent {

	@MotanReferer(basicReferer = "bp-accountBasicRefererConfigBean")
	private AccountRPC accountRPC;

//	@MotanReferer(basicReferer = "bp-userBasicRefererConfigBean")
//	private AppRPC appRPC;

	/**
	 * 缓存用户创建信息的 过期时间 默认6小时过期
	 */
	@Value("${user.create.expire:6}")
	private int userCreateExpire;


	/**
	 * 微信pkg配置
	 */
//	private Map<Long, WechatAppDTO> wechatAppDTOMap;
//
//	@PostConstruct
//	@Scheduled(cron = "0 0/5 * * * ?")
//	private void init() {
//		List<WechatAppDTO> wechatAppDTOList = appRPC.getAllWechatApp();
//		wechatAppDTOMap = wechatAppDTOList.stream().collect(Collectors.toMap(wechatAppDTO -> wechatAppDTO.getId(),
//				wechatAppDTO -> wechatAppDTO, (oldValue, newValue) -> newValue));
//	}
	/**
	 * 缓存100万个用户创建时间
	 *
	 * @return
	 */
	@Bean(name = "userCreateTimeCache")
	public LoadingCache<String, Long> initCafeeCache() {
		Caffeine<Object, Object> cacheBuilder = Caffeine.newBuilder();
		cacheBuilder.maximumSize(400000);
		// 写入后6小时过期
		cacheBuilder.expireAfterWrite(userCreateExpire, TimeUnit.HOURS);
		LoadingCache<String, Long> cache = cacheBuilder.recordStats().build(key -> {
			try {
				List<String> pkgUser = CacheKeyConstants.SPLITTER_LINE.splitToList(key);
				if (CollectionUtils.isEmpty(pkgUser) || pkgUser.size() < 2) {
					return CacheKeyConstants.DEFAULT_LONG_EMPTY;
				}
				String appId = pkgUser.get(0);
				String userId = pkgUser.get(1);
				if(StringUtils.isEmpty(appId) || StringUtils.isEmpty(userId)){
					return CacheKeyConstants.DEFAULT_LONG_EMPTY;
				}
				AccountDTO accountDTO = accountRPC.getAccountInfo(Long.valueOf(userId), Long.valueOf(appId));

				if (accountDTO != null) {
					return accountDTO.getCreateTime();
				}
			} catch (Exception e) {
				log.error("user createTime error ! key={}", key, e);
			}
			return CacheKeyConstants.DEFAULT_LONG_EMPTY;
		});
		return cache;
	}

}
