package com.coohua.user.event.biz.dc.mapper;

import com.coohua.user.event.biz.dc.entity.CdpAdData;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * <p>
 *  Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2023-06-15
 */
public interface CdpAdDataMapper extends BaseMapper<CdpAdData> {

    @Select({"select * from ads.cdp_ad_data where logday =#{logday}"})
    List<CdpAdData> queryByLogday(@Param("logday")String logday);
}
