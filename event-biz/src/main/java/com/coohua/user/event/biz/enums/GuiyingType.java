package com.coohua.user.event.biz.enums;

public enum GuiyingType {
    oaid("adOaid", "adOaid"),//	        用户id
    imei("imei", "imei"),//	        用户id
    deive("adDeive", "adDeive"),//	         30天内第一次打开APP
    androidId("androidId", "androidId"), // idfa加md5
    mac("mac", "mac"),
    idfa("idfa", "idfa"),////   用户点击广告后注册成为APP的新用户
    ipua("ipua", "ipua"),
    openid("openid", "openid"),
    caid("caid", "caid"),
    uid("uid", "uid"),
    iunod("iunod", "iunod"),
    ip("ip", "ip"),
    ipmd("ipmd", "ipmd"),
    sdk("sdk", "sdk"), //头条实时归因类型
    defaultGy("defaultGy", "defaultGy"),
    packageGy("packageGy", "packageGy"),
    ;
    public String value;
    public String name;

    GuiyingType(String value, String name) {
        this.value = value;
        this.name = name;
    }

    public static GuiyingType getDspType(String value) {
        if (value != null) {
            GuiyingType[] otypes = GuiyingType.values();
            for (GuiyingType memberType : otypes) {
                if (value.equals(memberType.value)) {
                    return memberType;
                }
            }
        }
        return null;
    }
}
