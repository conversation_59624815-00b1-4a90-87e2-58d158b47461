package com.coohua.user.event.biz.util;

import cn.hutool.core.net.NetUtil;

import java.util.regex.Matcher;
import java.util.regex.Pattern;

public class IpUtil {

    private static final Pattern IPV4_PATTERN = Pattern.compile(
            "^([01]?\\d\\d?|2[0-4]\\d|25[0-5])\\." +
                    "([01]?\\d\\d?|2[0-4]\\d|25[0-5])\\." +
                    "([01]?\\d\\d?|2[0-4]\\d|25[0-5])\\." +
                    "([01]?\\d\\d?|2[0-4]\\d|25[0-5])$"
    );

    public static boolean isIpMatched(String inputIp, String pattern) {
        if (isIPv4(pattern)) {
            // 精确匹配
            return inputIp.equals(pattern);
        } else if (pattern.contains("*")) {
            // 通配符匹配
            String[] patternParts = pattern.split("\\.");
            String[] inputParts = inputIp.split("\\.");

            for (int i = 0; i < 4; i++) {
                if (!"*".equals(patternParts[i]) && !patternParts[i].equals(inputParts[i])) {
                    return false;
                }
            }

            return true;
        } else {
            // CIDR匹配
            return NetUtil.isInRange(inputIp, pattern);
        }
    }

    private static boolean isIPv4(String ip) {
        Matcher matcher = IPV4_PATTERN.matcher(ip);
        return matcher.matches();
    }
}
