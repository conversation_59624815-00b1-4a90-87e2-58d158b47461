package com.coohua.user.event.biz.enums;

/**
 * <AUTHOR>
 * @since 2021/9/3
 */
public enum  UserRiskEnums {
    NO_RISK(0,"无风险"),
    C1_RISK(3,"无风险-C1类型"),
    B1_<PERSON>IS<PERSON>(7,"中风险-B1类型"),
    B_RISK(8,"中风险-B类型"),
    A_RISK(9,"高风险-A类型"),
    ;

    private Integer level;
    private String desc;

    UserRiskEnums(Integer level, String desc) {
        this.level = level;
        this.desc = desc;
    }

    public static UserRiskEnums level(Integer level){
        for (UserRiskEnums enums:UserRiskEnums.values()){
            if (enums.getLevel().equals(level)){
                return enums;
            }
        }
        return UserRiskEnums.NO_RISK;
    }

    public Integer getLevel() {
        return level;
    }

    public void setLevel(Integer level) {
        this.level = level;
    }

    public String getDesc() {
        return desc;
    }

    public void setDesc(String desc) {
        this.desc = desc;
    }
}
