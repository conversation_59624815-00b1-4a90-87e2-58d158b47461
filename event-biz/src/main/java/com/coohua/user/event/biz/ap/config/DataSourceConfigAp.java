package com.coohua.user.event.biz.ap.config;

import com.alibaba.druid.spring.boot.autoconfigure.DruidDataSourceBuilder;
import com.baomidou.mybatisplus.extension.plugins.PaginationInterceptor;
import com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean;
import lombok.extern.slf4j.Slf4j;
import org.apache.ibatis.session.SqlSessionFactory;
import org.mybatis.spring.annotation.MapperScan;
import org.mybatis.spring.mapper.MapperScannerConfigurer;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.boot.autoconfigure.condition.ConditionalOnBean;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.io.ClassPathResource;
import org.springframework.core.io.support.PathMatchingResourcePatternResolver;

import javax.sql.DataSource;
import java.sql.SQLException;

/**
 * <AUTHOR>
 * @since 2021/8/2
 */
@Slf4j
@Configuration
@MapperScan(basePackages = {"com.coohua.user.event.biz.ap.mapper"})
public class DataSourceConfigAp {

    @Bean(name = "datasourceBPAP")
    @ConfigurationProperties(prefix = "spring.datasource.ap")
    public DataSource dataSource() throws SQLException {
        return DruidDataSourceBuilder.create().build();
    }

    @Bean(name = "datasourceBPAPSqlSessionFactoryBean")
    @ConditionalOnBean(name = "datasourceBPAP")
    public SqlSessionFactory sqlSessionFactoryBean(@Qualifier("datasourceBPAP") DataSource dataSource) throws Exception {
        MybatisSqlSessionFactoryBean mybatisSqlSessionFactoryBean = new MybatisSqlSessionFactoryBean();
        mybatisSqlSessionFactoryBean.setDataSource(dataSource);
        mybatisSqlSessionFactoryBean.setConfigLocation(new ClassPathResource("config/mybatis_config.xml"));
        mybatisSqlSessionFactoryBean.setMapperLocations(new PathMatchingResourcePatternResolver().getResources("classpath*:mapper/ap/*.xml"));
        PaginationInterceptor paginationInterceptor = new PaginationInterceptor();
        paginationInterceptor.setDialectType("mysql");
        paginationInterceptor.setLimit(-1);

        mybatisSqlSessionFactoryBean.setPlugins(paginationInterceptor);
        return mybatisSqlSessionFactoryBean.getObject();
    }

    @Bean(name = "BPAPMapperScannerConfigurer")
    @ConditionalOnBean(name = "datasourceBPAPSqlSessionFactoryBean")
    public MapperScannerConfigurer mapperScannerConfigurer(){
        MapperScannerConfigurer configurer = new MapperScannerConfigurer();
        configurer.setBasePackage("com.coohua.user.event.biz.ap.mapper");
        configurer.setSqlSessionFactoryBeanName("datasourceBPAPSqlSessionFactoryBean");
        return configurer;
    }


}
