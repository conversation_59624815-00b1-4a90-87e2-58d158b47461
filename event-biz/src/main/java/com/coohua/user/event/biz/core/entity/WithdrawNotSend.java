package com.coohua.user.event.biz.core.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.Date;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR>
 * @since 2023-09-25
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value="WithdrawNotSend对象", description="")
public class WithdrawNotSend implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    private Integer appId;

    private Long userId;

    private String orderNo;

    @ApiModelProperty(value = "0-不发送 1-可以发送")
    private Integer canSend;

    private Integer amount;

    private String remark;

    private Date createTime;

    private Date updateTime;

    private String registerChannel;

    private String product;

    private String productName;

    @ApiModelProperty(value = "0-未退款 1-直接已退款")
    private Integer isRefund;

    public WithdrawNotSend() {
    }

    public WithdrawNotSend(String product, Long userId, String orderNo) {
        this.product = product;
        this.userId = userId;
        this.orderNo = orderNo;
    }
}
