package com.coohua.user.event.biz.service;

import com.alibaba.fastjson.JSON;
import com.coohua.user.event.biz.click.entity.WithdrawExChannel;
import com.coohua.user.event.biz.click.entity.WithdrawExChannelAndModel;
import com.coohua.user.event.biz.click.service.ClickHouseService;
import com.coohua.user.event.biz.util.DateUtil;
import com.coohua.user.event.biz.util.Lists;
import com.coohua.user.event.biz.util.Strings;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;
import top.zrbcool.pepper.boot.jediscluster.JedisClusterClient;
import top.zrbcool.pepper.boot.jediscluster.JedisClusterClientRefer;

import javax.annotation.PostConstruct;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @since 2023/9/20
 */
@Slf4j
@Service
public class WithdrawExChannelService {

    @Autowired
    private ClickHouseService clickHouseService;
    @JedisClusterClientRefer(namespace = "user-event")
    private JedisClusterClient userEventJedisClusterClient;

    private static final String EX_CHANNEL = "user:withdraw:ex:channel:";

    private List<WithdrawExChannel> exChannels = new ArrayList<>();


    public void refreshToRedisExChannel(){
        log.info("==> Start Refresh Withdraw Channel ..");
        List<WithdrawExChannel> exChannels =  clickHouseService.queryWithdrawExChannel();
        if (Lists.noEmpty(exChannels)){
            String result = JSON.toJSONString(exChannels);
            log.info("==> Withdraw Current Ex Channel:{}", result);
            userEventJedisClusterClient.set(EX_CHANNEL+ DateUtil.dateToString(new Date()),result);
        }
        log.info("==> End Refresh Withdraw Channel ..");
    }

    @PostConstruct
    @Scheduled(cron = "0 0/5 * * * ?")
    public void refreshExChannel(){
        log.info("==> Start Refresh Withdraw Channel Config ..");
        String result = userEventJedisClusterClient.get(EX_CHANNEL+ DateUtil.dateToString(new Date()));
        if (Strings.noEmpty(result)){
            List<WithdrawExChannel> res = JSON.parseArray(result,WithdrawExChannel.class);
            log.info("==> Withdraw Current get Ex Channel:{}", res);
            exChannels = res;
        }else {
            exChannels = new ArrayList<>();
        }

        log.info("==> End Refresh Withdraw Channel Config ..");
    }

    public boolean isExChannel(String product,String channel){
        if (CollectionUtils.isEmpty(exChannels)) {
            return false;
        } else {
            return exChannels.stream().anyMatch(r -> Objects.equals(r.getProduct(), product) && Objects.equals(r.getChannel(),channel));
        }
    }




}
