package com.coohua.user.event.biz.dc.dto;

import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2023/7/6
 */
@Data
public class ChannelConfig {
    // ks/gdt为前缀匹配模式
    private String channel;
    private List<String> cityList;
    // 锁城市列表模式 1-全锁所有城市 2-全部城市放开 3-选择城市列表
    private Integer lockCityType;
    // 全锁APP版本
    private List<String> allLockVersion;
    // 锁OCPC模式 1-不识别OCPC模式全锁 2-OCPC用户通过 3-无所谓
    private Integer lockOcpcType;
}
