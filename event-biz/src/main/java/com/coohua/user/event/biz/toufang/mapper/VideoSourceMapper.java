package com.coohua.user.event.biz.toufang.mapper;

import com.coohua.user.event.biz.toufang.entity.VideoBaseEntity;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2020/11/5
 */
public interface VideoSourceMapper {

    @Select({"SELECT " +
            " vi.id as video_id, " +
            " vi.video_name, " +
            " tri.title, " +
            " vi.app_name, " +
            " vi.edit_time, " +
            " vi.director, " +
            " vi.editor, " +
            " vi.actor, " +
            " vi.videographer, " +
            " vi.e_tag, " +
            " vi.video_url, " +
            " vi.video_property, " +
            " vi.online_time, " +
            " vi.delivery_time " +
            " FROM " +
            " video_information vi " +
            " LEFT JOIN toutiao_report_ideaday tri ON  vi.app_name = tri.app_name " +
            " WHERE " +
            " tri.video_source = '自家' " +
            " AND tri.video_url IS NOT NULL " +
            " AND vi.video_url IS NOT NULL " +
            " AND vi.video_url != '' and  vi.id BETWEEN #{start} and #{end} GROUP BY tri.title,vi.id order by vi.id"})
    List<VideoBaseEntity> queryVideoBaseList(@Param("start")Integer start,@Param("end")Integer end);


    @Select({"select id from video_information order by id desc limit 1"})
    Integer selectVideoMaxId();
}
