package com.coohua.user.event.biz.service;

import com.alibaba.fastjson.JSON;
import com.aliyun.openservices.shade.org.apache.commons.lang3.time.DateUtils;
import com.coohua.bp.user.remote.dto.AppDTO;
import com.coohua.user.event.biz.click.entity.ProductEntity;
import com.coohua.user.event.biz.click.entity.VideoAdReportEntitiy;
import com.coohua.user.event.biz.enums.GrayType;
import com.coohua.user.event.biz.service.bean.UserActive;
import com.coohua.user.event.biz.service.rsp.BatchQueryResponse;
import com.coohua.user.event.biz.service.rsp.CheckInnerRsp;
import com.coohua.user.event.biz.util.AppConfig;
import com.coohua.user.event.biz.util.DateUtil;
import com.coohua.user.event.biz.util.Lists;
import com.coohua.user.event.biz.util.Strings;
import com.ctrip.framework.apollo.spring.annotation.ApolloJsonValue;
import lombok.extern.slf4j.Slf4j;
import org.checkerframework.checker.units.qual.C;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;
import redis.clients.jedis.Jedis;
import redis.clients.jedis.Pipeline;
import top.zrbcool.pepper.boot.jediscluster.JedisClusterClient;
import top.zrbcool.pepper.boot.jediscluster.JedisClusterClientRefer;

import javax.annotation.PostConstruct;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2023/8/28
 */
@Slf4j
@Service
public class UserAdExposureCheckService {

    public static final String USER_EXPOSURE_VIDEO_TIME = "user:exposure:video:time:";
    // 只有穿优快
    public static final String USER_EXPOSURE_MAIN_VIDEO_TIME = "user:exposure:main:video:time:";
    public static final String USER_EXPOSURE_MAIN_THIRD = "{main:third}:user:exposure:main:third:time:";
    public static final String PRE_FIX = "{main:third}:";
    public static final String COLON = ":";



    @Autowired
    private TFUserService tfUserService;
    @JedisClusterClientRefer(namespace = "ap-cluster")
    private JedisClusterClient apJedisClusterClient;
    @Autowired
    private UserGrayService userGrayService;
    @Autowired
    private InnerPullService innerPullService;
    @Autowired
    private HBaseUserAdViewService hBaseUserAdViewService;

    @ApolloJsonValue("${join.check.inner.app:[1010]}")
    private List<Long> joinAppList;

    @ApolloJsonValue("${tx.check.product.list:[\"kxhy2\",\"tyrj3\"]}")
    private List<String> checkTxProductList;


    public boolean isExposureGapThird(Long appId,Long userId){
        ProductEntity productEntity = AppConfig.appIdMap.get(appId);
        if (productEntity == null){
            log.error("App Id {} {} not found Product.",appId,userId);
            return false;
        }
        // 切为设备归因
        if (tfUserService.isUseDeviceGui(productEntity.getProduct())) {
            try {
                UserActive userActive = tfUserService.queryUserDeviceGui(productEntity.getProduct(), userId.toString(), appId);
                if (userActive == null || userActive.isOcpc()) return false;
            } catch (Exception e) {
                log.error("isExposureGapThird查询用户设备归因失败 {} {}", appId, userId, e);
            }
        } else {
            List<String> ntfList =  tfUserService.queryNtfUser(productEntity.getProduct(),
                    Collections.singletonList(userId.toString()),"withdraw-query");
            if (ntfList.size() == 0){
                return false;
            }
        }

        // 查询GAP
        String keyOwn = buildExposureVideoMain(userId,appId);
        String keyThird = buildExposureVideoMainThird(Math.toIntExact(appId),userId.toString());
        String resultOwn = apJedisClusterClient.get(keyOwn);
        String resultThird = apJedisClusterClient.get(keyThird);

        Float own = Strings.noEmpty(resultOwn) ? Float.parseFloat(resultOwn) : 0f;
        Float third = Strings.noEmpty(resultThird) ? Float.parseFloat(resultThird) : 0f;

        if (own < 100f) return false;

        return own > third && third / own < 0.4f;
    }

    public boolean checkExitsThirdCallBack(Long appId, Long userId, Date[] dates){
        for(Date indexDate : dates){
            String yesterThird = buildExposureVideoMainThird(Math.toIntExact(appId),userId.toString(), indexDate);
            String yesterResultThird = apJedisClusterClient.get(yesterThird);
            Float yesterThirdRes = Strings.noEmpty(yesterResultThird) ? Float.parseFloat(yesterResultThird) : 0f;
            if(yesterThirdRes > 0){
                return  true;
            }
        }
        return false;
    }

    public boolean isNtfExposureMoreThan500(Long appId,Long userId){
        String userDayClickLimitKey = buildExposureVideo(userId, appId);
        String result = apJedisClusterClient.get(userDayClickLimitKey);
        if (Strings.noEmpty(result)){
            int ret = Integer.parseInt(result);
            if (ret > 500){
                ProductEntity productEntity = AppConfig.appIdMap.get(appId);
                if (productEntity == null){
                    log.error("App Id {} {} not fount Product.",appId,userId);
                }
                // 切为设备归因
                if (tfUserService.isUseDeviceGui(productEntity.getProduct())) {
                    try {
                        UserActive userActive = tfUserService.queryUserDeviceGui(productEntity.getProduct(), userId.toString(), appId);
                        if (tfUserService.isDeviceGuiLogEnabled) {
                            log.info("UserAdExposureCheckService设备归因 {} {} {}", productEntity.getProduct(), userId, userActive);
                        }
                        if (userActive == null || !userActive.isOcpc()) {
                            userGrayService.grayUserAsync(GrayType.USER, Collections.singletonList(userId.toString()),
                                    "非投放用户曝光500-提现拉黑",productEntity.getProduct(),"android","realtime-withdraw");
                            return true;
                        }
                    } catch (Exception e) {
                        log.error("isNtfExposureMoreThan500查询用户设备归因失败 {} {}",appId,userId, e);
                        return false;
                    }
                } else {
                    List<String> ntfList =  tfUserService.queryNtfUser(productEntity.getProduct(),
                            Collections.singletonList(userId.toString()),"withdraw-query");
                    // 用户为非投放用户 直接封禁
                    if (ntfList.size() > 0){
                        userGrayService.grayUserAsync(GrayType.USER, Collections.singletonList(userId.toString()),
                                "非投放用户曝光500-提现拉黑",productEntity.getProduct(),"android","realtime-withdraw");
                        return true;
                    }
                }

                // 若用户为投放用户 限制次数2k
                if (ret > 2000){
                    userGrayService.grayUserAsync(GrayType.USER, Collections.singletonList(userId.toString()),
                            "投放用户曝光2000-提现拉黑",productEntity.getProduct(),"android","realtime-withdraw");
                    return true;
                }
                return false;
            }
        }
        return false;
    }

    public CheckInnerRsp checkInnerPull(Long appId,Long userId,Integer amount,Integer totalAmount){
        CheckInnerRsp checkInnerRsp = new CheckInnerRsp();
        checkInnerRsp.setReject(false);
        checkInnerRsp.setNotFundActive(false);
        // 是否七组
        ProductEntity  productEntity = AppConfig.appIdMap.get(appId);
        if (productEntity == null){
            log.error("App Id {} {} not fount Product.",appId,userId);
            return checkInnerRsp;
        }
        //TODO 临时屏蔽七组
        if("项目七组".equals(productEntity.getProductGroup())){
            return checkInnerRsp;
        }

        if (!"项目七组".equals(productEntity.getProductGroup()) && !"项目一组".equals(productEntity.getProductGroup())){
            return checkInnerRsp;
        }

        if ("项目一组".equals(productEntity.getProductGroup()) && !checkTxProductList.contains(productEntity.getProduct())){
            return checkInnerRsp;
        }

        UserActive userActive = tfUserService.queryUserActiveIfNoFundQueryRpc(productEntity.getProduct(),userId.toString(),appId);
        if (userActive == null){
            log.info("未查询到 USER_ACTIVE {} {} 审核暂时不通过",appId,userId);
            checkInnerRsp.setReject(true);
            checkInnerRsp.setNotFundActive(true);
            checkInnerRsp.setReason("未查询到UserActive");
            return checkInnerRsp;
        }

        if ("ios".equalsIgnoreCase(userActive.getOs())){
            log.info("查询到 IOS {} {} 通过",appId,userId);
            return checkInnerRsp;
        }

        Date now = new Date();
        Date dayBegin = DateUtil.stringToDate(DateUtil.dateToString(now) + " 00:00:00",DateUtil.COMMON_TIME_FORMAT);
        if (dayBegin.after(userActive.getCreateTime())) {
            log.info("{} {} {} 非本日新增 跳过检测",appId,userId,DateUtil.dateToStringWithTime(userActive.getCreateTime()));
            return checkInnerRsp;
        }

        List<String> queryArpuUserList = new ArrayList<>();
        queryArpuUserList.add(userId.toString());
        // 查询所有徒弟
        List<String> innerPull = innerPullService.queryByMasterId(appId.intValue(),userId.toString());
        if (Lists.noEmpty(innerPull)){
            queryArpuUserList.addAll(innerPull);
        }

        List<Long> queryUserList = queryArpuUserList.stream().map(Long::valueOf).collect(Collectors.toList());
        log.info("中台提现核查Arpu {} {} {}",appId,userId,JSON.toJSONString(queryUserList));

        // 总Arpu==>
        Map<Long, BatchQueryResponse> responseMap = hBaseUserAdViewService.batchQuery(queryUserList,"android",appId.intValue());
        Map<Long, BatchQueryResponse> harmonyMap = hBaseUserAdViewService.batchQuery(queryUserList,"harmonyos",appId.intValue());
        int ecpm = 0;
        for (BatchQueryResponse response : responseMap.values()){
            ecpm = ecpm + response.getEcpmSum();
        }
        for (BatchQueryResponse response : harmonyMap.values()){
            ecpm = ecpm + response.getEcpmSum();
        }

        float totalWithdraw = totalAmount /100f;
        float totalArpu = ecpm /1000f;
        // 命中规则
        if (totalWithdraw > totalArpu){
            log.info("用户 {} {} 提现 {} 内拉新总Arpu {} 提现超过Arpu 拒绝提现",appId,userId,totalWithdraw,totalArpu);
//            userGrayService.grayUser(GrayType.USER, Collections.singletonList(userId.toString()),
//                    "提现审核-七组提现超过Arpu拉黑",productEntity.getProduct(),"android",false,false);
            checkInnerRsp.setReject(true);
            checkInnerRsp.setArpu(totalArpu);
            checkInnerRsp.setWithdraw(totalWithdraw);
            return checkInnerRsp;
        }
        return checkInnerRsp;
    }


    public static String buildExposureVideo(Long userId, Long appId){
        Date now = new Date();
        String day = DateUtil.dateToString(now,DateUtil.ISO_DATE_FORMAT);
        return USER_EXPOSURE_VIDEO_TIME + day + COLON + appId + COLON + userId;
    }

    public static String buildExposureVideoMain(Long userId, Long appId){
        Date now = new Date();
        String day = DateUtil.dateToString(now,DateUtil.ISO_DATE_FORMAT);
        return USER_EXPOSURE_MAIN_VIDEO_TIME + day + COLON + appId + COLON + userId;
    }

    private String buildExposureVideoMainThird(int appId, String userId, Date yesterDay) {
        String day = DateUtil.dateToString(yesterDay, DateUtil.ISO_DATE_FORMAT);
        return USER_EXPOSURE_MAIN_THIRD + day + COLON + appId + COLON + userId;
    }

    public static String buildInitSelectMainThird(Integer appId,String userId, String dayS){
        return USER_EXPOSURE_MAIN_THIRD + dayS + COLON + appId + COLON + userId;
    }
    public static String buildExposureVideoMainThird(Integer appId,String userId){
        Date now = new Date();
        String day = DateUtil.dateToString(now,DateUtil.ISO_DATE_FORMAT);
        return USER_EXPOSURE_MAIN_THIRD + day + COLON + appId + COLON + userId;
    }

    public void batchSaveThird(List<String> flList){
        try (Jedis jedis = apJedisClusterClient.getResource(PRE_FIX)){
            List<VideoAdReportEntitiy> videoAdReportEntitiys = flList.stream()
                    .map(r -> JSON.parseObject(r,VideoAdReportEntitiy.class)).collect(Collectors.toList());

            Pipeline pipeline = jedis.pipelined();
            videoAdReportEntitiys.forEach(videoAdReportEntitiy -> {
                String key = buildExposureVideoMainThird(videoAdReportEntitiy.getAppId(),videoAdReportEntitiy.getUserId());
                pipeline.incr(key);
                pipeline.expire(key, 2 * 60 * 60 * 24);
            });
            pipeline.sync();
        }catch (Exception e){
            log.error("Batch SaveEx:",e);
        }
    }

    public void initRedis(List<Map<String,Object>> flList, String s){
        try (Jedis jedis = apJedisClusterClient.getResource(PRE_FIX)){

            Pipeline pipeline = jedis.pipelined();
            flList.forEach(videoAdReportEntitiy -> {
                String key = buildInitSelectMainThird(
                        Integer.valueOf(videoAdReportEntitiy.get("app_id").toString())
                        , videoAdReportEntitiy.get("user_id").toString()
                        , s
                );
//                System.out.print(" key : " + key);
//                System.out.println(" count : " + videoAdReportEntitiy.get("count").toString());
                pipeline.set(key, videoAdReportEntitiy.get("count").toString());
                pipeline.expire(key, 1 * 60 * 60 * 24);
            });
            pipeline.sync();
        }catch (Exception e){
            log.error("Batch SaveEx:",e);
        }
    }
}
