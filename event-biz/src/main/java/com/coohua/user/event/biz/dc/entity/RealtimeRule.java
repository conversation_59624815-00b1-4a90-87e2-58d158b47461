package com.coohua.user.event.biz.dc.entity;

import com.alibaba.fastjson.JSON;
import com.coohua.user.event.biz.dc.dto.ActionRuleDto;
import com.coohua.user.event.biz.util.Lists;
import lombok.Data;
import org.apache.directory.api.util.Strings;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2022/1/22
 */
@Data
public class RealtimeRule {

    private Integer id;
    private String product;
    private Integer state;
    private Integer ocpcType;
    private String channels;
    private String models;
    private String ips;
    private Date createTime;
    private Date updateTime;
    private String actionRule;
    private Integer actionType;
    private String ruleName;
    private Integer appId;
    private String productName;

    private Integer level;
    private String brands;

    private String sources;
    private Integer newUserType;

    public List<String> getChannelList(){
        if (Strings.isEmpty(this.channels)){
            return new ArrayList<>();
        }
        return Arrays.stream(this.channels.split(",")).filter(Strings::isNotEmpty).collect(Collectors.toList());
    }

    public List<String> getBrandList(){
        if (Strings.isEmpty(this.brands)){
            return new ArrayList<>();
        }
        return Arrays.stream(this.brands.split(",")).filter(Strings::isNotEmpty).collect(Collectors.toList());
    }

    public List<String> getSourceList(){
        if (Strings.isEmpty(this.sources)){
            return new ArrayList<>();
        }
        return Arrays.stream(this.sources.split(",")).filter(Strings::isNotEmpty).collect(Collectors.toList());
    }

    public boolean checkSources(String sourceTarget){
        List<String> source = getSourceList();
        if (source.size() > 0){
            return source.contains(sourceTarget);
        }
        return true;
    }

    public boolean checkBrand(String brand){
        List<String> source = getBrandList();
        if (source.size() > 0){
            return source.contains(brand);
        }
        return true;
    }

    public boolean checkChannel(String channel){
        List<String> source = getChannelList();
        if (source.size() > 0){
            return source.contains(channel);
        }
        return true;
    }

    public List<String> getModelList(){
        if (Strings.isEmpty(this.models)){
            return new ArrayList<>();
        }

        if (this.models.contains("iPhone")){
            return Arrays.stream(this.models.split("[|]")).filter(Strings::isNotEmpty).collect(Collectors.toList());

        }
        return Arrays.stream(this.models.split(",")).filter(Strings::isNotEmpty).collect(Collectors.toList());
    }

    public boolean checkModel(String model){
        List<String> source = getModelList();
        if (source.size() > 0){
            return source.contains(model);
        }
        return true;
    }

    public List<String> getIpList(){
        if (Strings.isEmpty(this.ips)){
            return new ArrayList<>();
        }
        return Arrays.stream(this.ips.split(",")).filter(Strings::isNotEmpty).collect(Collectors.toList());
    }

    public boolean checkIp(String ip){
        List<String> source = getIpList();
        if (source.size() > 0){
            for (String ipPre : source){
                if (ip.startsWith(ipPre)){
                    return true;
                }
            }
            // 若有ip列表 最终不符合 必须TK了
            return false;
        }
        return true;
    }

    public boolean checkAdType(String adType){
        List<Integer> platformLimit = getActionDto().getPlatformLimit();
        if (Lists.isEmpty(platformLimit) || platformLimit.contains(0)){
            return true;
        }
        return true;
    }

    public ActionRuleDto getActionDto(){
        if (Strings.isEmpty(this.actionRule)){
            return new ActionRuleDto();
        }
        ActionRuleDto actionRuleDto = JSON.parseObject(this.actionRule,ActionRuleDto.class);
        actionRuleDto.setChannels(this.channels);
        actionRuleDto.setName(this.ruleName);
        actionRuleDto.setModels(this.models);
        actionRuleDto.setBrands(this.brands);
        actionRuleDto.setOcpcType(this.ocpcType);
        actionRuleDto.setIps(this.ips);
        actionRuleDto.setSources(this.sources);
        actionRuleDto.setNewUserType(this.newUserType);
        actionRuleDto.setActionType(this.getActionType());
        return actionRuleDto;
    }
}
