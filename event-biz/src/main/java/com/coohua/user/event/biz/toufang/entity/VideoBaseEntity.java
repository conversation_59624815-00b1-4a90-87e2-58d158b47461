package com.coohua.user.event.biz.toufang.entity;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 * @since 2020/11/5
 */
@Data
public class VideoBaseEntity {

    @ApiModelProperty(value = "视频id")
    private Integer videoId;

    @ApiModelProperty(value = "视频名称")
    private String videoName;

    @ApiModelProperty(value = "产品名称")
    private String appName;

    @ApiModelProperty(value = "剪辑时间")
    private Date editTime;

    @ApiModelProperty(value = "编导")
    private String director;

    @ApiModelProperty(value = "剪辑")
    private String editor;

    @ApiModelProperty(value = "演员")
    private String actor;

    @ApiModelProperty(value = "摄像")
    private String videographer;

    @ApiModelProperty(value = "文件唯一约束")
    private String eTag;

    @ApiModelProperty(value = "视频链接")
    private String videoUrl;

    @ApiModelProperty(value = "属性，用于关联视频与计划关系")
    private String videoProperty;

    @ApiModelProperty(value = "上线时间")
    private Date onlineTime;

    @ApiModelProperty(value = "交付时间")
    private Date deliveryTime;

    @ApiModelProperty(value = "创意标题")
    private String title;
}
