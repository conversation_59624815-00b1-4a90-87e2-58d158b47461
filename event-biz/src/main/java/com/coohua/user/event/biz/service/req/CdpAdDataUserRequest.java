package com.coohua.user.event.biz.service.req;

import com.coohua.user.event.biz.dc.entity.CdpAdDataUser;
import lombok.Data;

import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2023/6/15
 */
@Data
public class CdpAdDataUserRequest {

    private String appId;
    private String date;
    private String applicationName;
    private String applicationPackage;
    private List<NewUserInfo> newUserList;

    @Data
    public static class NewUserInfo{
        private String belongDate;
        private Integer ecpm;
        private Integer adIncome;
        private Integer remainUserNum;
        private Integer withdrawAmount;
    }

    public CdpAdDataUserRequest build(List<CdpAdDataUser> list){
        CdpAdDataUserRequest request = new CdpAdDataUserRequest();
        CdpAdDataUser step1 = list.get(0);

        request.setAppId(step1.getAppId());
        request.setApplicationName(step1.getApplicationName());
        request.setApplicationPackage(step1.getApplicationPackage());
        request.setDate(step1.getLogday());

        List<NewUserInfo> userInfos = list.stream().map(cdpAdDataUser -> {
            NewUserInfo newUserInfo  = new NewUserInfo();
            newUserInfo.setBelongDate(cdpAdDataUser.getBelongDate());
            newUserInfo.setAdIncome(cdpAdDataUser.getAdIncome());
            newUserInfo.setEcpm(cdpAdDataUser.getEcpm());
            newUserInfo.setWithdrawAmount(cdpAdDataUser.getWithdrawAmount());
            newUserInfo.setRemainUserNum(cdpAdDataUser.getRemainUserNum());
            return newUserInfo;
        }).collect(Collectors.toList());


        request.setNewUserList(userInfos);
        return request;
    }
}
