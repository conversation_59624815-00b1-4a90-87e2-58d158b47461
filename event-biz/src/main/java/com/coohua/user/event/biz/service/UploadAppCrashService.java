package com.coohua.user.event.biz.service;

import com.alibaba.fastjson.JSON;
import com.coohua.user.event.biz.service.bean.CoreEventReq;
import com.coohua.user.event.biz.service.bean.EventProperties;
import com.pepper.metrics.core.AlertProfiler;
import com.pepper.metrics.core.AlertStats;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.kafka.clients.consumer.ConsumerRecord;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.stereotype.Service;

import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @since 2020/8/25
 */
@Slf4j
@Service
public class UploadAppCrashService implements InitializingBean {

    private AlertStats alertStats;
    private AlertStats alertStatsClick;
    private AlertStats cosErStats;


    public void uploadCrash(ConsumerRecord<String, String> record){
        String msg = record.value();
        if (StringUtils.isNotEmpty(msg)) {
            CoreEventReq coreEventReq = JSON.parseObject(msg, CoreEventReq.class);
            EventProperties properties = coreEventReq.getProperties();
            if ("通知开启状态".equals(properties.getElement_page())){
                alertStatsClick.incrementAndGet(
                        "APP_NAME", properties.product,
                        "APP_VERSION ", properties.$app_version,
                        "OS_VERSION", properties.$os + "-" + properties.$os_version
                );
            }else {
                if ("cosEr".equals(properties.getAd_action())){
                    cosErStats.incrementAndGet(
                            "APP_NAME", properties.product,
                            "APP_VERSION ", properties.$app_version,
                            "OS_VERSION", properties.$os + "-" + properties.$os_version);
                }else {
                    alertStats.incrementAndGet(
                            "APP_NAME", properties.product,
                            "APP_VERSION ", properties.$app_version,
                            "OS_VERSION", properties.$os + "-" + properties.$os_version
                    );
                }
            }
        }
    }

    @Override
    public void afterPropertiesSet() throws Exception {
        alertStats = AlertProfiler.Builder.builder()
                .name("app_crash_event_upload")
                .create();

        alertStatsClick = AlertProfiler.Builder.builder()
                .name("app_click_event_upload")
                .create();

        cosErStats = AlertProfiler.Builder.builder()
                .name("app_cos_er_event_upload")
                .create();
    }
}
