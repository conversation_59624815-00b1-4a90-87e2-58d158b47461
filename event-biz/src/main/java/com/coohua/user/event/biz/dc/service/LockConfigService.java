package com.coohua.user.event.biz.dc.service;

import com.alibaba.fastjson.JSON;
import com.coohua.user.event.biz.click.entity.ProductEntity;
import com.coohua.user.event.biz.dc.dto.ProductLockBean;
import com.coohua.user.event.biz.core.dto.req.CreateLockConfig;
import com.coohua.user.event.biz.dc.dto.LockConfigRsp;
import com.coohua.user.event.biz.dc.entity.LockConfig;
import com.coohua.user.event.biz.dc.mapper.LockConfigMapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.coohua.user.event.biz.dc.dto.Pages;
import com.coohua.user.event.biz.util.AppConfig;
import com.coohua.user.event.biz.util.DateUtil;
import com.coohua.user.event.biz.util.Strings;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

/**
* <p>
    * 产品锁区配置 服务实现类
    * </p>
*
* <AUTHOR>
* @since 2023-07-06
*/
@Service
public class LockConfigService extends ServiceImpl<LockConfigMapper, LockConfig> {

    public Pages<LockConfigRsp> queryList(String product, Pages<LockConfigRsp> page){
        if (Strings.isEmpty(product)){
            product = null;
        }
        int from = (page.getPageNo() - 1) * page.getPageSize();
        List<LockConfig> adFxConfigs = this.baseMapper.queryList(product,from,page.getPageSize());

        page.setItems(adFxConfigs.stream().map(this::convertToRsp).collect(Collectors.toList()));
        page.setCount(this.baseMapper.queryCount(product));
        return page;
    }

    private LockConfigRsp convertToRsp(LockConfig lockConfig){
        LockConfigRsp lockConfigRsp = new LockConfigRsp();
        lockConfigRsp.setId(lockConfig.getId());
        lockConfigRsp.setOs(lockConfig.getOs());
        lockConfigRsp.setCreateTime(DateUtil.dateToStringWithTime(lockConfig.getCreateTime()));
        lockConfigRsp.setUpdateTime(DateUtil.dateToStringWithTime(lockConfig.getUpdateTime()));
        lockConfigRsp.setProduct(lockConfig.getProduct());
        lockConfigRsp.setProductName(lockConfig.getProductName());
        lockConfigRsp.setProductGroup(lockConfig.getProductGroup());
        lockConfigRsp.setState(lockConfig.getState());
        lockConfigRsp.setConfig(JSON.parseObject(lockConfig.getConfig(), ProductLockBean.class));
        return lockConfigRsp;
    }

    public boolean addNewConfig(CreateLockConfig createLockConfig){

        LockConfig lockConfig = new LockConfig();
        lockConfig.setState(createLockConfig.getState());

        ProductEntity productEntity = AppConfig.appIdMap.get(createLockConfig.getAppId());
        if (productEntity == null){
            throw new RuntimeException("未查询到当前APP");
        }
        lockConfig.setProduct(productEntity.getProduct());
        lockConfig.setProductGroup(productEntity.getProductGroup());
        lockConfig.setProductName(productEntity.getProductName());

        lockConfig.setOs(createLockConfig.getOs());
        Date now = new Date();
        lockConfig.setCreateTime(now);
        lockConfig.setUpdateTime(now);

        lockConfig.setConfig(JSON.toJSONString(createLockConfig.getProductLockBean()));
        return save(lockConfig);
    }

    public boolean editConfig(CreateLockConfig createLockConfig){
        LockConfig lockConfig = getById(createLockConfig.getId());
        if (lockConfig == null){
            throw new RuntimeException("未查询到当前记录");
        }
        lockConfig.setState(createLockConfig.getState());

        ProductEntity productEntity = AppConfig.appIdMap.get(createLockConfig.getAppId());
        if (productEntity == null){
            throw new RuntimeException("未查询到当前APP");
        }
        lockConfig.setProduct(productEntity.getProduct());
        lockConfig.setProductGroup(productEntity.getProductGroup());
        lockConfig.setProductName(productEntity.getProductName());

        lockConfig.setOs(createLockConfig.getOs());
        Date now = new Date();
        lockConfig.setUpdateTime(now);

        lockConfig.setConfig(JSON.toJSONString(createLockConfig.getProductLockBean()));
        return updateById(lockConfig);
    }

    public boolean switchStateConfig(Integer id,Integer state){
        LockConfig lockConfig = getById(id);
        if (lockConfig == null){
            throw new RuntimeException("未查询到当前记录");
        }
        lockConfig.setState(state);
        return updateById(lockConfig);
    }
}
