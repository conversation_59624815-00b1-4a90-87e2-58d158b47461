package com.coohua.user.event.biz.dc.entity;

import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 * @since 2020/9/8
 */
@Data
public class TouFangAllOfflineEntity {

    /**
     * id
     */
    private Integer id;

    /**
     * logday
     */
    private Date logday;

    private Integer appId;
    /**
     * app_name
     */
    private String appName;


    private String appGroup;

    /**
     * os
     */
    private String os;

    /**
     * channel_name
     */
    private String channelName;

    /**
     * cods
     */
    private BigDecimal cods;

    /**
     * rebate_cost
     */
    private BigDecimal rebateCost;

    /**
     * activate
     */
    private Long activate;

    /**
     * create_time
     */
    private Date createTime;

    /**
     * update_time
     */
    private Date updateTime;

}
