package com.coohua.user.event.biz.service;

import com.alibaba.fastjson.JSON;
import com.coohua.user.event.biz.click.entity.ProductEntity;
import com.coohua.user.event.biz.enums.ActiveChannel;
import com.coohua.user.event.biz.enums.GrayType;
import com.coohua.user.event.biz.service.bean.UserActive;
import com.coohua.user.event.biz.service.bean.UserDeviceBean;
import com.coohua.user.event.biz.service.bean.UserIdBean;
import com.coohua.user.event.biz.service.rsp.CheckInnerRsp;
import com.coohua.user.event.biz.user.entity.GlobalWhiteEntity;
import com.coohua.user.event.biz.user.mapper.UserWhiteMapper;
import com.coohua.user.event.biz.util.AppConfig;
import com.coohua.user.event.biz.util.DateUtil;
import com.coohua.user.event.biz.util.RedisKeyConstants;
import com.ctrip.framework.apollo.spring.annotation.ApolloJsonValue;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.directory.api.util.Strings;
import org.joda.time.LocalDate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;
import redis.clients.jedis.Jedis;
import redis.clients.jedis.Pipeline;
import redis.clients.jedis.Response;
import top.zrbcool.pepper.boot.jediscluster.JedisClusterClient;
import top.zrbcool.pepper.boot.jediscluster.JedisClusterClientRefer;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.util.*;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2023/8/28
 */
@Slf4j
@Service
public class UserDeviceCheckService {

    private static final String PRE_FIX = "{user:gray}:";

    private final static String PRE_FIX_DEVICE = "{user:device}:";

    public static final String USER_WITHDRAW_AMOUNT = "user:withdraw:amount:tr:";

    public static final String COLON = ":";

    @JedisClusterClientRefer(namespace = "user-event")
    private JedisClusterClient userEventJedisClusterClient;
    @JedisClusterClientRefer(namespace = "ap-cluster")
    private JedisClusterClient apJedisClusterClient;

    private List<String> whiteList = new ArrayList<>();

    @Autowired
    private UserGrayService userGrayService;
    @Autowired
    private TFUserService tfUserService;
    @Resource
    private UserWhiteMapper userWhiteMapper;
    @ApolloJsonValue("${tx.check.app.list:[788]}")
    private List<Long> checkTxAppList;

    @ApolloJsonValue("${tx.check.app.active.list:[872]}")
    private List<Long> checkTxAppActiveList;

    @ApolloJsonValue("${skip.check.channel.list:[]}")
    private List<Long> skipChannelCheckAppList;
    @ApolloJsonValue("${tx.check.channel.list:[\"ttznwims12b\"]}")
    private List<String> checkTxChannelList;
    // 加白设备不拉黑
    @ApolloJsonValue("${tx.white.device.list:[\"4f4ff20a-0481-4df0-93c7-59a6908a083d\"]}")
    private Set<String> whiteDeviceSet;

    @ApolloJsonValue("${check.no.dist.list:[]}")
    private Set<String> checkNoDistList;

    @Autowired
    private WithdrawExChannelService withdrawExChannelService;
    @Autowired
    private WithdrawCheckExService withdrawCheckExService;

    private static final String REDIS_COUNT = "{count:slot}:event:count:%s:%s";
    private static final String REDIS_COUNT_WITH_OS = "{count:slot}:event:count:%s:%s:%s";
    private static final Pattern  MODEL_PATTERN  = Pattern.compile("^[a-zA-Z0-9_.,+\\-()]+$");;

    @ApolloJsonValue("${tx.check.product.list:[\"kxhy2\",\"tyrj3\"]}")
    private List<String> checkTxProductList;



    @PostConstruct
    @Scheduled(cron = "0 */5 * * * ?")
    public void refreshWhite(){
        log.info("refresh white info list start...");
        List<GlobalWhiteEntity> globalWhiteEntities = userWhiteMapper.listWhites();
        whiteList = globalWhiteEntities.stream().map(GlobalWhiteEntity::getWhiteInfo).collect(Collectors.toList());
        log.info("refresh white info list end...");
    }

    public boolean isWhiteUser(Long userId){
        return whiteList.contains(userId.toString());
    }

    public boolean checkSpecialChannel(Long userId,Long appId,Integer amount,UserActive userActive){
        if (skipChannelCheckAppList.contains(appId)){
            return false;
        }
        if ("ios".equalsIgnoreCase(userActive.getOs())){
            return false;
        }
        if (StringUtils.isNotEmpty(userActive.getChannel()) && Objects.equals(userActive.getChannel(), "ALIYUN_MAN_CHANNEL") ) {
            log.info("阿里云渠道拒绝提现 {} {} {} {}", appId, userId, userActive.getChannel(), amount);
            return true;
        }
        return false;
    }

    public boolean checkOdsPoint(Long userId,Long appId,Integer amount,UserActive userActive){
        ProductEntity productEntity = AppConfig.appIdMap.get(appId);
        if (productEntity == null){
            log.error("AppId {} {} 未查询到产品配置", appId,userId);
            return false;
        }

        if (checkNoDistList.contains(productEntity.getProductGroup())){
            return false;
        }

//        if ("项目一组".equals(productEntity.getProductGroup()) && !checkTxAppList.contains(appId)){
//            return false;
//        }

        if (!userActive.isZiRan()){
            return false;
        }
        if (!checkTxAppList.contains(appId)) {
            //大部分产品这里只拦新增
            if (userActive.getCreateTime().compareTo(LocalDate.now().toDate()) < 0) {
                log.info("AppId {} {} 不处理老用户 {}", appId,userId,userActive.getCreateTime());
                return false;
            }
        }

        return checkNoDist(userId, appId, amount, productEntity, "android");
    }

    public boolean checkNoDist(Long userId, Long appId, Integer amount, ProductEntity productEntity, String os) {

        if (checkNoDistList.contains(productEntity.getProductGroup())){
            return false;
        }

        String key = String.format(REDIS_COUNT_WITH_OS, os, productEntity.getProduct(), userId);
        String result = userEventJedisClusterClient.get(key);
        if (StringUtils.isNotEmpty(result)) {
            if (Integer.parseInt(result) < 1) {
                log.info("埋点个数检测未通过 {} {} {} {} 个", appId, userId, amount, result);
                return true;
            }
        } else {
            log.info("埋点个数检测未通过 {} {} {} 0个", appId, userId, amount);
            return true;
        }
        return false;
    }

    public boolean checkUserIsInWaitCheckChannel(Long userId,Long appId,Integer amount, UserActive userActive){

        boolean isExChannel = withdrawExChannelService.isExChannel(userActive.getProduct(),userActive.getChannel());
        if (isExChannel){
            log.info("人均提现渠道异常 用户 {} {} {} 提现 {} 且处于异常渠道 记录日志-拒绝审核",appId,userId,userActive.getChannel(),amount);
            return true;
        }
        return false;
    }

    public boolean checkUserIsBigWithdrawCheckChannel(Long userId,Long appId,Integer amount,UserActive userActive){
        boolean isExChannel = withdrawCheckExService.isExChannel(userActive.getProduct(),userActive.getChannel());
        if (isExChannel){
            log.info("新增人均提现渠道异常 用户 {} {} {} 提现 {} 且处于异常渠道 记录日志-拒绝审核",appId,userId,userActive.getChannel(),amount);
            return true;
        }
        return false;
    }

    public boolean checkUserIsBigHourWithdrawCheckChannel(Long userId,Long appId,Integer amount,UserActive userActive){

        boolean isExChannel = withdrawCheckExService.isExHourChannel(userActive.getProduct(),userActive.getChannel(),String.valueOf(userId));
        if (isExChannel){
            log.info("分时新增用户24h内提现异常 用户 {} {} {} 提现 {}  记录日志-拒绝审核",appId,userId,userActive.getChannel(),amount);
            return true;
        }
        return false;
    }
    /*public boolean checkUserIsBigHourNatureWithdrawCheckChannel(Long userId,Long appId,Integer amount,UserActive userActive){

        boolean isExChannel = withdrawCheckExService.isExNatureHourChannel(userActive.getProduct(),userActive.getChannel(),String.valueOf(userId));
        if (isExChannel){
            log.info("分时新增自然量用户24h内提现异常 用户 {} {} {} 提现 {}  记录日志-拒绝审核",appId,userId,userActive.getChannel(),amount);
            return true;
        }
        return false;
    }*/

    public boolean checkIosWithdrawCheckCaid(Long userId,Long appId,Integer amount, UserActive userActive){
        if ("android".equalsIgnoreCase(userActive.getOs())){
            return false;
        }
        String iosCaidCommonLey = RedisKeyConstants.iosCaidCommonKey(userActive.getProduct(), userActive.getCaid());
        Set<String> smembers = userEventJedisClusterClient.smembers(iosCaidCommonLey);
        if (smembers.size() >= 3) {
            log.info("ios检测caid用户异常 {} {} {} {} 提现 {}  {} 记录日志-拒绝审核",appId,userId,userActive.getChannel() ,userActive.getCaid(),JSON.toJSONString(smembers), amount);
            return true;
        }
        return false;
    }

    public boolean checkUserIsInWaitCheckChannelAndModel(Long userId,Long appId,Integer amount, UserActive userActive){
        ProductEntity productEntity = AppConfig.appIdMap.get(appId);
        if (productEntity == null){
            log.error("AppId {} {} 未查询到产品配置", appId,userId);
            return false;
        }

        if (Objects.equals(userActive.getSource(),"自然量") && (Objects.isNull(userActive.getProduct()) || !AppConfig.productEnSet.contains(userActive.getProduct()))) {
            log.info("微信注册无埋点上报异常 用户 {} {} {} {} 提现 {} 且产品不存在 记录日志-拒绝审核",appId,userId,userActive.getProduct(), userActive.getChannel(),amount);
            return true;
        }
        if ("项目七组".equals(productEntity.getProductGroup()) && StringUtils.isNotBlank(userActive.getModel())){
            if (!MODEL_PATTERN.matcher(userActive.getModel().replaceAll(" ","")).find()) {
                log.info("微信注册无埋点上报异常 用户 {} {} {} {} 提现 {} 且处于异常机型 乱码 记录日志-拒绝审核", appId, userId, userActive.getChannel(), userActive.getModel(), amount);
                return true;
            }
        }
        boolean isExChannel = withdrawCheckExService.isExChannelAndModel(productEntity.getProduct(),userActive.getChannel(),userActive.getModel(), userId);
        if (isExChannel){
            log.info("微信注册无埋点上报异常 用户 {} {} {} {} 提现 {} 且处于异常机型 记录日志-拒绝审核", appId, userId, userActive.getChannel(), userActive.getModel(), amount);
            return true;
        }
        boolean isExChannelOa = withdrawCheckExService.isExChannelAndOs(productEntity.getProduct(),userActive.getChannel(),userActive.getOaid());
        if (isExChannelOa){
            log.info("微信注册无埋点上报异常 用户 {} {} {} {} 提现 {} 且处于异常oaid 记录日志-拒绝审核", appId, userId, userActive.getChannel(), userActive.getModel(), amount);
            return true;
        }

        return false;
    }

    public boolean checkRealtimeUserChannelNtf(Long userId,Long appId,Integer amount,UserActive userActive){
        ProductEntity productEntity = AppConfig.appIdMap.get(appId);
        if (productEntity == null){
            log.error("AppId {} {} 未查询到产品配置", appId,userId);
            return false;
        }
        String key = String.format(REDIS_COUNT_WITH_OS, userActive.getOs(), productEntity.getProduct(), userId);
        String result = userEventJedisClusterClient.get(key);
        Integer pointCount = 0;
        if (StringUtils.isNotEmpty(result)){
            pointCount = Integer.parseInt(result);
        }

        if (checkTxChannelList.contains(userActive.getChannel())){
            ActiveChannel activeChannel = ActiveChannel.getByDesc(userActive.getSource());
            if (!ActiveChannel.isOCPCChanel(activeChannel)){
                log.info("七组内拉新临时拉灰 {} {} {} 渠道 {} 自然量 拒绝提现 不通过",appId,userId,userActive.getChannel(),userActive.getSource());
                userGrayService.sendUserGrayWithdrawReason(appId.intValue(),userId.toString(),"七组内拉新临时拉灰");
                return true;
            }
        }

        Date now = new Date();
        if (pointCount > 0){
            // 查看是否只是三大
            String keyOwn = UserAdExposureCheckService.buildExposureVideoMain(userId,appId);
            String resultOwn = apJedisClusterClient.get(keyOwn);
            if (Strings.isNotEmpty(resultOwn)){
                if (pointCount.equals(Integer.valueOf(resultOwn))){
                    if ("android".equals(userActive.getOs())){
                        Date dayBegin = DateUtil.stringToDate(DateUtil.dateToString(now) + " 00:00:00",DateUtil.COMMON_TIME_FORMAT);
                        // 今日注册
                        if (dayBegin.before(userActive.getCreateTime())){
                            log.info("{} {} 注册时间 {} 曝光埋点 {} 总埋点 {} 异常 拒绝提现",appId,userId,
                                    DateUtil.dateToStringWithTime(userActive.getCreateTime()),resultOwn,pointCount);
                            userGrayService.sendUserGrayWithdrawReason(appId.intValue(),userId.toString(),"只存在曝光埋点-拉灰");
                            return true;
                        }
                    }
                }
            }
            return false;
        }

        log.info("用户 {} {} 无埋点 查看来源",appId,userId);

        ActiveChannel activeChannel = ActiveChannel.getByDesc(userActive.getSource());

        // 若1秒内注册
        if (!ActiveChannel.isOCPCChanel(activeChannel) && now.getTime() - userActive.getCreateTime().getTime() < 1000){
            log.info("{} {} {} 渠道 {} 自然量 拒绝提现 不通过",appId,userId,userActive.getChannel(),userActive.getSource());
            userGrayService.grayUserAsync(GrayType.USER, Collections.singletonList(userId.toString()),
                    "自然量无埋点用户提现速率过快",productEntity.getProduct(),"android","realtime-withdraw");
            return true;
        }
        return false;
    }

    public CheckInnerRsp isAlreadyGrayDeviceUser(Long appId, Long userId){
        CheckInnerRsp checkInnerRsp = new CheckInnerRsp();
        checkInnerRsp.setReject(false);
        ProductEntity productEntity = AppConfig.appIdMap.get(appId);
        if (productEntity == null){
            log.error("AppId {} {} 未查询到产品配置", appId,userId);
            return checkInnerRsp;
        }
        UserIdBean userIdBean = getUserDeviceInfo(productEntity.getProduct(),userId.toString());
        if (userIdBean == null){
            log.warn("未在Redis查询到设备信息状况 {} {}",appId,userId);
            return checkInnerRsp;
        }
        Set<String> deviceSet = new HashSet<>();
        deviceSet.addAll(userIdBean.getDeviceId());
        deviceSet.addAll(userIdBean.getImei());
        deviceSet.addAll(userIdBean.getOaid());
        deviceSet.addAll(userIdBean.getDistinctId());
        if(deviceSet.isEmpty()) {
            log.warn("未在Redis查询到设备信息状况 {} {}",appId,userId);
            return checkInnerRsp;
        }

        List<String> deviceIDList = queryGrayedDevice(new ArrayList<>(deviceSet));

        if (deviceIDList.size() > 0){
            log.info("查询到用户 {} {} 关联的设备 {} 曾被拉黑 拒绝提现申请",appId,userId,JSON.toJSONString(deviceIDList));
            checkInnerRsp.setReject(true);
            checkInnerRsp.setReason("关联设备处于黑名单-拒绝提现");
            return checkInnerRsp;
        }

        if (userIdBean.getCaid() != null && userIdBean.getCaid().size() >= 20){
            log.info("查询到用户 {} {} 关联的CAID过多 {} 拒绝提现申请",appId,userId,JSON.toJSONString(userIdBean.getCaid()));
            checkInnerRsp.setReject(true);
            checkInnerRsp.setReason("IOS关联CAID过多-拒绝提现");
            userGrayService.sendUserGrayWithdrawReason(appId.intValue(),userId.toString(),"IOS关联CAID过多拉灰");
            return checkInnerRsp;
        }
        return checkInnerRsp;
    }


    public Integer getUserWithdraw(Long appId,Long userId,Integer amount){
        String key = buildWithdrawKey(userId,appId);
        String result = userEventJedisClusterClient.get(key);
        if (Strings.isNotEmpty(result)){
            amount = amount + Integer.parseInt(result);
        }
        return amount;
    }

    public Integer getUserYesterdayWithdraw(Long appId,Long userId){
        //这里存入的时候只实时设置了24小时过期时间，所以不存在并不代表昨日未提现
        String key = buildYesterdayWithdrawKey(userId,appId);
        String result = userEventJedisClusterClient.get(key);
        if (Strings.isNotEmpty(result)){
            return Integer.parseInt(result);
        }
        return 0;
    }

    public boolean isUserDeviceHasManyWechat(Long appId,Long userId){
        ProductEntity productEntity = AppConfig.appIdMap.get(appId);
        if (productEntity == null){
            log.error("AppId {} {} 未查询到产品配置", appId,userId);
            return false;
        }

        int checkFlag = 5;
        // 切为设备归因-投放用户PASS
        if (tfUserService.isUseDeviceGui(productEntity.getProduct())) {
            try {
                UserActive userActive = tfUserService.queryUserDeviceGui(productEntity.getProduct(), userId.toString(), appId);
                if (userActive != null && userActive.isOcpc()) {
                    checkFlag = 10;
                }
                if (tfUserService.isDeviceGuiLogEnabled) {
                    log.info("UserDeviceCheckService设备归因 {} {} {}", productEntity.getProduct(), userId, userActive);
                }
            } catch (Exception e) {
                log.error("isUserDeviceHasManyWechat查询用户设备归因失败 {} {}", appId, userId, e);
            }
        } else {
            List<String> ntfList =  tfUserService.queryNtfUser(productEntity.getProduct(),
                    Collections.singletonList(userId.toString()),"withdraw-query");
            if (ntfList.size() == 0){
                // 投放用户PASS
                checkFlag = 10;
            }
        }


        UserIdBean userIdBean = getUserDeviceInfo(productEntity.getProduct(),userId.toString());
        if (userIdBean == null){
            log.warn("未在Redis查询到设备信息状况 {} {}",appId,userId);
            return false;
        }
        Set<String> deviceSet = new HashSet<>();
        deviceSet.addAll(userIdBean.getDeviceId());
        deviceSet.addAll(userIdBean.getImei());
        deviceSet.addAll(userIdBean.getOaid());
        deviceSet.addAll(userIdBean.getDistinctId());
        // 查看用户
        List<UserDeviceBean> results = getDeviceBeanMap(productEntity.getProduct(),deviceSet);
        for (UserDeviceBean userDeviceBean : results){
            if (userDeviceBean.getUserId().size() > checkFlag){
                log.info("设备 {} {} 关联了 {} 个 用户 {}",appId,userDeviceBean.getDeviceId(),
                        userDeviceBean.getUserId().size(),JSON.toJSONString(userDeviceBean.getUserId()));
                String remark = "投放用户多设备关联-提现拉黑";
                if (checkFlag < 10){
                    remark = "非投放用户多设备关联-提现拉黑";
                }
                userGrayService.grayUserAsync(GrayType.USER, Collections.singletonList(userId.toString()),
                        remark,productEntity.getProduct(),"android","realtime-withdraw");
                return true;
            }
        }
        return false;
    }


    public boolean newUserWithdrawLimit(Long userId, Long appId, Integer totalAmount){
        ProductEntity productEntity = AppConfig.appIdMap.get(appId);
        if (productEntity == null){
            log.error("AppId {} {} 未查询到产品配置", appId,userId);
            return false;
        }


        if (!"项目七组".equals(productEntity.getProductGroup()) && !"项目一组".equals(productEntity.getProductGroup())){
            return false;
        }

        if ("项目一组".equals(productEntity.getProductGroup()) && !checkTxProductList.contains(productEntity.getProduct())){
            return false;
        }

        if(totalAmount < 10000 && "项目七组".equals(productEntity.getProductGroup())){
            return false;
        }

        UserActive userActive = tfUserService.queryUserActiveIfNoFundQueryRpc(productEntity.getProduct(),userId.toString(),appId);
        if (userActive == null){
            log.info("未查询到 USER_ACTIVE {} {} 审核暂时不通过",appId,userId);
            return true;
        }

        Date now = new Date();
        Date dayBegin = DateUtil.stringToDate(DateUtil.dateToString(now) + " 00:00:00",DateUtil.COMMON_TIME_FORMAT);
        if (dayBegin.before(userActive.getCreateTime())) {
            log.info("用户 {} {} {} 累积提现达到 20元 延缓提现",
                    appId,userId,DateUtil.dateToString(userActive.getCreateTime()));
            return true;
        }
        return false;
    }

    public Integer getAppAmount(String os, Long appId, Integer amount){
        String key = buildAppAmountKey(os, appId);
        String result = userEventJedisClusterClient.get(key);
        if (Strings.isNotEmpty(result)){
            amount = amount + Integer.parseInt(result);
        }
        return amount;
    }

    public Integer getAppZiranAmount(String os, Long appId, Integer amount){
        String key = buildAppZiranAmountKey(os, appId);
        String result = userEventJedisClusterClient.get(key);
        if (Strings.isNotEmpty(result)){
            amount = amount + Integer.parseInt(result);
        }
        return amount;
    }


    public void incrAppTodayAmount(String os, Long appId, Integer amount) {
        String key = buildAppAmountKey(os, appId);
        userEventJedisClusterClient.incrBy(key,amount);
        userEventJedisClusterClient.expire(key, 60 * 60 * 24);
    }

    public void incrZiranAppTodayAmount(String os, Long appId, Integer amount) {
        String key = buildAppZiranAmountKey(os, appId);
        userEventJedisClusterClient.incrBy(key,amount);
        userEventJedisClusterClient.expire(key, 60 * 60 * 24);
    }

    public static String buildAppAmountKey(String os, Long appId){
        Date now = new Date();
        String day = DateUtil.dateToString(now,DateUtil.ISO_DATE_FORMAT);
        return "withdraw:amount:tr:" + os + COLON + appId  + COLON + day;
    }

    public static String buildAppZiranAmountKey(String os, Long appId){
        Date now = new Date();
        String day = DateUtil.dateToString(now,DateUtil.ISO_DATE_FORMAT);
        return "withdraw:2day:ziran:amount:tr:" + os + COLON + appId + COLON + day;
    }

    private static String buildWithdrawKey(Long userId, Long appId){
        Date now = new Date();
        String day = DateUtil.dateToString(now,DateUtil.ISO_DATE_FORMAT);
        return USER_WITHDRAW_AMOUNT + appId + COLON + userId + COLON + day;
    }


    public void incrTodayAmount(Long userId, Long appId,Integer amount){
        String key = buildWithdrawKey(userId,appId);
        userEventJedisClusterClient.incrBy(key,amount);
        userEventJedisClusterClient.expire(key, 60 * 60 * 24);
    }

    private static String buildYesterdayWithdrawKey(Long userId, Long appId){
        Date now = new Date();
        Date yesterday = DateUtil.dateIncreaseByDay(now, -1);
        String day = DateUtil.dateToString(yesterday,DateUtil.ISO_DATE_FORMAT);
        return USER_WITHDRAW_AMOUNT + appId + COLON + userId + COLON + day;
    }

    public boolean canAdd(String orderNo){
        String result = userEventJedisClusterClient.get(orderNo);
        if (Strings.isNotEmpty(result)){
            return false;
        }

        userEventJedisClusterClient.set(orderNo,"true");
        userEventJedisClusterClient.expire(orderNo, 60 * 60 * 24);
        return true;
    }

    public void saveGrayDevice(List<String> deviceIdList){
        try (Jedis jedis = userEventJedisClusterClient.getResource(PRE_FIX)){
            Pipeline pipeline = jedis.pipelined();
            deviceIdList.forEach(device ->{
                if (Strings.isEmpty(device) || device.startsWith("0000") || "null".equals(device) || "wdvid".equals(device)  || whiteDeviceSet.contains(device)){
                    return;
                }
                String key = buildGrayDeviceKey(device);
                pipeline.set(key,device);
                pipeline.expire(key, 60 * 60 * 24 * 90);
            });
            pipeline.sync();
        }catch (Exception e){
            log.error("Save Ex:",e);
        }
    }

    private String buildGrayDeviceKey(String deviceId){
        return PRE_FIX + "device:gray:" +deviceId;
    }

    public List<String> queryGrayedDevice(List<String> checkList){
        List<String> results = userEventJedisClusterClient.mget(checkList.stream()
                .map(this::buildGrayDeviceKey).toArray(String[]::new));
        return results.stream()
                .filter(Strings::isNotEmpty)
                .filter(r -> !"null".equals(r))
                .filter(r -> !"0".equals(r))
                .filter(r -> !r.startsWith("0000"))
                .collect(Collectors.toList());
    }

    private UserIdBean getUserDeviceInfo(String product,String userId){
        String key = buildUserIdDeviceKey(product, userId);
        String result = userEventJedisClusterClient.get(key);
        if (Strings.isNotEmpty(result)){
            return JSON.parseObject(result,UserIdBean.class);
        }
        return null;
    }

    private List<UserDeviceBean> getDeviceBeanMap(String product,Set<String> deviceIdSet){
        List<UserDeviceBean> resultList = new ArrayList<>();
        try (Jedis jedis = userEventJedisClusterClient.getResource(PRE_FIX_DEVICE)){
            Pipeline pipeline = jedis.pipelined();
            List<Response<String>> responses = new ArrayList<>();
            deviceIdSet.forEach(device ->{
                Response<String> response = pipeline.get(buildUserIdKey(product,device));
                responses.add(response);
            });
            pipeline.sync();
            for (Response<String> response: responses){
                String res = response.get();
                if (Strings.isNotEmpty(res)){
                    resultList.add(JSON.parseObject(res,UserDeviceBean.class));
                }
            }
        }catch (Exception e){
            log.error("Query UserDeiceBean Ex：",e);
        }
        return resultList;
    }

    public String buildUserIdDeviceKey(String product,String userId){
        return PRE_FIX_DEVICE + "user:device:" + product+":"+userId;
    }

    public String buildUserIdKey(String product,String deviceId){
        return PRE_FIX_DEVICE + "user:id:" + product+":"+deviceId;
    }

    public void getAndMergeSave(Map<String, UserIdBean> saveMap){
        try (Jedis jedis = userEventJedisClusterClient.getResource(PRE_FIX_DEVICE)){
            Pipeline pipeline = jedis.pipelined();
            saveMap.forEach((k,v)->{
                Response<String> rs = pipeline.get(k);
                pipeline.sync();
                Set<String> deviceId = v.getDeviceId() == null ? new HashSet<>(): v.getDeviceId();
                Set<String> imei = v.getImei() == null ? new HashSet<>(): v.getImei();
                Set<String> oaid = v.getOaid() == null ? new HashSet<>(): v.getOaid();
                Set<String> caid = v.getCaid() == null ? new HashSet<>(): v.getCaid();
                Set<String> distinctId =v.getDistinctId() == null ? new HashSet<>(): v.getDistinctId();
                if (rs != null){
                    String result = rs.get();
                    if (com.coohua.user.event.biz.util.Strings.noEmpty(result)){
                        UserIdBean userIdBean = JSON.parseObject(result,UserIdBean.class);
                        deviceId.addAll(userIdBean.getDeviceId());
                        imei.addAll(userIdBean.getImei());
                        oaid.addAll(userIdBean.getOaid());
                        if (userIdBean.getCaid() != null && userIdBean.getCaid().size() > 0) {
                            caid.addAll(userIdBean.getCaid());
                        }
                        distinctId.addAll(userIdBean.getDistinctId());
                    }
                }
                v.setDeviceId(deviceId);
                v.setImei(imei);
                v.setDistinctId(distinctId);
                v.setOaid(oaid);
                v.setCaid(caid);
                pipeline.set(k, JSON.toJSONString(v));
                pipeline.expire(k, 60 * 60 * 24 * 7);
            });
            pipeline.sync();
            pipeline.close();
        }catch (Exception e){
            log.error("Save batchMap Ex:",e);
        }
    }

    public void getAndMergeSaveDevice(Map<String, UserDeviceBean> saveMap){
        try (Jedis jedis = userEventJedisClusterClient.getResource(PRE_FIX_DEVICE)){
            Pipeline pipeline = jedis.pipelined();
            saveMap.forEach((k,v)->{
                Response<String> rs = pipeline.get(k);
                pipeline.sync();
                Set<String> userId = v.getUserId() == null ? new HashSet<>(): v.getUserId();
                if (rs != null){
                    String result = rs.get();
                    if (com.coohua.user.event.biz.util.Strings.noEmpty(result)){
                        UserDeviceBean userIdBean = JSON.parseObject(result,UserDeviceBean.class);
                        userId.addAll(userIdBean.getUserId());
                    }
                }
                v.setUserId(userId);
                pipeline.set(k, JSON.toJSONString(v));
                pipeline.expire(k, 60 * 60 * 24 * 7);
            });
            pipeline.sync();
            pipeline.close();
        }catch (Exception e){
            log.error("Save batchMap Ex:",e);
        }
    }

}
