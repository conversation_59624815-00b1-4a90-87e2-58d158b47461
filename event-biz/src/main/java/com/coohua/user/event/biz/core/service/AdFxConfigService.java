package com.coohua.user.event.biz.core.service;

import com.alibaba.fastjson.JSON;
import com.coohua.user.event.biz.click.entity.ProductEntity;
import com.coohua.user.event.biz.core.dto.req.AddAdConfigReq;
import com.coohua.user.event.biz.core.dto.req.AddUserAdConfig;
import com.coohua.user.event.biz.core.dto.rsp.AdFxConfigRsp;
import com.coohua.user.event.biz.core.entity.AdFxConfig;
import com.coohua.user.event.biz.core.mapper.AdFxConfigMapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.coohua.user.event.biz.dc.dto.Pages;
import com.coohua.user.event.biz.dc.dto.RealtimeRuleResponse;
import com.coohua.user.event.biz.dc.entity.RealtimeRule;
import com.coohua.user.event.biz.service.AdCloseService;
import com.coohua.user.event.biz.util.*;
import org.apache.commons.math3.stat.descriptive.summary.Product;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.concurrent.CompletableFuture;
import java.util.stream.Collectors;

/**
* <p>
    *  服务实现类
    * </p>
*
* <AUTHOR>
* @since 2023-06-27
*/
@Service
public class AdFxConfigService extends ServiceImpl<AdFxConfigMapper, AdFxConfig> {

    @Autowired
    private AdCloseService adCloseService;

    public List<AdFxConfig> queryStateInitAll(){
        return lambdaQuery().eq(AdFxConfig::getState,1).list();
    }

    public Pages<AdFxConfigRsp> queryList(String product, Pages<AdFxConfigRsp> page){
        if (Strings.isEmpty(product)){
            product = null;
        }
        int from = (page.getPageNo() - 1) * page.getPageSize();
        List<AdFxConfig> adFxConfigs = this.baseMapper.queryList(product,from,page.getPageSize());

        page.setItems(adFxConfigs.stream().map(this::convertToAdFxConf).collect(Collectors.toList()));
        page.setCount(this.baseMapper.queryCount(product));
        return page;
    }

    private AdFxConfigRsp convertToAdFxConf(AdFxConfig adFxConfig){
        AdFxConfigRsp adFxConfigRsp = new AdFxConfigRsp();
        BeanUtils.copyProperties(adFxConfig,adFxConfigRsp);

        adFxConfigRsp.setOcpcList(JSON.parseArray(adFxConfig.getOcpcSource(),String.class));
        adFxConfigRsp.setPlatformLimit(JSON.parseArray(adFxConfig.getPlatform(),Integer.class));
        adFxConfigRsp.setCreateTime(DateUtil.dateToStringWithTime(adFxConfig.getCreateTime()));
        adFxConfigRsp.setUpdateTime(DateUtil.dateToStringWithTime(adFxConfig.getUpdateTime()));
        adFxConfigRsp.setType(adFxConfig.getAdType());
        return adFxConfigRsp;
    }

    private void checkParam(AddAdConfigReq req){
        if (req.getFxType() == 2){
            if (Lists.isEmpty(req.getPlatformLimit())){
                throw new RuntimeException("请选择针对的广告平台");
            }
        }else if (req.getFxType() == 3){
            if (req.getAdId() == 0){
                throw new RuntimeException("请填写AdID");
            }
        }
        if (req.getOcpcType() == 1){
            if (Lists.isEmpty(req.getOcpcList())){
                throw new RuntimeException("请至少勾选1个投放用户来源");
            }
        }
    }

    public boolean addUserAdConfig(AddUserAdConfig addUserAdConfig){
        List<String> userIdList = Arrays.asList(addUserAdConfig.getTargetIds().split(","));
        if (userIdList.size() == 0){
            return false;
        }
        CompletableFuture.runAsync(() -> {
            userIdList.forEach(userId -> adCloseService.setSkipPlatform(addUserAdConfig.getAppId(),userId,
                    addUserAdConfig.getPlatformLimit(),
                    60*60*24* addUserAdConfig.getDay()));
        });
        return true;
    }

    public boolean addNewAdConfig(AddAdConfigReq req){
        checkParam(req);
        AdFxConfig adFxConfig = new AdFxConfig();
        adFxConfig.setAdId(req.getAdId());
        adFxConfig.setAdType(req.getType());
        adFxConfig.setAppId(req.getAppId());
        ProductEntity productEntity = AppConfig.appIdMap.get(Long.valueOf(req.getAppId()));
        if (productEntity != null){
            adFxConfig.setAppName(productEntity.getProductName());
        }
        adFxConfig.setState(req.getState());
        adFxConfig.setOcpcType(req.getOcpcType());
        adFxConfig.setFxType(req.getFxType());

        if (Lists.noEmpty(req.getPlatformLimit())){
            adFxConfig.setPlatform(JSON.toJSONString(req.getPlatformLimit()));
        }else {
            adFxConfig.setPlatform("[]");
        }
        if (Lists.noEmpty(req.getOcpcList())){
            adFxConfig.setOcpcSource(JSON.toJSONString(req.getOcpcList()));
        }else {
            adFxConfig.setOcpcSource("[]");
        }
        Date now = new Date();
        adFxConfig.setCreateTime(now);
        adFxConfig.setUpdateTime(now);
        return save(adFxConfig);
    }

    public boolean editAdConfig(AddAdConfigReq req){
        checkParam(req);
        AdFxConfig adFxConfig = getById(req.getId());
        if (adFxConfig == null){
            throw new RuntimeException("未查询到该配置记录");
        }
        adFxConfig.setAdId(req.getAdId());
        adFxConfig.setAdType(req.getType());
        adFxConfig.setAppId(req.getAppId());
        ProductEntity productEntity = AppConfig.appIdMap.get(Long.valueOf(req.getAppId()));
        if (productEntity != null){
            adFxConfig.setAppName(productEntity.getProductName());
        }
        adFxConfig.setState(req.getState());
        adFxConfig.setOcpcType(req.getOcpcType());
        adFxConfig.setFxType(req.getFxType());

        if (Lists.noEmpty(req.getPlatformLimit())){
            adFxConfig.setPlatform(JSON.toJSONString(req.getPlatformLimit()));
        }else {
            adFxConfig.setPlatform("[]");
        }
        if (Lists.noEmpty(req.getOcpcList())){
            adFxConfig.setOcpcSource(JSON.toJSONString(req.getOcpcList()));
        }else {
            adFxConfig.setOcpcSource("[]");
        }
        Date now = new Date();
        adFxConfig.setUpdateTime(now);
        return updateById(adFxConfig);
    }

    public boolean switchStateAdConfig(Integer id,Integer state){
        AdFxConfig adFxConfig = getById(id);
        if (adFxConfig == null){
            throw new RuntimeException("未查询到该配置记录");
        }
        Date now = new Date();
        adFxConfig.setUpdateTime(now);
        adFxConfig.setState(state);
        return updateById(adFxConfig);
    }
}
