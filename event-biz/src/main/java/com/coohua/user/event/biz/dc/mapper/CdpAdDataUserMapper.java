package com.coohua.user.event.biz.dc.mapper;

import com.coohua.user.event.biz.dc.entity.CdpAdData;
import com.coohua.user.event.biz.dc.entity.CdpAdDataUser;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * <p>
 *  Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2023-06-15
 */
public interface CdpAdDataUserMapper extends BaseMapper<CdpAdDataUser> {


    @Select({"select * from ads.cdp_ad_data_user where logday =#{logday}"})
    List<CdpAdDataUser> queryByLogday(@Param("logday")String logday);
}
