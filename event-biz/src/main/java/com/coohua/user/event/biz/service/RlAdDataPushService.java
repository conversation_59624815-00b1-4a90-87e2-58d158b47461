package com.coohua.user.event.biz.service;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.aliyun.openservices.shade.com.google.common.base.Charsets;
import com.coohua.user.event.biz.dc.entity.CdpAdData;
import com.coohua.user.event.biz.dc.entity.CdpAdDataUser;
import com.coohua.user.event.biz.dc.entity.CdpCompilations;
import com.coohua.user.event.biz.dc.entity.HsResultData;
import com.coohua.user.event.biz.dc.service.CdpAdDataService;
import com.coohua.user.event.biz.dc.service.CdpAdDataUserService;
import com.coohua.user.event.biz.dc.service.CdpCompilationsService;
import com.coohua.user.event.biz.dc.service.HsResultDataService;
import com.coohua.user.event.biz.service.req.*;
import com.coohua.user.event.biz.util.*;
import com.google.common.hash.Hashing;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import java.util.*;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2023/5/31
 */
@Slf4j
@Service
public class RlAdDataPushService {

    @Autowired
    private CdpCompilationsService cdpCompilationsService;
    @Autowired
    private CdpAdDataService cdpAdDataService;
    @Autowired
    private CdpAdDataUserService cdpAdDataUserService;
    @Autowired
    private HsResultDataService hsResultDataService;

    private static final Map<String,Object> publicHeader = new HashMap<String,Object>(){{
        put("Content-Type", MediaType.APPLICATION_FORM_URLENCODED_VALUE);
    }};

    public void pushDataToThird(String logday){
        if (Strings.isEmpty(logday)){
            logday = DateUtil.dateToString(DateUtil.dateIncreaseByDay(new Date(),-2));
        }
        log.info("===> push cdp logday {}",logday);
        pushUserData(logday);
//        pushNewUserData(logday);
//        pushCompilations(logday);
    }

    public void pushDataToHs(String logday){
        if (Strings.isEmpty(logday)){
            logday = DateUtil.dateToString(DateUtil.dateIncreaseByDay(new Date(),-3));
            pushHsData(logday);
            logday = DateUtil.dateToString(DateUtil.dateIncreaseByDay(new Date(),-2));
            pushHsData(logday);
            logday = DateUtil.dateToString(DateUtil.dateIncreaseByDay(new Date(),-1));
            pushHsData(logday);
        }else {
            log.info("===> push hs logday {}", logday);
            pushHsData(logday);
        }
    }

    private void pushHsData(String logday){
        String postUrl = "https://rd.huashengxiaoshuo.com/api/freeDj/callBack";
        List<HsResultData> hsResultDatas = hsResultDataService.queryByLogday(logday);
        String apiKey = "20230626";
        String sk = "4b7a5c8d2e6f9g1h3i0j7k5l8m9n5o7p8q2r1s3t5u9v4w7x1y2z";
        if (Lists.noEmpty(hsResultDatas)){
            hsResultDatas.forEach(hsResultData -> {
                long time = System.currentTimeMillis()/1000;
                HsAdDataRequest hsAdDataRequest = new HsAdDataRequest().build(hsResultData,apiKey,time);
                String sourceA = apiKey + time;
                String sourceB = sourceA + sk;
                String sign = Hashing.md5().newHasher().putString(sourceB, Charsets.UTF_8).hash().toString().toUpperCase();
                hsAdDataRequest.setSign(sign);
                String result = HttpClients.POST(postUrl,toMap(hsAdDataRequest));
                log.info("GET ==> {}",result);
            });
        }

    }

    private static Map<String,Object> toMap(Object request){
        return JSONObject.parseObject(JSON.toJSONString(request));
    }

    private void pushUserData(String logday){
        String postUrl = "https://cdp.qiguoread.com/cdp/organization_business_data/ad_data";
        List<CdpAdData> cdpAdDataList = cdpAdDataService.queryByLogday(logday);
        if (Lists.noEmpty(cdpAdDataList)){
            cdpAdDataList.forEach(cdpAdData -> {

                CpdBasicRequest<CdpAdDataRequest> basicRequest = new CpdBasicRequest<>();

                CdpAdDataRequest request = new CdpAdDataRequest().build(cdpAdData);
                Long time = System.currentTimeMillis()/1000;
                String sign = createSign(HttpClients.beanToMap(request),"85d8e11d74cdebc3b0605a049020a202d3f36293",time);

                basicRequest.setParam(request);
                basicRequest.setSignature(sign);
                basicRequest.setTimestamp(time);
                basicRequest.setAppId(cdpAdData.getAppId());

                String rsp = HttpClients.POST(postUrl,basicRequest.buildJson(),true );
            });
        }
    }

    private void pushNewUserData(String logday){
        String postUrl = "https://cdp.qiguoread.com/cdp/organization_business_data/new_user_data";
        List<CdpAdDataUser> adDataUserList = cdpAdDataUserService.queryByLogday(logday);
        if (Lists.noEmpty(adDataUserList)){
            Map<String,List<CdpAdDataUser>> appMap = adDataUserList.stream().collect(Collectors.groupingBy(CdpAdDataUser::getApplicationName));
            appMap.forEach((appName,list) -> {
                CdpAdDataUser cdpAdDataUser = list.get(0);
                CpdBasicRequest<CdpAdDataUserRequest> basicRequest = new CpdBasicRequest<>();
                Long time = System.currentTimeMillis()/1000;

                CdpAdDataUserRequest request = new CdpAdDataUserRequest().build(list);
                CdpAdDataUserRequest requestSign = new CdpAdDataUserRequest().build(list);
                requestSign.setNewUserList(null);
                String sign = createSign(HttpClients.beanToMap(requestSign),"85d8e11d74cdebc3b0605a049020a202d3f36293",time);

                basicRequest.setParam(request);
                basicRequest.setSignature(sign);
                basicRequest.setTimestamp(time);
                basicRequest.setAppId(cdpAdDataUser.getAppId());

                String rsp = HttpClients.POST(postUrl,basicRequest.buildJson(),true );
            });
        }
    }

    // 前端没埋点 传不过去
    private void pushCompilations(String logday){
        String  postUrl = "https://cdp.qiguoread.com/cdp/organization_business_data/compilations";
        List<CdpCompilations> cdpCompilationsList = cdpCompilationsService.queryByLogday(logday);
        if (Lists.noEmpty(cdpCompilationsList)){
            cdpCompilationsList.forEach(cdpCompilation -> {

                CpdBasicRequest<CdpCompilationsRequest> basicRequest = new CpdBasicRequest<>();

                CdpCompilationsRequest request = new CdpCompilationsRequest().build(cdpCompilation);
                Long time = System.currentTimeMillis()/1000;
                String sign = createSign(HttpClients.beanToMap(request),"85d8e11d74cdebc3b0605a049020a202d3f36293",time);

                basicRequest.setParam(request);
                basicRequest.setSignature(sign);
                basicRequest.setTimestamp(time);
                basicRequest.setAppId(cdpCompilation.getAppId());

                String rsp = HttpClients.POST(postUrl,basicRequest.buildJson(),true );
            });
        }
    }

    private static String createSign(Map<String, ?> paramsMap, String secret, Long time){
        StringBuilder paramNameValue = new StringBuilder();
        if (Objects.nonNull(paramsMap)) {
            Set<String> keySet = paramsMap.keySet();
            List<String> paramNames = new ArrayList<String>(keySet);
            Collections.sort(paramNames);
            for (String paramName : paramNames) {
                paramNameValue.append(paramName).append(paramsMap.get(paramName));
            }
        }
        String source = secret + paramNameValue.toString() + time;

        return Hashing.md5().newHasher().putString(source, Charsets.UTF_8).hash().toString();
    }
}
