package com.coohua.user.event.biz.user.mapper;

import com.coohua.user.event.biz.user.entity.UserCancelEntity;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2022/2/24
 */
public interface UserCancelMapper {

    @Select("select * from bp_user.user_cancel where source_type in ('cancelUser','shieldUserByPackage','signOutUser','UserDeviceCount')" +
            " and create_time >= #{createTime}")
    List<UserCancelEntity> queryPackageAndDevice(@Param("createTime")Date createTime);

    @Select("select COUNT(1) from bp_user.user_gray where user_id = #{userId}")
    Integer countIsGrayUser(@Param("userId") Long userId);

    @Select("select COUNT(1) from bp_user.user_gray where reason = #{reason} and create_time > #{time}")
    Integer countAlreadyGrayToday(@Param("reason")String reason,@Param("time")Long time);
}
