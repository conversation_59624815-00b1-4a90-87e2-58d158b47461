package com.coohua.user.event.biz.dc.mapper;

import com.coohua.user.event.biz.dc.entity.LockConfig;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * <p>
 * 产品锁区配置 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2023-07-06
 */
public interface LockConfigMapper extends BaseMapper<LockConfig> {

    @Select({"<script>",
            "select * from `user-event`.lock_config",
            "<if test ='null != product'>" ,
            "  where app_id = #{product} " ,
            "</if>" ,
            "limit #{offset},#{limit}" ,
            "</script>",
    })
    List<LockConfig> queryList(@Param("product") String product, @Param("offset")Integer offset, @Param("limit")Integer limit);

    @Select({"<script>",
            "select count(1) from `user-event`.lock_config",
            "<if test ='null != product'>" ,
            "  where app_id = #{product} " ,
            "</if>" ,
            "</script>",
    })
    Integer queryCount(@Param("product") String product);
}
