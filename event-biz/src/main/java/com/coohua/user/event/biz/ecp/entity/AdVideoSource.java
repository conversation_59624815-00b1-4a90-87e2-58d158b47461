package com.coohua.user.event.biz.ecp.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import java.util.Date;
import com.baomidou.mybatisplus.annotation.TableId;
import java.io.Serializable;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * 视频源数据
 * </p>
 *
 * <AUTHOR>
 * @since 2020-11-05
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value="AdVideoSource对象", description="视频源数据")
public class AdVideoSource implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "自增主键")
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    @ApiModelProperty(value = "视频id")
    private Integer videoId;

    @ApiModelProperty(value = "视频名称")
    private String videoName;

    @ApiModelProperty(value = "产品名称")
    private String appName;

    @ApiModelProperty(value = "剪辑时间")
    private Date editTime;

    @ApiModelProperty(value = "编导")
    private String director;

    @ApiModelProperty(value = "剪辑")
    private String editor;

    @ApiModelProperty(value = "演员")
    private String actor;

    @ApiModelProperty(value = "摄像")
    private String videographer;

    @ApiModelProperty(value = "文件唯一约束")
    private String eTag;

    @ApiModelProperty(value = "视频链接")
    private String videoUrl;

    @ApiModelProperty(value = "属性，用于关联视频与计划关系")
    private String videoProperty;

    @ApiModelProperty(value = "上线时间")
    private Date onlineTime;

    @ApiModelProperty(value = "交付时间")
    private Date deliveryTime;

    @ApiModelProperty(value = "创意列表")
    private String ideaInfo;

    @ApiModelProperty(value = "创建时间")
    private Date createTime;

    @ApiModelProperty(value = "更新时间")
    private Date updateTime;


}
