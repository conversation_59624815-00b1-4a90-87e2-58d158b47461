package com.coohua.user.event.biz.user.entity;

import lombok.Data;

/**
 * <AUTHOR>
 * @since 2020/9/17
 */
@Data
public class UserEntity {
    /**
     * id
     */
    private Long id;

    /**
     * 微信 unionid
     */
    private String unionId;

    /**
     * 手机号
     */
    private String mobile;

    /**
     * 头像
     */
    private String photoUrl;

    /**
     * 昵称
     */
    private String nickName;

    /**
     * 账户密码，暂时不用
     */
    private String password;

    /**
     * 0: 正常n1: 注销【不再允许注册】n2: 拉灰
     */
    private Integer state;

    /**
     * 0: androidn1: ios
     */
    private Integer os;

    /**
     * 手机品牌napple、huawei、xiaomi
     */
    private String brand;

    /**
     * 下载渠道
     */
    private String channel;

    /**
     * device_id
     */
    private String deviceId;

    /**
     * 1.0.0
     */
    private String appVersion;

    /**
     * 厂商 ui 版本号n华为： emui 5.0.2nios:
     */
    private String romVersion;

    /**
     * 操作系统版本 nios： 10.0.1nandroid： 8.0.9
     */
    private String osVersion;

    /**
     * 创建时间
     */
    private Long createTime;

    /**
     * 更新时间
     */
    private Long updateTime;
}
