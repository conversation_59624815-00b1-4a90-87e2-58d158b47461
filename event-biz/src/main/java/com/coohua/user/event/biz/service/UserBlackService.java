package com.coohua.user.event.biz.service;

import com.coohua.user.event.biz.click.entity.UserMacIpCheckEntity;
import com.coohua.user.event.biz.click.service.ClickHouseService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.List;

@Slf4j
@Service
public class UserBlackService {
    @Autowired
    private ClickHouseService clickHouseService;
    @Autowired
    private UserGrayService userGrayService;

    public void userBlackReqIdAbnormal() {
        List<UserMacIpCheckEntity> userList = clickHouseService.queryReqIdAbnormalUserList();

        if (CollectionUtils.isEmpty(userList)) {
            log.info("userBlackReqIdAbnormal userList is empty");
            return;
        }

        for (UserMacIpCheckEntity userInfo : userList) {
            // 拉黑
            userGrayService.doUserGray(userInfo.getUserIdStr(), userInfo.getDeviceIdStr(), "req_id异常拉黑", userInfo.getProduct());
        }
    }

    public void userMultiReqIdAbnormal() {
        List<UserMacIpCheckEntity> userList = clickHouseService.queryMultiReqIdAbnormalUserList();

        if (CollectionUtils.isEmpty(userList)) {
            log.info("userMultiReqIdAbnormal userList is empty");
            return;
        }

        for (UserMacIpCheckEntity userInfo : userList) {
            // 拉黑
            userGrayService.doUserGray(userInfo.getUserIdStr(), userInfo.getDeviceIdStr(), "单产品不同用户req_id重复-异常拉黑", userInfo.getProduct());
        }
    }
}
