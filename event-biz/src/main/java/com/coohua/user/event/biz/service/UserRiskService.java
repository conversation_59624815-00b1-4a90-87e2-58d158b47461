package com.coohua.user.event.biz.service;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.coohua.user.event.biz.ap.entity.ClickConfig;
import com.coohua.user.event.biz.ap.service.ClickConfigService;
import com.coohua.user.event.biz.ap.service.ExUserConfigService;
import com.coohua.user.event.biz.ap.vo.ExUserConfigVo;
import com.coohua.user.event.biz.ap.vo.ExUserRedisVo;
import com.coohua.user.event.biz.ap.vo.UserRiskLimitVo;
import com.coohua.user.event.biz.ap.vo.UserRiskVo;
import com.coohua.user.event.biz.click.entity.ProductEntity;
import com.coohua.user.event.biz.click.entity.UserMacIpCheckEntity;
import com.coohua.user.event.biz.click.entity.UserRiskLevelEntity;
import com.coohua.user.event.biz.click.entity.UserScoreEntity;
import com.coohua.user.event.biz.click.service.ClickHouseService;
import com.coohua.user.event.biz.enums.UserRiskEnums;
import com.coohua.user.event.biz.service.bean.OcpcEvent;
import com.coohua.user.event.biz.util.*;
import com.ctrip.framework.apollo.spring.annotation.ApolloJsonValue;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import redis.clients.jedis.Jedis;
import redis.clients.jedis.Pipeline;
import redis.clients.jedis.exceptions.JedisMovedDataException;
import top.zrbcool.pepper.boot.jediscluster.JedisClusterClient;
import top.zrbcool.pepper.boot.jediscluster.JedisClusterClientRefer;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2021/9/3
 */
@Slf4j
@Service
public class UserRiskService {

    @Autowired
    private ClickHouseService clickHouseService;
    @Autowired
    private ClickConfigService clickConfigService;

    @Autowired
    private UserGrayService userGrayService;
    @Autowired
    private TFUserService tfUserService;

    @JedisClusterClientRefer(namespace = "user-event")
    private JedisClusterClient jedisClusterClient;

    @JedisClusterClientRefer(namespace = "ap-cluster")
    private JedisClusterClient apJedisClusterClient;

    @ApolloJsonValue("${max.check.fail.count}")
    private Integer failnum;

    @Autowired
    private ExUserConfigService exUserConfigService;
    @Resource
    private UserIncomeAbnormalService userIncomeAbnormalService;

    private Map<Integer, ProductEntity> productHashMap = new HashMap<>();

    public Boolean switchUserAd(Integer appId, Long userId,Boolean switchAd){
        try {
            if (appId == null || userId == null){
                return false;
            }
            log.info("设置用户广告开关{}-{}={}",appId,userId,switchAd);
            String key = RedisUtil.buildUserReaderAd(appId.toString(),userId.toString());
            apJedisClusterClient.set(key,switchAd.toString());
            return true;
        }catch (Exception e){
            log.error("Set AP-Cluster Ex:",e);
            return false;
        }
    }

    public UserRiskVo queryUserRiskInfo(Integer appId,Long userId){
        UserRiskLevelEntity result = queryFromRedis(appId,userId);
        UserRiskVo vo = new UserRiskVo();
        if (result == null || appId == null || userId == null) {
            vo.setLevel(UserRiskEnums.NO_RISK.getLevel());
            vo.setDesc(UserRiskEnums.NO_RISK.getDesc());
            return vo;
        }

        UserRiskEnums userRiskEnums = UserRiskEnums.level(result.getLevel());
        vo.setLevel(userRiskEnums.getLevel());
        vo.setDesc(userRiskEnums.getDesc());
        return vo;
    }
    /**
     * description: 广告查询用户风险等级
     * date: 2021/9/23 16:20
     * params: [appId, userId]
    */
    public UserRiskVo queryUserDailyRiskInfo(Integer appId,Long userId){
        UserRiskLevelEntity result = queryRiskLevelFromRedis(appId,userId);
        UserRiskVo vo = new UserRiskVo();
        if (result == null || appId == null || userId == null) {
            vo.setLevel(UserRiskEnums.NO_RISK.getLevel());
            vo.setDesc(UserRiskEnums.NO_RISK.getDesc());
            return vo;
        }

        UserRiskEnums userRiskEnums = UserRiskEnums.level(result.getLevel());
        vo.setLevel(userRiskEnums.getLevel());
        vo.setDesc(userRiskEnums.getDesc());
        return vo;
    }

    /**
     * description: 刷新用户稳定标签,成功-记录等级;失败-记录失败次数
     * date: 2021/9/18 20:18
    */
    public UserRiskVo refreshUserDailyRisk(Integer appId, Long userId,Integer userLevel,Boolean check){
        UserRiskVo userRiskVo = new UserRiskVo();
        userRiskVo.setAppId(appId);
        userRiskVo.setUserId(userId);
        try {
            if (appId == null || userId == null || userLevel == null){
                userRiskVo.setRefreshRisk(false);
                return userRiskVo;
            }
            //过期时间
            Long time = DateUtil.getNowToNextDaySeconds();
            //校验成功
            if (check){
                UserRiskLevelEntity userRiskLevel = new UserRiskLevelEntity();
                userRiskLevel.setUserId(userId.toString());
                userRiskLevel.setAppId(appId);
                if(!UserRiskEnums.A_RISK.getLevel().equals(userLevel) || UserRiskEnums.B_RISK.getLevel().equals(userLevel)){
                    //等级不够不需要刷新
                    userRiskVo.setRefreshRisk(false);
                    return userRiskVo;
                }
                //实时A标签的用户设置稳定标签为B1
                if (UserRiskEnums.A_RISK.getLevel().equals(userLevel)){
                    userRiskLevel.setLevel(UserRiskEnums.B1_RISK.getLevel());
                    userRiskVo.setLevel(UserRiskEnums.B1_RISK.getLevel());
                    userRiskVo.setDesc(UserRiskEnums.B1_RISK.getDesc());
                }
                if (UserRiskEnums.B_RISK.getLevel().equals(userLevel)){
                    userRiskLevel.setLevel(UserRiskEnums.C1_RISK.getLevel());
                    userRiskVo.setLevel(UserRiskEnums.C1_RISK.getLevel());
                    userRiskVo.setDesc(UserRiskEnums.C1_RISK.getDesc());
                }
                log.info("设置用户稳定标签{}-{}={}",appId,userId,JSON.toJSONString(userRiskLevel));
                String key = RedisUtil.buildUserRiskDailyKey(userId.toString(),appId);
                jedisClusterClient.set(key,JSON.toJSONString(userRiskLevel));
                //设置过期时间,第二天失效
                jedisClusterClient.expire(key,time.intValue());
                userRiskVo.setRefreshRisk(true);
            }else {
                userRiskVo.setLevel(userLevel);
                //记录失败次数
                String key = RedisUtil.buildUserRiskFailCheckKey(userId.toString(),appId);
                long count = jedisClusterClient.incr(key);
                jedisClusterClient.expire(key,time.intValue());
                if (count >= (failnum == null?3:failnum)){ //3次不通过则拉黑
                    //禁止用户看广告
                    switchUserAd(appId, userId, false);
                    //拉黑
                    ProductEntity productEntity = productHashMap.get(appId);
                    if (productEntity == null){
                        productEntity = clickHouseService.queryProductByAppId(appId);
                    }
                    if (productEntity == null){
                        log.error("验证码超过验证次数拉黑,appId查询无产品信息:{}",appId);
                        userRiskVo.setRefreshRisk(false);
                        return userRiskVo;
                    }
                    String remark = "广告验证码超过验证次数-拉黑";
                    userGrayService.sendGray(String.valueOf(userId),productEntity.getProduct(),remark);
                }
            }
            userRiskVo.setRefreshRisk(true);
        }catch (Exception e){
            log.error("refreshUserDailyRisk Set AP-Cluster Ex:",e);
            userRiskVo.setRefreshRisk(false);
        }
        return userRiskVo;
    }

    private UserRiskLevelEntity queryFromRedis(Integer appId,Long userId){
        try {
            String result = jedisClusterClient.get(RedisUtil.buildUserRiskKey(userId.toString(),appId));
            if (Strings.noEmpty(result)){
                return JSON.parseObject(result,UserRiskLevelEntity.class);
            }
        }catch (Exception e){
            log.error("Exception e:",e);
        }
        return null;
    }

    private UserRiskLevelEntity queryRiskLevelFromRedis(Integer appId,Long userId){
        try {
            /*
             * description: 先查用户稳定标签,如果没有再次查询实时标签
            */
            String riskDailyResult = jedisClusterClient.get(RedisUtil.buildUserRiskDailyKey(userId.toString(),appId));
            if (Strings.noEmpty(riskDailyResult)){
                return JSON.parseObject(riskDailyResult,UserRiskLevelEntity.class);
            }
            String result = jedisClusterClient.get(RedisUtil.buildUserRiskKey(userId.toString(),appId));
            if (Strings.noEmpty(result)){
                return JSON.parseObject(result,UserRiskLevelEntity.class);
            }
        }catch (Exception e){
            log.error("Exception e:",e);
        }
        return null;
    }

    public UserRiskLimitVo queryUserLimit(Integer appId, Long userId){
        UserRiskLimitVo vo = new UserRiskLimitVo();
        vo.setRestrictUser(Boolean.FALSE);
        try {
            String res = jedisClusterClient.get(RedisUtil.buildUserExConfigKey(appId,userId.toString()));
            if (Strings.noEmpty(res)){
                ExUserRedisVo vox = JSON.parseObject(res,ExUserRedisVo.class);
                vo.setRewardRate(vox.getRewardRate());
                vo.setWithdrawRate(vox.getWithdrawRate());
                vo.setVideoLimit(vox.getVideoLimit());
                vo.setRuleId(vox.getRuleId());
                vo.setRestrictUser(Boolean.TRUE);
            }
        }catch (Exception e){
            log.error("Do Query UserLimit Error:",e);
        }
        return vo;
    }

    @ApolloJsonValue("${limit.white.skip.device:[\"865221048193532\"]}")
    private List<String> deviceIdList;

    public void refreshAllUserLimitIntoRedis(){
        log.info("开始处理异常用户集中筛查....");
        // 查询配置
        Map<Integer, List<ExUserConfigVo>> configMap = exUserConfigService.queryConfigMap();
        if (configMap.size() == 0){
            log.info("未查询到相关结果..");
            return;
        }
        // 查询对应用户
        configMap.forEach((appId,configList) ->{
            ProductEntity productEntity = AppConfig.appIdMap.get(Long.valueOf(appId));
            if (productEntity == null){
                return;
            }

            configList.sort(Comparator.comparing(ExUserConfigVo::getRuleLevel));
            configList.forEach(config ->{
                List<String> userIdList = new ArrayList<>();
                //        是否使用设备归因
                // 配置的产品列表为空或者包含当前产品，使用设备归因，否则走原来逻辑
                if (tfUserService.isUseDeviceGui(productEntity.getProduct())) {
                    try {
                        userIdList = getUserIdListByDeviceGui(config, productEntity, deviceIdList);
                    } catch (Exception e) {
                        log.error("refreshAllUserLimitIntoRedis {} {} {} 设备归因异常", appId, config, deviceIdList, e);
                    }

                } else {
                    userIdList = clickHouseService.queryExUserIdList(productEntity.getProduct(),
                            config.getChannelList(),
                            config.getIpList(),
                            config.getModelList(),
                            config.getPhoneList(),
                            config.getOcpcChannel(),deviceIdList);

                    if (CollectionUtils.isEmpty(userIdList)) return;

                    if (config.getOcpcType() != 1){
                        // 需要过滤投放-非投放用户
                        List<String> noTfList = tfUserService.queryNtfUser(productEntity.getProduct(),userIdList,"config_default_trace");
                        // 仅对投放生效
                        if (config.getOcpcType() == 2){
                            userIdList = userIdList.stream()
                                    .filter(id -> !noTfList.contains(id))
                                    .collect(Collectors.toList());
                            log.info("目标群体被覆盖为投放用户,共计{}个",userIdList.size());
                        }else if (config.getOcpcType() == 3){
                            // 仅对非投放生效
                            userIdList = noTfList;
                            log.info("目标群体被覆盖为非投放用户,共计{}个",userIdList.size());
                        }
                    }
                }

                if (userIdList.size() > 0){
                    log.info("产品-[{}],配置id-[{}],查询到{}个异常用户",productEntity.getProduct(),config.getRuleId(),userIdList.size());
                    // 写入Redis
                    saveIntoRedis(productEntity,config,userIdList);
                }
            });
        });
        log.info("完成处理异常用户集中筛查....");
    }

    private List<String> getUserIdListByDeviceGui(ExUserConfigVo config, ProductEntity productEntity, List<String> deviceIdList) {
        List<String> userIdList = new ArrayList<>();
        List<UserScoreEntity> userEntityList = clickHouseService.queryExUserEntityList(productEntity.getProduct(),
                config.getChannelList(),
                config.getIpList(),
                config.getModelList(),
                config.getPhoneList(),
                config.getOcpcChannel(),deviceIdList);
        if (CollectionUtils.isEmpty(userEntityList)) return userIdList;

        userIdList = userEntityList.stream().map(UserScoreEntity::getUserId).collect(Collectors.toList());

        if (config.getOcpcType() != 1){

            List<OcpcEvent> ocpcEventList = tfUserService.queryUserDeviceGuiBatch(userEntityList);


            // 仅对投放生效
            if (config.getOcpcType() == 2){
                log.info("目标群体通过设备归因被覆盖为投放用户,共计{}个",userIdList.size());

                userIdList = ocpcEventList.stream().map(OcpcEvent::getUserId).collect(Collectors.toList());
            }else if (config.getOcpcType() == 3){
                // 仅对非投放生效
                log.info("目标群体通过设备归因被覆盖为非投放用户,共计{}个",userIdList.size());

                userIdList = userIdList.stream()
                        .filter(userId -> !ocpcEventList.stream().anyMatch(event -> event.getUserId().equals(userId)))
                        .collect(Collectors.toList());
            }
        }

        if (tfUserService.isDeviceGuiLogEnabled) {
            log.info("UserRiskService设备归因 {} {} {} {}", productEntity.getProduct(), userIdList, config.getOcpcType(), userEntityList);
        }

        return userIdList;
    }

    private void saveIntoRedis(ProductEntity productEntity,ExUserConfigVo configVo,List<String> userIdList){
        Map<String,String> saveMap = userIdList.stream().collect(Collectors.toMap(
                uid-> RedisUtil.buildUserExConfigKey(productEntity.getId(),uid),
                uid->{
                    ExUserRedisVo vo = new ExUserRedisVo();
                    vo.setAppId(productEntity.getId());
                    vo.setProduct(productEntity.getProduct());
                    vo.setRewardRate(configVo.getRewardLimit());
                    vo.setWithdrawRate(configVo.getWithdrawLimit());
                    vo.setVideoLimit(configVo.getVideoLimit());
                    vo.setUserId(uid);
                    vo.setRuleId(configVo.getRuleId());
                    return JSON.toJSONString(vo);
                },
                (key1,key2) -> key1
        ));
        try (Jedis jedis = jedisClusterClient.getResource("{risk:slot}:")) {
            Pipeline pipeline = jedis.pipelined();
            saveMap.forEach((k,v)->{
                pipeline.set(k,v);
                // 过期时间24小时
                pipeline.expire(k,60*60*24);
            });
            pipeline.sync();
        } catch (JedisMovedDataException jmde) {
            jedisClusterClient.get("EmptyKey");
        }
    }

    public void refreshAllUserRiskIntoRedis(){
        log.info("开始更新用户风险等级......");
        Map<Integer, ClickConfig> clickConfigMap =  clickConfigService.queryConfigRange(null);
        Map<Integer, Integer> queryRangMap = clickConfigService.convertExposureQueryRange(clickConfigMap);
        clickConfigMap.forEach((exposure, clickConfig) ->{
            Integer exposureStart = queryRangMap.get(exposure);
            List<UserRiskLevelEntity> riskAList = clickHouseService.queryUserRiskAList(exposureStart,exposure,
                    clickConfig.getClickA(),UserRiskEnums.A_RISK.getLevel());
            List<UserRiskLevelEntity> riskBList = clickHouseService.queryUserRiskBList(exposureStart,exposure,
                    clickConfig.getClickA(),clickConfig.getClickB(),UserRiskEnums.B_RISK.getLevel());
            // 处理完成风险B级别
            saveUserRiskToRedis(riskBList);
            // B-降级释放广告
            // 处理完成风险A级别
            saveUserRiskToRedis(riskAList);
        });
        log.info("更新用户风险等级完成......");
        //产品信息更新
        List<ProductEntity> productEntities = clickHouseService.queryAllProduct();
        productHashMap = productEntities.stream().
                collect(Collectors.toMap( product -> product.getId(),product -> product,(oldValue, newValue) -> newValue));
    }

    private void saveUserRiskToRedis(List<UserRiskLevelEntity> riskList){
        if (Lists.noEmpty(riskList)){
            Map<String,String> saveMap = riskList.stream().collect(Collectors.toMap(
                    r-> RedisUtil.buildUserRiskKey(r.getUserId(),r.getAppId()),
                    JSON::toJSONString,
                    (key1,key2) -> key1
            ));
            try (Jedis jedis = jedisClusterClient.getResource("{risk:slot}:")) {
                Pipeline pipeline = jedis.pipelined();
                saveMap.forEach((k,v)->{
                    pipeline.set(k,v);
                    // 过期时间2小时
                    pipeline.expire(k,60*60*2);
                });
                pipeline.sync();
            } catch (JedisMovedDataException jmde) {
                jedisClusterClient.get("EmptyKey");
            }
        }
    }

    public void refreshAllExIpUser(String param) {
        int count = 10;
        if (StringUtils.isNotBlank(param)) {
            count = Integer.parseInt(param);
        }

        List<UserMacIpCheckEntity> userList = clickHouseService.queryAllExIpUser(count);

        if (CollectionUtils.isEmpty(userList)){
            log.info("未查询到异常IP用户");
            return;
        }

        List<String> keys = userList.stream().filter(e -> !"************".equals(e.getIp())).map(e -> RedisKeyConstants.getExIpKey(e.getOs(), e.getProduct(), e.getIp())).collect(Collectors.toList());

        userIncomeAbnormalService.batchSaveToRedis(keys, "{user:ex:ip}", RedisKeyConstants.EXPIRE_ONE_DAYS);
    }
}
