package com.coohua.user.event.biz.dc.service;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.coohua.user.event.biz.click.entity.ProductEntity;
import com.coohua.user.event.biz.dc.entity.UserCallBackIntercept;
import com.coohua.user.event.biz.dc.mapper.UserCallBackInterceptMapper;
import com.coohua.user.event.biz.util.AppConfig;
import org.apache.commons.collections.CollectionUtils;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.io.File;
import java.io.FileOutputStream;
import java.time.LocalDateTime;
import java.util.List;
import java.util.concurrent.CompletableFuture;

/**
* <AUTHOR>
* @description 针对表【user_call_back_intercept(异常用户激活回传拦截)】的数据库操作Service实现
* @createDate 2025-02-25 16:39:54
*/
@Service
public class UserCallBackInterceptService extends ServiceImpl<UserCallBackInterceptMapper, UserCallBackIntercept> {

    @Resource(name = "callBackInterceptTaskPool")
    private ThreadPoolTaskExecutor callBackInterceptTaskPool;
    @Resource
    private UserCallBackInterceptMapper userCallBackInterceptMapper;

    public void saveSync(String callBackUserStr) {
        CompletableFuture.runAsync(() -> {
            UserCallBackIntercept userCallBackIntercept = JSONObject.parseObject(callBackUserStr, UserCallBackIntercept.class);
            userCallBackIntercept.setCreateTime(LocalDateTime.now());
            userCallBackIntercept.setUpdateTime(LocalDateTime.now());
            userCallBackInterceptMapper.insert(userCallBackIntercept);
        }, callBackInterceptTaskPool);

    }

    public List<UserCallBackIntercept> selectInterceptUserList(String todayStr) {
        List<UserCallBackIntercept> list = userCallBackInterceptMapper.selectInterceptUserList(todayStr);
        if (CollectionUtils.isEmpty(list)) {
            return list;
        }

        for (UserCallBackIntercept interceptUser : list) {
            ProductEntity entity = AppConfig.productEnMap.getOrDefault(interceptUser.getProduct(), new ProductEntity());

            interceptUser.setProductName(entity.getProductName());
        }

        return list;
    }

    public File exportExcel(String filePath, String fileName, List<UserCallBackIntercept> list) {

        if (System.getProperty("os.name").toLowerCase().contains("mac os")) {
            filePath = "/Users/<USER>/Downloads";
        }

        //文件保存位置
        File saveDir = new File(filePath);
        if (!saveDir.exists()) {
            saveDir.mkdir();
        }
        File file = new File(saveDir + File.separator + fileName + ".xlsx");
        Workbook workbook = new XSSFWorkbook();
        try {

            Sheet sheet = workbook.createSheet("UserCallBackIntercept");

            // Create header row
            Row headerRow = sheet.createRow(0);
            headerRow.createCell(0).setCellValue("用户id");
            headerRow.createCell(1).setCellValue("os");
            headerRow.createCell(2).setCellValue("产品名称");
            headerRow.createCell(3).setCellValue("产品打点名");
            headerRow.createCell(4).setCellValue("渠道");
            headerRow.createCell(5).setCellValue("来源");
            headerRow.createCell(6).setCellValue("账户id");
            headerRow.createCell(7).setCellValue("备注");
            headerRow.createCell(8).setCellValue("创建时间");
            headerRow.createCell(9).setCellValue("更新时间");

            // Fill data rows
            int rowNum = 1;
            for (UserCallBackIntercept userCallBackIntercept : list) {
                Row row = sheet.createRow(rowNum++);
                row.createCell(0).setCellValue(userCallBackIntercept.getUserId());
                row.createCell(1).setCellValue(userCallBackIntercept.getOs());
                row.createCell(2).setCellValue(userCallBackIntercept.getProductName());
                row.createCell(3).setCellValue(userCallBackIntercept.getProduct());
                row.createCell(4).setCellValue(userCallBackIntercept.getChannel());
                row.createCell(5).setCellValue(userCallBackIntercept.getSource());
                row.createCell(6).setCellValue(userCallBackIntercept.getAccountId());
                row.createCell(7).setCellValue(userCallBackIntercept.getRemark());
                row.createCell(8).setCellValue(userCallBackIntercept.getCreateTime().toString());
                row.createCell(9).setCellValue(userCallBackIntercept.getUpdateTime().toString());
            }

            try {
                FileOutputStream out = new FileOutputStream(file);
                workbook.write(out);
            } catch (Exception e) {
                e.printStackTrace();
            }

        } catch (Exception e) {
            e.printStackTrace();
        }
        return file;
    }
}




