package com.coohua.user.event.biz.config;

import com.alibaba.csp.sentinel.concurrent.NamedThreadFactory;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;

import java.util.concurrent.ArrayBlockingQueue;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;

@Configuration
@Slf4j
public class ThreadPoolConf {

	@Bean(name = "partitionJobTaskPool")
	public ThreadPoolTaskExecutor productPoolConf(){
		ThreadPoolTaskExecutor pool = new ThreadPoolTaskExecutor();
		//核心线程数目
		pool.setCorePoolSize(16);
		//指定最大线程数
		pool.setMaxPoolSize(200);
		//队列中最大的数目
		pool.setQueueCapacity(5000);
		//线程名称前缀
		pool.setThreadNamePrefix("partitionJobTaskPool_");
		//rejection-policy：当pool已经达到max size的时候，如何处理新任务
		// useDiscard 直接抛弃
		pool.setRejectedExecutionHandler(new ThreadPoolExecutor.DiscardPolicy());// + discard 策略
		//线程空闲后的最大存活时间
		pool.setKeepAliveSeconds(30);
		pool.initialize();
		log.info("partitionJobTaskPool 已初始化");
		return pool;
	}

	@Bean(name = "callBackInterceptTaskPool")
	public ThreadPoolTaskExecutor callBackInterceptPoolConf(){
		ThreadPoolTaskExecutor pool = new ThreadPoolTaskExecutor();
		//核心线程数目
		pool.setCorePoolSize(Runtime.getRuntime().availableProcessors());
		//指定最大线程数
		pool.setMaxPoolSize(Runtime.getRuntime().availableProcessors() * 2);
		//队列中最大的数目
		pool.setQueueCapacity(5000);
		//线程名称前缀
		pool.setThreadNamePrefix("CallBackInterceptTaskPool_");
		//rejection-policy：当pool已经达到max size的时候，如何处理新任务
		pool.setRejectedExecutionHandler(new ThreadPoolExecutor.CallerRunsPolicy());// + discard 策略
		//线程空闲后的最大存活时间
		pool.setKeepAliveSeconds(30);
		pool.initialize();
		log.info("callBackInterceptTaskPool 已初始化");
		return pool;
	}

	@Bean(name = "syncWithdrawTaskPool")
	public ThreadPoolTaskExecutor syncWithdrawTaskPool(){
		ThreadPoolTaskExecutor pool = new ThreadPoolTaskExecutor();
		//核心线程数目
		pool.setCorePoolSize(Runtime.getRuntime().availableProcessors());
		//指定最大线程数
		pool.setMaxPoolSize(Runtime.getRuntime().availableProcessors() * 2);
		//队列中最大的数目
		pool.setQueueCapacity(5000);
		//线程名称前缀
		pool.setThreadNamePrefix("SyncWithdrawTaskPool_");
		//rejection-policy：当pool已经达到max size的时候，如何处理新任务
		pool.setRejectedExecutionHandler(new ThreadPoolExecutor.CallerRunsPolicy());// + discard 策略
		//线程空闲后的最大存活时间
		pool.setKeepAliveSeconds(30);
		pool.initialize();
		log.info("syncWithdrawTaskPool 已初始化");
		return pool;
	}

	public static final ThreadPoolExecutor RELEASE_CAN_SEND_MALL = new ThreadPoolExecutor(Runtime.getRuntime().availableProcessors() * 6, Runtime.getRuntime().availableProcessors() * 12,60L, TimeUnit.SECONDS,
			new ArrayBlockingQueue<>(5000), new NamedThreadFactory("releaseCanSendMall",false),new ThreadPoolExecutor.CallerRunsPolicy());

	public static final ThreadPoolExecutor RELEASE_CAN_SEND_MALL_BY_PRODUCT = new ThreadPoolExecutor(Runtime.getRuntime().availableProcessors() * 2, Runtime.getRuntime().availableProcessors() * 4,60L, TimeUnit.SECONDS,
			new ArrayBlockingQueue<>(500), new NamedThreadFactory("releaseCanSendMall",false),new ThreadPoolExecutor.CallerRunsPolicy());



}
