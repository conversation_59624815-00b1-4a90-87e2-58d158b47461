package com.coohua.user.event.biz.click.entity;

import com.alibaba.fastjson.JSON;
import com.aliyun.openservices.shade.com.google.common.base.Charsets;
import com.coohua.user.event.biz.util.DateUtil;
import com.google.common.hash.Hashing;
import lombok.Data;

import java.nio.charset.Charset;
import java.sql.Timestamp;
import java.util.Date;

@Data
public class LBSBean {
    private String product;
    private String deviceId;
    private Long userId;
    private String imei;
    private String oaid;
    private String mac;
    private String ip;
    private String city;
    private String androidId;
    private String model;
    private String brand;
    private String appVersion;
    private String channel;
    private String osVersion;
    private String appId;

    private java.sql.Date logday;
    private Date createTime;

    public static LBSBean parseFromJs(String js){
        return JSON.parseObject(js,LBSBean.class);
    }

    public Timestamp getCrTime(){
        return new Timestamp(createTime.getTime());
    }

    public String buildRedisKey(){
        return String.format("{lbs}:has:saved:%s:%s:%s", DateUtil.dateToString(createTime),this.product,this.userId);
    }

    public String getSaveHash(){
        return  Hashing.sha256()
                .newHasher()
                .putString(String.format("%s:%s:%s:%s:%s",DateUtil.dateToString(createTime),product,userId,ip,city), Charsets.UTF_8)
                .hash().toString();
    }
}
