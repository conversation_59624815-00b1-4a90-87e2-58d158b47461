package com.coohua.user.event.biz.dc.mapper;

import com.coohua.user.event.biz.dc.entity.DailyResultEntity;
import org.apache.ibatis.annotations.Delete;
import org.apache.ibatis.annotations.Insert;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @since 2020/9/7
 */
public interface DailyResultMapper {


    @Select({"select group_name,sum(IFNULL(total_income,0) - IFNULL(withdraw_money,0) - IFNULL(toufang_money,0)) as money from ads.daily_result where logday1 = #{logDay} group by group_name "})
    List<Map<String,Double>> queryDailyAmount(@Param("logDay")String logDay);
}
