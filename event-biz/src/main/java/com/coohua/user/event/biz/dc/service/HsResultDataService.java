package com.coohua.user.event.biz.dc.service;

import com.coohua.user.event.biz.dc.entity.HsResultData;
import com.coohua.user.event.biz.dc.mapper.HsResultDataMapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;

import java.util.List;

/**
* <p>
    *  服务实现类
    * </p>
*
* <AUTHOR>
* @since 2023-06-28
*/
@Service
public class HsResultDataService extends ServiceImpl<HsResultDataMapper, HsResultData> {
    public List<HsResultData> queryByLogday(String logday){
        return baseMapper.queryByLogday(logday);
    }
}
