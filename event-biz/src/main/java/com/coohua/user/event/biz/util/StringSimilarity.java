package com.coohua.user.event.biz.util;

import lombok.extern.slf4j.Slf4j;

@Slf4j
public class StringSimilarity {
    
    /**
     * 默认相似度阈值(70%)
     */
    private static final double DEFAULT_SIMILARITY_THRESHOLD = 0.70;

    /**
     * 计算两个字符串的编辑距离
     */
    private static int levenshteinDistance(String str1, String str2) {
        int[][] distance = new int[str1.length() + 1][str2.length() + 1];
        
        for (int i = 0; i <= str1.length(); i++) {
            distance[i][0] = i;
        }
        for (int j = 0; j <= str2.length(); j++) {
            distance[0][j] = j;
        }
        
        for (int i = 1; i <= str1.length(); i++) {
            for (int j = 1; j <= str2.length(); j++) {
                if (str1.charAt(i - 1) == str2.charAt(j - 1)) {
                    distance[i][j] = distance[i - 1][j - 1];
                } else {
                    distance[i][j] = Math.min(
                            Math.min(distance[i - 1][j], distance[i][j - 1]),
                            distance[i - 1][j - 1]
                    ) + 1;
                }
            }
        }
        
        return distance[str1.length()][str2.length()];
    }

    /**
     * 计算两个字符串的相似度(0-1之间)
     */
    public static double similarity(String str1, String str2) {
        if (Strings.isEmpty(str1) || Strings.isEmpty(str2)) {
            return 0.0;
        }
        
        if (str1.equals(str2)) {
            return 1.0;
        }
        
        int maxLength = Math.max(str1.length(), str2.length());
        if (maxLength == 0) {
            return 1.0;
        }
        
        return 1.0 - ((double) levenshteinDistance(str1, str2)) / maxLength;
    }

    /**
     * 判断两个字符串是否相似（使用默认阈值70%）
     *
     * @param str1 第一个字符串
     * @param str2 第二个字符串
     * @return 如果相似度>=70%返回true，否则返回false
     */
    public static boolean isSimilar(String str1, String str2) {
        return isSimilar(str1, str2, DEFAULT_SIMILARITY_THRESHOLD);
    }

    /**
     * 判断两个字符串是否相似
     *
     * @param str1 第一个字符串
     * @param str2 第二个字符串
     * @param threshold 相似度阈值(0.0-1.0)
     * @return 如果相似度>=阈值返回true，否则返回false
     */
    public static boolean isSimilar(String str1, String str2, double threshold) {
        if (threshold < 0.0 || threshold > 1.0) {
            throw new IllegalArgumentException("相似度阈值必须在0.0-1.0之间");
        }
        return similarity(str1, str2) >= threshold;
    }
}