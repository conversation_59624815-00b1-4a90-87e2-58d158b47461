package com.coohua.user.event.biz.click.entity;

import com.alibaba.fastjson.JSON;
import com.coohua.user.event.biz.util.Strings;
import lombok.Data;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2022/10/11
 */
@Data
public class UserCsjEcpmExEntity {
    private String product;
    private String exChannel;
    private String exManufacturer;
    private String exModel;
    private String exType;

    public List<String> getExChannelList(){
        if (Strings.isEmpty(this.exChannel)){
            return new ArrayList<>();
        }

        return convertList(this.exChannel);
    }

    public List<String> getExManufacturerList(){
        if (Strings.isEmpty(this.exManufacturer)){
            return new ArrayList<>();
        }

        return convertList(this.exManufacturer);
    }

    public List<String> getExModelList(){
        if (Strings.isEmpty(this.exModel)){
            return new ArrayList<>();
        }

        return convertList(this.exModel);
    }

    private List<String> convertList(String rs){
        return JSON.parseArray(rs,String.class).stream().filter(Strings::noEmpty).collect(Collectors.toList());
    }
}
