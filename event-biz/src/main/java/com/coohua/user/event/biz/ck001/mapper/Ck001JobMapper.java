package com.coohua.user.event.biz.ck001.mapper;

import com.coohua.user.event.biz.click.entity.UserMacIpCheckEntity;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

public interface Ck001JobMapper {

    @Select({"<script>",
            "select product,\n" +
                    "       arrayFilter(r->r != '0', groupUniqArray(userid)) as userIdStr\n" +
                    "from (\n" +
                    "         select product,\n" +
                    "                userid,\n" +
                    "                device_id,\n" +
                    "                floor(sum(toFloat32OrZero(extend1)) / 1000, 2)                                             as inc,\n" +
                    "                floor(sum(if(lower(ad_type_name) like '%广点通%', toFloat64OrZero(extend1), 0)) / 1000, 2) as gdt_inc,\n" +
                    "                floor(gdt_inc / inc, 4)                                                                    as gdt_inc_rate\n" +
                    "         from (select product, userid, device_id, pos_id, extend1\n" +
                    "               from ods.event_exposure_dist\n" +
                    "               where os = 'android'\n" +
                    "                 and logday >= today() - 1\n" +
                    "<choose>" +
                        "<when test='null != productList and productList.size() > 0'>" +
                        "                 and product in \n" +
                        "                 <foreach collection='productList' item='product' index='index' open='(' separator=',' close=')'>\n" +
                        "                   #{product}\n" +
                        "                 </foreach>\n" +
                        "</when>" +
                        "<otherwise>" +
                        "                 and product global in (select product from dwd.product_map_dist where product_group = '项目十组')\n" +
                        "</otherwise>" +
                    "</choose>" +
                    "                 and ad_action = 'exposure'\n" +
                    "               group by product, userid, device_id, time, pos_id, extend1\n" +
                    "                  ) a\n" +
                    "                  global\n" +
                    "                  left join (select distinct pos_id, ad_type_name, type_name from dwd.product_ad_conf_dist where lower(ad_type_name) like '%广点通%') b\n" +
                    "                            on a.pos_id = b.pos_id\n" +
                    "         group by product, userid, device_id\n" +
                    "         having inc > #{inc}\n" +
                    "            and gdt_inc_rate > #{rate}) res\n" +
                    "group by product",
            "</script>"})
    List<UserMacIpCheckEntity> queryGdtGroup10UserInfo(@Param("productList") List<String> productList, @Param("rate") Double rate, @Param("inc") Double inc);

    @Select("select product,\n" +
            "       arrayFilter(r->r != '0', groupUniqArray(userid)) as userIdStr\n" +
            "from (\n" +
            "         select product,\n" +
            "                userid,\n" +
            "                device_id,\n" +
            "                floor(sum(toFloat32OrZero(extend1)) / 1000, 2)                                             as inc,\n" +
            "                floor(sum(if(lower(ad_type_name) like '%sigmob%', toFloat64OrZero(extend1), 0)) / 1000, 2) as sig_inc,\n" +
            "                floor(sig_inc / inc, 4)                                                                    as sig_inc_rate\n" +
            "         from (select product, userid, device_id, time, pos_id, extend1\n" +
            "               from ods.event_exposure_dist\n" +
            "               where os = 'android' and event = 'AdData'\n" +
            "                 and logday >= today() - 4\n" +
            "                 and product global in (select product from dwd.product_map_dist where product_group = #{group})\n" +
            "                 and channel like '%ksdr%'\n" +
            "                 and ad_action = 'exposure'\n" +
            "               group by product, userid, device_id, time, pos_id, extend1\n" +
            "                  ) a\n" +
            "                  global\n" +
            "                  left join (select distinct pos_id, ad_type_name, type_name from dwd.product_ad_conf_dist) b\n" +
            "                            on a.pos_id = b.pos_id\n" +
            "         group by product, userid, device_id\n" +
            "         having inc > 5\n" +
            "            and sig_inc_rate > 0.8) res\n" +
            "group by product")
    List<UserMacIpCheckEntity> querySgmGroup10UserInfo(@Param("group") String group);
}
