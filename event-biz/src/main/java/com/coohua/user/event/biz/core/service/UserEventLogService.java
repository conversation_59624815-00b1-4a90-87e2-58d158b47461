package com.coohua.user.event.biz.core.service;

import com.alibaba.fastjson.JSONObject;
import com.coohua.bp.user.remote.dto.CommonHeaderDTO;
import com.coohua.user.event.biz.cache.UserEventLogCacheService;
import com.coohua.user.event.biz.core.dto.UserEventLogEntity;
import com.coohua.user.event.biz.core.entity.UserEventLog;
import com.coohua.user.event.biz.core.mapper.UserEventCountMapper;
import com.coohua.user.event.biz.core.mapper.UserEventLogMapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.coohua.user.event.biz.enums.CacheKeyConstants;
import com.coohua.user.event.biz.user.mapper.BpUserMapper;
import com.coohua.user.event.biz.util.AccessKeyUtils;
import com.coohua.user.event.biz.util.DateUtil;
import lombok.extern.slf4j.Slf4j;
import org.checkerframework.checker.units.qual.A;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.util.*;

/**
* <p>
    *  服务实现类
    * </p>
*
* <AUTHOR>
* @since 2020-09-04
*/
@Service
@Slf4j
@ConditionalOnProperty(name = "spring.application.name",havingValue = "user-event")
public class UserEventLogService extends ServiceImpl<UserEventLogMapper, UserEventLog> {
	@Autowired
	private UserEventLogCacheService cacheService;
	@Resource
	private UserEventLogMapper userEventLogMapper;

	/**
	 * 记录用户服务请求日志
	 * @param commonHeaderDTO
	 */
	public void saveUserLogByCommonHeader(CommonHeaderDTO commonHeaderDTO) {
		if(StringUtils.isEmpty(commonHeaderDTO.getAccessKey())){
			return;
		}
		String[] accessKeyArray = commonHeaderDTO.getAccessKey().split("_");
		//accessKey非法
		if(accessKeyArray.length!=2){
			return;
		}
		long userId = Long.parseLong(accessKeyArray[1]);
		Long userCreateTime = cacheService.getUserCreateTime(commonHeaderDTO.getAppId(), String.valueOf(userId));
		// 用户创建时间信息不存在直接返回
		if (userCreateTime == null || CacheKeyConstants.DEFAULT_LONG_EMPTY.equals(userCreateTime)) {
			return;
		}
		UserEventLog userEventLog = new UserEventLog();
		if(!StringUtils.isEmpty(commonHeaderDTO.getAppId())) {
			userEventLog.setAppId(Integer.valueOf(commonHeaderDTO.getAppId()));
		}
		userEventLog.setUserId(Long.valueOf(userId));
		userEventLog.setDeviceId(commonHeaderDTO.getDeviceId());
		userEventLog.setChannel(commonHeaderDTO.getChannel());
		userEventLog.setIntOs(commonHeaderDTO.getIntOs());
		if(!StringUtils.isEmpty(commonHeaderDTO.getPkgId())) {
			userEventLog.setPkgId(Integer.valueOf(commonHeaderDTO.getPkgId()));
		}
		//用户创建时间
		userEventLog.setUserCreateTime(new Date(userCreateTime));
		save(userEventLog);
	}

	public void saveUserLogForCheckAuth(String userId, String appId) {
		Long userCreateTime = cacheService.getUserCreateTime(appId, userId);
		if (userCreateTime == null || CacheKeyConstants.DEFAULT_LONG_EMPTY.equals(userCreateTime)) {
			return;
		}
		UserEventLog userEventLog = new UserEventLog();
		userEventLog.setAppId(Integer.valueOf(appId));
		userEventLog.setUserId(Long.valueOf(userId));
		//用户创建时间
		userEventLog.setUserCreateTime(new Date(userCreateTime));
		saveToday(userEventLog);
	}

	private void saveToday(UserEventLog userEventLog){
		String tableName =  "user_event_log_" + DateUtil.dateToString(new Date(),DateUtil.ISO_DATE_FORMAT);
		UserEventLogEntity userEventLogEntity = new UserEventLogEntity();
		BeanUtils.copyProperties(userEventLog,userEventLogEntity);
		userEventLogEntity.setTableName(tableName);
		userEventLogMapper.insertEvent(userEventLogEntity);
	}

}
