package com.coohua.user.event.biz.core.mapper;

import com.coohua.user.event.biz.core.entity.UserDeviceCount20250506;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;

import java.util.List;

/**
* <AUTHOR>
* @description 针对表【user_device_count_20250506(用户设备数表-临时)】的数据库操作Mapper
* @createDate 2025-05-06 15:57:08
* @Entity com.coohua.user.event.biz.core.entity.UserDeviceCount20250506
*/
public interface UserDeviceCount20250506Mapper extends BaseMapper<UserDeviceCount20250506> {

    @Select("select * from `user-event`.user_device_count_20250506")
    List<UserDeviceCount20250506> selectAll();

    @Update("update `user-event`.user_device_count_20250506 set model_str = #{modelStr},imei_str = #{imeiStr},device_str= #{deviceStr},android_str=#{androidStr},oaidStr=#{oaidStr},max_device_count=#{maxDeviceCount} where id = #{id}")
    Integer updateDevice(UserDeviceCount20250506 count20250506);
}




