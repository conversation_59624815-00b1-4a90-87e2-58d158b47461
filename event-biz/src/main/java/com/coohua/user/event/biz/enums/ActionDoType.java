package com.coohua.user.event.biz.enums;

/**
 * <AUTHOR>
 * @since 2022/1/26
 */
public enum ActionDoType {

    ECPM_CHECK(1,"ECPM"),
    RULE_CHECK(2,"RULE")
    ;

    private Integer type;
    private String desc;

    ActionDoType(Integer type, String desc) {
        this.type = type;
        this.desc = desc;
    }

    public Integer getType() {
        return type;
    }

    public void setType(Integer type) {
        this.type = type;
    }

    public String getDesc() {
        return desc;
    }

    public void setDesc(String desc) {
        this.desc = desc;
    }
}
