package com.coohua.user.event.biz.toufang.mapper;

import com.coohua.user.event.biz.toufang.entity.CoreEventEntity;
import com.coohua.user.event.biz.toufang.entity.UserEventModelEntity;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2020/9/19
 */
public interface CoreEventMapper {

    @Select({"SELECT" +
            " dsp," +
            " product," +
            " os," +
            " click_id," +
            " user_event_id," +
            " cid," +
            " cid_name," +
            " group_id," +
            " group_name," +
            " ad_id," +
            " aid_name," +
            " account_id," +
            " account_name," +
            " device_id," +
            " oa_id," +
            " log_date," +
            " log_hour," +
            " count(event1_type) 'event1_type'," +
            " count(event2_type) 'event2_type'," +
            " count(event3_type) 'event3_type'," +
            " count(event4_type) 'event4_type'," +
            " count(event5_type) 'event5_type'" +
            " FROM" +
            " `coo-dispense`.`core_event`" +
            " WHERE" +
            " log_date = #{logDay}" +
            " and del_flag = 0" +
            " GROUP BY product,dsp,os"})
    List<CoreEventEntity> queryCoreEvent(@Param("logDay")String logDay);


    @Select({
            "SELECT" +
                    " dsp," +
                    " product," +
                    " os," +
                    " count(DISTINCT ocpc_device_id) as count_device_id" +
                    " FROM " +
                    " `coo-dispense`.`user_event` " +
                    " WHERE " +
                    " dsp = 'kuaishou' " +
                    " and create_time BETWEEN #{begin} and #{end}" +
                    " and event_type = 0" +
                    " and req_url not like '%&is_direct_match=false'" +
                    " group by product,os"
    })
    List<UserEventModelEntity> queryUserEventKuaiShou(@Param("begin") Date begin,@Param("end") Date end);

    @Select({
            "SELECT" +
                    " dsp," +
                    " product," +
                    " os," +
                    " count(DISTINCT ocpc_device_id) as count_device_id" +
                    " FROM" +
                    " `coo-dispense`.`user_event`" +
                    " WHERE" +
                    " dsp != 'kuaishou' " +
                    " and create_time BETWEEN #{begin} and #{end}" +
                    " and event_type = 0" +
                    " group by dsp,product,os"
    })
    List<UserEventModelEntity> queryUserEventOther(@Param("begin") Date begin,@Param("end") Date end);
}
