package com.coohua.user.event.biz.dc.dto;

import lombok.Data;
import org.apache.directory.api.util.Strings;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2022/1/25
 */
@Data
public class ActionRuleDto {
    private Integer actionType;
    private Integer type;
    private Integer appId;
    private String product;
    private Integer minEcpm;
    private Integer maxEcpm;
    private Integer videoTimes;
    private Integer videoLimit;
    private Integer withdrawLimit;
    private Integer rewardLimit;
    private List<Integer> platformLimit;
    private List<Integer> platformLimitSkip;

    private String channels;
    private String models;
    private String brands;
    private String ips;
    private Integer ocpcType;
    private Integer newUserType;
    private String sources;

    private String name;

    public List<String> getChannelList(){
        if (Strings.isEmpty(this.channels)){
            return new ArrayList<>();
        }
        return Arrays.stream(this.channels.split(",")).filter(Strings::isNotEmpty).collect(Collectors.toList());
    }

    public List<String> getModelList(){
        if (Strings.isEmpty(this.models)){
            return new ArrayList<>();
        }

        if (this.models.contains("iPhone")){
            return Arrays.stream(this.models.split("[|]")).filter(Strings::isNotEmpty).collect(Collectors.toList());

        }
        return Arrays.stream(this.models.split(",")).filter(Strings::isNotEmpty).collect(Collectors.toList());
    }

    public List<String> getBrandList(){
        if (Strings.isEmpty(this.brands)){
            return new ArrayList<>();
        }
        return Arrays.stream(this.brands.split(",")).filter(Strings::isNotEmpty).collect(Collectors.toList());
    }

    public List<String> getIpList(){
        if (Strings.isEmpty(this.ips)){
            return new ArrayList<>();
        }
        return Arrays.stream(this.ips.split(",")).filter(Strings::isNotEmpty).collect(Collectors.toList());
    }

    public List<String> getSourceList(){
        if (Strings.isEmpty(this.sources)){
            return new ArrayList<>();
        }
        return Arrays.stream(this.sources.split(",")).filter(Strings::isNotEmpty).collect(Collectors.toList());
    }
}
