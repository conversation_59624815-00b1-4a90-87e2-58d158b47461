package com.coohua.user.event.biz.core.entity;

import java.math.BigDecimal;
import com.baomidou.mybatisplus.annotation.IdType;
import java.util.Date;
import com.baomidou.mybatisplus.annotation.TableId;
import java.io.Serializable;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * 提现结果表
 * </p>
 *
 * <AUTHOR>
 * @since 2020-09-03
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value="BpAmountResult对象", description="提现结果表")
public class BpAmountResult implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.INPUT)
    private Integer id;

    @ApiModelProperty(value = "产品名称")
    private String appName;

    private String appGroup;

    @ApiModelProperty(value = "系统 ios/andriod")
    private String os;

    @ApiModelProperty(value = "提现金额 单位元")
    private BigDecimal amount;

    @ApiModelProperty(value = "日期 格式:yyyy-MM-dd")
    private String dateStr;

    @ApiModelProperty(value = "pkgId")
    private String pkgId;

    @ApiModelProperty(value = "创建时间")
    private Date createTime;

    @ApiModelProperty(value = "更新时间")
    private Date updateTime;


}
