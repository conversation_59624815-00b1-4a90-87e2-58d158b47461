package com.coohua.user.event.biz.service.req;

import com.coohua.user.event.biz.dc.entity.HsResultData;
import com.coohua.user.event.biz.util.DateUtil;
import lombok.Data;

/**
 * <AUTHOR>
 * @since 2023/6/28
 */
@Data
public class HsAdDataRequest {
    private String date;
    private String partner;
    private String platform_name;
    private Double cost;
    private Double ad_revenue;
    private Double withdrawal_amount;
    private Long timestamp;
    private String sign;
    private String api_key;

    public HsAdDataRequest build(HsResultData hsResultData,String apiKey,long time){
        HsAdDataRequest request = new HsAdDataRequest();
        request.setDate(DateUtil.dateToString(hsResultData.getLogday()));
        request.setPartner("北京臻盛网络技术有限公司东台分公司");
        request.setPlatform_name(hsResultData.getPlatformName());
        request.setCost(hsResultData.getCost());
        request.setAd_revenue(hsResultData.getAdRevenue());
        request.setWithdrawal_amount(hsResultData.getWithdrawAmount());
        request.setTimestamp(time);
        request.setApi_key(apiKey);
        return request;
    }
}
