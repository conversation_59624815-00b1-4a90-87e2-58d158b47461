package com.coohua.user.event.biz.ck001.config;

import com.alibaba.druid.spring.boot.autoconfigure.DruidDataSourceBuilder;
import com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean;
import lombok.extern.slf4j.Slf4j;
import org.apache.ibatis.session.SqlSessionFactory;
import org.mybatis.spring.annotation.MapperScan;
import org.mybatis.spring.mapper.MapperScannerConfigurer;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.boot.autoconfigure.condition.ConditionalOnBean;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.io.ClassPathResource;
import org.springframework.core.io.support.PathMatchingResourcePatternResolver;

import javax.sql.DataSource;
import java.sql.SQLException;

@Configuration
@MapperScan(basePackages = {"com.coohua.user.event.biz.ck001.mapper"})
public class Ck001DataSourceConfig {
    @Bean(name = "datasourceClickHouse001")
    @ConfigurationProperties(prefix = "spring.datasource.ck001")
    public DataSource dataSource() throws SQLException {
        return DruidDataSourceBuilder.create().build();
    }

    @Bean(name = "datasourceClickHouse001SqlSessionFactoryBean")
    @ConditionalOnBean(name = "datasourceClickHouse001")
    public SqlSessionFactory sqlSessionFactoryBean(@Qualifier("datasourceClickHouse001") DataSource dataSource) throws Exception {
        MybatisSqlSessionFactoryBean mybatisSqlSessionFactoryBean = new MybatisSqlSessionFactoryBean();
        mybatisSqlSessionFactoryBean.setDataSource(dataSource);
        mybatisSqlSessionFactoryBean.setConfigLocation(new ClassPathResource("config/mybatis_config.xml"));
        mybatisSqlSessionFactoryBean.setMapperLocations(new PathMatchingResourcePatternResolver().getResources("classpath*:mapper/ck001/*.xml"));
        return mybatisSqlSessionFactoryBean.getObject();
    }

    @Bean(name = "clickHouse001MapperScannerConfigurer")
    @ConditionalOnBean(name = "datasourceClickHouse001SqlSessionFactoryBean")
    public MapperScannerConfigurer mapperScannerConfigurer(){
        MapperScannerConfigurer configurer = new MapperScannerConfigurer();
        configurer.setBasePackage("com.coohua.user.event.biz.ck001.mapper");
        configurer.setSqlSessionFactoryBeanName("datasourceClickHouse001SqlSessionFactoryBean");
        return configurer;
    }
}
