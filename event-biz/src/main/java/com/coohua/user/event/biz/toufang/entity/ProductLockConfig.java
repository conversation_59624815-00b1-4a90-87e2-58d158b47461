package com.coohua.user.event.biz.toufang.entity;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * <p>
 * 产品锁区配置表
 * </p>
 *
 * <AUTHOR>
 */
@Data
public class ProductLockConfig implements Serializable {

    private static final long serialVersionUID=1L;

    private Integer id;

    private Integer stateFlag;

    private Integer appId;

    private Integer os;

    private String product;

    private String channels;

    private String cities;

    private String pkgs;

    private String whitelist;

    private Integer whiteType;

    private String blockAllList;

    private Integer delFlag;

    private Date createTime;

    private Date updateTime;

}
