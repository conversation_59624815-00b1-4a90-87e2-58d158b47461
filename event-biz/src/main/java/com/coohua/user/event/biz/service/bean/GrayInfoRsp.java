package com.coohua.user.event.biz.service.bean;

import lombok.Data;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2022/3/8
 */
@Data
public class GrayInfoRsp {

    private List<OcpcInfoDto> ocpcInfoDtoList = new ArrayList<>();
    private List<GrayDetailDto> grayDetailDtoList = new ArrayList<>();
    private List<DeviceDto> deviceList = new ArrayList<>();
    private OcpcInfoBean changeOcpcInfoData = new OcpcInfoBean();

}
