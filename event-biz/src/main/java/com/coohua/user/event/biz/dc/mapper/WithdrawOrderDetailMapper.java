package com.coohua.user.event.biz.dc.mapper;

import com.coohua.user.event.biz.dc.entity.WithdrawOrderDetail;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.*;

import java.util.List;

/**
 * <p>
 * 支付订单 & 付款订单总表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2020-09-14
 */
public interface WithdrawOrderDetailMapper {


    @Insert({
            "<script>",
            "INSERT INTO ads.withdraw_order_detail (" +
                    "product_cn," +
                    "product_en," +
                    "product_group," +
                    "logday," +
                    "order_no," +
                    "user_id," +
                    "os," +
                    "device_id," +
                    "sub_type," +
                    "status," +
                    "title," +
                    "channel," +
                    "amount," +
                    "check_auth," +
                    "extra," +
                    "create_time," +
                    "update_time," +
                    "wechat_id," +
                    "withdraw_type"+
                    ")" +
                    "VALUES" +
                    "<foreach collection='withdrawOrderDetailList'  item='withdrawOrderDetail' separator=','>" +
                    "(" +
                    "#{withdrawOrderDetail.productCn}," +
                    "#{withdrawOrderDetail.productEn}," +
                    "#{withdrawOrderDetail.productGroup}," +
                    "#{withdrawOrderDetail.logday}," +
                    "#{withdrawOrderDetail.orderNo}," +
                    "#{withdrawOrderDetail.userId}," +
                    "#{withdrawOrderDetail.os}," +
                    "#{withdrawOrderDetail.deviceId}," +
                    "#{withdrawOrderDetail.subType}," +
                    "#{withdrawOrderDetail.status}," +
                    "#{withdrawOrderDetail.title}," +
                    "#{withdrawOrderDetail.channel},"+
                    "#{withdrawOrderDetail.amount},"+
                    "#{withdrawOrderDetail.checkAuth},"+
                    "#{withdrawOrderDetail.extra},"+
                    "#{withdrawOrderDetail.createTime},"+
                    "#{withdrawOrderDetail.updateTime},"+
                    "#{withdrawOrderDetail.wechatId},"+
                    "#{withdrawOrderDetail.withdrawType}"+
                    ")" +
                    "</foreach>",
            "</script>",
    })
    int saveBatch(@Param("withdrawOrderDetailList") List<WithdrawOrderDetail> withdrawOrderDetailList);


    @Update({"truncate table ads.withdraw_order_detail"})
    void truncateWithdrawOrderDetail();


    @Delete({"delete from ads.withdraw_order_detail where create_time > #{start} and create_time < #{end} and product_cn = #{name}"})
    int delete(@Param("start")Long start,@Param("end")Long end,@Param("name") String name);


    @Delete({"delete from ads.withdraw_order_detail where id = #{id}"})
    int deleteB(@Param("id")Long id);

    @Select({"select * from ads.withdraw_order_detail where os is null"})
    List<WithdrawOrderDetail> queryAll();

    @Delete({"delete from ads.withdraw_order_detail where logday = #{logday}"})
    int deleteByLogDay(@Param("logday")String logDay);

}
