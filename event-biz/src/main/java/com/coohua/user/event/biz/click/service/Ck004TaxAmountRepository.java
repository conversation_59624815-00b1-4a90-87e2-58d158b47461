package com.coohua.user.event.biz.click.service;

import com.coohua.user.event.biz.click.entity.WithdrawMchDV;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.stereotype.Component;
import ru.yandex.clickhouse.ClickHouseDataSource;
import ru.yandex.clickhouse.settings.ClickHouseProperties;

import javax.annotation.PreDestroy;
import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.SQLException;
import java.util.List;

/**
 * 暂时不支持并发
 */
@Slf4j
@Component
public class Ck004TaxAmountRepository implements InitializingBean {

    private static final String USER = "default";
    private static final String PASSWORD = "Phw7a7A4";
    private static final String URL = "************************************";
    private ClickHouseDataSource dataSource;
    private Connection connection;
    private PreparedStatement ps;
    public int batchCounter = 0;
    private static final String INSERT_TAX = "INSERT INTO test.withdraw_mch_d_v2 (logday,product,product_name,product_group,order_no,batch_id,user_id,os,status,title,channel,amount,check_auth,mch_id,withdraw_type\n" +
            ") VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?);";

    @PreDestroy
    public void destroy() {
        if (ps != null) {
            try {
                ps.close();
            } catch (SQLException throwables) {
                log.error("", throwables);
            }
        }

        if (connection != null) {
            try {
                connection.close();
            } catch (SQLException throwables) {
                log.error("", throwables);
            }
        }

    }

    @Override
    public void afterPropertiesSet() throws Exception {
        Class.forName("ru.yandex.clickhouse.ClickHouseDriver");
        ClickHouseProperties properties = new ClickHouseProperties();
        properties.setUser(USER);
        properties.setPassword(PASSWORD);
        dataSource = new ClickHouseDataSource(URL, properties);
    }

    public void batchSave(List<WithdrawMchDV> withdrawMchDVS) throws SQLException {
        if (connection == null) {
            connection = dataSource.getConnection();
        }
        if (ps == null) {
            ps = connection.prepareStatement(INSERT_TAX);
        }
        for (WithdrawMchDV withdrawMchDV : withdrawMchDVS) {
            addToBatch(withdrawMchDV);
        }
        long begin = System.currentTimeMillis();
        ps.executeBatch();
        log.info("[CK]保存数据成功! batchSize:{}, cost:{}mms", batchCounter, System.currentTimeMillis() - begin);
        batchCounter = 0;
    }

    public synchronized void executeBatch() throws SQLException {
        if (ps != null) {
            long begin = System.currentTimeMillis();
            ps.executeBatch();
            log.info("[CK]保存数据成功! batchSize:{}, cost:{}mms", batchCounter, System.currentTimeMillis() - begin);
            batchCounter = 0;
        }
    }

    public synchronized void addToBatch(WithdrawMchDV withdrawMchDV) throws SQLException {
        if (connection == null) {
            connection = dataSource.getConnection();
        }
        if (ps == null) {
            ps = connection.prepareStatement(INSERT_TAX);
        }
        int colIndex = 1;
        ps.setDate(colIndex++, withdrawMchDV.getLogday());
        ps.setString(colIndex++, withdrawMchDV.getProduct());
        ps.setString(colIndex++, withdrawMchDV.getProductName());
        ps.setString(colIndex++, withdrawMchDV.getProductGroup());
        ps.setString(colIndex++, withdrawMchDV.getOrderNo());
        ps.setString(colIndex++, withdrawMchDV.getBatchId());
        ps.setLong(colIndex++, withdrawMchDV.getUserId());
        ps.setString(colIndex++, withdrawMchDV.getOs());
        ps.setShort(colIndex++, withdrawMchDV.getStatus());
        ps.setString(colIndex++, withdrawMchDV.getTitle());
        ps.setLong(colIndex++, withdrawMchDV.getChannel());
        ps.setLong(colIndex++, withdrawMchDV.getAmount());
        ps.setLong(colIndex++, withdrawMchDV.getCheckAuth());
        ps.setString(colIndex++, withdrawMchDV.getMchId());
        ps.setShort(colIndex++, withdrawMchDV.getWithdrawType());


        ps.addBatch();
        batchCounter++;
    }

}
