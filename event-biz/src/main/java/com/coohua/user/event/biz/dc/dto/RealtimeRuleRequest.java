package com.coohua.user.event.biz.dc.dto;

import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2022/1/26
 */
@Data
public class RealtimeRuleRequest {

    private Integer id;
    private Long appId;
    private Integer state;
    private Integer ocpcType;
    private String channels;
    private String models;
    private String ips;
    private Integer actionType;
    private String ruleName;

    private Integer level;
    private String brands;
    private String sources;
    private Integer newUserType;

    private Integer type;
    private Integer minEcpm;
    private Integer maxEcpm;
    private Integer videoTimes;
    private Integer videoLimit;
    private Integer withdrawLimit;
    private Integer rewardLimit;
    private List<Integer> platformLimit;
    private List<String> platformSkipList;
}
