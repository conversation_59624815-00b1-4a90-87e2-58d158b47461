package com.coohua.user.event.biz.util;

import com.alibaba.fastjson.JSON;
import com.coohua.bp.user.remote.api.AppRPC;
import com.coohua.bp.user.remote.dto.AppDTO;
import com.weibo.api.motan.config.springsupport.annotation.MotanReferer;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2020/12/2
 */
@Slf4j
@Component
public class AppDrawConf {

    private final static String TABLE_PREFIX = "bp_mall.withdraw_order_";

    @MotanReferer(basicReferer = "bp-userBasicRefererConfigBean")
    private AppRPC appRPC;

    public static Map<Long, AppDTO> appIdMap;

    public static Map<String, List<AppDTO>> appSecondMapList;

    public static Map<Integer,List<String>> appWithdrawTableMap;

    public static Map<Long,Long> appIdConvertMap;


    @PostConstruct
    @Scheduled(cron = "0 0 * * * ?")
    public void refresh(){
        log.info("Start Refresh AppWithDrawConf.....");
        List<AppDTO> appDTOList = appRPC.getAll();
        appSecondMapList = appDTOList.stream().collect(Collectors.groupingBy(a -> invoke(a.getName())));
        appIdMap = appDTOList.stream().collect(Collectors.toMap(AppDTO::getId, a -> a, (oldVal, newVal) -> newVal));


        Map<Integer,List<String>> tempMap = new HashMap<>();
        Map<Long, Long> convertMap = new HashMap<>();
        appIdMap.keySet().stream().sorted(Long::compareTo).forEach(appId ->{
            AppDTO appDTO = appIdMap.get(appId);
            List<AppDTO> dtoList = appSecondMapList.get(invoke(appDTO.getName()));
            if (appId == 44){
                dtoList = new ArrayList<AppDTO>(){{add(new AppDTO(){{setId(44L);setName("我的花园");setDescription("我的花园");setCreateTime(1594968602352L);}});}};
            }
            if (appId == 357){
                dtoList = new ArrayList<AppDTO>(){{add(new AppDTO(){{setId(357L);setName("我的花园");setDescription("我的花园");setCreateTime(1609248600112L);}});}};
            }
            tempMap.put(Math.toIntExact(appDTO.getId()),dtoList.stream().map(dto-> TABLE_PREFIX + dto.getId()).collect(Collectors.toList()));
            dtoList.forEach(idx ->convertMap.putIfAbsent(idx.getId(),appDTO.getId()));
            convertMap.put(629L,629L);
        });

        appWithdrawTableMap = tempMap;
        appIdConvertMap = convertMap;

        log.info(JSON.toJSONString(appWithdrawTableMap));
        log.info(JSON.toJSONString(appIdConvertMap));
        log.info("End Refresh AppWithDrawConf.....");
    }


    private static String invoke(String name){
        if (Strings.noEmpty(name)){
            return name
                    .replaceAll("第二账户", "")
                    .replaceAll("第三账户", "")
                    .replaceAll("第四账户", "");
        }
        return "";
    }
}
