package com.coohua.user.event.biz.click.mapper;

import com.coohua.user.event.biz.click.entity.UserRiskLevelEntity;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2021/9/3
 */
public interface ClickHouseUserGrayMapper {

    @Select({"select r2.id      as app_id, " +
            "       r1.user_id as user_id, " +
            "       r1.os as os, " +
            "       r1.product as product, " +
            "       r1.ex      as exposure, " +
            "       r1.ck      as click," +
            "       #{riskLevel} as level " +
            "from ( " +
            "         select user_id, product,max(os) as os, max(exposure_count) as ex, max(real_click_count) as ck " +
            "         from user_gray.user_click_rate " +
            "         where logday = today() " +
            "           and exposure_count >= #{exposureStart} " +
            "           and exposure_count < #{exposureEnd} " +
            "           and click_count >= #{clickLimit} " +
            "         group by user_id, product) r1 " +
            "         left join dwd.app_mapping_dist r2 " +
            "                   on r1.product = r2.product"})
    List<UserRiskLevelEntity> queryUserRiskAList(@Param("exposureStart") Integer exposureStart,
                                                 @Param("exposureEnd") Integer exposureEnd,
                                                 @Param("clickLimit") Integer clickLimit,
                                                 @Param("riskLevel") Integer riskLevel);


    @Select({"select r2.id      as app_id, " +
            "       r1.user_id as user_id, " +
            "       r1.product as product, " +
            "       r1.os as os, " +
            "       r1.ex      as exposure, " +
            "       r1.ck      as click ," +
            "       #{riskLevel} as level " +
            "from ( " +
            "         select user_id, product,max(os) as os, max(exposure_count) as ex, max(real_click_count) as ck " +
            "         from user_gray.user_click_rate " +
            "         where logday = today() " +
            "           and exposure_count >= #{exposureStart} " +
            "           and exposure_count < #{exposureEnd} " +
            "           and click_count >= #{clickBLimit} " +
            "           and click_count < #{clickALimit} " +
            "         group by user_id, product) r1 " +
            "         left join dwd.app_mapping_dist r2 " +
            "                   on r1.product = r2.product"})
    List<UserRiskLevelEntity> queryUserRiskBList(@Param("exposureStart") Integer exposureStart,
                                                 @Param("exposureEnd") Integer exposureEnd,
                                                 @Param("clickALimit") Integer clickALimit,
                                                 @Param("clickBLimit") Integer clickBLimit,
                                                 @Param("riskLevel") Integer riskLevel);


    @Select("select groupUniqArray(ip) from ods_mysql.already_gray_user where toDate(create_time) >= today() -7 and ip not like '%xxx' and ip !=  ''")
    String queryGrayUserIpList();

    @Select(" select count() from ods_mysql.already_gray_user where target_type =2 and remark like 'ECPM集中%' " +
            " and toDate(create_time) = today() ")
    Integer countAlreadyGray();
}
