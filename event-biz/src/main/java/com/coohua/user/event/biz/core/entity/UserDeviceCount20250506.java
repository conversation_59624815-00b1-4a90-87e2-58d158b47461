package com.coohua.user.event.biz.core.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.util.List;
import java.util.Set;

/**
 * 用户设备数表-临时
 * @TableName user_device_count_20250506
 */
@TableName(value ="user_device_count_20250506")
@Data
public class UserDeviceCount20250506 {
    /**
     * 
     */
    @TableId(type = IdType.AUTO)
    private Integer id;

    /**
     * 
     */
    private Integer appId;

    /**
     * 
     */
    private String product;

    /**
     * 
     */
    private Long userId;

    /**
     * 
     */
    private String deviceStr;
    private Set<String> deviceSet;

    /**
     * 
     */
    private String imeiStr;
    private Set<String> imeiSet;

    /**
     * 
     */
    private String androidStr;
    private Set<String> androidSet;

    private String oaidStr;
    private Set<String> oaidSet;

    /**
     * 
     */
    private String modelStr;
    private Set<String> modelSet;

    private Integer maxDeviceCount;
}