package com.coohua.user.event.biz.dc.service;

import com.coohua.user.event.biz.dc.entity.CdpCompilations;
import com.coohua.user.event.biz.dc.mapper.CdpCompilationsMapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;

import java.util.List;

/**
* <p>
    *  服务实现类
    * </p>
*
* <AUTHOR>
* @since 2023-06-15
*/
@Service
public class CdpCompilationsService extends ServiceImpl<CdpCompilationsMapper, CdpCompilations> {

    public List<CdpCompilations> queryByLogday(String logday){
        return baseMapper.queryByLogday(logday);
    }
}
