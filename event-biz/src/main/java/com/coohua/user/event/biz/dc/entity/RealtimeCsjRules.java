package com.coohua.user.event.biz.dc.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import java.util.Date;
import com.baomidou.mybatisplus.annotation.TableId;
import java.io.Serializable;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR>
 * @since 2022-10-11
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class RealtimeCsjRules implements Serializable {

    private static final long serialVersionUID = 1L;

    private Integer id;

    private String name;

    private Integer csjRate;

    private Integer uv;

    private Integer pv;

    private String channelStart;

    private Integer includeType;

    private Integer modelType;

    private Integer avgEcpm;

    private String actionTask;

    private Date createTime;

    private Date updateTime;


}
