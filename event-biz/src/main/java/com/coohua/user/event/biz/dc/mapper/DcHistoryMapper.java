package com.coohua.user.event.biz.dc.mapper;

import com.coohua.user.event.biz.dc.entity.DailyResultEntity;
import com.coohua.user.event.biz.dc.entity.DailyResultHisEntity;
import org.apache.ibatis.annotations.*;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2020/9/4
 */
public interface DcHistoryMapper {

    @Select({"select * from test.daily_result_temp where logday = #{date}"})
    List<DailyResultHisEntity> queryHistory(@Param("date")String date);

    @Select({"select * from test.daily_result_his where logday1 = #{date}"})
    List<DailyResultHisEntity> queryHistoryHis(@Param("date")String date);


    @Select({"select count(1) from  ads.daily_result where logday = #{date} and product = #{product} and os = #{os}"})
    int countDcData(@Param("date")String date,@Param("product")String product,@Param("os")String os);

    @Delete({"delete from ads.daily_result where logday = #{date} and product = #{product} and os = #{os}"})
    int delete(@Param("date")String date,@Param("product")String product,@Param("os")String os);

    @Insert({"INSERT INTO ads.daily_result (" +
            "logday1," +
            "logday," +
            "group_name," +
            "product," +
            "os," +
            "active_device," +
            "active_retain_1," +
            "active_retain_2," +
            "active_retain_3," +
            "active_retain_4," +
            "active_retain_5," +
            "active_retain_6," +
            "active_retain_7," +
            "active_retain_14," +
            "active_retain_30," +
            "activate_device," +
            "activate_retain_1," +
            "activate_retain_2," +
            "activate_retain_3," +
            "activate_retain_4," +
            "activate_retain_5," +
            "activate_retain_6," +
            "activate_retain_7," +
            "activate_retain_14," +
            "activate_retain_30," +
            "withdraw_money," +
            "mall_order," +
            "toufang_money," +
            "toufang_device," +
            "platform_chuanshanjia_income," +
            "platform_guangdiantong_income," +
            "platform_kuaishou_income," +
            "platform_qita_income," +
            "straight_zhengqi_income," +
            "straight_feizhengqi_income," +
            "platform_income," +
            "straight_income," +
            "total_income," +
            "video_exposure_opposite," +
            "video_exposure_mine)" +
            "VALUES(#{logday1}," +
            "#{logday}," +
            "#{groupName}," +
            "#{product}," +
            "#{os}," +
            "#{activeDevice}," +
            "#{activeRetain1}," +
            "#{activeRetain2}," +
            "#{activeRetain3}," +
            "#{activeRetain4}," +
            "#{activeRetain5}," +
            "#{activeRetain6}," +
            "#{activeRetain7}," +
            "#{activeRetain14}," +
            "#{activeRetain30}," +
            "#{activateDevice}," +
            "#{activateRetain1}," +
            "#{activateRetain2}," +
            "#{activateRetain3}," +
            "#{activateRetain4}," +
            "#{activateRetain5}," +
            "#{activateRetain6}," +
            "#{activateRetain7}," +
            "#{activateRetain14}," +
            "#{activateRetain30}," +
            "#{withdrawMoney}," +
            "#{mallOrder}," +
            "#{toufangMoney}," +
            "#{toufangDevice}," +
            "#{platformChuanshanjiaIncome}," +
            "#{platformGuangdiantongIncome}," +
            "#{platformKuaishouIncome}," +
            "#{platformQitaIncome}," +
            "#{straightZhengqiIncome}," +
            "#{straightFeizhengqiIncome}," +
            "#{platformIncome}," +
            "#{straightIncome}," +
            "#{totalIncome}," +
            "#{videoExposureOpposite},#{videoExposureMine})"})
    int insertToDcData(DailyResultEntity entity);

}
