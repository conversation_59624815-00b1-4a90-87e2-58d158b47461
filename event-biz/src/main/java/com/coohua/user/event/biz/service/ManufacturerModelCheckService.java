package com.coohua.user.event.biz.service;

import com.alibaba.fastjson.JSON;
import com.coohua.user.event.biz.click.entity.UserGrayChannelEntity;
import com.coohua.user.event.biz.click.service.ClickHouseService;
import com.coohua.user.event.biz.util.Lists;
import com.coohua.user.event.biz.util.RedisKeyConstants;
import com.coohua.user.event.biz.util.Strings;
import com.ctrip.framework.apollo.spring.annotation.ApolloJsonValue;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;
import top.zrbcool.pepper.boot.jediscluster.JedisClusterClient;
import top.zrbcool.pepper.boot.jediscluster.JedisClusterClientRefer;

import javax.annotation.PostConstruct;
import java.util.*;

/**
 * <AUTHOR>
 * @since 2024/4/20
 */
@Slf4j
@Service
public class ManufacturerModelCheckService {

    @Autowired
    private ClickHouseService clickHouseService;
    @JedisClusterClientRefer(namespace = "user-event")
    private JedisClusterClient userEventJedisClusterClient;

    @ApolloJsonValue("${user.black.manufacturer.list:[\"Xamoii\",\"Ximaio\",\"amoiXi\",\"aoiXim\",\"ioamiX\",\"mXiiao\",\"Xoimia\",\"aXoimi\",\"Xioami\",\"iiXmao\"]}")
    private Set<String> userBlackManufacturerSet;
    @ApolloJsonValue("${user.black.model.list:[\"X3I M\",\"MI X3\",\"X I3M\",\"3IX M\",\"X 3MI\",\"XI3M\",\"XMI3\",\"M3I X\",\"XM3I\",\"XI 3M\"]}")
    private Set<String> userBlackModelSet;

    private static final String MANUFACTURER_MODEL_LIST = "user:Manufacturer:model:List";

//    private List<UserGrayChannelEntity> manufacturerModelList = new ArrayList<>();

    private Map<String, String> MODEL_MANUFACTURER_MAP = new HashMap<>();



    public void refreshManufacturerModelListToRedis(){
        log.info("==> Start Refresh Manufacturer Model List ..");
        List<UserGrayChannelEntity> manufacturerModelList =  clickHouseService.queryManufacturerModelList();
        if (Lists.noEmpty(manufacturerModelList)){
            if (manufacturerModelList.size() > 20000) {
                manufacturerModelList = manufacturerModelList.subList(0, 20000);
            }
            String result = JSON.toJSONString(manufacturerModelList);
            log.info("==> Get Current Manufacturer Model:{}", result);
            userEventJedisClusterClient.setex(MANUFACTURER_MODEL_LIST, RedisKeyConstants.EXPIRE_ONE_DAYS, result);
        }
        log.info("==> End Refresh Manufacturer Model List ..");
    }



    @PostConstruct
    @Scheduled(cron = "0 0/3 * * * ?")
    public void refreshManufacturerModelMap(){
        log.info("==> Start Refresh Manufacturer Model Map Config ..");
        String result = userEventJedisClusterClient.get(MANUFACTURER_MODEL_LIST);
        if (Strings.noEmpty(result)){
            List<UserGrayChannelEntity> res = JSON.parseArray(result,UserGrayChannelEntity.class);
            for (UserGrayChannelEntity re : res) {
                if (StringUtils.isNotBlank(re.getModel())) {
                    // 一个model存在对应多个机型？
                    MODEL_MANUFACTURER_MAP.put(re.getModel(), re.getManufacturer());
                }
            }
            log.info("==> Current Get Manufacturer Model  Map:{}", res);
        }else {
            MODEL_MANUFACTURER_MAP = new HashMap<>();
        }
        log.info("==> End Refresh Manufacturer Model Map Config ..");
    }

    public boolean isBlackModelAndManufacturer(String model){
        if (StringUtils.isBlank(model)) {
            return false;
        }
        if(userBlackModelSet.contains(model) || (MODEL_MANUFACTURER_MAP.containsKey(model) && userBlackManufacturerSet.contains(MODEL_MANUFACTURER_MAP.get(model)))) {
            return true;
        }
        return false;
    }

}
