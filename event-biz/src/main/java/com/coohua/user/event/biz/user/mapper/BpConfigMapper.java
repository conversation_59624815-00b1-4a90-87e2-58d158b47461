package com.coohua.user.event.biz.user.mapper;

import com.coohua.user.event.biz.user.entity.GlobalConfigEntity;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2021/8/5
 */
public interface BpConfigMapper {

    @Select({"select * from `bp-config`.tb_bp_config_globalconfig where state = 1 and name='daily.ad.times.limit'"})
    List<GlobalConfigEntity> queryGroup5LimitApp();
}
