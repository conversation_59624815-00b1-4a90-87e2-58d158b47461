package com.coohua.user.event.biz.toufang.service;

import com.coohua.user.event.biz.toufang.entity.CoreEventEntity;
import com.coohua.user.event.biz.toufang.entity.UserEventModelEntity;
import com.coohua.user.event.biz.toufang.mapper.CoreEventMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2020/9/19
 */
@Slf4j
@Service
public class CoreEventService {

    @Resource
    private CoreEventMapper coreEventMapper;

    public List<CoreEventEntity> queryCoreEvent(String logDay){
        return coreEventMapper.queryCoreEvent(logDay);
    }

    private List<UserEventModelEntity> queryUserEventKuaiShou(Date begin,Date end){
        return coreEventMapper.queryUserEventKuaiShou(begin,end);
    }

    private List<UserEventModelEntity> queryUserEventOther(Date begin,Date end){
        return coreEventMapper.queryUserEventOther(begin,end);
    }

    public List<UserEventModelEntity> queryUserEvent(Date begin,Date end){
        return new ArrayList<UserEventModelEntity>(){{
            List<UserEventModelEntity>userEventModelEntities = queryUserEventOther(begin,end);
            List<UserEventModelEntity>userEventModelEntityList = queryUserEventKuaiShou(begin,end);
            if (userEventModelEntities.size()>0){
                addAll(userEventModelEntities);
            }
            if (userEventModelEntityList.size()>0){
                addAll(userEventModelEntityList);
            }
        }};
    }

}
