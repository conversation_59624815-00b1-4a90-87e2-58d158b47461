package com.coohua.user.event.biz.toufang.service;

import com.coohua.user.event.biz.toufang.entity.ProductLockConfig;
import com.coohua.user.event.biz.toufang.mapper.ProductAreaConfigMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2022/2/16
 */
@Slf4j
@Service
public class ProductLockService {

    @Resource
    private ProductAreaConfigMapper productAreaConfigMapper;

    public List<ProductLockConfig> queryProductConfig(){
        return productAreaConfigMapper.queryAllConfig();
    }

}
