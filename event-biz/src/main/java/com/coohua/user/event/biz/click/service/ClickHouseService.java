package com.coohua.user.event.biz.click.service;

import com.alibaba.fastjson.JSON;
import com.coohua.user.event.biz.click.entity.*;
import com.coohua.user.event.biz.click.mapper.*;
import com.coohua.user.event.biz.dc.dto.RealtimeCsjRulesDto;
import com.coohua.user.event.biz.dc.entity.WithdrawOrderDetail;
import com.coohua.user.event.biz.service.bean.UserActive;
import com.coohua.user.event.biz.util.DateUtil;
import com.coohua.user.event.biz.util.Lists;
import com.coohua.user.event.biz.util.Strings;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2020/9/7
 */
@Slf4j
@Service
public class ClickHouseService {

    @Resource
    private ClickHoseBaseProductMapper clickHoseBaseProductMapper;
    @Resource
    private ClickHouseDwdMapper clickHouseDwdMapper;
    @Resource
    private ClickHouseOdsMapper clickHouseOdsMapper;
    @Resource
    private ClickHouseUserGrayMapper clickHouseUserGrayMapper;
    @Resource
    private ClickHouseAdsMapper clickHouseAdsMapper;


    public List<Integer> queryConfigAreaProduct(){
        return clickHouseOdsMapper.queryActiveFull();
    }

    public List<ProductEntity> queryAllProduct(){
        return clickHoseBaseProductMapper.queryProductMap();
    }
    public ProductEntity queryProductByAppId(Integer appId){
        return clickHoseBaseProductMapper.queryProductByAppId(appId);
    }

    public List<UserProfileDist> queryUserInfo(List<Long> userIdList){
        return clickHouseDwdMapper.queryUserInfoListJustIdAndOs(userIdList);
    }

    public List<ProductEntity> queryTop40(){
        return clickHouseDwdMapper.queryTop40();
    }

    public List<AbnormalUserEntity> queryProductGrayUser(String product,String logDay,Integer limit){
        return clickHouseOdsMapper.queryProductExposure(product,logDay,limit);
    }

    /**
     * 查询昨日看广告用户个数
     * @param appId 产品id
     * @return 用户看广告个数
     */
    public Integer countYesterdayUser(String appId){
        return clickHouseOdsMapper.countUserId(appId);
    }

    /**
     * 查询昨日用户平均ECPM
     * @param appId 应用ID
     * @param offset 偏移
     * @param limit 条数
     * @return 实体结果
     */
    public List<UserAvgEcpmEntity> queryYesterdayUserEcpm(String appId,Integer offset,Integer limit){
        return clickHouseOdsMapper.queryUserEcpmBeanList(appId,offset,limit);
    }

    /**
     * 查询该IP下所有设备号
     * @param product 产品
     * @param logday 日期
     * @return 异常用户值
     */
    public List<UserIpEntity> queryGrayUserIpList(String product,String logday){
        return clickHouseOdsMapper.queryGrayUserIp(product,logday);
    }

    public List<UserExposureEntity> queryUserExposure(String product,String logday){
        return clickHouseOdsMapper.queryExposureEx(product,logday);
    }

    /**
     * 查询是否投放设备
     * @param product 产品
     * @param deviceIdList 设备列表
     * @return 结果信息
     */
    public UserNoTfEntity queryUserNoTfInfo(String product,List<String> deviceIdList){
        return clickHouseDwdMapper.countFromAllTfDist(product,deviceIdList);
    }

    /**
     * 通过产品和设备查询userID
     * @param product 产品
     * @param deviceIdList 设备列表
     * @return 用户列表
     */
    public String queryUserInfo(String product,List<String> deviceIdList){
        return clickHouseDwdMapper.queryUserIdListByProductAndDeviceId(product,deviceIdList);
    }

    public String queryDeviceInfo(List<String> deviceIdList){
        return clickHouseDwdMapper.queryUserIdListByDeviceId(deviceIdList);
    }

    /**
     * 通过产品和用户查设备ID
     * @param product 产品
     * @param userIdList 用户列表
     * @return 设备列表
     */
    public String queryDeviceInfo(String product,List<String> userIdList){
        return clickHouseDwdMapper.queryUserIdListByProductAndUserId(product,userIdList);
    }

    /**
     * 师徒关系 -- 徒弟个数超过十人
     * @param product 产品
     * @param logday 日期
     * @return 师徒关系列表
     */
    public List<UserGrayFriendShipEntity> queryFriendList(String product,String logday){
        return clickHouseDwdMapper.queryUserFriendShipList(product,logday);
    }

    /**
     * description: 当日已被封禁用户的师徒人(排除师傅是ocpc投放用户)
     * date: 2021/9/8 11:35
     * params: [product, logday]
     * return: java.util.List<java.lang.String>
    */
    public List<String> queryAlreadyGrayUserFriendList(String product,String logday){
        return clickHouseDwdMapper.queryAlreadyGrayUserFriendShipList(product,logday);
    }

    /**
     * 手机型号占比查询
     * @param product 产品
     * @param logday 日期
     * @param userIdList 用户列表
     * @return 手机型号占比列表
     */
    public List<UserGrayModelEntity> queryUserModelCount(String product,String logday,List<String> userIdList){
        return clickHouseOdsMapper.queryGrayUserModel(product,logday,userIdList);
    }

    public List<UserGrayChannelEntity> queryGrayUserChannel(String product,String logday){
        return clickHouseOdsMapper.queryGrayUserChannel(product,logday);
    }

    public UserNoTfEntity queryTfClickDevice(String product,List<String> userIdList){
        return clickHouseOdsMapper.countFromAllTfClick(product,userIdList);
    }

    public List<UserScoreEntity> queryUserScoreEx(){
        return clickHouseOdsMapper.queryScoreException();
    }

    public List<ProductGrayRateEntity> queryGrayRate(){
        return clickHouseOdsMapper.queryGrayRate();
    }

    public List<ProductGrayRateEntity> queryAlreadyGrayCount(Integer limitCount){
        return clickHouseOdsMapper.queryAlreadyGrayUser(limitCount);
    }

    public List<UserRiskLevelEntity> queryUserRiskAList(Integer exposureStart,Integer exposureEnd,Integer clickLimit,Integer riskLevel){
        return clickHouseUserGrayMapper.queryUserRiskAList(exposureStart, exposureEnd, clickLimit, riskLevel);
    }

    public List<UserRiskLevelEntity> queryUserRiskBList(Integer exposureStart,Integer exposureEnd,Integer clickALimit,
                                                        Integer clickBLimit,Integer riskLevel){
        return clickHouseUserGrayMapper.queryUserRiskBList(exposureStart, exposureEnd, clickALimit, clickBLimit, riskLevel);
    }

    public List<ExceptionAdPosEntity> queryExceptionList(String logday,String typeName,Integer income,Integer rate){
        return clickHouseOdsMapper.queryExceptionAdPos(logday,typeName,income,rate);
    }

    public List<ExceptionAdPosEntity> queryExceptionList(String logday,String typeName,Integer lowPrice){
        String[] logdayArgs = logday.split("[-]");
        String logday1 = logdayArgs[0]+"-"+logdayArgs[1]+"-01";
        log.info("Source Day:{} , firstDay:{}",logday,logday1);
        return clickHouseOdsMapper.querySumIncomeAdPos(logday,typeName,logday1,lowPrice);
    }

    public String queryGrayIpList(){
        return clickHouseUserGrayMapper.queryGrayUserIpList();
    }

    public Integer countAlreadyGrayUser(){
        return clickHouseUserGrayMapper.countAlreadyGray();
    }


    public List<String> queryExUserIdList(String product,List<String> channelList,List<String> ipList,List<String> modelList,
                                  List<String> phoneList,Integer ocpcChannel,List<String> whiteList){
        String userStr = clickHouseOdsMapper.queryTargetUser(product,channelList,ipList,modelList,phoneList,ocpcChannel,whiteList);
        if (Strings.noEmpty(userStr)){
            return JSON.parseArray(userStr,String.class).stream().filter(Strings::noEmpty).collect(Collectors.toList());
        }
        return new ArrayList<>();
    }

    public List<UserScoreEntity> queryExUserEntityList(String product, List<String> channelList, List<String> ipList, List<String> modelList, List<String> phoneList, Integer ocpcChannel, List<String> whiteList) {
        return clickHouseOdsMapper.queryTargetUserEntity(product,channelList,ipList,modelList,phoneList,ocpcChannel,whiteList);
    }

    public List<String> queryRegisteredIdFromDevice(String product,List<String> deviceId){
        return clickHouseOdsMapper.queryRegisteredIdFromDevice(product,deviceId);
    }

    public Integer queryRetainRelation(List<Long> userId, Integer appId){
        return clickHouseAdsMapper.countRetainUser(userId,appId);
    }

    public List<InnerPullBean> queryRelationUsers(String logday){
        return clickHouseDwdMapper.queryRelation(logday);
    }

    public List<InnerMasterBean> queryInnerMasterInfo(String logday){
        return clickHouseDwdMapper.queryInnerMasterInfo(logday);
    }

    public List<InnerPullBean> userIdList(String product,Integer max,Integer min,Integer times,
                                          List<String> channelList,
                                          List<String> modelList,
                                          List<String> brandList,
                                          List<String> ipList,
                                          List<String> sourceList,
                                          Integer newUserType,
                                          Integer ocpcType,List<String> filterPlatform){
        String ipPer = ipList.stream().map(r -> "^"+r).collect(Collectors.joining("|"));
        return clickHouseOdsMapper.queryECPMExUser(product,max,min,times,channelList,modelList,brandList,ipPer,
                sourceList,newUserType,ocpcType,filterPlatform);
    }

    public List<RelationshipEntity> queryRelationship(String product,Long userId){
        return clickHouseDwdMapper.queryUserRelation(product,userId);
    }

    public List<VideoAdReportEntitiy> queryVideoDetailList(Integer appId,Long userId){
        return clickHouseOdsMapper.queryUserVideoDetail(appId,userId.toString());
    }

    public List<UserArpuEntity> queryAvgVideoArpu(Integer appId,Long userId){
        return clickHouseOdsMapper.queryAvgVideo(appId,userId.toString());
    }

    public List<WithdrawOrderDetail> queryWithDraw(String product,Long userId){
        return clickHouseDwdMapper.queryWithdrawEntity(product,userId);
    }

    public List<UserMacIpCheckEntity> queryMacIpCheck(){
        return clickHouseOdsMapper.queryUserMacIpCheck();
    }

    public List<UserMacIpCheckEntity> queryEventUploadExp(){
        return clickHouseOdsMapper.queryUserEventUploadExp();
    }

    public List<UserMacIpCheckEntity> queryModelMatchedChannel(){
        return clickHouseOdsMapper.queryModelMatchedChannel();
    }

    public List<UserMacIpCheckEntity> queryExArpuUser(){
        return clickHouseOdsMapper.queryExArpuUser();
    }

    public List<UserMacIpCheckEntity> queryKuaishouViewExUser(){
        return clickHouseOdsMapper.queryKuaishouViewExUser();
    }

    public List<UserMacIpCheckEntity> queryQpVideoExUser(){
        return clickHouseOdsMapper.queryQpVideoExUser();
    }
    public List<UserMacIpCheckEntity> queryZiRanQpVideoExUser(){
        return clickHouseOdsMapper.queryZiRanQpVideoExUser();
    }
    public List<UserMacIpCheckEntity> queryTxQpVideoExUser(List<String> products){
        return clickHouseOdsMapper.queryTxQpVideoExUser(products);
    }
    public List<UserMacIpCheckEntity> queryKSDRGdtKsExCloseAd(){
        return clickHouseOdsMapper.queryKSDRGdtKsExCloseAd();
    }
    public List<UserMacIpCheckEntity> queryManuExCloseAd(List<String> channelList){
        return clickHouseOdsMapper.queryManuExCloseAd(channelList);
    }
    public List<UserMacIpCheckEntity> queryLuanModelExCloseAd(){
        return clickHouseOdsMapper.queryLuanModelExCloseAd();
    }
    public List<UserMacIpCheckEntity> queryChangeReqIdCloseAd(){
        return clickHouseOdsMapper.queryChangeReqIdCloseAd();
    }
    public List<UserMacIpCheckEntity> queryChangeReqId(){
        return clickHouseOdsMapper.queryChangeReqId();
    }

    public List<SkipAdEntity> queryKSDRExCloseAd(){
        return clickHouseOdsMapper.queryKSDRExCloseAd();
    }

    public List<SkipAdEntity> queryRepeatExposureExCloseAd(){
        return clickHouseOdsMapper.queryRepeatExposureExCloseAd();
    }
    public List<SkipAdEntity> queryIncomeSkipExCloseAd(){
        return clickHouseOdsMapper.queryIncomeSkipExCloseAd();
    }
    public List<UserMacIpCheckEntity> queryIncomeBlackExCloseAd(){
        return clickHouseOdsMapper.queryIncomeBlackExCloseAd();
    }
    public List<UserMacIpCheckEntity> queryManyChannelBlackExCloseAd(List<String> products){
        return clickHouseOdsMapper.queryManyChannelBlackExCloseAd(products);
    }
    public List<UserMacIpCheckEntity> queryManyModelBlackExCloseAd(List<String> products){
        return clickHouseOdsMapper.queryManyModelBlackExCloseAd(products);
    }
    public List<UserMacIpCheckEntity> queryExChannelBlackExCloseAd(){
        return clickHouseOdsMapper.queryExChannelBlackExCloseAd();
    }

    public List<SkipAdEntity> queryHuaWeiExCloseAd(){
        return clickHouseOdsMapper.queryHuaWeiExCloseAd();
    }
    public List<SkipAdEntity> queryGdtExCloseAd(){
        return clickHouseOdsMapper.queryGdtExCloseAd();
    }
    public List<SkipAdEntity> queryGdtExAdTypeCloseAd(){
        return clickHouseOdsMapper.queryGdtExAdTypeCloseAd();
    }

    public List<UserMacIpCheckEntity> queryClickExUser(List<String> typeList){
        return clickHouseOdsMapper.queryClickExUser(typeList);
    }

    public List<UserMacIpCheckEntity> queryActiveNlxOldUser(){
        return clickHouseOdsMapper.queryActiveNlxOldUser();
    }

    public List<UserMacIpCheckEntity> queryPvExUser(){
        String userIds = clickHouseOdsMapper.queryExUserList();
        List<String> userIdList = JSON.parseArray(userIds,String.class);
        if (Lists.isEmpty(userIdList)){
            return new ArrayList<>();
        }
        return clickHouseOdsMapper.queryPvExUser(userIdList);
    }

    public List<UserMacIpCheckEntity> queryExIpUser(){
//        List<String> ipList = clickHouseOdsMapper.queryPreExIpList();
//        if (Lists.isEmpty(ipList)){
//            return new ArrayList<>();
//        }
        return clickHouseOdsMapper.queryExIpUser();
    }

    public List<UserMacIpCheckEntity> queryRepeatRequestId(){
        return clickHouseOdsMapper.queryRepeatRequestId();
    }

    public List<UserMacIpCheckEntity> queryEcpmFilterCount(){
        return clickHouseOdsMapper.queryEcpmFilterCount();
    }

    public List<UserMacIpCheckEntity> queryMacModelCheck(){
        return clickHouseOdsMapper.queryUserModelCheck();
    }

    public List<UserMacIpCheckEntity> queryMacModelCheckLessModel(){
        return clickHouseOdsMapper.queryUserModelCheckLessModel();
    }

    public List<UserMacIpCheckEntity> queryXiaoMiExModel(){
        List<UserMacIpCheckEntity> resultList = new ArrayList<>();
        List<UserMacIpCheckEntity> userMacIpCheckEntities = clickHouseOdsMapper.queryXiaoMiExModel();
        if (Lists.noEmpty(userMacIpCheckEntities)){
            resultList.addAll(userMacIpCheckEntities);
        }

        Date now = new Date();
        if (now.getHours() <= 16){
            return resultList;
        }

        // 下线2025-0414
//        log.info("Need Query New Rules..");
//        List<UserMacIpCheckEntity> subList = clickHouseOdsMapper.queryXiaoMiExModelSub();
//        if (Lists.noEmpty(subList)){
//            resultList.addAll(subList);
//        }
        return resultList;
    }

    public List<UserMacIpCheckEntity> queryHuaWeiExModel(){
        return clickHouseOdsMapper.queryHuaWeiExModel();
    }

    public List<UserMacIpCheckEntity> querySamsangExModel(){
        return clickHouseOdsMapper.querySamsungExModel();
    }

    public List<UserMacIpCheckEntity> queryManuColdUser(){
        return clickHouseOdsMapper.queryManuColdUser();
    }

    public List<UserMacIpCheckEntity> queryCsjRequestIdRepeat(){
        return clickHouseOdsMapper.queryCsjRepeat();
    }

    public List<String> getHsWithdrawProducts(){
        String rest = clickHouseOdsMapper.queryHsWithdrawProduct();
        return JSON.parseArray(rest,String.class);
    }

    public List<UserHsNoCallBackEntity> queryProductHsExUser(String product){

        Date now = new Date();
        int hour = now.getHours();
        int queryHour = hour - 1;
        String logday = DateUtil.dateToString(now);
        if (hour == 0){
            queryHour = 23;
            logday = DateUtil.dateIncreaseByDay(logday,DateUtil.ISO_EXPANDED_DATE_FORMAT,-1);
        }
        List<UserHsNoCallBackEntity> result = new ArrayList<>();
        // 先检测是否可以执行火山check
        Integer count = clickHouseOdsMapper.countHsRecord(product);
        if (count <= 1000){
            log.info("记录过少不进行判定");
            return result;
        }
        List<UserHsNoCallBackEntity> userHsNoCallBackEntities = clickHouseOdsMapper.queryProductHsNoCallBackUser(product,logday,queryHour);
        if (Lists.noEmpty(userHsNoCallBackEntities)){
            result.addAll(userHsNoCallBackEntities);
        }
        List<UserHsNoCallBackEntity> userHsNoCallBackEntityList = clickHouseOdsMapper.queryProductHsNoCallBackWithdrawUser(product,logday,queryHour);
        if (Lists.noEmpty(userHsNoCallBackEntityList)){
            result.addAll(userHsNoCallBackEntityList);
        }
        // 单产品单日超过1050 不执行拉灰
        if (result.size() > 1050){
            log.info("单条规则拉灰过多不再拉黑");
            return new ArrayList<>();
        }
        return result;
    }


    public List<Long> queryNoWithdrawCallUser(String product){
        String rest = clickHouseOdsMapper.queryHsWithdrawNoCallUser(product);
        return JSON.parseArray(rest,Long.class);
    }


    public Map<String,String> queryDeviceIdOnlyThreeDays(String product,Long userId){
        UserDeviceEntity entity = clickHouseOdsMapper.queryUserDeviceInfo(product,userId.toString());
        Map<String,String> resMap = new HashMap<>();

        if (entity != null){
            resMap.put("oaid",entity.getOaid());
            resMap.put("imei",entity.getImei());
            resMap.put("androidId",entity.getAndroidId());
            resMap.put("deviceId",entity.getDeviceId());
        }

        return resMap;
    }

    public List<UserCsjEcpmExEntity> queryExChannelAndM(Integer csjRate,Integer pv,
                                                        Integer uv,Integer avgEcpm,
                                                        Integer modelType,Integer includeType,
                                                        List<String> channelStartList){
        String rx = channelStartList.stream().map(r-> "^"+r).collect(Collectors.joining("|"));
        return clickHouseOdsMapper.queryExChannel((double)csjRate/100,pv,uv,avgEcpm,modelType,includeType,rx);
    }

    public List<UserCsjEcpmExEntity> queryExChannelAndMGdt(Integer csjRate,Integer pv, Integer uv,Integer avgEcpm){
        List<UserCsjEcpmExEntity> result = new ArrayList<>();
        List<UserCsjEcpmExEntity> exEntities = clickHouseOdsMapper.queryExChannelGdt((double)csjRate/100,pv,uv,avgEcpm,0);
        if (Lists.noEmpty(exEntities)){
            result.addAll(exEntities);
        }
        List<UserCsjEcpmExEntity> exEntitiesModel = clickHouseOdsMapper.queryExChannelGdt((double)csjRate/100,pv,uv,avgEcpm,1);
        if (Lists.noEmpty(exEntitiesModel)){
            result.addAll(exEntitiesModel);
        }
        return result;
    }

    public List<UserMacIpCheckEntity> queryExChannelAndMUser(String product,
                                         List<RealtimeCsjRulesDto.ActionLimit> actionLimitList,
                                         List<String> channelList,
                                         List<String> manuList,
                                         List<String> modelList){

        if (Lists.isEmpty(actionLimitList)){
            return new ArrayList<>();
        }
        return clickHouseOdsMapper.queryExChannelUserAndDevice(product,actionLimitList,channelList,manuList,modelList);
    }


    public List<UserEcpmCheckBean> queryExDrChannel(){
        return clickHouseOdsMapper.queryExDrChannelToday();
    }

    public UserMacIpCheckEntity queryExDrUser(String product,Double avgEcpm){
        return clickHouseOdsMapper.queryExDrUser(product,avgEcpm);
    }

    public List<Long> queryBiddingAdId(){
        return clickHouseDwdMapper.queryBiddingAdList();
    }

    public List<AdEcpmConfig> queryAdEcpmConf(){
        return clickHouseDwdMapper.queryEcpmConfig();
    }

    public List<String> getRiskIpList(){
        String result = clickHouseOdsMapper.queryRiskIpList();
        if (Strings.isEmpty(result)){
            return new ArrayList<>();
        }
        return JSON.parseArray(result,String.class);
    }

    public List<AdTypeEntity> getAllAdType(){
        return clickHouseDwdMapper.queryAllAdType();
    }

    public List<UserCallBackBean> queryUserGdtGap(List<Integer> appIdList){
        return clickHouseOdsMapper.queryGdtGap(appIdList);
    }


    public List<WithdrawExChannel> queryWithdrawExChannel(){
        return clickHouseOdsMapper.queryWithdrawExChannel();
    }

    public List<WithdrawExChannelAndModel> queryWithdrawExChannelModel(){
        return clickHouseOdsMapper.queryWithdrawExChannelModel();
    }
    public List<WithdrawExChannelAndModel> queryWithdrawExChannelOa(){
        return clickHouseOdsMapper.queryWithdrawExChannelOa();
    }

    public List<WithdrawExChannelAndModel> queryWithdrawBigExChannel(){
        return clickHouseOdsMapper.queryWithdrawBigExChannel();
    }
    public List<SkipAdEntity> queryCsjIncomeDelayUser(){
        return clickHouseOdsMapper.queryCsjIncomeDelayUser();
    }

    public List<WithdrawExChannelAndModel> queryWithdrawBigHourExChannel(){
        return clickHouseOdsMapper.queryWithdrawBigHourExChannel();
    }
    public List<WithdrawExChannelAndModel> queryWithdrawBigHourNatureExChannel(){
        return clickHouseOdsMapper.queryWithdrawBigHourNatureExChannel();
    }
    public List<WithdrawExChannelAndModel> queryWithdrawBigHourStopExChannel(){
        return clickHouseOdsMapper.queryWithdrawBigHourStopExChannel();
    }

    public List<UserActive> queryActiveUserByChannel(String product,String channel, Date beginTime, Date endTime){
        return clickHouseOdsMapper.queryActiveUserByChannel(product,channel,beginTime,endTime);
    }

    public List<UserGrayChannelEntity> queryManufacturerModelList(){
        return clickHouseOdsMapper.queryManufacturerModelList();
    }

    public List<Map<String,String>> selectTestAccount(){
        return clickHouseOdsMapper.selectTestAccount();
    }

    public List<UserMacIpCheckEntity> queryIosNaturalUserInfo() {
        return clickHouseOdsMapper.queryIosNaturalUserInfo();
    }

    public List<UserMacIpCheckEntity> queryIosIncomeAbnormalUserInfo() {
        return clickHouseOdsMapper.queryIosIncomeAbnormalUserInfo();
    }

    public List<UserMacIpCheckEntity> queryReqIdAbnormalUserList() {
        return clickHouseOdsMapper.queryReqIdAbnormalUserList();
    }

    public List<UserMacIpCheckEntity> queryAndroidBaiduIncomeAbnormalUserInfo() {
        return clickHouseOdsMapper.queryAndroidBaiduIncomeAbnormalUserInfo();
    }

    public List<UserMacIpCheckEntity> queryNonBaiduAndroidIncomeAbnormalUserInfo() {
        return clickHouseOdsMapper.queryNonBaiduAndroidIncomeAbnormalUserInfo();
    }

    public List<UserMacIpCheckEntity> queryKsdrHwGdtIncomeAbnormalUserInfo() {
        return clickHouseOdsMapper.queryKsdrHwGdtIncomeAbnormalUserInfo();
    }

    public List<UserMacIpCheckEntity> queryEcpmFilterCountByGroup10() {
        return clickHouseOdsMapper.queryEcpmFilterCountByGroup10();
    }

    public List<UserMacIpCheckEntity> queryUserCallBackInterceptNonBaiduUserInfo() {
        return clickHouseOdsMapper.queryUserCallBackInterceptNonBaiduUserInfo();
    }

    public List<UserMacIpCheckEntity> queryBdArpuIncAbnormalUserInfo() {
        return clickHouseOdsMapper.queryBdArpuIncAbnormalUserInfo();
    }

    public List<UserMacIpCheckEntity> queryGdtGapAbnormalUserInfo() {
        return clickHouseOdsMapper.queryGdtGapAbnormalUserInfo();
    }

    public List<UserMacIpCheckEntity> queryBaiduIncomeAbnormalGroup5UserInfo() {
        return clickHouseOdsMapper.queryBaiduIncomeAbnormalGroup5UserInfo();
    }

    public List<UserMacIpCheckEntity> queryUserCallBackAbnormaList() {
        return clickHouseOdsMapper.queryUserCallBackAbnormaList();
    }

    public List<UserMacIpCheckEntity> queryAllExIpUser(Integer count) {
        return clickHouseOdsMapper.queryAllExIpUser(count);
    }

    public List<UserMacIpCheckEntity> querySgmGdtGroupUserInfo() {
        return clickHouseOdsMapper.querySgmGdtGroupUserInfo();
    }

    public List<UserMacIpCheckEntity> queryMultiReqIdAbnormalUserList() {
        return clickHouseOdsMapper.queryMultiReqIdAbnormalUserList();
    }

    public List<UserMacIpCheckEntity> queryGdtGapExUserInfo() {
        return clickHouseOdsMapper.queryGdtGapExUserInfo();
    }
}
