package com.coohua.user.event.biz.dc.mapper;

import com.coohua.user.event.biz.dc.dto.LtvAllQueryResponse;
import com.coohua.user.event.biz.dc.entity.DailyLtvAllAutoCalcuSub;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * <p>
 *  Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2021-08-24
 */
public interface DailyLtvAllAutoCalcuSubMapper extends BaseMapper<DailyLtvAllAutoCalcuSub> {

    @Select({" SELECT product_group, sum((platform_income - cpa - withdraw_cost) * toufang_device + IFNULL(ltv_other,0) + IFNULL(cost_protect,0)" +
            " + IFNULL(jlj_ks,0) + IFNULL(pre_kf_cost,0)) as rosResult " +
            " FROM ads.daily_ltv_all_auto_calcu_sub  where period = 45 and logday =#{logday}  group by product_group"})
    List<LtvAllQueryResponse> queryAllSubLtv(@Param("logday") String logday);
}
