package com.coohua.user.event.biz.dc.mapper;

import com.coohua.user.event.biz.dc.entity.AbUserEcpmEntity;
import com.coohua.user.event.biz.dc.entity.GrayUserEntity;
import org.apache.ibatis.annotations.Insert;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2021/8/18
 */
public interface AbUserEcpmMapper {

    @Insert({
            "<script>",
            "INSERT INTO ads.ab_user_ecpm (" +
                    "app_id," +
                    "user_id," +
                    "strategy_id" +
                    ")" +
                    "VALUES",
            "<foreach collection='abUserEcpmEntityList'  item='abUser' separator=','>" ,
            "(" +
                    "#{abUser.appId}," +
                    "#{abUser.userId}," +
                    "#{abUser.strategyId}" +
                    ")",
            "</foreach>",
            "</script>",
    })
    int batchInsertAbUser(@Param("abUserEcpmEntityList") List<AbUserEcpmEntity> abUserEcpmEntityList);
}
