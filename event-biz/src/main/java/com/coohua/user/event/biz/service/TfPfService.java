package com.coohua.user.event.biz.service;

import com.coohua.user.event.biz.click.mapper.ClickHouseTfPfMapper;
import com.coohua.user.event.biz.service.bean.TfPfAdBean;
import com.coohua.user.event.biz.service.bean.TfPfUserBean;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2024/2/2
 */
@Slf4j
@Service
public class TfPfService {

    @Resource
    private ClickHouseTfPfMapper clickHouseTfPfMapper;


    public List<TfPfAdBean> queryWaitPfAd(String logday, String product){
        // 只对一组非短剧类产品生效
        return clickHouseTfPfMapper.queryTfPfAd(logday);
    }

    public List<TfPfUserBean> queryWaitPfUser(List<String> adIds,String logday){
        return clickHouseTfPfMapper.queryTfPfUser(adIds,logday);
    }
}
