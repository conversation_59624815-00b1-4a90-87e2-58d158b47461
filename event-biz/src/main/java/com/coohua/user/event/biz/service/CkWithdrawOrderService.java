package com.coohua.user.event.biz.service;

import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.coohua.user.event.biz.click.entity.ProductEntity;
import com.coohua.user.event.biz.click.service.CkBatchDailyRepository;
import com.coohua.user.event.biz.click.service.CkBatchRepository;
import com.coohua.user.event.biz.click.service.ClickHouseService;
import com.coohua.user.event.biz.dc.entity.WithdrawOrderDetail;
import com.coohua.user.event.biz.kafka.BizKafkaSender;
import com.coohua.user.event.biz.user.entity.OsBean;
import com.coohua.user.event.biz.user.entity.UserMeta;
import com.coohua.user.event.biz.user.entity.WechatApp;
import com.coohua.user.event.biz.user.mapper.BaseQueryMapper;
import com.coohua.user.event.biz.user.service.BpMallService;
import com.coohua.user.event.biz.user.service.BpUserService;
import com.coohua.user.event.biz.util.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.ArrayBlockingQueue;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2021/2/5
 * clickhouse 提现表维护
 * CK使用去重引擎，实时数据可以放部分小范围数据进入
 */
@Slf4j
@Service
public class CkWithdrawOrderService {
    @Autowired
    private ClickHouseService clickHouseService;
    @Autowired
    private BpMallService bpMallService;
    @Autowired
    private BpUserService bpUserService;
    @Resource
    private BaseQueryMapper baseQueryMapper;
    @Autowired
    private CkBatchRepository ckBatchRepository;
    @Autowired
    private CkBatchDailyRepository ckBatchDailyRepository;
    @Autowired
    private AppDrawConf appDrawConf;
    @Autowired
    private BizKafkaSender bizKafkaSender;

    private boolean flag;
    private boolean flagAll;
    // 40W缓冲区
    private final ArrayBlockingQueue<WithdrawOrderDetail> arrayBlockingQueue = new ArrayBlockingQueue<>(Math.toIntExact(DEFAULT_PAGE * 20));
    private final ArrayBlockingQueue<WithdrawOrderDetail> arrayBlockingQueueDaily = new ArrayBlockingQueue<>(Math.toIntExact(DEFAULT_PAGE * 20));

    private final static Long DEFAULT_PAGE = 20000L;

    public static void main(String[] args) {
        System.out.println(DateUtil.getCurrHourTime(new Date()));
        Date crHour = DateUtil.stringToDate(DateUtil.getCurrHourTime(new Date()),DateUtil.COMMON_TIME_FORMAT);
        System.out.println(DateUtil.dateIncreaseBySeconds(crHour,- Integer.parseInt("2") * 60 * 60));
    }
//
//    @PostConstruct
//    public void test(){
//        appDrawConf.refresh();
//        transferDataToCk();
//    }

    public void transferDataToCk(String param){
        Date now = new Date();
        Date crHour = DateUtil.stringToDate(DateUtil.getCurrHourTime(new Date()),DateUtil.COMMON_TIME_FORMAT);
        // 小贷半小时
        Date begin = DateUtil.dateIncreaseBySeconds(crHour,- 60 * 60);
        if (StringUtils.isNotBlank(param)){
            begin = DateUtil.dateIncreaseBySeconds(crHour,- Integer.parseInt(param) * 60 * 60);
        }
        Long start = begin.getTime();
        Long end = now.getTime();
        transfer(start,end,begin,now,this.arrayBlockingQueue,false);
        System.out.println("over");
    }

    public void transferDataToCkDaily(String inParam){
        Date now = new Date();
        Date logday = DateUtil.stringToDate(DateUtil.dateToString(now));
        // 两天
        Date begin = DateUtil.dateIncreaseByDay(logday,- 2);
        if (Strings.noEmpty(inParam)){
            String[] dates = inParam.split(",");
            logday = DateUtil.stringToDate(dates[1]);
            begin = DateUtil.stringToDate(dates[0]);
        }

        log.info("Query Range:{}~{}",DateUtil.dateToString(begin),DateUtil.dateToString(logday));
        Long start = begin.getTime();
        Long end = logday.getTime();
        List<String> logdayList = conertLogdayList(begin,logday);
        // 先DROP PARTITION
        log.info("transferDataToCkDaily: step1 {}", logdayList);
        logdayList.forEach(ckBatchDailyRepository::dropPartition);
        // 拉取数据
        log.info("transferDataToCkDaily: step2");
        transfer(start,end,begin,now,this.arrayBlockingQueueDaily,true);
        log.info("transferDataToCkDaily: done");
        // 合并补充
//        logdayList.forEach(ckBatchDailyRepository::insertIntoDist);
    }

    public void transferHistoryDataToCkDaily(String inParam){
        if (StringUtils.isBlank(inParam)) {
            return;
        }

        Date now = new Date();
        Date logday = DateUtil.stringToDate(DateUtil.dateToString(now));
        // 两天
        Date begin = DateUtil.dateIncreaseByDay(logday,- 2);
        if (Strings.noEmpty(inParam)){
            String[] dates = inParam.split(",");
            logday = DateUtil.stringToDate(dates[1]);
            begin = DateUtil.stringToDate(dates[0]);
        }

        log.info("Query Range:{}~{}",DateUtil.dateToString(begin),DateUtil.dateToString(logday));
        Long start = begin.getTime();
        Long end = logday.getTime();
        List<String> logdayList = conertLogdayList(begin,logday);
        // 拉取数据
        log.info("transferDataToCkDaily: step2");
        transfer(start,end,begin,now,this.arrayBlockingQueueDaily,true);
        log.info("transferDataToCkDaily: done");
        // 合并补充
//        logdayList.forEach(ckBatchDailyRepository::insertIntoDist);
    }

    private static List<String> conertLogdayList(Date begin,Date end){
        List<String> logdayList = new ArrayList<>();
        int i = DateUtil.daysBetween(begin,end);
        for(int day =0; day< i; day ++){
            logdayList.add(DateUtil.dateToString(DateUtil.dateIncreaseByDay(begin,day)));
        }
        return logdayList;
    }

    private void transfer(Long start,Long end,Date begin,Date now,ArrayBlockingQueue<WithdrawOrderDetail> blockingQueue,Boolean allDay){
        log.info("[CK]开始迁移提现表详情.....");
        log.info("本批次计算区间{} - {}",start,end);
        log.info("本批次计算区间{} - {}",DateUtil.dateToStringWithTime(begin),DateUtil.dateToStringWithTime(now));

        List<ProductEntity> productEntityList = clickHouseService.queryAllProduct();

        List<WechatApp> wechatApps = baseQueryMapper.queryAllPkgConf();
        Map<Long,List<WechatApp>> wechatAppMap = wechatApps.stream().collect(Collectors.groupingBy(WechatApp::getAppId));

        if (allDay){
            flagAll = true;
//            CompletableFuture.runAsync(this::batchInsertDaily);
        }else {
            flag = true;
//            CompletableFuture.runAsync(this::batchInsert);
        }

        productEntityList.parallelStream().forEach(product->{
            try {
                List<String> tableNameList = AppDrawConf.appWithdrawTableMap.get(product.getId());
                List<WechatApp> wechatAppsNow = wechatAppMap.get((long) product.getId());
                if (wechatAppsNow == null || wechatAppsNow.size() == 0){
                    log.warn("配置异常....,未查询到pkgId {}", product);
                    return;
                }

                List<Long> pkgList = wechatAppsNow.stream().map(WechatApp::getId).collect(Collectors.toList());

                if (Lists.noEmpty(tableNameList)) {
                    tableNameList.forEach(key -> transfer(product, start, end, key, pkgList, blockingQueue, allDay));
                }else {
                    log.error(">>> {} -> {}",product,JSON.toJSONString(tableNameList));
                }
            }catch (Exception e){
                log.error("产品 {} 异常:",product,e);
            }
        });

        if (allDay) {
            flagAll = false;
        } else {
            flag = false;
        }
        log.info("结束迁移提现表详情.....");
    }

    private void batchInsert(){
        while (true) {
            if (!flag && this.arrayBlockingQueue.size() == 0){
                log.info("ArrayBlockingQueue 已消费完毕... 本批次结束");
                return;
            }
           try {
               ckBatchRepository.addToBatch(this.arrayBlockingQueue.take());
               // 最后一批或者当前无插入 暂时提交 size∈[0,20001]
               if (ckBatchRepository.batchCounter > DEFAULT_PAGE || this.arrayBlockingQueue.size() == 0){
                   ckBatchRepository.executeBatch();
               }
           }catch (Exception e){
               log.error("Ex:",e);
           }
        }
    }

    private void batchInsertDaily(){
        while (true) {
            if (!flagAll && this.arrayBlockingQueueDaily.size() == 0){
                log.info("ArrayBlockingQueueDaily 已消费完毕... 本批次结束");
                return;
            }
            try {
                ckBatchDailyRepository.addToBatch(this.arrayBlockingQueueDaily.take());
                // 最后一批或者当前无插入 暂时提交 size∈[0,20001]
                if (ckBatchDailyRepository.batchCounter > DEFAULT_PAGE || this.arrayBlockingQueueDaily.size() == 0){
                    ckBatchDailyRepository.executeBatch();
                }
            }catch (Exception e){
                log.error("Ex:",e);
            }
        }
    }

    private void transfer(ProductEntity entity,Long begin,Long end,String tableName,List<Long> pkgIdList,ArrayBlockingQueue<WithdrawOrderDetail> arrayBlockingQueue, boolean allDay){
        // 再往前一天
        Long beforeOneDay = begin - 2*24 * 60 * 60 * 1000;
        if (!allDay){
            beforeOneDay = begin;
        }
        Long count = bpMallService.countOrderList(tableName,beforeOneDay);
        Long minId = bpMallService.selectMinId(tableName,beforeOneDay);
        Long maxId = bpMallService.selectMaxId(tableName,end);

        if (count == 0 || maxId == null || maxId == 0L){
            log.info("产品[{}]无需同步数据...",entity.getProductName());
            return;
        }

        log.info("产品[{}]需要同步{}条数据",entity.getProductName(),count);
        if (count > DEFAULT_PAGE){
            for (Long i= minId; i<= maxId; i= i+DEFAULT_PAGE){
                Long indexOffset = DEFAULT_PAGE;
                if (i + DEFAULT_PAGE > maxId){
                    indexOffset = maxId - i;
                }
                doMainSolve(tableName,i,indexOffset,entity,pkgIdList,arrayBlockingQueue,begin, end, allDay);
            }
        }else {
            doMainSolve(tableName,minId,DEFAULT_PAGE,entity,pkgIdList,arrayBlockingQueue,begin, end, allDay);
        }

    }

    private void doMainSolve(String tableName, Long i,
                             Long indexOffset,
                             ProductEntity entity,
                             List<Long> pkgList,
                             ArrayBlockingQueue<WithdrawOrderDetail> arrayBlockingQueue,
                             long begin, Long end, boolean allDay){
        log.info("正在处理第{}-{}",i,indexOffset);
        List<WithdrawOrderDetail> withdrawOrderDetails = bpMallService.queryOrderList(tableName,i,indexOffset);

        // 补充用户信息
        Map<Long, UserMeta> userEntityMap = queryUserEntityMap(withdrawOrderDetails,pkgList);
        Map<Long, String> osMap = queryOsMap(withdrawOrderDetails,entity);
        withdrawOrderDetails.parallelStream().forEach(withdrawOrderDetail -> {
            withdrawOrderDetail.setId(null);
            withdrawOrderDetail.setProductCn(entity.getProductName());
            withdrawOrderDetail.setProductEn(entity.getProduct());
            withdrawOrderDetail.setProductGroup(entity.getProductGroup());
            UserMeta userEntity = userEntityMap.getOrDefault(withdrawOrderDetail.getUserId(),new UserMeta());
            String os = osMap.get(withdrawOrderDetail.getId());
            if (Strings.noEmpty(os)){
                withdrawOrderDetail.setOs(os);
            }else {
                if (userEntity.getOs() != null) {
                    withdrawOrderDetail.setOs(userEntity.getOs() == 1 ? "ios" : "android");
                    if (userEntity.getOs() == 2) {
                        withdrawOrderDetail.setOs("wmin");
                    }
                } else {
                    withdrawOrderDetail.setOs("android");
                }
            }
            // 一念修仙改为世外人家2
            if ("yinianxiuxian".equals(entity.getProduct()) && "ios".equals(withdrawOrderDetail.getOs())){
                withdrawOrderDetail.setProductCn("世外人家2");
                withdrawOrderDetail.setProductEn("xiuxianswrj");
            }
            //
            if ("zhuzhushijie".equals(entity.getProduct())){
                withdrawOrderDetail.setProductCn("猪猪世界");
                withdrawOrderDetail.setProductEn("zzsj");
            }
            withdrawOrderDetail.setDeviceId(userEntity.getDeviceId());
            withdrawOrderDetail.setLogdayCk(new java.sql.Date(withdrawOrderDetail.getUpdateTime()));
            try {
                if (withdrawOrderDetail.getLogdayCk().getTime()>=begin && withdrawOrderDetail.getLogdayCk().getTime() < end){
                    if (allDay) {
                        // kafka
                        bizKafkaSender.sendToWithdraw(JSONUtil.toJsonStr(withdrawOrderDetail));
                    } else {
                        bizKafkaSender.sendToWithdrawToday(JSONUtil.toJsonStr(withdrawOrderDetail));
                    }
                }
            } catch (Exception e) {
                log.error("sync withdraw kafka EX:",e);
            }
        });

    }


    private Map<Long, UserMeta> queryUserEntityMap(List<WithdrawOrderDetail> withdrawOrderDetails,List<Long> pkgList){
        List<Long> userList = withdrawOrderDetails.parallelStream().map(WithdrawOrderDetail::getUserId).collect(Collectors.toList());
        if (userList.size() == 0){
            return new HashMap<>();
        }
        try {
            List<UserMeta> userEntityList = bpUserService.queryUserByIds(userList,pkgList);
            return userEntityList.parallelStream().collect(Collectors.toMap(UserMeta::getUserId, userEntity->userEntity,(u1,u2) -> u1));
        } catch (Exception e){
            log.error("Ex:",e);
            DingTalkPushUtils.pushMessage("提现-离线任务事务超时，请注意！");
            return new HashMap<>();
        }
    }

    private Map<Long,String> queryOsMap(List<WithdrawOrderDetail> withdrawOrderDetails,ProductEntity productEntity){
        List<Long> ids = withdrawOrderDetails.stream().map(WithdrawOrderDetail::getId).collect(Collectors.toList());
        Set<String> days = withdrawOrderDetails.stream()
                .map(r-> DateUtil.dateToString(new Date(r.getCreateTime()),DateUtil.ISO_DATE_FORMAT))
                .collect(Collectors.toSet());
        try {
            Date day3Ago = DateUtil.dateIncreaseByDay(new Date(),-3);
            if (ids.size() > 0){
                List<OsBean> osBeanList = new ArrayList<>();
                for (String day : days){
                    String tableName = "bp_mall.biz_order_union_" + day;
                    if (DateUtil.stringToDate(day,DateUtil.ISO_DATE_FORMAT).after(day3Ago)) {
                        List<OsBean> osBeans = bpMallService.queryOsBeanList(tableName, ids, productEntity.getId());
                        if (osBeans.size() > 0) {
                            osBeanList.addAll(osBeans);
                        }
                    }
                }
                return osBeanList.stream().collect(Collectors.toMap(OsBean::getOrderNo, r->{
                    if (1==r.getOs()){
                        return "android";
                    }else if (2==r.getOs()){
                        return "ios";
                    }
                    return "";
                },(r1,r2)->r1));
            }
        }catch (Exception e){
            log.warn("QueryWithdrawEx:",e);
        }
        return new HashMap<>();
    }

}
