package com.coohua.user.event.biz.enums;

/**
 * <AUTHOR>
 * @since 2022/10/11
 */
public enum IncludeType {
    IN(1,"包含"),
    NOT_IN(0,"不包含");

    private Integer code;
    private String desc;

    IncludeType(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public Integer getCode() {
        return code;
    }

    public void setCode(Integer code) {
        this.code = code;
    }

    public String getDesc() {
        return desc;
    }

    public void setDesc(String desc) {
        this.desc = desc;
    }
}
