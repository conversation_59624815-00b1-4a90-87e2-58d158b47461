package com.coohua.user.event.biz.user.mapper;

import com.coohua.user.event.biz.user.entity.UserEntity;
import com.coohua.user.event.biz.user.entity.UserMeta;
import org.apache.ibatis.annotations.Delete;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2020/9/17
 */
public interface BpUserMapper {


    @Select({
            "<script>",
            "select * from bp_user.user where id in ",
            "<foreach collection='ids' index='index' item='item' open='(' separator=',' close=')'> #{item}</foreach>",
            "</script>",
    })
    List<UserEntity> queryUserInfoByIdBatch(@Param("ids") List<Long> userIdList);



    @Select({
            "<script>",
            "select * from bp_user.user_meta where user_id in ",
            "<foreach collection='ids' index='index' item='item' open='(' separator=',' close=')'> #{item} </foreach>",
            " and pkg_id in ",
            "<foreach collection='pkgIds' index='index' item='item' open='(' separator=',' close=')'> #{item} </foreach>",
            "</script>",
    })
    List<UserMeta> queryUserInfoByIdBatchs(@Param("ids") List<Long> userIdList,@Param("pkgIds")List<Long> pkgList);


    @Select("       select * from bp_user.user_meta where user_id = #{userId} and pkg_id = (                " +
            "       select id from bp_user.wechat_app where app_id = #{appId})                                    ")
    UserMeta selectByUserIdAndPkgId(@Param("userId")Long userId,@Param("appId")Integer appId);


    @Delete("<script> delete from bp_user.user_gray where user_id in" +
            "<foreach collection='ids' index='index' item='item' open='(' separator=',' close=')'> #{item} </foreach>" +
            "</script>")
    int deleteUserGrayByUserIdList(@Param("ids") List<Long> userIdList);

    @Select("select user_id from bp_user.user_gray where reason = '只存在曝光埋点-拉灰'\n" +
            "  and create_time < unix_timestamp('2025-07-26 00:00:00') * 1000 and create_time >= unix_timestamp('2025-07-25 00:00:00') * 1000")
    List<Long> queryUserIdList();
}
