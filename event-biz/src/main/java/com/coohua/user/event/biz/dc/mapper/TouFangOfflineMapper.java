package com.coohua.user.event.biz.dc.mapper;

import com.coohua.user.event.biz.dc.entity.TouFangAllOfflineEntity;
import com.coohua.user.event.biz.dc.entity.TouFangOfflineEntity;
import org.apache.ibatis.annotations.Delete;
import org.apache.ibatis.annotations.Insert;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2020/9/7
 */
public interface TouFangOfflineMapper {

    @Select(("select id,product_name,channel_name,os,logday,sum(device_num) as device_num,sum(toufang_amount) as toufang_amount  from ads.toufang_offline where logday = #{dateStr} and os = 'android' group by product_name"))
    List<TouFangOfflineEntity> queryOfflineDataAndroid(@Param("dateStr") String dateStr);

    @Select(("select id,product_name,channel_name,os,logday,sum(device_num) as device_num,sum(toufang_amount) as toufang_amount " +
            "from ads.toufang_offline where logday = #{dateStr} and os = 'ios' group by product_name"))
    List<TouFangOfflineEntity> queryOfflineDataIos(@Param("dateStr") String dateStr);



    @Select({"select * from ads.toufang_offline where logday = #{dateStr}"})
    List<TouFangOfflineEntity> queryOfflineDataByDate(@Param("dateStr") String dateStr);

    @Insert({
            "<script>",
            "INSERT INTO ads.toufang_all_offline (" +
                    "logday," +
                    "app_name," +
                    "app_group," +
                    "os," +
                    "channel_name," +
                    "cods," +
                    "rebate_cost," +
                    "activate," +
                    "create_time," +
                    "update_time" +
                    ")" +
                    "VALUES" +
                    "<foreach collection='touFangOfflineEntityList'  item='touFangOfflineEntity' separator=','>" +
                    "(" +
                    "#{touFangOfflineEntity.logday}," +
                    "#{touFangOfflineEntity.appName}," +
                    "#{touFangOfflineEntity.appGroup}," +
                    "#{touFangOfflineEntity.os}," +
                    "#{touFangOfflineEntity.channelName}," +
                    "#{touFangOfflineEntity.cods}," +
                    "#{touFangOfflineEntity.rebateCost}," +
                    "#{touFangOfflineEntity.activate}," +
                    "#{touFangOfflineEntity.createTime}," +
                    "#{touFangOfflineEntity.updateTime}"+
                    ")" +
                    "</foreach>",
            "</script>",
    })
    int insertBatch(@Param("touFangOfflineEntityList") List<TouFangAllOfflineEntity> touFangOfflineEntityList);

    @Delete({"delete from ads.toufang_all_offline where logday = #{dateStr} and channel_name = 'toutiao'"})
    int delByLogDay(@Param("dateStr") String dateStr);

    @Delete({"delete from ads.toufang_all_offline where logday = #{dateStr} and channel_name = 'kuaishou'"})
    int delByLogDayKuaishou(@Param("dateStr") String dateStr);

    @Delete({"delete from ads.toufang_all_offline where logday = #{dateStr} and channel_name = 'guangdiantong'"})
    int delByLogDayTecent(@Param("dateStr") String dateStr);

    @Delete({"delete from ads.toufang_all_offline where logday = #{dateStr} and channel_name != 'toutiao'"})
    int delByLogDayNotToutiao(@Param("dateStr") String dateStr);

    @Delete({"delete from ads.toufang_all_offline where logday = #{dateStr} and channel_name not in ('toutiao','kuaishou','guangdiantong')"})
    int delByLogDayNotOther(@Param("dateStr") String dateStr);
}
