package com.coohua.user.event.biz.dc.service;

import com.coohua.user.event.biz.dc.entity.HistoryIncomeEntity;
import com.coohua.user.event.biz.dc.entity.HistoryResult1Entity;
import com.coohua.user.event.biz.dc.mapper.HistoryIncomeMapper;
import com.coohua.user.event.biz.util.DateUtil;
import com.coohua.user.event.biz.util.Strings;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2020/9/7
 */
@Slf4j
@Service
public class HistoryIncomeService {

    @Resource
    private HistoryIncomeMapper historyIncomeMapper;

    public void synchHistoryIncome(String logDay){
        if (Strings.isEmpty(logDay)){
            log.error("执行日期不能为空！");
            return;
        }
        log.info("开始历史数据同步.....");
        log.info("当前同步时间为{}",logDay);
        List<HistoryIncomeEntity> historyIncomeEntityList = historyIncomeMapper.selectByLogDay(logDay);
        if (historyIncomeEntityList == null || historyIncomeEntityList.size() == 0){
            log.warn("未查询到数据...跳过！");
            return;
        }

        historyIncomeEntityList.forEach(historyIncomeEntity -> {
            HistoryResult1Entity entity = new HistoryResult1Entity();
            entity.setLogday(historyIncomeEntity.getLogday());
            entity.setProduct(historyIncomeEntity.getProduct());
            entity.setOs(historyIncomeEntity.getOs());
            entity.setPlatformIncome(historyIncomeEntity.getPlatformIncome());
            entity.setTotalIncome(historyIncomeEntity.getTotalIncome());
            entity.setStraightIncome(historyIncomeEntity.getStraightIncome());
            int count = historyIncomeMapper.updateHistoryResult1(entity);
            log.info("本次对产品{}|{}|{}的更新影响行数为{}.",historyIncomeEntity.getLogday(),historyIncomeEntity.getProduct(),historyIncomeEntity.getOs(),count);
        });

        log.info("结束历史数据同步.....");
    }
}
