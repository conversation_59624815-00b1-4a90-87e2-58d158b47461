package com.coohua.user.event.biz.service;

import com.alibaba.fastjson.JSONObject;
import com.coohua.user.event.biz.util.HttpClients;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.MediaType;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @since 2021/2/20
 */
@Slf4j
@Service
public class AzkabanRemoteCallService {

    private final static String baseUrl = "http://172.16.11.50:9091";

    private static String sessionId;

    /**
     * Azkaban登录Session 24h过期
     */
//    @Scheduled(cron = "0 0 0/12 * * ?")
//    @PostConstruct
    private static void  refreshSessionId(){
        Map<String,Object> param = new HashMap<>();
        param.put("action","login");
        param.put("username","azkaban");
        param.put("password","azkaban");

        Map<String,Object> header = new HashMap<>();
        header.put("Content-Type", MediaType.APPLICATION_FORM_URLENCODED_VALUE);
        String res = HttpClients.fromPost(baseUrl,header,param);
        sessionId =  JSONObject.parseObject(res).getString("session.id");
        log.info(">>>> LOGIN AZKABAN,SessionID={}...",sessionId);
    }

    // 调起Azkaban任务
    private static String callExecute(String project,String flow){
        return HttpClients.GET(baseUrl+"/executor?ajax=executeFlow&project="
                + project +"&flow="+flow+"&session.id="+sessionId + "&flowOverride[product]=kxhy");
    }

    public static void main(String[] args) {
        refreshSessionId();
        callExecute("daily_ltv_auto_refresh","basic_config");
    }
}
