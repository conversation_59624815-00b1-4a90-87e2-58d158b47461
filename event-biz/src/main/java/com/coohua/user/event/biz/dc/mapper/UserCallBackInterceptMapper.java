package com.coohua.user.event.biz.dc.mapper;

import com.coohua.user.event.biz.dc.entity.UserCallBackIntercept;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Insert;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
* <AUTHOR>
* @description 针对表【user_call_back_intercept(异常用户激活回传拦截)】的数据库操作Mapper
* @createDate 2025-02-25 16:39:54
* @Entity com.coohua.user.event.biz.dc.entity.UserCallBackIntercept
*/
public interface UserCallBackInterceptMapper extends BaseMapper<UserCallBackIntercept> {

    @Insert("INSERT INTO `user-event`.user_call_back_intercept " +
            "(product,user_id,channel,os,source,account_id,remark,create_time,update_time) " +
            "VALUES (#{prodect},#{userId},#{channel},#{os},#{source},#{accountId},#{remark},#{createTime},#{updateTime})")
    @Override
    int insert(UserCallBackIntercept entity);

    @Select("select id,\n" +
            "       product,\n" +
            "       os,\n" +
            "       user_id,\n" +
            "       channel,\n" +
            "       source,\n" +
            "       account_id,\n" +
            "       remark,\n" +
            "       create_time,\n" +
            "       update_time\n" +
            "from `user-event`.user_call_back_intercept\n" +
            "where create_time >= #{todayStr}")
    List<UserCallBackIntercept> selectInterceptUserList(@Param("todayStr") String todayStr);
}




