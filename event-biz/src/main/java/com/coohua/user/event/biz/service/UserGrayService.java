package com.coohua.user.event.biz.service;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.coohua.bp.user.remote.api.UserRPC;
import com.coohua.bp.user.remote.dto.BlacklistUserParam;
import com.coohua.user.event.biz.click.entity.*;
import com.coohua.user.event.biz.click.service.ClickHouseService;
import com.coohua.user.event.biz.dc.dto.ActionRuleDto;
import com.coohua.user.event.biz.dc.entity.GrayUserEntity;
import com.coohua.user.event.biz.dc.entity.UserGrayIpEntity;
import com.coohua.user.event.biz.dc.mapper.GrayUserMapper;
import com.coohua.user.event.biz.dc.mapper.UserGrayIpMapper;
import com.coohua.user.event.biz.dc.service.RealtimeRuleService;
import com.coohua.user.event.biz.enums.ActionType;
import com.coohua.user.event.biz.enums.GrayType;
import com.coohua.user.event.biz.service.bean.GrayPreBean;
import com.coohua.user.event.biz.service.bean.GrayUserPostRequest;
import com.coohua.user.event.biz.service.bean.UserIpBean;
import com.coohua.user.event.biz.user.entity.GlobalConfigEntity;
import com.coohua.user.event.biz.user.entity.UserEntity;
import com.coohua.user.event.biz.user.mapper.BpConfigMapper;
import com.coohua.user.event.biz.user.mapper.UserCancelMapper;
import com.coohua.user.event.biz.user.service.BpUserService;
import com.coohua.user.event.biz.util.*;
import com.ctrip.framework.apollo.spring.annotation.ApolloJsonValue;
import com.pepper.metrics.core.ThreadFactory;
import com.weibo.api.motan.config.springsupport.annotation.MotanReferer;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Service;
import top.zrbcool.pepper.boot.jediscluster.JedisClusterClient;
import top.zrbcool.pepper.boot.jediscluster.JedisClusterClientRefer;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.Objects;
import java.util.*;
import java.util.concurrent.ArrayBlockingQueue;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2021/8/5
 */
@Slf4j
@Service
public class UserGrayService {

    @MotanReferer(basicReferer = "bp-userBasicRefererConfigBean")
    private UserRPC userRPC;

    @Autowired
    private ClickHouseService clickHouseService;
    @Resource
    private BpConfigMapper bpConfigMapper;
    @Resource
    private GrayUserMapper grayUserMapper;
    @Resource
    private UserGrayIpMapper userGrayIpMapper;
    @Autowired
    private BpUserService bpUserService;
    @Autowired
    private UserDeviceCheckService userDeviceCheckService;

    @Value("${enable.gray.user}")
    private Boolean enableGrayUser;

    @Value("${max.gray.user.count}")
    private Integer maxGrayUserCount;

    @Value("${check.gray.user.rate}")
    private Double grayUserRate;

    @Value("${check.gray.user.limit.count:1000}")
    private Integer limitCount;

    @ApolloJsonValue("${ip.gray.own.list}")
    private List<String> ownIpGrayList;

    @ApolloJsonValue("${hs.check.call.app.list:[637,652,657,659,662,663,675,681]}")
    private List<Integer> checkHsHistoryAppList;

    @ApolloJsonValue("${hs.check.call.gray.app.list:[662,637,655]}")
    private List<Integer> checkHsHistoryGrayAppList;

    @ApolloJsonValue("${manufacturer.check.channel.list:[]}")
    private List<String> manuCheckChannelList;

    @Autowired
    private TFUserService tfUserService;

    @Resource(name = "partitionJobTaskPool")
    private ThreadPoolTaskExecutor executor;

    @Autowired
    private RealtimeRuleService realtimeRuleService;
    @Autowired
    private AdCloseService adCloseService;

    @JedisClusterClientRefer(namespace = "user-event")
    private JedisClusterClient userEventJedisClusterClient;
    @ApolloJsonValue("${tx.check.qp.products:[\"jysgd\"]}")
    private List<String> txCheckQpProducts;

    @ApolloJsonValue("${user.gray.ios.natural.product.list:[\"sgdw\"]}")
    private List<String> userGrayIosNaturalProductList;


    public void runGray(String date){
        Date crashDate =  DateUtil.dateIncreaseByDay(DateUtil.stringToDate(DateUtil.dateToString(new Date())),-1);
        if (Strings.noEmpty(date)) {
            crashDate = DateUtil.stringToDate(date);
        }
        String logDay = DateUtil.dateToString(crashDate);
        log.info(">>> 用户曝光检测.执行日期:{}",logDay);
        List<ProductEntity> productEntityList = clickHouseService.queryAllProduct();
        try {
            Map<Integer,ProductEntity> productMap = productEntityList.stream()
                    .collect(Collectors.toMap(ProductEntity::getId, k->k,(k1, k2)->k1));
            solveDay(logDay,productMap);
        }catch (Exception e){
            log.error("QueryExposureException:",e);
        }

        log.info(">>> 用户曝光检测执行 {} 完毕...",logDay);


        log.info("开始设备检测....");
        productEntityList.forEach(productEntity -> {
            try {
                doFriendCheck(productEntity.getProduct(),logDay);
            }catch (Exception e){
                log.error("QueryFriendShipException:",e);
            }

        });
        log.info("设备检测完成....");
    }

//    @PostConstruct
    public void runEcpmCheck(){
        log.info(">>>>开始ECPM曝光巡查");
        List<ActionRuleDto> list = realtimeRuleService.queryEcpmConfig();
        list.forEach(actionRuleDto -> {
            try {
                log.info("--> Check Rule {} ",actionRuleDto.getName());
                List<String> needFilterPlatformList = new ArrayList<>();
                if (Lists.noEmpty(actionRuleDto.getPlatformLimit()) && !actionRuleDto.getPlatformLimit().contains(0)) {
                    needFilterPlatformList = actionRuleDto.getPlatformLimit().stream()
                            .map(r->{
                                switch (r){
                                    case 1: return "穿山甲";
                                    case 2: return "广点通";
                                    case 3: return "快手";
                                    case 4: return "百度";
                                    case 5: return "VIVO";
                                    case 6: return "OPPO";
                                }
                                return "未知";
                            }).collect(Collectors.toList());
                }

                List<InnerPullBean> userList = clickHouseService.userIdList(actionRuleDto.getProduct(),
                        actionRuleDto.getMaxEcpm(),
                        actionRuleDto.getMinEcpm(),
                        actionRuleDto.getVideoTimes(),
                        actionRuleDto.getChannelList(),
                        actionRuleDto.getModelList(),
                        actionRuleDto.getBrandList(),
                        actionRuleDto.getIpList(),
                        actionRuleDto.getSourceList(),
                        actionRuleDto.getNewUserType(),
                        actionRuleDto.getOcpcType(),
                        needFilterPlatformList
                );
                if (Lists.noEmpty(userList)){
                    if (ActionType.GRAY_BLACK.getType().equals(actionRuleDto.getActionType()) ||
                            ActionType.GRAY_BLACK_PART.getType().equals(actionRuleDto.getActionType())) {

                        boolean isGlobalGray = true;
                        if (ActionType.GRAY_BLACK_PART.getType().equals(actionRuleDto.getActionType())){
                            isGlobalGray = false;
                        }
                        boolean finalIsGlobalGray = isGlobalGray;
                        userList.forEach(innerPullBean -> {
                            List<String> userIdList = JSON.parseArray(innerPullBean.getUserStr(), String.class);
                            List<String> deviceIdList = JSON.parseArray(innerPullBean.getDeviceStr(), String.class);
                            String remark = String.format("ECPM∈[%s,%s]曝光%s次-产品配置规则", actionRuleDto.getMaxEcpm(),
                                    actionRuleDto.getMinEcpm(),
                                    actionRuleDto.getVideoTimes());
                            // 执行全局拉黑
                            grayUser(GrayType.USER, userIdList, remark, innerPullBean.getProduct(), "android", false, finalIsGlobalGray);
                            grayUser(GrayType.DEVICE, deviceIdList, remark, innerPullBean.getProduct(), "android", false, finalIsGlobalGray);
                        });
                    }else if (ActionType.LIMIT_ECPM.getType().equals(actionRuleDto.getActionType())){
                        // 是否设置 pv
                        // 是否设置 跳过
                        if (Lists.noEmpty(actionRuleDto.getPlatformLimitSkip())) {
                            userList.forEach(innerPullBean -> {
                                ProductEntity productEntity = AppConfig.productEnMap.get(innerPullBean.getProduct());
                                if (productEntity == null) {
                                    return;
                                }
                                List<String> userIdList = JSON.parseArray(innerPullBean.getUserStr(), String.class);
                                userIdList.forEach(usr -> {
                                    adCloseService.setSkipPlatform(productEntity.getId(), usr,
                                            actionRuleDto.getPlatformLimitSkip(),
                                            60 * 60 * 24 * 15);
                                });
                            });
                        }
                    }
                }
            }catch (Exception e){
                log.warn("Slove Task Error:",e);
            }
        });
        log.info(">>>>完成ECPM曝光巡查");
    }

    private void doExposureCheck(String product,String logday){
        log.info("开始计算产品曝光:{}-{}的数据",product,logday);
        List<UserExposureEntity> userExposureEntities = clickHouseService.queryUserExposure(product,logday);

        List<String> deviceId = userExposureEntities.stream().map(UserExposureEntity::getDeviceId).collect(Collectors.toList());
        if (deviceId.size() > 0) {
            String userIdStr = clickHouseService.queryUserInfo(product, deviceId);
            List<String> userList = JSON.parseArray(userIdStr, String.class);
            List<Long> userIdLongList = userList
                    .stream().map(Long::valueOf).collect(Collectors.toList());
            List<String> unionList = new ArrayList<>();
            if (userIdLongList.size() > 0) {
                unionList = bpUserService.queryUserById(userIdLongList)
                        .stream().map(UserEntity::getUnionId).collect(Collectors.toList());
            }
            List<String> finalUnionList = unionList;
            sendGray(Collections.singletonList(new GrayPreBean() {{
                setUnionId(finalUnionList);
                setUserId(userList);
                setDeviceId(deviceId);
            }}), product, "曝光pv异常-点击/曝光比率异常");
        }
        log.info("已完成曝光黑产设备检测:{}-{}",product,logday);
    }

    public void doUserGray(){
        log.info(">>>> 开始拉黑设备...");
        List<UserScoreEntity> userScoreEntities = clickHouseService.queryUserScoreEx();
        if (Lists.noEmpty(userScoreEntities)){
            List<UserScoreEntity> filterList = tfUserService.filterOCPCUser(userScoreEntities);
            Map<String,List<UserScoreEntity>> stringListMap = filterList.stream()
                    .collect(Collectors.groupingBy(UserScoreEntity::getRemark));
            stringListMap.forEach((remark,list)->{
                Map<String,List<UserScoreEntity>> productMap = list.stream().collect(Collectors.groupingBy(UserScoreEntity::getProduct));
                if ("分渠道投放用户占比过低-非投放用户拉黑".equals(remark)) {
                    // 查UserId
                    productMap.forEach((product, rlist) -> {
                        List<String> deviceIdList = rlist.stream().map(UserScoreEntity::getDeviceId)
                                .filter(device -> !device.startsWith("0000"))
                                .collect(Collectors.toList());
                        // 关联userId
                        ProductEntity productEntity = AppConfig.productEnMap.get(product);
                        if (productEntity == null){
                            log.warn("Pr Empty..");
                            return;
                        }
                        List<String> userIdLists = clickHouseService.queryRegisteredIdFromDevice(product,deviceIdList);
                        if (Lists.noEmpty(userIdLists)) {
                            userIdLists.forEach(userId -> {
                                sendUserGrayWithdrawReason(productEntity.getId(), userId, "分渠道投放用户占比过低-非投放用户拉灰");
                            });
                        }
                    });
                } else {
                    productMap.forEach((product, rlist) -> {
                        ProductEntity productEntity = AppConfig.productEnMap.get(product);
//                        if ("项目一组".equals(productEntity.getProductGroup()) && StringUtils.isNotBlank(remark) && remark.contains("AndrondIP地址前三段机型8以上")) {
//                            return;
//                        }

                        GrayPreBean grayPreBean = new GrayPreBean();
                        List<String> deviceIdList = rlist.stream().map(UserScoreEntity::getDeviceId)
                                .filter(device -> !device.startsWith("0000"))
                                .collect(Collectors.toList());
                        if (deviceIdList.size() == 0) {
                            return;
                        }
                        String userIdStr = clickHouseService.queryUserInfo(product, deviceIdList);
                        List<String> userList = JSON.parseArray(userIdStr, String.class);
                        grayPreBean.setDeviceId(deviceIdList);
                        if (userList == null) {
                            userList = new ArrayList<>();
                        }
                        userList.addAll(rlist.stream().map(UserScoreEntity::getUserId).collect(Collectors.toList()));
                        List<Long> userIdLongList = userList
                                .stream().filter(r -> Strings.noEmpty(r) && !"null".equals(r)).map(Long::valueOf).collect(Collectors.toList());
                        List<String> unionList = new ArrayList<>();
                        if (userIdLongList.size() > 0) {
                            unionList = bpUserService.queryUserById(userIdLongList)
                                    .stream().map(UserEntity::getUnionId).collect(Collectors.toList());
                        }
                        grayPreBean.setUserId(userList);
                        grayPreBean.setUnionId(unionList);
                        if (rlist.size() > 0) {
                            grayPreBean.setOs(rlist.get(0).getOs());
                            grayPreBean.setIp(rlist.get(0).getIp());
                        }
                        sendGray(Collections.singletonList(grayPreBean), product, remark);
                    });
                }
            });
        }

        // 实时规则补充 针对全量用户
//        List<UserMacIpCheckEntity> entities = clickHouseService.queryMacIpCheck();
//        if (Lists.noEmpty(entities)){
//            entities.forEach(e -> doUserGray(e.getUserIdStr(),e.getDeviceIdStr(),"快手达人/头条达人IP聚集",null));
//        }

//        List<UserMacIpCheckEntity> ipCheckEntities = clickHouseService.queryMacModelCheck();
//        if (Lists.noEmpty(ipCheckEntities)){
//            ipCheckEntities.forEach(e -> doUserGray(e.getUserIdStr(),e.getDeviceIdStr(),"快手达人/头条达人机型聚集",null));
//        }

//        List<UserMacIpCheckEntity> queryMacModelCheckLessModel = clickHouseService.queryMacModelCheckLessModel();
//        if (Lists.noEmpty(queryMacModelCheckLessModel)){
//            queryMacModelCheckLessModel.forEach(e -> doUserGray(e.getUserIdStr(),e.getDeviceIdStr(),"冷门机型聚集PV异常",e.getProduct()));
//        }

        List<UserMacIpCheckEntity> queryXiaoMiExModel = clickHouseService.queryXiaoMiExModel();
        if (Lists.noEmpty(queryXiaoMiExModel)){
            queryXiaoMiExModel.forEach(e -> doUserGray(e.getUserIdStr(),e.getDeviceIdStr(),"小米异常机型聚集",e.getProduct()));
        }

        // 华为机型异常Check
        List<UserMacIpCheckEntity> queryHuaWeiExModel = clickHouseService.queryHuaWeiExModel();
        if (Lists.noEmpty(queryHuaWeiExModel)){
            queryHuaWeiExModel.forEach(e -> doUserGray(e.getUserIdStr(),e.getDeviceIdStr(),"华为异常机型聚集",e.getProduct()));
        }

        // 三星机型异常Check
        List<UserMacIpCheckEntity> querySamsangExModel = clickHouseService.querySamsangExModel();
        if (Lists.noEmpty(querySamsangExModel)){
            querySamsangExModel.forEach(e -> doUserGray(e.getUserIdStr(),e.getDeviceIdStr(),"三星异常机型聚集",e.getProduct()));
        }

        // 冷门厂商Check
//        List<UserMacIpCheckEntity> queryManuColdUser = clickHouseService.queryManuColdUser();
//        if (Lists.noEmpty(queryManuColdUser)){
//            queryManuColdUser.forEach(e -> doUserGray(e.getUserIdStr(),e.getDeviceIdStr(),"冷门厂商用户PV过高",e.getProduct()));
//        }

        // 埋点异常
        List<UserMacIpCheckEntity> queryEventDistExp = clickHouseService.queryEventUploadExp();
        if (Lists.noEmpty(queryEventDistExp)){
            queryEventDistExp.forEach(e -> doUserGray(e.getUserIdStr(),e.getDeviceIdStr(),"用户曝光埋点异常",e.getProduct()));
        }
        // 穿山甲request_id repeat check
//        List<UserMacIpCheckEntity> queryCsjRequestIdRepeat = clickHouseService.queryCsjRequestIdRepeat();
//        if (Lists.noEmpty(queryCsjRequestIdRepeat)){
//            queryCsjRequestIdRepeat.forEach(e -> doUserGray(e.getUserIdStr(),e.getDeviceIdStr(),"穿山甲请求id@tag重复次数过高",e.getProduct()));
//        }
        log.info(">>>> 拉黑设备完成...");
    }

    public void userGraySub(){
        log.info("===> 开始用户拉黑巡查");
        // OV渠道专防
        List<UserMacIpCheckEntity> queryModelMatchedChannel = clickHouseService.queryModelMatchedChannel();
        if (Lists.noEmpty(queryModelMatchedChannel)){
            queryModelMatchedChannel.forEach(e -> doUserGray(e.getUserIdStr(),e.getDeviceIdStr(),"OV渠道-非投放其他厂商用户",e.getProduct()));
        }
        // 用户累计arpu
        List<UserMacIpCheckEntity> queryExArpuUser = clickHouseService.queryExArpuUser();
        if (Lists.noEmpty(queryExArpuUser)){
            queryExArpuUser.forEach(e -> doUserGray(e,"用户累计ARPU-分平台占比异常"));
        }
        // IP聚集
        List<UserMacIpCheckEntity> queryExIpUser = clickHouseService.queryExIpUser();
        if (Lists.noEmpty(queryExIpUser)){

            // ip错误 一组暂下线 20250320
            queryExIpUser.forEach(e -> {
                ProductEntity productEntity = AppConfig.productEnMap.get(e.getProduct());
//                if (productEntity != null && !"项目一组".equals(productEntity.getProductGroup())) {
                    doUserGray(e.getUserIdStr(), e.getDeviceIdStr(), "IP聚集异常-同机型超过10", e.getProduct());
//                }
            });


            // IP记录黑名单进行实时拉黑
            // 小黑屋暂缓
//            List<String> ipMap = queryExIpUser.stream().map(UserMacIpCheckEntity::getIp).collect(Collectors.toList());
//            Date now = new Date();
//            String key = "{user:ip}:ip:gray:list"+DateUtil.dateToString(now,DateUtil.ISO_DATE_FORMAT);
//            // 进小黑屋
//            userEventJedisClusterClient.set(key,JSON.toJSONString(ipMap));
//            log.info("==> ip :{} join ExIp",JSON.toJSONString(ipMap));
//            userEventJedisClusterClient.expire(key,60*60*24*3);
        }
        // IOS RequestID 重复 2023-11-09 下线
//        List<UserMacIpCheckEntity> queryRepeatRequestId = clickHouseService.queryRepeatRequestId();
//        if (Lists.noEmpty(queryRepeatRequestId)){
//            queryRepeatRequestId.forEach(e -> doUserGray(e.getUserIdStr(),e.getDeviceIdStr(),"IOS-RequestID重复过多",e.getProduct()));
//        }
        // ECPM核查
        List<UserMacIpCheckEntity> highEcpmGreyUserList = queryHighEcpmExposeUser();

        if (Lists.noEmpty(highEcpmGreyUserList)){
            highEcpmGreyUserList.forEach(e -> doUserGray(e.getUserIdStr(),e.getDeviceIdStr(),"高ECPM曝光次数过多",e.getProduct()));
        }
        // PV核查
        List<UserMacIpCheckEntity> querPvExCount = clickHouseService.queryPvExUser();
        if (Lists.noEmpty(querPvExCount)){
            querPvExCount.forEach(e -> doUserGray(e.getUserIdStr(),e.getDeviceIdStr(),"PV过多异常",e.getProduct()));
        }

        // 内拉新渠道check 老用户设备
        // 20250726 ck压力大 暂下线
//        List<UserMacIpCheckEntity> queryActiveNlxOldUser = clickHouseService.queryActiveNlxOldUser();
//        if (Lists.noEmpty(queryActiveNlxOldUser)){
//            queryActiveNlxOldUser.forEach(e -> doUserGray(e.getUserIdStr(),e.getDeviceIdStr(),"内拉新渠道更换设备过多",e.getProduct()));
//        }
        // 新版本Click/Pv核查
        List<UserMacIpCheckEntity> queryClickExUser = clickHouseService.queryClickExUser(Collections.singletonList("视频"));
        if (Lists.noEmpty(queryClickExUser)){
            queryClickExUser.forEach(e -> doUserGray(e.getUserIdStr(),e.getDeviceIdStr(),"激励视频-点击/曝光比率异常",e.getProduct()));
        }
        List<UserMacIpCheckEntity> queryClickExUserCp = clickHouseService.queryClickExUser(Arrays.asList("插屏","新插屏"));
        if (Lists.noEmpty(queryClickExUserCp)){
            queryClickExUserCp.forEach(e -> doUserGray(e.getUserIdStr(),e.getDeviceIdStr(),"插屏-点击/曝光比率异常",e.getProduct()));
        }


        List<UserMacIpCheckEntity> queryKsViewExUser = clickHouseService.queryKuaishouViewExUser();
        if (Lists.noEmpty(queryKsViewExUser)){
            queryKsViewExUser.forEach(e -> doUserGray(e.getUserIdStr(),e.getDeviceIdStr(),"快手平台广告曝光异常",e.getProduct()));
        }


        List<UserMacIpCheckEntity> queryQpVideoExUser = clickHouseService.queryQpVideoExUser();
        if (Lists.noEmpty(queryQpVideoExUser)){
            queryQpVideoExUser.forEach(e -> doUserGray(e.getUserIdStr(),e.getDeviceIdStr(),"全屏视频收入占比异常",e.getProduct()));
        }
        List<UserMacIpCheckEntity> queryZiRanQpVideoExUser = clickHouseService.queryZiRanQpVideoExUser();
        if (Lists.noEmpty(queryZiRanQpVideoExUser)){
            queryZiRanQpVideoExUser.forEach(e -> doUserGray(e.getUserIdStr(),e.getDeviceIdStr(),"自然量全屏视频收入占比异常",e.getProduct()));
        }
        if(CollectionUtils.isNotEmpty(txCheckQpProducts)) {
            List<UserMacIpCheckEntity> queryTxQpVideoExUser = clickHouseService.queryTxQpVideoExUser(txCheckQpProducts);
            if (Lists.noEmpty(queryTxQpVideoExUser)) {
                queryTxQpVideoExUser.forEach(e -> doUserGray(e.getUserIdStr(), e.getDeviceIdStr(), "提现大于5元, 全屏视频收入占比异常", e.getProduct()));
            }
        }

        if(CollectionUtils.isNotEmpty(manuCheckChannelList)) {
            List<UserMacIpCheckEntity> manuCheckExUser = clickHouseService.queryManuExCloseAd(manuCheckChannelList);
            int hour = LocalDateTime.now().getHour();
            if (Lists.noEmpty(manuCheckExUser) && hour >= 7) {
                manuCheckExUser.forEach(e -> doUserGray(e.getUserIdStr(), e.getDeviceIdStr(), "篡改厂商机型", e.getProduct()));
            } else {
                log.info("篡改厂商机型 " + JSON.toJSONString(manuCheckExUser));
            }
        }
        List<UserMacIpCheckEntity> luanModelExUser = clickHouseService.queryLuanModelExCloseAd();
        if (Lists.noEmpty(luanModelExUser)) {
            luanModelExUser.forEach(e -> doUserGray(e.getUserIdStr(), e.getDeviceIdStr(), "篡改厂商机型", e.getProduct()));
        }

//        List<UserMacIpCheckEntity> changeReqIdExUser = clickHouseService.queryChangeReqIdCloseAd();
        List<UserMacIpCheckEntity> changeReqIdExUser = clickHouseService.queryChangeReqId();
        if (Lists.noEmpty(changeReqIdExUser)) {
            List<UserMacIpCheckEntity> filterList = new ArrayList<>();
            for (UserMacIpCheckEntity userInfo : changeReqIdExUser) {
                if (StringUtils.isBlank(userInfo.getExtend1())) {
                    continue;
                }
                List<String> reqIds = JSONObject.parseArray(userInfo.getExtend1(), String.class);
                boolean flag = false;
                for (String reqId : reqIds) {
                    if (reqId == null || reqId.length() <= 13) {
                        flag = true;
                        break;
                    }

                    long timestampValue = Long.parseLong(reqId.substring(0, 13));

//                    long oneYearInMillis = 365L * 24 * 60 * 60 * 1000;
//                    boolean isInYearRange = timestampValue >= (currentTime - oneYearInMillis)
//                            && timestampValue <= (currentTime + oneYearInMillis);

                    Calendar calendar = Calendar.getInstance();
                    calendar.setTimeInMillis(timestampValue);
                    int year = calendar.get(Calendar.YEAR);
                    boolean isReasonableYear = year >= 2020 && year <= 3000;
                    // 时间不在合理范围内，拉黑
                    if (!isReasonableYear) {
                        flag = true;
                        break;
                    }
                }
                if (flag) {
                    filterList.add(userInfo);
                }
            }
            if (CollectionUtils.isNotEmpty(filterList)) {
                filterList.forEach(e -> doUserGray(e.getUserIdStr(), e.getDeviceIdStr(), "篡改ReqId", e.getProduct()));
            }
        }

        List<UserMacIpCheckEntity> queryExChannelBlackExUser = clickHouseService.queryExChannelBlackExCloseAd();
        if (Lists.noEmpty(queryExChannelBlackExUser)){
            queryExChannelBlackExUser.forEach(e -> doUserGray(e.getUserIdStr(),e.getDeviceIdStr(),"M2007J1SC异常用户",e.getProduct()));
        }


        // 曝光存在重复 req_id
        List<SkipAdEntity> repeatExposureUserList = clickHouseService.queryRepeatExposureExCloseAd();
        skipAdByPlatform(repeatExposureUserList, 60 * 60 * 24 * 1);


        // 华为广告跳过设置
        List<SkipAdEntity> hwExUserList = clickHouseService.queryHuaWeiExCloseAd();
        if (Lists.noEmpty(hwExUserList)){
            for (SkipAdEntity skipAdEntity : hwExUserList){
                ProductEntity productEntity = AppConfig.productEnMap.get(skipAdEntity.getProduct());
                if (productEntity == null){
                    continue;
                }
                // 华为广告 8
                adCloseService.setSkipPlatform(productEntity.getId(),skipAdEntity.getUserid(),new ArrayList<Integer>(){{add(8);}},60*60*24*2);
            }
        }
        //分钟内曝光次数异常跳过
        List<SkipAdEntity> gdtExUserList = clickHouseService.queryGdtExCloseAd();
        // gdt插屏ecpm异常用户
        List<SkipAdEntity> gdtExAdTypeCloseAd = clickHouseService.queryGdtExAdTypeCloseAd();
        gdtExUserList.addAll(gdtExAdTypeCloseAd);
        if (Lists.noEmpty(gdtExUserList)){
            for (SkipAdEntity skipAdEntity : gdtExUserList){
                ProductEntity productEntity = AppConfig.productEnMap.get(skipAdEntity.getProduct());
                if (productEntity == null){
                    continue;
                }
                // 腾讯广告 2
                adCloseService.setSkipPlatform(productEntity.getId(),skipAdEntity.getUserid(),new ArrayList<Integer>(){{add(2);}},60*60*24*2);
            }
        }
        log.info("===> 结束用户拉黑巡查");
    }

    /**
     * 高ecpm曝光用户
     * @return
     */
    private List<UserMacIpCheckEntity> queryHighEcpmExposeUser() {
        List<UserMacIpCheckEntity> highEcpmGreyUserList = new ArrayList<>();

        List<UserMacIpCheckEntity> queryEcpmFilterCount = clickHouseService.queryEcpmFilterCount();
        List<UserMacIpCheckEntity> queryEcpmFilterGroup10 = clickHouseService.queryEcpmFilterCountByGroup10();
        if (CollectionUtils.isNotEmpty(queryEcpmFilterCount)) {
            List<UserMacIpCheckEntity> highEcpmGreyUserNoGroup10 = queryEcpmFilterCount
                    .stream()
                    .filter(e -> {
                        ProductEntity productEntity = AppConfig.productEnMap.get(e.getProduct());
                        if (productEntity == null) return true;
                        return !"项目十组".equals(productEntity.getProductGroup()) && !"项目七组".equals(productEntity.getProductGroup());
                    })
                    .collect(Collectors.toList());
            highEcpmGreyUserList.addAll(highEcpmGreyUserNoGroup10);
        }

        if (CollectionUtils.isNotEmpty(queryEcpmFilterGroup10)) {
            List<UserMacIpCheckEntity> highEcpmGreyUserGroup10 = queryEcpmFilterGroup10.stream().filter(e -> {
                ProductEntity productEntity = AppConfig.productEnMap.get(e.getProduct());
                if (productEntity == null) return true;
                return "项目十组".equals(productEntity.getProductGroup()) || "项目七组".equals(productEntity.getProductGroup());
            }).collect(Collectors.toList());

            highEcpmGreyUserList.addAll(highEcpmGreyUserGroup10);
        }

        return highEcpmGreyUserList;
    }

    public void userGraySub3() {
        log.info("===> 开始用户拉黑巡查3");
        //快手或广点通平台收入占比异常跳过
        List<SkipAdEntity> incomeSkipExExUserList = clickHouseService.queryIncomeSkipExCloseAd();
        skipAdByPlatform(incomeSkipExExUserList, 60*60*24*2);

        List<UserMacIpCheckEntity> queryIncomeBlackExUser = clickHouseService.queryIncomeBlackExCloseAd();
        if (Lists.noEmpty(queryIncomeBlackExUser)){
            queryIncomeBlackExUser.forEach(e -> doUserGray(e.getUserIdStr(),e.getDeviceIdStr(),"快手或广点通平台收入占比异常",e.getProduct()));
        }
        List<UserMacIpCheckEntity> queryKSDRGdtKsExUser = clickHouseService.queryKSDRGdtKsExCloseAd();
        if (Lists.noEmpty(queryKSDRGdtKsExUser)){
            queryKSDRGdtKsExUser.forEach(e -> doUserGray(e.getUserIdStr(),e.getDeviceIdStr(),"快手达人渠道收入占比过高跳过异常",e.getProduct()));
        }

        // 快手达人停快手广告
        List<SkipAdEntity> ksDrExUserList = clickHouseService.queryKSDRExCloseAd();
        skipAdByPlatform(ksDrExUserList, 60*60*24*2);
        log.info("===> 结束用户拉黑巡查3");
    }

    public void userGraySub4() {
        log.info("===> 开始用户拉黑巡查4");
        List<ProductEntity> productEntityList = clickHouseService.queryAllProduct();
        Map<String, List<ProductEntity>> groupMap = productEntityList.stream().collect(Collectors.groupingBy(ProductEntity::getProductGroup));

        for (Map.Entry<String, List<ProductEntity>> entry : groupMap.entrySet()) {
            if (!StringUtils.equalsAny(entry.getKey(), "项目一组", "项目五组", "项目七组", "项目十组", "中台")) {
                continue;
            }
            List<String> products = entry.getValue().stream().map(ProductEntity::getProduct).filter(StringUtils::isNotBlank).collect(Collectors.toList());
            // 下边这两个比较费时，一个一分钟 同产品一个用户对应多个渠道号/机型拉黑
            List<UserMacIpCheckEntity> queryManyChannelBlackExUser = clickHouseService.queryManyChannelBlackExCloseAd(products);
            if (Lists.noEmpty(queryManyChannelBlackExUser)){
                queryManyChannelBlackExUser.forEach(e -> doUserGray(e.getUserIdStr(),e.getDeviceIdStr(),"同产品一个用户对应多个渠道号拉黑",e.getProduct()));
            }
            List<UserMacIpCheckEntity> queryManyModelBlackExUser = clickHouseService.queryManyModelBlackExCloseAd(products);
            if (Lists.noEmpty(queryManyModelBlackExUser)){
                queryManyModelBlackExUser.forEach(e -> doUserGray(e.getUserIdStr(),e.getDeviceIdStr(),"同产品一个用户对应多个机型拉黑",e.getProduct()));
            }
        }

        log.info("===> 结束用户拉黑巡查4");
    }
    /**
     * 拉灰火山记录异常的用户
     */
    public void grayHsExUser(){
        log.info("开始火山回调检测....");
        // Check上一小时的数据
        Date today = DateUtil.stringToDate(DateUtil.dateToString(new Date()) + " 00:00:00",DateUtil.COMMON_TIME_FORMAT);
        if (Lists.noEmpty(checkHsHistoryAppList)){
            for (Integer appId : checkHsHistoryAppList){
                ProductEntity productEntity = AppConfig.appIdMap.get(Long.valueOf(appId));
                if (productEntity == null){
                    continue;
                }
                try {
                    checkHsCall(productEntity);
                }catch (Exception e){
                    log.error("solve Hs ex:",e);
                }
                // 检查是否拉灰过多 是否报警
                checkGrayCount(productEntity,today);
            }
        }
        // 查询火山上报withdraw事件的产品
//        checkWithdraw(today);
        log.info("结束火山回调检测....");
    }

    private void checkWithdraw(Date today){
        List<String> targetList = clickHouseService.getHsWithdrawProducts();
        if (Lists.noEmpty(targetList)){
            for (String product : targetList){
                ProductEntity productEntity = AppConfig.productEnMap.get(product);
                if (productEntity == null){
                    continue;
                }
                try {
                    checkHsWithdrawCall(productEntity);
                }catch (Exception e){
                    log.error("solve Hs ex:",e);
                }
                checkGrayCount(productEntity,today);
            }
        }
    }

    private void checkGrayCount(ProductEntity productEntity,Date today){
        // 检查是否拉灰过多 是否报警
        String reason = String.format("数据系统检测火山无调用_%s", productEntity.getId());
        Integer count = userCancelMapper.countAlreadyGrayToday(reason,today.getTime());
        if (count > 1000){
            String msg = productEntity.getProductName()+"-" + reason + ",拉灰:"+count +"个";
            DingTalkPushUtils.sendMessageGrayUser(msg,1000);
        }
    }

    private void checkHsWithdrawCall(ProductEntity productEntity){
        // 查询1h前 2h范围内提现的tt/ksdr用户 检测这些用户是否上报火山回调
        List<Long> queryNoHsCallWithdrawUserList = clickHouseService.queryNoWithdrawCallUser(productEntity.getProduct());
        if (Lists.noEmpty(queryNoHsCallWithdrawUserList)){
            for (Long userId : queryNoHsCallWithdrawUserList){
                checkAndSendGary(userId,productEntity);
            }
        }
    }

    private void checkAndSendGary(Long userId,ProductEntity productEntity){
        // 查询是否已拉灰
        Integer count = userCancelMapper.countIsGrayUser(userId);
        if (count > 0){
            log.info("appId {} userId {} already Gray",productEntity.getId(),userId);
            return;
        }
        sendUserGrayWithdraw(productEntity.getId(), String.valueOf(userId));
        if (checkHsHistoryGrayAppList.contains(productEntity.getId())){
            grayUser(GrayType.USER, Collections.singletonList(userId.toString()),
                    "数据系统检测火山无调用",productEntity.getProduct(),
                    "android",false,false);
        }
    }

    @Resource
    private UserCancelMapper userCancelMapper;

    private void checkHsCall(ProductEntity productEntity){
        log.info("开始检查产品：{} ",productEntity.getProduct());
        List<UserHsNoCallBackEntity> userHsNoCallBackEntities = clickHouseService.queryProductHsExUser(productEntity.getProduct());
        if (Lists.noEmpty(userHsNoCallBackEntities)){
            userHsNoCallBackEntities.forEach(userHsNoCallBackEntity -> {
                checkAndSendGary(Long.valueOf(userHsNoCallBackEntity.getUserId()),productEntity);
            });
        }
        log.info("结束检查产品：{} ",productEntity.getProduct());
    }

    private void doUserGray(UserMacIpCheckEntity e, String remark){
        if(Strings.isEqual(e.getTeamName(), "项目十组") ||
                Strings.isEqual(e.getTeamName(), "项目七组") || Strings.isEqual(e.getTeamName(), "项目五组")){
            if(
                e.getPv() > 200
                && e.getAvgEcpm() > 2000
            ){
                doUserGray(e.getUserIdStr(), e.getDeviceIdStr(), remark, e.getProduct());
            }
        }else{
            doUserGray(e.getUserIdStr(), e.getDeviceIdStr(), remark, e.getProduct());
        }
    }

    public void doUserGray(String userStr,String deviceIdStr, String remark,String product){
        if ("dszg".equals(product)){
            log.info("MiniGame Don't Gray...");
            return;
        }
        List<String> userIdList = JSONArray.parseArray(userStr,String.class)
                .stream()
                .filter(Strings::noEmpty)
                .filter(r -> !r.equals("null"))
                .filter(r -> !r.contains("ad"))
                .collect(Collectors.toList());
        List<String> deviceIdList = JSONArray.parseArray(deviceIdStr,String.class).stream()
                .filter(Strings::noEmpty)
                .filter(r -> !r.equals("wdvid"))
                .collect(Collectors.toList());
        List<Long> userIdLongList = userIdList
                .stream()
                .map(Long::valueOf)
                .collect(Collectors.toList());
        List<String> unionList = new ArrayList<>();
        if (userIdLongList.size() > 0) {
            unionList = bpUserService.queryUserById(userIdLongList)
                    .stream().map(UserEntity::getUnionId).collect(Collectors.toList());
        }
        // 存储拉黑的设备
        if (CollectionUtils.isNotEmpty(deviceIdList)) {
            deviceIdList.forEach(targetId -> userDeviceCheckService.saveGrayDevice(Collections.singletonList(targetId)));
        }
        List<String> finalUnionList = unionList;
        sendGray(Collections.singletonList(new GrayPreBean() {{
            setUnionId(finalUnionList);
            setUserId(userIdList);
            setDeviceId(deviceIdList);
        }}), product, remark);
    }

    private void doIpCheck(String product,String logday){
        log.info("开始计算产品:{}-{}的数据",product,logday);
        List<UserIpEntity> userIpEntities = clickHouseService.queryGrayUserIpList(product,logday);
        List<UserIpBean> userIpBeans = userIpEntities.parallelStream().map(userIpEntity -> {
            UserIpBean bean = new UserIpBean();
            bean.setIp(userIpEntity.getIp());
            bean.setDeviceCount(userIpEntity.getDeviceCount());
            bean.setDeviceArray(JSON.parseArray(userIpEntity.getDeviceArray(),String.class));
            bean.setOs(userIpEntity.getOs());
            return bean;
        }).collect(Collectors.toList());
        log.info("查询基础异常完毕....");
        List<GrayPreBean> grayPreBeanList = userIpBeans.stream()
                // 增加ip过滤
                .filter(userIpBean -> !ownIpGrayList.contains(userIpBean.getIp()))
                .map(userIpBean -> {
            UserNoTfEntity entity = clickHouseService.queryUserNoTfInfo(product,userIpBean.getDeviceArray());
            List<String> tfDeviceList = JSON.parseArray(entity.getDeviceArray(),String.class);

            if ((userIpBean.getDeviceArray().size() - tfDeviceList.size() > 50  && tfDeviceList.size() < 5)
                    || (userIpBean.getDeviceArray().size() > 20 && tfDeviceList.size() < 5)
                    || (userIpBean.getDeviceArray().size() > 10 && tfDeviceList.size() < 2)){
                GrayPreBean grayPreBean = new GrayPreBean();
                List<String> deviceId =  userIpBean.getDeviceArray().stream()
                        .filter(device -> !tfDeviceList.contains(device))
                        .collect(Collectors.toList());
                grayPreBean.setDeviceId(deviceId);
                if (deviceId.size() > 0) {
                    String userStrList = clickHouseService.queryUserInfo(product, deviceId);
                    List<String> userList = JSON.parseArray(userStrList, String.class);
                    grayPreBean.setUserId(userList);
                    List<Long> userIdLongList = grayPreBean.getUserId()
                            .stream().map(Long::valueOf).collect(Collectors.toList());
                    List<String> unionList = bpUserService.queryUserById(userIdLongList)
                            .stream().map(UserEntity::getUnionId).collect(Collectors.toList());
                    grayPreBean.setUnionId(unionList);
                }
                grayPreBean.setIp(userIpBean.getIp());
                grayPreBean.setOs(userIpBean.getOs());
                return grayPreBean;
            }
            return null;
        }).filter(Objects::nonNull).collect(Collectors.toList());


        if (grayPreBeanList.size() > 0) {
            log.info("命中风控规则设备:{}", JSON.toJSONString(grayPreBeanList));
            String remark = "非投放渠道用户超过50人";
            sendGray(grayPreBeanList, product, remark);
        }
        log.info("已完成IP黑产设备检测:{}-{}",product,logday);
    }

    public void sycnInsert(Long appId,String remark,String accessKey){
        CompletableFuture.runAsync(()->{
            try {
                ProductEntity productEntity = AppConfig.appIdMap.get(appId);
                if (productEntity == null){
                    return;
                }
                String finRemark = productEntity.getProductGroup() +"-"+productEntity.getProductName()+"-" + remark;
                saveGrayEntity(productEntity.getProduct(),finRemark,accessKey);
            }catch (Exception e){
                log.error("SAVE ERROR:",e);
            }
        });
    }

    private boolean saveGrayEntity(String product,String remark,String accessKey){
//        String product = AppConfig.appIdMap.getOrDefault(appId,new ProductEntity()).getProduct();
        if (userGrayIpMapper.countExist(product,GrayType.USER.getType(),accessKey) > 0){
            log.info("{}-{} 已存在，不重复拉黑..",product,accessKey);
            return false;
        }
        UserGrayIpEntity entity = new UserGrayIpEntity();
        entity.setProduct(product);
        entity.setRemark(remark);
        entity.setTargetId(accessKey);
        entity.setTargetType(GrayType.USER.getType());

        Date now = new Date();
        entity.setCreateTime(now);
        entity.setUpdateTime(now);
        userGrayIpMapper.insert(entity);
        log.info("成功拉黑: {} {}",product,accessKey);
        return true;
    }

    /**
     * description: 产品+用户维度拉黑用户
     * date: 2021/9/22 15:36
     * params: [userId, product, remark]
     * return: void
    */
    public void sendGray(String userId,String product,String remark){
        log.info("开始拉黑单个用户:{}-{}",product,userId);
        GrayPreBean grayPreBean = new GrayPreBean();
        grayPreBean.setUserId(Collections.singletonList(userId));
        //查询deviceId
        String deviceStrList = clickHouseService.queryDeviceInfo(product, Collections.singletonList(userId));
        List<String> deviceList = JSON.parseArray(deviceStrList, String.class);
        grayPreBean.setDeviceId(deviceList);
        List<Long> userIdLongList = grayPreBean.getUserId()
                .stream().map(Long::valueOf).collect(Collectors.toList());
        List<String> unionList = bpUserService.queryUserById(userIdLongList)
                .stream().map(UserEntity::getUnionId).collect(Collectors.toList());
        grayPreBean.setUnionId(unionList);
        sendGray(Collections.singletonList(grayPreBean), product, remark);
        log.info("拉黑单个用户完成:{}-{}",product,userId);
    }

    private void sendGray(List<GrayPreBean> grayPreBeanList,String product,String remark){
        Date now = new Date();
        grayPreBeanList.forEach(grayPreBean ->{
            if (ownIpGrayList.contains(grayPreBean.getIp())){
                return;
            }
            if (Lists.noEmpty(grayPreBean.getDeviceId())){
                grayPreBean.getDeviceId().stream().filter(Strings::noEmpty).limit(maxGrayUserCount)
                        .forEach(deviceId ->sendGray(GrayType.DEVICE,deviceId,product,grayPreBean,now,remark));
                if (grayPreBean.getDeviceId().size() > maxGrayUserCount){
                    DingTalkPushUtils.sendMessage(product,remark,grayPreBean.getDeviceId().size(),GrayType.DEVICE.getDesc());
                }
            }

            if (Lists.noEmpty(grayPreBean.getUserId())){
                grayPreBean.getUserId().stream().filter(Strings::noEmpty).limit(maxGrayUserCount)
                        .forEach(userId ->sendGray(GrayType.USER,userId,product,grayPreBean,now,remark));
                if (grayPreBean.getUserId().size() > maxGrayUserCount){
                    DingTalkPushUtils.sendMessage(product,remark,grayPreBean.getUserId().size(),GrayType.USER.getDesc());
                }
            }
            if (Lists.noEmpty(grayPreBean.getUnionId())){
                grayPreBean.getUnionId().stream().filter(Strings::noEmpty).limit(maxGrayUserCount)
                        .forEach(union ->sendGray(GrayType.UNION,union,product,grayPreBean,now,remark));
                if (grayPreBean.getUserId().size() > maxGrayUserCount){
                    DingTalkPushUtils.sendMessage(product,remark,grayPreBean.getUnionId().size(),GrayType.UNION.getDesc());
                }
            }
        });

    }

    private boolean sendGray(GrayType grayType , String targetId, String product, GrayPreBean grayPreBean, Date now, String remark){
        return sendGray(grayType,targetId,product,grayPreBean,now,remark,false,true);
    }

    private boolean sendGray(GrayType grayType , String targetId, String product,
                             GrayPreBean grayPreBean, Date now, String remark,
                             Boolean update,Boolean isGlobalGray){
        if ("dszg".equals(product)){
            log.info("Dont Gray dszg G9");
            return true;
        }

        if (GrayType.DEVICE.equals(grayType)){
            userDeviceCheckService.saveGrayDevice(Collections.singletonList(targetId));
        }

        if (update){
            if (sendBlack(grayType,product,targetId,remark,isGlobalGray)) {
                UserGrayIpEntity userGrayIpEntity = userGrayIpMapper.selectOne(product, grayType.getType(), targetId);
                if(userGrayIpEntity == null){
                    userGrayIpMapper.insert(new UserGrayIpEntity() {{
                        setProduct(product);
                        setTargetType(grayType.getType());
                        setTargetId(targetId);
                        setOs(grayPreBean.getOs());
                        setIp(grayPreBean.getIp());
                        setRemark(remark);
                        setCreateTime(now);
                        setUpdateTime(now);
                    }});
                    return true;
                }else {
                    userGrayIpMapper.update(new UserGrayIpEntity() {{
                        setId(userGrayIpEntity.getId());
                        setProduct(product);
                        setTargetType(grayType.getType());
                        setTargetId(targetId);
                        setOs(grayPreBean.getOs());
                        setIp(grayPreBean.getIp());
                        setRemark(remark);
                        setUpdateTime(now);
                    }});
                    return true;
                }
            }
            return false;
        }else {

            if (userGrayIpMapper.countExist(product, grayType.getType(), targetId) == 0) {
                if (sendBlack(grayType,product,targetId,remark,isGlobalGray)) {
                    userGrayIpMapper.insert(new UserGrayIpEntity() {{
                        setProduct(product);
                        setTargetType(grayType.getType());
                        setTargetId(targetId);
                        setOs(grayPreBean.getOs());
                        setIp(grayPreBean.getIp());
                        setRemark(remark);
                        setCreateTime(now);
                        setUpdateTime(now);
                    }});
                    return true;
                }
                return false;
            } else {
//                log.info("TargetId:{} 曾被拉黑，不重复操作...", targetId);
                return false;
            }
        }
    }

    private boolean sendBlack(GrayType grayType,String product,String targetId,String remark,Boolean isGlobalGray){
        BlacklistUserParam blacklistUserParam = new BlacklistUserParam();
        ProductEntity productEntity = AppConfig.productEnMap.getOrDefault(product, new ProductEntity() {{
            setId(0);
            setProduct("None");
        }});
        blacklistUserParam.setAppId(productEntity.getId().toString());
        blacklistUserParam.setProduct(productEntity.getProduct());
        blacklistUserParam.setGlobalGray(isGlobalGray);
        blacklistUserParam.setReason(remark);
        switch (grayType) {
            case DEVICE:
                if (Strings.isEmpty(targetId) || targetId.equals("0000-0000-0000-0000") || targetId.equals("0")) {
                    log.info("当前设备跳过拉黑...");
                    return false;
                }
                blacklistUserParam.setDeviceId(targetId);
                log.info("已拉黑设备:{}", targetId);
                break;
            case USER:
                blacklistUserParam.setUserId(targetId);
                log.info("已拉黑用户:{}", targetId);
                break;
            case UNION:
                blacklistUserParam.setUnionId(targetId);
                log.info("已拉黑联合ID:{}", targetId);
                break;
        }
        userRPC.blacklistUser(blacklistUserParam);
        return true;
    }

    private void doFriendCheck(String product,String logday){
        log.info("开始检测师徒关系:{}-{}",product,logday);
        List<UserGrayFriendShipEntity> userGrayFriendShipEntityList = clickHouseService.queryFriendList(product,logday);
        if (Lists.isEmpty(userGrayFriendShipEntityList)){
            log.info("内拉新人数不足..跳过检测！");
            return;
        }

        List<GrayPreBean> grayPreBeanList = userGrayFriendShipEntityList.stream().map(userGrayFriendShipEntity -> {
            List<String> userIdList = JSON.parseArray(userGrayFriendShipEntity.getDownArray(),String.class);
            if (Lists.noEmpty(userIdList)){
                userIdList.add(userGrayFriendShipEntity.getMasterId());
            }
            if (userIdList.size() > 0){
                List<UserGrayModelEntity> userGrayModelEntities = clickHouseService.queryUserModelCount(product,logday,userIdList);
                if (Lists.noEmpty(userGrayModelEntities)){
                    int sum = userGrayModelEntities.stream().mapToInt(UserGrayModelEntity::getUserCount).sum();
                    boolean flag = false;
                    for (UserGrayModelEntity userGrayModelEntity: userGrayModelEntities){
                        if ((float)userGrayModelEntity.getUserCount() / sum > 0.5){
                            flag = true;
                            break;
                        }
                    }
                    if (flag) {
                        GrayPreBean grayPreBean = new GrayPreBean();
                        grayPreBean.setUserId(userIdList);
                        String deviceStr = clickHouseService.queryDeviceInfo(product,userIdList);
                        List<String> deviceIdList = JSON.parseArray(deviceStr,String.class);
                        grayPreBean.setDeviceId(deviceIdList);
                        List<Long> userIdLongList = grayPreBean.getUserId()
                                .stream().map(Long::valueOf).collect(Collectors.toList());
                        List<String> unionList = bpUserService.queryUserById(userIdLongList)
                                .stream().map(UserEntity::getUnionId).collect(Collectors.toList());
                        grayPreBean.setUnionId(unionList);
                        return grayPreBean;
                    }
                }
            }
            return null;
        }).filter(Objects::nonNull).collect(Collectors.toList());


        if (grayPreBeanList.size() > 0) {
            log.info("师徒关系命中风控规则设备:{}", JSON.toJSONString(grayPreBeanList));
            String remark = "内拉新个数10-同手机型号超50%-用户拉黑";
            sendGray(grayPreBeanList, product, remark);
        }
        log.info("已完成师徒关系黑产设备检测:{}-{}",product,logday);
    }

    private void doAlreadyGrayFriendCheck(String product,String logday){
        log.info("开始检测c的师徒关系:{}-{}",product,logday);
        List<String> grayUserFriendShipUserIdList = clickHouseService.queryAlreadyGrayUserFriendList(product,logday);
        if (Lists.isEmpty(grayUserFriendShipUserIdList)){
            log.info("已封禁用户无师徒关系..跳过检测！");
            return;
        }

        List<GrayPreBean> grayPreBeanList = new ArrayList<>();
        if (grayUserFriendShipUserIdList.size() > 0){
            List<String> userIdList = grayUserFriendShipUserIdList;
            GrayPreBean grayPreBean = new GrayPreBean();
            grayPreBean.setUserId(userIdList);
            String deviceStr = clickHouseService.queryDeviceInfo(product,userIdList);
            List<String> deviceIdList = JSON.parseArray(deviceStr,String.class);
            grayPreBean.setDeviceId(deviceIdList);
            List<Long> userIdLongList = grayPreBean.getUserId()
                    .stream().map(Long::valueOf).collect(Collectors.toList());
            List<String> unionList = bpUserService.queryUserById(userIdLongList)
                    .stream().map(UserEntity::getUnionId).collect(Collectors.toList());
            grayPreBean.setUnionId(unionList);
            grayPreBeanList.add(grayPreBean);
        }
        if (grayPreBeanList.size() > 0) {
            log.info("已封禁用户师徒关系命中风控规则设备:{}", JSON.toJSONString(grayPreBeanList));
            String remark = "已封禁用户不是ocpc师傅-已封禁用户徒弟-用户拉黑";
            sendGray(grayPreBeanList, product, remark);
        }
        log.info("已完成师徒关系黑产设备检测:{}-{}",product,logday);
    }

    private void doChannelCheck(String product,String logday){
        log.info("开始检测渠道数据:{}-{}",product,logday);
        List<UserGrayChannelEntity> userIpEntities = clickHouseService.queryGrayUserChannel(product,logday);
        List<UserIpBean> userIpBeans = userIpEntities.parallelStream().map(userIpEntity -> {
            UserIpBean bean = new UserIpBean();
            bean.setDeviceArray(JSON.parseArray(userIpEntity.getDeviceArray(),String.class));
            return bean;
        }).collect(Collectors.toList());

        List<GrayPreBean> grayPreBeanList = userIpBeans.stream().map(userIpBean -> {
            UserNoTfEntity entity = clickHouseService.queryUserNoTfInfo(product,userIpBean.getDeviceArray());
            List<String> tfDeviceList = JSON.parseArray(entity.getDeviceArray(),String.class);

            if ((float)tfDeviceList.size() /userIpBean.getDeviceArray().size() < 0.1f){
                UserNoTfEntity entityClick = clickHouseService.queryTfClickDevice(product,userIpBean.getDeviceArray());
                if ((float)entityClick.getCount()/userIpBean.getDeviceArray().size() < 0.1f) {
                    GrayPreBean grayPreBean = new GrayPreBean();
                    List<String> deviceId = userIpBean.getDeviceArray().stream()
                            .filter(device -> !tfDeviceList.contains(device))
                            .collect(Collectors.toList());
                    grayPreBean.setDeviceId(deviceId);
                    if (deviceId.size() > 0) {
                        String userStrList = clickHouseService.queryUserInfo(product, deviceId);
                        List<String> userList = JSON.parseArray(userStrList, String.class);
                        grayPreBean.setUserId(userList);
                        if (grayPreBean.getUserId().size() > 0) {
                            List<Long> userIdLongList = grayPreBean.getUserId()
                                    .stream().filter(str-> Strings.noEmpty(str) && !str.equals("null")).map(Long::valueOf).collect(Collectors.toList());
                            List<String> unionList = bpUserService.queryUserById(userIdLongList)
                                    .stream().map(UserEntity::getUnionId).collect(Collectors.toList());
                            grayPreBean.setUnionId(unionList);
                        }
                    }
                    return grayPreBean;
                }
            }
            return null;
        }).filter(Objects::nonNull).collect(Collectors.toList());


        if (grayPreBeanList.size() > 0) {
            log.info("命中风控规则设备:{}", JSON.toJSONString(grayPreBeanList));
            String remark = "渠道检测异常";
            sendGray(grayPreBeanList, product, remark);
        }

        log.info("已完成渠道数据检测:{}-{}",product,logday);
    }

    private void solveDay(String logDay,Map<Integer,ProductEntity> productMap){
        List<GlobalConfigEntity> globalConfigEntities = bpConfigMapper.queryGroup5LimitApp();

        if (Lists.noEmpty(globalConfigEntities)){
            log.info(">>> 处理Group5,共{}个APP需要处理..",globalConfigEntities.size());
            globalConfigEntities.forEach(globalConfigEntity -> {
                ProductEntity productEntity = productMap.get(globalConfigEntity.getProduct());
                if (productEntity == null){
                    return;
                }
                Integer limit = Integer.valueOf(globalConfigEntity.getValue());
                List<AbnormalUserEntity> abnormalUserEntities = clickHouseService.queryProductGrayUser(productEntity.getProduct(),logDay,limit);
                saveAndGrayUser(logDay,abnormalUserEntities,productEntity,limit);
            });
            log.info(">>> 处理Group5完成");
        }
    }

    private void saveAndGrayUser(String logDay,List<AbnormalUserEntity> abnormalUserEntityList,ProductEntity productEntity,Integer limit){
        if (Lists.isEmpty(abnormalUserEntityList)){
            log.info("产品{}无异常用户..跳过",productEntity.getProduct());
            return;
        }

        if (enableGrayUser) {
            abnormalUserEntityList.forEach(abnormalUserEntity -> {
                try {
                    userRPC.signOutUser(Long.valueOf(productEntity.getId()), abnormalUserEntity.getUserId());
                } catch (Exception e) {
                    log.error("拉灰用户异常:", e);
                }
                log.info("曝光异常,已拉黑用户:{},来自产品:{}", abnormalUserEntity.getUserId(), productEntity.getProduct());
            });
        }

        Date now = new Date();
        List<GrayUserEntity> grayUserEntities = abnormalUserEntityList.stream()
                .map(abnormalUserEntity -> {
                    GrayUserEntity grayUserEntity = new GrayUserEntity();

                    grayUserEntity.setProduct(productEntity.getProduct());
                    grayUserEntity.setProductName(productEntity.getProductName());
                    grayUserEntity.setProductGroup(productEntity.getProductGroup());
                    grayUserEntity.setConfigTimes(limit);
                    grayUserEntity.setExposureTimes(abnormalUserEntity.getExposureTimes());
                    grayUserEntity.setUserId(Long.valueOf(abnormalUserEntity.getUserId()));
                    grayUserEntity.setOs(abnormalUserEntity.getOs());
                    grayUserEntity.setLogday(DateUtil.stringToDate(logDay));
                    grayUserEntity.setCreateTime(now);
                    grayUserEntity.setUpdateTime(now);
                    return grayUserEntity;
                }).collect(Collectors.toList());
        int count = grayUserMapper.batchInsertGrayUser(grayUserEntities);
        log.info("成功插入{}条异常用户..",count);
    }

    public void checkGrayRate(){
        List<ProductGrayRateEntity> grayRateEntities = clickHouseService.queryGrayRate();
        List<ProductGrayRateEntity> filterResult = grayRateEntities.stream()
                .filter(productGrayRateEntity -> productGrayRateEntity.getGrayRate() > grayUserRate)
                .collect(Collectors.toList());
        if (filterResult.size() > 0){
            StringBuilder content = new StringBuilder();
            for (ProductGrayRateEntity entity : filterResult){
                content.append("> 产品：").append(entity.getProduct())
                        .append(",系统：").append(entity.getOs())
                        .append(",今日累计拉黑：").append(entity.getGray())
                        .append(",DNU：").append(entity.getDau())
                        .append(",Rate：").append(entity.getGrayRate())
                        .append("% \n\n");

            }
            DingTalkPushUtils.sendMessage(content.toString(),grayUserRate);
        }

        List<ProductGrayRateEntity> grayUserList = clickHouseService.queryAlreadyGrayCount(limitCount);
        if (grayUserList.size() > 0){
            StringBuilder content = new StringBuilder();
            for (ProductGrayRateEntity entity : grayUserList){
                content.append("> 产品：").append(entity.getProduct())
                        .append(",原因：").append(entity.getRemark())
                        .append(",今日累计拉黑用户：").append(entity.getGrayUser())
                        .append(" \n\n");
            }
            DingTalkPushUtils.sendMessage(content.toString(),limitCount);
        }
    }

    public void autoGrayUser(String targetIds, String targetType, Integer appId, String globalGray){
        Boolean isGlobalGray = globalGray.equals("1");
        List<String> targetList = Arrays.asList(targetIds.split(","));
        if (targetList.size() == 0){
            return;
        }
        if (targetList.size() > 1000){
            throw new RuntimeException("单次提交不能超过1000条");
        }
        CompletableFuture.runAsync(() ->{
            if (GrayType.DEVICE.getType().toString().equals(targetType)) {
                if (Strings.noEmpty(targetIds)) {
                    List<String> grayList = Arrays.stream(targetIds.split(","))
                            .filter(rx -> !rx.startsWith("0000"))
                            .collect(Collectors.toList());
                    if (grayList.size() > 0) {
                        grayUser(GrayType.DEVICE, grayList,appId,isGlobalGray);
                    }else {
                        log.warn("本批次设备未查询到对应用户,放弃拉黑...");
                    }
                } else {
                    log.warn("本批次设备未查询到对应用户,放弃拉黑...");
                }
            } else if (GrayType.USER.getType().toString().equals(targetType)) {
                grayUser(GrayType.USER, targetList,appId,isGlobalGray);
            }
        });
    }

    public void autoUnGrayUser(String targetIds,String targetType){
        List<String> targetList = Arrays.asList(targetIds.split(","));
        if (targetList.size() == 0){
            return;
        }
        if (targetList.size() > 1000){
            throw new RuntimeException("单次提交不能超过1000条");
        }
        CompletableFuture.runAsync(() ->{
            long start = System.currentTimeMillis();
            if (GrayType.DEVICE.getType().toString().equals(targetType)) {
                unGrayUser(GrayType.DEVICE, targetList);
            } else if (GrayType.USER.getType().toString().equals(targetType)) {
                unGrayUser(GrayType.USER, targetList);
            }else {
                unGrayUser(GrayType.UNION, targetList);
            }
            System.out.println("当前批花费时间: "+(System.currentTimeMillis()-start));
        });
    }

    @PostConstruct
    public void unBlackUserBatch(){
        ThreadPoolExecutor threadPoolExecutor = new ThreadPoolExecutor(Runtime.getRuntime().availableProcessors(), Runtime.getRuntime().availableProcessors() * 2, 100, TimeUnit.SECONDS,
                new ArrayBlockingQueue<>(500), new ThreadFactory("UserUnGary-"), new ThreadPoolExecutor.CallerRunsPolicy());

        List<UserGrayIpEntity> userIsList = userGrayIpMapper.selctGaryUser();
        final int BATCH_SIZE = 1000;
        for (int i = 0; i < userIsList.size(); i += BATCH_SIZE) {
            int end = Math.min(i + BATCH_SIZE, userIsList.size());
            List<UserGrayIpEntity> batch = userIsList.subList(i, end);
            Map<Integer, List<UserGrayIpEntity>> collect = batch.stream().collect(Collectors.groupingBy(UserGrayIpEntity::getTargetType));
            collect.forEach((k, v) -> {

                String batchIds = v.stream().map(UserGrayIpEntity::getTargetId).collect(Collectors.joining(","));
                CompletableFuture.runAsync(() -> {
                    autoUnGrayUser(batchIds, String.valueOf(k));
                }, threadPoolExecutor);

            });
        }
    }


    public void grayUserAsync(GrayType grayType,Map<String,String> targetMap,String product,String os,String trace,Boolean isGlobalGray){
        CompletableFuture.runAsync(() ->{
            Integer count = 0;
            for (Map.Entry<String, String> entry : targetMap.entrySet()) {
                String target = entry.getKey();
                String reason = entry.getValue();
                sendGray(grayType, target, product, new GrayPreBean() {{ setOs(os); }}, new Date(), reason,false,isGlobalGray);
                count++;
            }
            log.info("{} ===> Risk End Gray {} Target {}",trace,count,JSON.toJSONString(targetMap));
        }, executor);
    }

    public void grayUserAsync(GrayType grayType,List<String> targetList,String remark,String product,String os,String trace){
        CompletableFuture.runAsync(() ->{
            Integer count = grayUser(grayType,targetList,remark,product,os,false,true);
            log.info("{} ===> Risk End Gray {} Target",trace,count);
        }, executor);
    }

    public Integer grayUser(GrayType grayType,List<String> targetList,String remark,
                            String product,String os,Boolean update,Boolean isGlobalGray){
        Integer count = 0;
        for (String s : targetList) {
            if (sendGray(grayType, s, product, new GrayPreBean(){{ setOs(os); }}, new Date(), remark,update,isGlobalGray)) {
                count++;
            }
        }
        if (GrayType.USER.equals(grayType)){
            List<Long> userIdLongList = targetList
                    .stream().map(Long::valueOf).collect(Collectors.toList());
            List<String> unionList = bpUserService.queryUserById(userIdLongList)
                    .stream().map(UserEntity::getUnionId).collect(Collectors.toList());
            unionList.forEach(targetId -> sendGray(GrayType.UNION,targetId,product,new GrayPreBean(){{ setOs(os); }},new Date(),remark,update,isGlobalGray));
        }

        return count;
    }

    public void grayUser(GrayType grayType,List<String> targetList,Integer appId,Boolean isGlobalGray){
        ProductEntity productEntity = new ProductEntity();
        if (appId != null){
            productEntity = AppConfig.appIdMap.get(Long.valueOf(appId));
        }
        grayUser(grayType,targetList,"手动拉黑",productEntity.getProduct(),null,true,isGlobalGray);
    }

    private void unGrayUser(GrayType grayType,List<String> targetList){
        unGrayUserPost(grayType,targetList);
        if (GrayType.USER.equals(grayType)) {
            List<Long> userIdLongList = targetList
                    .stream().map(Long::valueOf).collect(Collectors.toList());
            List<String> unionList = bpUserService.queryUserById(userIdLongList)
                    .stream().map(UserEntity::getUnionId).collect(Collectors.toList());
            unGrayUserPost(GrayType.UNION,unionList);
        }
    }

    private void unGrayUserPost(GrayType grayType,List<String> targetList){
        GrayUserPostRequest request = new GrayUserPostRequest();
        switch (grayType) {
            case DEVICE:
                request.setBlackType("2");
                break;
            case USER:
                request.setBlackType("1");
                break;
            case UNION:
                request.setBlackType("3");
                break;
        }
        request.setBlackIds(targetList);

        String url = "http://bp-api.shinet-inc.com/bp-data-station/shield/removeGlobalBlack";
        HttpClients.POST(url,JSON.toJSONString(request));
        targetList.forEach(targetId ->{
            Integer count = userGrayIpMapper.delete(null,grayType.getType(),targetId);
            UserGrayIpEntity userGrayIpEntity = new UserGrayIpEntity();
            userGrayIpEntity.setTargetId(targetId);
            userGrayIpEntity.setTargetType(grayType.getType());
            userGrayIpEntity.setCreateTime(new Date());
            userGrayIpEntity.setUpdateTime(new Date());
            userGrayIpMapper.insertIntoUserGrayIpDelRecord(userGrayIpEntity);
            if (count == 1) {
                log.info("成功将设备{}移除黑名单...", targetId);
            }else {
                log.info("该设备:{}不在黑名单中...",targetId);
            }
        });
    }

    /**
     * 拉灰用户
     * @param appId 产品appId
     * @param userId 用户UserId
     */
    public void sendUserGrayWithdraw(Integer appId,String userId){
        Boolean result = userRPC.grayUser(Long.valueOf(appId),Long.valueOf(userId),String.format("数据系统检测火山无调用_%s", appId));
        if (result){
            log.info(">>>> 拉灰[{}-{}] 成功...",appId,userId);
        }else {
            log.error(">>>> 拉灰[{}-{}] 异常...",appId,userId);
        }
    }

    public void sendUserGrayWithdrawReason(Integer appId,String userId,String reason){
        Boolean result = userRPC.grayUser(Long.valueOf(appId),Long.valueOf(userId),reason);
        if (result){
            log.info(">>>> 拉灰[{}-{}] 成功... Reason {} ",appId,userId,reason);
        }else {
            log.error(">>>> 拉灰[{}-{}] 异常...",appId,userId);
        }
    }

    public void skipAdByPlatform(List<SkipAdEntity> skipAdEntityList,int second){
        if (Lists.noEmpty(skipAdEntityList)){
            for (SkipAdEntity skipAdEntity : skipAdEntityList){
                ProductEntity productEntity = AppConfig.productEnMap.get(skipAdEntity.getProduct());
                if (productEntity == null){
                    continue;
                }
                List<Integer> skipPlat = new ArrayList<>();
                if (skipAdEntity.getCsjSkip() !=null && skipAdEntity.getCsjSkip() == 1){
                    skipPlat.add(1);
                }
                if (skipAdEntity.getGdtSkip() !=null && skipAdEntity.getGdtSkip() == 1){
                    skipPlat.add(2);
                }
                if (skipAdEntity.getKsSkip() !=null && skipAdEntity.getKsSkip() == 1){
                    skipPlat.add(3);
                }
                if (skipAdEntity.getAqySkip() !=null && skipAdEntity.getAqySkip() == 1){
                    skipPlat.add(0);
                }
                if (skipAdEntity.getBdSkip() !=null && skipAdEntity.getBdSkip() == 1){
                    skipPlat.add(4);
                }
                if (skipAdEntity.getVivoSkip() !=null && skipAdEntity.getVivoSkip() == 1){
                    skipPlat.add(5);
                }
                if (skipAdEntity.getOppoSkip() !=null && skipAdEntity.getOppoSkip() == 1){
                    skipPlat.add(6);
                }
                if (skipAdEntity.getHwSkip() !=null && skipAdEntity.getHwSkip() == 1){
                    skipPlat.add(8);
                }
                if (skipAdEntity.getAliSkip() !=null && skipAdEntity.getAliSkip() == 1){
                    skipPlat.add(9);
                }
                adCloseService.setSkipPlatform(productEntity.getId(),skipAdEntity.getUserid(),skipPlat ,second);
            }
        }
    }

    /**
     * ios自然拉灰
     */
    public void UserGrayIosNatural() {
        List<UserMacIpCheckEntity> iosNaturalUserInfoList = clickHouseService.queryIosNaturalUserInfo();

        List<UserMacIpCheckEntity> needGrayList = iosNaturalUserInfoList;
        if (CollectionUtils.isNotEmpty(userGrayIosNaturalProductList)) {
            needGrayList = iosNaturalUserInfoList.stream().filter(e -> userGrayIosNaturalProductList.contains(e.getProduct())).collect(Collectors.toList());
        }

        if (Lists.noEmpty(needGrayList)) {
            needGrayList.forEach(e -> doUserGray(e.getUserIdStr(), e.getDeviceIdStr(), "百度iOS收入占比异常", e.getProduct()));
        }
    }
}
