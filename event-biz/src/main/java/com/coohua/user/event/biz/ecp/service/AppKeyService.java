package com.coohua.user.event.biz.ecp.service;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.coohua.user.event.biz.ecp.entity.AdVideoSource;
import com.coohua.user.event.biz.ecp.entity.AppKey;
import com.coohua.user.event.biz.ecp.mapper.AdVideoSourceMapper;
import com.coohua.user.event.biz.ecp.mapper.AppKeyMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.PostConstruct;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
* <AUTHOR>
* @since 2020-11-05
*/
@Slf4j
@Service
public class AppKeyService extends ServiceImpl<AppKeyMapper, AppKey> {

    public static Map<Long,AppKey> appKeyMap;

    @Scheduled(cron = "0 0/10 * * * ?")
    @PostConstruct
    public void refreshMap(){
        appKeyMap = lambdaQuery().list().stream()
                .filter(appKey -> appKey.getAdId() != null)
                .collect(Collectors.toMap(AppKey::getAdId,a->a,(a1,a2) -> a1));
    }
}
