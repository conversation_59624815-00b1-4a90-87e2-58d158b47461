package com.coohua.user.event.biz.dc.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import java.io.Serializable;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR>
 * @since 2023-06-15
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value="CdpAdData对象", description="")
public class CdpAdData implements Serializable {

    private static final long serialVersionUID = 1L;

    private String logday;

    private String appId;

    private String applicationName;

    private String applicationPackage;

    private Integer activeUserLpu;

    private Integer activeUserMotivationNum;

    private Integer activeUserNum;

    private Integer expend;

    private Integer keyActionCompletedNum;

    private Integer motivationalVideoEcpm;

    private Integer motivationalVodepIncome;

    private Integer newUserLpu;

    private Integer newUserMotivationalVideoWatchUserNum;

    private Integer newUserNum;

    private Integer totalActiveUserMotivationalVideoTimes;

    private Integer totalEcpm;

    private Integer totalIncome;

    private Integer totalNewUserMotivationalVideoWatchTimes;

    private Integer totalWithdrawAmount;


}
