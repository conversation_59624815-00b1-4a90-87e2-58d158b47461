package com.coohua.user.event.biz.service.bean;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.Date;

@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value = "OcpcEvent对象", description = "")
public class OcpcEvent implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "主键")
    private Long id;

    @ApiModelProperty(value = "应用ID")
    private Integer appId;

    @ApiModelProperty(value = "用户ID")
    private String userId;

    @ApiModelProperty(value = "原始deviceId")
    private String sourceDeviceId;

    @ApiModelProperty(value = "原始oaId")
    private String sourceOaid;

    private String product;

    private String os;

    private String ocpcDeviceId;

    private String oaid;

    @ApiModelProperty(value = "oaid加md5")
    private String oaid2;

    @ApiModelProperty(value = "idfa加md5")
    private String idfa2;

    @ApiModelProperty(value = "事件类型")
    private Integer eventType;

    @ApiModelProperty(value = "类型名称")
    private String eventTypeName;

    private Long toutiaoClickId;

    @ApiModelProperty(value = "clickId")
    private Long clickId;

    @ApiModelProperty(value = "accountId")
    private String accountId;

    @ApiModelProperty(value = "account名称")
    private String accountName;

    @ApiModelProperty(value = "dsp平台")
    private String dsp;

    private String reqUrl;

    private String reqRsp;

    @ApiModelProperty(value = "补偿回调时间")
    private Date lastRecallTime;

    @ApiModelProperty(value = "更新时间")
    private Date updateTime;

    @ApiModelProperty(value = "创建时间")
    private Date createTime;

    private String macId;

    private String callbackUrl;

    @ApiModelProperty(value = "click_time")
    private Date clickTime;

    private String androidId;

    /**
     * 目前用做记录关键行为回传触发时的值
     */
    private String remark;

    /**
     * 快手undirectshort回传(多触点转化数据回传)标记, 0待回传 1已回传
     */
    private Integer kuaishouUdsTag;

    /**
     * 头条额外回传（关键行为回传到注册和下单）标记, 0待回传 1已回传
     */
    private Integer toutiaoExtraTag;

    @ApiModelProperty(value = "创意ID")
    private String creativeId;

    @ApiModelProperty(value = "计划ID")
    private String planId;

    @ApiModelProperty(value = "广告组ID")
    private String groupId;

    private String ip;
    private String ua;
    private String model;
    @ApiModelProperty(value = "openId")
    private String openId;

    @ApiModelProperty(value = "wappid")
    private String wAppId;
    private String caid;
    private String gyType;
    private String clickIdT;
}