package com.coohua.user.event.biz.service;

import com.alibaba.fastjson.JSON;
import com.aliyun.openservices.ons.api.Action;
import com.aliyun.openservices.ons.api.ConsumeContext;
import com.aliyun.openservices.ons.api.Message;
import com.aliyun.openservices.ons.api.MessageListener;
import com.coohua.user.event.biz.click.entity.VideoAdReportEntitiy;
import com.coohua.user.event.biz.click.service.CkVideoPonitRepository;
import com.coohua.user.event.biz.config.MqConfig;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.stereotype.Component;

import java.util.Arrays;

/**
 * <AUTHOR>
 * @since 2021/2/18
 */
@Slf4j
@Component
@ConditionalOnProperty(name = "spring.application.name",havingValue = "user-event-job")
public class AdPointConsumer implements MessageListener {
    @Autowired
    private MqConfig mqConfig;
    @Autowired
    private CkVideoPonitRepository ckVideoPonitRepository;
    @Value("${ck_upload_count}")
    private Integer solveCount;

    @Override
    public Action consume(Message message, ConsumeContext context) {
        try {
//            if (message.getTag().equals(mqConfig.getTag())){
              // 不处理了 三方回调就不记录了
//            psToCk(new String(message.getBody(),"utf-8"));
//            }
            return Action.CommitMessage;
        } catch (Exception e) {
            //消费失败
            return Action.ReconsumeLater;
        }
    }

    private void psToCk(String msg){
//        try {
//            VideoAdReportEntitiy videoAdReportEntitiy = JSON.parseObject(msg,VideoAdReportEntitiy.class);
//            ckVideoPonitRepository.addToBatch(videoAdReportEntitiy);
//            if (ckVideoPonitRepository.batchCounter >=  solveCount){
//                ckVideoPonitRepository.executeBatch();
//            }
//        }catch (Exception e){
//            log.error("{},SOLVE Message ERROR:",msg,e);
//        }
    }

}
