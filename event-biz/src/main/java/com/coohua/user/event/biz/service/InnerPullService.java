package com.coohua.user.event.biz.service;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.coohua.user.event.biz.click.entity.InnerMasterBean;
import com.coohua.user.event.biz.click.entity.InnerPullBean;
import com.coohua.user.event.biz.click.service.ClickHouseService;
import com.coohua.user.event.biz.enums.ActiveChannel;
import com.coohua.user.event.biz.util.DateUtil;
import com.coohua.user.event.biz.util.HBaseParameterEnum;
import com.coohua.user.event.biz.util.HBaseUtils;
import com.coohua.user.event.biz.util.Strings;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.tuple.Pair;
import org.apache.hadoop.hbase.TableName;
import org.apache.hadoop.hbase.client.*;
import org.apache.hadoop.hbase.util.Bytes;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2022/1/12
 */
@Slf4j
@Service
public class InnerPullService {

    @Resource(name = "linDormConnection")
    private Connection ocpcHadoopConnection;

    private static final String TABLE_NAME = "UserRelation";
    private static final String TABLE_RELATION = "UserRelationShip";

    @Autowired
    private ClickHouseService clickHouseService;

    private static final String UA_KEY = "%s:%s";

    public boolean queryIsInnerUser(Integer appId,Long userId){
        try {
            String key = String.format("%s:%s", appId, userId);

            Pair<Boolean, byte[]> result = HBaseUtils.searchDataFromHadoopWithoutHash(ocpcHadoopConnection,TABLE_NAME,key);

            if (result.getRight() == null) {
                return false;
            }

            return "true".equals(String.valueOf(result.getLeft()));
        }catch (Exception e){
            log.error("Querry HBase Er:",e);
        }

        return false;
    }

    public List<String> queryNInnerPull(Integer app,List<String> userIdList,String trace){
        List<String> rowKeyList = userIdList.stream()
                .map(r ->  String.format(UA_KEY,app, r))
                .collect(Collectors.toList());
        Map<String,String> resultMap  = HBaseUtils.searchDataFromHadoopWithoutHash(ocpcHadoopConnection,TABLE_NAME,rowKeyList);
        List<String> notExist = new ArrayList<>();
        for (String userId: userIdList){
            String key = HBaseUtils.hashRowKeyByLastCharacter(String.format(UA_KEY,app, userId));
            String result = resultMap.get(key);
            if (!Strings.noEmpty(result)) {
                notExist.add(key.split(":")[1]);
            }
        }
        if (notExist.size() > 0) {
            log.warn("{} ==> {} Not Find in UserRelation.. Skip", trace, JSON.toJSONString(notExist));
        }
        return notExist;
    }

    public List<String> queryByMasterId(Integer app,String masterId){
        try (Table tableBool = ocpcHadoopConnection.getTable(TableName.valueOf(TABLE_RELATION))) {
            String key = String.format(UA_KEY, app, masterId);
            Get get = new Get(Bytes.toBytes(key));
            get.addColumn(HBaseParameterEnum.FAMILY.getBinaryContent(), HBaseParameterEnum.DEFAULT_COLUMN.getBinaryContent());
            Result result = tableBool.get(get);
            byte[] byteRes = result.getValue(HBaseParameterEnum.FAMILY.getBinaryContent(), HBaseParameterEnum.DEFAULT_COLUMN.getBinaryContent());
            if (byteRes != null){
                return JSON.parseArray(Bytes.toString(byteRes),String.class);

            }
        } catch (Exception e) {
            log.error("Hadoop师徒关系查询失败..", e);
        }
        return new ArrayList<>();
    }


    // 初始化数据R
    public void refreshRelationShip(){
        Date now = new Date();
        for (int i = 4; i<=90; i++) {
            Date day = DateUtil.dateIncreaseByDay(now,-i);
            String logday = DateUtil.dateToString(day);
            log.info("Current Logday:{}",logday);
            List<InnerMasterBean> relationships = clickHouseService.queryInnerMasterInfo(logday);
            try (Table tableBool = ocpcHadoopConnection.getTable(TableName.valueOf(TABLE_RELATION))) {
                List<Put> rts = new ArrayList<>();
                for (InnerMasterBean innerMasterBean : relationships) {

                    List<String> userIdList = JSON.parseArray(innerMasterBean.getUserStr(), String.class);
                    String key = String.format("%s:%s", innerMasterBean.getAppId(), innerMasterBean.getMasterId());
                    Get get = new Get(Bytes.toBytes(key));
                    get.addColumn(HBaseParameterEnum.FAMILY.getBinaryContent(), HBaseParameterEnum.DEFAULT_COLUMN.getBinaryContent());
                    Result result = tableBool.get(get);
                    byte[] byteRes = result.getValue(HBaseParameterEnum.FAMILY.getBinaryContent(), HBaseParameterEnum.DEFAULT_COLUMN.getBinaryContent());
                    Set<String> prentIdSet = new HashSet<>(userIdList);
                    if (byteRes != null) {
                        List<String> rs = JSON.parseArray(Bytes.toString(byteRes), String.class);
                        if (rs != null) {
                            prentIdSet.addAll(rs);
                        }
                    }
                    Put put = new Put(Bytes.toBytes(key));
                    put.addColumn(HBaseParameterEnum.FAMILY.getBinaryContent(), HBaseParameterEnum.DEFAULT_COLUMN.getBinaryContent(),
                            JSON.toJSONBytes(prentIdSet));
                    rts.add(put);
                }
                tableBool.put(rts);
            } catch (Exception e) {
                log.error("Hadoop表存储失败 ", e);
            }
        }
    }

    public void refresh(){
        Date now = new Date();
        for (int i = 0; i<=90; i++) {
            Date day = DateUtil.dateIncreaseByDay(now,-i);
            String logday = DateUtil.dateToString(day);
            log.info("Current Logday:{}",logday);
            List<InnerPullBean> relationships = clickHouseService.queryRelationUsers(logday);
            relationships.forEach(innerPullBean -> {
                try (Table tableBool = ocpcHadoopConnection.getTable(TableName.valueOf(TABLE_NAME))) {
                    List<String> userIdList = JSON.parseArray(innerPullBean.getUserStr(),String.class);
                    List<Put> putList = userIdList.stream().map(r ->{
                        String key = String.format("%s:%s",innerPullBean.getAppId(),r);
                        Put put = new Put(Bytes.toBytes(key));
                        put.addColumn(HBaseParameterEnum.FAMILY.getBinaryContent(), HBaseParameterEnum.DEFAULT_COLUMN.getBinaryContent(), Bytes.toBytes("true"));
                        return put;
                    }).collect(Collectors.toList());
                    tableBool.put(putList);
                }catch (Exception e) {
                    log.error("Hadoop表存储失败 ", e);
                }
            });

        }
    }
}
