package com.coohua.user.event.biz.dc.dto;

import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2022/1/26
 */
@Data
public class RealtimeRuleResponse {
    private Integer id;
    private String product;
    private Integer state;
    private Integer ocpcType;
    private String channels;
    private String models;
    private String ips;
    private String createTime;
    private String updateTime;
    private Integer actionType;
    private String ruleName;
    private Integer appId;
    private String productName;
    private Integer level;
    private String brands;
    private String sources;
    private Integer newUserType;


    private Integer type;
    private Integer minEcpm;
    private Integer maxEcpm;
    private Integer videoTimes;
    private Integer videoLimit;
    private Integer withdrawLimit;
    private Integer rewardLimit;
    private List<Integer> platformLimit;
    private List<String> platformSkipList;
}
