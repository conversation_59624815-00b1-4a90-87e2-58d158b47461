package com.coohua.user.event.biz.kafka;

import lombok.extern.slf4j.Slf4j;
import org.apache.kafka.clients.producer.KafkaProducer;
import org.apache.kafka.clients.producer.ProducerConfig;
import org.apache.kafka.clients.producer.ProducerRecord;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Properties;
import java.util.concurrent.CompletableFuture;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicLong;

@Slf4j
@Component
public class BizKafkaSender  implements InitializingBean {
    private KafkaProducer<String, String> producer;
    private AtomicLong counter = new AtomicLong(0);


    @Resource(name = "syncWithdrawTaskPool")
    private ThreadPoolTaskExecutor syncWithdrawTaskPool;

    @Override
    public void afterPropertiesSet() throws Exception {
        Properties props = new Properties();
        //设置接入点，请通过控制台获取对应Topic的接入点。
        props.put(ProducerConfig.BOOTSTRAP_SERVERS_CONFIG, "alikafka-pre-cn-zvp2j2vhf007-1-vpc.alikafka.aliyuncs.com:9092," +
                "alikafka-pre-cn-zvp2j2vhf007-2-vpc.alikafka.aliyuncs.com:9092," +
                "alikafka-pre-cn-zvp2j2vhf007-3-vpc.alikafka.aliyuncs.com:9092");
        //消息队列Kafka版消息的序列化方式。
        props.put(ProducerConfig.KEY_SERIALIZER_CLASS_CONFIG, "org.apache.kafka.common.serialization.StringSerializer");
        props.put(ProducerConfig.VALUE_SERIALIZER_CLASS_CONFIG, "org.apache.kafka.common.serialization.StringSerializer");
        //请求的最长等待时间。
        props.put(ProducerConfig.MAX_BLOCK_MS_CONFIG, 30 * 1000);
        props.put(ProducerConfig.RETRIES_CONFIG, 5);
        props.put(ProducerConfig.ACKS_CONFIG, "1");
        //构造Producer对象，注意，该对象是线程安全的。
        //一般来说，一个进程内一个Producer对象即可。如果想提高性能，可构造多个对象，但最好不要超过5个。
        this.producer = new KafkaProducer<>(props);
        CompletableFuture.runAsync(() ->{
            while (true) {
                try {
                    TimeUnit.SECONDS.sleep(1);
                    long sec = counter.getAndSet(0);
                    if (sec > 0) {
                        log.info("BizKafkaSender MSG.SEC={}", sec);
                    }
                } catch (InterruptedException e) {
                    log.error("", e);
                }
            }
        });
    }

    public void sendToWithdraw(String msg){
        try {
            syncWithdrawTaskPool.submit(()->{
                ProducerRecord<String, String> kafkaMessage = new ProducerRecord<>("user_event_withdraw_sync", msg);
                producer.send(kafkaMessage, (recordMetadata, e) -> counter.incrementAndGet());
            });
        }catch (Exception e){
            log.error("Error:",e);
        }

    }

    public void sendToWithdrawToday(String msg) {
        try {
            syncWithdrawTaskPool.submit(()->{
                ProducerRecord<String, String> kafkaMessage = new ProducerRecord<>("user_event_today_withdraw_sync", msg);
                producer.send(kafkaMessage, (recordMetadata, e) -> counter.incrementAndGet());
            });
        }catch (Exception e){
            log.error("Error:",e);
        }
    }
}
