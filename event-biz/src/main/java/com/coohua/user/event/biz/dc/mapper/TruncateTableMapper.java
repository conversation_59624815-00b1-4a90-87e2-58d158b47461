package com.coohua.user.event.biz.dc.mapper;

import org.apache.ibatis.annotations.Delete;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Update;

/**
 * <AUTHOR>
 * @since 2020/9/9
 */
public interface TruncateTableMapper {

    @Update({"truncate table ads.business_result"})
    void truncateBusinessResult();

    @Update({"truncate table ads.daily_channel_device"})
    void truncateDailyChannelDevice();

    @Update({"truncate table ads.daily_result"})
    void truncateDailyResult();

    @Update({"truncate table ads.product_using_time"})
    void truncateProductUsingTime();

    @Update({"truncate table ads.viedo_detail"})
    void truncateVideoDetail();

    @Update({"truncate table ads.daily_prentice_device"})
    void truncateDailyPrenticeDevice();

    @Update({"truncate table ads.video_temp"})
    void truncateAdsVideoTemp();

    @Update({"truncate table ads.business_temp"})
    void truncateBusinessTemp();

    @Delete({"delete from ads.realtime_au_temp where logday = #{logDay}"})
    int deleteRealTimeAuTemp(@Param("logDay") String logDay);

    @Delete({"delete from ads.realtime_nu_temp where logday = #{logDay}"})
    int deleteRealTimeNuTemp(@Param("logDay") String logDay);

}
