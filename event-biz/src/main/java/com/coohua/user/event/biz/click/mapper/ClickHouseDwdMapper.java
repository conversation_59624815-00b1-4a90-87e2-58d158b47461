package com.coohua.user.event.biz.click.mapper;

import com.coohua.user.event.biz.click.entity.*;
import com.coohua.user.event.biz.dc.entity.WithdrawOrderDetail;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2020/9/16
 */
public interface ClickHouseDwdMapper {

    @Select({
            "<script>",
            "select userid,os,device_id,product from dwd.user_profile_dist where userid in ",
            "<foreach collection='ids' index='index' item='item' open='(' separator=',' close=')'>#{item}</foreach>",
            "</script>",
    })
    List<UserProfileDist> queryUserInfoListJustIdAndOs(@Param("ids") List<Long> userIdList);


    @Select({
            "select t3.id as id, t1.product as product, t2.product_name as product_name ,t2.product_group as product_group " +
                    "from ( " +
                    "         select product, count(device_id) as c " +
                    "         from dwd.au_device_dist " +
                    "         where logday = today() " +
                    "         group by product " +
                    "         order by c desc " +
                    "         limit 40) as t1 " +
                    "         left join dwd.product_map_dist t2 on t1.product = t2.product " +
                    "         left join dwd.app_mapping_dist t3 on t1.product = t3.product"
    })
    List<ProductEntity> queryTop40();

    @Select({"<script>",
            "select uniqExact(device_id) as count,groupUniqArray(device_id) as deviceArray from dwd.product_ocpc_activate_dist where product = #{product} and " +
                    " logday >= today()-15 and device_id in ",
            "<foreach collection='deviceIdList' index='index' item='item' open='(' separator=',' close=')'>#{item}</foreach>",
            "</script>"})
    UserNoTfEntity countFromAllTfDist(@Param("product") String product,
                                      @Param("deviceIdList")List<String> deviceIdList);

    @Select({"<script>",
            "select groupUniqArray(userid) as deviceArray from dwd.device_dist where  " +
                    " logday >= today()-15 and device_id in ",
            "<foreach collection='deviceIdList' index='index' item='item' open='(' separator=',' close=')'>#{item}</foreach>",
            "</script>"})
    String queryUserIdListByProductAndDeviceId(@Param("product") String product,
                                                     @Param("deviceIdList")List<String> deviceIdList);


    @Select({"<script>",
            "select groupUniqArray(device_id) as deviceArray from dwd.device_dist where " +
                    " logday >= today()-15 and device_id in ",
            "<foreach collection='deviceIdList' index='index' item='item' open='(' separator=',' close=')'>#{item}</foreach>",
            "</script>"})
    String queryUserIdListByDeviceId(@Param("deviceIdList")List<String> deviceIdList);

    @Select({"<script>",
            "select groupUniqArray(device_id) as deviceArray from dwd.device_dist where product = #{product} and " +
                    " logday >= today()-15 and userid in ",
            "<foreach collection='userIdList' index='index' item='item' open='(' separator=',' close=')'>#{item}</foreach>",
            "</script>"})
    String queryUserIdListByProductAndUserId(@Param("product") String product,
                                               @Param("userIdList")List<String> userIdList);

    @Select({" select master_id, groupUniqArray(prentice_id) downArray " +
            " from dwd.mysql_relationship_dist " +
            " where product = #{product} " +
            "  and logday = #{logday} " +
            " group by master_id " +
            " having length(downArray) >= 10; "})
    List<UserGrayFriendShipEntity> queryUserFriendShipList(@Param("product") String product,
                                                           @Param("logday")String logday);

    @Select({"select distinct userid" +
            "  from (" +
            "      select  master_id as userid from dwd.mysql_relationship_dist" +
            "       where logday = #{logday}" +
            "        and product = #{product}" +
            "        and prentice_id global in (" +
            "          select toInt64(target_id)" +
            "          from ods_mysql.already_gray_user" +
            "          where product = #{product}" +
            "            and target_type = 2" +
            "            and target_id is not null" +
            "            and target_id != 'null'" +
            "            and toDate(already_gray_user.create_time) = #{logday}" +
            "      )" +
            "        and master_id global not in (" +
            "          select toInt64OrZero(userid)" +
            "          from dwd.au_device_dist" +
            "          where logday = #{logday}" +
            "            and product = #{product}" +
            "            and device_id global in (" +
            "              select distinct device_id from dwd.au_alliance_tf_dist where logday = #{logday} and " +
            "               product = #{product}" +
            "          )" +
            "      )" +
            "        and status = 1" +
            "      union all" +
            "      select prentice_id as userid from dwd.mysql_relationship_dist" +
            "      where logday = #{logday}" +
            "        and product = #{product}" +
            "        and master_id global in (" +
            "          select toInt64(target_id)" +
            "          from ods_mysql.already_gray_user" +
            "          where product = #{product}" +
            "            and target_type = 2" +
            "            and target_id is not null" +
            "            and target_id != 'null'" +
            "            and toDate(already_gray_user.create_time) = #{logday}" +
            "      )" +
            "        and status = 1); "})
    List<String> queryAlreadyGrayUserFriendShipList(@Param("product") String product,
                                                           @Param("logday")String logday);

    @Select({"select r2.id as app_id,r1.userArray as user_str from ( " +
            "               select product, " +
            "                      arrayDistinct(arrayFlatten([groupUniqArray(master_id),groupUniqArray(prentice_id)])) as userArray " +
            "               from dwd.mysql_relationship_dist " +
            "               where logday = #{logday} " +
            "                 and status = 1 " +
            "               group by product " +
            ") r1 left join dwd.app_mapping_dist r2 on r1.product = r2.product"})
    List<InnerPullBean> queryRelation(@Param("logday")String logday);

    @Select("select * from dwd.mysql_relationship_dist where product =#{product} and master_id = #{userId}")
    List<RelationshipEntity> queryUserRelation(@Param("product") String product,@Param("userId") Long userId);

    @Select("select * from dwd.withdraw_dist where product =#{product} and user_id = #{userId} and logday >= today() - 7")
    List<WithdrawOrderDetail> queryWithdrawEntity(@Param("product") String product,@Param("userId") Long userId);

    @Select("select distinct ad_id from dwd.tb_ap_bidding_dist")
    List<Long> queryBiddingAdList();

    @Select(" select id as ad_id, ifNull(ecpm, 10) as ecpm " +
            " from dwd.tb_ap_ad_budget_dist " +
            " where (toString(id) " +
            "    global in (select ad_id " +
            "               from dwd.product_ad_conf_dist " +
            "               where type_name = '视频' " +
            "                 and pos_id global in (select pos_id " +
            "                                       from dwd.pos_ecpm_dist " +
            "                                       where logday >= today() - 3))) " +
            "   or id > 350000")
    List<AdEcpmConfig> queryEcpmConfig();


    @Select("select * from dwd.ad_type_basic_dist")
    List<AdTypeEntity> queryAllAdType();

    @Select({"select r2.id as app_id,r1.master_id as master_id,r1.userArray as user_str from ( " +
            "                 select product,master_id, " +
            "                        groupUniqArray(prentice_id) as userArray " +
            "                 from dwd.mysql_relationship_dist " +
            "                 where logday = #{logday} " +
            "                   and status = 1 " +
            "                 group by product,master_id " +
            ") r1 left join dwd.app_mapping_dist r2 on r1.product = r2.product"})
    List<InnerMasterBean> queryInnerMasterInfo(@Param("logday")String logday);

}
