package com.coohua.user.event.biz.click.mapper;

import com.coohua.user.event.biz.click.entity.ProductEntity;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2020/9/14
 */
public interface ClickHoseBaseProductMapper {

    @Select({"SELECT" +
            " amd.id AS id," +
            " pmd.product AS product," +
            " pmd.product_name AS product_name," +
            " pmd.product_group AS product_group" +
            " FROM " +
            " dwd.app_mapping_dist amd " +
            " INNER JOIN dwd.product_map_dist pmd ON amd.product = pmd.product"})
    List<ProductEntity> queryProductMap();

    @Select({"SELECT" +
            " amd.id AS id," +
            " pmd.product AS product," +
            " pmd.product_name AS product_name," +
            " pmd.product_group AS product_group" +
            " FROM " +
            " (select  * from dwd.app_mapping_dist where id = #{appId}) amd " +
            " INNER JOIN dwd.product_map_dist pmd ON amd.product = pmd.product"})
    ProductEntity queryProductByAppId(@Param("appId") Integer appId);
}
