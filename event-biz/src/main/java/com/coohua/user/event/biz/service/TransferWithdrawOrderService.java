package com.coohua.user.event.biz.service;

import com.alibaba.fastjson.JSON;
import com.coohua.user.event.biz.click.entity.ProductEntity;
import com.coohua.user.event.biz.click.entity.UserProfileDist;
import com.coohua.user.event.biz.click.service.ClickHouseService;
import com.coohua.user.event.biz.dc.entity.WithdrawOrderDetail;
import com.coohua.user.event.biz.dc.service.WithdrawOrderDetailService;
import com.coohua.user.event.biz.user.entity.AppWithdrawConfEntity;
import com.coohua.user.event.biz.user.entity.UserEntity;
import com.coohua.user.event.biz.user.entity.UserMeta;
import com.coohua.user.event.biz.user.entity.WechatApp;
import com.coohua.user.event.biz.user.mapper.BaseQueryMapper;
import com.coohua.user.event.biz.user.service.BaseQueryService;
import com.coohua.user.event.biz.user.service.BpMallService;
import com.coohua.user.event.biz.user.service.BpUserService;
import com.coohua.user.event.biz.util.AppDrawConf;
import com.coohua.user.event.biz.util.DateUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2020/9/14
 */
@Slf4j
@Service
public class TransferWithdrawOrderService {


    @Autowired
    private ClickHouseService clickHouseService;
    @Autowired
    private BpMallService bpMallService;
    @Autowired
    private WithdrawOrderDetailService withdrawOrderDetailService;
    @Autowired
    private BpUserService bpUserService;
    @Resource
    private BaseQueryMapper baseQueryMapper;

    private final static Long DEFAULT_PAGE = 10000L;
    private final static String TABLE_PREFIX = "bp_mall.withdraw_order_";

    public void transferOrderDetail(){
        log.info("开始迁移提现表详情.....");

        Date now = new Date();
        Date yesterday = DateUtil.dateIncreaseByDay(now,-1);
        String indexDay = DateUtil.dateToString(yesterday,DateUtil.ISO_EXPANDED_DATE_FORMAT);

        Date begin = DateUtil.stringToDate(indexDay+ " 23:00:00");
//        Date currentHour = DateUtil.stringToDate(indexStr,DateUtil.COMMON_TIME_FORMAT);
//        Date begin = DateUtil.dateIncreaseByHour(currentHour,-4);
//        withdrawOrderDetailService.truncateWithdrawOrderDetail();

//        Date begin = DateUtil.dateIncreaseByDay(currentHour,-7);

        Long start = begin.getTime();
        Long end = now.getTime();

        log.info("本批次计算区间{} - {}",start,end);

        List<ProductEntity> productEntityList = clickHouseService.queryAllProduct();

        List<WechatApp> wechatApps = baseQueryMapper.queryAllPkgConf();
        Map<Long,List<WechatApp>> wechatAppMap = wechatApps.stream().collect(Collectors.groupingBy(WechatApp::getAppId));

        productEntityList.parallelStream().forEach(product->{
            try {
                log.info("删除产品[{}]的数据",product.getProductName());
                int count = withdrawOrderDetailService.delete(start,end,product.getProductName());
                log.info("产品 [{}] 成功删除 {} 条",product.getProductName(),count);
                log.info("正在同步产品[{}]的数据",product.getProductName());
                List<String> tableNameList = AppDrawConf.appWithdrawTableMap.get(product.getId());
                List<WechatApp> wechatAppsNow = wechatAppMap.get((long) product.getId());
                if (wechatAppsNow == null || wechatAppsNow.size() == 0){
                    log.error("配置异常....,未查询到pkgId");
                    return;
                }

                List<Long> pkgList = wechatAppsNow.stream().map(WechatApp::getId).collect(Collectors.toList());

                tableNameList.forEach(key -> transfer(product,start,end,key,pkgList));
                log.info("产品[{}]的数据同步完成...",product.getProductName());
            }catch (Exception e){
                log.error("产品{}异常:",product.getProductName(),e);
            }

        });

        String logDay = DateUtil.dateToString(DateUtil.dateIncreaseByDay(now,-7));
        int del = withdrawOrderDetailService.deleteByLogDay(logDay);
        if (del > 0) {
            log.info("清除{}条数{}",logDay,del);
        }

        log.info("结束迁移提现表详情.....");
    }

    private void transfer(ProductEntity entity,Long begin,Long end,String tableName,List<Long> pkgIdList){
        Long count = bpMallService.countOrderList(tableName,begin);
        Long minId = bpMallService.selectMinId(tableName,begin);

        if (count == 0){
            log.info("产品[{}]无需同步数据...",entity.getProductName());
            return;
        }

        log.info("产品[{}]需要同步{}条数据",entity.getProductName(),count);
        if (count > DEFAULT_PAGE){
            Long maxId = bpMallService.selectMaxId(tableName,end);
            for (Long i= minId; i<= maxId; i= i+DEFAULT_PAGE){
                Long indexOffset = DEFAULT_PAGE;
                if (i + DEFAULT_PAGE > maxId){
                    indexOffset = maxId - i;
                }

                try {
                    doMainSolve(tableName,i,indexOffset,entity,pkgIdList);
                }catch (Exception e){
                    log.error("第一次处理异常...重试");
                    doMainSolve(tableName,i,indexOffset,entity,pkgIdList);
                }

            }
        }else {
            try {
                doMainSolve(tableName,minId,DEFAULT_PAGE,entity,pkgIdList);
            }catch (Exception e){
                log.error("第一次处理异常...重试");
                doMainSolve(tableName,minId,DEFAULT_PAGE,entity,pkgIdList);
            }

        }

    }

    private void doMainSolve(String tableName,Long i,Long indexOffset,ProductEntity entity,List<Long> pkgList){
        log.info("正在处理第{}-{}",i,indexOffset);
        List<WithdrawOrderDetail> withdrawOrderDetails = bpMallService.queryOrderList(tableName,i,indexOffset);

        // 补充用户信息
        Map<Long,UserMeta> userEntityMap = queryUserEntityMap(withdrawOrderDetails,pkgList);

        int saveNum = withdrawOrderDetailService.saveBatch(entity,withdrawOrderDetails,userEntityMap);
        log.info("本批次成功处理了{}条",saveNum);
    }


    private Map<Long, UserMeta> queryUserEntityMap(List<WithdrawOrderDetail> withdrawOrderDetails,List<Long> pkgList){
        List<Long> userList = withdrawOrderDetails.parallelStream().map(WithdrawOrderDetail::getUserId).collect(Collectors.toList());
        if (userList.size() == 0){
            return new HashMap<>();
        }

        List<UserMeta> userEntityList = bpUserService.queryUserByIds(userList,pkgList);

        return userEntityList.parallelStream().collect(Collectors.toMap(UserMeta::getUserId, userEntity->userEntity,(u1,u2) -> u1));
    }

}
