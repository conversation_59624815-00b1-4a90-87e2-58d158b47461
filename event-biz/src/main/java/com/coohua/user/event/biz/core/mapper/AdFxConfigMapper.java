package com.coohua.user.event.biz.core.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.coohua.user.event.biz.core.entity.AdFxConfig;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * <p>
 *  Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2023-06-27
 */
public interface AdFxConfigMapper extends BaseMapper<AdFxConfig> {

    @Select({"<script>",
            "       select * from `user-event`.ad_fx_config                            ",
            "       <if test ='null != product'>                                       " ,
            "         where app_id = #{product}                                        " ,
            "       </if>                                                              " ,
            "       limit #{offset},#{limit}                                           " ,
            "</script>",
    })
    List<AdFxConfig> queryList(@Param("product") String product, @Param("offset")Integer offset, @Param("limit")Integer limit);

    @Select({"<script>",
            "select count(1) from `user-event`.ad_fx_config",
            "<if test ='null != product'>" ,
            "  where app_id = #{product} " ,
            "</if>" ,
            "</script>",
    })
    Integer queryCount(@Param("product") String product);
}
