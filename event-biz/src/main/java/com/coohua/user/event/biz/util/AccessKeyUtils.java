package com.coohua.user.event.biz.util;

import org.apache.commons.codec.digest.DigestUtils;

import java.util.Random;

/**
 * @Desc
 * <AUTHOR>
 * @Date 2019/8/29
 **/
public class AccessKeyUtils {

    public static Long STEP_INTERVAL = 1000000000L;
    private static String ACCESS_KEY_TEMPLATE = "%s_%d";
    private static Random random = new Random();

    /**
     * 生成随机 AccessKey
     *
     * @param userId
     * @return
     */
    public static String generateAccessKey(Long userId, Integer step) {
        String token = DigestUtils.md5Hex(random.nextInt() + userId + "");
        return String.format(ACCESS_KEY_TEMPLATE, token, userId + step * STEP_INTERVAL);
    }

    public static Long getComputedUserId(Long userId, Integer step){
        return getRealUserId(userId) + step * STEP_INTERVAL;
    }

    public static Long getRealUserId(Long userId){
        return userId % STEP_INTERVAL;
    }

    public static String getUserId(String ak){
        if (Strings.isEmpty( ak)){
            return "";
        }
        String[] rx = ak.split("_");
        return rx[rx.length-1];
    }

}
