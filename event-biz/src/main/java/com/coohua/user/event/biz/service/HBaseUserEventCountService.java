package com.coohua.user.event.biz.service;

import cn.hutool.core.date.StopWatch;
import com.aliyun.openservices.shade.com.alibaba.fastjson.JSON;
import com.coohua.user.event.biz.click.entity.ProductEntity;
import com.coohua.user.event.biz.service.bean.UserEventCountBean;
import com.coohua.user.event.biz.service.bean.UserEventCountHBaseBean;
import com.coohua.user.event.biz.util.AppConfig;
import com.coohua.user.event.biz.util.HBaseUtils;
import com.coohua.user.event.biz.util.Lists;
import com.coohua.user.event.biz.util.RedisKeyConstants;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.MapUtils;
import org.apache.hadoop.hbase.TableName;
import org.apache.hadoop.hbase.TableNotFoundException;
import org.apache.hadoop.hbase.client.*;
import org.apache.hadoop.hbase.io.compress.Compression;
import org.apache.hadoop.hbase.io.encoding.DataBlockEncoding;
import org.apache.hadoop.hbase.util.Bytes;
import org.apache.logging.log4j.util.Strings;
import org.springframework.stereotype.Service;
import redis.clients.jedis.Jedis;
import redis.clients.jedis.Pipeline;
import redis.clients.jedis.exceptions.JedisMovedDataException;
import top.zrbcool.pepper.boot.jediscluster.JedisClusterClient;
import top.zrbcool.pepper.boot.jediscluster.JedisClusterClientRefer;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.io.IOException;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2022/6/16
 */
@Slf4j
@Service
public class HBaseUserEventCountService {

    @Resource(name = "hbaseConnection")
    private Connection HBaseConnection;

    @JedisClusterClientRefer(namespace = "user-event")
    private JedisClusterClient jedisClusterClient;

    private final static String TABLE_NAME = "user_event_count";
    private final static  byte[] families = Bytes.toBytes("family");

    @PostConstruct
    public void createTable() throws IOException {
        try (Admin admin = HBaseConnection.getAdmin()) {
            // 建表
            try {
                admin.getDescriptor(TableName.valueOf(TABLE_NAME));
            } catch (TableNotFoundException te) {
                ColumnFamilyDescriptor familyDescriptor = ColumnFamilyDescriptorBuilder
                        .newBuilder(families)
                        .setCompressionType(Compression.Algorithm.ZSTD)
                        .setDataBlockEncoding(DataBlockEncoding.DIFF)
                        .setTimeToLive(60 * 60 * 24 * 2) //存两天
                        .build();

                TableDescriptor tableDescriptor = TableDescriptorBuilder.newBuilder(TableName.valueOf(TABLE_NAME))
                        .setColumnFamily(familyDescriptor)
                        .setCompactionEnabled(true)
                        .build();

                admin.createTable(tableDescriptor, HBaseUtils.ONLY_SPLIT_KEYS);
            }
        } catch (IOException e) {
            log.error("{}表创建失败:{}", TABLE_NAME, e);
            throw e;
        }
    }

    private static final String USER_KEY ="user:count:%s:%s";


    private String buildUserCount(String product,String userId){
        return HBaseUtils.hashRowKeyByLastCharacter(String.format(USER_KEY,product,userId));
    }

    public void saveBatchUserCount(List<String> batchList){
        if (Lists.isEmpty(batchList)){
            return;
        }

        Map<String,List<UserEventCountBean>> countMap = batchList.stream()
                .map(r -> JSON.parseObject(r,UserEventCountBean.class))
                .filter(userEventCountBean -> Strings.isNotBlank(userEventCountBean.getUserId()) &&
                        !"0".equalsIgnoreCase(userEventCountBean.getUserId()) &&
                        !"null".equalsIgnoreCase(userEventCountBean.getUserId()))
                .collect(Collectors.groupingBy(
                        userEventCountBean -> this.buildUserCount(userEventCountBean.getProduct(), userEventCountBean.getUserId())
                ));

        Map<String, UserEventCountHBaseBean> responseMap = new HashMap<>();
        countMap.forEach((k,v) ->{
            long userId = 0L;
            String product = "",os = "";

            Long allCount = 0L,adData = 0L,appUse = 0L,appClick = 0L,appStatus = 0L,startup = 0L;
            for(UserEventCountBean userEventCountBean : v){
                try {
                    userId = Long.parseLong(userEventCountBean.getUserId());
                    product = userEventCountBean.getProduct();
                    os = userEventCountBean.getOs();
                    allCount ++;
                    if ("AdData".equalsIgnoreCase(userEventCountBean.getEvent())){
                        adData++;
                    }else if ("AppUse".equalsIgnoreCase(userEventCountBean.getEvent())){
                        appUse++;
                    }else if ("AppClick".equalsIgnoreCase(userEventCountBean.getEvent())){
                        appClick++;
                    }else if ("AppStatus".equalsIgnoreCase(userEventCountBean.getEvent())){
                        appStatus++;
                    }else if ("Startup".equalsIgnoreCase(userEventCountBean.getEvent())){
                        startup++;
                    }
                }catch (Exception e){
                    log.warn("Solve Warning:",e);
                }
            }
            UserEventCountHBaseBean response = new UserEventCountHBaseBean();
            response.setQueryKey(k);
            response.setUserId(userId);
            response.setProduct(product);
            response.setAdDataCount(adData);
            response.setAppClickCount(appClick);
            response.setAppStatusCount(appStatus);
            response.setStartUpCount(startup);
            response.setEventCount(allCount);
            response.setAppUseCount(appUse);
//            response.setOs(os);
            responseMap.put(k,response);
        });
        this.insert(responseMap);

    }

    public void saveToRedis(List<String> batchList){
        try {
//            StopWatch stopWatch = new StopWatch("user-event-count-redis");
//            stopWatch.start("convert object");
            List<UserEventCountBean> userEventCountBeans = batchList.stream()
                    .map(r -> JSON.parseObject(r, UserEventCountBean.class))
                    .filter(userEventCountBean -> Strings.isNotBlank(userEventCountBean.getUserId()) &&
                            !"0".equalsIgnoreCase(userEventCountBean.getUserId()) &&
                            !"null".equalsIgnoreCase(userEventCountBean.getUserId()))
                    .collect(Collectors.toList());

            Map<String, Long> saveMap = userEventCountBeans.stream()
                    .collect(Collectors.groupingBy(
                            userEventCountBean -> String.format(REDIS_COUNT, userEventCountBean.getProduct(), userEventCountBean.getUserId()),
                            Collectors.counting()
                    ));
            Map<String, Long> saveMapWithOs = userEventCountBeans.stream()
                    .collect(Collectors.groupingBy(
                            userEventCountBean -> String.format(REDIS_COUNT_WITH_OS, userEventCountBean.getOs(), userEventCountBean.getProduct(), userEventCountBean.getUserId()),
                            Collectors.counting()
                    ));
            if (MapUtils.isNotEmpty(saveMapWithOs)) {
                saveMap.putAll(saveMapWithOs);
            }

            Map<String, Long> ipCountMap = userEventCountBeans.stream()
                    .filter(userEventCountBean -> Strings.isNotBlank(userEventCountBean.getOs()) && "android".equalsIgnoreCase(userEventCountBean.getOs()))
                    .filter(userEventCountBean -> Strings.isNotBlank(userEventCountBean.getIp()) && userEventCountBean.getIp().split(",").length > 1)
                    .collect(Collectors.groupingBy(
                            userEventCountBean -> RedisKeyConstants.getMultiIpCountKey(userEventCountBean.getOs(), userEventCountBean.getProduct(), userEventCountBean.getUserId()),
                            Collectors.counting()
                    ));

            if (MapUtils.isNotEmpty(ipCountMap)) {
                saveMap.putAll(ipCountMap);
            }

            Map<String, Set<String>> userIpMap = userEventCountBeans.stream()
                    .filter(userEventCountBean -> Strings.isNotBlank(userEventCountBean.getIp()))
                    .collect(Collectors.groupingBy(
                            userEventCountBean -> RedisKeyConstants.getUserIpKey(userEventCountBean.getOs(), userEventCountBean.getProduct(), userEventCountBean.getUserId()),
                            Collectors.reducing(
                                    new HashSet<String>(),
                                    bean -> Arrays.stream(bean.getIp().split(","))
                                            .map(String::trim)
                                            .collect(Collectors.toSet()),
                                    (set1, set2) -> {
                                        Set<String> merged = new HashSet<>(set1);
                                        merged.addAll(set2);
                                        return merged;
                                    }
                            )
                    ));

//            stopWatch.stop();
            // 增加设备关联 Redis损耗大 暂时不开放
//            Map<String, Long> saveDeviceMap = batchList.stream()
//                    .map(r -> JSON.parseObject(r, UserEventCountBean.class))
//                    .filter(userEventCountBean -> Strings.isNotBlank(userEventCountBean.getDeviceId()) &&
//                            !userEventCountBean.getDeviceId().contains("0000-") &&
//                            !"0".equalsIgnoreCase(userEventCountBean.getDeviceId()) &&
//                            !"null".equalsIgnoreCase(userEventCountBean.getDeviceId()))
//                    .collect(Collectors.groupingBy(
//                            userEventCountBean -> String.format(REDIS_COUNT, userEventCountBean.getProduct(), userEventCountBean.getDeviceId()),
//                            Collectors.counting()
//                    ));
            //
//            stopWatch.start("save redis 1");
            if (MapUtils.isNotEmpty(saveMap)) {
                try (Jedis jedis = jedisClusterClient.getResource("{count:slot}:")) {
                    Pipeline pipeline = jedis.pipelined();
                    saveMap.forEach((k, v) -> {
                        pipeline.incrBy(k, v);
                        // 过期时间 两天
                        pipeline.expire(k, 60 * 60 * 24 * 2);
                    });
//                saveDeviceMap.forEach((k, v) -> {
//                    pipeline.incrBy(k, v);
//                    // 过期时间 两天
//                    pipeline.expire(k, 60 * 60 * 24 * 2);
//                });
                    pipeline.sync();
                } catch (JedisMovedDataException jmde) {
                    jedisClusterClient.get("EmptyKey");
                }
            }
//            stopWatch.stop();

//            stopWatch.start("save redis 2");

            if (MapUtils.isNotEmpty(userIpMap)) {
                try (Jedis jedis = jedisClusterClient.getResource("{user:ip}")) {
                    Pipeline pipeline = jedis.pipelined();
                    userIpMap.forEach((k, v) -> {
                        pipeline.sadd(k, v.toArray(new String[0]));
                        pipeline.expire(k, 60 * 60 * 24);
                    });
                    pipeline.sync();
                } catch (JedisMovedDataException jmde) {
                    jedisClusterClient.get("EmptyKey");
                }
            }
//            stopWatch.stop();
//            log.info("event count cost: {}", stopWatch.prettyPrint(TimeUnit.MILLISECONDS));
        }catch (Exception e){
            log.error("Redis Save Ex: ",e);
        }
    }

    private void insert(Map<String,UserEventCountHBaseBean> batchQueryResponseMap){
        try (Table table = HBaseConnection.getTable(TableName.valueOf(TABLE_NAME))) {
            List<Increment> increments = new ArrayList<>();
            batchQueryResponseMap.forEach((key,value) ->{
                Increment increment = new Increment(Bytes.toBytes(key));
                increment.addColumn(families, Bytes.toBytes(EVENT_COUNT), value.getEventCount());
                increment.addColumn(families, Bytes.toBytes(AD_DATA), value.getAdDataCount());
                increment.addColumn(families, Bytes.toBytes(APP_STATUS), value.getAppStatusCount());
                increment.addColumn(families, Bytes.toBytes(APP_CLICK), value.getAppClickCount());
                increment.addColumn(families, Bytes.toBytes(APP_USE), value.getAppUseCount());
                increment.addColumn(families, Bytes.toBytes(START_UP), value.getStartUpCount());
                increments.add(increment);
            });

//            increments.parallelStream().forEach(increment -> {
//                try {
//                    table.increment(increment);
//                } catch (IOException e) {
//                   log.warn("Increment Error:",e);
//                }
//            });
            // 使用批次写入
            Object[] results = new Object[increments.size()];
            table.batch(increments,results);

            log.info("USER_EVENT_COUNT SAVE {}",increments.size());
        } catch (IOException | InterruptedException e) {
            log.warn("QueryException:",e);
        }
    }

    private static final String USER_ID = "user_id";
    private static final String PRODUCT = "product";
    private static final String OS = "os";
    private static final String EVENT_COUNT = "event_count";
    private static final String AD_DATA = "ad_data";
    private static final String APP_USE = "app_use";
    private static final String APP_CLICK = "app_click";
    private static final String APP_STATUS = "app_status";
    private static final String START_UP = "startup";

    // 当前需求 只需要记录总数，过期时间两天..
    private static final String REDIS_COUNT = "{count:slot}:event:count:%s:%s";
    private static final String REDIS_COUNT_WITH_OS = "{count:slot}:event:count:%s:%s:%s";


    public Integer filterUserMoreThan(Long appId,List<Long> userId,Integer count){
        ProductEntity productEntity = AppConfig.appIdMap.get(appId);
        if (productEntity == null){
            return 0;
        }
        return countBatchFromHBase(productEntity.getProduct(),userId,count);
    }

    public Integer filterUserMoreThanRedis(Long appId,List<Long> userId,Integer count){
        ProductEntity productEntity = AppConfig.appIdMap.get(appId);
        if (productEntity == null){
            return 0;
        }
        return countBatchFromRedis(productEntity.getProduct(),userId,count);
    }

    public UserEventCountHBaseBean queryUserEventCount(Long appId,Long userId){
        ProductEntity productEntity = AppConfig.appIdMap.get(appId);
        if (productEntity == null){
            return  UserEventCountHBaseBean.buildDefault("None",userId);
        }
        return searchFormHBase(productEntity.getProduct(),userId);
    }

    public UserEventCountHBaseBean queryUserEventCount(String product,Long userId){
        return searchFormHBase(product,userId);
    }


    // 切换为Redis查询
    private Integer countBatchFromRedis(String product,List<Long> userIdList,Integer count){
        Integer counts = 0;

        List<String> results = jedisClusterClient.mget(userIdList.stream()
                .map(r -> String.format(REDIS_COUNT, product, r)).toArray(String[]::new));
        for (String result : results){
            if (Strings.isNotEmpty(result)){
                try {
                    if (Integer.parseInt(result) >= count){
                        counts ++;
                    }
                }catch (Exception e){
                    log.error("Parse Er:",e);
                }
            }
        }
        return counts;
    }

    public Integer countBatchFromHBase(String product,List<Long> userIdList,Integer count){
        Integer counts = 0;
        try (Table table = HBaseConnection.getTable(TableName.valueOf(TABLE_NAME))) {
            List<Get> gets = userIdList.stream().map(userId ->{
                String key = buildUserCount(product,userId.toString());
                return new Get(Bytes.toBytes(key));
            }).collect(Collectors.toList());

            Result[] results = table.get(gets);
            if (results != null && results.length > 0) {
                for (Result result :results) {
                    if (getCountFromResult(result, EVENT_COUNT) >= count){
                        counts++;
                    }
                }
            }
        } catch (IOException e) {
            log.warn("QueryException:",e);
        }
        return counts;
    }

    private UserEventCountHBaseBean searchFormHBase(String product,Long userId){

        try (Table table = HBaseConnection.getTable(TableName.valueOf(TABLE_NAME))) {
            String key = buildUserCount(product,userId.toString());
            Get get = new Get(Bytes.toBytes(key));
            Result result = table.get(get);
            UserEventCountHBaseBean userEventCountHBaseBean =  new UserEventCountHBaseBean();
            userEventCountHBaseBean.setProduct(product);
            userEventCountHBaseBean.setUserId(userId);
            userEventCountHBaseBean.setEventCount(getCountFromResult(result,EVENT_COUNT));
            userEventCountHBaseBean.setAdDataCount( getCountFromResult(result,AD_DATA));
            userEventCountHBaseBean.setAppClickCount(getCountFromResult(result,APP_CLICK));
            userEventCountHBaseBean.setAppStatusCount(getCountFromResult(result,APP_STATUS));
            userEventCountHBaseBean.setStartUpCount( getCountFromResult(result,START_UP));
            userEventCountHBaseBean.setAppUseCount(getCountFromResult(result,APP_USE));
            return userEventCountHBaseBean;
        } catch (IOException e) {
            log.warn("QueryException:",e);
        }
        return UserEventCountHBaseBean.buildDefault(product,userId);
    }

    private Long getCountFromResult(Result result,String cloumn){
        byte[] eventCountByte = result.getValue(families, Bytes.toBytes(cloumn));
        if (eventCountByte == null){
            return 0L;
        }
        return Bytes.toLong(eventCountByte);
    }

}
