package com.coohua.user.event.biz.service.req;

import com.coohua.user.event.biz.dc.entity.CdpAdData;
import com.coohua.user.event.biz.dc.entity.CdpCompilations;
import lombok.Data;

/**
 * <AUTHOR>
 * @since 2023/6/15
 */
@Data
public class CdpCompilationsRequest {
    private String appId;
    private String date;
    private String applicationName;
    private String applicationPackage;
    private Integer adUnlockTimes;
    private String compilationsId;
    private Integer totalUserNum;
    private Integer totalWatchTimes;

    public CdpCompilationsRequest build(CdpCompilations cdpCompilations){
        CdpCompilationsRequest request = new CdpCompilationsRequest();
        request.setAppId(cdpCompilations.getAppId());
        request.setDate(cdpCompilations.getLogday());
        request.setApplicationName(cdpCompilations.getApplicationName());
        request.setApplicationPackage(cdpCompilations.getApplicationPackage());
        request.setAdUnlockTimes(cdpCompilations.getAdUnlockTimes());
        request.setCompilationsId(cdpCompilations.getCompilationsId());
        request.setTotalUserNum(cdpCompilations.getTotalUserNum());
        request.setTotalWatchTimes(cdpCompilations.getTotalWatchTimes());
        return request;
    }
}
