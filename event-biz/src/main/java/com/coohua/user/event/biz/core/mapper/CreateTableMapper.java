package com.coohua.user.event.biz.core.mapper;

import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Update;

/**
 * <AUTHOR>
 * @since 2020/9/5
 */
public interface CreateTableMapper {


    @Update({
            "<script>",
            "CREATE TABLE ${tableName} (" +
            "  `id` bigint(20) NOT NULL AUTO_INCREMENT," +
            "  `app_id` int(11) DEFAULT NULL COMMENT '应用id'," +
            "  `user_id` bigint(20) DEFAULT NULL COMMENT '用户id'," +
            "  `device_id` varchar(128) DEFAULT NULL COMMENT '设备号'," +
            "  `channel` varchar(128) DEFAULT NULL COMMENT '渠道'," +
            "  `int_os` smallint(2) DEFAULT NULL COMMENT '进入初始化系统'," +
            "  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '日志记录时间'," +
            "  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '修改时间'," +
            "  `user_create_time` datetime DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP COMMENT '用户创建时间'," +
            "  `pkg_id` int(11) DEFAULT NULL," +
            "  PRIMARY KEY (`id`)," +
            "  KEY `crt_idx` (`create_time`) USING BTREE," +
            "  KEY `ucrt_idx` (`user_create_time`) USING BTREE," +
            "  KEY `app_idx` (`app_id`) USING BTREE," +
            "  KEY `dvid_idx` (`device_id`) USING BTREE" +
            ") ENGINE=InnoDB  DEFAULT CHARSET=utf8mb4",
            "</script>",
    })
    void createEventLog(@Param("tableName") String tableName);
}
