package com.coohua.user.event.biz.dc.mapper;

import com.coohua.user.event.biz.dc.entity.DailyLtvAdvertiserVideoDetailsAutoCalcu;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.Date;

/**
 * <p>
 *  Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2021-07-07
 */
public interface DailyLtvAdvertiserVideoDetailsAutoCalcuMapper extends BaseMapper<DailyLtvAdvertiserVideoDetailsAutoCalcu> {

    @Select({"<script>",
            "select advertiser_id,video_name, sum(ltv) as ltv, sum(toufang_device) as toufang_device, sum(ltv_other) as ltv_other " ,
                    "from ads.daily_ltv_advertiser_video_details_auto_calcu " ,
                    "where logday = #{logday} " ,
                    "<if test ='null != product'>" ,
                    "  and product = #{product} " ,
                    "</if>" ,
                    "  and period = 25 " ,
                    "  and advertiser_id = #{advertiserId} " ,
                    "  and video_name = #{videoName} " ,
                    "group by advertiser_id,video_name",
            "</script>"
    })
    DailyLtvAdvertiserVideoDetailsAutoCalcu selectByAdvertiserId(@Param("logday") String logday,
                                                                 @Param("product") String product,
                                                                 @Param("advertiserId") String advertiserId,
                                                                 @Param("videoName") String videoName);

    @Select({"<script>",
            "select advertiser_id,video_name,toufang_type, sum(ltv) as ltv, sum(toufang_device) as toufang_device, sum(ltv_other) as ltv_other " ,
            "from ads.daily_ltv_advertiser_video_details_auto_calcu " ,
            "where logday = #{logday} " ,
            "<if test ='null != product'>" ,
            "  and product = #{product} " ,
            "</if>" ,
            "  and period = 25 " ,
            "  and advertiser_id = #{advertiserId} " ,
            "  and video_name = #{videoName} " ,
            "  and toufang_type = #{toufangType} " ,
            "group by advertiser_id,video_name,toufang_type",
            "</script>"
    })
    DailyLtvAdvertiserVideoDetailsAutoCalcu selectByAdvertiserIdAndTouFangType(@Param("logday") String logday,
                                                                 @Param("product") String product,
                                                                 @Param("advertiserId") String advertiserId,
                                                                 @Param("toufangType") String toufangType,
                                                                 @Param("videoName") String videoName);
}
