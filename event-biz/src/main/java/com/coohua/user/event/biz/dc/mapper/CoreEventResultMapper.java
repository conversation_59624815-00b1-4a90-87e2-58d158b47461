package com.coohua.user.event.biz.dc.mapper;

import com.coohua.user.event.biz.dc.entity.CoreEventResultEntity;
import org.apache.ibatis.annotations.Delete;
import org.apache.ibatis.annotations.Insert;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2020/9/19
 */
public interface CoreEventResultMapper {


    @Delete({"delete from ads.core_event_result where logday =#{logDay}"})
    int delete(@Param("logDay")String logDay);

    @Insert({
            "<script>",
            "INSERT INTO ads.core_event_result (" +
                    "logday," +
                    "product_group," +
                    "product_en," +
                    "product_cn," +
                    "os," +
                    "dsp," +
                    "account_id," +
                    "cid," +
                    "gid," +
                    "pid," +
                    "count_active," +
                    "event_type1," +
                    "event_type2," +
                    "event_type3," +
                    "event_type4," +
                    "event_type5" +
                    ")",
            "VALUES",
            "<foreach collection='coreEventResultEntityList'  item='coreEventResultEntity' separator=','>" +
                    "(" +
                    "#{coreEventResultEntity.logday}," +
                    "#{coreEventResultEntity.productGroup}," +
                    "#{coreEventResultEntity.productEn}," +
                    "#{coreEventResultEntity.productCn}," +
                    "#{coreEventResultEntity.os}," +
                    "#{coreEventResultEntity.dsp}," +
                    "#{coreEventResultEntity.accountId}," +
                    "#{coreEventResultEntity.cid}," +
                    "#{coreEventResultEntity.gid}," +
                    "#{coreEventResultEntity.pid}," +
                    "#{coreEventResultEntity.countActive}," +
                    "#{coreEventResultEntity.eventType1}," +
                    "#{coreEventResultEntity.eventType2}," +
                    "#{coreEventResultEntity.eventType3}," +
                    "#{coreEventResultEntity.eventType4}," +
                    "#{coreEventResultEntity.eventType5}" +
                    ")",
            "</foreach>",
            "</script>",
    })
    int insertBatch(@Param("coreEventResultEntityList") List<CoreEventResultEntity> coreEventResultEntityList);
}
