package com.coohua.user.event.biz.core.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import java.util.Date;
import com.baomidou.mybatisplus.annotation.TableId;
import java.io.Serializable;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR>
 * @since 2020-09-03
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value="UserEvent对象", description="")
public class UserEvent implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    @ApiModelProperty(value = "应用id")
    private Integer appId;

    @ApiModelProperty(value = "用户id")
    private Long userId;

    @ApiModelProperty(value = "设备号")
    private String deviceId;

    @ApiModelProperty(value = "渠道")
    private String channel;

    @ApiModelProperty(value = "用户留存类型")
    private String userStat;

    @ApiModelProperty(value = "进入初始化系统")
    private Integer intOs;

    @ApiModelProperty(value = "计算日期")
    private String dateStr;

    @ApiModelProperty(value = "请求json")
    private String requestJson;

    @ApiModelProperty(value = "创建时间")
    private Date createTime;

    @ApiModelProperty(value = "更新时间")
    private Date updateTime;


}
