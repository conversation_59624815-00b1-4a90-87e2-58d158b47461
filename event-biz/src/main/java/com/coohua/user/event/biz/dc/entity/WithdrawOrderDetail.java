package com.coohua.user.event.biz.dc.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import java.io.Serializable;
import java.sql.Date;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * 支付订单 & 付款订单总表
 * </p>
 *
 * <AUTHOR>
 * @since 2020-09-14
 */
@Data
@EqualsAndHashCode(callSuper = false)
@ApiModel(value="WithdrawOrderDetail对象", description="支付订单 & 付款订单总表")
public class WithdrawOrderDetail implements Serializable {

    private static final long serialVersionUID = 1L;

    private Long id;

    private String productCn;

    private String productEn;

    private String productGroup;

    private String logday;

    private Date logdayCk;

    @ApiModelProperty(value = "微信最多 32 位")
    private String orderNo;

    @ApiModelProperty(value = "用户ID ")
    private Long userId;

    private String os;

    private String deviceId;

    private Integer subType;

    @ApiModelProperty(value = "订单状态\n1: 待审核, 2: 审核通过 3: 审核不通过-待退款 4.审核不通过-已退款 5. 付款成功 6.付款失败-待退款 6.付款失败-已退款")
    private Integer status;

    private String title;


    private Integer channel;

    private Integer amount;

    private Integer checkAuth;

    private String extra;

    private Long createTime;

    private Long updateTime;

    private Long wechatId;

    @ApiModelProperty(value = "1.积分提现 2.金币提现")
    private Integer withdrawType;

}
