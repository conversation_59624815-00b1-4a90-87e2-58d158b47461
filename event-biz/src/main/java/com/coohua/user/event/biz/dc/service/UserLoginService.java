package com.coohua.user.event.biz.dc.service;

import com.coohua.user.event.biz.dc.entity.LoginUserInfo;
import com.coohua.user.event.biz.dc.mapper.UserInfoMapper;
import com.jcraft.jsch.UserInfo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @since 2023/3/29
 */
@Slf4j
@Service
public class UserLoginService {

    @Resource
    private UserInfoMapper userInfoMapper;

    public LoginUserInfo checkLogin(String ur){
        return userInfoMapper.queryByUser(ur);
    }

    public LoginUserInfo checkLoginById(String id){
        return userInfoMapper.queryByUserId(id);
    }
}
