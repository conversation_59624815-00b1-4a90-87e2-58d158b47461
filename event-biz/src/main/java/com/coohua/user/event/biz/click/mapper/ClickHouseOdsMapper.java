package com.coohua.user.event.biz.click.mapper;

import com.coohua.user.event.biz.click.entity.*;
import com.coohua.user.event.biz.dc.dto.RealtimeCsjRulesDto;
import com.coohua.user.event.biz.service.bean.UserActive;
import org.apache.ibatis.annotations.Insert;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @since 2021/8/5
 */
public interface ClickHouseOdsMapper {

    @Select({"select product, os, userid as user_id, count() as exposure_times " +
            "from ods.event_dist r1 global " +
            "         left join dwd.ad_type_basic_dist r2 on r1.ad_type = toString(r2.ad_type) " +
            "where r1.product = #{product} " +
            "  and r1.logday = #{logday} " +
            "  and r1.ad_action = 'exposure' " +
            "  and r1.event = 'AdData' " +
            "  and r2.type_name = '视频' " +
            "group by product, userid, os " +
            "having exposure_times > #{timeLimit} * 1.1; "})
    List<AbnormalUserEntity> queryProductExposure(@Param("product") String product,
                                                  @Param("logday") String logDay,
                                                  @Param("timeLimit") Integer limit);

    @Select({
            "              select app_id,user_id,count() as count from ods.ad_point_dist                                                     " +
            "              where logday = '2024-07-08'                                                     " +
            "              and uri like '/user-call-back/call_back/ad%'                                                     " +
            "              and user_id >= #{minUserId}                                                      " +
            "              and user_id <= #{maxUserId}                                                      " +
            "              group by app_id,user_id                                                     "
    })
    List<Map<String,Object>> initSelect(@Param("minUserId") String minUserId,@Param("maxUserId") String maxUserId);


    @Select({"select uniqExact(user_id) from ods.ad_point_dist  where app_id = #{appId} and logday = yesterday();"})
    Integer countUserId(@Param("appId")String appId);

    @Select(" select user_id, avg(toInt32(price)) as avg_ecpm " +
            " from ods.ad_point_dist " +
            " where app_id = '461' " +
            "  and logday = yesterday() " +
            "  and uri = '/ap-data-consumer/upload' " +
            " group by user_id " +
            " order by user_id " +
            " limit #{offset},#{limit}")
    List<UserAvgEcpmEntity> queryUserEcpmBeanList(@Param("appId") String appId,
                                                  @Param("offset") Integer offset,
                                                  @Param("limit") Integer limit);

    @Select({" select os, ip, groupUniqArray(device_id) as device_array, length(device_array) as device_count  " +
            "    from ods.event_dist  " +
            " where (logday = yesterday() or (hour <= toHour(now()) -2 and logday = today())) " +
            "  and product = #{product}  " +
            " group by os, ip  " +
            " having device_count >= 10;"})
    List<UserIpEntity> queryGrayUserIp(@Param("product") String product,
                                       @Param("logday")String logday);

    @Select({" select device_id, os, sum(if(ad_action = 'exposure', 1, 0)) as exposure, sum(if(ad_action = 'click', 1, 0)) as click " +
            " from ods.event_dist " +
            " where product = #{product}  " +
            "  and logday = #{logday} " +
            "  and ad_action in ('click', 'exposure') " +
            "  and ad_type global in (select toString(ad_type) from dwd.ad_type_basic_dist where type_name = '视频') " +
            " group by device_id, os " +
            " having (exposure > 25 and click > exposure) or (exposure > 10 and click > 5*exposure)"})
    List<UserExposureEntity> queryExposureEx(@Param("product") String product,
                           @Param("logday")String logday);

    @Select({"<script>",
            "select concat(manufacturer,'-',model) as manufacturer,uniqExact(userid) as userCount from ods.event_dist where product = #{product} and " +
                    " logday = #{logday} and userid in ",
            "<foreach collection='userIdList' index='index' item='item' open='(' separator=',' close=')'>#{item}</foreach>" +
                    " group by manufacturer;",
            "</script>"})
    List<UserGrayModelEntity> queryGrayUserModel(@Param("product") String product,
                                                 @Param("logday")String logday,
                                                 @Param("userIdList")List<String> deviceIdList);

    @Select({" select channel,groupUniqArray(device_id) deviceArray " +
            " from ods.event_dist " +
            " where logday >= #{logday}" +
            "  and event = 'AdData' " +
            "  and product =  #{product}  " +
            "  and ad_action = 'exposure' " +
            "  and (channel like 'csj%' or channel like 'ttzz%' or channel like 'tx%' or channel like 'ks%' or " +
            "       channel like 'gdt%') " +
            " group by channel " +
            " having length(deviceArray) > 50 and length(deviceArray) < 1000 "})
    List<UserGrayChannelEntity> queryGrayUserChannel(@Param("product") String product,
                                                     @Param("logday")String logday);

    @Select({"<script>",
            "select uniqExact(ocpc_device_id) as count,groupUniqArray(ocpc_device_id) as deviceArray from ods.toutiao_click_dist where product = #{product} and " +
                    " logday >= yesterday() and ocpc_device_id in ",
            "<foreach collection='deviceIdList' index='index' item='item' open='(' separator=',' close=')'>lower(hex(MD5(#{item})))</foreach>",
            "</script>"})
    UserNoTfEntity countFromAllTfClick(@Param("product") String product,
                                       @Param("deviceIdList")List<String> deviceIdList);

    @Select({" select r1.logday, product, os, r2.userid as user_id, r1.device_id, ip, remark from ( " +
            "               select * " +
            "               from ads.gray_user_info " +
            "               where logday = today() " +
            "                 and device_id not in " +
            "                     (select target_id from ods_mysql.already_gray_user where target_type = 1 group by target_id) " +
            "                  ) r1 left  join (select * from dwd.au_device_dist where logday = today() and product global in ( select product " +
            "               from ads.gray_user_info " +
            "               where logday = today()) and device_id global in(select device_id " +
            "               from ads.gray_user_info " +
            "               where logday = today()) and match(device_id, '^00000.*') = 0) r2 on " +
            " r1.product = r2.product and r1.os = r2.os and r1.device_id  =r2.device_id " +
            " where userid != '' and userid != 'null' and userid != '0' and device_id != '0'" +
            " group by r1.logday, product, os, r2.userid, r1.device_id, ip, remark"})
    List<UserScoreEntity> queryScoreException();

    @Select({" select a1.*, a2.dau, toDecimal32(a1.gray / a2.dau * 100, 2) as gray_rate " +
            " from ( " +
            "         select product, os, uniqExact(device_id) as gray " +
            "         from ( " +
            "               select r2.product as product, os as os, device_id " +
            "               from (select product, os,target_id " +
            "                     from ods_mysql.already_gray_user " +
            "                     where toDate(create_time) = today() " +
            "                       and target_type = 1) r1 " +
            "                        left join ( " +
            "                   select product, os, device_id " +
            "                   from dwd.device_dist " +
            "                   where logday = today() " +
            "                     and device_id global in " +
            "                         (select target_id " +
            "                          from ods_mysql.already_gray_user " +
            "                          where toDate(create_time) = today() " +
            "                            and target_type = 1) " +
            "                   group by product, os, device_id " +
            "                   ) r2 on r1.product = r2.product and r1.target_id = r2.device_id  and r1.os = r2.os " +
            "               where r2.device_id != '' " +
            "                  ) " +
            "         group by product, os " +
            "         ) a1 " +
            "         left join (select product, os, uniqExact(device_id) as dau " +
            "                    from dwd.device_dist " +
            "                    where logday = today() " +
            "                    group by product, os) a2 on a1.product = a2.product and a1.os = a2.os " +
            " where a2.dau > 100; "})
    List<ProductGrayRateEntity> queryGrayRate();


    @Select({" select product, " +
            "       tupleElement(res, 1)                      as remark, " +
            "       tupleElement(res, 2)                      as grayUser, " +
            "       allGray                                   as gray, " +
            "       toDecimal32(grayUser / gray * 100, 2)     as grayRate " +
            " from ( " +
            "         select product, " +
            "                groupArray(tr)                                                                   l, " +
            "                groupArray(grayUser)                                                             m, " +
            "                arrayMap((x, y) -> (x, y), l, m)                                              as res, " +
            "                arraySum(x->tupleElement(x, 2), res)                                          as allGray, " +
            "                arraySum(x->if(tupleElement(x, 1) = '外部调用-用户注销', tupleElement(x, 2), 0), res) as registerCancel " +
            "         from ( " +
            "               select product, " +
            "                      case " +
            "                          when remark like '自定义规则拉黑%' then '自定义规则拉黑' " +
            "                          when remark like '包名拉黑%' then '包名拉黑' " +
            "                          else remark end  as tr, " +
            "                      uniqExact(target_id) as grayUser " +
            "               from ods_mysql.already_gray_user " +
            "               where toDate(create_time) = today() " +
            "                 and target_type = 2 " +
            "                 and remark != '手动拉黑' " +
            "               group by product, tr " +
            "                  ) " +
            "         group by product " +
            "         having allGray - registerCancel > #{limitCount} " +
            "         ) " +
            "         array join res " +
            " order by product, grayUser desc "})
    List<ProductGrayRateEntity> queryAlreadyGrayUser(@Param("limitCount") Integer limitCount);

    @Select({"select r1.ad_id     as ad_id, " +
            "       r2.id        as app_id, " +
            "       r1.product   as product, " +
            "       r1.os   as os, " +
            "       r1.logday   as logday, " +
            "       r1.product_name   as product_name, " +
            "       r1.pos_id    as pos_id, " +
            "       r1.pos_name  as pos_name, " +
            "       r1.ad_type   as ad_type, " +
            "       r1.type_name as type_name, " +
            "       r3.source_name as source_name, " +
            "       toDecimal32(r1.sub_rate,2) as rate, " +
            "       toDecimal32(r1.income,2) as income " +
            " from (select *, (pv_third - pv) / pv * 100 as sub_rate " +
            "      from old_mysql_ads.business_ad_cp_info " +
            "      where logday = #{logday} " +
            "        and type_name = #{typeName}" +
            "        and income > #{income} " +
            "        and sub_rate < #{rate}" +
            "        and platform in ('chuanshanjia','guangdiantong') " +
            "      order by platform) r1 " +
            " left join dwd.app_mapping_dist r2 on r1.product = r2.product " +
            " left join dwd.ad_type_basic_dist r3 on r1.ad_type = toString(r3.ad_type) "
    })
    List<ExceptionAdPosEntity> queryExceptionAdPos(@Param("logday")String logday,
                               @Param("typeName")String typeName,
                               @Param("income")Integer income,
                               @Param("rate") Integer rate);

    @Select({
            " select r1.ad_id     as ad_id, " +
            "   r2.id        as app_id, " +
            "   r1.product   as product, " +
            "   r1.os   as os, " +
            "   r1.product_name   as product_name, " +
            "   r1.pos_id    as pos_id, " +
            "   r1.pos_name  as pos_name, " +
            "   r1.ad_type   as ad_type, " +
            "   r1.type_name as type_name, " +
            "   r3.source_name as source_name, " +
            "   toDecimal32(sum(r1.income),2) as income," +
            "   max(r1.logday)  as maxDay " +
            " from (select *, (pv_third - pv) / pv * 100 as sub_rate " +
            "  from old_mysql_ads.business_ad_cp_info " +
            "  where logday <= #{logday} " +
            "    and type_name =  #{typeName} and logday >= #{logday1} " +
            "    and platform in ('chuanshanjia','guangdiantong') " +
            "  order by platform) r1 " +
            " left join dwd.app_mapping_dist r2 on r1.product = r2.product " +
            " left join dwd.ad_type_basic_dist r3 on r1.ad_type = toString(r3.ad_type) " +
            " group by ad_id,app_id,product,os,product_name,pos_id,pos_name,ad_type,type_name,source_name " +
            " having income > #{incomes} and maxDay = #{logday}"
    })
    List<ExceptionAdPosEntity> querySumIncomeAdPos(@Param("logday")String logday,
                                                   @Param("typeName")String typeName,
                                                   @Param("logday1")String logday1,
                                                   @Param("incomes")Integer incomes);


    @Select({
            "<script>",
            " select groupUniqArray(userid) " +
                    " from ods.event_dist " +
                    " where logday = today() " +
                    "  and userid != 'null' " +
                    "  and userid != '0' " +
                    "  and userid is not null " +
                    "  and product = #{product} " +
                    "<if test='null != channelList and channelList.size() >0'> " +
                    " and channel in " +
                    "  <foreach collection='channelList' item='channel' index='index' open='(' separator=', ' close=')'> " +
                    "    #{channel} " +
                    "  </foreach> " +
                    "</if>" ,
                    "<if test='null != ipList and ipList.size() >0'> " +
                    " and ip in " +
                    "  <foreach collection='ipList' item='ip' index='index' open='(' separator=', ' close=')'> " +
                    "    #{ip} " +
                    "  </foreach> " +
                    "</if>" ,
                    "<if test='null != modelList and modelList.size() >0'> " +
                    " and model in " +
                    "  <foreach collection='modelList' item='model' index='index' open='(' separator=', ' close=')'> " +
                    "    #{model} " +
                    "  </foreach> " +
                    "</if>" ,
                    "<if test='null != phoneList and phoneList.size() >0'> " +
                    " and manufacturer in " +
                    "  <foreach collection='phoneList' item='phone' index='index' open='(' separator=', ' close=')'> " +
                    "    #{phone} " +
                    "  </foreach> " +
                    "</if>" ,
                    "<if test='null != whiteList and whiteList.size() >0'> " +
                    " and device_id not in " +
                    "  <foreach collection='whiteList' item='device' index='index' open='(' separator=', ' close=')'> " +
                    "    #{device} " +
                    "  </foreach> " +
                    "</if>" ,
                    "<if test='ocpcChannel == 1'>" +
                    "and (channel like 'csj%' or channel like 'ttzz%' or channel like 'tx%' or " +
                    "                channel like 'ks%' or " +
                    "                channel like 'gdt%')",
                    "</if>" ,
                    "<if test='ocpcChannel == 2'>" +
                    "and not (channel like 'csj%' or channel like 'ttzz%' or channel like 'tx%' or " +
                    "                channel like 'ks%' or " +
                    "                channel like 'gdt%')",
                    "</if>" ,
        "</script>",
    })
    String queryTargetUser(@Param("product")String product,
                           @Param("channelList") List<String> channelList,
                           @Param("ipList") List<String> ipList,
                           @Param("modelList") List<String> modelList,
                           @Param("phoneList") List<String> phoneList,
                           @Param("ocpcChannel") Integer ocpcChannel,
                           @Param("whiteList") List<String> whiteList
    );

    @Select({
            "<script>",
            " select userid as userId, os, product " +
                    " from ods.event_dist " +
                    " where logday = today() " +
                    "  and userid != 'null' " +
                    "  and userid != '0' " +
                    "  and userid is not null " +
                    "  and product = #{product} " +
                    "<if test='null != channelList and channelList.size() >0'> " +
                    " and channel in " +
                    "  <foreach collection='channelList' item='channel' index='index' open='(' separator=', ' close=')'> " +
                    "    #{channel} " +
                    "  </foreach> " +
                    "</if>" ,
            "<if test='null != ipList and ipList.size() >0'> " +
                    " and ip in " +
                    "  <foreach collection='ipList' item='ip' index='index' open='(' separator=', ' close=')'> " +
                    "    #{ip} " +
                    "  </foreach> " +
                    "</if>" ,
            "<if test='null != modelList and modelList.size() >0'> " +
                    " and model in " +
                    "  <foreach collection='modelList' item='model' index='index' open='(' separator=', ' close=')'> " +
                    "    #{model} " +
                    "  </foreach> " +
                    "</if>" ,
            "<if test='null != phoneList and phoneList.size() >0'> " +
                    " and manufacturer in " +
                    "  <foreach collection='phoneList' item='phone' index='index' open='(' separator=', ' close=')'> " +
                    "    #{phone} " +
                    "  </foreach> " +
                    "</if>" ,
            "<if test='null != whiteList and whiteList.size() >0'> " +
                    " and device_id not in " +
                    "  <foreach collection='whiteList' item='device' index='index' open='(' separator=', ' close=')'> " +
                    "    #{device} " +
                    "  </foreach> " +
                    "</if>" ,
            "<if test='ocpcChannel == 1'>" +
                    "and (channel like 'csj%' or channel like 'ttzz%' or channel like 'tx%' or " +
                    "                channel like 'ks%' or " +
                    "                channel like 'gdt%')",
            "</if>" ,
            "<if test='ocpcChannel == 2'>" +
                    "and not (channel like 'csj%' or channel like 'ttzz%' or channel like 'tx%' or " +
                    "                channel like 'ks%' or " +
                    "                channel like 'gdt%')",
            "</if>" ,
            "</script>",
    })
    List<UserScoreEntity> queryTargetUserEntity(@Param("product") String product, @Param("channelList") List<String> channelList, @Param("ipList") List<String> ipList, @Param("modelList") List<String> modelList, @Param("phoneList") List<String> phoneList, @Param("ocpcChannel") Integer ocpcChannel, @Param("whiteList") List<String> whiteList);

    @Select({
            "<script>",
            "select toString(user_id) from dwd.user_active_dist where product = #{product} and toString(user_id) global in ( " +
            "    select userid " +
            "    from dwd.au_device_dist " +
            "    where logday >= today() - 3 " +
            "      and device_id in " +
            "<foreach collection='deviceIdList' index='index' item='item' open='(' separator=',' close=')'>#{item}</foreach>",
            "      and product = #{product} " +
            ")",
            "</script>",
    })
    List<String> queryRegisteredIdFromDevice(@Param("product") String product,@Param("deviceIdList") List<String> deviceList);

    @Select({
            "<script>",
            "select product,groupUniqArray(userid)  as userStr,arrayDistinct(arrayFlatten([groupUniqArray(device_id),groupUniqArray(imei),groupUniqArray(oaid)]))  as deviceStr from (  " +
            "               select product, userid,max(device_id) as device_id,max(oaid) as oaid,max(imei) as imei, count() as times  " +
            "               from (  " +
            "                        select product, os,channel,model,manufacturer,ip,a1.ad_type as ad_type, userid,device_id,imei,oaid, ad_id, toFloat64(ecpm) as ecpm  " +
            "                        from ods.event_exposure_dist a1 global  " +
            "                                 left join dwd.tb_ap_ad_budget_dist a2 on a1.ad_id = toString(a2.id)  " +
            "                        where logday = today()  " +
            "                          and ad_action = 'exposure'  " +
            "                          and product = #{product} " +
            "                          and ad_type global in  " +
            "                              (select toString(ad_type) from dwd.ad_type_basic_dist where type_name = '视频')  " +
            "                          and ad_id global not in  " +
            "                              (select toString(ad_id) from dwd.tb_ap_bidding_dist where del_flag = 1)  " +
            "                        union all  " +
            "                        select product, os,channel,model,manufacturer,ip,ad_type, userid,device_id,imei,oaid, ad_id, toFloat64OrZero(extend1) as ecpm  " +
            "                        from ods.event_exposure_dist  " +
            "                        where logday = today()  " +
            "                          and ad_action = 'exposure'  " +
            "                          and product = #{product} " +
            "                          and ad_type global in  " +
            "                              (select toString(ad_type) from dwd.ad_type_basic_dist where type_name = '视频')  " +
            "                          and ad_id global in (select toString(ad_id) from dwd.tb_ap_bidding_dist where del_flag = 1)  " +
            "                        ) as t  " +
            "               where ecpm between #{min} and #{max}  " +
            " <if test='null != channelList and channelList.size() >0'> " +
            " and channel in " +
            "  <foreach collection='channelList' item='channelT' index='index' open='(' separator=', ' close=')'> " +
            "    #{channelT} " +
            "  </foreach> " +
            " </if>" ,
            " <if test='null != modelList and modelList.size() >0'> " +
            " and model in " +
            "  <foreach collection='modelList' item='modelT' index='index' open='(' separator=', ' close=')'> " +
            "    #{modelT} " +
            "  </foreach> " +
            " </if>" ,
            " <if test='null != brandList and brandList.size() >0'> " +
            " and manufacturer in " +
            "  <foreach collection='brandList' item='brandT' index='index' open='(' separator=', ' close=')'> " +
            "    #{brandT} " +
            "  </foreach> " +
            " </if>" ,
            " <if test=\"ipPer!=null and ipPer!=''\">  " +
            "  and match(channel,#{ipPer}) =1 " +
            " </if>" ,
            " <if test='null != filterPlatform and filterPlatform.size() >0'> " +
            " and ad_type global in (select toString(ad_type) from dwd.ad_type_basic_dist where source_name in  " +
            "  <foreach collection='filterPlatform' item='sourceName' index='index' open='(' separator=', ' close=')'> " +
            "    #{sourceName} " +
            "  </foreach> " +
            " )</if>" ,
            " <if test='null != sourceList and sourceList.size() >0'> " +
            " and userid global in (select toString(user_id) from dwd.user_active_dist where product = #{product} and " +
            " source in  " +
            "  <foreach collection='sourceList' item='source' index='index' open='(' separator=', ' close=')'> " +
            "    #{source} " +
            "  </foreach> " +
            " )</if>" ,
            " <if test='null != ocpcType and ocpcType == 1'> " +
            " and userid global in(select toString(user_id) from dwd.user_active_dist where " +
            "                    product = #{product} and source in ('toutiao','kuaishou','guangdiantong')  " +
            "                   ) " +
            " </if>" ,
            " <if test='null != ocpcType and ocpcType == 2'> " +
            " and userid global in(select toString(user_id) from dwd.user_active_dist where " +
            "                    product = #{product} and source not in ('toutiao','kuaishou','guangdiantong')  " +
            "                   ) " +
            " </if>" ,
            " <if test='null != newUserType and newUserType == 1'> " +
            " and userid global in(select toString(user_id) from dwd.user_active_dist where " +
            "                    product = #{product} and toDate(create_time) = today()  " +
            "                   ) " +
            " </if>" ,
            " <if test='null != newUserType and newUserType == 2'> " +
            " and userid global in(select toString(user_id) from dwd.user_active_dist where " +
            "                    product = #{product} and toDate(create_time) &lt; today()  " +
            "                   ) " +
            " </if>" ,
            "               group by product, os, userid  " +
            "               having times &gt; #{times}  " +
            "                  )  " +
            " group by product ",
            "</script>",
    })
    List<InnerPullBean> queryECPMExUser(@Param("product") String product,@Param("max") Integer max,
                                        @Param("min") Integer min,@Param("times")Integer times,
                                        @Param("channelList") List<String> channelList,
                                        @Param("modelList")List<String> modelList,
                                        @Param("brandList")List<String> brandList,
                                        @Param("ipPer")String ipPer,
                                        @Param("sourceList")List<String> sourceList,
                                        @Param("newUserType")Integer newUserType,
                                        @Param("ocpcType")Integer ocpcType,
                                        @Param("filterPlatform")List<String> filterPlatform
    );


    @Select("select * from ods.ad_point_dist where logday >= today() -3 " +
            "and app_id = #{appId} and user_id = #{userId} and uri = '/ap-data-consumer/upload' " +
            "and ad_id global in (select ad_id from dwd.product_ad_conf_dist where type_name = '视频') " +
            "order by time")
    List<VideoAdReportEntitiy> queryUserVideoDetail(@Param("appId") Integer appId, @Param("userId") String userId);

    @Select("select logday,count() as videoCount,toDecimal32(avg(toInt32OrZero(price)),2) as avgEcpm " +
            "from ods.ad_point_dist where logday >= today() -7 " +
            "and app_id = #{appId} and user_id = #{userId} and uri = '/ap-data-consumer/upload' " +
            "and ad_id global in (select ad_id from dwd.product_ad_conf_dist where type_name = '视频') " +
            "group by logday " +
            "order by logday")
    List<UserArpuEntity> queryAvgVideo(@Param("appId") Integer appId, @Param("userId") String userId);


    @Select("select imei,android_id,oaid,device_id from ods.event_dist where product= #{product} and userid = #{userId}  " +
            "and logday >= today() - 3 and device_id !='' and android_id != '' and imei !='null' and imei !=''  " +
            "group by imei,android_id,oaid,device_id  " +
            "limit 1;")
    UserDeviceEntity queryUserDeviceInfo(@Param("product") String product,@Param("userId") String userId);


    @Select({"select ip, channel, groupUniqArray(userid) as userIdStr, groupUniqArray(device_id) as deviceIdStr " +
            "         from ods.event_dist " +
            "         where logday = today() " +
            "           and (channel like '%ksdr%' or channel like '%ttdr%') " +
            "           and mac in ('02:00:00:00:00:00', '02:00:00:00:00:02') " +
            "           and userid != '0' and userid !='null' " +
            "         group by ip, channel " +
            "         having length(userIdStr) >= 4"})
    List<UserMacIpCheckEntity> queryUserMacIpCheck();

    @Select(" select product,groupUniqArray(userid) as userIdStr,groupUniqArray(device_id) as deviceIdStr from ( " +
            "               select product, " +
            "                      userid, " +
            "                      max(device_id)                                       as device_id, " +
            "                      count(if(ad_action = 'exposure'  and substring(ad_type,1,4) " +
            "                   in ('1015','1018','1008','1093','1085','1104','1096','1097','1095','1200','1098'), 1, null))           as exposure, " +
            "                      count(if(ad_action = 'video_cache_finish', 1, null)) as video_cache_finish, " +
            "                      count(if(ad_action != 'exposure', 1, null))          as exp " +
            "               from ods.event_dist " +
            "               where logday = today() " +
            "                 and event = 'AdData' " +
            "                 and os = 'android' and product_part != 'wmin' " +
            "               group by userid, product " +
            "               having exposure > 5 " +
            "                  and video_cache_finish = 0 " +
            "                  and exp <= 10 " +
            "                  ) " +
            " group by product")
    List<UserMacIpCheckEntity> queryUserEventUploadExp();


    @Select({" select product, os, groupUniqArray(userid) as userIdStr, groupUniqArray(device_id) as deviceIdStr " +
            " from ods.event_exposure_dist " +
            " where match(channel, '^vivo*|^oppo*') = 1 " +
            "  and os = 'android' " +
            "  and ad_action = 'exposure' " +
            "  and logday = today() " +
            "  and match(lower(manufacturer), '^vivo*|^oppo*|^oneplus*') = 0 " +
            "  and userid global in " +
            "      (select toString(user_id) from dwd.user_active_dist where toDate(create_time) > today() - 30 and source = '自然量') " +
            " group by product, os"})
    List<UserMacIpCheckEntity> queryModelMatchedChannel();

    @Select({
            " select " +
            "        teamName, " +
            "        product, " +
            "        os, " +
            "        groupUniqArray(user_id)   as userIdStr, " +
            "        groupUniqArray(device_id) as deviceIdStr, " +
            "        pv, " +
            "        avgEcpm " +
            " from (select " +
            "              p.product_group as teamName, " +
            "              r1.product as product, " +
            "              os, " +
            "              userid                  as user_id, " +
            "              max(device_id)          as device_id, " +
            "              count()                 as pv, " +
            "              sum(if(toFloat64OrZero(extend1) > 0 and toFloat64OrZero(extend1) < 50000, toFloat64OrZero(extend1), " +
            "                     r2.ecpm))        as sumEcpm, " +
            "              avg(if(toFloat64OrZero(extend1) > 0 and toFloat64OrZero(extend1) < 50000, toFloat64OrZero(extend1), " +
            "                     r2.ecpm))        as avgEcpm, " +
            "              sum(if(ad_type global in (select toString(ad_type) from dwd.ad_type_basic_dist where source_name = '穿山甲'), " +
            "                     if(toFloat64OrZero(extend1) > 0 and toFloat64OrZero(extend1) < 50000, toFloat64OrZero(extend1), " +
            "                        r2.ecpm), 0)) as csjIn, " +
            "              sum(if(ad_type global in (select toString(ad_type) from dwd.ad_type_basic_dist where source_name = '广点通'), " +
            "                     if(toFloat64OrZero(extend1) > 0 and toFloat64OrZero(extend1) < 50000, toFloat64OrZero(extend1), " +
            "                        r2.ecpm), 0)) as gdtIn, " +
            "              sum(if(ad_type global in (select toString(ad_type) from dwd.ad_type_basic_dist where source_name = '快手'), " +
            "                     if(toFloat64OrZero(extend1) > 0 and toFloat64OrZero(extend1) < 50000, toFloat64OrZero(extend1), " +
            "                        r2.ecpm), 0)) as ksIn " +
            "       from ods.event_exposure_dist r1 " +
            "                left join dwd.tb_ap_ad_budget_dist r2 on r1.ad_id = toString(r2.id) " +
            "                left join dwd.product_map_dist p on p.product = r1.product " +
            "       where logday = today() " +
            "         and ad_action = 'exposure' " +
            "         and ad_type global in (select toString(ad_type) from dwd.ad_type_basic_dist where type_name = '视频') " +
            "       group by teamName,product, os, userid " +
            "       having pv > 100 " +
            "          and avgEcpm > 1000 " +
            "          and (csjIn / sumEcpm > 0.9 or ksIn / sumEcpm > 0.9 or gdtIn / sumEcpm > 0.9)) " +
            " group by  teamName,product, os, pv, avgEcpm " +

            "  "
    })
    List<UserMacIpCheckEntity>  queryExArpuUser();

    @Select(" select ip " +
            " from (select product, " +
            "             os, " +
            "             ip, " +
            "             uniqExact(userid) as uc " +
            "      from ods.event_exposure_dist " +
            "      where logday = today() " +
            "        and ad_action = 'exposure' " +
            "        and ad_type global in (select toString(ad_type) from dwd.ad_type_basic_dist where type_name = '视频') " +
            "      group by ip, product, os " +
            "      having uc > 10) " +
            " group by ip")
    List<String> queryPreExIpList();

    @Select("select product, " +
            "       os, " +
            "       channel, " +
            "       manufacturer, " +
            "       model, " +
            "       IPv4NumToStringClassC(IPv4StringToNum(ip))                               as start, " +
            "       groupUniqArray(userid)                                                   as userIdStr, " +
            "       arrayDistinct(arrayFlatten([groupUniqArray(imei),groupUniqArray(oaid)])) as deviceIdStr " +
            "from ods.event_exposure_dist " +
            "where logday = today() " +
            "  and os = 'android' " +
            "  and ad_action = 'exposure' " +
            "  and userid global in ( " +
            "    select toString(user_id) from dwd.user_active_dist where toDate(create_time) = today() " +
            ") " +
            "group by channel, manufacturer, model, start, product, os " +
            "having length(userIdStr) > 10")
    List<UserMacIpCheckEntity> queryExIpUser();

    @Select("select product,  " +
            "       os,  " +
            "       arrayDistinct(arrayFlatten(groupUniqArray(usrAry))) as userIdStr,  " +
            "       arrayDistinct(arrayFlatten(groupUniqArray(devAry))) as deviceIdStr  " +
            " from (  " +
            "      select product,  " +
            "             os,  " +
            "             element_uri,  " +
            "             count()                   as c,  " +
            "             groupUniqArray(userid)    as usrAry,  " +
            "             groupUniqArray(device_id) as devAry  " +
            "      from ods.event_exposure_dist  " +
            "      where logday = today()  " +
            "        and os = 'ios'  " +
            "        and ad_action = 'exposure'  " +
            "        and ad_type like '1015%'  " +
            "        and element_uri != ''  " +
            "      group by element_uri, product, os  " +
            "      having c > 10  " +
            "         )  " +
            " group by product, os")
    List<UserMacIpCheckEntity> queryRepeatRequestId();


    @Select("select product, " +
            "       os, " +
            "       groupUniqArray(userid)                                                         as userIdStr, " +
            "       arrayDistinct(arrayFlatten([groupUniqArray(imeiAry),groupUniqArray(oaidAry),groupUniqArray(deviceAry)])) as firstAry, " +
            "       arrayFilter(r -> r!='' and match(r, '^0000.*') = 0, firstAry) as deviceIdStr " +
            " from ( " +
            "      select r1.product, " +
            "             r1.os, " +
            "             r1.userid, " +
            "             groupUniqArray(r1.device_id) as deviceAry, " +
            "             groupUniqArray(r1.imei) as imeiAry, " +
            "             groupUniqArray(r1.oaid) as oaidAry, " +
            "             count()                 as pv, " +
            "             sum(if(toFloat64OrZero(r1.extend1) > 20000, r2.ecpm, toFloat64OrZero(r1.extend1)) > " +
            "                 1000)               as moreThan1k, " +
            "             sum(if(toFloat64OrZero(r1.extend1) > 20000, r2.ecpm, toFloat64OrZero(r1.extend1)) > " +
            "                 3000)               as moreThan3k, " +
            "             sum(if(toFloat64OrZero(r1.extend1) > 20000, r2.ecpm, toFloat64OrZero(r1.extend1)) > " +
            "                 600)                as moreThan6b " +
            "      from ods.event_exposure_dist r1 " +
            "               left join dwd.tb_ap_ad_budget_dist r2 on r1.ad_id = toString(r2.id) " +
            "      where logday = today() " +
            "        and ad_action = 'exposure' and userid != '0' " +
            "        and ad_type global in (select toString(ad_type) from dwd.ad_type_basic_dist where type_name = '视频') " +
            "      group by r1.product, r1.os, r1.userid " +
            "      having (pv > 15 and ( moreThan3k > 15 or moreThan1k > 50 or moreThan6b > 100) and os ='android') " +
            "              or " +
            "             (pv > 30 and ( moreThan3k > 30 or moreThan1k > 200 or moreThan6b > 300) and os ='ios') " +
            "         ) " +
            " group by product, os")
    List<UserMacIpCheckEntity> queryEcpmFilterCount();

    @Select({"select model, channel, groupUniqArray(userid) as userIdStr, groupUniqArray(device_id) as deviceIdStr " +
            "from ( " +
            "      select model, channel, userid,max(device_id) as device_id, count() as pv " +
            "      from ods.event_exposure_dist " +
            "      where logday = today() " +
            "        and (channel like '%ksdr%' or channel like '%ttdr%') " +
            "        and mac in ('02:00:00:00:00:00', '02:00:00:00:00:02') " +
            "        and ad_action = 'exposure' " +
            "        and ad_type global in " +
            "            (select toString(ad_type) from dwd.ad_type_basic_dist where type_name = '视频') " +
            "      group by model, channel, userid " +
            "      having pv >= 100 " +
            "         ) " +
            "group by model, channel " +
            "having length(userIdStr) > 3; " +
            " "})
    List<UserMacIpCheckEntity> queryUserModelCheck();


    @Select("select product, model, groupUniqArray(userid) as userIdStr, groupUniqArray(device_id) as deviceIdStr  " +
            "from ( " +
            "   select product, model, userid,device_id, count() as pv " +
            "   from ods.event_exposure_dist " +
            "   where logday = today() " +
            "     and os = 'android' " +
            "     and lower(manufacturer) not in ('xiaomi', 'huawei', 'oppo', 'vivo', 'honor', 'realme', 'samsung', 'oneplus') " +
            "     and ad_action = 'exposure' and userid != 'null' and userid!='' and userid != '0' " +
            "     and ad_type global in " +
            "         (select toString(ad_type) from dwd.ad_type_basic_dist where type_name = '视频') " +
            "   group by model, userid, product,device_id " +
            "   having pv > 100 " +
            "      ) " +
            "group by product, model " +
            "having length(userIdStr) > 5;")
    List<UserMacIpCheckEntity> queryUserModelCheckLessModel();


    @Select("select product, model, groupUniqArray(userid) as userIdStr, groupUniqArray(device_id) as deviceIdStr from ods.event_dist where logday = today() and manufacturer = 'Xiaomi'  " +
            "and endsWith(lower(model),'c') = 0 and startsWith(lower(model),'m') = 0 and startsWith(lower(model),'red') = 0  " +
            "and startsWith(lower(model),'xiaomi') = 0 and startsWith(model,'2') = 0 and userid != 'null' and userid!='' and userid != '0' " +
            "group by product, model;"
    )
    List<UserMacIpCheckEntity> queryXiaoMiExModel();


    @Select(" select product, " +
            "       groupUniqArray(device_id)                                   as dev, " +
            "       groupUniqArray(userid)                                      as uve, " +
            "       arrayFilter(r -> r != '', dev)                              as deviceIdStr, " +
            "       arrayFilter(r -> r != '' and r != 'null' and r != '0', uve) as userIdStr " +
            " from ods.event_dist " +
            " where logday = today() " +
            "  and model global in " +
            "      ( " +
            "          select model " +
            "          from ods.event_dist " +
            "          where logday = today() " +
            "            and manufacturer = 'Xiaomi' " +
            "            and" +
            "            (" +
            "               (length(model) = 9 and endsWith(lower(model), 'c') = 1 and match(lower(left(model, 8)), '[a-z]') = 0 )" +
            "                or" +
            "               (length(model) < 9 and endsWith(lower(model),'c') = 1 and startsWith(lower(model),'m') = 0)" +
            "            )" +
            "          group by model " +
            "          having count(model) < 1000 " +
            "      ) " +
            " group by product")
    List<UserMacIpCheckEntity> queryXiaoMiExModelSub();


    @Select(" select product,channel,groupUniqArray(userid) as userIdStr, groupUniqArray(imei) as deviceIdStr from ( " +
            "               select product, userid, device_id, imei, model, channel, sum(ad_action = 'exposure') pv " +
            "               from ods.event_dist " +
            "               where logday = today() " +
            "                 and lower(manufacturer) = 'huawei' " +
            "                 and product in ('wgxt','cydzy3','tysh2','jnxy','xiuxianswrj','xfms','xfjy','kaixindazhanggui','qzwztyygj') " +
            "                 and device_id = '' " +
            "                 and imei != 'null' " +
            "                 and userid != '0' " +
            "               group by product, userid, device_id, imei, channel, model " +
            "               having pv > 50 " +
            "                  ) " +
            " group by product,channel")
    List<UserMacIpCheckEntity> queryHuaWeiExModel();

    @Select({"select product,groupUniqArray(userid) as userIdStr,groupUniqArray(imei) as deviceIdStr from (  " +
            " select product, device_id, imei, userid, model  " +
            " from ods.event_dist  " +
            " where logday = today()  " +
            "   and manufacturer = 'samsung'  " +
            "   and model like 'SM-F____'  " +
            "   and endsWith(model, '0') = 0  " +
            "   and match(lower(model), '[a-z]$') = 0  " +
            " group by product, device_id, imei, userid, model  " +
            "    )  " +
            "group by product"})
    List<UserMacIpCheckEntity> querySamsungExModel();

    @Select("select product,arrayDistinct(arrayFlatten(groupUniqArray(userAry))) as userIdStr, " +
            "       arrayDistinct(arrayFlatten(groupUniqArray(deviceAry))) as deviceIdStr from ( " +
            "   select manufacturer, " +
            "          product, " +
            "          groupUniqArray(userid)    as userAry, " +
            "          groupUniqArray(device_id) as deviceAry, " +
            "          count()                   as pv, " +
            "          pv / length(userAry)      as ratePv " +
            "   from ods.event_exposure_dist " +
            "   where logday = today() " +
            "     and ad_action = 'exposure' and manufacturer global not in (select manufacturer from (  " +
            " select manufacturer,count() as c from ods.event_dist where logday = yesterday() group by manufacturer  " +
            " order by c desc limit 50  " +
            " )) " +
            "   group by manufacturer, product " +
            "   having length(userAry) < 5 " +
            "      and ratePv > 50 " +
            ") group by product")
    List<UserMacIpCheckEntity> queryManuColdUser();



    @Select("select product,os, " +
            "       groupUniqArray(userid)   userIdStr, " +
            "       arrayDistinct(arrayFlatten(groupUniqArray(deviceAry))) deviceIdStr " +
            "from ( " +
            "      select product,os,element_uri, count() as v, userid, groupUniqArray(device_id) deviceAry " +
            "      from ods.event_exposure_dist " +
            "      where logday = today() and length(element_uri) = 74 " +
            "        and ad_action = 'exposure' " +
            "        and ad_type global in ( " +
            "          select toString(ad_type) from dwd.ad_type_basic_dist where type_name = '视频' and source_name = '穿山甲' " +
            "      ) " +
            "      group by product,os,element_uri,userid " +
            "      having v > 10 " +
            "         ) " +
            "group by product,os;")
    List<UserMacIpCheckEntity> queryCsjRepeat();


    @Select("select userid as userId,count() as pv " +
            " from ods.event_dist " +
            " where logday = #{logday} " +
            "   and product = #{product} " +
            "   and ad_action ='exposure' " +
            "   and hour <= #{hour} " +
            "   and os = 'android' " +
            "   and ad_type global in (select toString(ad_type) from dwd.ad_type_basic_dist where type_name = '视频') " +
            "   and userid global in ( " +
            "     select toString(user_id) " +
            "     from dwd.user_active_dist " +
            "     where toDate(create_time) >= toDate(#{logday}) - 1 " +
            "       and product = #{product} " +
            "       ) " +
            "   and userid global not in ( " +
            "     select toString(user_id) " +
            "     from user_gray.byte_user_model " +
            "     where product = #{product}) " +
            " group by userid " +
            "having pv > 30 ")
    List<UserHsNoCallBackEntity> queryProductHsNoCallBackUser(@Param("product")String product,
                                                              @Param("logday")String logday,
                                                              @Param("hour")Integer hour);

    @Select("select count()  " +
            "from user_gray.byte_user_model  " +
            "where product = #{product} and logday = today()")
    Integer countHsRecord(@Param("product") String product);

    @Select("select user_id as userId,sum(amount/100) as withdraw " +
            " from dwd.withdraw_dist " +
            " where logday = #{logday} " +
            "   and product = #{product} " +
            "   and os = 'android' " +
            "   and toHour(toDateTime(create_time/1000)) <= #{hour} " +
            "   and user_id global in ( " +
            "     select user_id " +
            "     from dwd.user_active_dist " +
            "     where toDate(create_time) >= toDate(#{logday}) - 1 " +
            "       and product = #{product} " +
            "       ) " +
            "   and toString(user_id) global not in ( " +
            "     select toString(user_id) " +
            "     from user_gray.byte_user_model " +
            "     where product = #{product}) and status = 5 " +
            " group by user_id " +
            "having withdraw > 5")
    List<UserHsNoCallBackEntity> queryProductHsNoCallBackWithdrawUser(@Param("product")String product,
                                                                      @Param("logday")String logday,
                                                                      @Param("hour")Integer hour);

    @Select("select groupUniqArray(product) from ( " +
            "  select product, uniqExact(user_id) as c " +
            "  from user_gray.byte_user_model " +
            "  where logday = today() " +
            "    and byte_service = 'withdrawal' " +
            "  group by product " +
            "  having c > 1000 " +
            ")")
    String queryHsWithdrawProduct();

    @Select("select groupUniqArray(user_id)  " +
            "from (  " +
            "         select user_id  " +
            "         from dwd.user_active_dist  " +
            "         where product =  #{product}  " +
            "           and user_id global in (  " +
            "             select user_id  " +
            "             from dwd.withdraw_dist  " +
            "             where logday = today()  " +
            "               and product =  #{product}  " +
            "               and status = 5  " +
            "               and create_time < (toUnixTimestamp(now()) - 60 * 60) * 1000  " +
            "         )  " +
            "           and (channel like 'tt%' or channel like 'ksdr%')  " +
            "         ) r1  " +
            "         left join (  " +
            "    select * from user_gray.byte_user_model where logday = today() and product =  #{product} and byte_service = 'withdrawal'  " +
            "    ) r2 on r1.user_id = r2.user_id  " +
            "where r2.user_id = 0  " +
            "   or r2.user_id is null;")
    String queryHsWithdrawNoCallUser(@Param("product")String product);


    @Select({
            "<script>",
            "select product,groupUniqArray(channel) as exChannel,groupUniqArray(manufacturer) as exManufacturer,ex_type  " +
            " <if test='modelType == 1'>,groupUniqArray(model) as exModel </if>" ,
            " from ( " +
            "      select product, " +
            "             channel, " +
            "             manufacturer, " +
            "             <if test='modelType == 1'>model,</if>" ,
            "             sum(ecpm / 1000)                             as sumIn, " +
            "             sum(if(source_name = '穿山甲', ecpm / 1000, 0)) as csjIn, " +
            "             sum(if(source_name = '广点通', ecpm / 1000, 0)) as gdtIn, " +
            "             uniqExact(userid)                            as uv, " +
            "             count()                                      as pv, " +
            "             avg(ecpm)                                    as rx, " +
            "             if(csjIn / sumIn &gt; #{csjRate},'穿山甲','广点通') as ex_type" +
            "      from ( select product, " +
            "                    os, " +
            "                    channel, " +
            "                    model, " +
            "                    manufacturer, " +
            "                    userid, " +
            "                    ad_id, " +
            "                    device_id, " +
            "                    ad_type, " +
            "                    toFloat64(ecpm) as ecpm " +
            "             from ods.event_exposure_dist a1 global " +
            "                      left join dwd.tb_ap_ad_budget_dist a2 on a1.ad_id = toString(a2.id) " +
            "             where logday = today() " +
            "               and ad_action = 'exposure'  " +
                    "<if test=\"channelStartStr != null and channelStartStr != ''\">and match(channel, #{channelStartStr})</if>" +
            "               and userid global in (select toString(user_id) from dwd.user_active_dist where toDate(create_time) >= today() -1)" +
            "               and ad_type global in (select toString(ad_type) from dwd.ad_type_basic_dist where type_name = '视频') " +
            "               and ad_id global not in (select toString(ad_id) from dwd.tb_ap_bidding_dist where del_flag = 1) " +
            "             union all " +
            "             select product, " +
            "                    os, " +
            "                    channel, " +
            "                    model, " +
            "                    manufacturer, " +
            "                    userid, " +
            "                    ad_id, " +
            "                    device_id, " +
            "                    ad_type, " +
            "                    toFloat64OrZero(extend1) as ecpm " +
            "             from ods.event_exposure_dist " +
            "             where logday = today() " +
            "               and ad_action = 'exposure' " +
                    "<if test=\"channelStartStr != null and channelStartStr != ''\">and match(channel, #{channelStartStr})</if>" +
            "               and userid global in (select toString(user_id) from dwd.user_active_dist where toDate(create_time) >= today() -1)" +
            "               and ad_type global in (select toString(ad_type) from dwd.ad_type_basic_dist where type_name = '视频') " +
            "               and ad_id global in (select toString(ad_id) from dwd.tb_ap_bidding_dist where del_flag = 1) ) as t " +
            "               left join dwd.ad_type_basic_dist t1 on t.ad_type = toString(t1.ad_type) " +
            "      group by product, channel, manufacturer" +
            "<if test='modelType == 1'>,model</if>" ,
            "      having (csjIn / sumIn &gt; #{csjRate} or gdtIn / sumIn &gt; #{csjRate})" +
            "         and uv &gt; #{uv} " +
            "         and pv &gt; #{pv} " +
            "         and rx &gt; #{avgEcpm} " +
            ")group by product,ex_type ",
            "<if test='modelType == 1'>,model</if>" ,
            "</script>"
    })
    List<UserCsjEcpmExEntity> queryExChannel(@Param("csjRate")Double csjRate,
                                                @Param("pv") Integer pv,
                                                @Param("uv") Integer uv,
                                                @Param("avgEcpm") Integer avgEcpm,
                                                @Param("modelType") Integer modelType,
                                                @Param("includeType") Integer includeType,
                                                @Param("channelStartStr") String channelStartStr
                                             );

    @Select({
            "<script>",
            "select product,groupUniqArray(channel) as exChannel,groupUniqArray(manufacturer) as exManufacturer,'广点通' as ex_type  " +
                    " <if test='modelType == 1'>,groupUniqArray(model) as exModel </if>" ,
            " from ( " +
                    "      select product, " +
                    "             channel, " +
                    "             manufacturer, " +
                    "             <if test='modelType == 1'>model,</if>" ,
            "             sum(ecpm / 1000)                             as sumIn, " +
                    "             sum(if(source_name = '广点通', ecpm / 1000, 0)) as gdtIn, " +
                    "             uniqExact(userid)                            as uv, " +
                    "             count()                                      as pv, " +
                    "             avg(ecpm)                                    as rx " +
                    "      from ( select product, " +
                    "                    os, " +
                    "                    channel, " +
                    "                    model, " +
                    "                    manufacturer, " +
                    "                    userid, " +
                    "                    ad_id, " +
                    "                    device_id, " +
                    "                    ad_type, " +
                    "                    toFloat64(ecpm) as ecpm " +
                    "             from ods.event_exposure_dist a1 global " +
                    "                      left join dwd.tb_ap_ad_budget_dist a2 on a1.ad_id = toString(a2.id) " +
                    "             where logday = today() " +
                    "               and ad_action = 'exposure'  " +
                    "               and ad_type global in (select toString(ad_type) from dwd.ad_type_basic_dist where type_name = '视频') " +
                    "               and ad_id global not in (select toString(ad_id) from dwd.tb_ap_bidding_dist where del_flag = 1) " +
                    "             union all " +
                    "             select product, " +
                    "                    os, " +
                    "                    channel, " +
                    "                    model, " +
                    "                    manufacturer, " +
                    "                    userid, " +
                    "                    ad_id, " +
                    "                    device_id, " +
                    "                    ad_type, " +
                    "                    toFloat64OrZero(extend1) as ecpm " +
                    "             from ods.event_exposure_dist " +
                    "             where logday = today() " +
                    "               and ad_action = 'exposure' " +
                    "               and ad_type global in (select toString(ad_type) from dwd.ad_type_basic_dist where type_name = '视频') " +
                    "               and ad_id global in (select toString(ad_id) from dwd.tb_ap_bidding_dist where del_flag = 1) ) as t " +
                    "               left join dwd.ad_type_basic_dist t1 on t.ad_type = toString(t1.ad_type) " +
                    "      group by product, channel, manufacturer" +
                    "<if test='modelType == 1'>,model</if>" ,
            "      having (gdtIn / sumIn &gt; #{csjRate})" +
                    "         and uv &gt; #{uv} " +
                    "         and pv &gt; #{pv} " +
                    "         and rx &gt; #{avgEcpm} " +
            ")group by product ",
            "<if test='modelType == 1'>,model</if>" ,
            "</script>"
    })
    List<UserCsjEcpmExEntity> queryExChannelGdt(@Param("csjRate")Double csjRate,
                                             @Param("pv") Integer pv,
                                             @Param("uv") Integer uv,
                                             @Param("avgEcpm") Integer avgEcpm,
                                             @Param("modelType") Integer modelType
    );


    @Select({
            "<script>",
            "select product, groupUniqArray(userid) userIdStr, groupUniqArray(device_id) as deviceIdStr  " +
                    "from (  " +
                    "      select product,  " +
                    "             userid,  " +
                    "             max(device_id) as device_id,  " +
                    "             count()        as pv,  " +
                    "             avg(ecpm)      as avgEcpm  " +
                    "      from ( select product,  " +
                    "                    os,  " +
                    "                    channel,  " +
                    "                    model,  " +
                    "                    manufacturer,  " +
                    "                    userid,  " +
                    "                    ad_id,  " +
                    "                    device_id,  " +
                    "                    ad_type,  " +
                    "                    toFloat64(ecpm) as ecpm  " +
                    "             from ods.event_exposure_dist a1 global  " +
                    "                      left join dwd.tb_ap_ad_budget_dist a2 on a1.ad_id = toString(a2.id)  " +
                    "             where logday = today()  " +
                    "               and ad_action = 'exposure'  " +
                    "               and product = #{product} " +
                    "<if test='null != channelList and channelList.size() >0'> " +
                                    " and channel in " +
                                    "  <foreach collection='channelList' item='channelT' index='index' open='(' separator=', ' close=')'> " +
                                    "    #{channelT} " +
                                    "  </foreach> " +
                                    "</if>" ,
                    "<if test='null != manuList and manuList.size() >0'> " +
                                    " and manufacturer in " +
                                    "  <foreach collection='manuList' item='man' index='index' open='(' separator=', ' close=')'> " +
                                    "    #{man} " +
                                    "  </foreach> " +
                                    "</if>" ,
                    "<if test='null != modelList and modelList.size() >0'> " +
                                    " and model in " +
                                    "  <foreach collection='modelList' item='modelT' index='index' open='(' separator=', ' close=')'> " +
                                    "    #{modelT} " +
                                    "  </foreach> " +
                    "</if>" ,
                    "               and userid global in  " +
                    "                   (select toString(user_id)  " +
                    "                    from dwd.user_active_dist  " +
                    "                    where toDate(create_time) >= today() - 1)  " +
                    "               and ad_type global in  " +
                    "                   (select toString(ad_type)  " +
                    "                    from dwd.ad_type_basic_dist  " +
                    "                    where type_name = '视频')  " +
                    "               and ad_id global not in  " +
                    "                   (select toString(ad_id)  " +
                    "                    from dwd.tb_ap_bidding_dist  " +
                    "                    where del_flag = 1)  " +
                    "             union all  " +
                    "             select product,  " +
                    "                    os,  " +
                    "                    channel,  " +
                    "                    model,  " +
                    "                    manufacturer,  " +
                    "                    userid,  " +
                    "                    ad_id,  " +
                    "                    device_id,  " +
                    "                    ad_type,  " +
                    "                    toFloat64OrZero(extend1) as ecpm  " +
                    "             from ods.event_exposure_dist  " +
                    "             where logday = today()  " +
                    "               and ad_action = 'exposure'  " +
                    "               and product = #{product}  " +
                    "<if test='null != channelList and channelList.size() >0'> " +
                                    " and channel in " +
                                    "  <foreach collection='channelList' item='channelT' index='index' open='(' separator=', ' close=')'> " +
                                    "    #{channelT} " +
                                    "  </foreach> " +
                    "</if>" ,
                    "<if test='null != manuList and manuList.size() >0'> " +
                                    " and manufacturer in " +
                                    "  <foreach collection='manuList' item='man' index='index' open='(' separator=', ' close=')'> " +
                                    "    #{man} " +
                                    "  </foreach> " +
                    "</if>" ,
                    "<if test='null != modelList and modelList.size() >0'> " +
                                    " and model in " +
                                    "  <foreach collection='modelList' item='modelT' index='index' open='(' separator=', ' close=')'> " +
                                    "    #{modelT} " +
                                    "  </foreach> " +
                    "</if>" ,
                    "               and userid global in  " +
                    "                   (select toString(user_id)  " +
                    "                    from dwd.user_active_dist  " +
                    "                    where toDate(create_time) >= today() - 1)  " +
                    "               and ad_type global in  " +
                    "                   (select toString(ad_type)  " +
                    "                    from dwd.ad_type_basic_dist  " +
                    "                    where type_name = '视频')  " +
                    "               and ad_id global in  " +
                    "                   (select toString(ad_id)  " +
                    "                    from dwd.tb_ap_bidding_dist  " +
                    "                    where del_flag = 1) ) as t  " +
                    "      group by product, userid  " +
                    "      having   " +
                    "<if test='null != actionLimitList and actionLimitList.size() >0'> " +
                    "  <foreach collection='actionLimitList' item='actionLimit' index='index' open='(' separator=' or ' close=')'> " +
                    "    ( pv > #{actionLimit.limitPv} and avgEcpm > #{actionLimit.limitAvgEcpm} ) " +
                    "  </foreach> " +
                    "</if>" ,
                    "         )  " +
                    "group by product",
            "</script>"
    })
    List<UserMacIpCheckEntity> queryExChannelUserAndDevice(@Param("product") String product,
                                                           @Param("actionLimitList") List<RealtimeCsjRulesDto.ActionLimit> actionLimitList,
                                                           @Param("channelList") List<String> channelList,
                                                           @Param("manuList") List<String> manuList,
                                                           @Param("modelList") List<String> modelList
                                                           );

    @Select(" select product, " +
            "       avg(if(toFloat64OrZero(extend1) > 30000, r2.ecpm, toFloat64OrZero(extend1))) " +
            "           as avgEcpm " +
            " from ods.event_exposure_dist r1 " +
            "         left join dwd.tb_ap_ad_budget_dist r2 on r1.ad_id = toString(r2.id) " +
            " where logday = today() " +
            "  and ad_action = 'exposure' " +
            "  and ad_type global in (select toString(ad_type) from dwd.ad_type_basic_dist where type_name = '视频') " +
            "  and match(channel, '^csj|^gdt|^ttzn|^ttzx') = 1 " +
            " group by product")
    List<UserEcpmCheckBean> queryExDrChannelToday();

    @Select(" select product, groupUniqArray(userid) as userIdStr " +
            "from ( " +
            "      select product, " +
            "             userid, " +
            "             count()                              pv, " +
            "             avg(if(toFloat64OrZero(extend1) > 30000, r2.ecpm, " +
            "                    toFloat64OrZero(extend1))) as avgEcpm " +
            "      from ods.event_exposure_dist r1 " +
            "               left join dwd.tb_ap_ad_budget_dist r2 on r1.ad_id = toString(r2.id) " +
            "      where logday = today() and product = #{product}" +
            "        and ad_action = 'exposure' " +
            "           and userid != '0'  " +
            "           and userid != 'null'  " +
            "        and manufacturer in ('HONOR', 'Xiaomi') " +
            "        and ad_type global in (select toString(ad_type) " +
            "                               from dwd.ad_type_basic_dist " +
            "                               where type_name = '视频') " +
            "        and match(channel, '^ttdr|^ksdr') = 1 " +
            "      group by product, userid " +
            "      having pv > 100 " +
            "         and avgEcpm > #{avgEcpm} * 5" +
            "         ) " +
            " group by product;")
    UserMacIpCheckEntity queryExDrUser(@Param("product") String product,@Param("avgEcpm") Double avgEcpm);

    @Select(" select groupUniqArray(ip) as ipStr from (  " +
            "                         select ip, uniqExact(device_id) as dau  " +
            "                         from ods.event_exposure_dist  " +
            "                         where logday >= yesterday()  " +
            "                           and ad_action = 'exposure'  " +
            "                         group by ip  " +
            " having dau > 50)")
    String queryRiskIpList();

    @Select({
            "<script>",
            " select r1.user_id,r1.app_id,sum(r1.pv) third,sum(r2.pv) own from ( " +
            "               select app_id,user_id,ad_id, count() as pv " +
            "               from ods.ad_point_dist " +
            "               where logday = today() " +
            "                 and app_id in " +
            "<if test='null != appIdList and appIdList.size() >0'> " +
            "  <foreach collection='appIdList' item='appId' index='index' open='(' separator=', ' close=')'> " +
            "    #{appId} " +
            "  </foreach> " +
            "</if>" ,
            "                 and dsp = 'guangdiantong' and toHour(call_time) >= toHour(now()) -2 " +
            "                 and uri != '/ap-data-consumer/upload' " +
            "               group by user_id,ad_id,app_id " +
            "                  ) r1 left join ( " +
            "                   select userid,ad_id, count() as pv " +
            "               from ods.event_dist " +
            "               where logday = today()  and hour >= toHour(now()) -2 " +
            "                 and product global in (select product from dwd.app_mapping_dist where id in " +
            "<if test='null != appIdList and appIdList.size() >0'> " +
            "  <foreach collection='appIdList' item='appId' index='index' open='(' separator=', ' close=')'> " +
            "    #{appId} " +
            "  </foreach> " +
            "</if>" ,
            " ) " +
            "                 and ad_action = 'reward' " +
            "               group by userid,ad_id " +
            "    )r2 on r1.user_id = r2.userid and r1.ad_id = r2.ad_id " +
            " group by r1.user_id,r1.app_id " +
            " having own - third > 3",
            "</script>"
    })
    List<UserCallBackBean> queryGdtGap(@Param("appIdList") List<Integer> appIdList);


    @Select({"<script>",
            "select product, " +
            "       os, " +
            "       groupUniqArray(userid)                                                                             as userIdStr, " +
            "       arrayDistinct(arrayFlatten([groupUniqArray(device_id),groupUniqArray(imei),groupUniqArray(oaid)])) as deviceIdStr " +
            "from ( " +
            "      select product, " +
            "             os, " +
            "             userid, " +
            "             max(device_id)                        as device_id, " +
            "             max(oaid)                             as oaid, " +
            "             max(imei)                             as imei, " +
            "             sum(if(ad_action = 'exposure', 1, 0)) as expC, " +
            "             sum(if(ad_action = 'click', 1, 0))    as clickC " +
            "      from ods.event_dist " +
            "      where logday = today() " +
            "        and ad_action in ('exposure', 'click') " +
            "        and ad_type global in (select toString(ad_type) " +
            "                               from dwd.ad_type_basic_dist " +
            "                               where  type_name in " +
            "  <foreach collection='typeList' item='typeName' index='index' open='(' separator=', ' close=')'> " +
            "    #{typeName} " +
            "  </foreach> " +
            ") " +
            "      group by product, os, userid " +
            "      having (expC  &lt; 11 and clickC  &gt; 300) " +
            "          or (expC  &gt; 10 and expC * 30  &lt; clickC) " +
            "         ) " +
            "group by product, os ",
            "</script>"
    })
    List<UserMacIpCheckEntity> queryClickExUser(@Param("typeList") List<String> typeList);


    @Select("select groupUniqArray(user_id)  " +
            "from (  " +
            "      select app_id,  " +
            "             user_id,  " +
            "             os,  " +
            "             toHour(call_time) as hour,  " +
            "             count()           as pv  " +
            "      from ods.ad_point_dist  " +
            "      where logday = today()  " +
            "        and uri = '/ap-data-consumer/upload'  " +
            "        and os = 'android'  " +
            "        and user_id != '0'  " +
            "        and user_id != 'null'  " +
            "      group by app_id, user_id, os, hour  " +
            "      having pv > 400  " +
            "      union all  " +
            "      select app_id,  " +
            "             user_id,  " +
            "             os,  " +
            "             max(toHour(call_time)) as hour,  " +
            "             count()                as pv  " +
            "      from ods.ad_point_dist  " +
            "      where logday = today()  " +
            "        and uri = '/ap-data-consumer/upload'  " +
            "        and os = 'android'  " +
            "        and user_id != '0'  " +
            "        and user_id != 'null'  " +
            "      group by app_id, user_id, os  " +
            "      having pv > 700  " +
            "         )")
    String queryExUserList();

    @Select({
            "<script>",
            " select product, " +
            "       os, " +
            "       groupUniqArray(userid)                                 as userIdStr, " +
            "       arrayDistinct(arrayFlatten(groupUniqArray(deviceAry))) as deviceIdStr " +
            " from ( " +
            "      select product, " +
            "             userid, " +
            "             os, " +
            "             groupArray(pv)                                  as pvAry, " +
            "             arrayDistinct(arrayFlatten(groupArray(flAray))) as deviceAry, " +
            "             arrayFilter(r-> r > 400, pvAry)                 as pvR, " +
            "             arraySum(pvAry)                                 as alPv " +
            "      from ( " +
            "            select product, " +
            "                   userid, " +
            "                   hour, " +
            "                   os, " +
            "                   count()                                    as pv, " +
            "                   groupUniqArray(device_id)                  as deviceAray, " +
            "                   groupUniqArray(oaid)                       as oaidAry, " +
            "                   groupUniqArray(imei)                       as imeiAry, " +
            "                   arrayFlatten([deviceAray,oaidAry,imeiAry]) as flAray " +
            "            from ods.event_exposure_dist " +
            "            where logday = today() " +
            "              and ad_action = 'exposure' " +
            "              and   userid in " +
            "  <foreach collection='userIdList' item='userId' index='index' open='(' separator=', ' close=')'> " +
            "    #{userId} " +
            "  </foreach> " +
            "              and ad_type global in ( " +
            "                select toString(ad_type) from dwd.ad_type_basic_dist where type_name = '视频' " +
            "            ) " +
            "              and os = 'android' " +
            "              and userid != '0' " +
            "              and userid != 'null' " +
            "              and userid != '' " +
            "            group by product, userid, hour, os " +
            "               ) " +
            "      group by product, userid, os " +
            "      having alPv > 700 or length(pvR) > 0 " +
            "         ) " +
            " group by product, os",
            "</script>"
    })
    List<UserMacIpCheckEntity> queryPvExUser(@Param("userIdList") List<String> userIdList);


    @Select(" select product, os, groupUniqArray(userid) as userIdStr, " +
            " arrayDistinct(arrayFlatten([groupUniqArray(device_id),groupUniqArray(imei),groupUniqArray(oaid)])) as deviceIdStr  " +
            " from ods.event_dist  " +
            " where logday = today()  " +
            "  and userid global in (  " +
            "    select toString(user_id)  " +
            "    from (  " +
            "          select user_id, uniqExact(oaid) oa_c, uniqExact(android_id) as ad_c  " +
            "          from dwd.user_active_dist  " +
            "          where channel like '%neilaxin%'  " +
            "            and source in ('INNER_OLD_PULL', 'neilaxin', '自然量')  " +
            "            and toDate(create_time) >= today() - 180  " +
            "          group by user_id  " +
            "          having oa_c > 5  " +
            "              or ad_c > 5  " +
            "             )  " +
            " )and device_id !=''  " +
            " group by product, os")
    List<UserMacIpCheckEntity> queryActiveNlxOldUser();


    @Select("select product,  " +
            "       os,  " +
            "       groupUniqArray(userid)                                                as userIdStr,  " +
            "       arrayDistinct(arrayFlatten(groupUniqArray([deviceAry,imeiAry,oaid]))) as deviceIdStr  " +
            " from (  " +
            "         select ad_id,  " +
            "                product,  " +
            "                os,  " +
            "                userid,  " +
            "                groupUniqArray(device_id)                                                               deviceAry,  " +
            "                groupUniqArray(imei)                                                                 as imeiAry,  " +
            "                groupUniqArray(oaid)                                                                 as oaid,  " +
            "                count()                                                                              as pv,  " +
            "                sum(if(toFloat64OrZero(extend1) > 100000, s2.ecpm, toFloat64OrZero(extend1)) / 1000) as arpu  " +
            "         from ods.event_exposure_dist s1  " +
            "                  left join dwd.tb_ap_ad_budget_dist s2 on s1.ad_id = toString(s2.id)  " +
            "         where logday = today()  " +
            "           and ad_type global in  " +
            "               (select toString(ad_type)  " +
            "                from dwd.ad_type_basic_dist  " +
            "                where source_name = '快手'  " +
            "                  and type_name = '视频')  " +
            "           and ad_action = 'exposure'  " +
            "           and userid != '0'  " +
            "           and userid != 'null'  " +
            "         group by ad_id, product, userid, os  " +
            "         having arpu > 35  " +
            "         )  " +
            " group by product, os")
    List<UserMacIpCheckEntity> queryKuaishouViewExUser();

    @Select(" select id from dwd.app_mapping_dist where product global in ( " +
            "    select product " +
            "    from ( " +
            "          select product, max(logday) as firstDay, min(logday) as lastDay " +
            "          from ( " +
            "                select toDate(create_time) as logday, product, uniqExact(user_id) as tf " +
            "                from dwd.user_active_dist " +
            "                where os = 'ios' " +
            "                  and source in ('toutiao', 'kuaishou', 'guangdiantong') " +
            "                  and logday between toDate(now()) - 30 and toDate(now()) " +
            "                group by logday, product " +
            "                having tf > 100 " +
            "                   ) " +
            "          group by product " +
            "          having firstDay > toDate(now()) - 7 " +
            "             and lastDay < toDate(now()) - 20 " +
            "             ) " +
            " ) and id > 800")
    List<Integer> queryActiveFull();


    @Select(" select product, channel " +
            " from ( " +
            "      select t1.product      as product, " +
            "             t1.channel      as channel, " +
            "             al_amount, " +
            "             withdraw_uc, " +
            "             al_amount / dau as avg_tx, " +
            "             dau " +
            "      from ( select s1.product, " +
            "                    s2.channel, " +
            "                    sum(tx)                 as al_amount, " +
            "                    uniqExact(s1.user_id)   as withdraw_uc, " +
            "                    al_amount / withdraw_uc as avg_tx " +
            "             from ( select product, user_id, sum(amount / 100) as tx " +
            "                    from dwd.withdraw_dist " +
            "                    where logday = today() " +
            "                      and status = 5 " +
            "                    group by product, user_id ) s1 " +
            "                      left join (select product, user_id, channel " +
            "                                 from dwd.user_active_dist " +
            "                                 where user_id global in " +
            "                                       (select user_id " +
            "                                        from dwd.withdraw_dist " +
            "                                        where logday = today() " +
            "                                          and status = 5) " +
            "                                 group by product, user_id, channel ) s2 " +
            "                                on s1.user_id = s2.user_id and s1.product = s2.product " +
            "             group by s1.product, s2.channel " +
            "               ) t1 " +
            "               left join ( select product, channel, uniqExact(device_id) as dau " +
            "                           from dwd.au_device_dist " +
            "                           where logday = today() " +
            "                           group by product, channel ) t2 " +
            "                         on t1.channel = t2.channel and t1.product = t2.product " +
            "      where al_amount > 1500 " +
            "        and t2.dau < 1000 " +
            "      union all " +
            "      select t1.product, " +
            "             t1.channel, " +
            "             al_amount, " +
            "             withdraw_uc, " +
            "             al_amount / dau as avg_tx, " +
            "             dau " +
            "      from ( select s1.product, " +
            "                    s2.channel, " +
            "                    sum(tx)                 as al_amount, " +
            "                    uniqExact(s1.user_id)   as withdraw_uc, " +
            "                    al_amount / withdraw_uc as avg_tx " +
            "             from ( select product, user_id, sum(amount / 100) as tx " +
            "                    from dwd.withdraw_dist " +
            "                    where logday = today() " +
            "                      and status = 5 " +
            "                    group by product, user_id ) s1 " +
            "                      left join (select product, user_id, channel " +
            "                                 from dwd.user_active_dist " +
            "                                 where user_id global in " +
            "                                       (select user_id " +
            "                                        from dwd.withdraw_dist " +
            "                                        where logday = today() " +
            "                                          and status = 5) " +
            "                                 group by product, user_id, channel ) s2 " +
            "                                on s1.user_id = s2.user_id and s1.product = s2.product " +
            "             group by s1.product, s2.channel " +
            "               ) t1 " +
            "               left join ( select product, channel, uniqExact(device_id) as dau " +
            "                           from dwd.au_device_dist " +
            "                           where logday = today() " +
            "                           group by product, channel ) t2 " +
            "                         on t1.channel = t2.channel and t1.product = t2.product " +
            "      where avg_tx > 1.5 " +
            "        and t2.dau > 1000 " +
            "         ) " +
            " group by product, channel ")
    List<WithdrawExChannel> queryWithdrawExChannel();

    @Select(" select product, channel, sum(amounts/100) all_amount, count() num, avg(amounts/100) amount   " +
            "                       from (   " +
            "                             select t1.product, t1.user_id, t2.channel, t1.amounts   " +
            "                             from (   " +
            "                                      select product, user_id, sum(amount) amounts   " +
            "                                      from dwd.withdraw_dist   " +
            "                                      where logday = today() and status = 5" +
            "                                        and (product, user_id) global in   " +
            "                                            (select product, user_id from dwd.user_active_dist where toDate(create_time) = today())   " +
            "                                      group by product, user_id   " +
            "                                      ) t1   " +
            "                                      left join (   " +
            "                                 select * from dwd.user_active_dist where toDate(create_time) = today()   " +
            "                                 ) t2 on t1.product = t2.product and t1.user_id = t2.user_id   " +
            "                                )   " +
            "                       group by product, channel   " +
            "                       having ((num > 18 and amount >4) or  ( num > 5 and amount >8 ))")
    List<WithdrawExChannelAndModel> queryWithdrawBigExChannel();

    @Select(" select product, channel, sum(amounts/100) all_amount, count() num, avg(amounts/100) amount, hour hourStr,groupUniqArray(user_id) userIds  " +
            "                    from (  " +
            "                          select t1.product, t1.user_id, t2.channel, t1.amounts, t2.create_time ,formatDateTime(t2.create_time,'%Y-%m-%d %H') hour  " +
            "                          from (  " +
            "                                   select product, user_id, sum(amount) amounts  " +
            "                                   from dwd.withdraw_dist  " +
            "                                   where logday >=yesterday()  " +
            "                                     and user_id != 0  " +
            "                                     and (product, user_id) global in  " +
            "                                         (select product, user_id from dwd.user_active_dist where os ='android' and toDate(create_time)  >=yesterday())  " +
            "                                   group by product, user_id  " +
            "                                   ) t1  " +
            "                                   left join (  " +
            "                              select * from dwd.user_active_dist where toDate(create_time) >=yesterday()  " +
            "                              ) t2 on t1.product = t2.product and t1.user_id = t2.user_id  " +
            "                             ) group by product,channel,hour having amount > 4 and  num >4")
    List<WithdrawExChannelAndModel> queryWithdrawBigHourExChannel();
    @Select(" select product,    " +
            "       channel,    " +
            "       sum(amounts / 100)      all_amount,    " +
            "       count()                 num,    " +
            "       avg(amounts / 100)      amount,    " +
            "       hour                    hourStr,    " +
            "       groupUniqArray(user_id) userIds    " +
            "from (    " +
            "      select t1.product,    " +
            "             t1.user_id,    " +
            "             t2.channel,    " +
            "             t1.amounts,    " +
            "             t2.create_time,    " +
            "             formatDateTime(t2.create_time, '%Y-%m-%d %H') hour    " +
            "      from (    " +
            "               select product, user_id, sum(amount) amounts    " +
            "               from dwd.withdraw_dist    " +
            "               where logday >= yesterday()    " +
            "                 and user_id != 0    " +
            "                 and (product, user_id) global in    " +
            "                     (select product, user_id    " +
            "                      from dwd.user_active_dist    " +
            "                      where os = 'android'    " +
            "                        and toDate(create_time) >= yesterday()    " +
            "                        and source in    " +
            "                            ('自然量', '自然ALIYUN量', 'yingyongbao', 'xiaomi', 'vivo', 'oppo', 'neilaxin', 'huawei',    " +
            "                             'INNER_OLD_PULL')    " +
            "                        and account_id is null    " +
            "                     )    " +
            "               group by product, user_id    " +
            "               ) t1    " +
            "               left join (    " +
            "          select *    " +
            "          from dwd.user_active_dist    " +
            "          where os = 'android'    " +
            "            and toDate(create_time) >= yesterday()    " +
            "            and source in    " +
            "                ('自然量', '自然ALIYUN量', 'yingyongbao', 'xiaomi', 'vivo', 'oppo', 'neilaxin', 'huawei', 'INNER_OLD_PULL')    " +
            "            and account_id is null    " +
            "          ) t2 on t1.product = t2.product and t1.user_id = t2.user_id    " +
            "         )    " +
            "group by product, channel, hour    " +
            "having amount > 1.5    " +
            "   and num >= 2")
    List<WithdrawExChannelAndModel> queryWithdrawBigHourNatureExChannel();

    @Select("select product,   " +
            "           userid ,   " +
            "           channel,   " +
            "           sum(toFloat64OrZero(extend1) / 1000)        as inc,   " +
            "           sum(if(ad_type global in   " +
            "                  (select toString(ad_type) from dwd.ad_type_basic_dist where source_name = '穿山甲'),   " +
            "                  toFloat64OrZero(extend1) / 1000, 0)) as csj_inc,   " +
            "           csj_inc / inc < 0.1                         as csj_skip   " +
            "    from ods.event_exposure_dist   " +
            "    where logday = today()   " +
            "      and os = 'android'   " +
            "      and (sdk_version like '*******%' or sdk_version like '*******%' or sdk_version like '2.0.5%')   " +
            "      and ad_action = 'exposure'   " +
            "      and channel not like 'bd%'   " +
            "      and channel not like 'cbd%'   " +
            "      and channel not like '%oppo%'   " +
            "      and channel not like '%vivo%'   " +
            "    group by product, userid, channel   " +
            "    having csj_inc / inc < 0.01   " +
            "       and inc > 15")
    List<SkipAdEntity> queryCsjIncomeDelayUser();

    @Select(" select product,    " +
            "       channel,    " +
            "       sum(amounts / 100)      all_amount,    " +
            "       count()                 num,    " +
            "       avg(amounts / 100)      amount,    " +
            "       hour                    hourStr,    " +
            "       groupUniqArray(user_id) userIds    " +
            "from (    " +
            "      select t1.product,    " +
            "             t1.user_id,    " +
            "             t2.channel,    " +
            "             t1.amounts,    " +
            "             t2.create_time,    " +
            "             formatDateTime(t2.create_time, '%Y-%m-%d %H') hour    " +
            "      from (    " +
            "               select product, user_id, sum(amount) amounts    " +
            "               from dwd.withdraw_dist    " +
            "               where logday >= yesterday()    " +
            "                 and user_id != 0    " +
            "                 and (product, user_id) global in    " +
            "                     (select product, user_id    " +
            "                      from dwd.user_active_dist    " +
            "                      where os = 'android'    " +
            "                        and toDate(create_time) >= yesterday()    " +
            "                        and source in    " +
            "                            ('自然量', '自然ALIYUN量', 'yingyongbao', 'xiaomi', 'vivo', 'oppo', 'neilaxin', 'huawei',    " +
            "                             'INNER_OLD_PULL')    " +
            "                        and account_id is null    " +
            "                     )    " +
            "               group by product, user_id    " +
            "               ) t1    " +
            "               left join (    " +
            "          select *    " +
            "          from dwd.user_active_dist    " +
            "          where os = 'android'    " +
            "            and toDate(create_time) >= yesterday()    " +
            "            and source in    " +
            "                ('自然量', '自然ALIYUN量', 'yingyongbao', 'xiaomi', 'vivo', 'oppo', 'neilaxin', 'huawei', 'INNER_OLD_PULL')    " +
            "            and account_id is null    " +
            "          ) t2 on t1.product = t2.product and t1.user_id = t2.user_id    " +
            "         )    " +
            "group by product, channel, hour    " +
            "having amount > 3    " +
            "   and num >= 2")
    List<WithdrawExChannelAndModel> queryWithdrawBigHourStopExChannel();


    @Select(" select product, channel, model, uniqExact(user_id) num  " +
            "            from dwd.user_active_dist  " +
            "            where toDate(create_time) = today()  " +
            "              and source in ('自然量','自然ALIYUN量','yingyongbao','xiaomi','vivo','oppo','neilaxin','huawei','INNER_OLD_PULL')  " +
            "              and account_id is null  " +
            "              and model is not null  " +
            "              and model != ''  " +
            "              and toString(user_id) global not in (  " +
            "                select distinct userid  " +
            "                from ods.event_dist  " +
            "                where logday = today()  " +
            "            )  " +
            "            group by product, os, channel, model  " +
            "            having num > 25")
    List<WithdrawExChannelAndModel> queryWithdrawExChannelModel();

    @Select(" select concat(substring(oaid,1,4),substring(oaid,length(oaid)-3,length(oaid)-1)) as oa,uniqExact(user_id) as uc,groupUniqArray(user_id),product,channel " +
            "from dwd.user_active_dist where toDate(create_time) = today() and oa!='' and oa!='********' " +
            "and source in ( '自然量','自然ALIYUN量','yingyongbao','xiaomi','vivo','oppo','neilaxin','huawei','INNER_OLD_PULL') " +
            "and account_id is null " +
            "group by oa,product,channel " +
            "having uc > 50")
    List<WithdrawExChannelAndModel> queryWithdrawExChannelOa();


    @Select(
            "select product,   " +
                    "                          userid,   " +
                    "                          sum(toFloat64OrZero(extend1) / 1000)        as inc,   " +
                    "                          sum(if(ad_type global in   " +
                    "                                 (select toString(ad_type) from dwd.ad_type_basic_dist where source_name = '快手'),   " +
                    "                                 toFloat64OrZero(extend1) / 1000, 0)) as ks_inc,   " +
                    "                          sum(if(ad_type global in   " +
                    "                                 (select toString(ad_type) from dwd.ad_type_basic_dist where source_name = '广点通'),   " +
                    "                                 toFloat64OrZero(extend1) / 1000, 0)) as gdt_inc,   " +
                    "                          sum(if(ad_type global in   " +
                    "                                 (select toString(ad_type) from dwd.ad_type_basic_dist where source_name = '穿山甲'),   " +
                    "                                 toFloat64OrZero(extend1) / 1000, 0)) as csj_inc,   " +
                    "                          sum(if(ad_type global in   " +
                    "                                 (select toString(ad_type) from dwd.ad_type_basic_dist where source_name = 'VIVO'),   " +
                    "                                 toFloat64OrZero(extend1) / 1000, 0)) as vivo_inc,   " +
                    "                          sum(if(ad_type global in   " +
                    "                                 (select toString(ad_type) from dwd.ad_type_basic_dist where source_name = '华为'),   " +
                    "                                 toFloat64OrZero(extend1) / 1000, 0)) as hw_inc,   " +
                    "                          sum(if(ad_type global in   " +
                    "                                 (select toString(ad_type) from dwd.ad_type_basic_dist where source_name = '阿里'),   " +
                    "                                 toFloat64OrZero(extend1) / 1000, 0)) as ali_inc,   " +
                    "                          sum(if(ad_type global in   " +
                    "                                 (select toString(ad_type) from dwd.ad_type_basic_dist where source_name = '百度'),   " +
                    "                                 toFloat64OrZero(extend1) / 1000, 0)) as bd_inc,   " +
                    "                          sum(if(ad_type global in   " +
                    "                                 (select toString(ad_type) from dwd.ad_type_basic_dist where source_name = 'OPPO'),   " +
                    "                                 toFloat64OrZero(extend1) / 1000, 0)) as oppo_inc,   " +
                    "                          sum(if(ad_type global in   " +
                    "                                 (select toString(ad_type) from dwd.ad_type_basic_dist where source_name = '爱奇艺'),   " +
                    "                                 toFloat64OrZero(extend1) / 1000, 0)) as aqy_inc,   " +
                    "                          ks_inc / inc > 0.90                         as ks_skip,   " +
                    "                          gdt_inc / inc > 0.90                        as gdt_skip,   " +
                    "                          csj_inc / inc > 0.90                        as csj_skip,   " +
                    "                          vivo_inc / inc > 0.90                       as vivo_skip,   " +
                    "                          hw_inc / inc > 0.90                         as hw_skip,   " +
                    "                          ali_inc / inc > 0.90                        as ali_skip,   " +
                    "                          bd_inc / inc > 0.90                         as bd_skip,   " +
                    "                          oppo_inc / inc > 0.90                       as oppo_skip,   " +
                    "                          aqy_inc / inc > 0.90                        as aqy_skip   " +
                    "                   from ods.event_exposure_dist   " +
                    "                   where logday = today()   " +
                    "                     and os = 'android'   " +
                    "                     and (sdk_version like '*******%' or sdk_version like '*******%' or sdk_version like '2.0.5%' )   " +
                    "                     and ad_action = 'exposure'   " +
                    "                     and channel like 'ksdr%'   " +
                    "                   group by product, userid, channel   " +
                    "                   having (ks_inc / inc > 0.90 or gdt_inc / inc > 0.90 or csj_inc / inc > 0.90 or vivo_inc / inc > 0.90 or   " +
                    "                           hw_inc / inc > 0.90 or ali_inc / inc > 0.90 or bd_inc / inc > 0.90 or oppo_inc / inc > 0.90 or   " +
                    "                           aqy_inc / inc > 0.90)   " +
                    "                      and inc > 10"
    )
    List<SkipAdEntity> queryKSDRExCloseAd();

    @Select(
            "select product, os, element_uri,userid, count() as coun,ad_type,if(ad_type like '1015%',1,0) csj_skip, if(ad_type like '1008%',1,0) gdt_skip    " +
                    "                  from ods.event_exposure_dist    " +
                    "                  where logday = today()    " +
                    "                    and ad_action = 'exposure'    " +
                    "                    and os = 'android'    " +
                    "                    and sdk_version >= '*******'    " +
                    "                    and  (ad_type like '1015%'or ad_type like '1008%')    " +
                    "                    and element_uri !=''    " +
                    "                    and element_uri !='null'    " +
                    "                  group by product, os, element_uri,userid,ad_type    " +
                    "                  having coun >3"
    )
    List<SkipAdEntity> queryRepeatExposureExCloseAd();

    @Select(
            "select product,  " +
                    "       userid,  " +
                    "       channel,  " +
                    "       sum(toFloat64OrZero(extend1) / 1000)        as inc,  " +
                    "       sum(if(ad_type global in  " +
                    "              (select toString(ad_type) from dwd.ad_type_basic_dist where source_name = '快手'),  " +
                    "              toFloat64OrZero(extend1) / 1000, 0)) as ks_inc,  " +
                    "       sum(if(ad_type global in  " +
                    "              (select toString(ad_type) from dwd.ad_type_basic_dist where source_name = '广点通'),  " +
                    "              toFloat64OrZero(extend1) / 1000, 0)) as gdt_inc,  " +
                    "       ks_inc / inc > 0.99                         as ks_skip,  " +
                    "       gdt_inc / inc > 0.99                        as gdt_skip  " +
                    "from ods.event_exposure_dist  " +
                    "where logday = today()  " +
                    "  and os = 'android'  " +
                    "  and (sdk_version like '*******%' or sdk_version like '*******%' or sdk_version like '2.0.5%')  " +
                    "  and ad_action = 'exposure'  " +
                    "group by product, userid, channel  " +
                    "having (ks_inc / inc > 0.99 or gdt_inc / inc > 0.99)  " +
                    "   and inc > 10"
    )
    List<SkipAdEntity> queryIncomeSkipExCloseAd();

    @Select(
            "select product,    " +
                    "       os,    " +
                    "       arrayFilter(r->r != '0',groupUniqArray(userid))                                                as userIdStr,    " +
                    "       arrayDistinct(arrayFilter(r->r != 'null' and r != '' ,arrayFlatten(groupUniqArray([deviceAry,imeiAry,oaid])))) as deviceIdStr    " +
                    "from (    " +
                    "      select product,    " +
                    "             os,    " +
                    "             userid,    " +
                    "             channel,    " +
                    "             groupUniqArray(device_id)                      deviceAry,    " +
                    "             groupUniqArray(imei)                        as imeiAry,    " +
                    "             groupUniqArray(oaid)                        as oaid,    " +
                    "             sum(toFloat64OrZero(extend1) / 1000)        as inc,    " +
                    "             sum(if(ad_type global in    " +
                    "                    (select toString(ad_type) from dwd.ad_type_basic_dist where source_name = '快手'),    " +
                    "                    toFloat64OrZero(extend1) / 1000, 0)) as ks_inc,    " +
                    "             sum(if(ad_type global in    " +
                    "                    (select toString(ad_type) from dwd.ad_type_basic_dist where source_name = '广点通'),    " +
                    "                    toFloat64OrZero(extend1) / 1000, 0)) as gdt_inc,    " +
                    "             ks_inc / inc > 0.99                         as ks_skip,    " +
                    "             gdt_inc / inc > 0.99                        as gdt_skip    " +
                    "      from ods.event_exposure_dist    " +
                    "      where logday = today()    " +
                    "        and os = 'android'    " +
                    "        and (sdk_version like '*******%' or sdk_version like '*******%' or sdk_version like '2.0.5%')    " +
                    "        and ad_action = 'exposure'    " +
                    "      group by product, os, userid, channel    " +
                    "      having (ks_inc / inc > 0.99 or gdt_inc / inc > 0.99)    " +
                    "         and inc > 20    " +
                    "         )    " +
                    "group by product, os"
    )
    List<UserMacIpCheckEntity> queryIncomeBlackExCloseAd();

    @Select({
            "<script>",
            "select product,  " +
                    "       os,  " +
                    "       arrayFilter(r->r != '0', groupUniqArray(userid))                                   as userIdStr,  " +
                    "       arrayDistinct(arrayFilter(r->r != 'null' and r != '',  " +
                    "                                 arrayFlatten(groupUniqArray([deviceAry,imeiAry,oaid])))) as deviceIdStr  " +
                    "from (  " +
                    "      select product,  " +
                    "             os,  " +
                    "             userid,  " +
                    "             uniqExact(channel)        pv,  " +
                    "             groupUniqArray(device_id) deviceAry,  " +
                    "             groupUniqArray(imei) as   imeiAry,  " +
                    "             groupUniqArray(oaid) as   oaid  " +
                    "      from ods.event_dist  " +
                    "      where logday = today()  " +
                    "      and product in  " +
                    "  <foreach collection='products' item='product' index='index' open='(' separator=', ' close=')'> " +
                    "    #{product} " +
                    "  </foreach> " +
                    "        and userid &gt;= '1'  " +
                    "        and userid != 'null'  " +
                    "        and userid != ''  " +
                    "        and os = 'android'  " +
                    "      group by product, os, userid  " +
                    "      having pv > 4  " +
                    "         )  " +
                    "group by product, os",
            "</script>"
    })
    List<UserMacIpCheckEntity> queryManyChannelBlackExCloseAd(@Param("products") List<String> products);

    @Select({
            "<script>",
            "select product,  " +
                    "       os,  " +
                    "       arrayFilter(r->r != '0', groupUniqArray(userid))                                   as userIdStr,  " +
                    "       arrayDistinct(arrayFilter(r->r != 'null' and r != '',  " +
                    "                                 arrayFlatten(groupUniqArray([deviceAry,imeiAry,oaid])))) as deviceIdStr  " +
                    "from (  " +
                    "      select product,  " +
                    "             os,  " +
                    "             userid,  " +
                    "             uniqExact(model)          pv,  " +
                    "             groupUniqArray(device_id) deviceAry,  " +
                    "             groupUniqArray(imei) as   imeiAry,  " +
                    "             groupUniqArray(oaid) as   oaid  " +
                    "      from ods.event_dist  " +
                    "      where logday = today()  " +
                    "      and product in  " +
                    "  <foreach collection='products' item='product' index='index' open='(' separator=', ' close=')'> " +
                    "    #{product} " +
                    "  </foreach> " +
                    "        and userid &gt;= '1'  " +
                    "        and userid != 'null'  " +
                    "        and userid != ''  " +
                    "        and os = 'android'  " +
                    "      group by product, os, userid  " +
                    "      having pv > 4  " +
                    "         )  " +
                    "group by product, os",
            "</script>"
    }
    )
    List<UserMacIpCheckEntity> queryManyModelBlackExCloseAd(@Param("products") List<String> products);

    @Select(
            "select product,   " +
                    "                           os,   " +
                    "                            arrayFilter(r->r != '0',groupUniqArray(user_id))                                      as userIdStr,   " +
                    "                           arrayDistinct(arrayFilter(r->r != 'null' and r != '' ,arrayFlatten(groupUniqArray([imeiAry,oaid])))) as deviceIdStr   " +
                    "                    from (   " +
                    "                          select product,   " +
                    "                                 os,   " +
                    "                                 user_id,   " +
                    "                                 groupUniqArray(imei) as imeiAry,   " +
                    "                                 groupUniqArray(oaid) as oaid   " +
                    "   " +
                    "                          from dwd.user_active_dist   " +
                    "                          where   (product, user_id) global in (   " +
                    "                              select product, toInt64(userid)   " +
                    "                              from (   " +
                    "                                    select logday,   " +
                    "                                           count(1),   " +
                    "                                           avg(toFloat64OrZero(extend1)) ecpm,   " +
                    "                                           product,   " +
                    "                                           channel,   " +
                    "                                           userid,   " +
                    "                                           device_id,   " +
                    "                                           ad_type   " +
                    "                                    from ods.event_exposure_dist   " +
                    "                                    where model = 'M2007J1SC'   " +
                    "                                      and logday = today()   " +
                    "                                      and userid !=''   " +
                    "                                      and userid !='null'   " +
                    "                                      and ad_action = 'exposure'   " +
                    "                                    group by logday, product, channel, userid, device_id, ad_type   " +
                    "                                    having ecpm > 800   " +
                    "                                       )   " +
                    "                          ) group by product, os, user_id   " +
                    "                             )   " +
                    "                    group by product, os"
    )
    List<UserMacIpCheckEntity> queryExChannelBlackExCloseAd();

    @Select("select product,userid from (  " +
            "               select product, userid, ad_id, count() as pv  " +
            "               from ods.event_exposure_dist  " +
            "               where logday = today()  " +
            "                 and ad_action = 'exposure'  " +
            "                 and ad_type global in (  " +
            "                   select toString(ad_type) from dwd.ad_type_basic_dist where type_name = '视频' and source_name = '华为'  " +
            "               )  " +
            "               group by product, userid, ad_id  " +
            "               having pv > 10  " +
            "                  )  " +
            "group by product,userid")
    List<SkipAdEntity> queryHuaWeiExCloseAd();

    @Select("select product,os,userid,formatDateTime(time,'%Y-%m-%d %H:%M') minu,count() count from ods.event_exposure_dist    " +
            "where logday =today()    " +
            "                    and ad_action = 'exposure'    " +
            "                    and sdk_version >= '*******'    " +
            "                    and ad_type like '1008%'    " +
            "group by product, os, userid, minu having count >15"
    )
    List<SkipAdEntity> queryGdtExCloseAd();

    @Select("select distinct product, os, userid, channel  " +
            "    from (  " +
            "             select product, os, userid, channel, avg(toFloat64OrZero(extend1)) ecpm, count() count  " +
            "             from ods.event_exposure_dist  " +
            "             where (sdk_version like '*******%' or sdk_version like '*******%' or  " +
            "                    sdk_version like '2.0.5%')  " +
            "               and logday = today()  " +
            "               and ad_type like '1062%'  " +
            "               and ad_action = 'exposure'  " +
            "               and (channel like 'ksdr%' or channel like 'update%')  " +
            "             group by product, os, userid, channel  " +
            "             having ((ecpm >= 1000 and count >= 5) or (ecpm >= 2000 and count >= 2))  " +
            "             union all  " +
            "             select product, os, userid, channel, avg(toFloat64OrZero(extend1)) ecpm, count() count  " +
            "             from ods.event_exposure_dist  " +
            "             where (sdk_version like '*******%' or sdk_version like '*******%' or  " +
            "                    sdk_version like '2.0.5%')  " +
            "               and logday = today()  " +
            "               and ad_type like '1084%'  " +
            "               and ad_action = 'exposure'  " +
            "               and (channel like 'ksdr%' or channel like 'update%')  " +
            "             group by product, os, userid, channel  " +
            "             having ((ecpm >= 1000 and count >= 5) or (ecpm > 2000 and count >= 2))  " +
            "             )"
    )
    List<SkipAdEntity> queryGdtExAdTypeCloseAd();

    @Select(
            "select product, " +
                    "       os, " +
                    "       groupUniqArray(userid)                                                as userIdStr, " +
                    "       arrayDistinct(arrayFlatten(groupUniqArray([deviceAry,imeiAry,oaid]))) as deviceIdStr " +
                    "from ( " +
                    "         select product, " +
                    "                os, " +
                    "                userid, " +
                    "                groupUniqArray(device_id)                      deviceAry, " +
                    "                groupUniqArray(imei)                        as imeiAry, " +
                    "                groupUniqArray(oaid)                        as oaid, " +
                    "                sum(toFloat64OrZero(extend1) / 1000)        as inc, " +
                    "                sum(if(ad_type global in " +
                    "                       (select toString(ad_type) from dwd.ad_type_basic_dist where type_name = '全屏视频'), " +
                    "                       toFloat64OrZero(extend1) / 1000, 0)) as qp_inc " +
                    "         from ods.event_exposure_dist " +
                    "         where logday = today() " +
                    "           and ad_action = 'exposure' " +
                    "         group by product, os, userid " +
                    "         having qp_inc / inc > 0.99 " +
                    "            and inc > 15 " +
                    "         ) " +
                    "group by product, os "
    )
    List<UserMacIpCheckEntity> queryQpVideoExUser();
    @Select(
            "select product,      " +
                    "       os,      " +
                    "       groupUniqArray(userid)                                      as userIdStr,      " +
                    "       arrayDistinct(arrayFlatten(groupUniqArray([imeiAry,oaid]))) as deviceIdStr      " +
                    "from (      " +
                    "      select product,      " +
                    "             os,      " +
                    "             user_id              as userid,      " +
                    "             groupUniqArray(imei) as imeiAry,      " +
                    "             groupUniqArray(oaid) as oaid      " +
                    "      from dwd.user_active_dist      " +
                    "      where (product, os, user_id) global in (      " +
                    "          select product, os, toInt64(userid)      " +
                    "          from (      " +
                    "                select product,      " +
                    "                       os,      " +
                    "                       userid,      " +
                    "                       sum(toFloat64OrZero(extend1) / 1000)        as inc,      " +
                    "                       sum(if(ad_type global in      " +
                    "                              (select toString(ad_type) from dwd.ad_type_basic_dist where type_name = '全屏视频'),      " +
                    "                              toFloat64OrZero(extend1) / 1000, 0)) as qp_inc      " +
                    "                from ods.event_exposure_dist      " +
                    "                where logday = today()      " +
                    "                  and ad_action = 'exposure'      " +
                    "                group by product, os, userid      " +
                    "                having qp_inc / inc > 0.90      " +
                    "                   and inc > 4      " +
                    "                   )      " +
                    "      )      " +
                    "        and toDate(create_time) >= today() - 7      " +
                    "        and source in ('自然量', 'neilaxin', 'INNER_OLD_PULL')      " +
                    "      group by product, os, user_id      " +
                    "         )      " +
                    "group by product, os"
    )
    List<UserMacIpCheckEntity> queryZiRanQpVideoExUser();

    @Select("<script>select product, " +
            "       os," +
            "       groupUniqArray(userid)                                                as userIdStr, " +
            "       arrayDistinct(arrayFlatten(groupUniqArray([deviceAry,imeiAry,oaid]))) as deviceIdStr " +
            " from (" +
            "         select product, os, userid," +
            "                groupUniqArray(device_id)                      deviceAry," +
            "                groupUniqArray(imei)                        as imeiAry," +
            "                groupUniqArray(oaid)                        as oaid," +
            "                sum(toFloat64OrZero(extend1) / 1000)        as inc," +
            "                sum(if(ad_type global in " +
            "                       (select toString(ad_type) from dwd.ad_type_basic_dist where type_name = '全屏视频')," +
            "                       toFloat64OrZero(extend1) / 1000, 0)) as qp_inc " +
            "         from ods.event_exposure_dist " +
            "         where logday = today() " +
            "           and ad_action = 'exposure'" +
            "        and (product, userid) global in (" +
            "             select product, toString(user_id) " +
            "                         from dwd.withdraw_dist " +
            "                         where logday = today() " +
            "                           and product in " +
            "<foreach collection='products' item='item' open='(' separator=',' close=')'>#{item}</foreach>" +
            "                           and status = 5 " +
            "                           and create_time >= toUnixTimestamp(toStartOfDay(now())) * 1000 group by product, user_id having sum(amount) > 500) " +
            "         group by product, os, userid " +
            "         having qp_inc / inc > 0.75 " +
            "         )" +
            "group by product, os" +
            "</script>")
    List<UserMacIpCheckEntity> queryTxQpVideoExUser(@Param("products") List<String> products);

    @Select(
            "select product,     " +
                    "                         os,     " +
                    "                         groupUniqArray(userid)                                                as userIdStr,     " +
                    "                         arrayDistinct(arrayFlatten(groupUniqArray([deviceAry,imeiAry,oaid]))) as deviceIdStr     " +
                    "                    from (     " +
                    "                        select product,     " +
                    "                               os,     " +
                    "                               userid,     " +
                    "                               groupUniqArray(device_id)                      deviceAry,     " +
                    "                               groupUniqArray(imei)                        as imeiAry,     " +
                    "                               groupUniqArray(oaid)                        as oaid,     " +
                    "                               sum(toFloat64OrZero(extend1) / 1000)        as inc,     " +
                    "                               sum(if(ad_type global in     " +
                    "                                      (select toString(ad_type) from dwd.ad_type_basic_dist where source_name = '快手'),     " +
                    "                                      toFloat64OrZero(extend1) / 1000, 0)) as ks_inc,     " +
                    "                               sum(if(ad_type global in     " +
                    "                                      (select toString(ad_type) from dwd.ad_type_basic_dist where source_name = '广点通'),     " +
                    "                                      toFloat64OrZero(extend1) / 1000, 0)) as gdt_inc,     " +
                    "                               sum(if(ad_type global in     " +
                    "                                      (select toString(ad_type) from dwd.ad_type_basic_dist where source_name = '穿山甲'),     " +
                    "                                      toFloat64OrZero(extend1) / 1000, 0)) as csj_inc,     " +
                    "                               sum(if(ad_type global in     " +
                    "                                      (select toString(ad_type) from dwd.ad_type_basic_dist where source_name = 'VIVO'),     " +
                    "                                      toFloat64OrZero(extend1) / 1000, 0)) as vivo_inc,     " +
                    "                               sum(if(ad_type global in     " +
                    "                                      (select toString(ad_type) from dwd.ad_type_basic_dist where source_name = '华为'),     " +
                    "                                      toFloat64OrZero(extend1) / 1000, 0)) as hw_inc,     " +
                    "                               sum(if(ad_type global in     " +
                    "                                      (select toString(ad_type) from dwd.ad_type_basic_dist where source_name = '阿里'),     " +
                    "                                      toFloat64OrZero(extend1) / 1000, 0)) as ali_inc,     " +
                    "                               sum(if(ad_type global in     " +
                    "                                      (select toString(ad_type) from dwd.ad_type_basic_dist where source_name = '百度'),     " +
                    "                                      toFloat64OrZero(extend1) / 1000, 0)) as bd_inc,     " +
                    "                               sum(if(ad_type global in     " +
                    "                                      (select toString(ad_type) from dwd.ad_type_basic_dist where source_name = 'OPPO'),     " +
                    "                                      toFloat64OrZero(extend1) / 1000, 0)) as oppo_inc,     " +
                    "                               sum(if(ad_type global in     " +
                    "                                      (select toString(ad_type) from dwd.ad_type_basic_dist where source_name = '爱奇艺'),     " +
                    "                                      toFloat64OrZero(extend1) / 1000, 0)) as aqy_inc     " +
                    "                        from ods.event_exposure_dist     " +
                    "                        where logday = today()     " +
                    "                          and os = 'android'     " +
                    "                          and (sdk_version like '*******%' or sdk_version like '*******%' or sdk_version like '2.0.5%' )     " +
                    "                          and ad_action = 'exposure'     " +
                    "                          and channel like 'ksdr%'     " +
                    "                        group by product, os, userid     " +
                    "                        having (ks_inc / inc > 0.90 or gdt_inc / inc > 0.90 or csj_inc / inc > 0.90 or vivo_inc / inc > 0.90 or     " +
                    "                                hw_inc / inc > 0.90     " +
                    "                            or ali_inc / inc > 0.90 or bd_inc / inc > 0.90 or oppo_inc / inc > 0.90 or aqy_inc / inc > 0.90)     " +
                    "                           and inc > 35     " +
                    "                           )     " +
                    "                    group by product, os"
    )
    List<UserMacIpCheckEntity> queryKSDRGdtKsExCloseAd();

    @Select({
            "<script>",
            "select product, " +
            "       os, " +
            "       arrayFilter(r->r != '0', groupUniqArray(userid))                                                               as userIdStr, " +
            "       arrayDistinct(arrayFilter(r->r != 'null' and r != '' ," +
            "                                 arrayFlatten(groupUniqArray([deviceAry,imeiAry,oaid]))))                             as deviceIdStr " +
            "from ( " +
            "      select product, " +
            "             os, " +
            "             userid, " +
            "             groupUniqArray(device_id) deviceAry, " +
            "             groupUniqArray(imei) as   imeiAry, " +
            "             groupUniqArray(oaid) as   oaid " +
            " " +
            "      from ods.event_dist" +
            "      where logday = today() " +
            "        and (channel, manufacturer) global in ( " +
            "          select channel, manufacturer " +
            "          from ( " +
            "                select channel, manufacturer, uniqExact(device_id) as c " +
            "                from ods.event_dist " +
            "                where logday = today() " +
            "                  and channel in  " +
            "  <foreach collection='channelList' item='channel' index='index' open='(' separator=', ' close=')'> " +
            "    #{channel} " +
            "  </foreach> " +
            "                  and manufacturer not in " +
            "                      ('vivo', 'OPPO', 'Apple', 'HUAWEI', 'HONOR', 'Xiaomi', 'VIVO', 'XIAOMI', " +
            "                       'realme', 'OnePlus', 'samsung', " +
            "                       'REALME', 'PTAC', 'CMDC', 'Tianyi', 'PTAC', 'CMDC', 'Tianyi', 'SAMSUNG', " +
            "                       'Anica', 'Realme', 'TIANYI', " +
            "                       'xiaolajiao', '360', 'Redmi', 'ivvi', 'OWWO', 'MEIZU', 'koobee', 'Sony', " +
            "                       'koobee', 'Sony', 'Google', 'Xioami', 'meizu', 'Samsung') " +
            "                group by channel, manufacturer " +
            "                having c &lt; 10 " +
            "                   ) " +
            "      ) " +
            "      group by product, os, userid " +
            "         ) " +
            "group by product, os",
            "</script>"
    })
    List<UserMacIpCheckEntity> queryManuExCloseAd(@Param("channelList") List<String> channelList);

    @Select(
            "select product, " +
                    "       os, " +
                    "       arrayFilter(r->r != '0', groupUniqArray(userid))                                   as userIdStr, " +
                    "       arrayDistinct(arrayFilter(r->r != 'null' and r != '', " +
                    "                                 arrayFlatten(groupUniqArray([deviceAry,imeiAry,oaid])))) as deviceIdStr " +
                    "from ( " +
                    "      select product, " +
                    "             os, " +
                    "             user_id                 userid, " +
                    "             groupUniqArray(imei)    deviceAry, " +
                    "             groupUniqArray(imei) as imeiAry, " +
                    "             groupUniqArray(oaid) as oaid " +
                    "      from dwd.user_active_dist " +
                    "      where toDate(create_time) >= yesterday() " +
                    "        and os = 'android' " +
                    "        and match(replaceAll(model, ' ', ''), '^[a-zA-Z0-9_.,+\\-()]+$') = 0 " +
                    "      group by product, os, userid " +
                    "         ) " +
                    "group by product, os"
    )
    List<UserMacIpCheckEntity> queryLuanModelExCloseAd();

    @Select(
            "select product,  " +
                    "       os,  " +
                    "       arrayFilter(r->r != '0', groupUniqArray(userid))                                   as userIdStr,  " +
                    "       arrayDistinct(arrayFilter(r->r != 'null' and r != '',  " +
                    "                                 arrayFlatten(groupUniqArray([deviceAry,imeiAry,oaid])))) as deviceIdStr  " +
                    "from (  " +
                    "      select product,  " +
                    "             os,  " +
                    "             userid                  userid,  " +
                    "             groupUniqArray(imei)    deviceAry,  " +
                    "             groupUniqArray(imei) as imeiAry,  " +
                    "             groupUniqArray(oaid) as oaid  " +
                    "  " +
                    "      from ods.event_exposure_dist  " +
                    "      where logday = today()  " +
                    "        and sdk_version >= '*******'  " +
                    "        and os = 'android'  " +
                    "        and ad_action = 'exposure'  " +
                    "        and (ad_type like '1015%' or ad_type like '1008%')  " +
                    "        and match(replaceAll(element_uri, ' ', ''), '^[0-9]*$') = 1  " +
                    "      group by product, os, userid  " +
                    "         )  " +
                    "group by product, os"
    )
    List<UserMacIpCheckEntity> queryChangeReqIdCloseAd();

    @Select("select product,\n" +
            "                                 os,\n" +
            "                                 userid                  userIdStr,\n" +
            "                                 arrayDistinct(arrayFilter(r->r != 'null' and r != '',\n" +
            "                                                     arrayFlatten(groupUniqArray([device_id, imei, oaid])))) as deviceStr,\n" +
            "                                 arrayDistinct(arrayFilter(r->r != 'null' and r != '',\n" +
            "                                                     arrayFlatten(groupUniqArray([element_uri])))) as extend1\n" +
            "\n" +
            "                          from ods.event_exposure_dist\n" +
            "                          where logday = today()\n" +
            "                            and sdk_version >= '*******'\n" +
            "                            and os = 'android'\n" +
            "                            and ad_action = 'exposure'\n" +
            "                            and (ad_type like '1015%' or ad_type like '1008%')\n" +
            "                            and match(replaceAll(element_uri, ' ', ''), '^[0-9]*$') = 1\n" +
            "\n" +
            "                          group by product, os, userid")
    List<UserMacIpCheckEntity> queryChangeReqId();

    @Select(
            "select product, " +
                    "       os, " +
                    "       arrayFilter(r->r != '0', groupUniqArray(userid))                                   as userIdStr, " +
                    "       arrayDistinct(arrayFilter(r->r != 'null' and r != '', " +
                    "                                 arrayFlatten(groupUniqArray([deviceAry,imeiAry,oaid])))) as deviceIdStr " +
                    "from ( " +
                    "      select product, " +
                    "             os, " +
                    "             user_id                 userid, " +
                    "             groupUniqArray(imei)    deviceAry, " +
                    "             groupUniqArray(imei) as imeiAry, " +
                    "             groupUniqArray(oaid) as oaid " +
                    "      from dwd.user_active_dist " +
                    "      where toDate(create_time) >= yesterday() " +
                    "        and os = 'android' " +
                    "        and match(replaceAll(model, ' ', ''), '^[a-zA-Z0-9_.,+\\-()]+$') = 0 " +
                    "      group by product, os, userid " +
                    "         ) " +
                    "group by product, os"
    )
    List<UserMacIpCheckEntity> queryAliYunExCloseAd();

    @Select({

            " select * from dwd.user_active_dist where product = #{product} and channel = #{channel} " +
                    " and create_time  > " +
                    "    #{beginTime} " +
                    " and create_time  < " +
                    "    #{endTime} "
    })
    List<UserActive> queryActiveUserByChannel(@Param("product")String product,
                                              @Param("channel")String channel,
                                              @Param("beginTime") Date beginTime,
                                              @Param("endTime")Date endTime);

    @Select(
            "select  manufacturer,model,uniqExact(userid) userCount from ods.event_dist where os ='android' group by manufacturer,model order by  userCount"
    )
    List<UserGrayChannelEntity> queryManufacturerModelList();


    @Select(" select a.*,p.remark,p.app_id,p.app_os from (" +
            " select product,userid from ods.event_dist" +
            " where logday = today()" +
            " and (oaid = '05ecd2b9-7550-48f6-8bf9-1c428aea594f'" +
            " or oaid = '1DB8027C1AE647199FBD9FAC46ED0CAD32bd132ebd5ea10c68a24f52d60e8410')" +
            " group by product,userid) a" +
            " join dwd.product p on a.product = p.name")
    List<Map<String,String>> selectTestAccount();

    @Select("select product,\n" +
            "                   arrayFilter(r->r != '0', groupUniqArray(userid))                                   as userIdStr,\n" +
            "                   arrayDistinct(arrayFilter(r->r != 'null' and r != '',\n" +
            "                                             arrayFlatten(groupUniqArray([deviceAry,imeiAry,oaid])))) as deviceIdStr\n" +
            "            from (\n" +
            "                     select product,\n" +
            "                            userid,\n" +
            "                            groupUniqArray(device_id)                      deviceAry,\n" +
            "                            groupUniqArray(imei)                        as imeiAry,\n" +
            "                            groupUniqArray(oaid)                        as oaid,\n" +
            "                            count(1)                                    as pv,\n" +
            "                            sum(toFloat64OrZero(extend1) / 1000)        as inc,\n" +
            "                            sum(if(ad_type global in\n" +
            "                                   (select toString(ad_type) from dwd.ad_type_basic_dist where source_name = '百度'),\n" +
            "                                   toFloat64OrZero(extend1) / 1000, 0)) as bd_inc\n" +
            "                     from ods.event_exposure_dist\n" +
            "                     where logday = today()\n" +
            "                       and os = 'ios'\n" +
            "                       and channel = 'AppStore'\n" +
            "                       and concat(product, device_id, os) global in (select concat(product, device_id, os)\n" +
            "                                                                            from dwd.product_ocpc_activate_dist\n" +
            "                                                                            where os = 'ios'\n" +
            "                                                                              and ocpc_channel = '自然量'\n" +
            "                                                                              and logday between today() - 180 and today()\n" +
            "                                                                            group by product, device_id, os)\n" +
            "                       and ad_action = 'exposure'\n" +
            "                     group by product, userid, device_id\n" +
            "                     having ((pv > 10 or inc > 10) and bd_inc / inc > 0.90)\n" +
            "                     )\n" +
            "            group by product, userid")
    List<UserMacIpCheckEntity> queryIosNaturalUserInfo();

    @Select("select product,\n" +
            "       userid                                                                             as userIdStr,\n" +
            "       arrayDistinct(arrayFilter(r->r != 'null' and r != '',\n" +
            "                                 arrayFlatten(groupUniqArray([deviceAry,imeiAry,oaid])))) as deviceIdStr\n" +
            "from (\n" +
            "         select product\n" +
            "              , userid\n" +
            "              , device_id deviceAry\n" +
            "              , imei imeiAry\n" +
            "              , oaid\n" +
            "              , sum(toFloat64OrZero(extend1) / 1000)                                    income\n" +
            "              , max(if(logday = today(), toFloat64OrZero(extend1), 0))                  maxecpm\n" +
            "              , sum(if(ad_type_name like '%百度%', toFloat64OrZero(extend1) / 1000, 0)) bdincome\n" +
            "              , count(if(logday = today(), 1, null))                                    pv\n" +
            "         from ods.event_exposure_dist ed\n" +
            "                  global\n" +
            "                  left join (\n" +
            "             select distinct pos_id, ad_type_name from dwd.product_ad_conf_dist\n" +
            "             ) b\n" +
            "                            on ed.pos_id = b.pos_id\n" +
            "         where device_id global in (select device_id\n" +
            "                                    from dwd.device_dist\n" +
            "                                    where logday >= today() - 30\n" +
            "                                      and os = 'ios')\n" +
            "           and ad_action = 'exposure'\n" +
            "           and os = 'ios'\n" +
            "           and logday >= today() - 2\n" +
            "         group by product, userid, device_id, imei, oaid\n" +
            "         having (maxecpm > 500 or pv > 10)\n" +
            "            and bdincome / income > 0.9)\n" +
            "group by product, userid")
    List<UserMacIpCheckEntity> queryIosIncomeAbnormalUserInfo();

    @Select("select product, os, arrayFilter(r->r != '0', groupUniqArray(userid))                                   as userIdStr, " +
            "arrayDistinct(arrayFilter(r->r != 'null' and r != '',\n" +
            "                                 arrayFlatten(groupUniqArray([device_id,imei,oaid])))) as deviceIdStr from (\n" +
            "select element_uri, product, os, userid, device_id, oaid, imei, count(element_uri)\n" +
            "from ods.event_exposure_dist\n" +
            "where logday = today()\n" +
            "  and ad_action = 'exposure'\n" +
            "  and element_uri global in (select element_uri\n" +
            "                             from (\n" +
            "                                      select element_uri, product\n" +
            "                                      from ods.event_exposure_dist\n" +
            "                                      where logday = today()\n" +
            "                                        and ad_action = 'exposure'\n" +
            "                                        and element_uri != ''\n" +
            "                                        and element_uri is not null\n" +
            "                                        and element_uri != '(null)'\n" +
            "                                        and length(element_uri) > 6\n" +
            "                                      group by element_uri, product\n" +
            "                                      having count(element_uri) > 1)\n" +
            "                             group by element_uri\n" +
            "                             having count(element_uri) > 1)\n" +
            "group by element_uri, product, os, userid, device_id, imei, oaid\n" +
            "order by element_uri)\n" +
            "group by userid, product, os")
    List<UserMacIpCheckEntity> queryReqIdAbnormalUserList();

    @Select("select product\n" +
            "     , userid as                                                               userIdStr\n" +
            "     , sum(toFloat64OrZero(extend1) / 1000)                                    income\n" +
            "     , sum(if(ad_type_name like '%百度%', toFloat64OrZero(extend1) / 1000, 0)) bdincome\n" +
            "     , count(1)                                                                pv\n" +
            "from ods.event_exposure_dist ed\n" +
            "         global\n" +
            "         left join (\n" +
            "    select distinct pos_id, ad_type_name from dwd.product_ad_conf_dist\n" +
            "    ) b\n" +
            "                   on ed.pos_id = b.pos_id\n" +
            "where device_id global in (select device_id\n" +
            "                           from dwd.device_dist\n" +
            "                           where logday between today() - 4 and today()\n" +
            "                             and lower(manufacturer) like '%mi%')\n" +
            "  and ad_action = 'exposure'\n" +
            "  and logday between yesterday() and today()\n" +
            "group by product, userid\n" +
            "having (income > 25 or pv > 100)\n" +
            "   and bdincome / income > 0.95\n")
    List<UserMacIpCheckEntity> queryAndroidBaiduIncomeAbnormalUserInfo();

    @Select("select product\n" +
            "                 , userid as                                                               userIdStr\n" +
            "                 , sum(toFloat64OrZero(extend1) / 1000)                                    income\n" +
            "                 , sum(if(ad_type_name like '%百度%', toFloat64OrZero(extend1) / 1000, 0)) bdincome\n" +
            "                 , count(1)                                                                pv\n" +
            "            from ods.event_exposure_dist ed\n" +
            "                     global\n" +
            "                     left join (\n" +
            "                select distinct pos_id, ad_type_name from dwd.product_ad_conf_dist\n" +
            "                ) b\n" +
            "                               on ed.pos_id = b.pos_id\n" +
            "            where device_id global in (select device_id\n" +
            "                                       from dwd.device_dist\n" +
            "                                       where logday between today() - 4 and today()\n" +
            "                                         and lower(manufacturer) like '%mi%')\n" +
            "              and ad_action = 'exposure'\n" +
            "              and logday between yesterday() and today()\n" +
            "            group by product, userid\n" +
            "            having (income > 30 or pv > 300)\n" +
            "               and bdincome / income > 0.97")
    List<UserMacIpCheckEntity> queryBaiduIncomeAbnormalGroup5UserInfo();

    @Select("select product\n" +
            "     , userid                                         userIdStr\n" +
            "     , device_id\n" +
            "     , sum(if(type_name = '视频', pv1, 0))         as pv\n" +
            "     , sum(inc)                                    as income\n" +
            "     , sum(if(ad_type_name like '%百度%', inc, 0)) as bdincome\n" +
            "     , bdincome / income                           as bd_pp\n" +
            "     , manufacturer\n" +
            "from (\n" +
            "         select product,\n" +
            "                userid,\n" +
            "                device_id,\n" +
            "                pos_id,\n" +
            "                count(1)                             as pv1,\n" +
            "                sum(toFloat64OrZero(extend1) / 1000) as inc,\n" +
            "                manufacturer\n" +
            "         from ods.event_exposure_dist\n" +
            "         where os = 'android'\n" +
            "           and ad_action = 'exposure'\n" +
            "           and logday = today()\n" +
            "           and device_id global not in (select device_id\n" +
            "                                        from dwd.product_ocpc_activate_dist\n" +
            "                                        where logday between today() - 29 and today()\n" +
            "                                          and ocpc_channel like '%baidu%'\n" +
            "                                          and os = 'android'\n" +
            "                                        group by device_id)\n" +
            "         group by product, userid, device_id, pos_id, manufacturer\n" +
            "         ) ed\n" +
            "         global\n" +
            "         left join (\n" +
            "    select distinct pos_id, ad_type_name, type_name from dwd.product_ad_conf_dist\n" +
            "    ) b on ed.pos_id = b.pos_id\n" +
            "group by product, userid, device_id, manufacturer\n" +
            "having bd_pp > 0.97\n" +
            "   and pv > 10")
    List<UserMacIpCheckEntity> queryNonBaiduAndroidIncomeAbnormalUserInfo();

    @Select("select product, userIdStr\n" +
            "from (select product,\n" +
            "             userid                                                                              as userIdStr,\n" +
            "             device_id,\n" +
            "             floor(sum(toFloat32OrZero(extend1)) / 1000, 2)                                      as inc,\n" +
            "             floor(sum(if(ad_type_name like '%广点通%', toFloat64OrZero(extend1), 0)) / 1000, 2) as gdt_inc,\n" +
            "             floor(gdt_inc / inc, 4)                                                             as gdt_inc_rate\n" +
            "      from (select product, userid, device_id, pos_id, extend1\n" +
            "            from ods.event_exposure_dist\n" +
            "            where os = 'android' and event = 'AdData'\n" +
            "               and product in ('cffk','cyww2','blgls','dcdy','cfmm','qtdd','cssb','cyww','cfdd')\n" +
            "              and logday >= today() - 6\n" +
            "              and channel like '%ksdr%'\n" +
            "              and ad_action = 'exposure'\n" +
            "            group by product, userid, device_id, pos_id, extend1\n" +
            "               ) a\n" +
            "               global\n" +
            "               left join (select distinct pos_id, ad_type_name, type_name from dwd.product_ad_conf_dist) b\n" +
            "                         on a.pos_id = b.pos_id\n" +
            "      group by product, userid, device_id\n" +
            "      having inc > 10\n" +
            "         and gdt_inc_rate > 0.98) res")
    List<UserMacIpCheckEntity> queryKsdrHwGdtIncomeAbnormalUserInfo();

    @Select("select product,\n" +
            "       os,\n" +
            "       groupUniqArray(userid)                                                                                   as userIdStr,\n" +
            "       arrayDistinct(arrayFlatten([groupUniqArray(imeiAry),groupUniqArray(oaidAry),groupUniqArray(deviceAry)])) as firstAry,\n" +
            "       arrayFilter(r -> r != '' and match(r, '^0000.*') = 0,\n" +
            "                   firstAry)                                                                                    as deviceIdStr\n" +
            "from (\n" +
            "         select r1.product,\n" +
            "                r1.os,\n" +
            "                r1.userid,\n" +
            "                groupUniqArray(r1.device_id) as deviceAry,\n" +
            "                groupUniqArray(r1.imei)      as imeiAry,\n" +
            "                groupUniqArray(r1.oaid)      as oaidAry,\n" +
            "                count()                      as pv,\n" +
            "                sum(if(toFloat64OrZero(r1.extend1) > 20000, r2.ecpm, toFloat64OrZero(r1.extend1)) >\n" +
            "                    1000)                    as moreThan1k,\n" +
            "                sum(if(toFloat64OrZero(r1.extend1) > 20000, r2.ecpm, toFloat64OrZero(r1.extend1)) >\n" +
            "                    3000)                    as moreThan3k,\n" +
            "                sum(if(toFloat64OrZero(r1.extend1) > 20000, r2.ecpm, toFloat64OrZero(r1.extend1)) >\n" +
            "                    600)                     as moreThan6b\n" +
            "         from ods.event_exposure_dist r1\n" +
            "                  left join dwd.tb_ap_ad_budget_dist r2 on r1.ad_id = toString(r2.id)\n" +
            "         where logday = today()\n" +
            "           and ad_action = 'exposure'\n" +
            "           and userid != '0'\n" +
            "           and ad_type global in (select toString(ad_type) from dwd.ad_type_basic_dist where type_name = '视频')\n" +
            "         group by r1.product, r1.os, r1.userid\n" +
            "         having (pv > 30 and (moreThan3k > 30 or moreThan1k > 200 or moreThan6b > 400))\n" +
            "         )\n" +
            "group by product, os")
    List<UserMacIpCheckEntity> queryEcpmFilterCountByGroup10();

    @Select("select userIdStr, product, device_id, os\n" +
            "from (select product\n" +
            "           , os\n" +
            "           , userid                                         userIdStr\n" +
            "           , device_id\n" +
            "           , sum(if(type_name = '视频', pv1, 0))         as sp_pv\n" +
            "           , sum(inc)                                    as income\n" +
            "           , sum(if(ad_type_name like '%百度%', inc, 0)) as bdincome\n" +
            "           , bdincome / income                           as bd_pp\n" +
            "      from (\n" +
            "               select os,\n" +
            "                      product,\n" +
            "                      userid,\n" +
            "                      device_id,\n" +
            "                      pos_id,\n" +
            "                      count(1)                             as pv1,\n" +
            "                      sum(toFloat64OrZero(extend1) / 1000) as inc\n" +
            "               from ods.event_exposure_dist\n" +
            "               where ad_action = 'exposure'\n" +
            "                 and logday = today()\n" +
            "                 and device_id global not in (select device_id\n" +
            "                                              from dwd.product_ocpc_activate_dist\n" +
            "                                              where logday between today() - 29 and today()\n" +
            "                                                and ocpc_channel like '%baidu%'\n" +
            "                                              group by device_id)\n" +
            "               group by product, userid, device_id, pos_id, os\n" +
            "               ) ed\n" +
            "               global\n" +
            "               left join (\n" +
            "          select distinct pos_id, ad_type_name, type_name from dwd.product_ad_conf_dist\n" +
            "          ) b on ed.pos_id = b.pos_id\n" +
            "      group by product, userid, device_id, os\n" +
            "      having ((os = 'android' and bd_pp > 0.97)\n" +
            "          or (os = 'ios' and bd_pp > 0.90))) c")
    List<UserMacIpCheckEntity> queryUserCallBackInterceptNonBaiduUserInfo();

    @Select("select userIdStr, product\n" +
            "from (select product\n" +
            "           , logday\n" +
            "           , os\n" +
            "           , userid                                         userIdStr\n" +
            "           , device_id\n" +
            "           , sum(if(type_name = '视频', pv1, 0))         as sp_pv\n" +
            "           , sum(inc)                                    as income\n" +
            "           , sum(if(ad_type_name like '%百度%', inc, 0)) as bdincome\n" +
            "           , bdincome / income                           as bd_pp\n" +
            "      from (\n" +
            "               select os,\n" +
            "                      logday,\n" +
            "                      product,\n" +
            "                      userid,\n" +
            "                      device_id,\n" +
            "                      pos_id,\n" +
            "                      count(1)                             as pv1,\n" +
            "                      sum(toFloat64OrZero(extend1) / 1000) as inc\n" +
            "               from ods.event_exposure_dist\n" +
            "               where ad_action = 'exposure'\n" +
            "                 and logday = today()\n" +
            "                 and os = 'android'\n" +
            "                 and (lower(manufacturer) like '%huawei%' or lower(manufacturer) like '%mi%')\n" +
            "                 and device_id global in (select device_id\n" +
            "                                          from dwd.product_ocpc_activate_dist\n" +
            "                                          where logday between today() - 6 and today()\n" +
            "                                          group by device_id)\n" +
            "               group by product, userid, device_id, pos_id, os, logday\n" +
            "               ) ed\n" +
            "               global\n" +
            "               left join (\n" +
            "          select distinct pos_id, ad_type_name, type_name from dwd.product_ad_conf_dist\n" +
            "          ) b on ed.pos_id = b.pos_id\n" +
            "      group by product, userid, device_id, os, logday\n" +
            "      having (bd_pp > 0.95 and income > 10)) c")
    List<UserMacIpCheckEntity> queryBdArpuIncAbnormalUserInfo();

    List<UserMacIpCheckEntity> queryGdtGapAbnormalUserInfo();

    @Select("select product, userid as userIdStr, pv\n" +
            "from (select product,\n" +
            "             userid,\n" +
            "             pv,\n" +
            "\n" +
            "             gdt_sp_pv,\n" +
            "             gdt_sp_rw,\n" +
            "             gdt_sp_rw / gdt_sp_pv as rate\n" +
            "      from (\n" +
            "               select logday,\n" +
            "                      product,\n" +
            "                      userid,\n" +
            "                      count(1)                                                                            as pv,\n" +
            "                      floor(sum(toFloat32OrZero(extend1)) / 1000, 2)                                      as inc,\n" +
            "                      sum(if(ad_type_name like '%广点通%', 1, 0))                                         as gdt_pv,\n" +
            "                      floor(sum(if(ad_type_name like '%广点通%', toFloat64OrZero(extend1), 0)) / 1000, 2) as gdt_inc,\n" +
            "                      floor(gdt_inc / inc, 4)                                                             as gdt_inc_rate,\n" +
            "                      sum(if(ad_type_name like '%广点通%' and type_name = '视频', 1, 0))                  as gdt_sp_pv\n" +
            "               from (select logday, product, userid, pos_id, extend1\n" +
            "                     from ods.event_exposure_dist\n" +
            "                     where os = 'android'\n" +
            "                       and logday = today()\n" +
            "                       and ad_action = 'exposure'\n" +
            "                        ) a\n" +
            "                        global\n" +
            "                        left join (select distinct pos_id, ad_type_name, type_name from dwd.product_ad_conf_dist) b\n" +
            "                                  on a.pos_id = b.pos_id\n" +
            "               group by logday, product, userid) t1\n" +
            "               global\n" +
            "               left join (select user_id, app_id, product, count(1) as gdt_sp_rw\n" +
            "                          from ods.ad_point_dist apd\n" +
            "                                   global\n" +
            "                                   left join dwd.product_map_dist pmd on toInt32(apd.app_id) = pmd.id\n" +
            "                          where dsp = 'guangdiantong'\n" +
            "                            and logday = today()\n" +
            "                            and uri like '%/user-call-back/call_back/%'\n" +
            "                          group by user_id, product, app_id) t2 on t1.userid = t2.user_id and t1.product = t2.product\n" +
            "      where gdt_sp_pv > 10\n" +
            "        and gdt_inc_rate > 0.8\n" +
            "        and rate < 0.2\n" +
            "        and inc > 5) res;")
    List<UserMacIpCheckEntity> queryUserCallBackAbnormaList();

    @Select("select toDate(create_time) as create_day,\n" +
            "       ip,\n" +
            "       product,\n" +
            "       os,\n" +
            "       uniqExact(user_id)  as counts\n" +
            "from dwd.user_active_dist\n" +
            "where toDate(create_time) = today()\n" +
            "group by create_day, ip, product, os\n" +
            "having counts > #{count}\n" +
            "order by create_day")
    List<UserMacIpCheckEntity> queryAllExIpUser(@Param("count") Integer count);

    @Select("select distinct user_id\n" +
            "from dwd.withdraw_dist wd\n" +
            "where wd.logday = today()\n" +
            "  and wd.status = 5\n" +
            "  and os = 'android'\n" +
            "  and product = 'dcdy'\n" +
            "and create_time between 1745424000000 and 1745465220000")
    List<String> queryOrderUserInfo();

    @Select("select product,\n" +
            "       arrayFilter(r->r != '0', groupUniqArray(userid)) as userIdStr\n" +
            "from (\n" +
            "         select product,\n" +
            "                userid,\n" +
            "                device_id,\n" +
            "                channel,\n" +
            "                floor(sum(toFloat32OrZero(extend1)) / 1000, 2)                                                  as inc,\n" +
            "                count(1)                                                                                        as pv,\n" +
            "                floor(sum(if(lower(ad_type_name) like '%sigmob%' or ad_type_name like '%广点通%',\n" +
            "                             toFloat32OrZero(extend1), 0)) / 1000,\n" +
            "                      2)                                                                                        as siggdt_inc,\n" +
            "                floor(siggdt_inc / inc, 4)                                                                      as inc_rate,\n" +
            "                sum(case\n" +
            "                        when (lower(ad_type_name) like '%sigmob%' or ad_type_name like '%广点通%')\n" +
            "                            then 1 end)                                                                         as siggdt_pv,\n" +
            "                floor(siggdt_pv / pv, 4)                                                                        as pv_rate\n" +
            "         from (select product, userid, device_id, channel, time, pos_id, extend1\n" +
            "               from ods.event_exposure_dist\n" +
            "               where os = 'android'\n" +
            "                 and logday >= today()\n" +
            "                 and ad_action = 'exposure'\n" +
            "                 and product global in (select product from dwd.product_map_dist where product_group = '项目一组' and product != 'hldls')\n" +
            "               group by product, userid, device_id, channel, time, pos_id, extend1\n" +
            "                  ) a\n" +
            "                  global\n" +
            "                  left join (select distinct pos_id, ad_type_name, type_name from dwd.product_ad_conf_dist) b\n" +
            "                            on a.pos_id = b.pos_id\n" +
            "         group by product, userid, device_id, channel\n" +
            "         having inc > 10\n" +
            "            and inc_rate > 0.95\n" +
            "         ) res\n" +
            "group by product")
    List<UserMacIpCheckEntity> querySgmGdtGroupUserInfo();

    List<UserMacIpCheckEntity> queryMultiReqIdAbnormalUserList();

    @Select("select product,\n" +
            "       arrayFilter(r->r != '0', groupUniqArray(userid)) as userIdStr\n" +
            "from (\n" +
            "         select product,\n" +
            "                userid,\n" +
            "                device_id,\n" +
            "                channel,\n" +
            "                floor(sum(toFloat32OrZero(extend1)) / 1000, 2) as                                   inc,\n" +
            "                floor(sum(if(ad_type_name like '%广点通%', toFloat64OrZero(extend1), 0)) / 1000, 2) gdt_inc,\n" +
            "                floor(gdt_inc / inc, 4)                        as                                   inc_rate\n" +
            "         from (select product, userid, device_id, channel, time, pos_id, extend1\n" +
            "               from ods.event_exposure_dist\n" +
            "               where os = 'android'\n" +
            "                 and logday >= today() - 1\n" +
            "                 and ad_action = 'exposure'\n" +
            "                 and manufacturer = 'XIAOMI'\n" +
            "               group by product, userid, device_id, channel, time, pos_id, extend1\n" +
            "                  ) a\n" +
            "                  global\n" +
            "                  left join (select distinct pos_id, ad_type_name, type_name from dwd.product_ad_conf_dist) b\n" +
            "                            on a.pos_id = b.pos_id\n" +
            "         group by product, userid, device_id, channel\n" +
            "         having inc > 30\n" +
            "            and inc_rate > 0.98) res\n" +
            "group by product")
    List<UserMacIpCheckEntity> queryGdtGapExUserInfo();

    @Select("select a.product_name as productName, a.pv as pv, a.amounts as amounts, b.yd_amount as ydAmount\n" +
            "from (\n" +
            "         select product_name, count() pv, sum(amount) as amounts\n" +
            "         from test.withdraw_mch_d\n" +
            "         where logday >= '2024-11-01'\n" +
            "           and logday < '2024-12-01'\n" +
            "           and batch_id != ''\n" +
            "         group by product_name\n" +
            "         ) a\n" +
            "         inner join\n" +
            "     (\n" +
            "         select month, product_name, round(toFloat64(replaceAll(yd_amount, ',', '')) * 100,0) as yd_amount\n" +
            "         from test.tmp_amount_product_m\n" +
            "         where month = '202411'\n" +
            "         ) b on a.product_name = b.product_name\n" +
            "          order by b.yd_amount" +
            ";")
    List<OrderAmount> taxAmountByMonth();



    @Select("select * from test.withdraw_mch_d where logday >= '2024-11-01' and logday < '2024-12-01'  ;")
    List<WithdrawMchDV> findOrdersByProduct(@Param("productName") String productName);

    @Insert("insert into test.withdraw_mch_d_v2 select * from (select * from test.withdraw_mch_d where logday >= '2024-09-01'  and logday < '2024-10-01'  and product_name = #{productName}  and order_no  =  );")
    int insertOrders(@Param("productName") String productName, List<WithdrawMchDV> selectedWithdrawMchDVS);

    @Select("select product,groupUniqArray(userid) as userIdStr from (\n" +
            "                                           select logday,\n" +
            "                                                  product,\n" +
            "                                                  channel,\n" +
            "                                                  userid,\n" +
            "                                                  count()                              pv,\n" +
            "                                                  avg(if(toFloat64OrZero(extend1) > 30000, r2.ecpm,\n" +
            "                                                         toFloat64OrZero(extend1))) as avgEcpm\n" +
            "                                           from ods.event_exposure_dist r1\n" +
            "                                                    left join dwd.tb_ap_ad_budget_dist r2 on r1.ad_id = toString(r2.id)\n" +
            "                                           where logday >= today() - 3\n" +
            "                                             and userid != '0'\n" +
            "                                             and userid != 'null'\n" +
            "                                             and manufacturer in ('HONOR', 'Xiaomi')\n" +
            "                                             and ad_type global in (select toString(ad_type)\n" +
            "                                                                    from dwd.ad_type_basic_dist\n" +
            "                                                                    where type_name = '视频')\n" +
            "                                             and match(channel, '^ttdr|^ksdr') = 1\n" +
            "                                           group by logday, product, channel, userid) t1\n" +
            "                                           inner join (\n" +
            "    select logday,\n" +
            "           product,\n" +
            "           floor(avg(if(toFloat64OrZero(extend1) > 30000, r2.ecpm, toFloat64OrZero(extend1))), 2) as avgEcpm1\n" +
            "    from ods.event_exposure_dist r1\n" +
            "             left join dwd.tb_ap_ad_budget_dist r2 on r1.ad_id = toString(r2.id)\n" +
            "    where logday >= today() - 3\n" +
            "      and ad_type global in (select toString(ad_type) from dwd.ad_type_basic_dist where type_name = '视频')\n" +
            "      and match(channel, '^csj|^gdt|^ttzn|^ttzx') = 1\n" +
            "    group by logday, product) t2 on t1.logday = t2.logday and t1.product = t2.product\n" +
            "where 3 < pv < 100\n" +
            "  and avgEcpm > avgEcpm1 * 5\n" +
            "group by product")
    List<UserMacIpCheckEntity> queryDelUser();
}
