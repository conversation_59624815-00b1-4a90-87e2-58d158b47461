package com.coohua.user.event.biz.dc.mapper;

import com.coohua.user.event.biz.dc.entity.RosEntity;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2023/11/30
 */
public interface RosMapper {
    @Select({"<script>",
            " select advertiser_id, sum(income) / sum(dnu) * 2 - sum(rebate_cost) / sum(dnu) as ros,sum(income) / sum(dnu) as arpu,sum(rebate_cost) / sum(dnu) as cpa, sum(withdraw_amount) / sum(dnu) as withdraw_amount " +
            " from ads.baidu_feed_report_data_group_process " +
            " where logday = #{logday} and advertiser_id in " ,
            " <foreach collection='advs' index='index' item='item' open='(' separator=',' close=')'>#{item}</foreach>",
            " group by advertiser_id ",
            "</script>"})
    List<RosEntity> queryBaiduRos(@Param("logday")String logday, @Param("advs")List<Long> advs);



    @Select({"<script>",
            " select advertiser_id,campaign_id, sum(income) / sum(dnu) * 2 - sum(rebate_cost) / sum(active) as ros,sum(income) / sum(dnu) as arpu,sum(rebate_cost) / sum(active) as cpa " +
                    " from ads.toutiao_report_tf_new_ad_process " +
                    " where logday = #{logday} and campaign_id in " ,
            " <foreach collection='advs' index='index' item='item' open='(' separator=',' close=')'>#{item}</foreach>",
            " group by advertiser_id,campaign_id ",
            "</script>"})
    List<RosEntity> queryToutiaoRos(@Param("logday")String logday, @Param("advs")List<Long> advs);

}
