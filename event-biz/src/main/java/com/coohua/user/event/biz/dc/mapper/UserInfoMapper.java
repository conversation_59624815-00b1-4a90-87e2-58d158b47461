package com.coohua.user.event.biz.dc.mapper;

import com.coohua.user.event.biz.dc.entity.LoginUserInfo;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

/**
 * <AUTHOR>
 * @since 2023/3/29
 */
public interface UserInfoMapper {

    @Select("select * from `user-event`.user_info where user_pr = #{ur}")
    LoginUserInfo queryByUser(@Param("ur") String ur);

    @Select("select * from `user-event`.user_info where id = #{ur}")
    LoginUserInfo queryByUserId(@Param("ur") String ur);
}
