package com.coohua.user.event.biz.ecp.entity;

/**
 * <AUTHOR>
 * @since 2021/7/16
 */

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import lombok.Data;

import java.io.Serializable;

/**
 * @description app_key
 * <AUTHOR> @date 2021-07-16
 */
@Data
public class AppKey implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(type = IdType.AUTO)
    /**
     * id
     */
    private Integer id;

    /**
     * ad_id
     */
    private Long adId;

    /**
     * key
     */
    private String keySr;

    public AppKey() {}
}
