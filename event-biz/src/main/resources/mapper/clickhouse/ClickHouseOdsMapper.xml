<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.coohua.user.event.biz.click.mapper.ClickHouseOdsMapper">

    <select id="queryMultiReqIdAbnormalUserList"
            resultType="com.coohua.user.event.biz.click.entity.UserMacIpCheckEntity">
        select os,
               product,
               arrayFilter(r->r != '0', groupUniqArray(userid))                                   as userIdStr,
                                     arrayDistinct(arrayFilter(r->r != 'null' and r != '',
                                                               arrayFlatten(groupUniqArray([device_id,imei,oaid])))) as deviceIdStr,
               element_uri,
               uniqExact(userid)         as uv
        from ods.event_exposure_dist
        where logday = today()
          and event = 'AdData'
          and ad_action = 'exposure'
          and element_uri global in (select distinct element_uri
                             from (
                                   select os, product, element_uri, uniqExact(userid) as counts
                                   from ods.event_dist
                                   where logday = today()
                                     and event = 'AdData'
                                     and ad_action = 'exposure'
                                     and element_uri != ''
                                     and element_uri is not null
                                     and element_uri != '(null)'
                                     and length(element_uri) > 6
                                   group by os, product, element_uri
                                      )
                             where counts > 2)
        group by os, product, element_uri
        order by uv desc
    </select>
    <select id="queryGdtGapAbnormalUserInfo"
            resultType="com.coohua.user.event.biz.click.entity.UserMacIpCheckEntity">
        select product,
               userid as userIdStr
        from ( select logday,
                      product,
                      userid,
                      channel,
                      model,
                      manufacturer,
                      count(1)                                                                            as pv,
                      floor(sum(toFloat32OrZero(extend1)) / 1000, 2)                                      as inc,
                      sum(if(ad_type_name like '%广点通%', 1, 0))                                         as gdt_pv,
                      floor(sum(if(ad_type_name like '%广点通%', toFloat64OrZero(extend1), 0)) / 1000, 2) as gdt_inc,
                      floor(gdt_inc / inc, 4)                                                             as gdt_inc_rate,
                      sum(if(ad_type_name like '%广点通%' and type_name = '视频', 1, 0))                  as gdt_sp_pv
               from (select logday,
                            product,
                            userid,
                            channel,
                            model,
                            manufacturer,
                            pos_id,
                            extend1
                     from ods.event_exposure_dist
                     where os = 'android'
                       and logday = today()
                       and ad_action = 'exposure'
                        ) a
                        global
                        left join (select distinct pos_id, ad_type_name, type_name from dwd.product_ad_conf_dist) b
                                  on a.pos_id = b.pos_id
               group by logday, product, userid, channel, model, manufacturer
               order by gdt_inc_rate desc) t1
                 global
                 left join (select user_id, app_id, product, count(1) as gdt_sp_rw
                            from ods.ad_point_dist apd
                                     global
                                     left join dwd.product_map_dist pmd on toInt32(apd.app_id) = pmd.id
                            where dsp = 'guangdiantong'
                              and logday = today()
                              and uri like '%/user-call-back/call_back/%'
                            group by user_id, app_id, product) t2 on t1.userid = t2.user_id and t1.product = t2.product
        where (channel not like '%ksdr%' and gdt_sp_pv &gt; 80 and gdt_inc_rate &gt; 0.9 and gdt_sp_rw / gdt_sp_pv &lt; 0.2 and inc &gt; 30)
           OR (channel like '%ksdr%' and gdt_sp_pv &gt; 100 and gdt_inc_rate &gt; 0.9 and gdt_sp_rw / gdt_sp_pv &lt; 0.2 and inc &gt; 50)
    </select>
</mapper>
