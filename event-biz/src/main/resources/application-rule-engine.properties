# ????Redis?????
# ?????????????Redis??????????

# ??????
rule.engine.enabled=true

# Redis??????????????Apollo???
# ????????Apollo????? "ad.user-event.redis" ??????
# app.jedis-cluster.user-event.address=redis-node1:6379,redis-node2:6379,redis-node3:6379
# app.jedis-cluster.user-event.pool.max-total=300
# app.jedis-cluster.user-event.pool.max-idle=200
# app.jedis-cluster.user-event.pool.min-idle=50
# app.jedis-cluster.user-event.pool.max-wait-millis=2000
# app.jedis-cluster.user-event.pool.test-on-borrow=false
# app.jedis-cluster.user-event.pool.test-on-return=false
# app.jedis-cluster.user-event.pool.test-while-idle=true
# app.jedis-cluster.user-event.pool.test-on-create=false

# ????????
rule.engine.cache.enabled=true
rule.engine.cache.ttl=3600

# ??????
logging.level.com.coohua.user.event.biz.rule=DEBUG
logging.level.org.springframework.data.redis=DEBUG
