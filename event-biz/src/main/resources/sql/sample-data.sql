-- 示例规则分组
INSERT INTO `rule_group` (`group_code`, `group_name`, `description`, `app_id`, `status`, `create_time`, `update_time`, `creator`, `updater`)
VALUES ('withdraw_check', '提现审核规则', '用于提现审核的规则分组', 1, 1, NOW(), NOW(), 'admin', 'admin');

-- 示例规则
INSERT INTO `rule_definition` (`rule_code`, `rule_name`, `description`, `priority`, `rule_group_id`, `status`, `create_time`, `update_time`, `creator`, `updater`)
VALUES ('non_ocpc_exposure_limit', '非投放用户当日曝光超过500拒绝提现', '非投放用户当日曝光超过500拒绝提现', 100, 1, 1, NOW(), NOW(), 'admin', 'admin');

-- 示例规则条件
INSERT INTO `rule_condition` (`rule_id`, `field_name`, `field_display_name`, `operator`, `field_value`, `connector`, `condition_order`)
VALUES (1, 'userActive', '用户活跃信息', 'eq', 'isOcpc() == false', 'and', 1);

INSERT INTO `rule_condition` (`rule_id`, `field_name`, `field_display_name`, `operator`, `field_value`, `connector`, `condition_order`)
VALUES (1, 'params', '参数', 'eq', 'get("exposureCount") != null && (Integer)get("exposureCount") > 500', 'and', 2);

-- 示例规则动作
INSERT INTO `rule_action` (`rule_id`, `action_type`, `action_params`, `action_order`)
VALUES (1, 'reject', 'directRefund=true,reason=当日曝光超过500-拒绝提现', 1);

-- 示例Redis参数配置
INSERT INTO `rule_parameter_redis` (`parameter_name`, `parameter_display_name`, `redis_key_template`, `redis_command`, `data_type`, `default_value`, `description`, `priority`, `status`, `create_time`, `update_time`, `creator`, `updater`)
VALUES ('exposureCount', '曝光次数', 'user:exposure:video:time:{userId}', 'get', 'integer', '0', '用户当日曝光次数', 100, 1, NOW(), NOW(), 'admin', 'admin');

INSERT INTO `rule_parameter_redis` (`parameter_name`, `parameter_display_name`, `redis_key_template`, `redis_command`, `data_type`, `default_value`, `description`, `priority`, `status`, `create_time`, `update_time`, `creator`, `updater`)
VALUES ('isBlackDevice', '设备是否在黑名单中', 'black:device:{userId}', 'exists', 'boolean', 'false', '检查设备是否在黑名单中', 200, 1, NOW(), NOW(), 'admin', 'admin');
