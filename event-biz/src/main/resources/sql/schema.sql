-- 规则分组表
CREATE TABLE IF NOT EXISTS `rule_group` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `group_code` varchar(64) NOT NULL COMMENT '分组编码',
  `group_name` varchar(128) NOT NULL COMMENT '分组名称',
  `description` varchar(255) DEFAULT NULL COMMENT '分组描述',
  `app_id` bigint(20) NOT NULL COMMENT '应用ID',
  `status` tinyint(4) DEFAULT '1' COMMENT '状态：0-禁用，1-启用',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  `creator` varchar(64) DEFAULT NULL COMMENT '创建人',
  `updater` varchar(64) DEFAULT NULL COMMENT '更新人',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_group_code` (`group_code`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='规则分组表';

-- 规则定义表
CREATE TABLE IF NOT EXISTS `rule_definition` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `rule_code` varchar(64) NOT NULL COMMENT '规则编码',
  `rule_name` varchar(128) NOT NULL COMMENT '规则名称',
  `description` varchar(255) DEFAULT NULL COMMENT '规则描述',
  `priority` int(11) DEFAULT '0' COMMENT '优先级，数字越小优先级越高',
  `rule_group_id` bigint(20) NOT NULL COMMENT '规则分组ID',
  `status` tinyint(4) DEFAULT '1' COMMENT '状态：0-禁用，1-启用',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  `creator` varchar(64) DEFAULT NULL COMMENT '创建人',
  `updater` varchar(64) DEFAULT NULL COMMENT '更新人',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_rule_code` (`rule_code`),
  KEY `idx_rule_group_id` (`rule_group_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='规则定义表';

-- 规则条件表
CREATE TABLE IF NOT EXISTS `rule_condition` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `rule_id` bigint(20) NOT NULL COMMENT '规则ID',
  `field_name` varchar(64) NOT NULL COMMENT '字段名称',
  `field_display_name` varchar(128) DEFAULT NULL COMMENT '字段显示名称',
  `operator` varchar(32) NOT NULL COMMENT '操作符',
  `field_value` varchar(1024) NOT NULL COMMENT '字段值',
  `connector` varchar(8) DEFAULT 'and' COMMENT '条件连接符：and, or',
  `condition_order` int(11) DEFAULT '0' COMMENT '条件顺序',
  PRIMARY KEY (`id`),
  KEY `idx_rule_id` (`rule_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='规则条件表';

-- 规则动作表
CREATE TABLE IF NOT EXISTS `rule_action` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `rule_id` bigint(20) NOT NULL COMMENT '规则ID',
  `action_type` varchar(32) NOT NULL COMMENT '动作类型',
  `action_params` text COMMENT '动作参数，JSON格式',
  `action_order` int(11) DEFAULT '0' COMMENT '动作顺序',
  PRIMARY KEY (`id`),
  KEY `idx_rule_id` (`rule_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='规则动作表';

-- 规则执行日志表
CREATE TABLE IF NOT EXISTS `rule_execution_log` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `app_id` bigint(20) NOT NULL COMMENT '应用ID',
  `user_id` bigint(20) NOT NULL COMMENT '用户ID',
  `order_no` varchar(64) DEFAULT NULL COMMENT '订单号',
  `amount` int(11) DEFAULT NULL COMMENT '金额',
  `rule_id` bigint(20) DEFAULT NULL COMMENT '命中的规则ID',
  `rule_name` varchar(128) DEFAULT NULL COMMENT '命中的规则名称',
  `result` tinyint(4) DEFAULT NULL COMMENT '执行结果：0-拒绝，1-通过',
  `reason` varchar(255) DEFAULT NULL COMMENT '拒绝原因',
  `execution_time` bigint(20) DEFAULT NULL COMMENT '执行耗时(毫秒)',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  PRIMARY KEY (`id`),
  KEY `idx_app_id_user_id` (`app_id`,`user_id`),
  KEY `idx_order_no` (`order_no`),
  KEY `idx_create_time` (`create_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='规则执行日志表';

-- Redis参数配置表
CREATE TABLE IF NOT EXISTS `rule_parameter_redis` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `parameter_name` varchar(64) NOT NULL COMMENT '参数名称',
  `parameter_display_name` varchar(128) NOT NULL COMMENT '参数显示名称',
  `redis_key_template` varchar(255) NOT NULL COMMENT 'Redis键模板，支持变量替换，如user:exposure:video:time:{userId}',
  `redis_command` varchar(32) NOT NULL COMMENT 'Redis命令：get, hget, exists, sismember等',
  `redis_field` varchar(255) DEFAULT NULL COMMENT 'Redis字段，用于hash类型',
  `data_type` varchar(32) NOT NULL COMMENT '数据类型：string, integer, boolean, json',
  `default_value` varchar(255) DEFAULT NULL COMMENT '默认值，当Redis中不存在时使用',
  `description` varchar(255) DEFAULT NULL COMMENT '参数描述',
  `priority` int(11) DEFAULT '100' COMMENT '优先级',
  `status` tinyint(4) DEFAULT '1' COMMENT '状态：0-禁用，1-启用',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  `creator` varchar(64) DEFAULT NULL COMMENT '创建人',
  `updater` varchar(64) DEFAULT NULL COMMENT '更新人',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_parameter_name` (`parameter_name`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='Redis参数配置表';
