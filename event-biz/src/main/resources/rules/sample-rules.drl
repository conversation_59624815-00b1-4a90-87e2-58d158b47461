package com.coohua.user.event.rule;

import com.coohua.user.event.api.dto.CheckResult;
import com.coohua.user.event.biz.service.bean.UserActive;
import com.coohua.user.event.biz.click.entity.ProductEntity;
import java.util.Map;
import java.util.Date;
import org.slf4j.Logger;

global org.slf4j.Logger logger;

// 示例规则1：非投放用户当日曝光超过500拒绝提现
rule "非投放用户当日曝光超过500拒绝提现"
    salience 100
    when
        $checkResult: CheckResult(pass == true)
        $params: Map()
        $userActive: UserActive(isOcpc() == false)
        eval($params.get("exposureCount") != null && (Integer)$params.get("exposureCount") > 500)
    then
        $checkResult.setPass(false);
        $checkResult.setDirectRefund(true);
        $checkResult.setReason("当日曝光超过500-拒绝提现");
        logger.info("规则拒绝: 非投放用户当日曝光超过500拒绝提现");
end

// 示例规则2：关联设备处于黑名单拒绝提现
rule "关联设备处于黑名单拒绝提现"
    salience 90
    when
        $checkResult: CheckResult(pass == true)
        $params: Map()
        eval($params.get("isBlackDevice") != null && (Boolean)$params.get("isBlackDevice"))
    then
        $checkResult.setPass(false);
        $checkResult.setDirectRefund(true);
        $checkResult.setReason("关联设备处于黑名单-拒绝提现");
        logger.info("规则拒绝: 关联设备处于黑名单拒绝提现");
end

// 示例规则3：iOS用户非AppStore渠道拒绝提现
rule "iOS用户非AppStore渠道拒绝提现"
    salience 80
    when
        $checkResult: CheckResult(pass == true)
        $userActive: UserActive(os == "ios", channel != null && !channel.equals("AppStore"))
        $productEntity: ProductEntity()
        eval(!$productEntity.getProduct().equals("hhfg") && !$productEntity.getProduct().equals("nxdxz") && !$productEntity.getProduct().equals("qzysdd"))
    then
        $checkResult.setPass(false);
        $checkResult.setDirectRefund(false);
        $checkResult.setReason("校验失败:规则95-拒绝提现");
        logger.info("规则拒绝: iOS用户非AppStore渠道拒绝提现");
end

// 示例规则4：自然量用户提现金额超过限制
rule "自然量用户提现金额超过限制"
    salience 70
    when
        $checkResult: CheckResult(pass == true)
        $userActive: UserActive(isZiRan() == true)
        $params: Map()
        $totalAmount: Integer()
        eval($totalAmount > 5000)
    then
        $checkResult.setPass(false);
        $checkResult.setDirectRefund(false);
        $checkResult.setReason("自然量用户提现金额超过限制-拒绝提现");
        logger.info("规则拒绝: 自然量用户提现金额超过限制");
end
