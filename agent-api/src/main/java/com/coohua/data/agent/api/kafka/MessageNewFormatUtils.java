package com.coohua.data.agent.api.kafka;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.codec.binary.Base64InputStream;
import org.apache.commons.io.IOUtils;
import org.apache.commons.lang3.StringUtils;


import java.io.ByteArrayInputStream;
import java.util.zip.GZIPInputStream;
import java.util.zip.ZipException;

/**
 * 解析原始信息，原始信息在nginx+lua入到kafka时，做了相关的压缩和base64，需要根据规则解析出来
 * 目前规则信息如下：
 * 1. nginx收到的由客户端或服务端上报过来的数据，都做了base64编码，但是不一定做压缩编码。 所以数据为 [gzip|not gzip] + base64
 * 2. 收到nginx数据后，lua 做了将上报数据拼接上头信息，然后整体做base64。规则为： base64(headersize+header+上报数据)
 * <p>
 * 所以数据最后的格式为 ： base64(headersize+header+base64([gizp|not gzip]))，解码的时候从外层到里层逐步解析就可以了
 * （panchao desc）
 */
@Slf4j
public class MessageNewFormatUtils {


    /**
     * 将message字符串封装成message对象
     *
     * @param msgOrigin
     * @return
     */
    public static String formatMessage(String msgOrigin) {

        GZIPInputStream unzipInput = null;
        Base64InputStream originBase64Input = null;
        try {
            ByteArrayInputStream originByteInput = new ByteArrayInputStream(msgOrigin.getBytes());
            originBase64Input = new Base64InputStream(originByteInput);
            //对原始message进行解码

            byte[] originByte = IOUtils.toByteArray(originBase64Input);
            //原始messge
            String originStr = new String(originByte);

            //header length
            Integer headerlength = Integer.valueOf(originStr.substring(0, 8));
            //header + body
            String exludeHeaderStr = originStr.substring(8);
            log.info("header + body : " + exludeHeaderStr);
            //header 数组
            byte[] headerBytes = new byte[headerlength];
            System.arraycopy(exludeHeaderStr.getBytes(), 0, headerBytes, 0, headerlength);
            //header数据
            String headerStr = new String(headerBytes);
            log.info("header : " + headerStr);

            //body数据,有可能是gzip压缩的,也有可能没有压缩,需要分别处理
            String bodyStr = originStr.substring(8 + headerStr.length());
            log.info("body : " + bodyStr);

            byte[] unzipBodyByte = null;
            Base64InputStream originBodyBase64NozipInput = null;
            try { // 假定数据为做了gzip
                ByteArrayInputStream originBodyInput = new ByteArrayInputStream(bodyStr.getBytes());
                //base64 解码
                Base64InputStream originBodyBase64GzipInput = new Base64InputStream(originBodyInput);
                // gzip 解压缩
                unzipInput = new GZIPInputStream(originBodyBase64GzipInput);
                //解压缩后的body数据
                unzipBodyByte = IOUtils.toByteArray(unzipInput);
            } catch (ZipException e) {//不是gzip格式
                try {
                    log.warn(bodyStr + " : " + e.getMessage());
                    ByteArrayInputStream originBodyInput = new ByteArrayInputStream(bodyStr.getBytes());
                    originBodyBase64NozipInput = new Base64InputStream(originBodyInput);
                    unzipBodyByte = IOUtils.toByteArray(originBodyBase64NozipInput);
                } catch (Exception ex) {//不是base64
                    log.error(bodyStr + " : 非gzip格式，不是BASE64格式!", ex);
                }
            } catch (Exception ex) {//不是base64的gzip格式
                log.error(bodyStr + " : 解析base64异常，不是BASE64格式!", ex);
            } finally {
                IOUtils.closeQuietly(originBodyBase64NozipInput);
                IOUtils.closeQuietly(unzipInput);

            }
            //原始body数据
            if (unzipBodyByte != null) {
                String unzipBodyStr = new String(unzipBodyByte);
                log.info("unzipBodyStr : " + unzipBodyStr);
                return unzipBodyStr;
            }
        } catch (Exception e) {
            log.error(e.getMessage(), e);
        } finally {
            IOUtils.closeQuietly(originBase64Input);

        }
        return null;
    }

    /**
     * 先base64解码，后zip解码
     * @param msg
     * @return
     */
    public static String formatMessageForGizp(String msg) {
    	if(StringUtils.isEmpty(msg)){
    		return null;
	    }
        byte[] unzipBodyByte = null;
        ByteArrayInputStream originBodyInput = null;
        Base64InputStream originBodyBase64GzipInput = null;
        GZIPInputStream gzipInputStream = null;
        Base64InputStream originBodyBase64NozipInput = null;
        try { // 假定数据为做了gzip
            originBodyInput = new ByteArrayInputStream(msg.getBytes());
            //base64 解码
            originBodyBase64GzipInput = new Base64InputStream(originBodyInput);
            // gzip 解压缩
            gzipInputStream = new GZIPInputStream(originBodyBase64GzipInput);
            //解压缩后的body数据
            unzipBodyByte = IOUtils.toByteArray(gzipInputStream);
        } catch (ZipException e) {//不是gzip格式
            try {
                originBodyInput = new ByteArrayInputStream(msg.getBytes());
                originBodyBase64NozipInput = new Base64InputStream(originBodyInput);
                unzipBodyByte = IOUtils.toByteArray(originBodyBase64NozipInput);
            } catch (Exception ex) {//不是base64
                log.error("formatMessageForGizp {}: 非gzip格式，不是BASE64格式!", msg, ex);
                return null;
            }
        } catch (Exception e) {
            log.error("formatMessageForGizp Exception msg {}",msg, e);
            return null;
        } finally {
            IOUtils.closeQuietly(gzipInputStream);
            IOUtils.closeQuietly(originBodyBase64GzipInput);
            IOUtils.closeQuietly(originBodyInput);
            IOUtils.closeQuietly(originBodyBase64NozipInput);

        }
        return new String(unzipBodyByte);
    }

    public static void  main(String[] args){
        String xxt = formatMessageForGizp("W3siZGlzdGluY3RfaWQiOiIxNzEzMjQ4NjEwNjU3LTM3NzQzNDgtMGJiOTgwZTBiZjA4Yzc4LTI3NTI3MTkxIiwibGliIjp7IiRsaWIiOiJNaW5pR2FtZSIsIiRsaWJfbWV0aG9kIjoiY29kZSIsIiRsaWJfdmVyc2lvbiI6IjAuMTIuMCJ9LCJwcm9wZXJ0aWVzIjp7ImNoYW5uZWwiOiJkeW1pbiIsInByb2R1Y3QiOiJ0dGpjIiwidGltZXN0YW1wQ2xpZW50IjoxNzEzMjY0NjYzMTM2LCJvcGVuSWQiOiJvcGVuaWQxMjMxMjMiLCJ1c2VySWQiOiIxMjMiLCJpbWVpIjoib3BlbmlkMTIzMTIzIiwiZGV2aWNlSWQiOiJvcGVuaWQxMjMxMjMiLCJvYWlkIjoib3BlbmlkMTIzMTIzIiwiYW5kcm9pZElkIjoib3BlbmlkMTIzMTIzIn0sImFub255bW91c19pZCI6IjE3MTMyNDg2MTA2NTctMzc3NDM0OC0wYmI5ODBlMGJmMDhjNzgtMjc1MjcxOTEiLCJ0eXBlIjoicHJvZmlsZV9zZXQiLCJ0aW1lIjoxNzEzMjY0NjYzMTY3LCJfbm9jYWNoZSI6IjM3MzE0ODE0ODE0ODEiLCJfZmx1c2hfdGltZSI6MTcxMzI2NDY2NTE0M30seyJkaXN0aW5jdF9pZCI6IjE3MTMyNDg2MTA2NTctMzc3NDM0OC0wYmI5ODBlMGJmMDhjNzgtMjc1MjcxOTEiLCJsaWIiOnsiJGxpYiI6Ik1pbmlHYW1lIiwiJGxpYl9tZXRob2QiOiJjb2RlIiwiJGxpYl92ZXJzaW9uIjoiMC4xMi4wIn0sInByb3BlcnRpZXMiOnsiJGxpYiI6Ik1pbmlHYW1lIiwiJGxpYl92ZXJzaW9uIjoiMC4xMi4wIiwiJGRhdGFfaW5nZXN0aW9uX3NvdXJjZSI6WyJCeXRlZGFuY2VHYW1lIl0sIiR0aW1lem9uZV9vZmZzZXQiOi00ODAsIiRuZXR3b3JrX3R5cGUiOiJXSUZJIiwiJG1hbnVmYWN0dXJlciI6ImRldnRvb2xzIiwiJG1vZGVsIjoiaVBob25lIDE1IFBybyIsIiRicmFuZCI6IkRFVlRPT0xTIiwiJHNjcmVlbl93aWR0aCI6MzkzLCIkc2NyZWVuX2hlaWdodCI6ODUyLCIkb3MiOiJkZXZ0b29scyIsIiRvc192ZXJzaW9uIjoiMTUiLCIkbXBfY2xpZW50X2FwcF92ZXJzaW9uIjoiNi42LjMiLCIkbXBfY2xpZW50X2Jhc2ljX2xpYnJhcnlfdmVyc2lvbiI6IjMuMTYuMCIsImV4dGVuZDEiOiJkZW5nbHUiLCIkaXNfZmlyc3RfZGF5Ijp0cnVlfSwiYW5vbnltb3VzX2lkIjoiMTcxMzI0ODYxMDY1Ny0zNzc0MzQ4LTBiYjk4MGUwYmYwOGM3OC0yNzUyNzE5MSIsInR5cGUiOiJ0cmFjayIsImV2ZW50IjoibXN0YXJ0IiwiX3RyYWNrX2lkIjo3NTMyMjMxNjgsInRpbWUiOjE3MTMyNjQ2NjMxNjgsIl9ub2NhY2hlIjoiOTE0ODM2MjQ4Mjg1MyIsIl9mbHVzaF90aW1lIjoxNzEzMjY0NjY1MTQzfV0%3D");
        System.out.println(xxt);
    }
}
