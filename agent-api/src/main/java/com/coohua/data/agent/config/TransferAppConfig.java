package com.coohua.data.agent.config;

import com.alibaba.fastjson.JSON;
import com.ctrip.framework.apollo.spring.annotation.ApolloJsonValue;
import com.ctrip.framework.apollo.spring.annotation.EnableApolloConfig;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.util.Map;

/**
 * <AUTHOR>
 * @since 2022/10/27
 */
@Slf4j
@Component
@EnableApolloConfig(value="yanfa.app.transfer")
public class TransferAppConfig {

    @ApolloJsonValue("${app.trans.map:{}}")
    private Map<String,ProductConfig> appTransferConfig;

    public Boolean needConvert(String appId,String os){
        if (StringUtils.isEmpty(appId) || StringUtils.isEmpty(os)){
            return false;
        }
        return appTransferConfig.containsKey(String.format("%s:%s",appId,os.toLowerCase()));
    }

    public Boolean needConvertProduct(String product,String os){
        if (StringUtils.isEmpty(product) || StringUtils.isEmpty(os)){
            return false;
        }
        return appTransferConfig.containsKey(String.format("%s:%s",product,os.toLowerCase()));
    }

    public ProductConfig getConfigAfterConvert(String appId,String os){
        ProductConfig  productConfig = appTransferConfig.get(String.format("%s:%s",appId,os.toLowerCase()));
        if (productConfig == null){
            log.error("配置产品映射异常...");
            return new ProductConfig();
        }
        return productConfig;
    }

    public ProductConfig getConfigAfterConvertByProduct(String product,String os){
        ProductConfig  productConfig = appTransferConfig.get(String.format("%s:%s",product,os.toLowerCase()));
        if (productConfig == null){
            log.error("配置产品映射异常...");
            return new ProductConfig();
        }
        return productConfig;
    }

    @PostConstruct
    public void logConfig(){
        log.info("==> load Apollo ConvertConfig: {}", JSON.toJSONString(appTransferConfig));
    }

    @Data
    public static class ProductConfig{
        private String product;
        private Integer appId;
        private Integer pkgId;
    }
}
