package com.coohua.data.agent.api.kafka;

import com.pepper.metrics.integration.custom.Profile;
import lombok.extern.slf4j.Slf4j;
import org.apache.kafka.clients.producer.KafkaProducer;
import org.apache.kafka.clients.producer.ProducerConfig;
import org.apache.kafka.clients.producer.ProducerRecord;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.stereotype.Component;
import java.util.Properties;
import java.util.concurrent.*;
import java.util.concurrent.atomic.AtomicLong;

/**
 * <AUTHOR>
 * @since 2024/2/3
 */
@Slf4j
@Component
public class RtaKafkaSender implements InitializingBean {

    private KafkaProducer<String, String> producer;

    private AtomicLong counter = new AtomicLong(0);

    private final String topic = "rta_clickhouse";

    @Profile
    public void sendMsg(String jsonString){
        ProducerRecord<String, String> kafkaMessage = new ProducerRecord<>(topic, jsonString);
        producer.send(kafkaMessage, (recordMetadata, e) -> counter.incrementAndGet());
    }


    @Override
    public void afterPropertiesSet() throws Exception {
        Properties props = new Properties();
        //设置接入点，请通过控制台获取对应Topic的接入点。
        props.put(ProducerConfig.BOOTSTRAP_SERVERS_CONFIG, "172.16.49.65:9092,172.16.49.75:9092,172.16.49.74:9092");
        //消息队列Kafka版消息的序列化方式。
        props.put(ProducerConfig.KEY_SERIALIZER_CLASS_CONFIG, "org.apache.kafka.common.serialization.StringSerializer");
        props.put(ProducerConfig.VALUE_SERIALIZER_CLASS_CONFIG, "org.apache.kafka.common.serialization.StringSerializer");
        //请求的最长等待时间。
        props.put(ProducerConfig.MAX_BLOCK_MS_CONFIG, 30 * 1000);
        props.put(ProducerConfig.BATCH_SIZE_CONFIG, 16*1024);// 160kb
        props.put(ProducerConfig.LINGER_MS_CONFIG, 1000);// 当linger.ms>0时，延时性会增加，但会提高吞吐量，因为会减少消息发送频率
        //构造Producer对象，注意，该对象是线程安全的。
        //一般来说，一个进程内一个Producer对象即可。如果想提高性能，可构造多个对象，但最好不要超过5个。
        this.producer = new KafkaProducer<>(props);

        new Thread(new Runnable() {
            @Override
            public void run() {
                while (true) {
                    try {
                        TimeUnit.SECONDS.sleep(10);
                        long sec = counter.getAndSet(0);
                        log.info("RTA MSG.SEC={}", sec);
                    } catch (InterruptedException e) {
                        log.error("", e);
                    }
                }
            }
        }).start();
    }

}

