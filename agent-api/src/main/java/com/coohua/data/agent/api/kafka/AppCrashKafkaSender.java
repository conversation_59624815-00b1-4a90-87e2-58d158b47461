package com.coohua.data.agent.api.kafka;

import com.alibaba.fastjson.JSON;
import com.coohua.data.agent.biz.dto.req.CoreEventReq;
import com.pepper.metrics.integration.custom.Profile;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.kafka.clients.producer.KafkaProducer;
import org.apache.kafka.clients.producer.ProducerConfig;
import org.apache.kafka.clients.producer.ProducerRecord;
import org.apache.kafka.clients.producer.RecordMetadata;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.stereotype.Component;

import java.util.Date;
import java.util.Properties;
import java.util.concurrent.Future;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicLong;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * <AUTHOR>
 * @since 2020/8/25
 */
@Slf4j
@Component
public class AppCrashKafkaSender implements InitializingBean {
    private KafkaProducer<String, String> producer;

    private AtomicLong counter = new AtomicLong(0);

    private String topic = "app_crash_event";
    public static final String IOS_OS = "ios";

    public static Pattern pattern = Pattern.compile("[\u4e00-\u9fa5]");

    @Profile
    public void sendEvent(CoreEventReq coreEventReq){
        coreEventReq.setSendTime(new Date());

        /**
         * product 去除中文
         */
        if (StringUtils.isNotBlank(coreEventReq.getProperties().getProduct())) {
            Matcher matcher = pattern.matcher(coreEventReq.getProperties().getProduct());
            String product = matcher.replaceAll("").replaceAll(" ", "");
            coreEventReq.getProperties().setProduct(product);
        }
        if (StringUtils.isNotBlank(coreEventReq.getProperties().$os)) {
            String lowerOs = coreEventReq.getProperties().$os.toLowerCase();
            if(lowerOs.contains(IOS_OS)){
                lowerOs = IOS_OS;
            }
            coreEventReq.getProperties().$os = lowerOs;
        }
        String jsonString = JSON.toJSONString(coreEventReq);
        ProducerRecord<String, String> kafkaMessage = new ProducerRecord<>(topic, jsonString);
        producer.send(kafkaMessage, (recordMetadata, e) -> counter.incrementAndGet());
    }

    public static void main(String[] args) {
        //加载kafka.properties。
        Properties props = new Properties();
        //设置接入点，请通过控制台获取对应Topic的接入点。
        props.put(ProducerConfig.BOOTSTRAP_SERVERS_CONFIG, "alikafka-pre-cn-zvp2j2vhf007-1-vpc.alikafka.aliyuncs.com:9092," +
                "alikafka-pre-cn-zvp2j2vhf007-2-vpc.alikafka.aliyuncs.com:9092," +
                "alikafka-pre-cn-zvp2j2vhf007-3-vpc.alikafka.aliyuncs.com:9092");
        //消息队列Kafka版消息的序列化方式。
        props.put(ProducerConfig.KEY_SERIALIZER_CLASS_CONFIG, "org.apache.kafka.common.serialization.StringSerializer");
        props.put(ProducerConfig.VALUE_SERIALIZER_CLASS_CONFIG, "org.apache.kafka.common.serialization.StringSerializer");
        //请求的最长等待时间。
        props.put(ProducerConfig.MAX_BLOCK_MS_CONFIG, 30 * 1000);

        //构造Producer对象，注意，该对象是线程安全的。
        //一般来说，一个进程内一个Producer对象即可。如果想提高性能，可构造多个对象，但最好不要超过5个。
        KafkaProducer<String, String> producer = new KafkaProducer<>(props);

        //构造一个消息队列Kafka版消息。
        String topic = "app_crash_event"; //消息所属的Topic，请在控制台创建后，填写在这里。
        String value = "this is the message's value"; //消息的内容。

        ProducerRecord<String, String>  kafkaMessage = new ProducerRecord<>(topic, value);

        try {
            //发送消息，并获得一个Future对象。
            Future<RecordMetadata> metadataFuture = producer.send(kafkaMessage);
            //同步获得Future对象的结果。
            RecordMetadata recordMetadata = metadataFuture.get();
            System.out.println("Produce ok:" + recordMetadata.toString());
        } catch (Exception e) {
            //要考虑重试，参见常见问题。
            System.out.println("error occurred");
            e.printStackTrace();
        }
    }

    @Override
    public void afterPropertiesSet() throws Exception {
        Properties props = new Properties();
        //设置接入点，请通过控制台获取对应Topic的接入点。
        props.put(ProducerConfig.BOOTSTRAP_SERVERS_CONFIG, "alikafka-pre-cn-zvp2j2vhf007-1-vpc.alikafka.aliyuncs.com:9092," +
                "alikafka-pre-cn-zvp2j2vhf007-2-vpc.alikafka.aliyuncs.com:9092," +
                "alikafka-pre-cn-zvp2j2vhf007-3-vpc.alikafka.aliyuncs.com:9092");
        //消息队列Kafka版消息的序列化方式。
        props.put(ProducerConfig.KEY_SERIALIZER_CLASS_CONFIG, "org.apache.kafka.common.serialization.StringSerializer");
        props.put(ProducerConfig.VALUE_SERIALIZER_CLASS_CONFIG, "org.apache.kafka.common.serialization.StringSerializer");
        //请求的最长等待时间。
        props.put(ProducerConfig.MAX_BLOCK_MS_CONFIG, 30 * 1000);
        props.put(ProducerConfig.BATCH_SIZE_CONFIG, 16*1024);// 160kb
        props.put(ProducerConfig.LINGER_MS_CONFIG, 1000);// 当linger.ms>0时，延时性会增加，但会提高吞吐量，因为会减少消息发送频率
        //构造Producer对象，注意，该对象是线程安全的。
        //一般来说，一个进程内一个Producer对象即可。如果想提高性能，可构造多个对象，但最好不要超过5个。
        this.producer = new KafkaProducer<>(props);

        new Thread(new Runnable() {
            @Override
            public void run() {
                while (true) {
                    try {
                        TimeUnit.SECONDS.sleep(1);
                        long sec = counter.getAndSet(0);
                        log.info("APP_CRASH MSG.SEC={}", sec);
                    } catch (InterruptedException e) {
                        log.error("", e);
                    }
                }
            }
        }).start();
    }
}
