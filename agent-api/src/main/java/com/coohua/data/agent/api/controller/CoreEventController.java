package com.coohua.data.agent.api.controller;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.coohua.data.agent.api.kafka.*;
import com.coohua.data.agent.biz.db.service.DataCheckService;
import com.coohua.data.agent.biz.dto.CoreEventDomain;
import com.coohua.data.agent.biz.dto.req.CoreEventReq;
import com.coohua.data.agent.biz.dto.rsp.ReturnResult;
import com.coohua.data.agent.biz.service.MessageFormatUtils;
import com.coohua.data.agent.config.TransferAppConfig;
import com.ctrip.framework.apollo.spring.annotation.ApolloJsonValue;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.logging.log4j.util.Strings;
import org.checkerframework.checker.units.qual.A;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import java.net.URLDecoder;
import java.nio.charset.StandardCharsets;
import java.util.*;

@Controller
@RequestMapping("/data/agent")
@Slf4j
public class CoreEventController {

    @Value("${enable.log:false}")
    private Boolean enableLog;

    @Value("${enable.json.save:false}")
    private Boolean jsonSave;
    @ApolloJsonValue("${enable.user.log.list:[\"520937198\"]}")
    private List<String> userRequestList;
    @ApolloJsonValue("${enable.project.list:[\"ttddx\"]}")
    private List<String> projectsJsTest;
    @ApolloJsonValue("${enable.request.upload.list:[\"jiangnannongjia_1.0.3\"]}")
    private List<String> enableRequestList;


    @Value("#{${posid.map:null}}")
    private Map<String, String> posIdMap;

    @Autowired
    KafkaSender kafkaSender;
    @Autowired
    AppCrashKafkaSender appCrashKafkaSender;
    @Autowired
    private BpAdKafkaSender bpAdKafkaSender;
//    @Autowired
//    private UserEventCountSender userEventCountSender;
    @Autowired
    private TransferAppConfig transferAppConfig;
    @Autowired
    private RewardKafkaSender rewardKafkaSender;

    @RequestMapping(value = "/upload", method = RequestMethod.HEAD)
    @ResponseBody
    public ReturnResult upEventHead() {
        return new ReturnResult();
    }


    @RequestMapping(value = "/upload", method = RequestMethod.GET)
    @ResponseBody
    public ReturnResult upEventGet(@RequestParam Map<String, String> params, HttpServletRequest request) {
        try {
            String data = params.get("data");
            if (StringUtils.isEmpty(data)) {
                return new ReturnResult();
            }
            String jsonTr = MessageFormatUtils.formatMessage(data);

            CoreEventReq coreEventReq = JSON.parseObject(jsonTr, CoreEventReq.class);
            coreEventReq.setProject(params.get("project"));
            String ip = getReomteIp(request);
            if(StringUtils.isNotBlank(ip)){
                coreEventReq.getProperties().setIp(ip);
            }
            if (enableLog) {
                coreEventReq.setJstest(JSONObject.toJSONString(params));
            }else if(jsonSave){
                coreEventReq.setJstest(jsonTr);
            }

            sendToKafKa(Objects.requireNonNull(coreEventReq));
        }catch (Exception e){
            log.error("",e);
        }
        return new ReturnResult();
    }


    @RequestMapping(value = "/upload", method = RequestMethod.POST)
    @ResponseBody
    public ReturnResult upEvent(@RequestParam Map<String, String> params, HttpServletRequest request) {
        String parsedStr = "";
        try {
            String jsonTr = MessageNewFormatUtils.formatMessageForGizp(params.get("data_list"));
            if(StringUtils.isEmpty(jsonTr)){
                return  new ReturnResult();
            }
            String ip = getReomteIp(request);
            parsedStr = jsonTr;
            List<CoreEventReq> coreEventReqs = JSONObject.parseArray(jsonTr, CoreEventReq.class);

            for (CoreEventReq coreEventReq : coreEventReqs) {
                if (coreEventReq.getProperties()!=null && projectsJsTest.contains(coreEventReq.getProperties().getProduct())) {
                    coreEventReq.setJstest(JSONObject.toJSONString(params));
                }
                if(coreEventReq.getProperties()!=null
                        && StringUtils.isNotBlank(coreEventReq.getProperties().getUserId())
                        && userRequestList.contains(coreEventReq.getProperties().getUserId())){
                    log.info(JSON.toJSONString(coreEventReq));
                }
                coreEventReq.setProject(params.get("project"));
                if(StringUtils.isNotBlank(ip)){
                    coreEventReq.getProperties().setIp(ip);
                }
                sendToKafKa(Objects.requireNonNull(coreEventReq));
            }
        }catch (Exception e){
            log.error("parse {} ex:",parsedStr,e);
        }
        return new ReturnResult();
    }


    @RequestMapping(value = "/uploadMini")
    @ResponseBody
    public ReturnResult uploadMini(@RequestParam Map<String, String> params, HttpServletRequest request) {
        try {
            String rs = params.get("data");
            if (StringUtils.isNotEmpty(rs)){
                if (rs.contains("%")){
                    rs = URLDecoder.decode(rs, StandardCharsets.UTF_8.toString());
                }
            }
            String jsonTr = MessageNewFormatUtils.formatMessageForGizp(rs);

            if(StringUtils.isEmpty(jsonTr)){
                return new ReturnResult();
            }
            String ip = getReomteIp(request);
            List<CoreEventReq> coreEventReqs = null;
            if(jsonTr.startsWith("[")){
                coreEventReqs = JSONObject.parseArray(jsonTr, CoreEventReq.class);
            }else{
                coreEventReqs = new ArrayList<>();
                CoreEventReq coreEventReq = JSONObject.parseObject(jsonTr, CoreEventReq.class);
                coreEventReqs.add(coreEventReq);
            }

            for (CoreEventReq coreEventReq : coreEventReqs) {
                if (coreEventReq.getProperties()!=null && projectsJsTest.contains(coreEventReq.getProperties().getProduct())) {
                    coreEventReq.setJstest(JSONObject.toJSONString(params));
                }
                if(coreEventReq.getProperties()!=null && StringUtils.isNotBlank(coreEventReq.getProperties().getUserId()) && userRequestList.contains(coreEventReq.getProperties().getUserId())){
                    log.info(JSON.toJSONString(coreEventReq));
                }
                coreEventReq.setProject(params.get("project"));
                if(StringUtils.isNotBlank(ip)){
                    coreEventReq.getProperties().setIp(ip);
                }
                if (StringUtils.isEmpty(coreEventReq.getProperties().getProduct())
                        && "wmin".equalsIgnoreCase(coreEventReq.getProperties().$os)){
                    coreEventReq.getProperties().setProduct("wdncxcx");
                }
                sendToKafKa(Objects.requireNonNull(coreEventReq));
            }
        }catch (Exception e){
            log.error("Solve MiSource: ",e);
        }
        return new ReturnResult();
    }

    private void replacePid(CoreEventReq coreEventReq){
        try {
            if(posIdMap!=null){
                if(StringUtils.isNotBlank(coreEventReq.getProperties().getPos_id())){
                    String posId = coreEventReq.getProperties().getPos_id();
                    String adId = posIdMap.get(posId);
                    if(StringUtils.isNotBlank(adId) && !StringUtils.equalsIgnoreCase(adId,coreEventReq.getProperties().getAd_id())){
                        coreEventReq.getProperties().setAd_id(adId);
                    }
                }
            }
        }catch (Exception e){
            log.error("",e);
        }
    }
    public static String getReomteIp(HttpServletRequest request) {
        try {
            String ip = request.getHeader("x-forwarded-for");
            if (StringUtils.isNotBlank(ip) && !"unknown".equalsIgnoreCase(ip)) {
                //取第一个IP，即真实客户端 IP
                int index = ip.indexOf(",");
                if (index != -1) {
                    log.info("新域名获取的IP: {}", ip);
                    ip = ip.substring(0, index).trim();
                }
                return ip;
            }
            if (ip == null || ip.length() == 0 || "unknown".equalsIgnoreCase(ip)) {
                ip = request.getHeader("Proxy-Client-IP");
            }
            if (ip == null || ip.length() == 0 || "unknown".equalsIgnoreCase(ip)) {
                ip = request.getHeader("WL-Proxy-Client-IP");
            }
            if (ip == null || ip.length() == 0 || "unknown".equalsIgnoreCase(ip)) {
                ip = request.getRemoteAddr();
            }
            return ip;
        } catch (Exception e) {
            log.warn("Invoke ip exp:" + e);
        }
        return null;
    }


    private static final List<String> notSendList = new ArrayList<String>(){{
        add("request");
        add("config_cache_video_load_finish_fail");
        add("config_cache_splash_load_finish_fail");
        add("config_cache_image_load_finish_fail");
        add("oppo_video_error");
        add("cache_load_fail");
        add("reqSuccess_cache");
        add("cache_load_success");
        add("cache_finish");
        add("error");
//        add("video_cache_finish");
    }};

    private final static List<String> needProcessAdActionList = new ArrayList<String>(){{
        add("reward");
        add("click");
        add("exposure");
    }};

    @Autowired
    DataCheckService dataCheckService;
    private void sendToKafKa(CoreEventReq coreEventReq){
        replacePid(coreEventReq);
        long dtime = System.currentTimeMillis();
        dataCheckService.updateCounter(coreEventReq);

        coreEventReq.setServerTime(dtime);

        if((System.currentTimeMillis()-dtime)>500){
            log.info("updateCounter cost "+(System.currentTimeMillis()-dtime));
        }
        // 过滤空
        if (StringUtils.isEmpty(coreEventReq.getEvent())){
            return;
        }

        try {
            if ("yinianxiuxian".equals(coreEventReq.getProperties().getProduct()) && "ios".equalsIgnoreCase(coreEventReq.getProperties().$os)){
                // 转化为世外人家2
                coreEventReq.getProperties().setProduct("xiuxianswrj");
            }
            // 其他转换
            if (transferAppConfig.needConvertProduct(coreEventReq.getProperties().getProduct(),coreEventReq.getProperties().$os)){
                TransferAppConfig.ProductConfig productConfig =
                        transferAppConfig.getConfigAfterConvertByProduct(coreEventReq.getProperties().getProduct()
                                ,coreEventReq.getProperties().$os);
                coreEventReq.getProperties().setProduct(productConfig.getProduct());
            }
            if ("wydfd".equals(coreEventReq.getProject())){
                // 处理为 mvp
                coreEventReq.setProject("mvp");
            }
        }catch (Exception e){
            log.error("Solve Convert Ex:",e);
        }

        if ("AdData".equals(coreEventReq.getEvent()) &&
                (notSendList.contains(coreEventReq.getProperties().getAd_action())
                || coreEventReq.getProperties().getAd_action().contains("reqSuccess_cache")
                || coreEventReq.getProperties().getAd_action().contains("cache_load_fail")
                || coreEventReq.getProperties().getAd_action().contains("reqSuccess")
                )
        ){
            try {
                if (!"request".equals(coreEventReq.getProperties().getAd_action())){
                    return;
                }
                if (enableRequestList == null || enableRequestList.size() == 0){
                    return;
                }
                String product = coreEventReq.getProperties().getProduct();
                String appVer = coreEventReq.getProperties().$app_version;
                if (StringUtils.isNotEmpty(product) && StringUtils.isNotEmpty(appVer)){
                    String key = product + "_" + appVer;
                    if (!enableRequestList.contains(key)){
                        return;
                    }
                }else {
                    return;
                }
            }catch (Exception e){
                log.error("solve err:",e);
                return;
            }
        }


        String os = coreEventReq.getProperties().$os;
        if(StringUtils.isNotBlank(os) && StringUtils.equalsIgnoreCase("HarmonyOS",os)){
            os="android";
        }else if(StringUtils.isNotBlank(os) && StringUtils.equalsIgnoreCase("macOS",os)){
            os="ios";
        }

        if(StringUtils.equalsIgnoreCase(coreEventReq.getProperties().$lib,"ios")){
            os="ios";
        }
        coreEventReq.getProperties().$os = os;
        //android ios
        if(StringUtils.isBlank(os) || (!"android".equalsIgnoreCase(os) && !"wmin".equalsIgnoreCase(os) && !"byte".equalsIgnoreCase(os) && !"ios".equalsIgnoreCase(os))){
            log.error("异常数据 "+coreEventReq.getProject()+" @ "+os+"@"+JSON.toJSONString(coreEventReq));
            return;
        }


        if (("AdData".equals(coreEventReq.getEvent()) || "AdAction".equals(coreEventReq.getEvent()) )
                && needProcessAdActionList.contains(coreEventReq.getProperties().getAd_action())){
            bpAdKafkaSender.sendEvent(coreEventReq);
            if ("reward".equals(coreEventReq.getProperties().getAd_action().toLowerCase())){
                rewardKafkaSender.sendEvent(coreEventReq);
            }
        }

        // 过滤 element_page 不是crash的
        if (StringUtils.isEmpty(coreEventReq.getProperties().getElement_page())){
            kafkaSender.sendEvent(coreEventReq);
//            userEventCountSender.sendEvent(coreEventReq);
            if ("cosEr".equals(coreEventReq.getProperties().getAd_action())){
                appCrashKafkaSender.sendEvent(coreEventReq);
            }
            return;
        }

        // EVENT 过滤
        if ("AppStatus".toLowerCase().equals(coreEventReq.getEvent().toLowerCase()) &&
                "crash".equals(coreEventReq.getProperties().getElement_page().toLowerCase())){
            appCrashKafkaSender.sendEvent(coreEventReq);
            kafkaSender.sendEvent(coreEventReq);//crash 点上报后端
//            userEventCountSender.sendEvent(coreEventReq);
        }else {
            kafkaSender.sendEvent(coreEventReq);
//            userEventCountSender.sendEvent(coreEventReq);

            if ("通知开启状态".equals(coreEventReq.getProperties().getElement_page())){
                appCrashKafkaSender.sendEvent(coreEventReq);
            }
        }
    }

    public static void main(String[] args){
//        String s ="H4sIAAAAAAAAAO1aTW/kNBj+LxmOnciOncTurWKLqITQCgQ9IBR5YmfGbSYJsdNSVj1y2R/AHjhxQCCkPSAuoIV/s6MV/4LXnna7k35oF2m3q9ZzmeT169jvx/PoGU++ehQVthflYaFltI0zknKcbkVWLxXcpjzLWY5ZliIExpMOjJF3j7YiqY3VTWn9zIinSFWy4own+QwnChxqPYu2H0Uf+O9op5F9C65b3lAcqd7otoEBEidx6syi614x4xivzc57qeyidcuUrVTnRqms0LU3LmOjGtP2RgorYtGI+sTq0sCVXzQ28jD+fO3xADx2Hu5NJj6O3SPV2Mlkcyw+EEdiMuEsOt2KlPNw+++6h2KuvtTqGDbQ9W2nequVuTrEUvS9Vj0Yn//5dPXjPy9+ebZ6/Ksbac0rQXJnkepIl+raNF6XriWkwkW/8+nudOcThNYP39yGKXulmuJYS7uAeiKGLowLpecLiCxJvHUpmqESpR16v+2Pv9jZ3927oSzHutLRtu0HBTeNssdtf1ictcj+3kduagf5KhrhWil6/gwS8NPq5x9Wj39f/f3Xi9+egMNgVL/nwsYoSTEheZ6DtVyIpvGhlebgsDYH3x2YElGEYQz6ElaNWEZ4xhClKU4opW7AFDCvn+tmHm1DPDNhrepPihrqV3sLeIimbU6W2p54g+iV8Be1gDQQHDNMeJ7BvYsU4zxOMuh/X2w5lK4JHvRi3jb7bV+79LbC16wisyqvcD6tuFLTXKV4SipaTbmUEmqZy1ImLmOwgUr3xhZSwAYqURsF/VVU9WAWxQbiEgQX5HRrE5yU0CyHvsBX4DMh9x6f0o0GaL4eNIUs4IHreb36ZlDGemvntokRfGjuvXznIZQgej0OwM1B/TqUu+HW6PVqayDK9ripWyHXOJRnm4N1YFGpKjHUtjgbPeeIC4oA4Llcipe5hDQ4D0DuGuDtYPzUStfAAZ+p+fnKo1yM6r9R/oSBZVwOlKGXxo3CHUH+IAfloc+C6/dyEc+G2SCH9kCL5ibmclg2Viy7D2vtW/kC1tRl/eoi30SSb8o0dMw0jOeEMsovE03O80A0gWj+H9Gob4EH4NmBad47psnorTDNlHDGOCMJu0w1oMYC1QSqCZrmbjFNztFtMA1NM8QxyeiYaDgswO490XSuUj7yQDWvQTWqVkvI3I0Uce5zdgCy+v6Pf588DYcebygQcIIQmFN66VSSJykKCiGcSoZTyVsFKKgonhOSJegSQAkNAA0SPpwW3DENzwlm70TDZ5e5Js04Q+wKMcBg7N5zTVDxQcW/DyKBjYHLKQUdTxyNjrwRD7++g4gPIv528QmbIRRzfhU+w6sFQcOHY/g7JeEB1swX4a1LeD5+tSBjCU6znI7/8APvBIeXDAPThNOCO0Y1oHbeyWnBmGqAaaDNSTr+x885YxyYJjBN0DR3i2gSkr19oskQOv36P82CrVc3MQAA";
//        String s = "H4sIAAAAAAAAAO2Vz2/TMBTH/xd3xy6KncT5cSv7IfUwrdIEHBCK3MRtvaVJZLurqmknLtMOiAsHhBDiBBc4I02Df2bd/g2evQ1WyrYD4jJyivP1c56/772P8uwApVqybC8VOUpW/cglhHo4biMtxhwlOIhpGMVxRIjrgjirQUT2BGqjXCgtykzbwygjYT/vc+IP+llAuQ8Bheij5ACt2CfqlLmsILRthXSfSyWqEjZ8x3WIkVld35AxyNF19JjrUWXTVDm/FnOumSisOHYUL1UlVc40c1jJipkWmYKVTeqofM/ZuYxYh4hOr9tqWR8b+7zUrdbinrPL9lmrhbGLDtuImxBjoK57bMifCD6FG9SyqrnUgqs/e6zUDS+RA26MmvN9kfFbK3ZbZcbg2hjtPepsue7l5xfTqUxyXqZTkesRSkLi/tJGXAxH2nTTqmNWTgYs0xPJJXxju9fbvqP4GZNS2MCzr5/nb08vPp7Mjz+ZnakYCJRoOeHwUnI9reReejUiT7ubXVMjKFdaMjNK6OLNyfz0NYjZiJWldZOp3Wk+nM0GLnaNTZg5YYpFaRQT1yc0iILQ3EKoFE7JoSiHKAEPfaY1l7O0gNYUVoFu5JPMdGlrti1NsKlKxWyhYQXuumbpefAyZplJ4yd0M1nbSDpBsraerG8aU5BpIKTSac5ml+ZgANJBMVGjdJGJmGCPHrYXCMKeF/kYLk+WCPJjlzQE1fVaIaz1Bp/78eEFH0PhUoPRTYKu9Suy5t++nH3/8HN3ori0sx74Po49Gj1s5lYxJT52PRIu/7agY0EDXQNdA91fQhcuQefj0MUUB8Hv0MU4wA10DXT/Arrzl0fn716cH72aH7//L6ELAS+f4mgJOoKxtwhdqsSwnNRL7AUkoCEl9OExt7IDjh8bx5UU0E1W3IpPw+R9TN41mNHh8x9gcFMWOhAAAA==";
//        String s ="H4sIAAAAAAAAAO1Vy24bNxT9F6pLeUBy3tq5bopq4VhokWQRBIObGUqiPcMZkNQLgYF2l2UWXTXfkH03/ZskSP4ilxzLtpTYqbvoShAEiec+yHvu6/krUlgN5UUhKzLKecZ4SPN0SKxsBBmxOE+yhHKWszxBcNMhSLwBGZJKGitVab0tiRlEIJI8ZElZZlWECrV8SUavyA/+lxyrSreoOvRAsRTayFahIAx4EDsYum4HpvhJt/qNsPPWXVS2ldiClbAgaw82gRHKtNpUYCEABfXGytLgP39tYKqL4Lde4yfUOJ6MBwMfyaOlUHYw2JUF57CEwSCkWUwuh0Q4HRdD5eR4e6fbTmgrhfl2hCVoLYVG8P3f7z68/efTH39+/v0vJ2nNrRizgAXUoZVYylLcyeRdjDXIhQt/8uPxKaW9+92HmFILoYqVrOycjFJOb7C5kLO5dVn2aANqMYXSLrR/99lkcnZvUlZyKsnI6oXAgxJ21eqL4qpEno1/HqMOVAU67C3LujXCY517I6OUMhp6HRc1oyx0z0Biq0XpuF5LaLff3hcayt4bKkpTVO1K1S1U/ozy/nL0nGFxiiksaltcSRdG6LG7Jol5koRY4Z4ouCYKw/Ry9yLVqk3TLoy3nMraCv2rmG0v3gv15JfHR08ej0/OTolzeosr7mja5zri0TV4OylLYWzRYTnOxFU5z0EqUBtorQQViFo2eLaOQuxN6Yonwe5MacTzLEvikPRdayw03UktfcHeNDCNMh/kN3NZzkEpX0ilOV/Ber1eNTSizKUZeZ5KjY+rYENGU6iNwIYopvXCzIvdMRFGPAsvhzsz5YgxlvE8zZPw66nCWXqYKhhD101gJp5KsTqMln85WjokrFDg6ol8ePP64+s35KbLybbN76/tO/sIax7N9EyqmW/5l2BxBmyKGjNWb6dPPyak3fTjRwvwf2rAuMMkCOOUhjGeXXCM8SBhaZzyeyZcCz5H+2PkOpEP7MRovxMjnuD0ib9e75z2O//QiIf1/l/Xu1jjdkbfexue397wNMnC72x48OuPPmzfs2h337PDvs9R43/Z99Hliy+rjxxPRQwAAA==";
        String s ="W3siZGlzdGluY3RfaWQiOiIxNjk1Njk3NDE4MzAyLTM5Nzg3MzctMGQ1ZTU3M2FjOTAxZTU4LTE1NTY2MDkwIiwibGliIjp7IiRsaWIiOiJNaW5pR2FtZSIsIiRsaWJfbWV0aG9kIjoiY29kZSIsIiRsaWJfdmVyc2lvbiI6IjAuMTIuMCJ9LCJwcm9wZXJ0aWVzIjp7IiRsaWIiOiJNaW5pR2FtZSIsIiRsaWJfdmVyc2lvbiI6IjAuMTIuMCIsIiRkYXRhX2luZ2VzdGlvbl9zb3VyY2UiOlsiQnl0ZWRhbmNlR2FtZSJdLCIkdGltZXpvbmVfb2Zmc2V0IjotNDgwLCIkbmV0d29ya190eXBlIjoiV0lGSSIsIiRtYW51ZmFjdHVyZXIiOiJkZXZ0b29scyIsIiRtb2RlbCI6ImlQaG9uZSAxMiIsIiRicmFuZCI6IkRFVlRPT0xTIiwiJHNjcmVlbl93aWR0aCI6MzkwLCIkc2NyZWVuX2hlaWdodCI6ODQ0LCIkb3MiOiJpb3MiLCIkb3NfdmVyc2lvbiI6IjE0IiwiJG1wX2NsaWVudF9hcHBfdmVyc2lvbiI6IjYuNi4zIiwiJG1wX2NsaWVudF9iYXNpY19saWJyYXJ5X3ZlcnNpb24iOiIyLjk2LjEiLCJwcm9kdWN0IjoieXlhY2NieXRlIiwiZXZlbnQiOiJBcHBDbGljayIsInN0cmF0ZWd5X2lkIjoiMSIsIm9haWQiOiJfMDAwZU1sMUZzVVdOVmV4UXFHMGFXOE1zbkpxRVBzbDZuOHoiLCIkZGV2aWNlX2lkIjoiXzAwMGVNbDFGc1VXTlZleFFxRzBhVzhNc25KcUVQc2w2bjh6IiwiJGltZWkiOiJfMDAwZU1sMUZzVVdOVmV4UXFHMGFXOE1zbkpxRVBzbDZuOHoiLCJ1c2VySWQiOiIxNTk5MzczNDAyIiwic2RrX3ZlcnNpb24iOiIxLjAuMCIsInRpbWVzdGFtcENsaWVudCI6MTY5NTcxNTk5OTAwMCwibGliIjoibnVsbCIsImFjdGlvbiI6IkFkRGF0YSIsImV4dGVuZDgiOiIwIiwiJGFwcF92ZXJzaW9uIjoiMS4wLjAiLCJlbGVtZW50X25hbWUiOiLnrKw05YWz5pWw5o2uOntcInBsYXlUaW1lc1wiOjEsXCJmYWlsVGltZXNcIjowLFwicHJvcFVzZVRpbWVzXCI6MCxcImZhaWxQcm9wVXNlVGltZXNcIjowLFwicGFzc0xldmVsQ291bnRcIjo0fSIsImVsZW1lbnRfcGFnZSI6Ilt5eWFjY2J5dGVd6YCa5YWz5oiQ5YqfIiwiJGlzX2ZpcnN0X2RheSI6dHJ1ZX0sImFub255bW91c19pZCI6IjE2OTU2OTc0MTgzMDItMzk3ODczNy0wZDVlNTczYWM5MDFlNTgtMTU1NjYwOTAiLCJ0eXBlIjoidHJhY2siLCJldmVudCI6IkFkRGF0YSIsIl90cmFja19pZCI6Nzg5MDA5NDkyLCJ0aW1lIjoxNjk1NzE1OTk5NDkyLCJfbm9jYWNoZSI6Ijg0MzUzNTY2NTI5NDkiLCJfZmx1c2hfdGltZSI6MTY5NTcxNjAwMDkzOH1d";
        String jsonTr = MessageNewFormatUtils.formatMessageForGizp(s);
        System.out.println("jsonTr = " + jsonTr);

    }
}
