package com.coohua.data.agent.api.controller;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.coohua.data.agent.api.kafka.*;
import com.coohua.data.agent.biz.db.service.DataCheckService;
import com.coohua.data.agent.biz.dto.req.CoreEventReq;
import com.coohua.data.agent.biz.dto.rsp.ReturnResult;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Controller;
import org.springframework.util.StreamUtils;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.servlet.http.HttpServletRequest;
import java.net.URLDecoder;
import java.nio.charset.StandardCharsets;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;

@Controller
@RequestMapping("/data/agent")
@Slf4j
public class ByteMinDjController {


    @Value("#{${posid.map:null}}")
    private Map<String, String> posIdMap;
    @Autowired
    KafkaSender kafkaSender;
    @Autowired
    AppCrashKafkaSender appCrashKafkaSender;
    @Autowired
    private BpAdKafkaSender bpAdKafkaSender;
//    @Autowired
//    private UserEventCountSender userEventCountSender;


    @RequestMapping(value = "/uploadMinData")
    @ResponseBody
    public ReturnResult uploadMini(Map<String, String> params, HttpServletRequest request) {
        try {
            String rs = new String(StreamUtils.copyToByteArray(request.getInputStream()));
            rs = rs.replace("\"", "").split("=")[1];

            log.info(rs);
            if (StringUtils.isBlank(rs)) {
                rs = request.getParameter("data_list");
            }
            if (StringUtils.isNotEmpty(rs)) {
                if (rs.contains("%")) {
                    rs = URLDecoder.decode(rs, StandardCharsets.UTF_8.toString());
                }
            }
            String jsonTr = MessageNewFormatUtils.formatMessageForGizp(rs);

            if (StringUtils.isEmpty(jsonTr)) {
                return new ReturnResult();
            }
            String ip = getReomteIp(request);
            List<CoreEventReq> coreEventReqs = null;
            if (jsonTr.startsWith("[")) {
                coreEventReqs = JSONObject.parseArray(jsonTr, CoreEventReq.class);
            } else {
                coreEventReqs = new ArrayList<>();
                CoreEventReq coreEventReq = JSONObject.parseObject(jsonTr, CoreEventReq.class);
                coreEventReqs.add(coreEventReq);
            }

            for (CoreEventReq coreEventReq : coreEventReqs) {
                coreEventReq.setProject(params.get("project"));
                if (StringUtils.isNotBlank(ip)) {
                    coreEventReq.getProperties().setIp(ip);
                }
                sendToKafKa(Objects.requireNonNull(coreEventReq));
            }
        } catch (Exception e) {
            log.error("Solve MiSource: ", e);
        }
        return new ReturnResult();
    }

    private void replacePid(CoreEventReq coreEventReq) {
        try {
            if (posIdMap != null) {
                if (StringUtils.isNotBlank(coreEventReq.getProperties().getPos_id())) {
                    String posId = coreEventReq.getProperties().getPos_id();
                    String adId = posIdMap.get(posId);
                    if (StringUtils.isNotBlank(adId) && !StringUtils.equalsIgnoreCase(adId, coreEventReq.getProperties().getAd_id())) {
                        coreEventReq.getProperties().setAd_id(adId);
                    }
                }
            }
        } catch (Exception e) {
            log.error("", e);
        }
    }

    public static String getReomteIp(HttpServletRequest request) {
        try {
            String ip = request.getHeader("x-forwarded-for");
            if (StringUtils.isNotBlank(ip) && !"unknown".equalsIgnoreCase(ip)) {
                //取第一个IP，即真实客户端 IP
                int index = ip.indexOf(",");
                if (index != -1) {
                    log.info("新域名获取的IP: {}", ip);
                    ip = ip.substring(0, index).trim();
                }
                return ip;
            }
            if (ip == null || ip.length() == 0 || "unknown".equalsIgnoreCase(ip)) {
                ip = request.getHeader("Proxy-Client-IP");
            }
            if (ip == null || ip.length() == 0 || "unknown".equalsIgnoreCase(ip)) {
                ip = request.getHeader("WL-Proxy-Client-IP");
            }
            if (ip == null || ip.length() == 0 || "unknown".equalsIgnoreCase(ip)) {
                ip = request.getRemoteAddr();
            }
            return ip;
        } catch (Exception e) {
            log.warn("Invoke ip exp:" + e);
        }
        return null;
    }


    private final static List<String> needProcessAdActionList = new ArrayList<String>() {{
        add("reward");
        add("click");
        add("exposure");
    }};

    @Autowired
    DataCheckService dataCheckService;

    private void sendToKafKa(CoreEventReq coreEventReq) {
        replacePid(coreEventReq);
        long dtime = System.currentTimeMillis();
        dataCheckService.updateCounter(coreEventReq);
        if ((System.currentTimeMillis() - dtime) > 500) {
            log.info("updateCounter cost " + (System.currentTimeMillis() - dtime));
        }
        // 过滤空
        if (StringUtils.isEmpty(coreEventReq.getEvent())) {
            return;
        }

        String os = coreEventReq.getProperties().$os;
        if (StringUtils.isNotBlank(os) && StringUtils.equalsIgnoreCase("HarmonyOS", os)) {
            os = "android";
        } else if (StringUtils.isNotBlank(os) && StringUtils.equalsIgnoreCase("macOS", os)) {
            os = "ios";
        }

        if (StringUtils.equalsIgnoreCase(coreEventReq.getProperties().$lib, "ios")) {
            os = "ios";
        }
        coreEventReq.getProperties().$os = os;
        //android ios
        if (StringUtils.isBlank(os) || (!"android".equalsIgnoreCase(os) && !"wmin".equalsIgnoreCase(os) && !"devtools".equalsIgnoreCase(os) && !"byte".equalsIgnoreCase(os) && !"ios".equalsIgnoreCase(os))) {
            log.error("异常数据 " + coreEventReq.getProject() + " @ " + os + "@" + JSON.toJSONString(coreEventReq));
            return;
        }


        // 过滤 element_page 不是crash的
        if (StringUtils.isEmpty(coreEventReq.getProperties().getElement_page())) {
            kafkaSender.sendEvent(coreEventReq);
//            userEventCountSender.sendEvent(coreEventReq);
            if ("cosEr".equals(coreEventReq.getProperties().getAd_action())) {
                appCrashKafkaSender.sendEvent(coreEventReq);
            }
            return;
        }

        // EVENT 过滤
        if ("AppStatus".toLowerCase().equals(coreEventReq.getEvent().toLowerCase()) &&
                "crash".equals(coreEventReq.getProperties().getElement_page().toLowerCase())) {
            appCrashKafkaSender.sendEvent(coreEventReq);
            kafkaSender.sendEvent(coreEventReq);//crash 点上报后端
//            userEventCountSender.sendEvent(coreEventReq);
        } else {
            kafkaSender.sendEvent(coreEventReq);
//            userEventCountSender.sendEvent(coreEventReq);

            if ("通知开启状态".equals(coreEventReq.getProperties().getElement_page())) {
                appCrashKafkaSender.sendEvent(coreEventReq);
            }
        }
    }

    public static void main(String[] args) {
        String s = "W3siZGlzdGluY3RfaWQiOiIxNjk1Njk3NDE4MzAyLTM5Nzg3MzctMGQ1ZTU3M2FjOTAxZTU4LTE1NTY2MDkwIiwibGliIjp7IiRsaWIiOiJNaW5pR2FtZSIsIiRsaWJfbWV0aG9kIjoiY29kZSIsIiRsaWJfdmVyc2lvbiI6IjAuMTIuMCJ9LCJwcm9wZXJ0aWVzIjp7IiRsaWIiOiJNaW5pR2FtZSIsIiRsaWJfdmVyc2lvbiI6IjAuMTIuMCIsIiRkYXRhX2luZ2VzdGlvbl9zb3VyY2UiOlsiQnl0ZWRhbmNlR2FtZSJdLCIkdGltZXpvbmVfb2Zmc2V0IjotNDgwLCIkbmV0d29ya190eXBlIjoiV0lGSSIsIiRtYW51ZmFjdHVyZXIiOiJkZXZ0b29scyIsIiRtb2RlbCI6ImlQaG9uZSAxMiIsIiRicmFuZCI6IkRFVlRPT0xTIiwiJHNjcmVlbl93aWR0aCI6MzkwLCIkc2NyZWVuX2hlaWdodCI6ODQ0LCIkb3MiOiJpb3MiLCIkb3NfdmVyc2lvbiI6IjE0IiwiJG1wX2NsaWVudF9hcHBfdmVyc2lvbiI6IjYuNi4zIiwiJG1wX2NsaWVudF9iYXNpY19saWJyYXJ5X3ZlcnNpb24iOiIyLjk2LjEiLCJwcm9kdWN0IjoieXlhY2NieXRlIiwiZXZlbnQiOiJBcHBDbGljayIsInN0cmF0ZWd5X2lkIjoiMSIsIm9haWQiOiJfMDAwZU1sMUZzVVdOVmV4UXFHMGFXOE1zbkpxRVBzbDZuOHoiLCIkZGV2aWNlX2lkIjoiXzAwMGVNbDFGc1VXTlZleFFxRzBhVzhNc25KcUVQc2w2bjh6IiwiJGltZWkiOiJfMDAwZU1sMUZzVVdOVmV4UXFHMGFXOE1zbkpxRVBzbDZuOHoiLCJ1c2VySWQiOiIxNTk5MzczNDAyIiwic2RrX3ZlcnNpb24iOiIxLjAuMCIsInRpbWVzdGFtcENsaWVudCI6MTY5NTcxNTk5OTAwMCwibGliIjoibnVsbCIsImFjdGlvbiI6IkFkRGF0YSIsImV4dGVuZDgiOiIwIiwiJGFwcF92ZXJzaW9uIjoiMS4wLjAiLCJlbGVtZW50X25hbWUiOiLnrKw05YWz5pWw5o2uOntcInBsYXlUaW1lc1wiOjEsXCJmYWlsVGltZXNcIjowLFwicHJvcFVzZVRpbWVzXCI6MCxcImZhaWxQcm9wVXNlVGltZXNcIjowLFwicGFzc0xldmVsQ291bnRcIjo0fSIsImVsZW1lbnRfcGFnZSI6Ilt5eWFjY2J5dGVd6YCa5YWz5oiQ5YqfIiwiJGlzX2ZpcnN0X2RheSI6dHJ1ZX0sImFub255bW91c19pZCI6IjE2OTU2OTc0MTgzMDItMzk3ODczNy0wZDVlNTczYWM5MDFlNTgtMTU1NjYwOTAiLCJ0eXBlIjoidHJhY2siLCJldmVudCI6IkFkRGF0YSIsIl90cmFja19pZCI6Nzg5MDA5NDkyLCJ0aW1lIjoxNjk1NzE1OTk5NDkyLCJfbm9jYWNoZSI6Ijg0MzUzNTY2NTI5NDkiLCJfZmx1c2hfdGltZSI6MTY5NTcxNjAwMDkzOH1d";
        String jsonTr = MessageNewFormatUtils.formatMessageForGizp(s);
        System.out.println("jsonTr = " + jsonTr);

    }
}
