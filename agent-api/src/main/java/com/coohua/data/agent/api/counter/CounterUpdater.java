package com.coohua.data.agent.api.counter;

import com.coohua.data.agent.biz.db.CacheConfig;
import com.coohua.data.agent.biz.db.entity.DataCheckAtomicLong;
import com.coohua.data.agent.biz.db.service.DataCheckService;
import com.google.common.cache.Cache;
import com.google.common.cache.LoadingCache;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.concurrent.ConcurrentMap;
import java.util.concurrent.TimeUnit;

@Slf4j
@Component
public class CounterUpdater implements InitializingBean {
    @Autowired
    DataCheckService dataCheckService;
    @Override
    public void afterPropertiesSet() throws Exception {
        new Thread(new Runnable() {
            @Override
            public void run() {
                while (true) {
                    try {
                        TimeUnit.SECONDS.sleep(15);
                        ConcurrentMap<String, DataCheckAtomicLong> dmp = CacheConfig.checkDataCache.asMap();
                        long dtime = System.currentTimeMillis();
                        dmp.forEach((key,dataCheckObj)->{
                            try {
                                dataCheckService.saveOrUpdateCheck(dataCheckObj);
                            }catch (Exception e){
                                log.error("",e);
                            }
                        });
                        log.info("save "+dmp.size()+" counter cost "+(System.currentTimeMillis()-dtime));
                    } catch (InterruptedException e) {
                        log.error("", e);
                    }
                }
            }
        }).start();

    }
}
