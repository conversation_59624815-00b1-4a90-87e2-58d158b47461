package com.coohua.user.event.admin.service;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.coohua.user.event.admin.rsp.UserAdSwitchRsp;
import com.coohua.user.event.admin.rsp.UserSkipPlatformRsp;
import com.coohua.user.event.biz.util.HBaseUtils;
import com.coohua.user.event.biz.util.RedisUtil;
import com.coohua.user.event.biz.util.Strings;
import lombok.extern.slf4j.Slf4j;
import org.apache.hadoop.hbase.TableName;
import org.apache.hadoop.hbase.client.Connection;
import org.apache.hadoop.hbase.client.Get;
import org.apache.hadoop.hbase.client.Result;
import org.apache.hadoop.hbase.client.Table;
import org.apache.hadoop.hbase.util.Bytes;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import top.zrbcool.pepper.boot.jediscluster.JedisClusterClient;
import top.zrbcool.pepper.boot.jediscluster.JedisClusterClientRefer;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2021/9/18
 */
@Slf4j
@Service
public class UserAdSwitchService {

    @JedisClusterClientRefer(namespace = "ap-cluster")
    private JedisClusterClient jedisClusterClient;

    @Autowired
    private ApolloConfigService apolloConfigService;
    @Resource(name = "hbaseConnection")
    private Connection hbaseConnection;

    public UserAdSwitchRsp queryUserSwitch(Integer appId, Long userId){
        UserAdSwitchRsp rsp = new UserAdSwitchRsp();
        rsp.setAppId(appId);
        rsp.setUserId(userId.toString());
        rsp.setStatus("0");
        String flag  = jedisClusterClient.get(RedisUtil.buildUserReaderAd(appId.toString(),userId.toString()));
        if (Strings.noEmpty(flag)){
            rsp.setStatus(flag.equals("true")?"0":"1");
        }
        return rsp;
    }

    public void updateUserSwitch(Integer appId,Long userId,Integer status){
        jedisClusterClient.set(RedisUtil.buildUserReaderAd(appId.toString(),userId.toString()), Boolean.valueOf(status == 0).toString());
    }

    public void updateUserExposure(Integer appId,Long userId,Integer time){
        jedisClusterClient.set(RedisUtil.buildExposureVideo(userId,appId), time.toString());
    }

    public UserSkipPlatformRsp queryUserSkipPlatform(Integer appId, Long userId){
        UserSkipPlatformRsp userSkipPlatformRsp = new UserSkipPlatformRsp();

        userSkipPlatformRsp.setAppId(appId);
        userSkipPlatformRsp.setUserId(userId);

        // 查看 Bidding
        List<String> skipBiddingList = apolloConfigService.getUserBiddingSkip(appId,userId)
                .stream()
                .map(this::convertToStr)
                .collect(Collectors.toList());
        userSkipPlatformRsp.setBiddingSKipList(skipBiddingList);
        userSkipPlatformRsp.setIsLockedArea("2");
        try (Table table = hbaseConnection.getTable(TableName.valueOf("user_locked"))) {
            Get user = new Get(Bytes.toBytes(buildUserIdKey(appId, userId)));
            Result result = table.get(user);
            if (result != null) {
                byte[] bytes = result.getValue(Bytes.toBytes("family"), Bytes.toBytes("qualifier"));
                boolean locked = bytes == null ? false : JSONObject.parseObject(bytes, Boolean.class);
                if (locked){
                    userSkipPlatformRsp.setIsLockedArea("1");
                }
            }
        }catch (Exception e){
            log.error("Er:",e);
        }
        // 查看bidding 开关
        Boolean biddingSwitch = apolloConfigService.getBiddingSwitch(appId,userId);
        userSkipPlatformRsp.setIsBiddingEnable(biddingSwitch?"1":"2");
        // 查看bidding 是否用作缓存
        Integer biddingNocache = apolloConfigService.getNocacheStg(appId,userId);
        userSkipPlatformRsp.setNoCacheStg(biddingNocache.toString());


        String key = String.format("ad:skip:list:%s:%s",appId,userId);
        String resp = jedisClusterClient.get(key);
        if (Strings.noEmpty(resp)){
            List<String> skip = JSON.parseArray(resp,Integer.class)
                    .stream()
                    .map(this::convertToStr)
                    .collect(Collectors.toList());
            userSkipPlatformRsp.setPlatformList(skip);
        }else {
            userSkipPlatformRsp.setPlatformList(new ArrayList<>());
        }
        return userSkipPlatformRsp;
    }

    public void updateUserSkipPlatform(UserSkipPlatformRsp rsp){
        if (rsp.getAppId() == null || rsp.getUserId() == null){
            return;
        }

        List<Integer> skipPlt = rsp.getPlatformList().stream().map(this::convertToInt).collect(Collectors.toList());
        List<Integer> skipBidding = rsp.getBiddingSKipList().stream().map(this::convertToInt).collect(Collectors.toList());
        Boolean bidding = Strings.noEmpty(rsp.getIsBiddingEnable()) && "1".equals(rsp.getIsBiddingEnable());
        Boolean lockedArea = Strings.noEmpty(rsp.getIsBiddingEnable()) && "1".equals(rsp.getIsLockedArea());
        Integer nocache = Strings.noEmpty(rsp.getNoCacheStg()) ? Integer.parseInt(rsp.getNoCacheStg()):1;
        String key = String.format("ad:skip:list:%s:%s",rsp.getAppId(),rsp.getUserId());


        Map<String,byte[]> saveToHadoopBatch = new HashMap<>();
        saveToHadoopBatch.put(buildUserIdKey(rsp.getAppId(),rsp.getUserId()),JSON.toJSONBytes(lockedArea));
        HBaseUtils.saveToHadoopBatch(hbaseConnection, "user_locked", saveToHadoopBatch);

        jedisClusterClient.set(key,JSON.toJSONString(skipPlt));
        apolloConfigService.setBiddingSkip(rsp.getAppId(),rsp.getUserId(),skipBidding);
        apolloConfigService.switchBidding(rsp.getAppId(),rsp.getUserId(),bidding);
        apolloConfigService.enableBiddingCache(rsp.getAppId(),rsp.getUserId(),nocache);
//        apolloConfigService.setUserAppointAdIds(rsp.getAppId(),rsp.getUserId(),rsp.getAdIds());
        apolloConfigService.apolloPub();
        log.info("成功修改 {} {}",rsp.getAppId(),rsp.getUserId());
    }

    public void updateUserAbGroup(Integer appId,Long userId,Integer groupId){
        apolloConfigService.changeAbGroup(appId,userId,groupId);
    }

    private String convertToStr(Integer r){
        switch (r){
            case 1:return "穿山甲";
            case 2:return "广点通";
            case 3:return "快手";
            case 4:return "百度";
            case 5:return "VIVO";
            case 6:return "OPPO";
            case 7:return "小米";
            case 8:return "华为";
            case 9:return "阿里";
            case 0:return "爱奇艺";
            case 91:return "simob";
            default: return "";
        }
    }

    private String buildUserIdKey(Integer appId,Long userId){
        return String.format("locked:user:%s:%s", appId,userId);
    }

    private Integer convertToInt(String r){
        switch (r){
            case "穿山甲":return 1;
            case "广点通":return 2;
            case "快手":return 3;
            case "百度":return 4;
            case "VIVO":return 5;
            case "OPPO":return 6;
            case "小米":return 7;
            case "华为":return 8;
            case "阿里":return 9;
            case "爱奇艺":return 0;
            case "simob":return 91;
            default: return 0;
        }
    }

    public String cleanAccessKey(Integer appId,Long userId,Integer os){
        if (Integer.valueOf(1).equals(os)) {
            // IOS需要添加白名单
            apolloConfigService.addUserToUserWhiteList(userId);
        }
        String keyBak = String.format("%s:%s",appId, userId);
        byte[] result = HBaseUtils.searchDataFromHadoop(hbaseConnection,"user_access_key_bak",keyBak);
        // userHbase查看key redis删除相关联的key;
        if (result != null){
            String accessKey = Bytes.toString(result);
            String start = accessKey.split("_")[0];

            return String.format("auth:access:key:%d:%s", appId, start);

        }else {
            throw new RuntimeException("未查询到相关AccessKey");
        }
    }
}
