package com.coohua.user.event.admin.exception;

/**
 * <AUTHOR>
 * @since 2021/9/18
 */
public class AdminBsException extends RuntimeException{
    private int code;
    private String message;

    public AdminBsException(int code, String message) {
        this.code = code;
        this.message = message;
    }

    public int getCode() {
        return code;
    }

    public void setCode(int code) {
        this.code = code;
    }

    @Override
    public String getMessage() {
        return message;
    }

    public void setMessage(String message) {
        this.message = message;
    }
}
