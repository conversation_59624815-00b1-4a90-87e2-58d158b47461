package com.coohua.user.event.admin.controller;

import com.coohua.bp.user.remote.api.UserRPC;
import com.coohua.user.event.admin.rsp.BaseResponse;
import com.coohua.user.event.admin.rsp.UserAdSwitchRsp;
import com.coohua.user.event.admin.rsp.UserSkipPlatformRsp;
import com.coohua.user.event.admin.service.UserAdSwitchService;
import com.weibo.api.motan.config.springsupport.annotation.MotanReferer;
import lombok.extern.slf4j.Slf4j;
import org.apache.ibatis.annotations.Param;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import top.zrbcool.pepper.boot.jediscluster.JedisClusterClient;
import top.zrbcool.pepper.boot.jediscluster.JedisClusterClientRefer;

/**
 * <AUTHOR>
 * @since 2021/9/22
 */
@Slf4j
@RequestMapping("ad")
@RestController
public class UserAdSwitchController {

    @Autowired
    private UserAdSwitchService userAdSwitchService;
    @JedisClusterClientRefer(namespace = "bp-user")
    private JedisClusterClient jedisClusterClient;
    @MotanReferer(basicReferer = "bp-userBasicRefererConfigBean")
    private UserRPC userRPC;

    @PostMapping("query")
    public BaseResponse<UserAdSwitchRsp> queryUserRisk(@RequestParam("appId")Integer appId,
                                                       @RequestParam("userId")Long userId){
        return BaseResponse.build(userAdSwitchService.queryUserSwitch(appId,userId));
    }

    @PostMapping("update")
    public BaseResponse<String> updateUserRisk(@RequestParam("appId")Integer appId,
                                               @RequestParam("userId")Long userId,
                                               @RequestParam("status")Integer status){
        userAdSwitchService.updateUserSwitch(appId,userId,status);
        return BaseResponse.SUCCEED;
    }

    @PostMapping("updateExposure")
    public BaseResponse<String> updateExposure(@RequestParam("appId")Integer appId,
                                               @RequestParam("userId")Long userId,
                                               @RequestParam("time")Integer time){
        userAdSwitchService.updateUserExposure(appId,userId,time);
        return BaseResponse.SUCCEED;
    }


    @PostMapping("getUserSkipAdPlat")
    public BaseResponse<UserSkipPlatformRsp> getUserPlatform(@RequestParam("appId")Integer appId, @RequestParam("userId")Long userId){
        BaseResponse<UserSkipPlatformRsp> baseResponse = new BaseResponse<>(0);
        baseResponse.setResult(userAdSwitchService.queryUserSkipPlatform(appId,userId));
        return baseResponse;
    }


    @PostMapping("updateSkipAdPlat")
    public BaseResponse<String> updateUserSkipPlatform(@RequestBody UserSkipPlatformRsp request){
        userAdSwitchService.updateUserSkipPlatform(request);
        return BaseResponse.SUCCEED;
    }


    @PostMapping("updateUserAb")
    public BaseResponse<String> updateAbGroup(@RequestParam("appId")Integer appId,
                                              @RequestParam("userId")Long userId,
                                              @RequestParam("groupId")Integer groupId){
        userAdSwitchService.updateUserAbGroup(appId,userId,groupId);
        return BaseResponse.SUCCEED;
    }


    @PostMapping("delUserAccess")
    public BaseResponse<String> sa(@RequestParam("appId")Integer appId,
                                   @RequestParam("userId")Long userId,
                                   @RequestParam("delUser")Integer delUser,
                                   @Param("os")Integer os){
        String redisKey = userAdSwitchService.cleanAccessKey(appId,userId,os);
        long rs = jedisClusterClient.del(redisKey);
        log.info("成功清除用户 {} AccessKey {} rs: {}",userId,redisKey,rs);
        if (Integer.valueOf(1).equals(delUser)){
            userRPC.clearUser(userId);
            log.info("成功清除用户 {}",userId);
        }
        return BaseResponse.SUCCEED;
    }
}
