package com.coohua.user.event.admin.rsp;

import com.coohua.user.event.biz.util.AppConfig;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@NoArgsConstructor
@AllArgsConstructor
@Data
public class AppDistVO {
    private String dist;
    private String name;

    public static AppDistVO createInstance(String dist){
        return new AppDistVO(dist, AppConfig.productEnMap.get(dist).getProductName());
    }
}
