package com.coohua.user.event.admin.service;

import com.alibaba.fastjson.JSON;
import com.coohua.user.event.admin.enums.RiskRules;
import com.coohua.user.event.admin.rsp.ExUserRsp;
import com.coohua.user.event.biz.ap.vo.ExUserRedisVo;
import com.coohua.user.event.biz.click.entity.UserRiskLevelEntity;
import com.coohua.user.event.biz.util.RedisUtil;
import com.coohua.user.event.biz.util.Strings;
import com.coohua.user.event.admin.rsp.UserRiskRsp;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import top.zrbcool.pepper.boot.jediscluster.JedisClusterClient;
import top.zrbcool.pepper.boot.jediscluster.JedisClusterClientRefer;

/**
 * <AUTHOR>
 * @since 2021/9/18
 */
@Slf4j
@Service
public class UserRiskAdminService {

    @JedisClusterClientRefer(namespace = "user-event")
    private JedisClusterClient jedisClusterClient;

    public UserRiskRsp queryUserRisk(Integer appId,Long userId){
        String res = jedisClusterClient.get(RedisUtil.buildUserRiskKey(userId.toString(),appId));
        if (Strings.noEmpty(res)){
            UserRiskLevelEntity userRiskLevelEntity = JSON.parseObject(res,UserRiskLevelEntity.class);
            return new UserRiskRsp(){{
               setAppId(userRiskLevelEntity.getAppId());
               setClick(userRiskLevelEntity.getClick());
               setExposure(userRiskLevelEntity.getExposure());
               setRiskLevel(userRiskLevelEntity.getLevel().toString());
               setUserId(userRiskLevelEntity.getUserId());
            }};
        }
        return new UserRiskRsp(){{
            setAppId(appId);
            setClick(0);
            setExposure(0);
            setRiskLevel("0");
            setUserId(userId.toString());
        }};
    }

    public ExUserRsp queryUserEvent(Integer appId, Long userId){
        String res = jedisClusterClient.get(RedisUtil.buildUserExConfigKey(appId,userId.toString()));
        ExUserRsp vo = new ExUserRsp();
        if (Strings.noEmpty(res)){
            ExUserRedisVo vox = JSON.parseObject(res,ExUserRedisVo.class);
            vo.setRewardRate(vox.getRewardRate().toString());
            vo.setWithdrawRate(vox.getWithdrawRate().toString());
            vo.setVideoLimit(vox.getVideoLimit().toString());
            vo.setUserId(vox.getUserId());
            vo.setReason(RiskRules.AUTO.getId().equals(vox.getRuleId())?RiskRules.AUTO.getDesc():"系统规则");
            vo.setRestrictUser(Boolean.TRUE);
        } else {
            String noLimit ="未限制";
            vo.setRewardRate(noLimit);
            vo.setWithdrawRate(noLimit);
            vo.setVideoLimit(noLimit);
            vo.setRestrictUser(Boolean.FALSE);
        }
        return vo;
    }

    public void updateUserEvent(Integer appId,Long userId,Integer withdrawRate,Integer rewardRate,Integer videoLimit){
        String key = RedisUtil.buildUserExConfigKey(appId,userId.toString());
        jedisClusterClient.set(key,JSON.toJSONString(new ExUserRedisVo(){{
            setVideoLimit(videoLimit);
            setRuleId(RiskRules.AUTO.getId());
            setAppId(appId);
            setUserId(userId.toString());
            setRewardRate(rewardRate);
            setWithdrawRate(withdrawRate);
        }}));
    }

    public void updateUserRisk(Integer appId,Long userId,Integer riskLevel){
        jedisClusterClient.set(RedisUtil.buildUserRiskKey(userId.toString(),appId),
                JSON.toJSONString(new UserRiskLevelEntity(){{
                    setAppId(appId);
                    setUserId(userId.toString());
                    setLevel(riskLevel);
                    setClick(0);
                    setExposure(0);
                }}));
    }
}
