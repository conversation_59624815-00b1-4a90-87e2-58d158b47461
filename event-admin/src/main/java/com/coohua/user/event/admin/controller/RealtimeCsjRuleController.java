package com.coohua.user.event.admin.controller;

import com.coohua.user.event.admin.rsp.BaseResponse;
import com.coohua.user.event.biz.dc.dto.RealtimeCsjRulesDto;
import com.coohua.user.event.biz.dc.service.RealtimeCsjRuleService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2022/10/11
 */
@Slf4j
@RequestMapping("realtime/csj")
@RestController
public class RealtimeCsjRuleController {

    @Autowired
    private RealtimeCsjRuleService realtimeCsjRuleService;

    @PostMapping(value = "list")
    public BaseResponse<List<RealtimeCsjRulesDto>> queryList(){
        BaseResponse<List<RealtimeCsjRulesDto>> response = new BaseResponse<>("OK");
        response.setResult(realtimeCsjRuleService.queryRealTimeRules());
        return response;
    }

    @PostMapping(value = "update")
    public BaseResponse<Void> update(@RequestBody RealtimeCsjRulesDto realtimeRuleRequest){
        realtimeCsjRuleService.updateRealtimeRules(realtimeRuleRequest);
        return new BaseResponse<>("OK");
    }
}
