package com.coohua.user.event.admin.controller;

import com.alibaba.fastjson.JSON;
import com.coohua.user.event.admin.enums.RiskRules;
import com.coohua.user.event.biz.dc.entity.RiskConfigEntity;
import com.coohua.user.event.biz.dc.service.RiskConfigService;
import com.coohua.user.event.admin.req.RiskSaveRequest;
import com.coohua.user.event.admin.rsp.BaseResponse;
import com.coohua.user.event.admin.rsp.RiskInfoRsp;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2021/11/6
 */
@Slf4j
@RequestMapping("config")
@RestController
public class RiskConfigController {

    @Autowired
    private RiskConfigService riskConfigService;


    @PostMapping("list")
    public BaseResponse<List<RiskInfoRsp>> queryList(@RequestParam("product")Integer product,
                                                     @RequestParam("os")Integer os){
        Map<Integer, RiskConfigEntity> riskConfigEntityMap = riskConfigService.queryConfig(product,os);
        List<RiskInfoRsp> riskInfoRspList = Arrays.stream(RiskRules.values())
                .filter(risk -> !RiskRules.AUTO.getId().equals(risk.getId()))
                .map(rule -> {
            RiskInfoRsp riskInfoRsp = new RiskInfoRsp();
            riskInfoRsp.setPr(rule.getPr());
            riskInfoRsp.setDesc(rule.getDesc());
            riskInfoRsp.setType(rule.getType());
            riskInfoRsp.setRuleId(rule.getId());
            riskInfoRsp.setScore(riskConfigEntityMap.getOrDefault(rule.getId(),new RiskConfigEntity(){{
                setScore(0);
            }}).getScore());
            return riskInfoRsp;
        }).collect(Collectors.toList());
        BaseResponse<List<RiskInfoRsp>> baseResponse = new BaseResponse<>("ok");
        baseResponse.setResult(riskInfoRspList);
        return baseResponse;
    }

    @PostMapping("refresh")
    public BaseResponse<String> refreshConfig(@RequestBody RiskSaveRequest request){
        log.info(JSON.toJSONString(request));
        Date now = new Date();
        List<RiskConfigEntity> entities = request.getRuleList()
                .stream()
                .filter(riskInfoRsp -> riskInfoRsp.getScore() > 0)
                .map(riskInfoRsp -> {
                    RiskConfigEntity entity = new RiskConfigEntity();
                    entity.setProduct(request.getProduct());
                    entity.setOs(request.getOs());
                    entity.setRuleId(riskInfoRsp.getRuleId());
                    entity.setScore(riskInfoRsp.getScore());
                    entity.setCreateTime(now);
                    entity.setUpdateTime(now);
                    return entity;
                })
                .collect(Collectors.toList());
        riskConfigService.saveOrUpdate(request.getProduct(),request.getOs(),entities);
        return new BaseResponse<>(0);
    }


}
