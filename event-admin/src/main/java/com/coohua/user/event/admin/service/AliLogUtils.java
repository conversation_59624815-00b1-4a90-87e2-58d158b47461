package com.coohua.user.event.admin.service;

import com.alibaba.fastjson.JSON;
import com.aliyun.openservices.log.Client;
import com.aliyun.openservices.log.common.LogContent;
import com.aliyun.openservices.log.common.QueriedLog;
import com.aliyun.openservices.log.exception.LogException;
import com.aliyun.openservices.log.response.GetLogsResponse;
import com.coohua.user.event.biz.util.DateUtil;
import lombok.extern.slf4j.Slf4j;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2022/3/9
 */
@Slf4j
public class AliLogUtils {
    //配置AccessKey、服务入口、Project名称、Logstore名称等相关信息。
    //阿里云访问密钥AccessKey。更多信息，请参见访问密钥。阿里云账号AccessKey拥有所有API的访问权限，风险很高。强烈建议您创建并使用RAM用户进行API访问或日常运维。
    static String accessId = "LTAI5tNtC1rEPb8xpDzBoQW5";
    static String accessKey = "******************************";
    //日志服务的服务入口。更多信息，请参见服务入口。
    //此处以杭州为例，其它地域请根据实际情况填写。
    static String host = "cn-beijing.log.aliyuncs.com";
    //创建日志服务Client。
    static Client client = new Client(host, accessId, accessKey);
    //Project名称。
    static String projectName = "service-log4j2";
    //Logstore名称。
    static String logstoreName = "pro";

    private static String query = "__source__:bp-user-service and message: \"uploadLbs start\" and %s and message:\"appId=%s\" | select message LIMIT 1";

    private static String queryLockArea = "* and message: REQ-RESULT and message: %s|select COUNT(DISTINCT split(message, ' ', 6)[4])";
    private static String queryLockAreaCount = "* and message: REQ-RESULT and message: %s|select COUNT(*)";

    // 查询登录日志
    public static Map<String,String> queryDeviceLog(Long userId, Integer appId){
        Date now = new Date();
        Long fromTime = DateUtil.dateIncreaseByDay(now,-3).getTime();
        Long toTime = now.getTime();
        String querySql = String.format(query, userId,appId);
        try {
            GetLogsResponse getLogsResponse = client.GetLogs(projectName, logstoreName,
                    fromTime.intValue(), toTime.intValue(), "", querySql);
            if (getLogsResponse == null || getLogsResponse.getLogs().size() == 0){
                return new HashMap<>();
            }
            String result = getLogsResponse.getLogs().get(0).mLogItem.mContents.get(0).mValue;
            List<String> perList = Arrays.stream(result.split("[(]")[1].split("[)]")[0].split(","))
                    .filter(r -> r.contains("deviceId") || r.contains("oaid") || r.contains("androidId") || r.contains("imei"))
                    .filter(r -> !r.contains("null"))
                    .collect(Collectors.toList());
            return perList.stream().filter(r -> r.split("=").length > 1).collect(Collectors.toMap(r-> r.split("=")[0],
                    r->r.split("=")[1],(r1,r2) -> r1));
        } catch (LogException e) {
            e.printStackTrace();
        }
        return new HashMap<>();
    }

    public static Long queryLockAreaLog(String querySqlr,String queryInfo,Long start,Long end){
        String querySql = String.format(querySqlr, queryInfo);
        try {
            GetLogsResponse getLogsResponse = client.GetLogs(projectName, logstoreName,
                    start.intValue(), end.intValue(), "", querySql);
            if (getLogsResponse == null || getLogsResponse.getLogs().size() == 0){
                return 0L;
            }
            String result = getLogsResponse.getLogs().get(0).mLogItem.mContents.get(0).mValue;
            return Long.valueOf(result);
        } catch (LogException e) {
            log.error("Error",e);
            return 0L;
        }
    }

    public static void main(String[] args) {
        queryDeviceLog(532714186L,507);
    }


}
