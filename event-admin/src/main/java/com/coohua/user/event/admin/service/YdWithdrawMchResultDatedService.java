package com.coohua.user.event.admin.service;

import com.baomidou.mybatisplus.extension.service.additional.query.impl.LambdaQueryChainWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.coohua.user.event.admin.rsp.YdWithdrawMchResultDatedVO;
import com.coohua.user.event.biz.dc.dto.Pages;
import com.coohua.user.event.biz.dc.entity.YdWithdrawMchResultDated;
import com.coohua.user.event.biz.dc.mapper.YdWithdrawMchResultDatedMapper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

/**
* <p>
    *  服务实现类
    * </p>
*
* <AUTHOR>
* @since 2021-07-07
*/
@Slf4j
@Service
public class YdWithdrawMchResultDatedService extends ServiceImpl<YdWithdrawMchResultDatedMapper, YdWithdrawMchResultDated> {

    @Autowired
    private YdWithdrawMchResultDatedMapper ydWithdrawMchResultDatedMapper;

    public Pages<YdWithdrawMchResultDatedVO> queryPage(String startDate, String endDate, String orderNo, String openId, Pages<YdWithdrawMchResultDatedVO> pages){
        log.info("开始查询微信订单数据，参数：startDate={}, endDate={}, orderNo={}, openId={}, pageNo={}, pageSize={}", 
                startDate, endDate, orderNo, openId, pages.getPageNo(), pages.getPageSize());
        
        LambdaQueryChainWrapper<YdWithdrawMchResultDated> queryChainWrapper = this.lambdaQuery()
                .eq(StringUtils.isNotBlank(openId), YdWithdrawMchResultDated::getOpenId, openId)
                .eq(StringUtils.isNotBlank(orderNo), YdWithdrawMchResultDated::getOrderNo, orderNo)
                .ge(StringUtils.isNotBlank(startDate), YdWithdrawMchResultDated::getLogday, startDate)
                .le(StringUtils.isNotBlank(endDate), YdWithdrawMchResultDated::getLogday, endDate);

        // 按创建时间倒序排列
        queryChainWrapper.orderByDesc(YdWithdrawMchResultDated::getCreateTime);

        Integer count = queryChainWrapper.count();
        pages.setCount(count);
        
        log.info("查询到总记录数：{}", count);
        
        if (count > 0) {
            queryChainWrapper.last(String.format(" limit %s,%s",(pages.getPageNo()-1)*pages.getPageSize(),pages.getPageSize()));

            List<YdWithdrawMchResultDated> ydWithdrawMchResultDateds = queryChainWrapper.list()
                    .stream()
                    .map(r ->{
                        YdWithdrawMchResultDated response = new YdWithdrawMchResultDated();
                        BeanUtils.copyProperties(r,response);
                        return response;
                    }).collect(Collectors.toList());
            List<YdWithdrawMchResultDatedVO> ydWithdrawMchResultDatedVOs = new ArrayList<>();
            if (CollectionUtils.isNotEmpty(ydWithdrawMchResultDateds)){
                ydWithdrawMchResultDateds.forEach(ydWithdrawMchResultDated -> {
                    YdWithdrawMchResultDatedVO ydWithdrawMchResultDatedVO = new YdWithdrawMchResultDatedVO();
                    BeanUtils.copyProperties(ydWithdrawMchResultDated, ydWithdrawMchResultDatedVO);
                    String appId = ydWithdrawMchResultDatedMapper.getAppId(ydWithdrawMchResultDated.getWechatId());
                    ydWithdrawMchResultDatedVO.setAppName(ydWithdrawMchResultDated.getProductName()+"软件");
                    ydWithdrawMchResultDatedVO.setAppId(appId);
                    ydWithdrawMchResultDatedVOs.add(ydWithdrawMchResultDatedVO);
                });
            }

            pages.setItems(ydWithdrawMchResultDatedVOs);
            
            log.info("返回当前页记录数：{}", ydWithdrawMchResultDateds.size());
        } else {
            pages.setItems(Collections.emptyList());
        }
        
        return pages;
    }

    public List<YdWithdrawMchResultDated> queryAllForExport(String startDate, String endDate, String orderNo, String openId){
        log.info("开始查询微信订单数据用于导出，参数：startDate={}, endDate={}, orderNo={}, openId={}", 
                startDate, endDate, orderNo, openId);
        
        LambdaQueryChainWrapper<YdWithdrawMchResultDated> queryChainWrapper = this.lambdaQuery()
                .eq(StringUtils.isNotBlank(openId), YdWithdrawMchResultDated::getOpenId, openId)
                .eq(StringUtils.isNotBlank(orderNo), YdWithdrawMchResultDated::getOrderNo, orderNo)
                .ge(StringUtils.isNotBlank(startDate), YdWithdrawMchResultDated::getLogday, startDate)
                .le(StringUtils.isNotBlank(endDate), YdWithdrawMchResultDated::getLogday, endDate);

        // 按创建时间倒序排列
        queryChainWrapper.orderByDesc(YdWithdrawMchResultDated::getCreateTime);

        List<YdWithdrawMchResultDated> result = queryChainWrapper.list()
                .stream()
                .map(r ->{
                    YdWithdrawMchResultDated response = new YdWithdrawMchResultDated();
                    BeanUtils.copyProperties(r,response);
                    return response;
                }).collect(Collectors.toList());
        
        log.info("导出查询到记录数：{}", result.size());
        return result;
    }
}
