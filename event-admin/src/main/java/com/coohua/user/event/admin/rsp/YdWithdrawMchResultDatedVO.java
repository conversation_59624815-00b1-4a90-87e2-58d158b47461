package com.coohua.user.event.admin.rsp;

import lombok.Data;

import java.util.Date;

@Data
public class YdWithdrawMchResultDatedVO {
    private Long id;
    private String logday;
    private String product;
    private String productName;
    private String appName;
    private String productGroup;
    private String appId;
    private String orderNo;
    private String batchId;
    private Long userId;
    private String os;
    private Short status;
    private String title;
    private Long channel;
    private Long amount;
    private Long checkAuth;
    private String mchId;
    private Short withdrawType;
    private Long wechatId;
    private String openId;
    private String unionId;
    private Date createTime;
    private Date updateTime;

}
