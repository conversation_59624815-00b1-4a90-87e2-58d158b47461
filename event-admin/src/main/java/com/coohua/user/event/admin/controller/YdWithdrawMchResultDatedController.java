package com.coohua.user.event.admin.controller;

import com.coohua.user.event.admin.rsp.BaseResponse;
import com.coohua.user.event.admin.rsp.YdWithdrawMchResultDatedVO;
import com.coohua.user.event.admin.service.YdWithdrawMchResultDatedService;
import com.coohua.user.event.biz.dc.dto.Pages;
import com.coohua.user.event.biz.dc.entity.YdWithdrawMchResultDated;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.List;

@Slf4j
@RequestMapping("wechatOrder")
@RestController
public class YdWithdrawMchResultDatedController {

    @Autowired
    private YdWithdrawMchResultDatedService ydWithdrawMchResultDatedService;


    @GetMapping("query")
    public BaseResponse<Pages<YdWithdrawMchResultDatedVO>> query(@RequestParam(value = "startDate", required = false) String startDate,
                                                              @RequestParam(value = "endDate", required = false) String endDate,
                                                              @RequestParam(value = "orderNo", required = false) String orderNo,
                                                              @RequestParam(value = "openId", required = false) String openId,
                                                              Pages<YdWithdrawMchResultDatedVO> page) {
        
        // 参数验证：至少有一个查询条件
        if (StringUtils.isBlank(startDate) && StringUtils.isBlank(endDate) && StringUtils.isBlank(orderNo) && StringUtils.isBlank(openId)) {
            BaseResponse<Pages<YdWithdrawMchResultDatedVO>> response = new BaseResponse<>("ERROR");
            response.setMessage("查询参数不能为空，请至少提供日期范围、订单号或OpenID中的一个");
            return response;
        }

        try {
            log.info("查询微信订单数据，参数：startDate={}, endDate={}, orderNo={}, openId={}, pageNo={}, pageSize={}", 
                    startDate, endDate, orderNo, openId, page.getPageNo(), page.getPageSize());
            
            BaseResponse<Pages<YdWithdrawMchResultDatedVO>> response = new BaseResponse<>("OK");
            Pages<YdWithdrawMchResultDatedVO> result = ydWithdrawMchResultDatedService.queryPage(startDate, endDate, orderNo, openId, page);
            response.setResult(result);
            
            log.info("查询微信订单数据成功，返回记录数：{}", result.getCount());
            return response;
        } catch (Exception e) {
            log.error("查询微信订单数据失败", e);
            BaseResponse<Pages<YdWithdrawMchResultDatedVO>> response = new BaseResponse<>("ERROR");
            response.setMessage("查询失败：" + e.getMessage());
            return response;
        }
    }

    @GetMapping("export")
    public void export(@RequestParam(value = "startDate", required = false) String startDate,
                       @RequestParam(value = "endDate", required = false) String endDate,
                       @RequestParam(value = "orderNo", required = false) String orderNo,
                       @RequestParam(value = "openId", required = false) String openId,
                       HttpServletResponse response) {
        
        // 参数验证：至少有一个查询条件
        if (StringUtils.isBlank(startDate) && StringUtils.isBlank(endDate) && StringUtils.isBlank(orderNo) && StringUtils.isBlank(openId)) {
            log.error("导出参数不能为空，请至少提供日期范围、订单号或OpenID中的一个");
            return;
        }

        try {
            log.info("开始导出微信订单数据，参数：startDate={}, endDate={}, orderNo={}, openId={}", 
                    startDate, endDate, orderNo, openId);
            
            // 查询所有数据（不分页）
            List<YdWithdrawMchResultDated> dataList = ydWithdrawMchResultDatedService.queryAllForExport(startDate, endDate, orderNo, openId);
            
            // 生成CSV文件
            generateCsvFile(dataList, response);
            
            log.info("导出微信订单数据成功，导出记录数：{}", dataList.size());
        } catch (Exception e) {
            log.error("导出微信订单数据失败", e);
        }
    }

    private void generateCsvFile(List<YdWithdrawMchResultDated> dataList, HttpServletResponse response) throws IOException {
        // 设置响应头
        String fileName = "微信订单数据_" + new SimpleDateFormat("yyyyMMdd_HHmmss").format(new Date()) + ".csv";
        String encodedFileName = URLEncoder.encode(fileName, StandardCharsets.UTF_8.toString());
        response.setContentType("text/csv;charset=UTF-8");
        response.setHeader("Content-Disposition", "attachment; filename=\"" + encodedFileName + "\"");
        
        // 写入BOM，确保Excel正确识别中文
        response.getOutputStream().write(0xEF);
        response.getOutputStream().write(0xBB);
        response.getOutputStream().write(0xBF);
        
        // 创建CSV内容
        StringBuilder csvContent = new StringBuilder();
        
        // 添加表头
        String[] headers = {
            "logday", "product", "product_name", "product_group", "order_no", "batch_id", "user_id", "os", 
            "status", "title", "channel", "amount", "check_auth", "mch_id", "withdraw_type", 
            "wechat_id", "open_id", "union_id", "create_time", "update_time"
        };
        
        csvContent.append(String.join(",", headers)).append("\n");
        
        // 添加数据行
        SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        for (YdWithdrawMchResultDated data : dataList) {
            StringBuilder row = new StringBuilder();
            
            row.append(escapeCsvField(data.getLogday())).append(",");
            row.append(escapeCsvField(data.getProduct())).append(",");
            row.append(escapeCsvField(data.getProductName())).append(",");
            row.append(escapeCsvField(data.getProductGroup())).append(",");
            row.append(escapeCsvField(data.getOrderNo())).append(",");
            row.append(escapeCsvField(data.getBatchId())).append(",");
            row.append(data.getUserId() != null ? data.getUserId() : "").append(",");
            row.append(escapeCsvField(data.getOs())).append(",");
            row.append(data.getStatus() != null ? data.getStatus() : "").append(",");
            row.append(escapeCsvField(data.getTitle())).append(",");
            row.append(data.getChannel() != null ? data.getChannel() : "").append(",");
            row.append(data.getAmount() != null ? data.getAmount() : "").append(",");
            row.append(data.getCheckAuth() != null ? data.getCheckAuth() : "").append(",");
            row.append(escapeCsvField(data.getMchId())).append(",");
            row.append(data.getWithdrawType() != null ? data.getWithdrawType() : "").append(",");
            row.append(data.getWechatId() != null ? data.getWechatId() : "").append(",");
            row.append(escapeCsvField(data.getOpenId())).append(",");
            row.append(escapeCsvField(data.getUnionId())).append(",");
            row.append(escapeCsvField(data.getCreateTime() != null ? dateFormat.format(data.getCreateTime()) : "")).append(",");
            row.append(escapeCsvField(data.getUpdateTime() != null ? dateFormat.format(data.getUpdateTime()) : ""));
            
            csvContent.append(row.toString()).append("\n");
        }
        
        // 写入响应流
        response.getOutputStream().write(csvContent.toString().getBytes(StandardCharsets.UTF_8));
    }
    
    private String escapeCsvField(String field) {
        if (field == null) {
            return "";
        }
        // 如果字段包含逗号、引号或换行符，需要用引号包围并转义内部引号
        if (field.contains(",") || field.contains("\"") || field.contains("\n")) {
            return "\"" + field.replace("\"", "\"\"") + "\"";
        }
        return field;
    }
}
