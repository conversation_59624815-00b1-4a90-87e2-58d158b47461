package com.coohua.user.event.admin.config;

import com.coohua.user.event.admin.intercepter.CommonInterceptor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.servlet.config.annotation.InterceptorRegistry;
import org.springframework.web.servlet.config.annotation.ResourceHandlerRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;

@Configuration
public class WebConfig {

    @Autowired
    private CommonInterceptor commonInterceptor;

    @Bean
    public WebMvcConfigurer webMvcConfigurer() {
        return new WebMvcConfigurer() {
            // 拦截器配置
            @Override
            public void addInterceptors(InterceptorRegistry registry) {
                registry.addInterceptor(commonInterceptor)
                        .addPathPatterns("/**")
                        .excludePathPatterns("/static/**");
            }

            @Override
            public void addResourceHandlers(ResourceHandlerRegistry registry) {
                registry.addResourceHandler("/static/**").addResourceLocations("classpath:/event/static/");
            }
        };
    }
}
