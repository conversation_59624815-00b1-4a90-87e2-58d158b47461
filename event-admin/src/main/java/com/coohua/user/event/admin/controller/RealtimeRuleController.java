package com.coohua.user.event.admin.controller;

import com.coohua.user.event.admin.rsp.BaseResponse;
import com.coohua.user.event.biz.dc.dto.Pages;
import com.coohua.user.event.biz.dc.dto.RealtimeRuleRequest;
import com.coohua.user.event.biz.dc.dto.RealtimeRuleResponse;
import com.coohua.user.event.biz.dc.service.RealtimeRuleService;
import lombok.extern.slf4j.Slf4j;
import org.apache.ibatis.annotations.Param;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2022/1/26
 */
@Slf4j
@RequestMapping("realtime")
@RestController
public class RealtimeRuleController {

    @Autowired
    private RealtimeRuleService realtimeRuleService;

    @PostMapping(value = "list")
    public BaseResponse<Pages<RealtimeRuleResponse>> queryList(
            @RequestParam(value = "product",required = false) String product,
            Pages<RealtimeRuleResponse> page){
        BaseResponse<Pages<RealtimeRuleResponse>> response = new BaseResponse<>("OK");
        response.setResult(realtimeRuleService.queryList(product,page));
        return response;
    }

    @PostMapping(value = "insert")
    public BaseResponse<Void> insert(@RequestBody RealtimeRuleRequest realtimeRuleRequest){
        realtimeRuleService.insert(realtimeRuleRequest);
        return new BaseResponse<>("OK");
    }

    @PostMapping(value = "update")
    public BaseResponse<Void> update(@RequestBody RealtimeRuleRequest realtimeRuleRequest){
        realtimeRuleService.update(realtimeRuleRequest);
        return new BaseResponse<>("OK");
    }

    @PostMapping(value = "switchFlag")
    public BaseResponse<Void> switchFlag(@RequestParam("id")Integer id,@RequestParam("state")Integer state){
        realtimeRuleService.switchFlag(id,state);
        return new BaseResponse<>("OK");
    }
}
