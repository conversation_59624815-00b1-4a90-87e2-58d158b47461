package com.coohua.user.event.admin.controller;

import com.coohua.user.event.admin.rsp.BaseResponse;
import com.coohua.user.event.admin.rsp.ExUserRsp;
import com.coohua.user.event.admin.rsp.UserRiskRsp;
import com.coohua.user.event.admin.service.UserRiskAdminService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 * @since 2021/9/18
 */
@RequestMapping("risk")
@RestController
public class UserRiskController {

    @Autowired
    private UserRiskAdminService userRiskAdminService;

    @PostMapping("query")
    public BaseResponse<UserRiskRsp> queryUserRisk(@RequestParam("appId")Integer appId,
                                                   @RequestParam("userId")Long userId){
        return BaseResponse.build(userRiskAdminService.queryUserRisk(appId,userId));
    }

    @PostMapping("update")
    public BaseResponse<String> updateUserRisk(@RequestParam("appId")Integer appId,
                                                    @RequestParam("userId")Long userId,
                                                    @RequestParam("riskLevel")Integer riskLevel){
        userRiskAdminService.updateUserRisk(appId,userId,riskLevel);
        return BaseResponse.SUCCEED;
    }

    @PostMapping("queryEvent")
    public BaseResponse<ExUserRsp> queryUserEvent(@RequestParam("appId")Integer appId,
                                                  @RequestParam("userId")Long userId){
        return BaseResponse.build(userRiskAdminService.queryUserEvent(appId,userId));
    }

    @PostMapping("updateEvent")
    public BaseResponse<String> updateUserEvent(@RequestParam("appId")Integer appId,
                                               @RequestParam("userId")Long userId,
                                               @RequestParam("rewardRate")Integer rewardRate,
                                               @RequestParam("withdrawRate")Integer withdrawRate,
                                               @RequestParam("videoLimit")Integer videoLimit){
        userRiskAdminService.updateUserEvent(appId,userId,withdrawRate,rewardRate,videoLimit);
        return BaseResponse.SUCCEED;
    }
}
