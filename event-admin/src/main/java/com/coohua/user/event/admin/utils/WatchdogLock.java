package com.coohua.user.event.admin.utils;

import com.coohua.user.event.biz.util.DingTalkPushUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import top.zrbcool.pepper.boot.jediscluster.JedisClusterClient;
import top.zrbcool.pepper.boot.jediscluster.JedisClusterClientRefer;

import java.util.UUID;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.concurrent.atomic.AtomicReference;

@Slf4j
@Component
public class WatchdogLock {

    @JedisClusterClientRefer(namespace = "user-event")
    private JedisClusterClient jedisClusterClient;


    /**
     * 自动获取锁 + 启动watchdog + 自动释放锁
     */
    public void runWithLock(String key, int expireSeconds, Runnable task) {
        String value = UUID.randomUUID().toString();
        AtomicBoolean taskRunning = new AtomicBoolean(true);

        boolean locked = tryLockWithWatchdog(key, value, expireSeconds, taskRunning);
        if (!locked) {
            log.warn("订单释放获取锁失败，key: {}", key);
            return;
        }

        try {
            task.run();
        } catch (Exception e) {
            log.error("订单释放异常", e);
            throw e;  // 可根据需要决定是否向外抛
        } finally {
            taskRunning.set(false); // 标记任务结束，watchdog 不再续期
            log.info("key为 {} 的任务结束，释放锁", key);
            unlock(key, value);
        }
    }

    public void runAsyncWithLock(String key, int expireSeconds, Runnable task) {
        CompletableFuture.runAsync(() -> runWithLock(key, expireSeconds, task));
    }

    private boolean tryLockWithWatchdog(String key, String value, int expireSeconds, AtomicBoolean taskRunning) {
        String result = jedisClusterClient.set(key, value, "NX", "EX", expireSeconds);
        log.info("watchdog开始运行，key: {}", key);
        if (!"OK".equals(result)) {
            return false;
        }
        long startTime = System.currentTimeMillis();
        AtomicReference<Integer> hour = new AtomicReference<>(0);
        ScheduledExecutorService scheduler = Executors.newSingleThreadScheduledExecutor();


        // 在当前线程里开启定时续期
        scheduler.scheduleAtFixedRate(() -> {
            try {
                if (!taskRunning.get()) {
                    log.info("任务已结束，watchdog 停止续期，key: {}", key);
                    taskRunning.set(false);
                    scheduler.shutdown();
                    return;
                }
                String currentValue = jedisClusterClient.get(key);
                if (value.equals(currentValue)) {
                    jedisClusterClient.expire(key, expireSeconds);
                    log.info("watchdog 延长锁 key: {}", key);
                } else {
                    log.info("watchdog 检测到锁已不属于自己，停止续期，key: {}", key);
                    taskRunning.set(false);
                    scheduler.shutdown();
                    return;
                }

                Long count = ((System.currentTimeMillis() - startTime) / (60 * 1000L)) / 10;
                if (count.intValue() >= 1 && count.intValue() <= 3) {
                    hour.set(count.intValue() * 10);
                    log.info("任务执行超过 " + hour.get() + " 分钟，key: " + key);
                    DingTalkPushUtils.sendMsgReleaseMall("任务执行超过 " + hour.get() + " 分钟，key: " + key);
                }
            } catch (Exception e) {
                log.error("watchdog 续期异常", e);
            }
        }, expireSeconds / 3, expireSeconds / 3, TimeUnit.SECONDS);

        return true;
    }

    private void unlock(String key, String value) {
        String luaScript =
                "if redis.call('get', KEYS[1]) == ARGV[1] then " +
                        "   return redis.call('del', KEYS[1]) " +
                        "else return 0 end";
        jedisClusterClient.eval(luaScript, 1, key, value);
        log.info("锁已释放，key: {}", key);
    }

}
