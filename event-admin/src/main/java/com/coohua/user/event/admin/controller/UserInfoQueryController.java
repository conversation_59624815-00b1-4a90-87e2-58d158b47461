package com.coohua.user.event.admin.controller;

import com.coohua.user.event.admin.rsp.BaseResponse;
import com.coohua.user.event.admin.rsp.ExUserRsp;
import com.coohua.user.event.admin.service.AliLogUtils;
import com.coohua.user.event.admin.service.UserRiskAdminService;
import com.coohua.user.event.biz.service.UserInfoQueryService;
import com.coohua.user.event.biz.service.bean.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @since 2022/3/8
 */
@Slf4j
@RequestMapping("user")
@RestController
public class UserInfoQueryController {

    @Autowired
    private UserInfoQueryService userInfoQueryService;
    @Autowired
    private UserRiskAdminService userRiskAdminService;

    @PostMapping(value = "searchUser")
    public BaseResponse<List<SearchUserInfoRsp>> searchUserInfoList(@RequestParam("appId")Integer appId,
                                                              @RequestParam("userId")Long userId){
        return BaseResponse.build(userInfoQueryService.queryList(userId,appId));
    }

    @PostMapping(value = "getUserInfo")
    public BaseResponse<UserInfoRsp> queryUserInfo(@RequestParam("appId")Integer appId,
                                                   @RequestParam("userId")Long userId){
        return BaseResponse.build(userInfoQueryService.queryUserInfo(userId,appId));
    }

    @PostMapping(value = "getUserArpu")
    public BaseResponse<UserArpuRsp> queryUserArpu(@RequestParam("appId")Integer appId,
                                                   @RequestParam("userId")Long userId){
        return BaseResponse.build(userInfoQueryService.queryUserArpu(userId,appId));
    }


    @PostMapping(value = "getUserGray")
    public BaseResponse<GrayInfoRsp> queryUserGray(@RequestParam("appId")Integer appId,
                                                   @RequestParam("userId")Long userId){
        Map<String,String> queryDevice = AliLogUtils.queryDeviceLog(userId,appId);
        GrayInfoRsp grayInfoRsp = userInfoQueryService.queryGrayInfoRsp(appId,userId,queryDevice);
        ExUserRsp exUserRsp = userRiskAdminService.queryUserEvent(appId,userId);
        if (exUserRsp.getRestrictUser()){
            grayInfoRsp.getGrayDetailDtoList().add(new GrayDetailDto(){{
                setType("广告受限");
                setRemark(exUserRsp.getReason());
                setStatus("生效中");
                setId(userId.toString());
                setCreateTime("未知-需查日志");
            }});
        }

        return BaseResponse.build(grayInfoRsp);
    }

    @PostMapping(value = "getUserWithDraw")
    public BaseResponse<List<UserWithdrawRsp>> queryUserWithDraw(@RequestParam("appId")Integer appId,
                                                                 @RequestParam("userId")Long userId){
        return BaseResponse.build(userInfoQueryService.queryUserWithdrawRsp(appId,userId));
    }


    @PostMapping(value = "changeOcpcFlag")
    public BaseResponse<Boolean> changeOcpcFlag(@RequestBody OcpcInfoBean ocpcInfoBean){

        userInfoQueryService.changeOcpcFlag(ocpcInfoBean);
        return BaseResponse.build(Boolean.TRUE);
    }
}
