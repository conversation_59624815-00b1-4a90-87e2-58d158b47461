package com.coohua.user.event.admin.controller;

import com.alibaba.druid.util.Base64;
import com.coohua.caf.core.util.MD5Util;
import com.coohua.user.event.admin.exception.AdminBsException;
import com.coohua.user.event.admin.rsp.BaseResponse;
import com.coohua.user.event.admin.rsp.LoginDto;
import com.coohua.user.event.biz.dc.entity.LoginUserInfo;
import com.coohua.user.event.biz.dc.service.UserLoginService;
import com.coohua.user.event.biz.util.RSAUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.Cookie;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.HashMap;
import java.util.Map;
import java.util.UUID;

/**
 * <AUTHOR>
 * @since 2021/9/16
 */
@RequestMapping("api")
@RestController
public class AdminController {

    private static String COOKIE_NAME = "user_event_admin";
    @Autowired
    private UserLoginService userLoginService;
    @PostMapping(value = "login")
    public BaseResponse<LoginDto> loginToSystem(@RequestParam("uname")String userName,
                                                @RequestParam("pwd") String passwWord,
                                                HttpServletRequest request, HttpServletResponse response) throws Exception {
        LoginUserInfo userInfo = userLoginService.checkLogin(userName);
        if (userInfo!= null && userInfo.getPwd().equals(passwWord)){
            LoginDto dto = new LoginDto();
            String uuid = UUID.randomUUID().toString();
            dto.setToken(uuid);
            dto.setShowName(userInfo.getUserName());

            String cookieValue = userInfo.getId() + "|" + userInfo.getUserName() + "|" + userInfo.getUserName() + "|" + uuid;
            // set session
            request.getSession().setAttribute(COOKIE_NAME, cookieValue);

            // set cookie
            String secret = Base64.byteArrayToBase64(RSAUtils.encryptByPublicKey(cookieValue.getBytes(), RSAUtils.DEFAULT_RSA_PUBLIC_KEY_BASE64));

            Cookie cookie = new Cookie(COOKIE_NAME, secret);
            cookie.setPath("/");
            cookie.setMaxAge(Integer.MAX_VALUE); // Cookie存活最大值
            response.addCookie(cookie);

            return BaseResponse.build(dto);
        }else {
            throw new AdminBsException(40000, "账户名或者密码错误，请重新登录");
        }
    }

    @PostMapping("check")
    public BaseResponse<String> check(){
        return BaseResponse.SUCCEED;
    }

}
