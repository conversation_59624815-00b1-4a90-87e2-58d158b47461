package com.coohua.user.event.admin.controller;

import com.coohua.user.event.admin.rsp.AppShortNameVO;
import com.coohua.user.event.admin.rsp.BaseResponse;
import com.coohua.user.event.admin.rsp.UserAdSwitchRsp;
import com.coohua.user.event.biz.core.dto.req.AddAdConfigReq;
import com.coohua.user.event.biz.core.dto.req.AddUserAdConfig;
import com.coohua.user.event.biz.core.dto.rsp.AdFxConfigRsp;
import com.coohua.user.event.biz.core.service.AdFxConfigService;
import com.coohua.user.event.biz.dc.dto.Pages;
import com.coohua.user.event.biz.dc.dto.RealtimeRuleResponse;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2023/6/27
 */
@RequestMapping("ad")
@RestController
public class AdConfigController {

    @Autowired
    private AdFxConfigService adFxConfigService;

    @PostMapping(value = "list")
    public BaseResponse<Pages<AdFxConfigRsp>> queryList(
            @RequestParam(value = "product",required = false) String product,
            Pages<AdFxConfigRsp> page){
        BaseResponse<Pages<AdFxConfigRsp>> response = new BaseResponse<>("OK");
        response.setResult(adFxConfigService.queryList(product,page));
        return response;
    }

    // 用户批量操作
    @PostMapping("batchUserAdConfig")
    public BaseResponse<Boolean> batchSetUserSwitch(@RequestBody AddUserAdConfig addUserAdConfig){
        BaseResponse<Boolean> response = new BaseResponse<>(0);
        Boolean result = adFxConfigService.addUserAdConfig(addUserAdConfig);
        response.setResult(result);
        return response;
    }


    // 新增
    @PostMapping("addOne")
    public BaseResponse<Boolean> addNewAdConfig(@RequestBody AddAdConfigReq addAdConfigReq){
        Boolean result = adFxConfigService.addNewAdConfig(addAdConfigReq);
        BaseResponse<Boolean> response = new BaseResponse<>(0);
        response.setResult(result);
        return response;
    }

    // 编辑
    @PostMapping("editOne")
    public BaseResponse<Boolean> editAdConfig(@RequestBody AddAdConfigReq addAdConfigReq){
        BaseResponse<Boolean> response = new BaseResponse<>(0);
        Boolean result = adFxConfigService.editAdConfig(addAdConfigReq);
        response.setResult(result);
        return response;
    }

    // 开关切换
    @PostMapping("switchFlag")
    public BaseResponse<Boolean> switchAdConfigState(@RequestParam("id")Integer Id,@RequestParam("state")Integer state){
        BaseResponse<Boolean> response = new BaseResponse<>(0);
        Boolean result = adFxConfigService.switchStateAdConfig(Id,state);
        response.setResult(result);
        return response;
    }
}
