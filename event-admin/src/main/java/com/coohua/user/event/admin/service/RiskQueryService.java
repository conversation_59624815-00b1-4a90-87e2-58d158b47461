package com.coohua.user.event.admin.service;

import com.coohua.user.event.biz.click.mapper.ClickHouseRiskMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @since 2021/11/19
 */
@Slf4j
@Service
public class RiskQueryService {

    @Resource
    private ClickHouseRiskMapper clickHouseRiskMapper;

    public Integer queryCount(){
        return 0;
    }

}
