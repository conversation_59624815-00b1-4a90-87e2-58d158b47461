package com.coohua.user.event.admin.controller;

import com.coohua.user.event.admin.rsp.AppDistVO;
import com.coohua.user.event.admin.rsp.AppShortNameVO;
import com.coohua.user.event.admin.rsp.BaseResponse;
import com.coohua.user.event.biz.util.AppConfig;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2021/11/6
 */
@RequestMapping("dist")
@RestController
public class DistController {

    @RequestMapping("/appList")
    public BaseResponse<List<AppShortNameVO>> shortNameList() {
        BaseResponse<List<AppShortNameVO>> response = new BaseResponse<>(0);
        List<AppShortNameVO> ret = new ArrayList<>();
        AppConfig.appIdMap.forEach((id,pr) ->{
            AppShortNameVO shortNameVO = new AppShortNameVO();
            shortNameVO.setValue(Math.toIntExact(id));
            shortNameVO.setLabel(pr.getProductName());
            ret.add(shortNameVO);
        });

        response.setResult(ret);
        return response;
    }

    @RequestMapping("/appDistList")
    public BaseResponse<List<AppDistVO>> getAppDistList() {
        BaseResponse<List<AppDistVO>> response = new BaseResponse<>(0);
        List<AppDistVO> ret = new ArrayList<>();
        AppConfig.appIdMap.forEach((id,pr) ->{
            AppDistVO temp = new AppDistVO();
            temp.setDist(pr.getProduct());
            temp.setName(pr.getProductName());
            ret.add(temp);
        });

        if(ret.isEmpty()){
            ret.add(new AppDistVO("ceshi", "测试"));
        }

        response.setResult(ret);
        return response;
    }
}
