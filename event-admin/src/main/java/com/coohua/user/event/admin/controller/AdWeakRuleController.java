package com.coohua.user.event.admin.controller;

import com.coohua.user.event.admin.rsp.AppDistVO;
import com.coohua.user.event.admin.rsp.BaseResponse;
import com.coohua.user.event.admin.service.AdWeakRuleService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import java.util.List;

@Slf4j
@RequestMapping("adWeakRule")
@RestController
public class AdWeakRuleController {

    @Autowired
    private AdWeakRuleService service;

    @PostMapping(value = "query")
    public BaseResponse<List<List<AppDistVO>>> queryList(){
        BaseResponse<List<List<AppDistVO>>> response = new BaseResponse<>("OK");
        response.setResult(service.query());
        return response;
    }

    @PostMapping(value = "insert")
    public BaseResponse<Void> insert(HttpServletRequest request, String product){
        Object user = request.getSession().getAttribute("user_event_admin");
        service.insert(product, user);
        return new BaseResponse<>("OK");
    }

    @PostMapping(value = "delete")
    public BaseResponse<Void> update(HttpServletRequest request, String product){
        Object user = request.getSession().getAttribute("user_event_admin");
        service.delete(product, user);
        return new BaseResponse<>("OK");
    }

    @PostMapping(value = "insertB")
    public BaseResponse<Void> insertB(HttpServletRequest request, String product){
        Object user = request.getSession().getAttribute("user_event_admin");
        service.insertB(product, user);
        return new BaseResponse<>("OK");
    }

    @PostMapping(value = "deleteB")
    public BaseResponse<Void> updateB(HttpServletRequest request, String product){
        Object user = request.getSession().getAttribute("user_event_admin");
        service.deleteB(product, user);
        return new BaseResponse<>("OK");
    }
}
