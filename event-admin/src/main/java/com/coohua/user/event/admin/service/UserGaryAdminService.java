package com.coohua.user.event.admin.service;

import com.coohua.user.event.admin.rsp.UserGrayRsp;
import com.coohua.user.event.biz.dc.entity.UserGrayIpEntity;
import com.coohua.user.event.biz.dc.mapper.UserGrayIpMapper;
import com.coohua.user.event.biz.util.DateUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2021/9/22
 */
@Service
@Slf4j
public class UserGaryAdminService {

    @Resource
    private UserGrayIpMapper userGrayIpMapper;

    public List<UserGrayRsp> queryUserGrayInfo(String targetIds, Integer targetType){
        List<String> targetIdList = Arrays.asList(targetIds.split(","));
        List<UserGrayIpEntity> userGrayIpEntityList = userGrayIpMapper.queryList(null,targetType,targetIdList);
        return userGrayIpEntityList.stream()
                .map(userGrayIpEntity -> {
                    UserGrayRsp rsp = new UserGrayRsp();
                    rsp.setProduct(userGrayIpEntity.getProduct());
                    rsp.setTargetId(userGrayIpEntity.getTargetId());
                    rsp.setTargetType(userGrayIpEntity.getTargetType());
                    rsp.setReason(userGrayIpEntity.getRemark());
                    rsp.setCreateTime(DateUtil.dateToStringWithTime(userGrayIpEntity.getCreateTime()));
                    return rsp;
                }).collect(Collectors.toList());
    }
}
