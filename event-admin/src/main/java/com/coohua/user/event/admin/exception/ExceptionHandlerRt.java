package com.coohua.user.event.admin.exception;

import com.coohua.user.event.admin.rsp.BaseResponse;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestControllerAdvice;

/**
 * <AUTHOR>
 * @since 2021/9/18
 */
@RestControllerAdvice
public class ExceptionHandlerRt {

    @ExceptionHandler(Exception.class)
    @ResponseBody
    public BaseResponse<String> exception(Exception e){
        if (e instanceof AdminBsException){
            AdminBsException ex = (AdminBsException) e;
            return BaseResponse.build(ex.getCode(),ex.getMessage(),null);
        }
        return BaseResponse.ERROR;
    }
}
