package com.coohua.user.event.admin.service;


import com.coohua.user.event.admin.rsp.AppDistVO;
import com.ctrip.framework.apollo.core.ConfigConsts;
import com.ctrip.framework.apollo.openapi.client.ApolloOpenApiClient;
import com.ctrip.framework.apollo.openapi.dto.OpenItemDTO;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

@Service
@Slf4j
public class AdWeakRuleService {
    @Resource(name = "apolloClient")
    private ApolloOpenApiClient client;
    private static final String APP_ID = "core-dispense";
    private static String ENV = "FAT";

    private static final String sudproducts = "ocpc.filter.sudproducts";
    private static final String sudproductsB = "ocpc.filter.sudBproducts";

    private OpenItemDTO apolloGet(String key){
        return client.getItem(APP_ID,ENV, ConfigConsts.CLUSTER_NAME_DEFAULT,ConfigConsts.NAMESPACE_APPLICATION,key);
    }

    public List<List<AppDistVO>> query() {
        String valueA = null;
        String valueB = null;
        List<AppDistVO> aList = null;
        List<AppDistVO> bList = null;
        try {
            valueA = getValue(sudproducts);
            valueB = getValue(sudproductsB);
            for(String temp : valueA.split(",")){
                aList.add(AppDistVO.createInstance(temp));
            }
            for(String temp : valueB.split(",")){
                bList.add(AppDistVO.createInstance(temp));
            }
        } catch (Exception e) {
            log.error("query ", e);
            if("DEV".equals(ENV)){
                ENV = "PRO";
            }else{
                ENV = "DEV";
            }
        }

        if(aList == null || aList.isEmpty()){
            aList = Lists.newArrayList(new AppDistVO("ceshi", "测试"));
        }
        if(bList == null || bList.isEmpty()){
            bList = Lists.newArrayList(new AppDistVO("ceshi", "测试"));
        }

        List<List<AppDistVO>> res = Lists.newArrayList(
                aList
                , bList
        );

        return res;
    }

    private String getValue(String sudproducts) {

        OpenItemDTO openItemDTO = client.getItem(APP_ID, ENV, ConfigConsts.CLUSTER_NAME_DEFAULT, ConfigConsts.NAMESPACE_APPLICATION, sudproducts);

        String valueA = openItemDTO.getValue();
        if (valueA == null) {
            valueA = "";
        }
        return valueA;
    }

    private void apolloPut(String appInfo, String sudproducts, Object user){
        try {
            OpenItemDTO itemDTO = new OpenItemDTO();
            itemDTO.setKey(sudproducts);
            itemDTO.setDataChangeLastModifiedTime(new Date());
            itemDTO.setComment(String.valueOf(user));
            client.createOrUpdateItem(APP_ID,ENV,
                    ConfigConsts.CLUSTER_NAME_DEFAULT,
                    ConfigConsts.NAMESPACE_APPLICATION, itemDTO);
        } catch (Exception e) {
            log.error("apolloPut ", e);
            if("DEV".equals(ENV)){
                ENV = "PRO";
            }else{
                ENV = "DEV";
            }
        }
    }


    public void insert(String product, Object user) {
        String value = getValue(sudproducts);
        if(StringUtils.isNotBlank(value)){
            value = value + "," + product;
        }else{
            value = product;
        }
        apolloPut(value, sudproducts, user);
    }

    public void delete(String product, Object user) {
        String value = getValue(sudproductsB);
        if(StringUtils.isBlank(value)){
            return;
        }
        String newValue = Arrays.stream(value.split(",")).filter(s -> {
            return !StringUtils.equals(s, product);
        }).collect(Collectors.joining(","));

        apolloPut(newValue, sudproducts, user);
    }

    public void insertB(String product, Object user) {
        String value = getValue(sudproductsB);
        if(StringUtils.isNotBlank(value)){
            value = value + "," + product;
        }else{
            value = product;
        }
        apolloPut(product, sudproductsB, user);
    }

    public void deleteB(String product, Object user) {
        String value = getValue(sudproductsB);
        if(StringUtils.isBlank(value)){
            return;
        }
        String newValue = Arrays.stream(value.split(",")).filter(s -> {
            return !StringUtils.equals(s, product);
        }).collect(Collectors.joining(","));

        apolloPut(product, sudproductsB, user);
    }
}
