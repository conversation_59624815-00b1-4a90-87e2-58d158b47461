package com.coohua.user.event.admin.service;

import com.aliyun.openservices.shade.com.google.common.util.concurrent.RateLimiter;
import com.coohua.bp.mall.remote.api.MallRPC;
import com.coohua.user.event.admin.utils.WatchdogLock;
import com.coohua.user.event.biz.config.ThreadPoolConf;
import com.coohua.user.event.biz.core.entity.WithdrawNotSend;
import com.coohua.user.event.biz.core.mapper.WithdrawNotSendMapper;
import com.coohua.user.event.biz.dc.entity.WithdrawOrderDetail;
import com.coohua.user.event.biz.user.service.BpMallService;
import com.coohua.user.event.biz.util.DingTalkPushUtils;
import com.ctrip.framework.apollo.spring.annotation.ApolloJsonValue;
import com.weibo.api.motan.config.springsupport.annotation.MotanReferer;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.hssf.usermodel.HSSFWorkbook;
import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;
import top.zrbcool.pepper.boot.jediscluster.JedisClusterClient;
import top.zrbcool.pepper.boot.jediscluster.JedisClusterClientRefer;

import java.io.IOException;
import java.io.InputStream;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

@Slf4j
@Service
public class DelayOrderService {

    @Autowired
    private WithdrawNotSendMapper withdrawNotSendMapper;
    @JedisClusterClientRefer(namespace = "user-event")
    private JedisClusterClient userEventJedisClusterClient;
    @Autowired
    private WatchdogLock watchdogLock;
    @Autowired
    private BpMallService bpMallService;
    @ApolloJsonValue("${delay.order.release.qps:17.0}")
    private Double orderReleaseQps = new Double(17.0);
    @Value("${order.release.batch.quantity:50}")
    private Integer orderReleaseBatchQuantity;

    @MotanReferer(basicReferer = "bp-mallBasicRefererConfigBean")
    private MallRPC mallRPC;

    public static final String XLS = "xls";
    public static final String XLSX = "xlsx";
    private RateLimiter MAX_CAN_SEND_UPDATE = RateLimiter.create(orderReleaseQps);



    @SneakyThrows
    public String batchCanSend(MultipartFile file, String fileType) {

        List<WithdrawNotSend> withdrawNotSends = convertExcel(file, fileType);
        if (withdrawNotSends.isEmpty()) {
            return "上传内容为空";
        }
        String headerOrderNo = withdrawNotSends.get(0).getOrderNo();
        String lockKey = "order:release:" + headerOrderNo;
        String lock = userEventJedisClusterClient.get(lockKey);
        if (StringUtils.isNotBlank(lock)) {
            return ("订单id开头为" + headerOrderNo + "的任务已在运行中");
        }

        AtomicInteger releaseSize = new AtomicInteger();
        CompletableFuture.runAsync(() -> executeReleaseTask(withdrawNotSends, headerOrderNo, releaseSize));
        return "上传成功";
    }


    public void executeReleaseTask(List<WithdrawNotSend> withdrawNotSends, String headerOrderNo, AtomicInteger releaseSize) {
        AtomicBoolean taskRunning = new AtomicBoolean(true);
        AtomicInteger lastReportedCount = new AtomicInteger(0);

        try {
            DingTalkPushUtils.sendMsgReleaseMall("本批次以订单id为 " + headerOrderNo + " 开头的文件开始释放订单");
            List<CompletableFuture> taskList = new ArrayList<>();
            Map<String, Integer> productAndAppId = withdrawNotSendMapper.getProductAndAppId().stream().collect(Collectors.toMap(WithdrawNotSend::getProduct, WithdrawNotSend::getAppId, (r1, r2) -> r2));
            Map<String, List<WithdrawNotSend>> withdrawNotSendMap = withdrawNotSends.stream().collect(Collectors.groupingBy(WithdrawNotSend::getProduct));
            withdrawNotSendMap.forEach((product, canSends) -> {
                taskList.add(CompletableFuture.runAsync(() -> {
                    Integer appId = productAndAppId.get(product);
                    List<String> appOrderNoList = canSends.stream().map(WithdrawNotSend::getOrderNo).collect(Collectors.toList());
                    List<WithdrawOrderDetail> withdrawOrderDetails = new ArrayList<>();
                    if (appOrderNoList.size() < orderReleaseBatchQuantity){
                        canSends.forEach(x -> {
                            WithdrawOrderDetail withdrawOrderDetail = bpMallService.queryOrder("bp_mall.withdraw_order_" + appId, Long.valueOf(x.getUserId()), x.getOrderNo());
                            if (withdrawOrderDetail != null){
                                withdrawOrderDetails.add(withdrawOrderDetail);
                            }
                        });
                    }else {
                        List<WithdrawOrderDetail> batchWithdrawDetails = bpMallService.queryOrder("bp_mall.withdraw_order_" + appId, appOrderNoList);
                        if (CollectionUtils.isNotEmpty(batchWithdrawDetails)){
                            withdrawOrderDetails.addAll(batchWithdrawDetails);
                        }
                    }

                    if (withdrawOrderDetails.isEmpty()) {
                        return;
                    }
                    Map<String, Long> orderNoAndId = withdrawOrderDetails.stream()
                            .collect(Collectors.toMap(WithdrawOrderDetail::getOrderNo, WithdrawOrderDetail::getId, (r1, r2) -> r2));
                    canSends.forEach(canSend -> {
                        if (orderNoAndId.get(canSend.getOrderNo()) != null) {
                            CompletableFuture.runAsync(() -> {
                                withdrawNotSendMapper.updateCanSend(appId, canSend.getUserId(), canSend.getOrderNo());
                                orderToCheck(Long.valueOf(appId), orderNoAndId.get(canSend.getOrderNo()));
                                releaseSize.getAndIncrement();
                                int currentCount = releaseSize.get();
                                int lastReported = lastReportedCount.get();
                                if (currentCount >= lastReported + 3000) {
                                    DingTalkPushUtils.sendMsgReleaseMall("以订单id为 " + headerOrderNo + " 开头的文件已成功释放订单" + currentCount + " 个");
                                    lastReportedCount.set(currentCount);
                                }
                                log.info("订单 {} 已成功批量推送审核", canSend.getOrderNo());
                            }, ThreadPoolConf.RELEASE_CAN_SEND_MALL);
                        }
                    });
                }, ThreadPoolConf.RELEASE_CAN_SEND_MALL_BY_PRODUCT));
            });
            CompletableFuture.allOf(taskList.toArray(new CompletableFuture[0])).join();
        } catch (Exception e) {
            log.error("批量释放拦截订单失败", e);
            DingTalkPushUtils.sendMsgReleaseMall("本批次以订单id为 " + headerOrderNo + " 开头的文件释放失败，请尝试调整文件格式重新上传");
        } finally {
            taskRunning.set(false);
        }
        DingTalkPushUtils.sendMsgReleaseMall("以订单id为 " + headerOrderNo + " 开头的文件已释放完毕，数量为:" + releaseSize + " 个");

    }

    private void orderToCheck(Long appId, Long orderId) {
        int maxAttempts = 3;
        int attempts = 0;
        while (attempts < maxAttempts) {
            try {
                pushOrderToCheck(appId, orderId);
                return;
            } catch (RuntimeException ex) {
                attempts++;
                log.error("订单 {} 第{}次重试", orderId, attempts);
                if (attempts >= maxAttempts) {
                    throw ex;
                }
                try {
                    Thread.sleep(10);
                } catch (InterruptedException ignored) {}
            }
        }
    }

    public void pushOrderToCheck(Long appId, Long id) {
        MAX_CAN_SEND_UPDATE.acquire();

        mallRPC.pushOrderToCheck(appId, id);
    }

    public List<WithdrawNotSend> convertExcel(MultipartFile file, String fileType) throws IOException {


        InputStream inputStream = file.getInputStream();

        List<WithdrawNotSend> withdrawNotSends = new ArrayList<>();

        Workbook workbook = new HSSFWorkbook();
        if(fileType.equalsIgnoreCase(XLS)){
            workbook = new HSSFWorkbook(inputStream);
        }else if(fileType.equalsIgnoreCase(XLSX)){
            workbook = new XSSFWorkbook(inputStream);
        }

        if (workbook == null){
            return withdrawNotSends;
        }

        Sheet sheet = workbook.getSheetAt(0); // 获取第一个工作表

        // 遍历从第二行（数据从第二行开始）开始的所有行
        for (int i = 1; i <= sheet.getLastRowNum(); i++) {
            Row row = sheet.getRow(i);  // 获取当前行

            if (row != null) {
                // 获取前三列的数据（假设列是从 0 开始的）
                String product = getCellValue(row.getCell(0));
                Long userId = Long.valueOf(getCellValue(row.getCell(1)));
                String orderNo = getCellValue(row.getCell(2));
                withdrawNotSends.add(new WithdrawNotSend(product, userId, orderNo));
            }
        }

        workbook.close();
        inputStream.close();
        return withdrawNotSends;
    }

    private static String getCellValue(Cell cell) {
        if (cell == null) {
            return "";
        }
        switch (cell.getCellType()) {
            case STRING:
                return cell.getStringCellValue();
            case NUMERIC:
                long longValue = (long) cell.getNumericCellValue(); // 强制转换为 long 类型
                return String.valueOf(longValue);
            default:
                return "";
        }
    }
}
