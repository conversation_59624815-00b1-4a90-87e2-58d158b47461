package com.coohua.user.event.admin.rsp;

import lombok.Data;

/**
 * <AUTHOR>
 * @since 2021/9/18
 */
@Data
public class BaseResponse<T> {

    private Integer code;
    private String message;
    private T result;

    private static final Integer S_OK = 0;
    private static final Integer S_ERR = 500;
    private static final String ERR_DES = "sada";

    public static final BaseResponse<String> ERROR = build(S_ERR,"哎呀,请求失败啦",null);
    public static final BaseResponse<String> SUCCEED = build(S_OK,"好耶,请求成功啦",null);


    public BaseResponse() {
    }

    public BaseResponse(Integer ret) {
        this.code = ret;
    }


    public BaseResponse(String message) {
        this.code = S_OK;
        this.message = message;
    }

    public BaseResponse(Integer code, String message, T result) {
        this.code = code;
        this.message = message;
        this.result = result;
    }

    public static <T> BaseResponse<T> build(String msg){
        return new BaseResponse<>(msg);
    }

    public static <T> BaseResponse<T> build(T result){
        return new BaseResponse<>(S_OK,"成功处理",result);
    }

    public static <T> BaseResponse<T> build(Integer code,String msg,T result){
        return new BaseResponse<>(code,msg,result);
    }
}
