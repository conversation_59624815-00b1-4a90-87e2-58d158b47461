package com.coohua.user.event.admin.controller;

import com.coohua.user.event.admin.req.GrayRequest;
import com.coohua.user.event.admin.rsp.BaseResponse;
import com.coohua.user.event.admin.rsp.UserGrayRsp;
import com.coohua.user.event.biz.click.entity.ProductEntity;
import com.coohua.user.event.biz.service.UserGrayService;
import com.coohua.user.event.admin.service.UserGaryAdminService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2021/9/22
 */
@RestController
@RequestMapping("gray")
public class UserGrayController {

    @Autowired
    private UserGaryAdminService userGaryAdminService;
    @Autowired
    private UserGrayService userGrayService;


    @PostMapping("query")
    public BaseResponse<List<UserGrayRsp>> queryUserRisk(@RequestParam("targetIds")String targetIds,
                                                         @RequestParam("targetType")Integer targetType){
        return BaseResponse.build(userGaryAdminService.queryUserGrayInfo(targetIds,targetType));
    }

    @PostMapping("doGray")
    public BaseResponse<String> doGray(@RequestBody GrayRequest grayRequest, HttpServletRequest request){
        userGrayService.autoGrayUser(grayRequest.getTargetIds(),grayRequest.getTargetType()
                ,grayRequest.getAppId(),grayRequest.getIsGlobalGray());
        return new BaseResponse<>("ok");
    }

    @PostMapping("doUnGray")
    public BaseResponse<String> doUnGray(@RequestBody GrayRequest grayRequest, HttpServletRequest request){
        userGrayService.autoUnGrayUser(grayRequest.getTargetIds(),grayRequest.getTargetType());
        return new BaseResponse<>("ok");
    }
}
