package com.coohua.user.event.admin.intercepter;

import com.alibaba.druid.util.Base64;
import com.coohua.user.event.admin.exception.AdminBsException;
import com.coohua.user.event.biz.dc.entity.LoginUserInfo;
import com.coohua.user.event.biz.dc.service.UserLoginService;
import com.coohua.user.event.biz.util.RSAUtils;
import com.coohua.user.event.biz.util.Strings;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.web.servlet.handler.HandlerInterceptorAdapter;

import javax.servlet.http.Cookie;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

/**
 * <AUTHOR>
 * @since 2021/9/18
 */
@Slf4j
@Component
public class CommonInterceptor extends HandlerInterceptorAdapter {

    @Autowired
    private UserLoginService userLoginService;

    @Override
    public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler) {
        try {
            String requestURI = request.getRequestURI();
            if (requestURI.contains("/api/login") || requestURI.contains("/wechatOrder")) { // 不拦截登录接口
                return true;
            }

            String value = "";
            Cookie[] cookies = request.getCookies();
            if (cookies == null){
                throw new RuntimeException("当前账户未登录！");
            }
            for (Cookie cookie : cookies) {
                if ("user_event_admin".equals(cookie.getName())) {
                    value = cookie.getValue();
                }
            }

            if (StringUtils.isNotEmpty(value)) {
                String decryptValue = new String(RSAUtils.decryptByPrivateKey(Base64.base64ToByteArray(value), RSAUtils.DEFAULT_RSA_PRIVATE_KEY_BASE64));
                //与session里的值比较。
                Object valueFromSession = request.getSession().getAttribute("user_event_admin");
                try {
                    if (Strings.isEmpty(valueFromSession)){
                        //
                        String[] prc = decryptValue.split("[|]");
                        LoginUserInfo loginUserInfo = userLoginService.checkLoginById(prc[0]);
                        if (loginUserInfo!= null){
                            request.getSession().setAttribute("user_event_admin", decryptValue);
                            valueFromSession = decryptValue;
                            log.info("Session失效  自动重新设置 {} ",decryptValue);
                        }
                    }
                    if (!decryptValue.equals(valueFromSession)){
                        log.info("ValueSession {} , Cookie {} 不一致,请注意查看",valueFromSession,decryptValue);
                        return false;
                    }
                    return true;
                }catch (Exception e){
                    throw new RuntimeException("当前账户未登录！");
                }
            }
        }catch (Exception e){
            log.error("LoginEx:",e);
        }

        throw new AdminBsException(500,"糟糕，没有登录无法查看..");
    }

}