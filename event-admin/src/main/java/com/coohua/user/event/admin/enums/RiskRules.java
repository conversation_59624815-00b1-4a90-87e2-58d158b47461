package com.coohua.user.event.admin.enums;

/**
 * <AUTHOR>
 * @since 2021/11/6
 */
public enum RiskRules {
    AUTO(99999,"手动处理","后台手动处理","用户数据后台手动处理"),
    IP_RULE_1(10000,"IP维度","IP下用户聚集","同一个IP下用户数量大于等于20个"),
    IP_RULE_2(10001,"IP维度","IP下用户聚集","同一个IP下用户数量大于8个小于20个"),
    IP_RULE_3(10010,"IP维度","历史风险IP字段","IP字段命中近30日异常IP字段"),
    IP_RULE_4(10011,"IP维度","历史风险IP字段","IP字段命中近7日异常IP字段"),
    IP_RULE_5(10020,"IP维度","IP变动","第N-1日或者第N日IP变动次数>5"),
    DEVICE_RULE_1(20000,"设备维度","机型异常","以产品为维度，第N-1日命中全局类规则2/3的用户机型为异常机型，对第N日此机型的非投放用户登录打分"),
    DEVICE_RULE_2(20010,"设备维度","信息异常","userid对应device_id数量>5"),
    DEVICE_RULE_3(20011,"设备维度","信息异常","device_id对应user数量>5"),
    DEVICE_RULE_4(20012,"设备维度","信息异常","imei获取不到"),
    DEVICE_RULE_5(20013,"设备维度","信息异常","userid获取不到"),
    DEVICE_RULE_6(20014,"设备维度","信息异常","device_id获取不到"),
    DEVICE_RULE_7(20020,"设备维度","权限异常","浮窗：ad_page"),
    DEVICE_RULE_8(20021,"设备维度","权限异常","无障碍：element_name"),
    DEVICE_RULE_9(20022,"设备维度","权限异常","Root：element_page"),
    DEVICE_RULE_10(20023,"设备维度","权限异常","USB：调试element_uri"),
    DEVICE_RULE_11(20030,"设备维度","网赚APP过多","device_id出现在自家产品数量>5个"),
    DEVICE_RULE_12(20031,"设备维度","网赚APP过多","device_id出现在自家产品数量>8个"),
    CHANNEL_RULE_1(30000,"来源渠道维度"," 投放渠道","渠道号csj，tt，ks，tx，gdt, 但无OCPC标识"),
    CHANNEL_RULE_2(30001,"来源渠道维度"," 应用市场","渠道号为HUAWEI|OPPO|VIVO, 但manufacutre不与渠道号对应"),
    ;

    private Integer id;
    private String pr;
    private String type;
    private String desc;

    RiskRules(Integer id, String pr, String type, String desc) {
        this.id = id;
        this.pr = pr;
        this.type = type;
        this.desc = desc;
    }

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getPr() {
        return pr;
    }

    public void setPr(String pr) {
        this.pr = pr;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public String getDesc() {
        return desc;
    }

    public void setDesc(String desc) {
        this.desc = desc;
    }
}
