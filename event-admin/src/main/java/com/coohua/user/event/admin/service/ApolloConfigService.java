package com.coohua.user.event.admin.service;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.ctrip.framework.apollo.core.ConfigConsts;
import com.ctrip.framework.apollo.openapi.client.ApolloOpenApiClient;
import com.ctrip.framework.apollo.openapi.dto.NamespaceReleaseDTO;
import com.ctrip.framework.apollo.openapi.dto.OpenClusterDTO;
import com.ctrip.framework.apollo.openapi.dto.OpenItemDTO;
import com.ctrip.framework.apollo.openapi.dto.OpenReleaseDTO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.util.*;

/**
 * <AUTHOR>
 * @since 2022/6/21
 * a
 */
@Slf4j
@Service
public class ApolloConfigService {

    @Resource(name = "apolloClient")
    private ApolloOpenApiClient client;
    private static final String APP_ID = "ap.gateway";
    private static final String APP_ID_USER = "bp-user-service";
    private static final String ENV = "PRO";

    public List<Integer> getUserBiddingSkip(Integer appId, Long userId){
        String key =  String.format("%s_%s",appId,userId);
        OpenItemDTO openItemDTO = apolloGet("bidding.sdk.skip.map");
        return JSON.parseObject(openItemDTO.getValue(),new TypeReference<Map<String,List<Integer>>>(){}).getOrDefault(key,new ArrayList<>());
    }

    public Boolean getBiddingSwitch(Integer appId, Long userId){
        String key =  String.format("%s_%s",appId,userId);
        OpenItemDTO biddingEnable = apolloGet("ad.sdk.bidding.request.user.map");
        return JSON.parseObject(biddingEnable.getValue(),new TypeReference<Map<String,Boolean>>(){}).getOrDefault(key,true);
    }

    public Integer getNocacheStg(Integer appId, Long userId){
        OpenItemDTO openItemDTO = apolloGet("ad.sdk.nocache.stg.user.map");
        String key =  String.format("%s_%s",appId,userId);
        return JSON.parseObject(openItemDTO.getValue(),new TypeReference<Map<String,Integer>>(){}).getOrDefault(key,1);
    }

    public void setBiddingSkip(Integer appId, Long userId, List<Integer> biddingItems){
        OpenItemDTO openItemDTO = apolloGet("bidding.sdk.skip.map");
        String key =  String.format("%s_%s",appId,userId);
        Map<String,List<Integer>> asBiddingMap = JSON.parseObject(openItemDTO.getValue(),new TypeReference<Map<String,List<Integer>>>(){});
        if (biddingItems == null){
            asBiddingMap.remove(key);
        }else {
            asBiddingMap.put(key,biddingItems);
        }
        openItemDTO.setValue(JSON.toJSONString(asBiddingMap));
        apolloPut(openItemDTO);
    }

    public void switchBidding(Integer appId,Long userId,Boolean open){
        String key =  String.format("%s_%s",appId,userId);
        OpenItemDTO biddingEnable = apolloGet("ad.sdk.bidding.request.user.map");
        Map<String,Boolean> asBiddingMap = JSON.parseObject(biddingEnable.getValue(),new TypeReference<Map<String,Boolean>>(){});
        asBiddingMap.put(key,open);
        biddingEnable.setValue(JSON.toJSONString(asBiddingMap));
        apolloPut(biddingEnable);
    }

    public void enableBiddingCache(Integer appId,Long userId,Integer nocache){
        OpenItemDTO openItemDTO = apolloGet("ad.sdk.nocache.stg.user.map");
        String key =  String.format("%s_%s",appId,userId);
        Map<String,Integer> asBiddingMap = JSON.parseObject(openItemDTO.getValue(),new TypeReference<Map<String,Integer>>(){});
        asBiddingMap.put(key,nocache);
        openItemDTO.setValue(JSON.toJSONString(asBiddingMap));
        apolloPut(openItemDTO);
    }

    public void changeAbGroup(Integer appId,Long userId,Integer groupId){
        String key =  String.format("%s_%s",appId,userId);
        OpenItemDTO openItemDTO = apolloGet("wihte_user_ab_value");
        Map<String,Integer> asBiddingMap = JSON.parseObject(openItemDTO.getValue(),new TypeReference<Map<String,Integer>>(){});
        asBiddingMap.put(key,groupId);
        openItemDTO.setValue(JSON.toJSONString(asBiddingMap));
        apolloPutAndPub(openItemDTO);
    }

    public void addUserToUserWhiteList(Long userId){
        OpenItemDTO openItemDTO = apolloGetUser("return.401.set");
        Set<Long> userSet = JSON.parseObject(openItemDTO.getValue(),new TypeReference<Set<Long>>(){});
        if (userSet.size() > 40){
            userSet = new HashSet<>();
        }
        userSet.add(userId);
        openItemDTO.setValue(JSON.toJSONString(userSet));
        apolloPutUser(openItemDTO);
        NamespaceReleaseDTO namespaceReleaseDTO = new NamespaceReleaseDTO();
        namespaceReleaseDTO.setReleasedBy("apollo");
        namespaceReleaseDTO.setReleaseTitle("REMOTE-"+System.currentTimeMillis());
        namespaceReleaseDTO.setReleaseComment("OPEN-API");
        client.publishNamespace(APP_ID_USER,ENV,
                ConfigConsts.CLUSTER_NAME_DEFAULT,
                ConfigConsts.NAMESPACE_APPLICATION,
                namespaceReleaseDTO);
    }

    private OpenItemDTO apolloGet(String key){
        return client.getItem(APP_ID,ENV,ConfigConsts.CLUSTER_NAME_DEFAULT,ConfigConsts.NAMESPACE_APPLICATION,key);
    }

    private OpenItemDTO apolloGetUser(String key){
        return client.getItem(APP_ID_USER,ENV,ConfigConsts.CLUSTER_NAME_DEFAULT,ConfigConsts.NAMESPACE_APPLICATION,key);
    }

    private void apolloPut(OpenItemDTO itemDTO){
        itemDTO.setDataChangeLastModifiedTime(new Date());
        client.createOrUpdateItem(APP_ID,ENV,
                ConfigConsts.CLUSTER_NAME_DEFAULT,
                ConfigConsts.NAMESPACE_APPLICATION,itemDTO);
    }

    private void apolloPutUser(OpenItemDTO itemDTO){
        itemDTO.setDataChangeLastModifiedTime(new Date());
        client.createOrUpdateItem(APP_ID_USER,ENV,
                ConfigConsts.CLUSTER_NAME_DEFAULT,
                ConfigConsts.NAMESPACE_APPLICATION,itemDTO);
    }

    public void apolloPub(){
        NamespaceReleaseDTO namespaceReleaseDTO = new NamespaceReleaseDTO();
        namespaceReleaseDTO.setReleasedBy("apollo");
        namespaceReleaseDTO.setReleaseTitle("REMOTE-"+System.currentTimeMillis());
        namespaceReleaseDTO.setReleaseComment("OPEN-API");
        client.publishNamespace(APP_ID,ENV,
                ConfigConsts.CLUSTER_NAME_DEFAULT,
                ConfigConsts.NAMESPACE_APPLICATION,
                namespaceReleaseDTO);
    }

    private void apolloPutAndPub(OpenItemDTO itemDTO){
        itemDTO.setDataChangeLastModifiedTime(new Date());
        client.createOrUpdateItem(APP_ID,ENV,
                ConfigConsts.CLUSTER_NAME_DEFAULT,
                ConfigConsts.NAMESPACE_APPLICATION,itemDTO);
        NamespaceReleaseDTO namespaceReleaseDTO = new NamespaceReleaseDTO();
        namespaceReleaseDTO.setReleasedBy("apollo");
        namespaceReleaseDTO.setReleaseTitle("REMOTE-"+System.currentTimeMillis());
        namespaceReleaseDTO.setReleaseComment("OPEN-API");
        client.publishNamespace(APP_ID,ENV,
                ConfigConsts.CLUSTER_NAME_DEFAULT,
                ConfigConsts.NAMESPACE_APPLICATION,
                namespaceReleaseDTO);
    }

}
