package com.coohua.user.event.admin.controller;

import com.coohua.user.event.admin.rsp.BaseResponse;
import com.coohua.user.event.biz.core.dto.req.CreateLockConfig;
import com.coohua.user.event.biz.dc.dto.LockConfigRsp;
import com.coohua.user.event.biz.dc.service.LockConfigService;
import com.coohua.user.event.biz.dc.dto.Pages;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

/**
 * <AUTHOR>
 * @since 2023/7/6
 */
@Slf4j
@RequestMapping("lock")
@RestController
public class LockConfigController {

    @Autowired
    private LockConfigService lockConfigService;

    @PostMapping(value = "list")
    public BaseResponse<Pages<LockConfigRsp>> queryList(
            @RequestParam(value = "product",required = false) String product,
            Pages<LockConfigRsp> page){
        BaseResponse<Pages<LockConfigRsp>> response = new BaseResponse<>("OK");
        response.setResult(lockConfigService.queryList(product,page));
        return response;
    }

    // 新增
    @PostMapping("addOne")
    public BaseResponse<Boolean> addNewAdConfig(@RequestBody CreateLockConfig createLockConfig){
        Boolean result = lockConfigService.addNewConfig(createLockConfig);
        BaseResponse<Boolean> response = new BaseResponse<>(0);
        response.setResult(result);
        return response;
    }

    // 编辑
    @PostMapping("editOne")
    public BaseResponse<Boolean> editAdConfig(@RequestBody CreateLockConfig createLockConfig){
        BaseResponse<Boolean> response = new BaseResponse<>(0);
        Boolean result = lockConfigService.editConfig(createLockConfig);
        response.setResult(result);
        return response;
    }

    // 开关切换
    @PostMapping("switchFlag")
    public BaseResponse<Boolean> switchAdConfigState(@RequestParam("id")Integer Id,@RequestParam("state")Integer state){
        BaseResponse<Boolean> response = new BaseResponse<>(0);
        Boolean result = lockConfigService.switchStateConfig(Id,state);
        response.setResult(result);
        return response;
    }
}
