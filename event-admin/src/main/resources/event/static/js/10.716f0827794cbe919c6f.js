webpackJsonp([10],{209:function(t,e,a){a(768);var r=a(89)(a(436),a(751),null,null);t.exports=r.exports},436:function(t,e,a){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.default={data:function(){return{appList:[],adConfigList:[],listCountNum:0,queryParam:{product:"",pageNo:1,pageSize:10}}},created:function(){this.getAppList(),this.getLockConfigList()},methods:{handleCurrentChangeForPlan:function(t){this.queryParam.pageNo=t,this.getLockConfigList()},getAppList:function(){var t=this;t.$axios.post("dist/appList",{},{}).then(function(e){if(200===e.status){var a=e.data;0===a.code?t.appList=a.result:t.$message.error(a.result)}else t.$message.error("服务器异常！")})},addAdConfig:function(){var t=this;t.$axios.post("ad/addOne",t.addAdConfigFrom,t.addAdConfigFrom).then(function(e){if(200===e.status){0===e.data.code?(t.$message({message:"添加成功",type:"success"}),t.isShowAddAdConfigFrom=!1,t.getAdList()):t.$message.error("更新状态失败")}else t.$message.error("服务器异常！")})},getLockConfigList:function(){var t=this;t.$axios.post("ad/list",{},{params:t.queryParam}).then(function(e){if(200===e.status){var a=e.data;0===a.code?(t.adConfigList=a.result.items,t.listCountNum=a.result.count):t.$message.error(a.result)}else t.$message.error("服务器异常！")})}}}},465:function(t,e,a){e=t.exports=a(29)(),e.push([t.i,".width-100{width:100px}.select-150 .el-input{width:150px}.inline-block{display:inline-block}",""])},751:function(t,e){t.exports={render:function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",{staticClass:"table"},[a("div",{staticClass:"crumbs"},[a("el-breadcrumb",{attrs:{separator:"/"}},[a("el-breadcrumb-item",[a("i",{staticClass:"el-icon-menu"}),t._v(" 产品风险控制管理")]),t._v(" "),a("el-breadcrumb-item",[t._v("产品锁区配置")])],1)],1),t._v(" "),a("el-row",[a("el-col",{attrs:{span:24}},[a("el-form",{attrs:{model:t.queryParam,size:"small",inline:""}},[a("el-form-item",{attrs:{label:"产品"}},[a("el-select",{staticClass:"select-150",attrs:{placeholder:"产品",filterable:""},model:{value:t.queryParam.product,callback:function(e){t.$set(t.queryParam,"product",e)},expression:"queryParam.product"}},t._l(t.appList,function(t){return a("el-option",{attrs:{label:t.label,value:t.value}})}),1)],1),t._v(" "),a("el-form-item",{staticStyle:{float:"right"}},[a("el-button",{attrs:{type:"warning"},on:{click:function(e){return t.getAdList()}}},[t._v("查询")]),t._v(" "),a("el-button",{attrs:{type:"primary",round:""},on:{click:function(e){return t.addAdConfigFromPre()}}},[t._v("广告位整改")]),t._v(" "),a("el-button",{attrs:{type:"primary",round:""},on:{click:function(e){return t.addUserConfigFromPre()}}},[t._v("批量用户整改")])],1)],1)],1)],1),t._v(" "),a("el-divider",{attrs:{"content-position":"left"}},[t._v("产品锁区配置")]),t._v(" "),a("el-row",[a("el-col",{attrs:{span:24}},[a("el-table",{staticStyle:{width:"100%"},attrs:{data:t.adConfigList,stripe:"","highlight-current-row":""}},[a("el-table-column",{attrs:{label:"开关"},scopedSlots:t._u([{key:"default",fn:function(e){return[a("el-switch",{attrs:{"active-color":"#13ce66","inactive-color":"#ff4949","active-value":1,"inactive-value":0},on:{change:function(a){return t.changeSwitchFlag(e.$index,e.row)}},model:{value:e.row.state,callback:function(a){t.$set(e.row,"state",a)},expression:"scope.row.state"}})]}}])}),t._v(" "),a("el-table-column",{attrs:{prop:"id",label:"Id",width:"100px"}}),t._v(" "),a("el-table-column",{attrs:{prop:"productName",label:"产品",width:"100px"}}),t._v(" "),a("el-table-column",{attrs:{prop:"fxType",label:"整改类型"},scopedSlots:t._u([{key:"default",fn:function(e){return[1===e.row.fxType?a("el-tag",{attrs:{type:"danger"}},[t._v("应用整改")]):t._e(),t._v(" "),2===e.row.fxType?a("el-tag",{attrs:{type:"warning"}},[t._v("广告平台整改")]):t._e(),t._v(" "),3===e.row.fxType?a("el-tag",{attrs:{type:"info"}},[t._v("广告位整改")]):t._e()]}}])}),t._v(" "),a("el-table-column",{attrs:{prop:"adId",label:"广告ID"}}),t._v(" "),a("el-table-column",{attrs:{prop:"updateTime",label:"最后修改"}}),t._v(" "),a("el-table-column",{attrs:{label:"操作",width:"200px"},scopedSlots:t._u([{key:"default",fn:function(e){return[a("el-button",{attrs:{size:"small"},on:{click:function(a){return t.updateAdFxConfigPre(e.row)}}},[t._v("修改")])]}}])})],1)],1)],1),t._v(" "),a("el-pagination",{staticStyle:{float:"right"},attrs:{"current-page":t.queryParam.pageNo,"page-size":10,layout:"total, prev, pager, next, jumper",total:t.listCountNum,background:""},on:{"current-change":t.handleCurrentChangeForPlan}})],1)},staticRenderFns:[]}},768:function(t,e,a){var r=a(465);"string"==typeof r&&(r=[[t.i,r,""]]),r.locals&&(t.exports=r.locals);a(90)("6ad8afe9",r,!0)}});