webpackJsonp([9],{210:function(e,t,o){o(758);var r=o(89)(o(437),o(741),null,null);e.exports=r.exports},437:function(e,t,o){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.default={data:function(){return{appList:[],platformList:[{label:"不限制",value:0},{label:"穿山甲",value:1},{label:"广点通",value:2},{label:"快手",value:3},{label:"百度",value:4},{label:"VIVO",value:5},{label:"OPPO",value:6}],queryParam:{product:"",pageNo:1,pageSize:10},listCountNum:0,configList:[],csjChapList:[],editExUserConfigForm:{id:0,appId:0,state:0,ocpcType:0,sources:"",newUserType:0,channels:"",models:"",ips:"",actionType:0,ruleName:"",level:0,brands:"",type:1,minEcpm:0,maxEcpm:0,videoTimes:0,videoLimit:0,withdrawLimit:0,rewardLimit:0,platformLimit:[0],platformSkipList:[]},addExUserConfigForm:{appId:0,state:0,ocpcType:0,newUserType:0,sources:"",channels:"",models:"",ips:"",actionType:0,ruleName:"",level:0,brands:"",type:1,minEcpm:0,maxEcpm:0,videoTimes:0,videoLimit:2800,withdrawLimit:100,rewardLimit:100,platformLimit:[0],platformSkipList:[]},isShowEditExUserConfigForm:!1,isShowAddExUserConfigForm:!1}},created:function(){this.getAppList(),this.getConfigList()},methods:{handleCurrentChangeForPlan:function(e){this.queryParam.pageNo=e,this.getConfigList()},getAppList:function(){var e=this;e.$axios.post("dist/appList",{},{}).then(function(t){if(200===t.status){var o=t.data;0===o.code?e.appList=o.result:e.$message.error(o.result)}else e.$message.error("服务器异常！")})},getConfigList:function(){var e=this;e.$axios.post("realtime/list",{},{params:e.queryParam}).then(function(t){if(200===t.status){var o=t.data;0===o.code?(e.configList=o.result.items,e.listCountNum=o.result.count):e.$message.error(o.result)}else e.$message.error("服务器异常！")})},addExUserConfigPre:function(){this.clearExUserConfigAddForm(),this.isShowAddExUserConfigForm=!0},clearExUserConfigAddForm:function(){this.addExUserConfigForm.id=0,this.addExUserConfigForm.appId=1,this.addExUserConfigForm.state=0,this.addExUserConfigForm.ocpcType=0,this.addExUserConfigForm.newUserType=0,this.addExUserConfigForm.channels="",this.addExUserConfigForm.sources="",this.addExUserConfigForm.models="",this.addExUserConfigForm.ips="",this.addExUserConfigForm.actionType=0,this.addExUserConfigForm.ruleName="",this.addExUserConfigForm.level=0,this.addExUserConfigForm.brands="",this.addExUserConfigForm.type=1,this.addExUserConfigForm.minEcpm=0,this.addExUserConfigForm.maxEcpm=0,this.addExUserConfigForm.videoTimes=0,this.addExUserConfigForm.videoLimit=2800,this.addExUserConfigForm.withdrawLimit=100,this.addExUserConfigForm.rewardLimit=100,this.addExUserConfigForm.platformLimit=[0],this.addExUserConfigForm.platformSkipList=[]},updateExUserConfigPre:function(e){this.editExUserConfigForm.id=e.id,this.editExUserConfigForm.appId=e.appId,this.editExUserConfigForm.state=e.state,this.editExUserConfigForm.ocpcType=e.ocpcType,this.editExUserConfigForm.newUserType=e.newUserType,this.editExUserConfigForm.channels=e.channels,this.editExUserConfigForm.sources=e.sources,this.editExUserConfigForm.models=e.models,this.editExUserConfigForm.ips=e.ips,this.editExUserConfigForm.actionType=e.actionType,this.editExUserConfigForm.ruleName=e.ruleName,this.editExUserConfigForm.level=e.level,this.editExUserConfigForm.brands=e.brands,this.editExUserConfigForm.type=e.type,this.editExUserConfigForm.minEcpm=e.minEcpm,this.editExUserConfigForm.maxEcpm=e.maxEcpm,this.editExUserConfigForm.videoTimes=e.videoTimes,this.editExUserConfigForm.videoLimit=e.videoLimit,this.editExUserConfigForm.withdrawLimit=e.withdrawLimit,this.editExUserConfigForm.rewardLimit=e.rewardLimit,this.editExUserConfigForm.platformLimit=e.platformLimit,this.editExUserConfigForm.platformSkipList=e.platformSkipList,this.isShowEditExUserConfigForm=!0},handleCurrentChangePage:function(e){this.queryParam.pageNo=e,this.getConfigList()},saveExUserConfig:function(){var e=this;e.$axios.post("realtime/update",e.editExUserConfigForm,{}).then(function(t){if(200===t.status){0===t.data.code?(e.$message({message:"添加成功",type:"success"}),e.isShowEditExUserConfigForm=!1,e.getConfigList()):e.$message.error("更新状态失败")}else e.$message.error("服务器异常！")})},addExUserConfig:function(){var e=this;e.$axios.post("realtime/insert",e.addExUserConfigForm,{}).then(function(t){if(200===t.status){0===t.data.code?(e.$message({message:"添加成功",type:"success"}),e.isShowAddExUserConfigForm=!1,e.getConfigList()):e.$message.error("更新状态失败")}else e.$message.error("服务器异常！")})},changeSwitchFlag:function(e,t){var o={};o.id=t.id,o.state=t.state;var r=this;r.$axios.post("realtime/switchFlag",{},{params:o}).then(function(e){if(200===e.status){0===e.data.code?r.$message({message:"更新状态成功",type:"success"}):r.$message.error("更新状态失败")}else r.$message.error("服务器异常！")})}}}},455:function(e,t,o){t=e.exports=o(29)(),t.push([e.i,".width-100{width:100px}.select-150 .el-input{width:150px}.inline-block{display:inline-block}",""])},741:function(e,t){e.exports={render:function(){var e=this,t=e.$createElement,o=e._self._c||t;return o("div",{staticClass:"table"},[o("div",{staticClass:"crumbs"},[o("el-breadcrumb",{attrs:{separator:"/"}},[o("el-breadcrumb-item",[o("i",{staticClass:"el-icon-menu"}),e._v(" 产品风险控制管理")]),e._v(" "),o("el-breadcrumb-item",[e._v("实时反作弊配置")])],1)],1),e._v(" "),o("el-dialog",{attrs:{title:"编辑实时反作弊规则",visible:e.isShowEditExUserConfigForm},on:{"update:visible":function(t){e.isShowEditExUserConfigForm=t}}},[o("el-form",{ref:"editExUserConfigForm",attrs:{model:e.editExUserConfigForm,"label-width":"200px"}},[o("el-form-item",{attrs:{label:"开关",prop:"state"}},[o("el-switch",{attrs:{"active-color":"#13ce66","inactive-color":"#ff4949","active-value":1,"inactive-value":0},model:{value:e.editExUserConfigForm.state,callback:function(t){e.$set(e.editExUserConfigForm,"state",t)},expression:"editExUserConfigForm.state"}})],1),e._v(" "),o("el-form-item",{attrs:{label:"规则名称",prop:"channels"}},[o("el-input",{attrs:{type:"text"},model:{value:e.editExUserConfigForm.ruleName,callback:function(t){e.$set(e.editExUserConfigForm,"ruleName",t)},expression:"editExUserConfigForm.ruleName"}})],1),e._v(" "),o("el-form-item",{attrs:{label:"产品"}},[o("el-select",{staticClass:"select-150",attrs:{placeholder:"产品",filterable:""},model:{value:e.editExUserConfigForm.appId,callback:function(t){e.$set(e.editExUserConfigForm,"appId",t)},expression:"editExUserConfigForm.appId"}},e._l(e.appList,function(e){return o("el-option",{attrs:{label:e.label,value:e.value}})}),1)],1),e._v(" "),o("el-form-item",{attrs:{label:"限制类型",prop:"type"}},[o("el-radio-group",{model:{value:e.editExUserConfigForm.type,callback:function(t){e.$set(e.editExUserConfigForm,"type",t)},expression:"editExUserConfigForm.type"}},[o("el-radio-button",{attrs:{label:"1"}},[e._v("ECPM_CHECK")]),e._v(" "),o("el-radio-button",{attrs:{label:"2"}},[e._v("RULE_CHECK")])],1)],1),e._v(" "),1==e.editExUserConfigForm.type?o("div",[o("el-form-item",{attrs:{label:"限制平台"}},[o("el-select",{staticClass:"select-150",attrs:{multiple:"",placeholder:"限制平台",filterable:""},model:{value:e.editExUserConfigForm.platformLimit,callback:function(t){e.$set(e.editExUserConfigForm,"platformLimit",t)},expression:"editExUserConfigForm.platformLimit"}},e._l(e.platformList,function(e){return o("el-option",{attrs:{label:e.label,value:e.value}})}),1)],1)],1):e._e(),e._v(" "),o("el-form-item",{attrs:{label:"异常Source",prop:"sources"}},[o("el-input",{attrs:{type:"textarea",rows:2,placeholder:"(英文,分割)"},model:{value:e.editExUserConfigForm.sources,callback:function(t){e.$set(e.editExUserConfigForm,"sources",t)},expression:"editExUserConfigForm.sources"}})],1),e._v(" "),o("el-form-item",{attrs:{label:"异常渠道",prop:"channels"}},[o("el-input",{attrs:{type:"textarea",rows:2,placeholder:"(英文,分割)"},model:{value:e.editExUserConfigForm.channels,callback:function(t){e.$set(e.editExUserConfigForm,"channels",t)},expression:"editExUserConfigForm.channels"}})],1),e._v(" "),o("el-form-item",{attrs:{label:"异常ip",prop:"ips"}},[o("el-input",{attrs:{type:"textarea",rows:2,placeholder:"(英文,分割) 支持前缀匹配"},model:{value:e.editExUserConfigForm.ips,callback:function(t){e.$set(e.editExUserConfigForm,"ips",t)},expression:"editExUserConfigForm.ips"}})],1),e._v(" "),o("el-form-item",{attrs:{label:"异常厂商",prop:"brands"}},[o("el-input",{attrs:{type:"textarea",rows:2,placeholder:"(英文,分割)"},model:{value:e.editExUserConfigForm.brands,callback:function(t){e.$set(e.editExUserConfigForm,"brands",t)},expression:"editExUserConfigForm.brands"}})],1),e._v(" "),o("el-form-item",{attrs:{label:"异常机型",prop:"models"}},[o("el-input",{attrs:{type:"textarea",rows:2,placeholder:"(英文,分割)"},model:{value:e.editExUserConfigForm.models,callback:function(t){e.$set(e.editExUserConfigForm,"models",t)},expression:"editExUserConfigForm.models"}})],1),e._v(" "),1==e.editExUserConfigForm.type?o("div",[o("el-form-item",{attrs:{label:"OCPC人群",prop:"ocpcType"}},[o("el-radio-group",{model:{value:e.editExUserConfigForm.ocpcType,callback:function(t){e.$set(e.editExUserConfigForm,"ocpcType",t)},expression:"editExUserConfigForm.ocpcType"}},[o("el-radio-button",{attrs:{label:"0"}},[e._v("全量用户")]),e._v(" "),o("el-radio-button",{attrs:{label:"1"}},[e._v("只对投放用户生效")]),e._v(" "),o("el-radio-button",{attrs:{label:"2"}},[e._v("只对非投放用户生效")])],1)],1),e._v(" "),o("el-form-item",{attrs:{label:"ECPM最小值",prop:"channels"}},[o("el-input",{attrs:{type:"text"},model:{value:e.editExUserConfigForm.minEcpm,callback:function(t){e.$set(e.editExUserConfigForm,"minEcpm",t)},expression:"editExUserConfigForm.minEcpm"}})],1),e._v(" "),o("el-form-item",{attrs:{label:"ECPM最大值",prop:"channels"}},[o("el-input",{attrs:{type:"text"},model:{value:e.editExUserConfigForm.maxEcpm,callback:function(t){e.$set(e.editExUserConfigForm,"maxEcpm",t)},expression:"editExUserConfigForm.maxEcpm"}})],1),e._v(" "),o("el-form-item",{attrs:{label:"观看次数",prop:"channels"}},[o("el-input",{attrs:{type:"text"},model:{value:e.editExUserConfigForm.videoTimes,callback:function(t){e.$set(e.editExUserConfigForm,"videoTimes",t)},expression:"editExUserConfigForm.videoTimes"}})],1)],1):e._e(),e._v(" "),2==e.editExUserConfigForm.type?o("div",[o("el-form-item",{attrs:{label:"OCPC人群",prop:"ocpcType"}},[o("el-radio-group",{model:{value:e.editExUserConfigForm.ocpcType,callback:function(t){e.$set(e.editExUserConfigForm,"ocpcType",t)},expression:"editExUserConfigForm.ocpcType"}},[o("el-radio-button",{attrs:{label:"0"}},[e._v("全量用户")]),e._v(" "),o("el-radio-button",{attrs:{label:"1"}},[e._v("只对投放用户生效")]),e._v(" "),o("el-radio-button",{attrs:{label:"2"}},[e._v("只对非投放用户生效")]),e._v(" "),o("el-radio-button",{attrs:{label:"3"}},[e._v("只对无师徒关系用户生效")])],1)],1)],1):e._e(),e._v(" "),o("el-form-item",{attrs:{label:"新老用户",prop:"newUserType"}},[o("el-radio-group",{model:{value:e.editExUserConfigForm.newUserType,callback:function(t){e.$set(e.editExUserConfigForm,"newUserType",t)},expression:"editExUserConfigForm.newUserType"}},[o("el-radio-button",{attrs:{label:"0"}},[e._v("全量用户")]),e._v(" "),o("el-radio-button",{attrs:{label:"1"}},[e._v("只对新用户生效")]),e._v(" "),o("el-radio-button",{attrs:{label:"2"}},[e._v("只对老用户生效")])],1)],1),e._v(" "),o("el-divider",[o("i",{staticClass:"el-icon-s-tools"})]),e._v(" "),o("el-form-item",{attrs:{label:"拉黑类型",prop:"ocpcType"}},[o("el-radio-group",{model:{value:e.editExUserConfigForm.actionType,callback:function(t){e.$set(e.editExUserConfigForm,"actionType",t)},expression:"editExUserConfigForm.actionType"}},[o("el-radio-button",{attrs:{label:"3"}},[e._v("拉黑处理(单产品)")]),e._v(" "),o("el-radio-button",{attrs:{label:"0"}},[e._v("拉黑处理(全产品)")]),e._v(" "),o("el-radio-button",{attrs:{label:"1"}},[e._v("拉灰")]),e._v(" "),o("el-radio-button",{attrs:{label:"2"}},[e._v("限制输出")])],1)],1),e._v(" "),2==e.editExUserConfigForm.actionType?o("div",[o("el-form-item",{attrs:{label:"曝光次数限制",prop:"videoLimit"}},[o("el-input",{staticClass:"inline-input width-100",attrs:{placeholder:"整数"},model:{value:e.editExUserConfigForm.videoLimit,callback:function(t){e.$set(e.editExUserConfigForm,"videoLimit",t)},expression:"editExUserConfigForm.videoLimit"}})],1),e._v(" "),o("el-form-item",{attrs:{label:"提现比例",prop:"withDrawRate"}},[o("el-input",{staticClass:"inline-input width-100",attrs:{placeholder:"整数"},model:{value:e.editExUserConfigForm.withdrawLimit,callback:function(t){e.$set(e.editExUserConfigForm,"withdrawLimit",t)},expression:"editExUserConfigForm.withdrawLimit"}}),e._v("%\n                ")],1),e._v(" "),o("el-form-item",{attrs:{label:"奖励发放比例",prop:"scoreRate"}},[o("el-input",{staticClass:"inline-input width-100",attrs:{placeholder:"整数"},model:{value:e.editExUserConfigForm.rewardLimit,callback:function(t){e.$set(e.editExUserConfigForm,"rewardLimit",t)},expression:"editExUserConfigForm.rewardLimit"}}),e._v("%\n                ")],1),e._v(" "),o("el-form-item",{attrs:{label:"跳过不观看的平台",prop:"platformSkipList"}},[o("el-checkbox-group",{model:{value:e.editExUserConfigForm.platformSkipList,callback:function(t){e.$set(e.editExUserConfigForm,"platformSkipList",t)},expression:"editExUserConfigForm.platformSkipList"}},[o("el-checkbox",{attrs:{label:"穿山甲",border:""}}),e._v(" "),o("el-checkbox",{attrs:{label:"广点通",border:""}}),e._v(" "),o("el-checkbox",{attrs:{label:"快手",border:""}})],1)],1)],1):e._e(),e._v(" "),o("el-divider",[o("i",{staticClass:"el-icon-s-tools"})]),e._v(" "),o("el-form-item",{attrs:{label:"优先级",prop:"ruleLevel"}},[o("el-input",{staticClass:"inline-input width-100",attrs:{type:"number",placeholder:"整数"},model:{value:e.editExUserConfigForm.level,callback:function(t){e.$set(e.editExUserConfigForm,"level",t)},expression:"editExUserConfigForm.level"}})],1)],1),e._v(" "),o("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[o("el-button",{on:{click:function(t){e.isShowEditExUserConfigForm=!1}}},[e._v("取 消")]),e._v(" "),o("el-button",{attrs:{type:"primary"},on:{click:function(t){return e.saveExUserConfig()}}},[e._v("确 定")])],1)],1),e._v(" "),o("el-dialog",{attrs:{title:"新建实时反作弊规则",visible:e.isShowAddExUserConfigForm},on:{"update:visible":function(t){e.isShowAddExUserConfigForm=t}}},[o("el-form",{ref:"addExUserConfigForm",attrs:{model:e.addExUserConfigForm,"label-width":"200px"}},[o("el-form-item",{attrs:{label:"开关",prop:"state"}},[o("el-switch",{attrs:{"active-color":"#13ce66","inactive-color":"#ff4949","active-value":1,"inactive-value":0},model:{value:e.addExUserConfigForm.state,callback:function(t){e.$set(e.addExUserConfigForm,"state",t)},expression:"addExUserConfigForm.state"}})],1),e._v(" "),o("el-form-item",{attrs:{label:"规则名称",prop:"channels"}},[o("el-input",{attrs:{type:"text"},model:{value:e.addExUserConfigForm.ruleName,callback:function(t){e.$set(e.addExUserConfigForm,"ruleName",t)},expression:"addExUserConfigForm.ruleName"}})],1),e._v(" "),o("el-form-item",{attrs:{label:"产品"}},[o("el-select",{staticClass:"select-150",attrs:{placeholder:"配置类型",filterable:""},model:{value:e.addExUserConfigForm.appId,callback:function(t){e.$set(e.addExUserConfigForm,"appId",t)},expression:"addExUserConfigForm.appId"}},e._l(e.appList,function(e){return o("el-option",{attrs:{label:e.label,value:e.value}})}),1)],1),e._v(" "),o("el-form-item",{attrs:{label:"限制类型",prop:"type"}},[o("el-radio-group",{model:{value:e.addExUserConfigForm.type,callback:function(t){e.$set(e.addExUserConfigForm,"type",t)},expression:"addExUserConfigForm.type"}},[o("el-radio-button",{attrs:{label:"1"}},[e._v("ECPM_CHECK")]),e._v(" "),o("el-radio-button",{attrs:{label:"2"}},[e._v("RULE_CHECK")])],1)],1),e._v(" "),1==e.addExUserConfigForm.type?o("div",[o("el-form-item",{attrs:{label:"限制平台"}},[o("el-select",{staticClass:"select-150",attrs:{multiple:"",placeholder:"限制平台",filterable:""},model:{value:e.addExUserConfigForm.platformLimit,callback:function(t){e.$set(e.addExUserConfigForm,"platformLimit",t)},expression:"addExUserConfigForm.platformLimit"}},e._l(e.platformList,function(e){return o("el-option",{attrs:{label:e.label,value:e.value}})}),1)],1)],1):e._e(),e._v(" "),o("el-form-item",{attrs:{label:"异常Source",prop:"sources"}},[o("el-input",{attrs:{type:"textarea",rows:2,placeholder:"(英文,分割)"},model:{value:e.editExUserConfigForm.sources,callback:function(t){e.$set(e.editExUserConfigForm,"sources",t)},expression:"editExUserConfigForm.sources"}})],1),e._v(" "),o("el-form-item",{attrs:{label:"异常渠道",prop:"channels"}},[o("el-input",{attrs:{type:"textarea",rows:2,placeholder:"(英文,分割)"},model:{value:e.addExUserConfigForm.channels,callback:function(t){e.$set(e.addExUserConfigForm,"channels",t)},expression:"addExUserConfigForm.channels"}})],1),e._v(" "),o("el-form-item",{attrs:{label:"异常ip",prop:"ips"}},[o("el-input",{attrs:{type:"textarea",rows:2,placeholder:"(英文,分割) 支持前缀匹配"},model:{value:e.addExUserConfigForm.ips,callback:function(t){e.$set(e.addExUserConfigForm,"ips",t)},expression:"addExUserConfigForm.ips"}})],1),e._v(" "),o("el-form-item",{attrs:{label:"异常厂商",prop:"phones"}},[o("el-input",{attrs:{type:"textarea",rows:2,placeholder:"(英文,分割)"},model:{value:e.addExUserConfigForm.brands,callback:function(t){e.$set(e.addExUserConfigForm,"brands",t)},expression:"addExUserConfigForm.brands"}})],1),e._v(" "),o("el-form-item",{attrs:{label:"异常机型",prop:"models"}},[o("el-input",{attrs:{type:"textarea",rows:2,placeholder:"(英文,分割)"},model:{value:e.addExUserConfigForm.models,callback:function(t){e.$set(e.addExUserConfigForm,"models",t)},expression:"addExUserConfigForm.models"}})],1),e._v(" "),1==e.addExUserConfigForm.type?o("div",[o("el-form-item",{attrs:{label:"OCPC人群",prop:"ocpcType"}},[o("el-radio-group",{model:{value:e.addExUserConfigForm.ocpcType,callback:function(t){e.$set(e.addExUserConfigForm,"ocpcType",t)},expression:"addExUserConfigForm.ocpcType"}},[o("el-radio-button",{attrs:{label:"0"}},[e._v("全量用户")]),e._v(" "),o("el-radio-button",{attrs:{label:"1"}},[e._v("只对投放用户生效")]),e._v(" "),o("el-radio-button",{attrs:{label:"2"}},[e._v("只对非投放用户生效")])],1)],1),e._v(" "),o("el-form-item",{attrs:{label:"ECPM最小值",prop:"channels"}},[o("el-input",{attrs:{type:"text"},model:{value:e.addExUserConfigForm.minEcpm,callback:function(t){e.$set(e.addExUserConfigForm,"minEcpm",t)},expression:"addExUserConfigForm.minEcpm"}})],1),e._v(" "),o("el-form-item",{attrs:{label:"ECPM最大值",prop:"channels"}},[o("el-input",{attrs:{type:"text"},model:{value:e.addExUserConfigForm.maxEcpm,callback:function(t){e.$set(e.addExUserConfigForm,"maxEcpm",t)},expression:"addExUserConfigForm.maxEcpm"}})],1),e._v(" "),o("el-form-item",{attrs:{label:"观看次数",prop:"channels"}},[o("el-input",{attrs:{type:"text"},model:{value:e.addExUserConfigForm.videoTimes,callback:function(t){e.$set(e.addExUserConfigForm,"videoTimes",t)},expression:"addExUserConfigForm.videoTimes"}})],1)],1):e._e(),e._v(" "),2==e.addExUserConfigForm.type?o("div",[o("el-form-item",{attrs:{label:"OCPC人群",prop:"ocpcType"}},[o("el-radio-group",{model:{value:e.addExUserConfigForm.ocpcType,callback:function(t){e.$set(e.addExUserConfigForm,"ocpcType",t)},expression:"addExUserConfigForm.ocpcType"}},[o("el-radio-button",{attrs:{label:"0"}},[e._v("全量用户")]),e._v(" "),o("el-radio-button",{attrs:{label:"1"}},[e._v("只对投放用户生效")]),e._v(" "),o("el-radio-button",{attrs:{label:"2"}},[e._v("只对非投放用户生效")]),e._v(" "),o("el-radio-button",{attrs:{label:"3"}},[e._v("只对无师徒关系用户生效")])],1)],1)],1):e._e(),e._v(" "),o("el-form-item",{attrs:{label:"新老用户",prop:"newUserType"}},[o("el-radio-group",{model:{value:e.addExUserConfigForm.newUserType,callback:function(t){e.$set(e.addExUserConfigForm,"newUserType",t)},expression:"addExUserConfigForm.newUserType"}},[o("el-radio-button",{attrs:{label:"0"}},[e._v("全量用户")]),e._v(" "),o("el-radio-button",{attrs:{label:"1"}},[e._v("只对新用户生效")]),e._v(" "),o("el-radio-button",{attrs:{label:"2"}},[e._v("只对老用户生效")])],1)],1),e._v(" "),o("el-divider",[o("i",{staticClass:"el-icon-s-tools"})]),e._v(" "),o("el-form-item",{attrs:{label:"拉黑类型",prop:"actionType"}},[o("el-radio-group",{model:{value:e.addExUserConfigForm.actionType,callback:function(t){e.$set(e.addExUserConfigForm,"actionType",t)},expression:"addExUserConfigForm.actionType"}},[o("el-radio-button",{attrs:{label:"3"}},[e._v("拉黑处理(单产品)")]),e._v(" "),o("el-radio-button",{attrs:{label:"0"}},[e._v("拉黑处理(全产品)")]),e._v(" "),o("el-radio-button",{attrs:{label:"1"}},[e._v("拉灰")]),e._v(" "),o("el-radio-button",{attrs:{label:"2"}},[e._v("限制输出")])],1)],1),e._v(" "),2==e.addExUserConfigForm.actionType?o("div",[o("el-form-item",{attrs:{label:"曝光次数限制",prop:"videoLimit"}},[o("el-input",{staticClass:"inline-input width-100",attrs:{placeholder:"整数"},model:{value:e.addExUserConfigForm.videoLimit,callback:function(t){e.$set(e.addExUserConfigForm,"videoLimit",t)},expression:"addExUserConfigForm.videoLimit"}})],1),e._v(" "),o("el-form-item",{attrs:{label:"提现比例",prop:"withDrawRate"}},[o("el-input",{staticClass:"inline-input width-100",attrs:{placeholder:"整数"},model:{value:e.addExUserConfigForm.withdrawLimit,callback:function(t){e.$set(e.addExUserConfigForm,"withdrawLimit",t)},expression:"addExUserConfigForm.withdrawLimit"}}),e._v("%\n                ")],1),e._v(" "),o("el-form-item",{attrs:{label:"奖励发放比例",prop:"scoreRate"}},[o("el-input",{staticClass:"inline-input width-100",attrs:{placeholder:"整数"},model:{value:e.addExUserConfigForm.rewardLimit,callback:function(t){e.$set(e.addExUserConfigForm,"rewardLimit",t)},expression:"addExUserConfigForm.rewardLimit"}}),e._v("%\n                ")],1),e._v(" "),o("el-form-item",{attrs:{label:"跳过不观看的平台",prop:"platformSkipList"}},[o("el-checkbox-group",{model:{value:e.addExUserConfigForm.platformSkipList,callback:function(t){e.$set(e.addExUserConfigForm,"platformSkipList",t)},expression:"addExUserConfigForm.platformSkipList"}},[o("el-checkbox",{attrs:{label:"穿山甲",border:""}}),e._v(" "),o("el-checkbox",{attrs:{label:"广点通",border:""}}),e._v(" "),o("el-checkbox",{attrs:{label:"快手",border:""}})],1)],1)],1):e._e(),e._v(" "),o("el-divider",[o("i",{staticClass:"el-icon-s-tools"})]),e._v(" "),o("el-form-item",{attrs:{label:"优先级",prop:"ruleLevel"}},[o("el-input",{staticClass:"inline-input width-100",attrs:{type:"number",placeholder:"整数"},model:{value:e.addExUserConfigForm.level,callback:function(t){e.$set(e.addExUserConfigForm,"level",t)},expression:"addExUserConfigForm.level"}})],1)],1),e._v(" "),o("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[o("el-button",{on:{click:function(t){e.isShowAddExUserConfigForm=!1}}},[e._v("取 消")]),e._v(" "),o("el-button",{attrs:{type:"primary"},on:{click:function(t){return e.addExUserConfig()}}},[e._v("确 定")])],1)],1),e._v(" "),o("el-row",[o("el-col",{attrs:{span:24}},[o("el-form",{attrs:{model:e.queryParam,size:"small",inline:""}},[o("el-form-item",{attrs:{label:"产品"}},[o("el-select",{staticClass:"select-150",attrs:{placeholder:"配置类型",filterable:""},model:{value:e.queryParam.product,callback:function(t){e.$set(e.queryParam,"product",t)},expression:"queryParam.product"}},e._l(e.appList,function(e){return o("el-option",{attrs:{label:e.label,value:e.value}})}),1)],1),e._v(" "),o("el-form-item",{staticStyle:{float:"right"}},[o("el-button",{attrs:{type:"warning"},on:{click:function(t){return e.getConfigList()}}},[e._v("查询")]),e._v(" "),o("el-button",{attrs:{type:"primary",round:""},on:{click:function(t){return e.addExUserConfigPre()}}},[e._v("新建实时规则")])],1)],1)],1)],1),e._v(" "),o("el-divider",{attrs:{"content-position":"left"}},[e._v("实时反作弊配置")]),e._v(" "),o("el-row",[o("el-col",{attrs:{span:24}},[o("el-table",{staticStyle:{width:"100%"},attrs:{data:e.configList,stripe:"","highlight-current-row":""}},[o("el-table-column",{attrs:{label:"开关"},scopedSlots:e._u([{key:"default",fn:function(t){return[o("el-switch",{attrs:{"active-color":"#13ce66","inactive-color":"#ff4949","active-value":1,"inactive-value":0},on:{change:function(o){return e.changeSwitchFlag(t.$index,t.row)}},model:{value:t.row.state,callback:function(o){e.$set(t.row,"state",o)},expression:"scope.row.state"}})]}}])}),e._v(" "),o("el-table-column",{attrs:{prop:"id",label:"Id",width:"100px"}}),e._v(" "),o("el-table-column",{attrs:{prop:"ruleName",width:"400",label:"规则名称"}}),e._v(" "),o("el-table-column",{attrs:{prop:"actionType",label:"执行类型"},scopedSlots:e._u([{key:"default",fn:function(t){return[0===t.row.actionType||3===t.row.actionType?o("span",[e._v("拉黑处理")]):1===t.row.actionType?o("span",[e._v("拉灰")]):2===t.row.actionType?o("span",[e._v("限制输出")]):e._e()]}}])}),e._v(" "),o("el-table-column",{attrs:{prop:"productName",label:"产品名称"}}),e._v(" "),o("el-table-column",{attrs:{prop:"updateTime",label:"最后修改"}}),e._v(" "),o("el-table-column",{attrs:{label:"操作",width:"200px"},scopedSlots:e._u([{key:"default",fn:function(t){return[o("el-button",{attrs:{size:"small"},on:{click:function(o){return e.updateExUserConfigPre(t.row)}}},[e._v("修改")])]}}])})],1)],1)],1),e._v(" "),o("el-pagination",{staticStyle:{float:"right"},attrs:{"current-page":e.queryParam.pageNo,"page-size":10,layout:"total, prev, pager, next, jumper",total:e.listCountNum,background:""},on:{"current-change":e.handleCurrentChangeForPlan}})],1)},staticRenderFns:[]}},758:function(e,t,o){var r=o(455);"string"==typeof r&&(r=[[e.i,r,""]]),r.locals&&(e.exports=r.locals);o(90)("89de8a24",r,!0)}});