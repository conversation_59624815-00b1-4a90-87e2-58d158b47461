webpackJsonp([6],{212:function(e,t,a){a(757);var r=a(89)(a(439),a(740),"data-v-415dee5c",null);e.exports=r.exports},274:function(e,t,a){e.exports={default:a(275),__esModule:!0}},275:function(e,t,a){var r=a(14),o=r.JSON||(r.JSON={stringify:JSON.stringify});e.exports=function(e){return o.stringify.apply(o,arguments)}},439:function(e,t,a){"use strict";Object.defineProperty(t,"__esModule",{value:!0});var r=a(274),o=a.n(r);t.default={name:"WechatOrderManagement",data:function(){return{searchForm:{dateRange:[],openId:"",orderNo:""},orderList:[],pagination:{pageNo:1,pageSize:10,count:0},loading:!1}},computed:{canSearch:function(){return this.searchForm.openId||this.searchForm.orderNo}},methods:{goHome:function(){this.$router.push("/")},refreshPage:function(){window.location.reload()},saveSearchState:function(){var e={searchForm:this.searchForm,pagination:this.pagination,orderList:this.orderList};localStorage.setItem("wechatOrderSearchState",o()(e))},restoreSearchState:function(){var e=localStorage.getItem("wechatOrderSearchState");if(e)try{var t=JSON.parse(e);this.searchForm=t.searchForm||this.searchForm,this.pagination=t.pagination||this.pagination,this.orderList=t.orderList||[]}catch(e){console.error("恢复查询状态失败:",e)}},clearSearchState:function(){localStorage.removeItem("wechatOrderSearchState")},searchOrders:function(){var e=this;if(!this.canSearch)return void this.$message.warning("请至少填写一个查询条件：OpenID或订单号");this.loading=!0;var t={openId:this.searchForm.openId||"",orderNo:this.searchForm.orderNo||"",pageNo:this.pagination.pageNo,pageSize:this.pagination.pageSize};this.searchForm.dateRange&&2===this.searchForm.dateRange.length&&(t.startDate=this.searchForm.dateRange[0],t.endDate=this.searchForm.dateRange[1]),this.$axios.get("/user-event-admin/wechatOrder/query",{params:t}).then(function(t){if(0===t.data.code){var a=t.data.result;e.orderList=a.items||[],e.pagination.count=a.count||0,e.$message.success("查询完成，共找到 "+e.pagination.count+" 条记录"),e.saveSearchState()}else e.$message.error(t.data.message||"查询失败")}).catch(function(t){console.error("查询订单失败:",t),e.$message.error("查询失败，请检查网络连接")}).finally(function(){e.loading=!1})},resetSearch:function(){this.searchForm={dateRange:[],openId:"",orderNo:""},this.pagination.pageNo=1,this.orderList=[],this.pagination.count=0,this.clearSearchState()},getStatusType:function(e){return{1:"warning",2:"success",3:"danger",4:"info",5:"success",6:"danger",7:"info"}[e]||"info"},getStatusText:function(e){return{1:"待审核",2:"审核通过",3:"审核不通过-待退款",4:"审核不通过-已退款",5:"付款成功",6:"付款失败-待退款",7:"付款失败-已退款"}[e]||"未知"},getWithdrawTypeText:function(e){return{1:"积分提现",2:"金币提现"}[e]||"未知"},formatDateTime:function(e){return e?new Date(e).toLocaleString("zh-CN"):""},viewWechatDetail:function(e){var t=this;console.log("点击查看详情，订单数据:",e),this.saveSearchState();var a=encodeURIComponent(o()(e));console.log("编码后的订单数据:",a);var r={path:"/wechat-payment-detail",query:{orderData:a}};console.log("准备跳转到:",r),this.$router.push(r).then(function(){console.log("路由跳转成功")}).catch(function(e){console.error("路由跳转失败:",e),t.$message.error("页面跳转失败，请重试")})},exportOrders:function(){var e=this;if(0===this.orderList.length)return void this.$message.warning("没有数据可导出");var t={openId:this.searchForm.openId||"",orderNo:this.searchForm.orderNo||""};this.searchForm.dateRange&&2===this.searchForm.dateRange.length&&(t.startDate=this.searchForm.dateRange[0],t.endDate=this.searchForm.dateRange[1]),this.$axios.get("/user-event-admin/wechatOrder/export",{params:t,responseType:"blob"}).then(function(t){var a=new Blob([t.data]),r=window.URL.createObjectURL(a),o=document.createElement("a");o.href=r,o.download="微信订单数据_"+(new Date).toISOString().split("T")[0]+".csv",o.click(),window.URL.revokeObjectURL(r),e.$message.success("订单导出成功")}).catch(function(t){console.error("导出订单失败:",t),e.$message.error("导出失败")})},handleSizeChange:function(e){this.pagination.pageSize=e,this.pagination.pageNo=1,this.searchOrders()},handleCurrentChange:function(e){this.pagination.pageNo=e,this.searchOrders()},loadGlobalStyles:function(){var e=document.createElement("style");e.type="text/css",e.innerHTML="\n        /* 强制覆盖全局的overflow: hidden */\n        html, body, #app {\n          overflow: auto !important;\n          height: 100% !important;\n        }\n        \n        /* 强制显示滚动条 */\n        ::-webkit-scrollbar {\n          width: 8px !important;\n          height: 8px !important;\n        }\n        \n        ::-webkit-scrollbar-track {\n          background: #f1f1f1 !important;\n          border-radius: 4px !important;\n        }\n        \n        ::-webkit-scrollbar-thumb {\n          background: #c1c1c1 !important;\n          border-radius: 4px !important;\n        }\n        \n        ::-webkit-scrollbar-thumb:hover {\n          background: #a8a8a8 !important;\n        }\n      ",e.id="wechat-order-global-styles";var t=document.getElementById("wechat-order-global-styles");t&&t.remove(),document.head.appendChild(e)}},mounted:function(){this.loadGlobalStyles(),this.restoreSearchState()},beforeDestroy:function(){var e=document.getElementById("wechat-order-global-styles");e&&e.remove()}}},454:function(e,t,a){t=e.exports=a(29)(),t.push([e.i,".wechat-order-management[data-v-415dee5c]{min-height:100vh;background-color:#f5f5f5;overflow:auto!important;height:100vh}.main-header[data-v-415dee5c]{background-color:#409eff;color:#fff;padding:0;box-shadow:0 2px 4px rgba(0,0,0,.1);position:fixed;top:0;left:0;right:0;z-index:1000;height:60px}.header-content[data-v-415dee5c]{display:flex;justify-content:space-between;align-items:center;height:100%;padding:0 20px}.logo h1[data-v-415dee5c]{margin:0;font-size:24px;font-weight:700}.nav-menu[data-v-415dee5c]{display:flex;gap:15px}.nav-menu .el-button[data-v-415dee5c]{color:#fff;font-size:14px}.nav-menu .el-button[data-v-415dee5c]:hover{color:#ffd04b}.main-container[data-v-415dee5c]{margin-top:60px;padding-bottom:20px;overflow:visible!important}.el-main[data-v-415dee5c]{padding:20px;overflow:visible!important}.search-card[data-v-415dee5c]{margin-bottom:20px}.search-form[data-v-415dee5c]{display:flex;flex-wrap:wrap}.search-tip[data-v-415dee5c]{margin-top:15px}.order-list-card[data-v-415dee5c]{margin-bottom:20px}.pagination-container[data-v-415dee5c]{margin-top:20px;text-align:center;padding-bottom:20px}.el-table[data-v-415dee5c]{width:100%}.el-table__body-wrapper[data-v-415dee5c]{overflow-y:auto!important;max-height:600px!important}.el-table .el-table__fixed[data-v-415dee5c]{box-shadow:2px 0 8px rgba(0,0,0,.1)}.el-table .el-table__fixed-right[data-v-415dee5c]{box-shadow:-2px 0 8px rgba(0,0,0,.1)}.el-table__header-wrapper[data-v-415dee5c]{position:sticky;top:0;z-index:1;background-color:#f5f7fa}.el-table__row[data-v-415dee5c]{height:50px}.el-table__body[data-v-415dee5c]{width:100%!important}.wxpay-detail-layout[data-v-415dee5c]{min-height:100vh;height:100vh;overflow:auto!important}.wxpay-content[data-v-415dee5c]{overflow-y:auto;height:100vh}",""])},740:function(e,t){e.exports={render:function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{staticClass:"wechat-order-management"},[a("el-header",{staticClass:"main-header"},[a("div",{staticClass:"header-content"},[a("div",{staticClass:"logo"},[a("h1",[e._v("微信订单管理系统")])]),e._v(" "),a("div",{staticClass:"nav-menu"},[a("el-button",{attrs:{type:"text"},on:{click:e.goHome}},[e._v("返回主页")]),e._v(" "),a("el-button",{attrs:{type:"text"},on:{click:e.refreshPage}},[e._v("刷新页面")])],1)])]),e._v(" "),a("el-container",{staticClass:"main-container"},[a("el-main",[a("el-card",{staticClass:"search-card"},[a("div",{attrs:{slot:"header"},slot:"header"},[a("span",[e._v("订单查询")])]),e._v(" "),a("el-form",{staticClass:"search-form",attrs:{inline:!0,model:e.searchForm}},[a("el-form-item",{attrs:{label:"日期范围"}},[a("el-date-picker",{attrs:{type:"daterange","range-separator":"至","start-placeholder":"开始日期","end-placeholder":"结束日期",format:"yyyy-MM-dd","value-format":"yyyy-MM-dd"},model:{value:e.searchForm.dateRange,callback:function(t){e.$set(e.searchForm,"dateRange",t)},expression:"searchForm.dateRange"}})],1),e._v(" "),a("el-form-item",{attrs:{label:"OpenID"}},[a("el-input",{attrs:{placeholder:"请输入OpenID"},model:{value:e.searchForm.openId,callback:function(t){e.$set(e.searchForm,"openId",t)},expression:"searchForm.openId"}})],1),e._v(" "),a("el-form-item",{attrs:{label:"订单号"}},[a("el-input",{attrs:{placeholder:"请输入订单号"},model:{value:e.searchForm.orderNo,callback:function(t){e.$set(e.searchForm,"orderNo",t)},expression:"searchForm.orderNo"}})],1),e._v(" "),a("el-form-item",[a("el-button",{attrs:{type:"primary",disabled:!e.canSearch},on:{click:e.searchOrders}},[e._v("查询")]),e._v(" "),a("el-button",{on:{click:e.resetSearch}},[e._v("重置")])],1)],1),e._v(" "),a("div",{staticClass:"search-tip"},[a("el-alert",{attrs:{title:"查询提示：OpenID和订单号至少需要填写一个条件，日期范围为可选条件",type:"info",closable:!1,"show-icon":""}})],1)],1),e._v(" "),a("el-card",{staticClass:"order-list-card"},[a("div",{attrs:{slot:"header"},slot:"header"},[a("span",[e._v("订单列表")]),e._v(" "),a("el-button",{staticStyle:{float:"right",padding:"3px 0"},attrs:{type:"text",disabled:0===e.orderList.length},on:{click:e.exportOrders}},[e._v("导出订单")])],1),e._v(" "),a("el-table",{directives:[{name:"loading",rawName:"v-loading",value:e.loading,expression:"loading"}],staticStyle:{width:"100%"},attrs:{data:e.orderList,border:"","max-height":"600"}},[a("el-table-column",{attrs:{prop:"orderNo",label:"订单号",width:"180",fixed:"left"}}),e._v(" "),a("el-table-column",{attrs:{prop:"userId",label:"用户ID",width:"120"}}),e._v(" "),a("el-table-column",{attrs:{prop:"openId",label:"OpenID",width:"200"}}),e._v(" "),a("el-table-column",{attrs:{prop:"productName",label:"产品名称",width:"150"}}),e._v(" "),a("el-table-column",{attrs:{prop:"amount",label:"订单金额/分",width:"120"},scopedSlots:e._u([{key:"default",fn:function(t){return[e._v("\n              "+e._s(t.row.amount)+"\n            ")]}}])}),e._v(" "),a("el-table-column",{attrs:{prop:"status",label:"订单状态",width:"140"},scopedSlots:e._u([{key:"default",fn:function(t){return[e._v("\n              "+e._s(e.getStatusText(t.row.status))+"\n            ")]}}])}),e._v(" "),a("el-table-column",{attrs:{prop:"os",label:"系统",width:"100"}}),e._v(" "),a("el-table-column",{attrs:{prop:"logday",label:"日期",width:"120"}}),e._v(" "),a("el-table-column",{attrs:{prop:"createTime",label:"创建时间",width:"180"},scopedSlots:e._u([{key:"default",fn:function(t){return[e._v("\n              "+e._s(e.formatDateTime(t.row.createTime))+"\n            ")]}}])}),e._v(" "),a("el-table-column",{attrs:{label:"操作",width:"120",fixed:"right"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("el-button",{attrs:{size:"mini",type:"success"},on:{click:function(a){return e.viewWechatDetail(t.row)}}},[e._v("查看详情")])]}}])})],1),e._v(" "),a("div",{staticClass:"pagination-container"},[a("el-pagination",{attrs:{"current-page":e.pagination.pageNo,"page-sizes":[10,20,50,100],"page-size":e.pagination.pageSize,layout:"total, sizes, prev, pager, next, jumper",total:e.pagination.count},on:{"size-change":e.handleSizeChange,"current-change":e.handleCurrentChange}})],1)],1)],1)],1)],1)},staticRenderFns:[]}},757:function(e,t,a){var r=a(454);"string"==typeof r&&(r=[[e.i,r,""]]),r.locals&&(e.exports=r.locals);a(90)("2aea37b2",r,!0)}});