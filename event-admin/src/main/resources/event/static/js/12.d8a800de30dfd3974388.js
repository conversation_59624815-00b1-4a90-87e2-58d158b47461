webpackJsonp([12],{207:function(e,t,o){o(755);var a=o(89)(o(434),o(738),null,null);e.exports=a.exports},434:function(e,t,o){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.default={data:function(){return{appList:[],adConfigList:[],listCountNum:0,platformList:[{label:"穿山甲",value:1},{label:"广点通",value:2},{label:"快手",value:3},{label:"百度",value:4},{label:"VIVO",value:5},{label:"OPPO",value:6},{label:"SIGMOB",value:91},{label:"阿里",value:9}],queryParam:{product:"",pageNo:1,pageSize:10},isShowAddAdConfigFrom:!1,isShowEditAdConfigFrom:!1,isShowAddUserConfigFrom:!1,addAdConfigFrom:{fxType:1,platformLimit:[],state:1,adId:0,appId:0,type:1,ocpcType:1,ocpcList:[]},addUserAdConfigFrom:{appId:0,day:0,targetIds:"",platformLimit:[]},editAdConfigFrom:{id:0,fxType:1,platformLimit:[],state:1,adId:0,appId:0,type:1,ocpcType:1,ocpcList:[]}}},created:function(){this.getAppList(),this.getAdList()},methods:{handleCurrentChangeForPlan:function(e){this.queryParam.pageNo=e,this.getAdList()},getAppList:function(){var e=this;e.$axios.post("dist/appList",{},{}).then(function(t){if(200===t.status){var o=t.data;0===o.code?e.appList=o.result:e.$message.error(o.result)}else e.$message.error("服务器异常！")})},addAdConfigFromPre:function(){this.isShowAddAdConfigFrom=!0},addUserConfigFromPre:function(){this.isShowAddUserConfigFrom=!0},addAdConfig:function(){var e=this;e.$axios.post("ad/addOne",e.addAdConfigFrom,e.addAdConfigFrom).then(function(t){if(200===t.status){0===t.data.code?(e.$message({message:"添加成功",type:"success"}),e.isShowAddAdConfigFrom=!1,e.getAdList()):e.$message.error("更新状态失败")}else e.$message.error("服务器异常！")})},addUserAdConfig:function(){var e=this;e.$axios.post("ad/batchUserAdConfig",e.addUserAdConfigFrom,e.addUserAdConfigFrom).then(function(t){if(200===t.status){0===t.data.code?(e.$message({message:"提交成功",type:"success"}),e.isShowAddUserConfigFrom=!1):e.$message.error("提交失败")}else e.$message.error("服务器异常！")})},getAdList:function(){var e=this;e.$axios.post("ad/list",{},{params:e.queryParam}).then(function(t){if(200===t.status){var o=t.data;0===o.code?(e.adConfigList=o.result.items,e.listCountNum=o.result.count):e.$message.error(o.result)}else e.$message.error("服务器异常！")})},updateAdFxConfigPre:function(e){this.editAdConfigFrom.id=e.id,this.editAdConfigFrom.fxType=e.fxType,this.editAdConfigFrom.platformLimit=e.platformLimit,this.editAdConfigFrom.state=e.state,this.editAdConfigFrom.adId=e.adId,this.editAdConfigFrom.appId=e.appId,this.editAdConfigFrom.type=e.type,this.editAdConfigFrom.ocpcType=e.ocpcType,this.editAdConfigFrom.ocpcList=e.ocpcList,this.isShowEditAdConfigFrom=!0},editAdConfig:function(){var e=this;e.$axios.post("ad/editOne",e.editAdConfigFrom,e.editAdConfigFrom).then(function(t){if(200===t.status){0===t.data.code?(e.$message({message:"修改成功",type:"success"}),e.isShowEditAdConfigFrom=!1,e.getAdList()):e.$message.error("更新状态失败")}else e.$message.error("服务器异常！")})},changeSwitchFlag:function(e,t){var o={};o.id=t.id,o.state=t.state;var a=this;a.$axios.post("ad/switchFlag",{},{params:o}).then(function(e){if(200===e.status){0===e.data.code?a.$message({message:"更新状态成功",type:"success"}):a.$message.error("更新状态失败")}else a.$message.error("服务器异常！")})}}}},452:function(e,t,o){t=e.exports=o(29)(),t.push([e.i,".width-100{width:100px}.select-150 .el-input{width:150px}.inline-block{display:inline-block}",""])},738:function(e,t){e.exports={render:function(){var e=this,t=e.$createElement,o=e._self._c||t;return o("div",{staticClass:"table"},[o("div",{staticClass:"crumbs"},[o("el-breadcrumb",{attrs:{separator:"/"}},[o("el-breadcrumb-item",[o("i",{staticClass:"el-icon-menu"}),e._v(" 产品风险控制管理")]),e._v(" "),o("el-breadcrumb-item",[e._v("广告位整改")])],1)],1),e._v(" "),o("el-dialog",{attrs:{title:"编辑整改广告位",visible:e.isShowEditAdConfigFrom},on:{"update:visible":function(t){e.isShowEditAdConfigFrom=t}}},[o("el-form",{ref:"editAdConfigFrom",attrs:{model:e.editAdConfigFrom,"label-width":"200px"}},[o("el-form-item",{attrs:{label:"开关",prop:"state"}},[o("el-switch",{attrs:{"active-color":"#13ce66","inactive-color":"#ff4949","active-value":1,"inactive-value":0},model:{value:e.editAdConfigFrom.state,callback:function(t){e.$set(e.editAdConfigFrom,"state",t)},expression:"editAdConfigFrom.state"}})],1),e._v(" "),o("el-form-item",{attrs:{label:"产品"}},[o("el-select",{staticClass:"select-150",attrs:{placeholder:"产品",filterable:""},model:{value:e.editAdConfigFrom.appId,callback:function(t){e.$set(e.editAdConfigFrom,"appId",t)},expression:"editAdConfigFrom.appId"}},e._l(e.appList,function(e){return o("el-option",{attrs:{label:e.label,value:e.value}})}),1)],1),e._v(" "),o("el-form-item",{attrs:{label:"整改目标",prop:"fxType"}},[o("el-radio-group",{model:{value:e.editAdConfigFrom.fxType,callback:function(t){e.$set(e.editAdConfigFrom,"fxType",t)},expression:"editAdConfigFrom.fxType"}},[o("el-radio-button",{attrs:{label:"1"}},[e._v("当前应用")]),e._v(" "),o("el-radio-button",{attrs:{label:"2"}},[e._v("单个广告平台")]),e._v(" "),o("el-radio-button",{attrs:{label:"3"}},[e._v("单个广告位")])],1)],1),e._v(" "),2==e.editAdConfigFrom.fxType?o("div",[o("el-form-item",{attrs:{label:"广告平台"}},[o("el-select",{staticClass:"select-150",attrs:{multiple:"",placeholder:"广告平台",filterable:""},model:{value:e.editAdConfigFrom.platformLimit,callback:function(t){e.$set(e.editAdConfigFrom,"platformLimit",t)},expression:"editAdConfigFrom.platformLimit"}},e._l(e.platformList,function(e){return o("el-option",{attrs:{label:e.label,value:e.value}})}),1)],1)],1):e._e(),e._v(" "),3==e.editAdConfigFrom.fxType?o("div",[o("el-form-item",{attrs:{label:"我方广告位ID",prop:"adId"}},[o("el-input",{attrs:{type:"number"},model:{value:e.editAdConfigFrom.adId,callback:function(t){e.$set(e.editAdConfigFrom,"adId",t)},expression:"editAdConfigFrom.adId"}})],1),e._v(" "),o("el-form-item",{attrs:{label:"广告类型",prop:"type"}},[o("el-radio-group",{model:{value:e.editAdConfigFrom.type,callback:function(t){e.$set(e.editAdConfigFrom,"type",t)},expression:"editAdConfigFrom.type"}},[o("el-radio-button",{attrs:{label:"1"}},[e._v("目标底价")]),e._v(" "),o("el-radio-button",{attrs:{label:"2"}},[e._v("实时竞价")])],1)],1)],1):e._e(),e._v(" "),1==e.editAdConfigFrom.ocpcType?o("div",[o("el-form-item",{attrs:{label:"投放用户来源",prop:"ocpcList"}},[o("el-checkbox-group",{model:{value:e.editAdConfigFrom.ocpcList,callback:function(t){e.$set(e.editAdConfigFrom,"ocpcList",t)},expression:"editAdConfigFrom.ocpcList"}},[o("el-checkbox",{attrs:{label:"toutiao",border:""}}),e._v(" "),o("el-checkbox",{attrs:{label:"kuaishou_normal",border:""}}),e._v(" "),o("el-checkbox",{attrs:{label:"kuaishou_daren",border:""}}),e._v(" "),o("el-checkbox",{attrs:{label:"guangdiantong",border:""}}),e._v(" "),o("el-checkbox",{attrs:{label:"baidufeed",border:""}}),e._v(" "),o("el-checkbox",{attrs:{label:"oppo",border:""}}),e._v(" "),o("el-checkbox",{attrs:{label:"vivo",border:""}}),e._v(" "),o("el-checkbox",{attrs:{label:"ziranliang",border:""}})],1)],1)],1):e._e()],1),e._v(" "),o("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[o("el-button",{on:{click:function(t){e.isShowEditAdConfigFrom=!1}}},[e._v("取 消")]),e._v(" "),o("el-button",{attrs:{type:"primary"},on:{click:function(t){return e.editAdConfig()}}},[e._v("确 定")])],1)],1),e._v(" "),o("el-dialog",{attrs:{title:"新建整改广告位",visible:e.isShowAddAdConfigFrom},on:{"update:visible":function(t){e.isShowAddAdConfigFrom=t}}},[o("el-form",{ref:"addAdConfigFrom",attrs:{model:e.addAdConfigFrom,"label-width":"200px"}},[o("el-form-item",{attrs:{label:"开关",prop:"state"}},[o("el-switch",{attrs:{"active-color":"#13ce66","inactive-color":"#ff4949","active-value":1,"inactive-value":0},model:{value:e.addAdConfigFrom.state,callback:function(t){e.$set(e.addAdConfigFrom,"state",t)},expression:"addAdConfigFrom.state"}})],1),e._v(" "),o("el-form-item",{attrs:{label:"产品"}},[o("el-select",{staticClass:"select-150",attrs:{placeholder:"产品",filterable:""},model:{value:e.addAdConfigFrom.appId,callback:function(t){e.$set(e.addAdConfigFrom,"appId",t)},expression:"addAdConfigFrom.appId"}},e._l(e.appList,function(e){return o("el-option",{attrs:{label:e.label,value:e.value}})}),1)],1),e._v(" "),o("el-form-item",{attrs:{label:"整改目标",prop:"fxType"}},[o("el-radio-group",{model:{value:e.addAdConfigFrom.fxType,callback:function(t){e.$set(e.addAdConfigFrom,"fxType",t)},expression:"addAdConfigFrom.fxType"}},[o("el-radio-button",{attrs:{label:"1"}},[e._v("当前应用")]),e._v(" "),o("el-radio-button",{attrs:{label:"2"}},[e._v("单个广告平台")]),e._v(" "),o("el-radio-button",{attrs:{label:"3"}},[e._v("单个广告位")])],1)],1),e._v(" "),2==e.addAdConfigFrom.fxType?o("div",[o("el-form-item",{attrs:{label:"广告平台"}},[o("el-select",{staticClass:"select-150",attrs:{multiple:"",placeholder:"广告平台",filterable:""},model:{value:e.addAdConfigFrom.platformLimit,callback:function(t){e.$set(e.addAdConfigFrom,"platformLimit",t)},expression:"addAdConfigFrom.platformLimit"}},e._l(e.platformList,function(e){return o("el-option",{attrs:{label:e.label,value:e.value}})}),1)],1)],1):e._e(),e._v(" "),3==e.addAdConfigFrom.fxType?o("div",[o("el-form-item",{attrs:{label:"我方广告位ID",prop:"adId"}},[o("el-input",{attrs:{type:"number"},model:{value:e.addAdConfigFrom.adId,callback:function(t){e.$set(e.addAdConfigFrom,"adId",t)},expression:"addAdConfigFrom.adId"}})],1),e._v(" "),o("el-form-item",{attrs:{label:"广告类型",prop:"type"}},[o("el-radio-group",{model:{value:e.addAdConfigFrom.type,callback:function(t){e.$set(e.addAdConfigFrom,"type",t)},expression:"addAdConfigFrom.type"}},[o("el-radio-button",{attrs:{label:"1"}},[e._v("目标底价")]),e._v(" "),o("el-radio-button",{attrs:{label:"2"}},[e._v("实时竞价")])],1)],1)],1):e._e(),e._v(" "),1==e.addAdConfigFrom.ocpcType?o("div",[o("el-form-item",{attrs:{label:"投放用户来源",prop:"ocpcList"}},[o("el-checkbox-group",{model:{value:e.addAdConfigFrom.ocpcList,callback:function(t){e.$set(e.addAdConfigFrom,"ocpcList",t)},expression:"addAdConfigFrom.ocpcList"}},[o("el-checkbox",{attrs:{label:"toutiao",border:""}}),e._v(" "),o("el-checkbox",{attrs:{label:"kuaishou_normal",border:""}}),e._v(" "),o("el-checkbox",{attrs:{label:"kuaishou_daren",border:""}}),e._v(" "),o("el-checkbox",{attrs:{label:"guangdiantong",border:""}}),e._v(" "),o("el-checkbox",{attrs:{label:"baidufeed",border:""}}),e._v(" "),o("el-checkbox",{attrs:{label:"oppo",border:""}}),e._v(" "),o("el-checkbox",{attrs:{label:"vivo",border:""}}),e._v(" "),o("el-checkbox",{attrs:{label:"ziranliang",border:""}})],1)],1)],1):e._e()],1),e._v(" "),o("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[o("el-button",{on:{click:function(t){e.isShowAddAdConfigFrom=!1}}},[e._v("取 消")]),e._v(" "),o("el-button",{attrs:{type:"primary"},on:{click:function(t){return e.addAdConfig()}}},[e._v("确 定")])],1)],1),e._v(" "),o("el-dialog",{attrs:{title:"批量用户整改",visible:e.isShowAddUserConfigFrom},on:{"update:visible":function(t){e.isShowAddUserConfigFrom=t}}},[o("el-form",{ref:"addUserAdConfigFrom",attrs:{model:e.addUserAdConfigFrom,"label-width":"200px"}},[o("el-form-item",{attrs:{label:"产品"}},[o("el-select",{staticClass:"select-150",attrs:{placeholder:"产品",filterable:""},model:{value:e.addUserAdConfigFrom.appId,callback:function(t){e.$set(e.addUserAdConfigFrom,"appId",t)},expression:"addUserAdConfigFrom.appId"}},e._l(e.appList,function(e){return o("el-option",{attrs:{label:e.label,value:e.value}})}),1)],1),e._v(" "),o("el-form-item",{attrs:{label:"生效用户",prop:"targetId"}},[o("el-input",{attrs:{type:"textarea",rows:2,placeholder:"(英文,分割)"},model:{value:e.addUserAdConfigFrom.targetIds,callback:function(t){e.$set(e.addUserAdConfigFrom,"targetIds",t)},expression:"addUserAdConfigFrom.targetIds"}})],1),e._v(" "),o("el-form-item",{attrs:{label:"生效时长(单位 天)",prop:"day"}},[o("el-input",{attrs:{type:"number"},model:{value:e.addUserAdConfigFrom.day,callback:function(t){e.$set(e.addUserAdConfigFrom,"day",t)},expression:"addUserAdConfigFrom.day"}})],1),e._v(" "),o("el-form-item",{attrs:{label:"跳过以下平台广告"}},[o("el-select",{staticClass:"select-150",attrs:{multiple:"",placeholder:"广告平台",filterable:""},model:{value:e.addUserAdConfigFrom.platformLimit,callback:function(t){e.$set(e.addUserAdConfigFrom,"platformLimit",t)},expression:"addUserAdConfigFrom.platformLimit"}},e._l(e.platformList,function(e){return o("el-option",{attrs:{label:e.label,value:e.value}})}),1)],1)],1),e._v(" "),o("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[o("el-button",{on:{click:function(t){e.isShowAddUserConfigFrom=!1}}},[e._v("取 消")]),e._v(" "),o("el-button",{attrs:{type:"primary"},on:{click:function(t){return e.addUserAdConfig()}}},[e._v("确 定")])],1)],1),e._v(" "),o("el-alert",{attrs:{title:"配置生效时间",type:"info",description:"批量用户整改配置完成立即生效，用户下次拉取的策略不再包含配置的内容，广告位整改配置生效时间为10min，即10min后用户可请求整改配置调整的广告。","show-icon":""}}),e._v(" "),o("el-divider"),e._v(" "),o("el-alert",{attrs:{title:"配置生效顺序",type:"warning",description:"广告位整改可以和 应用整改/平台整改同时生效，若同时配置三者，只会生效【平台整改】和【广告位整改】，若用户自己设置了广告跳过策略,则生效【用户配置】和【广告位整改】","show-icon":""}}),e._v(" "),o("el-divider"),e._v(" "),o("el-row",[o("el-col",{attrs:{span:24}},[o("el-form",{attrs:{model:e.queryParam,size:"small",inline:""}},[o("el-form-item",{attrs:{label:"产品"}},[o("el-select",{staticClass:"select-150",attrs:{placeholder:"产品",filterable:""},model:{value:e.queryParam.product,callback:function(t){e.$set(e.queryParam,"product",t)},expression:"queryParam.product"}},e._l(e.appList,function(e){return o("el-option",{attrs:{label:e.label,value:e.value}})}),1)],1),e._v(" "),o("el-form-item",{staticStyle:{float:"right"}},[o("el-button",{attrs:{type:"warning"},on:{click:function(t){return e.getAdList()}}},[e._v("查询")]),e._v(" "),o("el-button",{attrs:{type:"primary",round:""},on:{click:function(t){return e.addAdConfigFromPre()}}},[e._v("广告位整改")]),e._v(" "),o("el-button",{attrs:{type:"primary",round:""},on:{click:function(t){return e.addUserConfigFromPre()}}},[e._v("批量用户整改")])],1)],1)],1)],1),e._v(" "),o("el-divider",{attrs:{"content-position":"left"}},[e._v("整改中广告位")]),e._v(" "),o("el-row",[o("el-col",{attrs:{span:24}},[o("el-table",{staticStyle:{width:"100%"},attrs:{data:e.adConfigList,stripe:"","highlight-current-row":""}},[o("el-table-column",{attrs:{label:"开关"},scopedSlots:e._u([{key:"default",fn:function(t){return[o("el-switch",{attrs:{"active-color":"#13ce66","inactive-color":"#ff4949","active-value":1,"inactive-value":0},on:{change:function(o){return e.changeSwitchFlag(t.$index,t.row)}},model:{value:t.row.state,callback:function(o){e.$set(t.row,"state",o)},expression:"scope.row.state"}})]}}])}),e._v(" "),o("el-table-column",{attrs:{prop:"id",label:"Id",width:"100px"}}),e._v(" "),o("el-table-column",{attrs:{prop:"appName",label:"产品",width:"100px"}}),e._v(" "),o("el-table-column",{attrs:{prop:"fxType",label:"整改类型"},scopedSlots:e._u([{key:"default",fn:function(t){return[1===t.row.fxType?o("el-tag",{attrs:{type:"danger"}},[e._v("应用整改")]):e._e(),e._v(" "),2===t.row.fxType?o("el-tag",{attrs:{type:"warning"}},[e._v("广告平台整改")]):e._e(),e._v(" "),3===t.row.fxType?o("el-tag",{attrs:{type:"info"}},[e._v("广告位整改")]):e._e()]}}])}),e._v(" "),o("el-table-column",{attrs:{prop:"adId",label:"广告ID"}}),e._v(" "),o("el-table-column",{attrs:{prop:"updateTime",label:"最后修改"}}),e._v(" "),o("el-table-column",{attrs:{label:"操作",width:"200px"},scopedSlots:e._u([{key:"default",fn:function(t){return[o("el-button",{attrs:{size:"small"},on:{click:function(o){return e.updateAdFxConfigPre(t.row)}}},[e._v("修改")])]}}])})],1)],1)],1),e._v(" "),o("el-pagination",{staticStyle:{float:"right"},attrs:{"current-page":e.queryParam.pageNo,"page-size":10,layout:"total, prev, pager, next, jumper",total:e.listCountNum,background:""},on:{"current-change":e.handleCurrentChangeForPlan}})],1)},staticRenderFns:[]}},755:function(e,t,o){var a=o(452);"string"==typeof a&&(a=[[e.i,a,""]]),a.locals&&(e.exports=a.locals);o(90)("52d49946",a,!0)}});