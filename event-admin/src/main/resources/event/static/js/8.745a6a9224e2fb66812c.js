webpackJsonp([8],{211:function(t,e,a){a(752);var r=a(89)(a(438),a(733),null,null);t.exports=r.exports},438:function(t,e,a){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.default={data:function(){return{tableData:[],spanArrC1:[],spanArrC2:[],appList:[],willGrayUserCount:0,queryParam:{product:0,os:1},isShowEditScore:!1,rowData:{},osList:[{key:1,val:"Android"},{key:2,val:"IOS"}]}},created:function(){this.getAppList(),this.loadListInfo()},methods:{getAppList:function(){var t=this;t.$axios.post("dist/appList",{},{}).then(function(e){if(200===e.status){var a=e.data;0===a.code?t.appList=a.result:t.$message.error(a.result)}else t.$message.error("服务器异常！")})},loadListInfo:function(){var t=this,e=this;e.tableData=[],e.spanArrC1=[],e.spanArrC2=[],e.$axios.post("config/list",{},{params:e.queryParam}).then(function(a){if(200===a.status){var r=a.data;0===r.code?(e.tableData=r.result,t.getSpanArr(e.tableData)):e.$message.error(r.result)}else e.$message.error("服务器异常！")})},getSpanArr:function(t){for(var e=0;e<t.length;e++)0===e?(this.spanArrC1.push(1),this.spanArrC2.push(1),this.posC1=0,this.posC2=0):(t[e].pr===t[e-1].pr?(this.spanArrC1[this.posC1]+=1,this.spanArrC1.push(0)):(this.spanArrC1.push(1),this.posC1=e),t[e].type===t[e-1].type?(this.spanArrC2[this.posC2]+=1,this.spanArrC2.push(0)):(this.spanArrC2.push(1),this.posC2=e))},objectSpanMethod:function(t){var e=(t.row,t.column,t.rowIndex),a=t.columnIndex;if(0===a){var r=this.spanArrC1[e];return{rowspan:r,colspan:r>0?1:0}}if(1===a){var s=this.spanArrC2[e];return{rowspan:s,colspan:s>0?1:0}}},searchByThisConfig:function(){this.$message.error("该功能正在开发中....")},saveConfig:function(){var t={product:this.queryParam.product,os:this.queryParam.os,ruleList:this.tableData},e=this;e.$axios.post("config/refresh",t,{}).then(function(t){if(200===t.status){var a=t.data;0===a.code?e.$message.success("成功配置"):e.$message.error(a.result)}else e.$message.error("服务器异常！")})},showEditClickConfig:function(t){this.isShowEditScore=!0,this.rowData=t},updateThisPartScore:function(){for(var t=[],e=0;e<this.tableData.length;e++){var a=this.tableData[e];this.rowData.ruleId===a.ruleId&&(a.scroe=this.rowData.score),t.push(a)}this.tableData=t,this.isShowEditScore=!1}}}},449:function(t,e,a){e=t.exports=a(29)(),e.push([t.i,".width-100{width:100px}.select-150 .el-input{width:150px}.inline-block{display:inline-block}",""])},733:function(t,e){t.exports={render:function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",{staticClass:"table"},[a("div",{staticClass:"crumbs"},[a("el-breadcrumb",{attrs:{separator:"/"}},[a("el-breadcrumb-item",[a("i",{staticClass:"el-icon-menu"}),t._v(" 产品风险控制管理")]),t._v(" "),a("el-breadcrumb-item",[t._v("产品风险控制")])],1)],1),t._v(" "),a("el-row",[a("el-col",{attrs:{span:24}},[a("el-form",{attrs:{model:t.queryParam,size:"small",inline:""}},[a("el-form-item",{attrs:{label:"产品"}},[a("el-select",{staticClass:"select-150",attrs:{placeholder:"配置类型",filterable:""},on:{change:function(e){return t.loadListInfo()}},model:{value:t.queryParam.product,callback:function(e){t.$set(t.queryParam,"product",e)},expression:"queryParam.product"}},t._l(t.appList,function(t){return a("el-option",{attrs:{label:t.label,value:t.value}})}),1)],1),t._v(" "),a("el-form-item",{attrs:{label:"系统"}},[a("el-select",{staticClass:"select-150",attrs:{filterable:"",placeholder:"系统"},on:{change:function(e){return t.loadListInfo()}},model:{value:t.queryParam.os,callback:function(e){t.$set(t.queryParam,"os",e)},expression:"queryParam.os"}},t._l(t.osList,function(t){return a("el-option",{attrs:{label:t.val,value:t.key}})}),1)],1),t._v(" "),a("el-form-item",{staticStyle:{float:"right"},attrs:{label:"预计拉黑人数"}},[a("el-input",{staticClass:"width-150",attrs:{disabled:"true"},model:{value:t.willGrayUserCount,callback:function(e){t.willGrayUserCount=e},expression:"willGrayUserCount"}})],1),t._v(" "),a("el-form-item",{staticStyle:{float:"right"}},[a("el-button",{attrs:{type:"primary",plain:"",icon:"el-icon-search"},on:{click:function(e){return t.searchByThisConfig()}}},[t._v("按照当前配置查询拉灰人数")])],1),t._v(" "),a("el-form-item",{staticStyle:{float:"right"}},[a("el-button",{attrs:{type:"warning"},on:{click:function(e){return t.saveConfig()}}},[t._v("保存")])],1)],1)],1)],1),t._v(" "),a("el-divider",{attrs:{"content-position":"left"}},[t._v("反作弊分值权重")]),t._v(" "),a("el-dialog",{attrs:{title:"修改分值",visible:t.isShowEditScore},on:{"update:visible":function(e){t.isShowEditScore=e}}},[a("el-form",{attrs:{model:t.rowData,"label-width":"200px"}},[a("el-form-item",{attrs:{label:"描述",prop:"desc"}},[a("el-input",{attrs:{disabled:""},model:{value:t.rowData.desc,callback:function(e){t.$set(t.rowData,"desc",e)},expression:"rowData.desc"}})],1),t._v(" "),a("el-form-item",{attrs:{label:"分值",prop:"score"}},[a("el-input",{staticClass:"width-100",model:{value:t.rowData.score,callback:function(e){t.$set(t.rowData,"score",e)},expression:"rowData.score"}})],1)],1),t._v(" "),a("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[a("el-button",{on:{click:function(e){t.isShowEditScore=!1}}},[t._v("取 消")]),t._v(" "),a("el-button",{attrs:{type:"primary"},on:{click:function(e){return t.updateThisPartScore()}}},[t._v("更 新")])],1)],1),t._v(" "),a("el-table",{staticStyle:{width:"100%","margin-top":"20px"},attrs:{data:t.tableData,"span-method":t.objectSpanMethod,border:""}},[a("el-table-column",{attrs:{prop:"pr",label:"维度",width:"100"}}),t._v(" "),a("el-table-column",{attrs:{prop:"type",label:"类型"}}),t._v(" "),a("el-table-column",{attrs:{prop:"desc",label:"描述"}}),t._v(" "),a("el-table-column",{attrs:{prop:"score",label:"分值",width:"100"}}),t._v(" "),a("el-table-column",{attrs:{label:"操作",width:"200px"},scopedSlots:t._u([{key:"default",fn:function(e){return[a("el-button",{attrs:{size:"small"},on:{click:function(a){return t.showEditClickConfig(e.row)}}},[t._v("修改分值")])]}}])})],1)],1)},staticRenderFns:[]}},752:function(t,e,a){var r=a(449);"string"==typeof r&&(r=[[t.i,r,""]]),r.locals&&(t.exports=r.locals);a(90)("6ab64870",r,!0)}});