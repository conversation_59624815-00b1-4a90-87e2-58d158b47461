webpackJsonp([16],{219:function(e,t,r){var a=r(89)(r(446),r(737),null,null);e.exports=a.exports},446:function(e,t,r){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.default={data:function(){return{appList:[],isShowGrayUser:!1,isShowUnGrayUser:!1,isShowQueryGrayUser:!1,isShowBatchGrayUser:!1,isShowQueryGrayUserResult:!1,isShowBatchSolveAdUser:!1,queryGrayUserForm:{targetIds:"",targetType:"1"},queryUnGrayUserForm:{targetIds:"",targetType:"1"},updateBatchGrayUserForm:{targetIds:"",targetType:"1",isGlobalGray:"1",appId:""},updateBatchCloseAdForm:{appId:"",userIds:"",platformList:[]},queryGrayUserResultData:[]}},created:function(){this.checkIsLogin(),this.getAppList()},methods:{checkIsLogin:function(){var e=this;e.$axios.post("api/check",{},{}).then(function(t){0!==t.data.code&&e.$message.error(t.data.message)})},getAppList:function(){var e=this;e.$axios.post("dist/appList",{},{}).then(function(t){if(200===t.status){var r=t.data;0===r.code?e.appList=r.result:e.$message.error(r.result)}else e.$message.error("服务器异常！")})},waitUp:function(){this.$message.error("施工中...")},queryUserGray:function(){var e=this;e.$axios.post("gray/query",{},{params:this.queryGrayUserForm}).then(function(t){if(200===t.status){var r=t.data;0===r.code?(e.queryGrayUserResultData=r.result,e.isShowQueryGrayUserResult=!0):e.$message.error(r.result)}else e.$message.error("服务器异常！")})},clearAndClose:function(){this.isShowBatchGrayUser=!1,this.updateBatchGrayUserForm.appId=""},batchGaryUser:function(){var e=this;e.$axios.post("gray/doGray",this.updateBatchGrayUserForm,{}).then(function(t){if(200===t.status){var r=t.data;0===r.code?(e.$message.success("已提交拉黑处理"),e.isShowBatchGrayUser=!1):e.$message.error(r.result)}else e.$message.error("服务器异常！")})},unGrayUser:function(){var e=this;e.$axios.post("gray/doUnGray",this.queryUnGrayUserForm,{}).then(function(t){if(200===t.status){var r=t.data;0===r.code?(e.$message.success("已提交解除拉黑处理"),e.isShowUnGrayUser=!1):e.$message.error(r.result)}else e.$message.error("服务器异常！")})},grayUser:function(){}}}},737:function(e,t){e.exports={render:function(){var e=this,t=e.$createElement,r=e._self._c||t;return r("div",{staticClass:"table"},[r("div",{staticClass:"crumbs"},[r("el-breadcrumb",{attrs:{separator:"/"}},[r("el-breadcrumb-item",[r("i",{staticClass:"el-icon-menu"}),e._v(" 用户封禁状态查询")]),e._v(" "),r("el-breadcrumb-item",[e._v("用户封禁")])],1),e._v(" "),r("el-divider",{attrs:{"content-position":"left"}},[e._v("可执行操作")]),e._v(" "),r("el-dialog",{attrs:{title:"查询用户封禁状态",visible:e.isShowQueryGrayUser},on:{"update:visible":function(t){e.isShowQueryGrayUser=t}}},[r("el-dialog",{attrs:{title:"查询结果",visible:e.isShowQueryGrayUserResult,"append-to-body":"true"},on:{"update:visible":function(t){e.isShowQueryGrayUserResult=t}}},[r("el-row",[r("el-col",{attrs:{span:24}},[r("el-table",{staticStyle:{width:"100%"},attrs:{data:e.queryGrayUserResultData,stripe:"","highlight-current-row":""}},[r("el-table-column",{attrs:{prop:"product",label:"产品"}}),e._v(" "),r("el-table-column",{attrs:{prop:"targetId",label:"封禁对象"}}),e._v(" "),r("el-table-column",{attrs:{prop:"targetType",label:"封禁类型"},scopedSlots:e._u([{key:"default",fn:function(t){return[1===t.row.targetType?r("span",[e._v("device_id")]):2===t.row.targetType?r("span",[e._v("user_id")]):3===t.row.targetType?r("span",[e._v("union_id")]):e._e()]}}])}),e._v(" "),r("el-table-column",{attrs:{prop:"reason",label:"封禁原因"}}),e._v(" "),r("el-table-column",{attrs:{prop:"createTime",label:"封禁时间"}})],1)],1)],1),e._v(" "),r("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[r("el-button",{on:{click:function(t){e.isShowQueryGrayUserResult=!1}}},[e._v("取 消")])],1)],1),e._v(" "),r("el-form",{ref:"queryForm",attrs:{model:e.queryGrayUserForm,"label-width":"200px",rules:e.rules}},[r("el-form-item",{attrs:{label:"查询内容",prop:"targetId"}},[r("el-input",{attrs:{type:"textarea",rows:2,placeholder:"(英文,分割)"},model:{value:e.queryGrayUserForm.targetIds,callback:function(t){e.$set(e.queryGrayUserForm,"targetIds",t)},expression:"queryGrayUserForm.targetIds"}})],1),e._v(" "),r("el-form-item",{attrs:{label:"查询类型",prop:"targetType"}},[r("el-select",{staticClass:"select-150",attrs:{filterable:"",placeholder:"请选择"},model:{value:e.queryGrayUserForm.targetType,callback:function(t){e.$set(e.queryGrayUserForm,"targetType",t)},expression:"queryGrayUserForm.targetType"}},[r("el-option",{attrs:{label:"device_id",value:"1"}}),e._v(" "),r("el-option",{attrs:{label:"user_id",value:"2"}}),e._v(" "),r("el-option",{attrs:{label:"union_id",value:"3"}})],1)],1)],1),e._v(" "),r("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[r("el-button",{on:{click:function(t){e.isShowQueryGrayUser=!1}}},[e._v("取 消")]),e._v(" "),r("el-button",{attrs:{type:"primary"},on:{click:function(t){return e.queryUserGray()}}},[e._v("确 定")])],1)],1),e._v(" "),r("el-dialog",{attrs:{title:"批量拉黑用户",visible:e.isShowBatchGrayUser},on:{"update:visible":function(t){e.isShowBatchGrayUser=t}}},[r("el-form",{attrs:{model:e.queryGrayUserForm,"label-width":"200px"}},[r("el-form-item",{attrs:{label:"拉黑目标",prop:"targetIds"}},[r("el-input",{attrs:{type:"textarea",rows:2,placeholder:"(英文,分割)"},model:{value:e.updateBatchGrayUserForm.targetIds,callback:function(t){e.$set(e.updateBatchGrayUserForm,"targetIds",t)},expression:"updateBatchGrayUserForm.targetIds"}})],1),e._v(" "),r("el-form-item",{attrs:{label:"拉黑类型",prop:"targetType"}},[r("el-select",{staticClass:"select-150",attrs:{filterable:"",placeholder:"请选择"},model:{value:e.updateBatchGrayUserForm.targetType,callback:function(t){e.$set(e.updateBatchGrayUserForm,"targetType",t)},expression:"updateBatchGrayUserForm.targetType"}},[r("el-option",{attrs:{label:"device_id",value:"1"}}),e._v(" "),r("el-option",{attrs:{label:"user_id",value:"2"}})],1)],1),e._v(" "),r("el-form-item",{attrs:{label:"是否全局拉黑",prop:"isGlobalGray"}},[[r("el-radio",{attrs:{label:"1"},model:{value:e.updateBatchGrayUserForm.isGlobalGray,callback:function(t){e.$set(e.updateBatchGrayUserForm,"isGlobalGray",t)},expression:"updateBatchGrayUserForm.isGlobalGray"}},[e._v("全局拉黑")]),e._v(" "),r("el-radio",{attrs:{label:"2"},model:{value:e.updateBatchGrayUserForm.isGlobalGray,callback:function(t){e.$set(e.updateBatchGrayUserForm,"isGlobalGray",t)},expression:"updateBatchGrayUserForm.isGlobalGray"}},[e._v("单产品拉黑")])]],2),e._v(" "),"2"===e.updateBatchGrayUserForm.isGlobalGray?r("div",[r("el-form-item",{attrs:{label:"产品",prop:"appId"}},[r("el-select",{staticClass:"select-150",attrs:{placeholder:"产品",filterable:""},model:{value:e.updateBatchGrayUserForm.appId,callback:function(t){e.$set(e.updateBatchGrayUserForm,"appId",t)},expression:"updateBatchGrayUserForm.appId"}},e._l(e.appList,function(e){return r("el-option",{attrs:{label:e.label,value:e.value}})}),1)],1)],1):e._e()],1),e._v(" "),r("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[r("el-button",{on:{click:function(t){return e.clearAndClose()}}},[e._v("取 消")]),e._v(" "),r("el-button",{attrs:{type:"primary"},on:{click:function(t){return e.batchGaryUser()}}},[e._v("确 定")])],1)],1),e._v(" "),r("el-dialog",{attrs:{title:"批量解除用户拉黑",visible:e.isShowUnGrayUser},on:{"update:visible":function(t){e.isShowUnGrayUser=t}}},[r("el-form",{attrs:{model:e.queryUnGrayUserForm,"label-width":"200px"}},[r("el-form-item",{attrs:{label:"解除目标",prop:"targetIds"}},[r("el-input",{attrs:{type:"textarea",rows:2,placeholder:"(英文,分割)"},model:{value:e.queryUnGrayUserForm.targetIds,callback:function(t){e.$set(e.queryUnGrayUserForm,"targetIds",t)},expression:"queryUnGrayUserForm.targetIds"}})],1),e._v(" "),r("el-form-item",{attrs:{label:"解除类型",prop:"targetType"}},[r("el-select",{staticClass:"select-150",attrs:{filterable:"",placeholder:"请选择"},model:{value:e.queryUnGrayUserForm.targetType,callback:function(t){e.$set(e.queryUnGrayUserForm,"targetType",t)},expression:"queryUnGrayUserForm.targetType"}},[r("el-option",{attrs:{label:"device_id",value:"1"}}),e._v(" "),r("el-option",{attrs:{label:"user_id",value:"2"}}),e._v(" "),r("el-option",{attrs:{label:"union_id",value:"3"}})],1)],1)],1),e._v(" "),r("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[r("el-button",{on:{click:function(t){e.isShowUnGrayUser=!1}}},[e._v("取 消")]),e._v(" "),r("el-button",{attrs:{type:"primary"},on:{click:function(t){return e.unGrayUser()}}},[e._v("确 定")])],1)],1),e._v(" "),r("el-dialog",{attrs:{title:"批量用户可观看视频平台",visible:e.isShowBatchSolveAdUser},on:{"update:visible":function(t){e.isShowBatchSolveAdUser=t}}},[r("el-form",{ref:"queryForm",attrs:{model:e.updateBatchCloseAdForm,"label-width":"200px",rules:e.rules}},[r("el-form-item",{attrs:{label:"产品APP_Id",prop:"appId"}},[r("el-select",{staticClass:"select-150",attrs:{placeholder:"产品",filterable:""},model:{value:e.updateBatchCloseAdForm.appId,callback:function(t){e.$set(e.updateBatchCloseAdForm,"appId",t)},expression:"updateBatchCloseAdForm.appId"}},e._l(e.appList,function(e){return r("el-option",{attrs:{label:e.label,value:e.value}})}),1)],1),e._v(" "),r("el-form-item",{attrs:{label:"用户Id",prop:"userId"}},[r("el-input",{model:{value:e.updateBatchCloseAdForm.userIds,callback:function(t){e.$set(e.updateBatchCloseAdForm,"userIds",t)},expression:"updateBatchCloseAdForm.userIds"}})],1),e._v(" "),r("el-form-item",{attrs:{label:"跳过不观看的平台",prop:"platformList"}},[r("el-checkbox-group",{model:{value:e.updateBatchCloseAdForm.platformList,callback:function(t){e.$set(e.updateBatchCloseAdForm,"platformList",t)},expression:"updateBatchCloseAdForm.platformList"}},[r("el-checkbox",{attrs:{label:"穿山甲",border:""}}),e._v(" "),r("el-checkbox",{attrs:{label:"广点通",border:""}}),e._v(" "),r("el-checkbox",{attrs:{label:"快手",border:""}}),e._v(" "),r("el-checkbox",{attrs:{label:"VIVO",border:""}}),e._v(" "),r("el-checkbox",{attrs:{label:"OPPO",border:""}}),e._v(" "),r("el-checkbox",{attrs:{label:"百度",border:""}}),e._v(" "),r("el-checkbox",{attrs:{label:"小米",border:""}}),e._v(" "),r("el-checkbox",{attrs:{label:"华为",border:""}})],1)],1)],1),e._v(" "),r("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[r("el-button",{on:{click:function(t){e.isShowBatchSolveAdUser=!1}}},[e._v("取 消")]),e._v(" "),r("el-button",{attrs:{type:"primary"},on:{click:function(t){return e.updateUserSkipPlat()}}},[e._v("更 新")])],1)],1),e._v(" "),r("el-form",{attrs:{inline:!0}},[r("el-form-item",{staticStyle:{float:"left"}},[r("el-button",{attrs:{type:"primary",round:""},on:{click:function(t){e.isShowQueryGrayUser=!0}}},[e._v("批量查询用户拉黑状态")]),e._v(" "),r("el-button",{attrs:{type:"primary",round:""},on:{click:function(t){e.isShowBatchGrayUser=!0}}},[e._v("批量手动拉黑用户")]),e._v(" "),r("el-button",{attrs:{type:"primary",round:""},on:{click:function(t){e.isShowUnGrayUser=!0}}},[e._v("批量解除用户拉黑")]),e._v(" "),r("el-button",{attrs:{type:"primary",round:""},on:{click:function(t){e.isShowBatchSolveAdUser=!0}}},[e._v("批量用户广告限制")]),e._v(" "),r("el-button",{attrs:{type:"primary",round:""},on:{click:function(t){return e.waitUp()}}},[e._v("解除用户拉灰")]),e._v(" "),r("el-button",{attrs:{type:"primary",round:""},on:{click:function(t){return e.waitUp()}}},[e._v("加入用户白名单")])],1)],1)],1)])},staticRenderFns:[]}}});