webpackJsonp([5],{215:function(e,t,r){r(766);var a=r(89)(r(442),r(748),"data-v-dff70b84",null);e.exports=a.exports},274:function(e,t,r){e.exports={default:r(275),__esModule:!0}},275:function(e,t,r){var a=r(14),s=a.JSON||(a.JSON={stringify:JSON.stringify});e.exports=function(e){return s.stringify.apply(s,arguments)}},442:function(e,t,r){"use strict";Object.defineProperty(t,"__esModule",{value:!0});var a=r(274),s=r.n(a);t.default={name:"UserInfo",data:function(){return{queryParam:{appId:395,userId:0},appList:[],userInfoList:[]}},created:function(){this.getAppList()},methods:{getAppList:function(){var e=this;e.$axios.post("dist/appList",{},{}).then(function(t){if(200===t.status){var r=t.data;0===r.code?e.appList=r.result:e.$message.error(r.result)}else e.$message.error("服务器异常！")})},queryDetail:function(e){var t={appId:e.appId,userId:e.userId};this.$router.push({path:"/userDetail",query:{target:s()(t)}})},queryUserInfo:function(){var e=this;e.$axios.post("user/searchUser",{},{params:e.queryParam}).then(function(t){if(200===t.status){var r=t.data;0===r.code?e.userInfoList=r.result:e.$message.error(r.result)}else e.$message.error("服务器异常！")})}}}},463:function(e,t,r){t=e.exports=r(29)(),t.push([e.i,".width-100[data-v-dff70b84]{width:100px}.select-150 .el-input[data-v-dff70b84]{width:150px}.inline-block[data-v-dff70b84]{display:inline-block}",""])},748:function(e,t){e.exports={render:function(){var e=this,t=e.$createElement,r=e._self._c||t;return r("div",{attrs:{id:"user-info"}},[r("el-row",[r("el-col",{attrs:{span:24}},[r("el-form",{attrs:{model:e.queryParam,size:"small",inline:""}},[r("el-form-item",{attrs:{label:"产品列表"}},[r("el-select",{staticClass:"select-150",attrs:{placeholder:"产品",filterable:""},model:{value:e.queryParam.appId,callback:function(t){e.$set(e.queryParam,"appId",t)},expression:"queryParam.appId"}},e._l(e.appList,function(e){return r("el-option",{attrs:{label:e.label,value:e.value}})}),1)],1),e._v(" "),r("el-form-item",{attrs:{label:"用户ID"}},[r("el-input",{staticClass:"width-150",model:{value:e.queryParam.userId,callback:function(t){e.$set(e.queryParam,"userId",t)},expression:"queryParam.userId"}})],1),e._v(" "),r("el-form-item",[r("el-button",{attrs:{type:"primary",plain:"",icon:"el-icon-search",circle:""},on:{click:function(t){return e.queryUserInfo()}}})],1)],1)],1)],1),e._v(" "),r("el-divider",{attrs:{"content-position":"left"}},[e._v("查询结果列表")]),e._v(" "),r("el-row",[r("el-col",{attrs:{span:24}},[r("el-table",{staticStyle:{width:"100%"},attrs:{data:e.userInfoList,stripe:"","highlight-current-row":""}},[r("el-table-column",{attrs:{prop:"userId",label:"userId"}}),e._v(" "),r("el-table-column",{attrs:{prop:"nickName",label:"用户昵称"}}),e._v(" "),r("el-table-column",{attrs:{prop:"channel",label:"渠道"}}),e._v(" "),r("el-table-column",{attrs:{prop:"createTime",label:"注册时间",width:"180"}}),e._v(" "),r("el-table-column",{attrs:{label:"操作",width:"180"},scopedSlots:e._u([{key:"default",fn:function(t){return[r("el-button",{attrs:{size:"small"},on:{click:function(r){return e.queryDetail(t.row)}}},[e._v("查看用户详情\n                        ")])]}}])})],1)],1)],1)],1)},staticRenderFns:[]}},766:function(e,t,r){var a=r(463);"string"==typeof a&&(a=[[e.i,a,""]]),a.locals&&(e.exports=a.locals);r(90)("97b55ef6",a,!0)}});