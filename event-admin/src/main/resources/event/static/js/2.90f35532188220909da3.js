webpackJsonp([2],{213:function(t,e,n){n(763),n(764);var i=n(89)(n(440),n(746),null,null);t.exports=i.exports},440:function(t,e,n){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),function(t){e.default={name:"WechatPaymentDetail",data:function(){return{loading:!1,originalBodyStyles:null,loadedCSSElements:null,orderData:{transferStatus:"",transferAmount:"",transferRemark:"",wechatDetailOrderNo:"",merchantDetailOrderNo:"",receiverOpenId:"",receiverName:"",receiverIdCard:"-",appId:"",appName:"",executeUser:"SYSTEM",createTime:"",updateTime:""}}},created:function(){this.loadOrderData()},mounted:function(){this.saveOriginalGlobalStyles(),this.loadWechatPaymentCSSIsolated(),this.initWechatPaymentPlatform()},beforeDestroy:function(){this.completeStyleCleanup()},methods:{loadOrderData:function(){try{var t=this.$route.query.orderData;if(t){var e=JSON.parse(decodeURIComponent(t));console.log("接收到的订单数据:",e),this.orderData={transferStatus:this.getStatusText(e.status),transferAmount:this.formatAmount(e.amount),transferRemark:e.title||"",wechatDetailOrderNo:e.batchId||"",merchantDetailOrderNo:e.orderNo||"",receiverOpenId:e.openId||"",receiverName:"",receiverIdCard:"-",appId:e.appId||"",appName:e.appName||"",executeUser:"SYSTEM",createTime:this.formatDate(e.createTime),updateTime:this.formatDate(e.updateTime)},console.log("映射后的订单数据:",this.orderData)}else console.warn("未找到订单数据参数"),this.$message&&this.$message.warning("未找到订单数据，请从列表页面进入")}catch(t){console.error("解析订单数据失败:",t),this.$message&&this.$message.error("订单数据解析失败")}},formatAmount:function(t){return t||0===t?(t/100).toFixed(2):"0.00"},getStatusText:function(t){return 5===t?"转账成功":"转账失败"},formatDate:function(t){if(!t)return"";if("string"==typeof t)return t;var e=new Date(t);return isNaN(e.getTime())?"":e.getFullYear()+"-"+String(e.getMonth()+1).padStart(2,"0")+"-"+String(e.getDate()).padStart(2,"0")+" "+String(e.getHours()).padStart(2,"0")+":"+String(e.getMinutes()).padStart(2,"0")+":"+String(e.getSeconds()).padStart(2,"0")},saveOriginalGlobalStyles:function(){this.originalBodyStyles={fontFamily:window.getComputedStyle(document.body).fontFamily,fontSize:window.getComputedStyle(document.body).fontSize}},restoreOriginalGlobalStyles:function(){if(this.originalBodyStyles){var t=document.body;t.style.fontFamily=this.originalBodyStyles.fontFamily,t.style.fontSize=this.originalBodyStyles.fontSize}document.body.style.fontFamily='-apple-system, BlinkMacSystemFont, "Segoe UI", "Roboto", "Oxygen", "Ubuntu", "Cantarell", "Fira Sans", "Droid Sans", "Helvetica Neue", sans-serif',document.body.style.fontSize="14px",document.querySelectorAll("*:not(.wechat-payment-container):not(.wechat-payment-container *)").forEach(function(t){t.style&&t.style.fontFamily&&t.style.fontFamily.includes("Microsoft YaHei")&&(t.style.fontFamily=""),t.style&&t.style.fontSize&&parseFloat(t.style.fontSize)<14&&(t.style.fontSize="")})},loadWechatPaymentCSSIsolated:function(){var t=this,e=["https://gtimg.wechatpay.cn/resource/xres/build/td/wxpay/mch_basic/mch_lego/v3.5.5/common.css","https://gtimg.wechatpay.cn/resource/xres/build/mmpay_html/wxpay/mch_payment/mch_trans_template/v1.5.4/css/chunk-vendors.899c47af.css","https://gtimg.wechatpay.cn/resource/xres/build/mmpay_html/wxpay/mch_payment/mch_trans_template/v1.5.4/css/index.6578089a.css"];this.loadedCSSElements=[],e.forEach(function(e,n){var i=document.createElement("link");i.rel="stylesheet",i.type="text/css",i.href=e,i.id="wechat-payment-css-"+n,document.head.appendChild(i),t.loadedCSSElements.push(i)});var n=document.createElement("style");n.type="text/css",n.innerHTML="\n        /* 仅保护其他页面的字体，不影响当前微信支付页面 */\n        body:not(.wechat-payment-container) {\n          font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen', 'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue', sans-serif !important;\n          font-size: 14px !important;\n        }\n\n        /* 保护Element UI组件，但不在微信支付容器内时 */\n        body:not(.wechat-payment-container) .el-table,\n        body:not(.wechat-payment-container) .el-card,\n        body:not(.wechat-payment-container) .el-button,\n        body:not(.wechat-payment-container) .el-form {\n          font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen', 'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue', sans-serif !important;\n          font-size: 14px !important;\n        }\n\n\n\n        /* 修复页面滚动问题 */\n        html, body {\n          overflow-y: auto !important;\n          height: auto !important;\n        }\n\n        .wechat-payment-container {\n          height: auto !important;\n          overflow: visible !important;\n        }\n\n        .wechat-payment-container .content,\n        .wechat-payment-container .content-bd {\n          height: auto !important;\n          max-height: none !important;\n          background: #fafafa;\n          overflow: visible !important;\n        }\n\n        /* 修复明细详情表单项的缩进 - 让标签顶格显示 */\n        .wechat-payment-container .batch-detail-info .el-form-item__label {\n          width: 120px !important;\n          text-align: left !important;\n          padding: 0 !important;\n          margin: 0 !important;\n          margin-right: 10px !important;\n          display: inline-block !important;\n          vertical-align: top !important;\n        }\n\n        .wechat-payment-container .batch-detail-info .el-form-item__content {\n          display: inline-block !important;\n          width: calc(100% - 140px) !important;\n          vertical-align: top !important;\n          margin-left: 0 !important;\n        }\n      ",n.id="font-protection-css",document.head.appendChild(n),this.loadedCSSElements.push(n)},removeWechatPaymentCSS:function(){this.loadedCSSElements&&(this.loadedCSSElements.forEach(function(t){t&&t.parentNode&&t.parentNode.removeChild(t)}),this.loadedCSSElements=null),["wechat-payment-css-0","wechat-payment-css-1","wechat-payment-css-2","font-protection-css"].forEach(function(t){var e=document.getElementById(t);e&&e.remove()})},initWechatPaymentPlatform:function(){this.$nextTick(function(){void 0!==t&&(t(".profile-box").mouseover(function(){t(".profile-box").addClass("show-profile-bd")}),t(".profile-box").mouseout(function(){t(".profile-box").removeClass("show-profile-bd")}),"undefined"!=typeof MCH&&MCH.left&&MCH.left.init())})},applyReceipt:function(){var t=this;this.loading=!0,setTimeout(function(){t.loading=!1,t.$message?t.$message.success("申请成功！"):alert("申请成功！")},2e3)},completeStyleCleanup:function(){var t=this;this.removeWechatPaymentCSS(),this.restoreOriginalGlobalStyles(),setTimeout(function(){t.restoreOriginalGlobalStyles()},50)}}}}.call(e,n(727))},460:function(t,e,n){e=t.exports=n(29)(),e.push([t.i,".page-panel{font-family:Microsoft YaHei,\\\\5FAE\\8F6F\\96C5\\9ED1,Arial,sans-serif!important;font-size:14px!important;color:#333!important;min-height:100vh;isolation:isolate;contain:layout style}.header{position:relative;z-index:100;clear:both;margin-bottom:0!important;padding-bottom:0!important}.container-wrp{z-index:10}.container-wrp,.container.wrap{clear:both;margin-bottom:0!important;padding-bottom:0!important}.container-wrp,.container.wrap,.sidebar{position:relative;margin-top:0!important;padding-top:0!important}.sidebar{float:left;z-index:20}.menu{margin-top:0!important;padding-top:0!important}.menu dl{margin-top:1px!important;position:relative;top:1px}.content{position:relative;z-index:20;margin-top:0!important;top:auto!important;left:16px;padding-left:0!important}.content-bd{overflow:visible!important;max-height:none!important}.content,.content-bd,.panel-box{box-shadow:none!important;border-right:none!important}.wechat-payment-container .el-breadcrumb__inner a{font-weight:400!important}.wechat-payment-container .el-form-item__label{color:#333!important}.wechat-payment-container .el-form-item--mini .el-form-item__content{line-height:27px!important}.wechat-payment-container .panel-box.with-padding .el-form-item:nth-child(2) .el-form-item__content strong{font-size:13px!important}.wechat-payment-container .el-form-item--button.el-form-item--large .el-form-item__content{margin-left:130px!important}.wechat-payment-container .panel-box.with-padding .el-form-item--mini:not(.el-form-item--button) .el-form-item__label{width:110px!important}.wechat-payment-container #panel-body{padding-right:1px!important}.el-loading-mask{position:absolute;top:0;left:0;right:0;bottom:0;background:hsla(0,0%,100%,.8);z-index:9999}.el-loading-spinner{position:absolute;top:50%;left:50%;transform:translate(-50%,-50%)}.circular{width:42px;height:42px;animation:loading-rotate 2s linear infinite}.path{stroke:#00c250;stroke-width:2;stroke-dasharray:90,150;stroke-dashoffset:0;stroke-linecap:round;animation:loading-dash 1.5s ease-in-out infinite}.wechat-payment-container .pendant{position:fixed!important;right:0!important;bottom:0!important;z-index:99999!important;display:block!important;visibility:visible!important;opacity:1!important;pointer-events:auto!important;background:red!important;padding:20px!important}.wechat-payment-container .pendant .wrap{position:relative!important;display:block!important;visibility:visible!important;opacity:1!important}.wechat-payment-container .pendant .feed-back,.wechat-payment-container .pendant .gain-act,.wechat-payment-container .pendant .help-faq,.wechat-payment-container .pendant .mobile-data{display:block!important;visibility:visible!important;opacity:1!important;pointer-events:auto!important;position:relative!important;margin-bottom:10px!important;padding:10px!important;background:hsla(0,0%,100%,.9)!important;border:2px solid blue!important;border-radius:5px!important;color:#000!important;cursor:pointer!important}.wechat-payment-container .pendant i{display:inline-block!important;visibility:visible!important;opacity:1!important;font-size:16px!important;color:#000!important}@keyframes loading-rotate{to{transform:rotate(1turn)}}@keyframes loading-dash{0%{stroke-dasharray:1,150;stroke-dashoffset:0}50%{stroke-dasharray:90,150;stroke-dashoffset:-35}to{stroke-dasharray:90,150;stroke-dashoffset:-124}}",""])},461:function(t,e,n){e=t.exports=n(29)(),e.push([t.i,"body:not(.wechat-payment-container){line-height:1.6!important}body:not(.wechat-payment-container),body:not(.wechat-payment-container) .el-button,body:not(.wechat-payment-container) .el-card,body:not(.wechat-payment-container) .el-date-picker,body:not(.wechat-payment-container) .el-form,body:not(.wechat-payment-container) .el-input,body:not(.wechat-payment-container) .el-table{font-family:-apple-system,BlinkMacSystemFont,Segoe UI,Roboto,Oxygen,Ubuntu,Cantarell,Fira Sans,Droid Sans,Helvetica Neue,sans-serif!important;font-size:14px!important}",""])},727:function(t,e,n){var i,r;/*!
 * jQuery JavaScript Library v1.12.4
 * http://jquery.com/
 *
 * Includes Sizzle.js
 * http://sizzlejs.com/
 *
 * Copyright jQuery Foundation and other contributors
 * Released under the MIT license
 * http://jquery.org/license
 *
 * Date: 2016-05-20T17:17Z
 */
!function(e,n){"object"==typeof t&&"object"==typeof t.exports?t.exports=e.document?n(e,!0):function(t){if(!t.document)throw new Error("jQuery requires a window with a document");return n(t)}:n(e)}("undefined"!=typeof window?window:this,function(n,a){function o(t){var e=!!t&&"length"in t&&t.length,n=vt.type(t);return"function"!==n&&!vt.isWindow(t)&&("array"===n||0===e||"number"==typeof e&&e>0&&e-1 in t)}function s(t,e,n){if(vt.isFunction(e))return vt.grep(t,function(t,i){return!!e.call(t,i,t)!==n});if(e.nodeType)return vt.grep(t,function(t){return t===e!==n});if("string"==typeof e){if(kt.test(e))return vt.filter(e,t,n);e=vt.filter(e,t)}return vt.grep(t,function(t){return vt.inArray(t,e)>-1!==n})}function l(t,e){do{t=t[e]}while(t&&1!==t.nodeType);return t}function c(t){var e={};return vt.each(t.match(jt)||[],function(t,n){e[n]=!0}),e}function u(){st.addEventListener?(st.removeEventListener("DOMContentLoaded",d),n.removeEventListener("load",d)):(st.detachEvent("onreadystatechange",d),n.detachEvent("onload",d))}function d(){(st.addEventListener||"load"===n.event.type||"complete"===st.readyState)&&(u(),vt.ready())}function p(t,e,n){if(void 0===n&&1===t.nodeType){var i="data-"+e.replace(Ft,"-$1").toLowerCase();if("string"==typeof(n=t.getAttribute(i))){try{n="true"===n||"false"!==n&&("null"===n?null:+n+""===n?+n:qt.test(n)?vt.parseJSON(n):n)}catch(t){}vt.data(t,e,n)}else n=void 0}return n}function f(t){var e;for(e in t)if(("data"!==e||!vt.isEmptyObject(t[e]))&&"toJSON"!==e)return!1;return!0}function h(t,e,n,i){if(Ht(t)){var r,a,o=vt.expando,s=t.nodeType,l=s?vt.cache:t,c=s?t[o]:t[o]&&o;if(c&&l[c]&&(i||l[c].data)||void 0!==n||"string"!=typeof e)return c||(c=s?t[o]=ot.pop()||vt.guid++:o),l[c]||(l[c]=s?{}:{toJSON:vt.noop}),"object"!=typeof e&&"function"!=typeof e||(i?l[c]=vt.extend(l[c],e):l[c].data=vt.extend(l[c].data,e)),a=l[c],i||(a.data||(a.data={}),a=a.data),void 0!==n&&(a[vt.camelCase(e)]=n),"string"==typeof e?null==(r=a[e])&&(r=a[vt.camelCase(e)]):r=a,r}}function m(t,e,n){if(Ht(t)){var i,r,a=t.nodeType,o=a?vt.cache:t,s=a?t[vt.expando]:vt.expando;if(o[s]){if(e&&(i=n?o[s]:o[s].data)){vt.isArray(e)?e=e.concat(vt.map(e,vt.camelCase)):e in i?e=[e]:(e=vt.camelCase(e),e=e in i?[e]:e.split(" ")),r=e.length;for(;r--;)delete i[e[r]];if(n?!f(i):!vt.isEmptyObject(i))return}(n||(delete o[s].data,f(o[s])))&&(a?vt.cleanData([t],!0):mt.deleteExpando||o!=o.window?delete o[s]:o[s]=void 0)}}}function v(t,e,n,i){var r,a=1,o=20,s=i?function(){return i.cur()}:function(){return vt.css(t,e,"")},l=s(),c=n&&n[3]||(vt.cssNumber[e]?"":"px"),u=(vt.cssNumber[e]||"px"!==c&&+l)&&Pt.exec(vt.css(t,e));if(u&&u[3]!==c){c=c||u[3],n=n||[],u=+l||1;do{a=a||".5",u/=a,vt.style(t,e,u+c)}while(a!==(a=s()/l)&&1!==a&&--o)}return n&&(u=+u||+l||0,r=n[1]?u+(n[1]+1)*n[2]:+n[2],i&&(i.unit=c,i.start=u,i.end=r)),r}function g(t){var e=Ut.split("|"),n=t.createDocumentFragment();if(n.createElement)for(;e.length;)n.createElement(e.pop());return n}function y(t,e){var n,i,r=0,a=void 0!==t.getElementsByTagName?t.getElementsByTagName(e||"*"):void 0!==t.querySelectorAll?t.querySelectorAll(e||"*"):void 0;if(!a)for(a=[],n=t.childNodes||t;null!=(i=n[r]);r++)!e||vt.nodeName(i,e)?a.push(i):vt.merge(a,y(i,e));return void 0===e||e&&vt.nodeName(t,e)?vt.merge([t],a):a}function b(t,e){for(var n,i=0;null!=(n=t[i]);i++)vt._data(n,"globalEval",!e||vt._data(e[i],"globalEval"))}function x(t){$t.test(t.type)&&(t.defaultChecked=t.checked)}function _(t,e,n,i,r){for(var a,o,s,l,c,u,d,p=t.length,f=g(e),h=[],m=0;m<p;m++)if((o=t[m])||0===o)if("object"===vt.type(o))vt.merge(h,o.nodeType?[o]:o);else if(Gt.test(o)){for(l=l||f.appendChild(e.createElement("div")),c=(Wt.exec(o)||["",""])[1].toLowerCase(),d=Yt[c]||Yt._default,l.innerHTML=d[1]+vt.htmlPrefilter(o)+d[2],a=d[0];a--;)l=l.lastChild;if(!mt.leadingWhitespace&&Xt.test(o)&&h.push(e.createTextNode(Xt.exec(o)[0])),!mt.tbody)for(o="table"!==c||Vt.test(o)?"<table>"!==d[1]||Vt.test(o)?0:l:l.firstChild,a=o&&o.childNodes.length;a--;)vt.nodeName(u=o.childNodes[a],"tbody")&&!u.childNodes.length&&o.removeChild(u);for(vt.merge(h,l.childNodes),l.textContent="";l.firstChild;)l.removeChild(l.firstChild);l=f.lastChild}else h.push(e.createTextNode(o));for(l&&f.removeChild(l),mt.appendChecked||vt.grep(y(h,"input"),x),m=0;o=h[m++];)if(i&&vt.inArray(o,i)>-1)r&&r.push(o);else if(s=vt.contains(o.ownerDocument,o),l=y(f.appendChild(o),"script"),s&&b(l),n)for(a=0;o=l[a++];)zt.test(o.type||"")&&n.push(o);return l=null,f}function C(){return!0}function w(){return!1}function S(){try{return st.activeElement}catch(t){}}function T(t,e,n,i,r,a){var o,s;if("object"==typeof e){"string"!=typeof n&&(i=i||n,n=void 0);for(s in e)T(t,s,n,i,e[s],a);return t}if(null==i&&null==r?(r=n,i=n=void 0):null==r&&("string"==typeof n?(r=i,i=void 0):(r=i,i=n,n=void 0)),!1===r)r=w;else if(!r)return t;return 1===a&&(o=r,r=function(t){return vt().off(t),o.apply(this,arguments)},r.guid=o.guid||(o.guid=vt.guid++)),t.each(function(){vt.event.add(this,e,r,i,n)})}function k(t,e){return vt.nodeName(t,"table")&&vt.nodeName(11!==e.nodeType?e:e.firstChild,"tr")?t.getElementsByTagName("tbody")[0]||t.appendChild(t.ownerDocument.createElement("tbody")):t}function E(t){return t.type=(null!==vt.find.attr(t,"type"))+"/"+t.type,t}function N(t){var e=oe.exec(t.type);return e?t.type=e[1]:t.removeAttribute("type"),t}function D(t,e){if(1===e.nodeType&&vt.hasData(t)){var n,i,r,a=vt._data(t),o=vt._data(e,a),s=a.events;if(s){delete o.handle,o.events={};for(n in s)for(i=0,r=s[n].length;i<r;i++)vt.event.add(e,n,s[n][i])}o.data&&(o.data=vt.extend({},o.data))}}function A(t,e){var n,i,r;if(1===e.nodeType){if(n=e.nodeName.toLowerCase(),!mt.noCloneEvent&&e[vt.expando]){r=vt._data(e);for(i in r.events)vt.removeEvent(e,i,r.handle);e.removeAttribute(vt.expando)}"script"===n&&e.text!==t.text?(E(e).text=t.text,N(e)):"object"===n?(e.parentNode&&(e.outerHTML=t.outerHTML),mt.html5Clone&&t.innerHTML&&!vt.trim(e.innerHTML)&&(e.innerHTML=t.innerHTML)):"input"===n&&$t.test(t.type)?(e.defaultChecked=e.checked=t.checked,e.value!==t.value&&(e.value=t.value)):"option"===n?e.defaultSelected=e.selected=t.defaultSelected:"input"!==n&&"textarea"!==n||(e.defaultValue=t.defaultValue)}}function j(t,e,n,i){e=ct.apply([],e);var r,a,o,s,l,c,u=0,d=t.length,p=d-1,f=e[0],h=vt.isFunction(f);if(h||d>1&&"string"==typeof f&&!mt.checkClone&&ae.test(f))return t.each(function(r){var a=t.eq(r);h&&(e[0]=f.call(this,r,a.html())),j(a,e,n,i)});if(d&&(c=_(e,t[0].ownerDocument,!1,t,i),r=c.firstChild,1===c.childNodes.length&&(c=r),r||i)){for(s=vt.map(y(c,"script"),E),o=s.length;u<d;u++)a=c,u!==p&&(a=vt.clone(a,!0,!0),o&&vt.merge(s,y(a,"script"))),n.call(t[u],a,u);if(o)for(l=s[s.length-1].ownerDocument,vt.map(s,N),u=0;u<o;u++)a=s[u],zt.test(a.type||"")&&!vt._data(a,"globalEval")&&vt.contains(l,a)&&(a.src?vt._evalUrl&&vt._evalUrl(a.src):vt.globalEval((a.text||a.textContent||a.innerHTML||"").replace(se,"")));c=r=null}return t}function L(t,e,n){for(var i,r=e?vt.filter(e,t):t,a=0;null!=(i=r[a]);a++)n||1!==i.nodeType||vt.cleanData(y(i)),i.parentNode&&(n&&vt.contains(i.ownerDocument,i)&&b(y(i,"script")),i.parentNode.removeChild(i));return t}function O(t,e){var n=vt(e.createElement(t)).appendTo(e.body),i=vt.css(n[0],"display");return n.detach(),i}function H(t){var e=st,n=de[t];return n||(n=O(t,e),"none"!==n&&n||(ue=(ue||vt("<iframe frameborder='0' width='0' height='0'/>")).appendTo(e.documentElement),e=(ue[0].contentWindow||ue[0].contentDocument).document,e.write(),e.close(),n=O(t,e),ue.detach()),de[t]=n),n}function q(t,e){return{get:function(){return t()?void delete this.get:(this.get=e).apply(this,arguments)}}}function F(t){if(t in ke)return t;for(var e=t.charAt(0).toUpperCase()+t.slice(1),n=Te.length;n--;)if((t=Te[n]+e)in ke)return t}function R(t,e){for(var n,i,r,a=[],o=0,s=t.length;o<s;o++)i=t[o],i.style&&(a[o]=vt._data(i,"olddisplay"),n=i.style.display,e?(a[o]||"none"!==n||(i.style.display=""),""===i.style.display&&It(i)&&(a[o]=vt._data(i,"olddisplay",H(i.nodeName)))):(r=It(i),(n&&"none"!==n||!r)&&vt._data(i,"olddisplay",r?n:vt.css(i,"display"))));for(o=0;o<s;o++)i=t[o],i.style&&(e&&"none"!==i.style.display&&""!==i.style.display||(i.style.display=e?a[o]||"":"none"));return t}function P(t,e,n){var i=Ce.exec(e);return i?Math.max(0,i[1]-(n||0))+(i[2]||"px"):e}function M(t,e,n,i,r){for(var a=n===(i?"border":"content")?4:"width"===e?1:0,o=0;a<4;a+=2)"margin"===n&&(o+=vt.css(t,n+Mt[a],!0,r)),i?("content"===n&&(o-=vt.css(t,"padding"+Mt[a],!0,r)),"margin"!==n&&(o-=vt.css(t,"border"+Mt[a]+"Width",!0,r))):(o+=vt.css(t,"padding"+Mt[a],!0,r),"padding"!==n&&(o+=vt.css(t,"border"+Mt[a]+"Width",!0,r)));return o}function I(t,e,n){var i=!0,r="width"===e?t.offsetWidth:t.offsetHeight,a=ve(t),o=mt.boxSizing&&"border-box"===vt.css(t,"boxSizing",!1,a);if(r<=0||null==r){if(r=ge(t,e,a),(r<0||null==r)&&(r=t.style[e]),fe.test(r))return r;i=o&&(mt.boxSizingReliable()||r===t.style[e]),r=parseFloat(r)||0}return r+M(t,e,n||(o?"border":"content"),i,a)+"px"}function B(t,e,n,i,r){return new B.prototype.init(t,e,n,i,r)}function $(){return n.setTimeout(function(){Ee=void 0}),Ee=vt.now()}function W(t,e){var n,i={height:t},r=0;for(e=e?1:0;r<4;r+=2-e)n=Mt[r],i["margin"+n]=i["padding"+n]=t;return e&&(i.opacity=i.width=t),i}function z(t,e,n){for(var i,r=(Y.tweeners[e]||[]).concat(Y.tweeners["*"]),a=0,o=r.length;a<o;a++)if(i=r[a].call(n,e,t))return i}function X(t,e,n){var i,r,a,o,s,l,c,u=this,d={},p=t.style,f=t.nodeType&&It(t),h=vt._data(t,"fxshow");n.queue||(s=vt._queueHooks(t,"fx"),null==s.unqueued&&(s.unqueued=0,l=s.empty.fire,s.empty.fire=function(){s.unqueued||l()}),s.unqueued++,u.always(function(){u.always(function(){s.unqueued--,vt.queue(t,"fx").length||s.empty.fire()})})),1===t.nodeType&&("height"in e||"width"in e)&&(n.overflow=[p.overflow,p.overflowX,p.overflowY],c=vt.css(t,"display"),"inline"===("none"===c?vt._data(t,"olddisplay")||H(t.nodeName):c)&&"none"===vt.css(t,"float")&&(mt.inlineBlockNeedsLayout&&"inline"!==H(t.nodeName)?p.zoom=1:p.display="inline-block")),n.overflow&&(p.overflow="hidden",mt.shrinkWrapBlocks()||u.always(function(){p.overflow=n.overflow[0],p.overflowX=n.overflow[1],p.overflowY=n.overflow[2]}));for(i in e)if(r=e[i],De.exec(r)){if(delete e[i],a=a||"toggle"===r,r===(f?"hide":"show")){if("show"!==r||!h||void 0===h[i])continue;f=!0}d[i]=h&&h[i]||vt.style(t,i)}else c=void 0;if(vt.isEmptyObject(d))"inline"===("none"===c?H(t.nodeName):c)&&(p.display=c);else{h?"hidden"in h&&(f=h.hidden):h=vt._data(t,"fxshow",{}),a&&(h.hidden=!f),f?vt(t).show():u.done(function(){vt(t).hide()}),u.done(function(){var e;vt._removeData(t,"fxshow");for(e in d)vt.style(t,e,d[e])});for(i in d)o=z(f?h[i]:0,i,u),i in h||(h[i]=o.start,f&&(o.end=o.start,o.start="width"===i||"height"===i?1:0))}}function U(t,e){var n,i,r,a,o;for(n in t)if(i=vt.camelCase(n),r=e[i],a=t[n],vt.isArray(a)&&(r=a[1],a=t[n]=a[0]),n!==i&&(t[i]=a,delete t[n]),(o=vt.cssHooks[i])&&"expand"in o){a=o.expand(a),delete t[i];for(n in a)n in t||(t[n]=a[n],e[n]=r)}else e[i]=r}function Y(t,e,n){var i,r,a=0,o=Y.prefilters.length,s=vt.Deferred().always(function(){delete l.elem}),l=function(){if(r)return!1;for(var e=Ee||$(),n=Math.max(0,c.startTime+c.duration-e),i=n/c.duration||0,a=1-i,o=0,l=c.tweens.length;o<l;o++)c.tweens[o].run(a);return s.notifyWith(t,[c,a,n]),a<1&&l?n:(s.resolveWith(t,[c]),!1)},c=s.promise({elem:t,props:vt.extend({},e),opts:vt.extend(!0,{specialEasing:{},easing:vt.easing._default},n),originalProperties:e,originalOptions:n,startTime:Ee||$(),duration:n.duration,tweens:[],createTween:function(e,n){var i=vt.Tween(t,c.opts,e,n,c.opts.specialEasing[e]||c.opts.easing);return c.tweens.push(i),i},stop:function(e){var n=0,i=e?c.tweens.length:0;if(r)return this;for(r=!0;n<i;n++)c.tweens[n].run(1);return e?(s.notifyWith(t,[c,1,0]),s.resolveWith(t,[c,e])):s.rejectWith(t,[c,e]),this}}),u=c.props;for(U(u,c.opts.specialEasing);a<o;a++)if(i=Y.prefilters[a].call(c,t,u,c.opts))return vt.isFunction(i.stop)&&(vt._queueHooks(c.elem,c.opts.queue).stop=vt.proxy(i.stop,i)),i;return vt.map(u,z,c),vt.isFunction(c.opts.start)&&c.opts.start.call(t,c),vt.fx.timer(vt.extend(l,{elem:t,anim:c,queue:c.opts.queue})),c.progress(c.opts.progress).done(c.opts.done,c.opts.complete).fail(c.opts.fail).always(c.opts.always)}function G(t){return vt.attr(t,"class")||""}function V(t){return function(e,n){"string"!=typeof e&&(n=e,e="*");var i,r=0,a=e.toLowerCase().match(jt)||[];if(vt.isFunction(n))for(;i=a[r++];)"+"===i.charAt(0)?(i=i.slice(1)||"*",(t[i]=t[i]||[]).unshift(n)):(t[i]=t[i]||[]).push(n)}}function J(t,e,n,i){function r(s){var l;return a[s]=!0,vt.each(t[s]||[],function(t,s){var c=s(e,n,i);return"string"!=typeof c||o||a[c]?o?!(l=c):void 0:(e.dataTypes.unshift(c),r(c),!1)}),l}var a={},o=t===tn;return r(e.dataTypes[0])||!a["*"]&&r("*")}function K(t,e){var n,i,r=vt.ajaxSettings.flatOptions||{};for(i in e)void 0!==e[i]&&((r[i]?t:n||(n={}))[i]=e[i]);return n&&vt.extend(!0,t,n),t}function Q(t,e,n){for(var i,r,a,o,s=t.contents,l=t.dataTypes;"*"===l[0];)l.shift(),void 0===r&&(r=t.mimeType||e.getResponseHeader("Content-Type"));if(r)for(o in s)if(s[o]&&s[o].test(r)){l.unshift(o);break}if(l[0]in n)a=l[0];else{for(o in n){if(!l[0]||t.converters[o+" "+l[0]]){a=o;break}i||(i=o)}a=a||i}if(a)return a!==l[0]&&l.unshift(a),n[a]}function Z(t,e,n,i){var r,a,o,s,l,c={},u=t.dataTypes.slice();if(u[1])for(o in t.converters)c[o.toLowerCase()]=t.converters[o];for(a=u.shift();a;)if(t.responseFields[a]&&(n[t.responseFields[a]]=e),!l&&i&&t.dataFilter&&(e=t.dataFilter(e,t.dataType)),l=a,a=u.shift())if("*"===a)a=l;else if("*"!==l&&l!==a){if(!(o=c[l+" "+a]||c["* "+a]))for(r in c)if(s=r.split(" "),s[1]===a&&(o=c[l+" "+s[0]]||c["* "+s[0]])){!0===o?o=c[r]:!0!==c[r]&&(a=s[0],u.unshift(s[1]));break}if(!0!==o)if(o&&t.throws)e=o(e);else try{e=o(e)}catch(t){return{state:"parsererror",error:o?t:"No conversion from "+l+" to "+a}}}return{state:"success",data:e}}function tt(t){return t.style&&t.style.display||vt.css(t,"display")}function et(t){if(!vt.contains(t.ownerDocument||st,t))return!0;for(;t&&1===t.nodeType;){if("none"===tt(t)||"hidden"===t.type)return!0;t=t.parentNode}return!1}function nt(t,e,n,i){var r;if(vt.isArray(e))vt.each(e,function(e,r){n||on.test(t)?i(t,r):nt(t+"["+("object"==typeof r&&null!=r?e:"")+"]",r,n,i)});else if(n||"object"!==vt.type(e))i(t,e);else for(r in e)nt(t+"["+r+"]",e[r],n,i)}function it(){try{return new n.XMLHttpRequest}catch(t){}}function rt(){try{return new n.ActiveXObject("Microsoft.XMLHTTP")}catch(t){}}function at(t){return vt.isWindow(t)?t:9===t.nodeType&&(t.defaultView||t.parentWindow)}var ot=[],st=n.document,lt=ot.slice,ct=ot.concat,ut=ot.push,dt=ot.indexOf,pt={},ft=pt.toString,ht=pt.hasOwnProperty,mt={},vt=function(t,e){return new vt.fn.init(t,e)},gt=/^[\s\uFEFF\xA0]+|[\s\uFEFF\xA0]+$/g,yt=/^-ms-/,bt=/-([\da-z])/gi,xt=function(t,e){return e.toUpperCase()};vt.fn=vt.prototype={jquery:"1.12.4",constructor:vt,selector:"",length:0,toArray:function(){return lt.call(this)},get:function(t){return null!=t?t<0?this[t+this.length]:this[t]:lt.call(this)},pushStack:function(t){var e=vt.merge(this.constructor(),t);return e.prevObject=this,e.context=this.context,e},each:function(t){return vt.each(this,t)},map:function(t){return this.pushStack(vt.map(this,function(e,n){return t.call(e,n,e)}))},slice:function(){return this.pushStack(lt.apply(this,arguments))},first:function(){return this.eq(0)},last:function(){return this.eq(-1)},eq:function(t){var e=this.length,n=+t+(t<0?e:0);return this.pushStack(n>=0&&n<e?[this[n]]:[])},end:function(){return this.prevObject||this.constructor()},push:ut,sort:ot.sort,splice:ot.splice},vt.extend=vt.fn.extend=function(){var t,e,n,i,r,a,o=arguments[0]||{},s=1,l=arguments.length,c=!1;for("boolean"==typeof o&&(c=o,o=arguments[s]||{},s++),"object"==typeof o||vt.isFunction(o)||(o={}),s===l&&(o=this,s--);s<l;s++)if(null!=(r=arguments[s]))for(i in r)t=o[i],n=r[i],o!==n&&(c&&n&&(vt.isPlainObject(n)||(e=vt.isArray(n)))?(e?(e=!1,a=t&&vt.isArray(t)?t:[]):a=t&&vt.isPlainObject(t)?t:{},o[i]=vt.extend(c,a,n)):void 0!==n&&(o[i]=n));return o},vt.extend({expando:"jQuery"+("1.12.4"+Math.random()).replace(/\D/g,""),isReady:!0,error:function(t){throw new Error(t)},noop:function(){},isFunction:function(t){return"function"===vt.type(t)},isArray:Array.isArray||function(t){return"array"===vt.type(t)},isWindow:function(t){return null!=t&&t==t.window},isNumeric:function(t){var e=t&&t.toString();return!vt.isArray(t)&&e-parseFloat(e)+1>=0},isEmptyObject:function(t){var e;for(e in t)return!1;return!0},isPlainObject:function(t){var e;if(!t||"object"!==vt.type(t)||t.nodeType||vt.isWindow(t))return!1;try{if(t.constructor&&!ht.call(t,"constructor")&&!ht.call(t.constructor.prototype,"isPrototypeOf"))return!1}catch(t){return!1}if(!mt.ownFirst)for(e in t)return ht.call(t,e);for(e in t);return void 0===e||ht.call(t,e)},type:function(t){return null==t?t+"":"object"==typeof t||"function"==typeof t?pt[ft.call(t)]||"object":typeof t},globalEval:function(t){t&&vt.trim(t)&&(n.execScript||function(t){n.eval.call(n,t)})(t)},camelCase:function(t){return t.replace(yt,"ms-").replace(bt,xt)},nodeName:function(t,e){return t.nodeName&&t.nodeName.toLowerCase()===e.toLowerCase()},each:function(t,e){var n,i=0;if(o(t))for(n=t.length;i<n&&!1!==e.call(t[i],i,t[i]);i++);else for(i in t)if(!1===e.call(t[i],i,t[i]))break;return t},trim:function(t){return null==t?"":(t+"").replace(gt,"")},makeArray:function(t,e){var n=e||[];return null!=t&&(o(Object(t))?vt.merge(n,"string"==typeof t?[t]:t):ut.call(n,t)),n},inArray:function(t,e,n){var i;if(e){if(dt)return dt.call(e,t,n);for(i=e.length,n=n?n<0?Math.max(0,i+n):n:0;n<i;n++)if(n in e&&e[n]===t)return n}return-1},merge:function(t,e){for(var n=+e.length,i=0,r=t.length;i<n;)t[r++]=e[i++];if(n!==n)for(;void 0!==e[i];)t[r++]=e[i++];return t.length=r,t},grep:function(t,e,n){for(var i=[],r=0,a=t.length,o=!n;r<a;r++)!e(t[r],r)!==o&&i.push(t[r]);return i},map:function(t,e,n){var i,r,a=0,s=[];if(o(t))for(i=t.length;a<i;a++)null!=(r=e(t[a],a,n))&&s.push(r);else for(a in t)null!=(r=e(t[a],a,n))&&s.push(r);return ct.apply([],s)},guid:1,proxy:function(t,e){var n,i,r;if("string"==typeof e&&(r=t[e],e=t,t=r),vt.isFunction(t))return n=lt.call(arguments,2),i=function(){return t.apply(e||this,n.concat(lt.call(arguments)))},i.guid=t.guid=t.guid||vt.guid++,i},now:function(){return+new Date},support:mt}),"function"==typeof Symbol&&(vt.fn[Symbol.iterator]=ot[Symbol.iterator]),vt.each("Boolean Number String Function Array Date RegExp Object Error Symbol".split(" "),function(t,e){pt["[object "+e+"]"]=e.toLowerCase()});var _t=/*!
 * Sizzle CSS Selector Engine v2.2.1
 * http://sizzlejs.com/
 *
 * Copyright jQuery Foundation and other contributors
 * Released under the MIT license
 * http://jquery.org/license
 *
 * Date: 2015-10-17
 */
function(t){function e(t,e,n,i){var r,a,o,s,c,d,p,f,h=e&&e.ownerDocument,m=e?e.nodeType:9;if(n=n||[],"string"!=typeof t||!t||1!==m&&9!==m&&11!==m)return n;if(!i&&((e?e.ownerDocument||e:P)!==A&&D(e),e=e||A,L)){if(11!==m&&(d=mt.exec(t)))if(r=d[1]){if(9===m){if(!(o=e.getElementById(r)))return n;if(o.id===r)return n.push(o),n}else if(h&&(o=h.getElementById(r))&&F(e,o)&&o.id===r)return n.push(o),n}else{if(d[2])return J.apply(n,e.getElementsByTagName(t)),n;if((r=d[3])&&b.getElementsByClassName&&e.getElementsByClassName)return J.apply(n,e.getElementsByClassName(r)),n}if(b.qsa&&!W[t+" "]&&(!O||!O.test(t))){if(1!==m)h=e,f=t;else if("object"!==e.nodeName.toLowerCase()){for((s=e.getAttribute("id"))?s=s.replace(gt,"\\$&"):e.setAttribute("id",s=R),p=w(t),a=p.length,c=ut.test(s)?"#"+s:"[id='"+s+"']";a--;)p[a]=c+" "+u(p[a]);f=p.join(","),h=vt.test(t)&&l(e.parentNode)||e}if(f)try{return J.apply(n,h.querySelectorAll(f)),n}catch(t){}finally{s===R&&e.removeAttribute("id")}}}return T(t.replace(at,"$1"),e,n,i)}function n(){function t(n,i){return e.push(n+" ")>x.cacheLength&&delete t[e.shift()],t[n+" "]=i}var e=[];return t}function i(t){return t[R]=!0,t}function r(t){var e=A.createElement("div");try{return!!t(e)}catch(t){return!1}finally{e.parentNode&&e.parentNode.removeChild(e),e=null}}function a(t,e){for(var n=t.split("|"),i=n.length;i--;)x.attrHandle[n[i]]=e}function o(t,e){var n=e&&t,i=n&&1===t.nodeType&&1===e.nodeType&&(~e.sourceIndex||X)-(~t.sourceIndex||X);if(i)return i;if(n)for(;n=n.nextSibling;)if(n===e)return-1;return t?1:-1}function s(t){return i(function(e){return e=+e,i(function(n,i){for(var r,a=t([],n.length,e),o=a.length;o--;)n[r=a[o]]&&(n[r]=!(i[r]=n[r]))})})}function l(t){return t&&void 0!==t.getElementsByTagName&&t}function c(){}function u(t){for(var e=0,n=t.length,i="";e<n;e++)i+=t[e].value;return i}function d(t,e,n){var i=e.dir,r=n&&"parentNode"===i,a=I++;return e.first?function(e,n,a){for(;e=e[i];)if(1===e.nodeType||r)return t(e,n,a)}:function(e,n,o){var s,l,c,u=[M,a];if(o){for(;e=e[i];)if((1===e.nodeType||r)&&t(e,n,o))return!0}else for(;e=e[i];)if(1===e.nodeType||r){if(c=e[R]||(e[R]={}),l=c[e.uniqueID]||(c[e.uniqueID]={}),(s=l[i])&&s[0]===M&&s[1]===a)return u[2]=s[2];if(l[i]=u,u[2]=t(e,n,o))return!0}}}function p(t){return t.length>1?function(e,n,i){for(var r=t.length;r--;)if(!t[r](e,n,i))return!1;return!0}:t[0]}function f(t,n,i){for(var r=0,a=n.length;r<a;r++)e(t,n[r],i);return i}function h(t,e,n,i,r){for(var a,o=[],s=0,l=t.length,c=null!=e;s<l;s++)(a=t[s])&&(n&&!n(a,i,r)||(o.push(a),c&&e.push(s)));return o}function m(t,e,n,r,a,o){return r&&!r[R]&&(r=m(r)),a&&!a[R]&&(a=m(a,o)),i(function(i,o,s,l){var c,u,d,p=[],m=[],v=o.length,g=i||f(e||"*",s.nodeType?[s]:s,[]),y=!t||!i&&e?g:h(g,p,t,s,l),b=n?a||(i?t:v||r)?[]:o:y;if(n&&n(y,b,s,l),r)for(c=h(b,m),r(c,[],s,l),u=c.length;u--;)(d=c[u])&&(b[m[u]]=!(y[m[u]]=d));if(i){if(a||t){if(a){for(c=[],u=b.length;u--;)(d=b[u])&&c.push(y[u]=d);a(null,b=[],c,l)}for(u=b.length;u--;)(d=b[u])&&(c=a?Q(i,d):p[u])>-1&&(i[c]=!(o[c]=d))}}else b=h(b===o?b.splice(v,b.length):b),a?a(null,o,b,l):J.apply(o,b)})}function v(t){for(var e,n,i,r=t.length,a=x.relative[t[0].type],o=a||x.relative[" "],s=a?1:0,l=d(function(t){return t===e},o,!0),c=d(function(t){return Q(e,t)>-1},o,!0),f=[function(t,n,i){var r=!a&&(i||n!==k)||((e=n).nodeType?l(t,n,i):c(t,n,i));return e=null,r}];s<r;s++)if(n=x.relative[t[s].type])f=[d(p(f),n)];else{if(n=x.filter[t[s].type].apply(null,t[s].matches),n[R]){for(i=++s;i<r&&!x.relative[t[i].type];i++);return m(s>1&&p(f),s>1&&u(t.slice(0,s-1).concat({value:" "===t[s-2].type?"*":""})).replace(at,"$1"),n,s<i&&v(t.slice(s,i)),i<r&&v(t=t.slice(i)),i<r&&u(t))}f.push(n)}return p(f)}function g(t,n){var r=n.length>0,a=t.length>0,o=function(i,o,s,l,c){var u,d,p,f=0,m="0",v=i&&[],g=[],y=k,b=i||a&&x.find.TAG("*",c),_=M+=null==y?1:Math.random()||.1,C=b.length;for(c&&(k=o===A||o||c);m!==C&&null!=(u=b[m]);m++){if(a&&u){for(d=0,o||u.ownerDocument===A||(D(u),s=!L);p=t[d++];)if(p(u,o||A,s)){l.push(u);break}c&&(M=_)}r&&((u=!p&&u)&&f--,i&&v.push(u))}if(f+=m,r&&m!==f){for(d=0;p=n[d++];)p(v,g,o,s);if(i){if(f>0)for(;m--;)v[m]||g[m]||(g[m]=G.call(l));g=h(g)}J.apply(l,g),c&&!i&&g.length>0&&f+n.length>1&&e.uniqueSort(l)}return c&&(M=_,k=y),v};return r?i(o):o}var y,b,x,_,C,w,S,T,k,E,N,D,A,j,L,O,H,q,F,R="sizzle"+1*new Date,P=t.document,M=0,I=0,B=n(),$=n(),W=n(),z=function(t,e){return t===e&&(N=!0),0},X=1<<31,U={}.hasOwnProperty,Y=[],G=Y.pop,V=Y.push,J=Y.push,K=Y.slice,Q=function(t,e){for(var n=0,i=t.length;n<i;n++)if(t[n]===e)return n;return-1},Z="checked|selected|async|autofocus|autoplay|controls|defer|disabled|hidden|ismap|loop|multiple|open|readonly|required|scoped",tt="[\\x20\\t\\r\\n\\f]",et="(?:\\\\.|[\\w-]|[^\\x00-\\xa0])+",nt="\\["+tt+"*("+et+")(?:"+tt+"*([*^$|!~]?=)"+tt+"*(?:'((?:\\\\.|[^\\\\'])*)'|\"((?:\\\\.|[^\\\\\"])*)\"|("+et+"))|)"+tt+"*\\]",it=":("+et+")(?:\\((('((?:\\\\.|[^\\\\'])*)'|\"((?:\\\\.|[^\\\\\"])*)\")|((?:\\\\.|[^\\\\()[\\]]|"+nt+")*)|.*)\\)|)",rt=new RegExp(tt+"+","g"),at=new RegExp("^"+tt+"+|((?:^|[^\\\\])(?:\\\\.)*)"+tt+"+$","g"),ot=new RegExp("^"+tt+"*,"+tt+"*"),st=new RegExp("^"+tt+"*([>+~]|"+tt+")"+tt+"*"),lt=new RegExp("="+tt+"*([^\\]'\"]*?)"+tt+"*\\]","g"),ct=new RegExp(it),ut=new RegExp("^"+et+"$"),dt={ID:new RegExp("^#("+et+")"),CLASS:new RegExp("^\\.("+et+")"),TAG:new RegExp("^("+et+"|[*])"),ATTR:new RegExp("^"+nt),PSEUDO:new RegExp("^"+it),CHILD:new RegExp("^:(only|first|last|nth|nth-last)-(child|of-type)(?:\\("+tt+"*(even|odd|(([+-]|)(\\d*)n|)"+tt+"*(?:([+-]|)"+tt+"*(\\d+)|))"+tt+"*\\)|)","i"),bool:new RegExp("^(?:"+Z+")$","i"),needsContext:new RegExp("^"+tt+"*[>+~]|:(even|odd|eq|gt|lt|nth|first|last)(?:\\("+tt+"*((?:-\\d)?\\d*)"+tt+"*\\)|)(?=[^-]|$)","i")},pt=/^(?:input|select|textarea|button)$/i,ft=/^h\d$/i,ht=/^[^{]+\{\s*\[native \w/,mt=/^(?:#([\w-]+)|(\w+)|\.([\w-]+))$/,vt=/[+~]/,gt=/'|\\/g,yt=new RegExp("\\\\([\\da-f]{1,6}"+tt+"?|("+tt+")|.)","ig"),bt=function(t,e,n){var i="0x"+e-65536;return i!==i||n?e:i<0?String.fromCharCode(i+65536):String.fromCharCode(i>>10|55296,1023&i|56320)},xt=function(){D()};try{J.apply(Y=K.call(P.childNodes),P.childNodes),Y[P.childNodes.length].nodeType}catch(t){J={apply:Y.length?function(t,e){V.apply(t,K.call(e))}:function(t,e){for(var n=t.length,i=0;t[n++]=e[i++];);t.length=n-1}}}b=e.support={},C=e.isXML=function(t){var e=t&&(t.ownerDocument||t).documentElement;return!!e&&"HTML"!==e.nodeName},D=e.setDocument=function(t){var e,n,i=t?t.ownerDocument||t:P;return i!==A&&9===i.nodeType&&i.documentElement?(A=i,j=A.documentElement,L=!C(A),(n=A.defaultView)&&n.top!==n&&(n.addEventListener?n.addEventListener("unload",xt,!1):n.attachEvent&&n.attachEvent("onunload",xt)),b.attributes=r(function(t){return t.className="i",!t.getAttribute("className")}),b.getElementsByTagName=r(function(t){return t.appendChild(A.createComment("")),!t.getElementsByTagName("*").length}),b.getElementsByClassName=ht.test(A.getElementsByClassName),b.getById=r(function(t){return j.appendChild(t).id=R,!A.getElementsByName||!A.getElementsByName(R).length}),b.getById?(x.find.ID=function(t,e){if(void 0!==e.getElementById&&L){var n=e.getElementById(t);return n?[n]:[]}},x.filter.ID=function(t){var e=t.replace(yt,bt);return function(t){return t.getAttribute("id")===e}}):(delete x.find.ID,x.filter.ID=function(t){var e=t.replace(yt,bt);return function(t){var n=void 0!==t.getAttributeNode&&t.getAttributeNode("id");return n&&n.value===e}}),x.find.TAG=b.getElementsByTagName?function(t,e){return void 0!==e.getElementsByTagName?e.getElementsByTagName(t):b.qsa?e.querySelectorAll(t):void 0}:function(t,e){var n,i=[],r=0,a=e.getElementsByTagName(t);if("*"===t){for(;n=a[r++];)1===n.nodeType&&i.push(n);return i}return a},x.find.CLASS=b.getElementsByClassName&&function(t,e){if(void 0!==e.getElementsByClassName&&L)return e.getElementsByClassName(t)},H=[],O=[],(b.qsa=ht.test(A.querySelectorAll))&&(r(function(t){j.appendChild(t).innerHTML="<a id='"+R+"'></a><select id='"+R+"-\r\\' msallowcapture=''><option selected=''></option></select>",t.querySelectorAll("[msallowcapture^='']").length&&O.push("[*^$]="+tt+"*(?:''|\"\")"),t.querySelectorAll("[selected]").length||O.push("\\["+tt+"*(?:value|"+Z+")"),t.querySelectorAll("[id~="+R+"-]").length||O.push("~="),t.querySelectorAll(":checked").length||O.push(":checked"),t.querySelectorAll("a#"+R+"+*").length||O.push(".#.+[+~]")}),r(function(t){var e=A.createElement("input");e.setAttribute("type","hidden"),t.appendChild(e).setAttribute("name","D"),t.querySelectorAll("[name=d]").length&&O.push("name"+tt+"*[*^$|!~]?="),t.querySelectorAll(":enabled").length||O.push(":enabled",":disabled"),t.querySelectorAll("*,:x"),O.push(",.*:")})),(b.matchesSelector=ht.test(q=j.matches||j.webkitMatchesSelector||j.mozMatchesSelector||j.oMatchesSelector||j.msMatchesSelector))&&r(function(t){b.disconnectedMatch=q.call(t,"div"),q.call(t,"[s!='']:x"),H.push("!=",it)}),O=O.length&&new RegExp(O.join("|")),H=H.length&&new RegExp(H.join("|")),e=ht.test(j.compareDocumentPosition),F=e||ht.test(j.contains)?function(t,e){var n=9===t.nodeType?t.documentElement:t,i=e&&e.parentNode;return t===i||!(!i||1!==i.nodeType||!(n.contains?n.contains(i):t.compareDocumentPosition&&16&t.compareDocumentPosition(i)))}:function(t,e){if(e)for(;e=e.parentNode;)if(e===t)return!0;return!1},z=e?function(t,e){if(t===e)return N=!0,0;var n=!t.compareDocumentPosition-!e.compareDocumentPosition;return n||(n=(t.ownerDocument||t)===(e.ownerDocument||e)?t.compareDocumentPosition(e):1,1&n||!b.sortDetached&&e.compareDocumentPosition(t)===n?t===A||t.ownerDocument===P&&F(P,t)?-1:e===A||e.ownerDocument===P&&F(P,e)?1:E?Q(E,t)-Q(E,e):0:4&n?-1:1)}:function(t,e){if(t===e)return N=!0,0;var n,i=0,r=t.parentNode,a=e.parentNode,s=[t],l=[e];if(!r||!a)return t===A?-1:e===A?1:r?-1:a?1:E?Q(E,t)-Q(E,e):0;if(r===a)return o(t,e);for(n=t;n=n.parentNode;)s.unshift(n);for(n=e;n=n.parentNode;)l.unshift(n);for(;s[i]===l[i];)i++;return i?o(s[i],l[i]):s[i]===P?-1:l[i]===P?1:0},A):A},e.matches=function(t,n){return e(t,null,null,n)},e.matchesSelector=function(t,n){if((t.ownerDocument||t)!==A&&D(t),n=n.replace(lt,"='$1']"),b.matchesSelector&&L&&!W[n+" "]&&(!H||!H.test(n))&&(!O||!O.test(n)))try{var i=q.call(t,n);if(i||b.disconnectedMatch||t.document&&11!==t.document.nodeType)return i}catch(t){}return e(n,A,null,[t]).length>0},e.contains=function(t,e){return(t.ownerDocument||t)!==A&&D(t),F(t,e)},e.attr=function(t,e){(t.ownerDocument||t)!==A&&D(t);var n=x.attrHandle[e.toLowerCase()],i=n&&U.call(x.attrHandle,e.toLowerCase())?n(t,e,!L):void 0;return void 0!==i?i:b.attributes||!L?t.getAttribute(e):(i=t.getAttributeNode(e))&&i.specified?i.value:null},e.error=function(t){throw new Error("Syntax error, unrecognized expression: "+t)},e.uniqueSort=function(t){var e,n=[],i=0,r=0;if(N=!b.detectDuplicates,E=!b.sortStable&&t.slice(0),t.sort(z),N){for(;e=t[r++];)e===t[r]&&(i=n.push(r));for(;i--;)t.splice(n[i],1)}return E=null,t},_=e.getText=function(t){var e,n="",i=0,r=t.nodeType;if(r){if(1===r||9===r||11===r){if("string"==typeof t.textContent)return t.textContent;for(t=t.firstChild;t;t=t.nextSibling)n+=_(t)}else if(3===r||4===r)return t.nodeValue}else for(;e=t[i++];)n+=_(e);return n},x=e.selectors={cacheLength:50,createPseudo:i,match:dt,attrHandle:{},find:{},relative:{">":{dir:"parentNode",first:!0}," ":{dir:"parentNode"},"+":{dir:"previousSibling",first:!0},"~":{dir:"previousSibling"}},preFilter:{ATTR:function(t){return t[1]=t[1].replace(yt,bt),t[3]=(t[3]||t[4]||t[5]||"").replace(yt,bt),"~="===t[2]&&(t[3]=" "+t[3]+" "),t.slice(0,4)},CHILD:function(t){return t[1]=t[1].toLowerCase(),"nth"===t[1].slice(0,3)?(t[3]||e.error(t[0]),t[4]=+(t[4]?t[5]+(t[6]||1):2*("even"===t[3]||"odd"===t[3])),t[5]=+(t[7]+t[8]||"odd"===t[3])):t[3]&&e.error(t[0]),t},PSEUDO:function(t){var e,n=!t[6]&&t[2];return dt.CHILD.test(t[0])?null:(t[3]?t[2]=t[4]||t[5]||"":n&&ct.test(n)&&(e=w(n,!0))&&(e=n.indexOf(")",n.length-e)-n.length)&&(t[0]=t[0].slice(0,e),t[2]=n.slice(0,e)),t.slice(0,3))}},filter:{TAG:function(t){var e=t.replace(yt,bt).toLowerCase();return"*"===t?function(){return!0}:function(t){return t.nodeName&&t.nodeName.toLowerCase()===e}},CLASS:function(t){var e=B[t+" "];return e||(e=new RegExp("(^|"+tt+")"+t+"("+tt+"|$)"))&&B(t,function(t){return e.test("string"==typeof t.className&&t.className||void 0!==t.getAttribute&&t.getAttribute("class")||"")})},ATTR:function(t,n,i){return function(r){var a=e.attr(r,t);return null==a?"!="===n:!n||(a+="","="===n?a===i:"!="===n?a!==i:"^="===n?i&&0===a.indexOf(i):"*="===n?i&&a.indexOf(i)>-1:"$="===n?i&&a.slice(-i.length)===i:"~="===n?(" "+a.replace(rt," ")+" ").indexOf(i)>-1:"|="===n&&(a===i||a.slice(0,i.length+1)===i+"-"))}},CHILD:function(t,e,n,i,r){var a="nth"!==t.slice(0,3),o="last"!==t.slice(-4),s="of-type"===e;return 1===i&&0===r?function(t){return!!t.parentNode}:function(e,n,l){var c,u,d,p,f,h,m=a!==o?"nextSibling":"previousSibling",v=e.parentNode,g=s&&e.nodeName.toLowerCase(),y=!l&&!s,b=!1;if(v){if(a){for(;m;){for(p=e;p=p[m];)if(s?p.nodeName.toLowerCase()===g:1===p.nodeType)return!1;h=m="only"===t&&!h&&"nextSibling"}return!0}if(h=[o?v.firstChild:v.lastChild],o&&y){for(p=v,d=p[R]||(p[R]={}),u=d[p.uniqueID]||(d[p.uniqueID]={}),c=u[t]||[],f=c[0]===M&&c[1],b=f&&c[2],p=f&&v.childNodes[f];p=++f&&p&&p[m]||(b=f=0)||h.pop();)if(1===p.nodeType&&++b&&p===e){u[t]=[M,f,b];break}}else if(y&&(p=e,d=p[R]||(p[R]={}),u=d[p.uniqueID]||(d[p.uniqueID]={}),c=u[t]||[],f=c[0]===M&&c[1],b=f),!1===b)for(;(p=++f&&p&&p[m]||(b=f=0)||h.pop())&&((s?p.nodeName.toLowerCase()!==g:1!==p.nodeType)||!++b||(y&&(d=p[R]||(p[R]={}),u=d[p.uniqueID]||(d[p.uniqueID]={}),u[t]=[M,b]),p!==e)););return(b-=r)===i||b%i==0&&b/i>=0}}},PSEUDO:function(t,n){var r,a=x.pseudos[t]||x.setFilters[t.toLowerCase()]||e.error("unsupported pseudo: "+t);return a[R]?a(n):a.length>1?(r=[t,t,"",n],x.setFilters.hasOwnProperty(t.toLowerCase())?i(function(t,e){for(var i,r=a(t,n),o=r.length;o--;)i=Q(t,r[o]),t[i]=!(e[i]=r[o])}):function(t){return a(t,0,r)}):a}},pseudos:{not:i(function(t){var e=[],n=[],r=S(t.replace(at,"$1"));return r[R]?i(function(t,e,n,i){for(var a,o=r(t,null,i,[]),s=t.length;s--;)(a=o[s])&&(t[s]=!(e[s]=a))}):function(t,i,a){return e[0]=t,r(e,null,a,n),e[0]=null,!n.pop()}}),has:i(function(t){return function(n){return e(t,n).length>0}}),contains:i(function(t){return t=t.replace(yt,bt),function(e){return(e.textContent||e.innerText||_(e)).indexOf(t)>-1}}),lang:i(function(t){return ut.test(t||"")||e.error("unsupported lang: "+t),t=t.replace(yt,bt).toLowerCase(),function(e){var n;do{if(n=L?e.lang:e.getAttribute("xml:lang")||e.getAttribute("lang"))return(n=n.toLowerCase())===t||0===n.indexOf(t+"-")}while((e=e.parentNode)&&1===e.nodeType);return!1}}),target:function(e){var n=t.location&&t.location.hash;return n&&n.slice(1)===e.id},root:function(t){return t===j},focus:function(t){return t===A.activeElement&&(!A.hasFocus||A.hasFocus())&&!!(t.type||t.href||~t.tabIndex)},enabled:function(t){return!1===t.disabled},disabled:function(t){return!0===t.disabled},checked:function(t){var e=t.nodeName.toLowerCase();return"input"===e&&!!t.checked||"option"===e&&!!t.selected},selected:function(t){return t.parentNode&&t.parentNode.selectedIndex,!0===t.selected},empty:function(t){for(t=t.firstChild;t;t=t.nextSibling)if(t.nodeType<6)return!1;return!0},parent:function(t){return!x.pseudos.empty(t)},header:function(t){return ft.test(t.nodeName)},input:function(t){return pt.test(t.nodeName)},button:function(t){var e=t.nodeName.toLowerCase();return"input"===e&&"button"===t.type||"button"===e},text:function(t){var e;return"input"===t.nodeName.toLowerCase()&&"text"===t.type&&(null==(e=t.getAttribute("type"))||"text"===e.toLowerCase())},first:s(function(){return[0]}),last:s(function(t,e){return[e-1]}),eq:s(function(t,e,n){return[n<0?n+e:n]}),even:s(function(t,e){for(var n=0;n<e;n+=2)t.push(n);return t}),odd:s(function(t,e){for(var n=1;n<e;n+=2)t.push(n);return t}),lt:s(function(t,e,n){for(var i=n<0?n+e:n;--i>=0;)t.push(i);return t}),gt:s(function(t,e,n){for(var i=n<0?n+e:n;++i<e;)t.push(i);return t})}},x.pseudos.nth=x.pseudos.eq;for(y in{radio:!0,checkbox:!0,file:!0,password:!0,image:!0})x.pseudos[y]=function(t){return function(e){return"input"===e.nodeName.toLowerCase()&&e.type===t}}(y);for(y in{submit:!0,reset:!0})x.pseudos[y]=function(t){return function(e){var n=e.nodeName.toLowerCase();return("input"===n||"button"===n)&&e.type===t}}(y);return c.prototype=x.filters=x.pseudos,x.setFilters=new c,w=e.tokenize=function(t,n){var i,r,a,o,s,l,c,u=$[t+" "];if(u)return n?0:u.slice(0);for(s=t,l=[],c=x.preFilter;s;){i&&!(r=ot.exec(s))||(r&&(s=s.slice(r[0].length)||s),l.push(a=[])),i=!1,(r=st.exec(s))&&(i=r.shift(),a.push({value:i,type:r[0].replace(at," ")}),s=s.slice(i.length));for(o in x.filter)!(r=dt[o].exec(s))||c[o]&&!(r=c[o](r))||(i=r.shift(),a.push({value:i,type:o,matches:r}),s=s.slice(i.length));if(!i)break}return n?s.length:s?e.error(t):$(t,l).slice(0)},S=e.compile=function(t,e){var n,i=[],r=[],a=W[t+" "];if(!a){for(e||(e=w(t)),n=e.length;n--;)a=v(e[n]),a[R]?i.push(a):r.push(a);a=W(t,g(r,i)),a.selector=t}return a},T=e.select=function(t,e,n,i){var r,a,o,s,c,d="function"==typeof t&&t,p=!i&&w(t=d.selector||t);if(n=n||[],1===p.length){if(a=p[0]=p[0].slice(0),a.length>2&&"ID"===(o=a[0]).type&&b.getById&&9===e.nodeType&&L&&x.relative[a[1].type]){if(!(e=(x.find.ID(o.matches[0].replace(yt,bt),e)||[])[0]))return n;d&&(e=e.parentNode),t=t.slice(a.shift().value.length)}for(r=dt.needsContext.test(t)?0:a.length;r--&&(o=a[r],!x.relative[s=o.type]);)if((c=x.find[s])&&(i=c(o.matches[0].replace(yt,bt),vt.test(a[0].type)&&l(e.parentNode)||e))){if(a.splice(r,1),!(t=i.length&&u(a)))return J.apply(n,i),n;break}}return(d||S(t,p))(i,e,!L,n,!e||vt.test(t)&&l(e.parentNode)||e),n},b.sortStable=R.split("").sort(z).join("")===R,b.detectDuplicates=!!N,D(),b.sortDetached=r(function(t){return 1&t.compareDocumentPosition(A.createElement("div"))}),r(function(t){return t.innerHTML="<a href='#'></a>","#"===t.firstChild.getAttribute("href")})||a("type|href|height|width",function(t,e,n){if(!n)return t.getAttribute(e,"type"===e.toLowerCase()?1:2)}),b.attributes&&r(function(t){return t.innerHTML="<input/>",t.firstChild.setAttribute("value",""),""===t.firstChild.getAttribute("value")})||a("value",function(t,e,n){if(!n&&"input"===t.nodeName.toLowerCase())return t.defaultValue}),r(function(t){return null==t.getAttribute("disabled")})||a(Z,function(t,e,n){var i;if(!n)return!0===t[e]?e.toLowerCase():(i=t.getAttributeNode(e))&&i.specified?i.value:null}),e}(n);vt.find=_t,vt.expr=_t.selectors,vt.expr[":"]=vt.expr.pseudos,vt.uniqueSort=vt.unique=_t.uniqueSort,vt.text=_t.getText,vt.isXMLDoc=_t.isXML,vt.contains=_t.contains;var Ct=function(t,e,n){for(var i=[],r=void 0!==n;(t=t[e])&&9!==t.nodeType;)if(1===t.nodeType){if(r&&vt(t).is(n))break;i.push(t)}return i},wt=function(t,e){for(var n=[];t;t=t.nextSibling)1===t.nodeType&&t!==e&&n.push(t);return n},St=vt.expr.match.needsContext,Tt=/^<([\w-]+)\s*\/?>(?:<\/\1>|)$/,kt=/^.[^:#\[\.,]*$/;vt.filter=function(t,e,n){var i=e[0];return n&&(t=":not("+t+")"),1===e.length&&1===i.nodeType?vt.find.matchesSelector(i,t)?[i]:[]:vt.find.matches(t,vt.grep(e,function(t){return 1===t.nodeType}))},vt.fn.extend({find:function(t){var e,n=[],i=this,r=i.length;if("string"!=typeof t)return this.pushStack(vt(t).filter(function(){for(e=0;e<r;e++)if(vt.contains(i[e],this))return!0}));for(e=0;e<r;e++)vt.find(t,i[e],n);return n=this.pushStack(r>1?vt.unique(n):n),n.selector=this.selector?this.selector+" "+t:t,n},filter:function(t){return this.pushStack(s(this,t||[],!1))},not:function(t){return this.pushStack(s(this,t||[],!0))},is:function(t){return!!s(this,"string"==typeof t&&St.test(t)?vt(t):t||[],!1).length}});var Et,Nt=/^(?:\s*(<[\w\W]+>)[^>]*|#([\w-]*))$/;(vt.fn.init=function(t,e,n){var i,r;if(!t)return this;if(n=n||Et,"string"==typeof t){if(!(i="<"===t.charAt(0)&&">"===t.charAt(t.length-1)&&t.length>=3?[null,t,null]:Nt.exec(t))||!i[1]&&e)return!e||e.jquery?(e||n).find(t):this.constructor(e).find(t);if(i[1]){if(e=e instanceof vt?e[0]:e,vt.merge(this,vt.parseHTML(i[1],e&&e.nodeType?e.ownerDocument||e:st,!0)),Tt.test(i[1])&&vt.isPlainObject(e))for(i in e)vt.isFunction(this[i])?this[i](e[i]):this.attr(i,e[i]);return this}if((r=st.getElementById(i[2]))&&r.parentNode){if(r.id!==i[2])return Et.find(t);this.length=1,this[0]=r}return this.context=st,this.selector=t,this}return t.nodeType?(this.context=this[0]=t,this.length=1,this):vt.isFunction(t)?void 0!==n.ready?n.ready(t):t(vt):(void 0!==t.selector&&(this.selector=t.selector,this.context=t.context),vt.makeArray(t,this))}).prototype=vt.fn,Et=vt(st);var Dt=/^(?:parents|prev(?:Until|All))/,At={children:!0,contents:!0,next:!0,prev:!0};vt.fn.extend({has:function(t){var e,n=vt(t,this),i=n.length;return this.filter(function(){for(e=0;e<i;e++)if(vt.contains(this,n[e]))return!0})},closest:function(t,e){for(var n,i=0,r=this.length,a=[],o=St.test(t)||"string"!=typeof t?vt(t,e||this.context):0;i<r;i++)for(n=this[i];n&&n!==e;n=n.parentNode)if(n.nodeType<11&&(o?o.index(n)>-1:1===n.nodeType&&vt.find.matchesSelector(n,t))){a.push(n);break}return this.pushStack(a.length>1?vt.uniqueSort(a):a)},index:function(t){return t?"string"==typeof t?vt.inArray(this[0],vt(t)):vt.inArray(t.jquery?t[0]:t,this):this[0]&&this[0].parentNode?this.first().prevAll().length:-1},add:function(t,e){return this.pushStack(vt.uniqueSort(vt.merge(this.get(),vt(t,e))))},addBack:function(t){return this.add(null==t?this.prevObject:this.prevObject.filter(t))}}),vt.each({parent:function(t){var e=t.parentNode;return e&&11!==e.nodeType?e:null},parents:function(t){return Ct(t,"parentNode")},parentsUntil:function(t,e,n){return Ct(t,"parentNode",n)},next:function(t){return l(t,"nextSibling")},prev:function(t){return l(t,"previousSibling")},nextAll:function(t){return Ct(t,"nextSibling")},prevAll:function(t){return Ct(t,"previousSibling")},nextUntil:function(t,e,n){return Ct(t,"nextSibling",n)},prevUntil:function(t,e,n){return Ct(t,"previousSibling",n)},siblings:function(t){return wt((t.parentNode||{}).firstChild,t)},children:function(t){return wt(t.firstChild)},contents:function(t){return vt.nodeName(t,"iframe")?t.contentDocument||t.contentWindow.document:vt.merge([],t.childNodes)}},function(t,e){vt.fn[t]=function(n,i){var r=vt.map(this,e,n);return"Until"!==t.slice(-5)&&(i=n),i&&"string"==typeof i&&(r=vt.filter(i,r)),this.length>1&&(At[t]||(r=vt.uniqueSort(r)),Dt.test(t)&&(r=r.reverse())),this.pushStack(r)}});var jt=/\S+/g;vt.Callbacks=function(t){t="string"==typeof t?c(t):vt.extend({},t);var e,n,i,r,a=[],o=[],s=-1,l=function(){for(r=t.once,i=e=!0;o.length;s=-1)for(n=o.shift();++s<a.length;)!1===a[s].apply(n[0],n[1])&&t.stopOnFalse&&(s=a.length,n=!1);t.memory||(n=!1),e=!1,r&&(a=n?[]:"")},u={add:function(){return a&&(n&&!e&&(s=a.length-1,o.push(n)),function e(n){vt.each(n,function(n,i){vt.isFunction(i)?t.unique&&u.has(i)||a.push(i):i&&i.length&&"string"!==vt.type(i)&&e(i)})}(arguments),n&&!e&&l()),this},remove:function(){return vt.each(arguments,function(t,e){for(var n;(n=vt.inArray(e,a,n))>-1;)a.splice(n,1),n<=s&&s--}),this},has:function(t){return t?vt.inArray(t,a)>-1:a.length>0},empty:function(){return a&&(a=[]),this},disable:function(){return r=o=[],a=n="",this},disabled:function(){return!a},lock:function(){return r=!0,n||u.disable(),this},locked:function(){return!!r},fireWith:function(t,n){return r||(n=n||[],n=[t,n.slice?n.slice():n],o.push(n),e||l()),this},fire:function(){return u.fireWith(this,arguments),this},fired:function(){return!!i}};return u},vt.extend({Deferred:function(t){var e=[["resolve","done",vt.Callbacks("once memory"),"resolved"],["reject","fail",vt.Callbacks("once memory"),"rejected"],["notify","progress",vt.Callbacks("memory")]],n="pending",i={state:function(){return n},always:function(){return r.done(arguments).fail(arguments),this},then:function(){var t=arguments;return vt.Deferred(function(n){vt.each(e,function(e,a){var o=vt.isFunction(t[e])&&t[e];r[a[1]](function(){var t=o&&o.apply(this,arguments);t&&vt.isFunction(t.promise)?t.promise().progress(n.notify).done(n.resolve).fail(n.reject):n[a[0]+"With"](this===i?n.promise():this,o?[t]:arguments)})}),t=null}).promise()},promise:function(t){return null!=t?vt.extend(t,i):i}},r={};return i.pipe=i.then,vt.each(e,function(t,a){var o=a[2],s=a[3];i[a[1]]=o.add,s&&o.add(function(){n=s},e[1^t][2].disable,e[2][2].lock),r[a[0]]=function(){return r[a[0]+"With"](this===r?i:this,arguments),this},r[a[0]+"With"]=o.fireWith}),i.promise(r),t&&t.call(r,r),r},when:function(t){var e,n,i,r=0,a=lt.call(arguments),o=a.length,s=1!==o||t&&vt.isFunction(t.promise)?o:0,l=1===s?t:vt.Deferred(),c=function(t,n,i){return function(r){n[t]=this,i[t]=arguments.length>1?lt.call(arguments):r,i===e?l.notifyWith(n,i):--s||l.resolveWith(n,i)}};if(o>1)for(e=new Array(o),n=new Array(o),i=new Array(o);r<o;r++)a[r]&&vt.isFunction(a[r].promise)?a[r].promise().progress(c(r,n,e)).done(c(r,i,a)).fail(l.reject):--s;return s||l.resolveWith(i,a),l.promise()}});var Lt;vt.fn.ready=function(t){return vt.ready.promise().done(t),this},vt.extend({isReady:!1,readyWait:1,holdReady:function(t){t?vt.readyWait++:vt.ready(!0)},ready:function(t){(!0===t?--vt.readyWait:vt.isReady)||(vt.isReady=!0,!0!==t&&--vt.readyWait>0||(Lt.resolveWith(st,[vt]),vt.fn.triggerHandler&&(vt(st).triggerHandler("ready"),vt(st).off("ready"))))}}),vt.ready.promise=function(t){if(!Lt)if(Lt=vt.Deferred(),"complete"===st.readyState||"loading"!==st.readyState&&!st.documentElement.doScroll)n.setTimeout(vt.ready);else if(st.addEventListener)st.addEventListener("DOMContentLoaded",d),n.addEventListener("load",d);else{st.attachEvent("onreadystatechange",d),n.attachEvent("onload",d);var e=!1;try{e=null==n.frameElement&&st.documentElement}catch(t){}e&&e.doScroll&&function t(){if(!vt.isReady){try{e.doScroll("left")}catch(e){return n.setTimeout(t,50)}u(),vt.ready()}}()}return Lt.promise(t)},vt.ready.promise();var Ot;for(Ot in vt(mt))break;mt.ownFirst="0"===Ot,mt.inlineBlockNeedsLayout=!1,vt(function(){var t,e,n,i;(n=st.getElementsByTagName("body")[0])&&n.style&&(e=st.createElement("div"),i=st.createElement("div"),i.style.cssText="position:absolute;border:0;width:0;height:0;top:0;left:-9999px",n.appendChild(i).appendChild(e),void 0!==e.style.zoom&&(e.style.cssText="display:inline;margin:0;border:0;padding:1px;width:1px;zoom:1",mt.inlineBlockNeedsLayout=t=3===e.offsetWidth,t&&(n.style.zoom=1)),n.removeChild(i))}),function(){var t=st.createElement("div");mt.deleteExpando=!0;try{delete t.test}catch(t){mt.deleteExpando=!1}t=null}();var Ht=function(t){var e=vt.noData[(t.nodeName+" ").toLowerCase()],n=+t.nodeType||1;return(1===n||9===n)&&(!e||!0!==e&&t.getAttribute("classid")===e)},qt=/^(?:\{[\w\W]*\}|\[[\w\W]*\])$/,Ft=/([A-Z])/g;vt.extend({cache:{},noData:{"applet ":!0,"embed ":!0,"object ":"clsid:D27CDB6E-AE6D-11cf-96B8-444553540000"},hasData:function(t){return!!(t=t.nodeType?vt.cache[t[vt.expando]]:t[vt.expando])&&!f(t)},data:function(t,e,n){return h(t,e,n)},removeData:function(t,e){return m(t,e)},_data:function(t,e,n){return h(t,e,n,!0)},_removeData:function(t,e){return m(t,e,!0)}}),vt.fn.extend({data:function(t,e){var n,i,r,a=this[0],o=a&&a.attributes;if(void 0===t){if(this.length&&(r=vt.data(a),1===a.nodeType&&!vt._data(a,"parsedAttrs"))){for(n=o.length;n--;)o[n]&&(i=o[n].name,0===i.indexOf("data-")&&(i=vt.camelCase(i.slice(5)),p(a,i,r[i])));vt._data(a,"parsedAttrs",!0)}return r}return"object"==typeof t?this.each(function(){vt.data(this,t)}):arguments.length>1?this.each(function(){vt.data(this,t,e)}):a?p(a,t,vt.data(a,t)):void 0},removeData:function(t){return this.each(function(){vt.removeData(this,t)})}}),vt.extend({queue:function(t,e,n){var i;if(t)return e=(e||"fx")+"queue",i=vt._data(t,e),n&&(!i||vt.isArray(n)?i=vt._data(t,e,vt.makeArray(n)):i.push(n)),i||[]},dequeue:function(t,e){e=e||"fx";var n=vt.queue(t,e),i=n.length,r=n.shift(),a=vt._queueHooks(t,e),o=function(){vt.dequeue(t,e)};"inprogress"===r&&(r=n.shift(),i--),r&&("fx"===e&&n.unshift("inprogress"),delete a.stop,r.call(t,o,a)),!i&&a&&a.empty.fire()},_queueHooks:function(t,e){var n=e+"queueHooks";return vt._data(t,n)||vt._data(t,n,{empty:vt.Callbacks("once memory").add(function(){vt._removeData(t,e+"queue"),vt._removeData(t,n)})})}}),vt.fn.extend({queue:function(t,e){var n=2;return"string"!=typeof t&&(e=t,t="fx",n--),arguments.length<n?vt.queue(this[0],t):void 0===e?this:this.each(function(){var n=vt.queue(this,t,e);vt._queueHooks(this,t),"fx"===t&&"inprogress"!==n[0]&&vt.dequeue(this,t)})},dequeue:function(t){return this.each(function(){vt.dequeue(this,t)})},clearQueue:function(t){return this.queue(t||"fx",[])},promise:function(t,e){var n,i=1,r=vt.Deferred(),a=this,o=this.length,s=function(){--i||r.resolveWith(a,[a])};for("string"!=typeof t&&(e=t,t=void 0),t=t||"fx";o--;)(n=vt._data(a[o],t+"queueHooks"))&&n.empty&&(i++,n.empty.add(s));return s(),r.promise(e)}}),function(){var t;mt.shrinkWrapBlocks=function(){if(null!=t)return t;t=!1;var e,n,i;return(n=st.getElementsByTagName("body")[0])&&n.style?(e=st.createElement("div"),i=st.createElement("div"),i.style.cssText="position:absolute;border:0;width:0;height:0;top:0;left:-9999px",n.appendChild(i).appendChild(e),void 0!==e.style.zoom&&(e.style.cssText="-webkit-box-sizing:content-box;-moz-box-sizing:content-box;box-sizing:content-box;display:block;margin:0;border:0;padding:1px;width:1px;zoom:1",e.appendChild(st.createElement("div")).style.width="5px",t=3!==e.offsetWidth),n.removeChild(i),t):void 0}}();var Rt=/[+-]?(?:\d*\.|)\d+(?:[eE][+-]?\d+|)/.source,Pt=new RegExp("^(?:([+-])=|)("+Rt+")([a-z%]*)$","i"),Mt=["Top","Right","Bottom","Left"],It=function(t,e){return t=e||t,"none"===vt.css(t,"display")||!vt.contains(t.ownerDocument,t)},Bt=function(t,e,n,i,r,a,o){var s=0,l=t.length,c=null==n;if("object"===vt.type(n)){r=!0;for(s in n)Bt(t,e,s,n[s],!0,a,o)}else if(void 0!==i&&(r=!0,vt.isFunction(i)||(o=!0),c&&(o?(e.call(t,i),e=null):(c=e,e=function(t,e,n){return c.call(vt(t),n)})),e))for(;s<l;s++)e(t[s],n,o?i:i.call(t[s],s,e(t[s],n)));return r?t:c?e.call(t):l?e(t[0],n):a},$t=/^(?:checkbox|radio)$/i,Wt=/<([\w:-]+)/,zt=/^$|\/(?:java|ecma)script/i,Xt=/^\s+/,Ut="abbr|article|aside|audio|bdi|canvas|data|datalist|details|dialog|figcaption|figure|footer|header|hgroup|main|mark|meter|nav|output|picture|progress|section|summary|template|time|video";!function(){var t=st.createElement("div"),e=st.createDocumentFragment(),n=st.createElement("input");t.innerHTML="  <link/><table></table><a href='/a'>a</a><input type='checkbox'/>",mt.leadingWhitespace=3===t.firstChild.nodeType,mt.tbody=!t.getElementsByTagName("tbody").length,mt.htmlSerialize=!!t.getElementsByTagName("link").length,mt.html5Clone="<:nav></:nav>"!==st.createElement("nav").cloneNode(!0).outerHTML,n.type="checkbox",n.checked=!0,e.appendChild(n),mt.appendChecked=n.checked,t.innerHTML="<textarea>x</textarea>",mt.noCloneChecked=!!t.cloneNode(!0).lastChild.defaultValue,e.appendChild(t),n=st.createElement("input"),n.setAttribute("type","radio"),n.setAttribute("checked","checked"),n.setAttribute("name","t"),t.appendChild(n),mt.checkClone=t.cloneNode(!0).cloneNode(!0).lastChild.checked,mt.noCloneEvent=!!t.addEventListener,t[vt.expando]=1,mt.attributes=!t.getAttribute(vt.expando)}();var Yt={option:[1,"<select multiple='multiple'>","</select>"],legend:[1,"<fieldset>","</fieldset>"],area:[1,"<map>","</map>"],param:[1,"<object>","</object>"],thead:[1,"<table>","</table>"],tr:[2,"<table><tbody>","</tbody></table>"],col:[2,"<table><tbody></tbody><colgroup>","</colgroup></table>"],td:[3,"<table><tbody><tr>","</tr></tbody></table>"],_default:mt.htmlSerialize?[0,"",""]:[1,"X<div>","</div>"]};Yt.optgroup=Yt.option,Yt.tbody=Yt.tfoot=Yt.colgroup=Yt.caption=Yt.thead,Yt.th=Yt.td;var Gt=/<|&#?\w+;/,Vt=/<tbody/i;!function(){var t,e,i=st.createElement("div");for(t in{submit:!0,change:!0,focusin:!0})e="on"+t,(mt[t]=e in n)||(i.setAttribute(e,"t"),mt[t]=!1===i.attributes[e].expando);i=null}();var Jt=/^(?:input|select|textarea)$/i,Kt=/^key/,Qt=/^(?:mouse|pointer|contextmenu|drag|drop)|click/,Zt=/^(?:focusinfocus|focusoutblur)$/,te=/^([^.]*)(?:\.(.+)|)/;vt.event={global:{},add:function(t,e,n,i,r){var a,o,s,l,c,u,d,p,f,h,m,v=vt._data(t);if(v){for(n.handler&&(l=n,n=l.handler,r=l.selector),n.guid||(n.guid=vt.guid++),(o=v.events)||(o=v.events={}),(u=v.handle)||(u=v.handle=function(t){return void 0===vt||t&&vt.event.triggered===t.type?void 0:vt.event.dispatch.apply(u.elem,arguments)},u.elem=t),e=(e||"").match(jt)||[""],s=e.length;s--;)a=te.exec(e[s])||[],f=m=a[1],h=(a[2]||"").split(".").sort(),f&&(c=vt.event.special[f]||{},f=(r?c.delegateType:c.bindType)||f,c=vt.event.special[f]||{},d=vt.extend({type:f,origType:m,data:i,handler:n,guid:n.guid,selector:r,needsContext:r&&vt.expr.match.needsContext.test(r),namespace:h.join(".")},l),(p=o[f])||(p=o[f]=[],p.delegateCount=0,c.setup&&!1!==c.setup.call(t,i,h,u)||(t.addEventListener?t.addEventListener(f,u,!1):t.attachEvent&&t.attachEvent("on"+f,u))),c.add&&(c.add.call(t,d),d.handler.guid||(d.handler.guid=n.guid)),r?p.splice(p.delegateCount++,0,d):p.push(d),vt.event.global[f]=!0);t=null}},remove:function(t,e,n,i,r){var a,o,s,l,c,u,d,p,f,h,m,v=vt.hasData(t)&&vt._data(t);if(v&&(u=v.events)){for(e=(e||"").match(jt)||[""],c=e.length;c--;)if(s=te.exec(e[c])||[],f=m=s[1],h=(s[2]||"").split(".").sort(),f){for(d=vt.event.special[f]||{},f=(i?d.delegateType:d.bindType)||f,p=u[f]||[],s=s[2]&&new RegExp("(^|\\.)"+h.join("\\.(?:.*\\.|)")+"(\\.|$)"),l=a=p.length;a--;)o=p[a],!r&&m!==o.origType||n&&n.guid!==o.guid||s&&!s.test(o.namespace)||i&&i!==o.selector&&("**"!==i||!o.selector)||(p.splice(a,1),o.selector&&p.delegateCount--,d.remove&&d.remove.call(t,o));l&&!p.length&&(d.teardown&&!1!==d.teardown.call(t,h,v.handle)||vt.removeEvent(t,f,v.handle),delete u[f])}else for(f in u)vt.event.remove(t,f+e[c],n,i,!0);vt.isEmptyObject(u)&&(delete v.handle,vt._removeData(t,"events"))}},trigger:function(t,e,i,r){var a,o,s,l,c,u,d,p=[i||st],f=ht.call(t,"type")?t.type:t,h=ht.call(t,"namespace")?t.namespace.split("."):[];if(s=u=i=i||st,3!==i.nodeType&&8!==i.nodeType&&!Zt.test(f+vt.event.triggered)&&(f.indexOf(".")>-1&&(h=f.split("."),f=h.shift(),h.sort()),o=f.indexOf(":")<0&&"on"+f,t=t[vt.expando]?t:new vt.Event(f,"object"==typeof t&&t),t.isTrigger=r?2:3,t.namespace=h.join("."),t.rnamespace=t.namespace?new RegExp("(^|\\.)"+h.join("\\.(?:.*\\.|)")+"(\\.|$)"):null,t.result=void 0,t.target||(t.target=i),e=null==e?[t]:vt.makeArray(e,[t]),c=vt.event.special[f]||{},r||!c.trigger||!1!==c.trigger.apply(i,e))){if(!r&&!c.noBubble&&!vt.isWindow(i)){for(l=c.delegateType||f,Zt.test(l+f)||(s=s.parentNode);s;s=s.parentNode)p.push(s),u=s;u===(i.ownerDocument||st)&&p.push(u.defaultView||u.parentWindow||n)}for(d=0;(s=p[d++])&&!t.isPropagationStopped();)t.type=d>1?l:c.bindType||f,a=(vt._data(s,"events")||{})[t.type]&&vt._data(s,"handle"),a&&a.apply(s,e),(a=o&&s[o])&&a.apply&&Ht(s)&&(t.result=a.apply(s,e),!1===t.result&&t.preventDefault());if(t.type=f,!r&&!t.isDefaultPrevented()&&(!c._default||!1===c._default.apply(p.pop(),e))&&Ht(i)&&o&&i[f]&&!vt.isWindow(i)){u=i[o],u&&(i[o]=null),vt.event.triggered=f;try{i[f]()}catch(t){}vt.event.triggered=void 0,u&&(i[o]=u)}return t.result}},dispatch:function(t){t=vt.event.fix(t);var e,n,i,r,a,o=[],s=lt.call(arguments),l=(vt._data(this,"events")||{})[t.type]||[],c=vt.event.special[t.type]||{};if(s[0]=t,t.delegateTarget=this,!c.preDispatch||!1!==c.preDispatch.call(this,t)){for(o=vt.event.handlers.call(this,t,l),e=0;(r=o[e++])&&!t.isPropagationStopped();)for(t.currentTarget=r.elem,n=0;(a=r.handlers[n++])&&!t.isImmediatePropagationStopped();)t.rnamespace&&!t.rnamespace.test(a.namespace)||(t.handleObj=a,t.data=a.data,void 0!==(i=((vt.event.special[a.origType]||{}).handle||a.handler).apply(r.elem,s))&&!1===(t.result=i)&&(t.preventDefault(),t.stopPropagation()));return c.postDispatch&&c.postDispatch.call(this,t),t.result}},handlers:function(t,e){var n,i,r,a,o=[],s=e.delegateCount,l=t.target;if(s&&l.nodeType&&("click"!==t.type||isNaN(t.button)||t.button<1))for(;l!=this;l=l.parentNode||this)if(1===l.nodeType&&(!0!==l.disabled||"click"!==t.type)){for(i=[],n=0;n<s;n++)a=e[n],r=a.selector+" ",void 0===i[r]&&(i[r]=a.needsContext?vt(r,this).index(l)>-1:vt.find(r,this,null,[l]).length),i[r]&&i.push(a);i.length&&o.push({elem:l,handlers:i})}return s<e.length&&o.push({elem:this,handlers:e.slice(s)}),o},fix:function(t){if(t[vt.expando])return t;var e,n,i,r=t.type,a=t,o=this.fixHooks[r];for(o||(this.fixHooks[r]=o=Qt.test(r)?this.mouseHooks:Kt.test(r)?this.keyHooks:{}),i=o.props?this.props.concat(o.props):this.props,t=new vt.Event(a),e=i.length;e--;)n=i[e],t[n]=a[n];return t.target||(t.target=a.srcElement||st),3===t.target.nodeType&&(t.target=t.target.parentNode),t.metaKey=!!t.metaKey,o.filter?o.filter(t,a):t},props:"altKey bubbles cancelable ctrlKey currentTarget detail eventPhase metaKey relatedTarget shiftKey target timeStamp view which".split(" "),fixHooks:{},keyHooks:{props:"char charCode key keyCode".split(" "),filter:function(t,e){return null==t.which&&(t.which=null!=e.charCode?e.charCode:e.keyCode),t}},mouseHooks:{props:"button buttons clientX clientY fromElement offsetX offsetY pageX pageY screenX screenY toElement".split(" "),filter:function(t,e){var n,i,r,a=e.button,o=e.fromElement;return null==t.pageX&&null!=e.clientX&&(i=t.target.ownerDocument||st,r=i.documentElement,n=i.body,t.pageX=e.clientX+(r&&r.scrollLeft||n&&n.scrollLeft||0)-(r&&r.clientLeft||n&&n.clientLeft||0),t.pageY=e.clientY+(r&&r.scrollTop||n&&n.scrollTop||0)-(r&&r.clientTop||n&&n.clientTop||0)),!t.relatedTarget&&o&&(t.relatedTarget=o===t.target?e.toElement:o),t.which||void 0===a||(t.which=1&a?1:2&a?3:4&a?2:0),t}},special:{load:{noBubble:!0},focus:{trigger:function(){if(this!==S()&&this.focus)try{return this.focus(),!1}catch(t){}},delegateType:"focusin"},blur:{trigger:function(){if(this===S()&&this.blur)return this.blur(),!1},delegateType:"focusout"},click:{trigger:function(){if(vt.nodeName(this,"input")&&"checkbox"===this.type&&this.click)return this.click(),!1},_default:function(t){return vt.nodeName(t.target,"a")}},beforeunload:{postDispatch:function(t){void 0!==t.result&&t.originalEvent&&(t.originalEvent.returnValue=t.result)}}},simulate:function(t,e,n){var i=vt.extend(new vt.Event,n,{type:t,isSimulated:!0});vt.event.trigger(i,null,e),i.isDefaultPrevented()&&n.preventDefault()}},vt.removeEvent=st.removeEventListener?function(t,e,n){t.removeEventListener&&t.removeEventListener(e,n)}:function(t,e,n){var i="on"+e;t.detachEvent&&(void 0===t[i]&&(t[i]=null),t.detachEvent(i,n))},vt.Event=function(t,e){if(!(this instanceof vt.Event))return new vt.Event(t,e);t&&t.type?(this.originalEvent=t,this.type=t.type,this.isDefaultPrevented=t.defaultPrevented||void 0===t.defaultPrevented&&!1===t.returnValue?C:w):this.type=t,e&&vt.extend(this,e),this.timeStamp=t&&t.timeStamp||vt.now(),this[vt.expando]=!0},vt.Event.prototype={constructor:vt.Event,isDefaultPrevented:w,isPropagationStopped:w,isImmediatePropagationStopped:w,preventDefault:function(){var t=this.originalEvent;this.isDefaultPrevented=C,t&&(t.preventDefault?t.preventDefault():t.returnValue=!1)},stopPropagation:function(){var t=this.originalEvent;this.isPropagationStopped=C,t&&!this.isSimulated&&(t.stopPropagation&&t.stopPropagation(),t.cancelBubble=!0)},stopImmediatePropagation:function(){var t=this.originalEvent;this.isImmediatePropagationStopped=C,t&&t.stopImmediatePropagation&&t.stopImmediatePropagation(),this.stopPropagation()}},vt.each({mouseenter:"mouseover",mouseleave:"mouseout",pointerenter:"pointerover",pointerleave:"pointerout"},function(t,e){vt.event.special[t]={delegateType:e,bindType:e,handle:function(t){var n,i=this,r=t.relatedTarget,a=t.handleObj;return r&&(r===i||vt.contains(i,r))||(t.type=a.origType,n=a.handler.apply(this,arguments),t.type=e),n}}}),mt.submit||(vt.event.special.submit={setup:function(){if(vt.nodeName(this,"form"))return!1;vt.event.add(this,"click._submit keypress._submit",function(t){var e=t.target,n=vt.nodeName(e,"input")||vt.nodeName(e,"button")?vt.prop(e,"form"):void 0;n&&!vt._data(n,"submit")&&(vt.event.add(n,"submit._submit",function(t){t._submitBubble=!0}),vt._data(n,"submit",!0))})},postDispatch:function(t){t._submitBubble&&(delete t._submitBubble,this.parentNode&&!t.isTrigger&&vt.event.simulate("submit",this.parentNode,t))},teardown:function(){if(vt.nodeName(this,"form"))return!1;vt.event.remove(this,"._submit")}}),mt.change||(vt.event.special.change={setup:function(){if(Jt.test(this.nodeName))return"checkbox"!==this.type&&"radio"!==this.type||(vt.event.add(this,"propertychange._change",function(t){"checked"===t.originalEvent.propertyName&&(this._justChanged=!0)}),vt.event.add(this,"click._change",function(t){this._justChanged&&!t.isTrigger&&(this._justChanged=!1),vt.event.simulate("change",this,t)})),!1;vt.event.add(this,"beforeactivate._change",function(t){var e=t.target;Jt.test(e.nodeName)&&!vt._data(e,"change")&&(vt.event.add(e,"change._change",function(t){!this.parentNode||t.isSimulated||t.isTrigger||vt.event.simulate("change",this.parentNode,t)}),vt._data(e,"change",!0))})},handle:function(t){var e=t.target;if(this!==e||t.isSimulated||t.isTrigger||"radio"!==e.type&&"checkbox"!==e.type)return t.handleObj.handler.apply(this,arguments)},teardown:function(){return vt.event.remove(this,"._change"),!Jt.test(this.nodeName)}}),mt.focusin||vt.each({focus:"focusin",blur:"focusout"},function(t,e){var n=function(t){vt.event.simulate(e,t.target,vt.event.fix(t))};vt.event.special[e]={setup:function(){var i=this.ownerDocument||this,r=vt._data(i,e);r||i.addEventListener(t,n,!0),vt._data(i,e,(r||0)+1)},teardown:function(){var i=this.ownerDocument||this,r=vt._data(i,e)-1;r?vt._data(i,e,r):(i.removeEventListener(t,n,!0),vt._removeData(i,e))}}}),vt.fn.extend({on:function(t,e,n,i){return T(this,t,e,n,i)},one:function(t,e,n,i){return T(this,t,e,n,i,1)},off:function(t,e,n){var i,r;if(t&&t.preventDefault&&t.handleObj)return i=t.handleObj,vt(t.delegateTarget).off(i.namespace?i.origType+"."+i.namespace:i.origType,i.selector,i.handler),this;if("object"==typeof t){for(r in t)this.off(r,e,t[r]);return this}return!1!==e&&"function"!=typeof e||(n=e,e=void 0),!1===n&&(n=w),this.each(function(){vt.event.remove(this,t,n,e)})},trigger:function(t,e){return this.each(function(){vt.event.trigger(t,e,this)})},triggerHandler:function(t,e){var n=this[0];if(n)return vt.event.trigger(t,e,n,!0)}});var ee=/ jQuery\d+="(?:null|\d+)"/g,ne=new RegExp("<(?:"+Ut+")[\\s/>]","i"),ie=/<(?!area|br|col|embed|hr|img|input|link|meta|param)(([\w:-]+)[^>]*)\/>/gi,re=/<script|<style|<link/i,ae=/checked\s*(?:[^=]|=\s*.checked.)/i,oe=/^true\/(.*)/,se=/^\s*<!(?:\[CDATA\[|--)|(?:\]\]|--)>\s*$/g,le=g(st),ce=le.appendChild(st.createElement("div"));vt.extend({htmlPrefilter:function(t){return t.replace(ie,"<$1></$2>")},clone:function(t,e,n){var i,r,a,o,s,l=vt.contains(t.ownerDocument,t);if(mt.html5Clone||vt.isXMLDoc(t)||!ne.test("<"+t.nodeName+">")?a=t.cloneNode(!0):(ce.innerHTML=t.outerHTML,ce.removeChild(a=ce.firstChild)),!(mt.noCloneEvent&&mt.noCloneChecked||1!==t.nodeType&&11!==t.nodeType||vt.isXMLDoc(t)))for(i=y(a),s=y(t),o=0;null!=(r=s[o]);++o)i[o]&&A(r,i[o]);if(e)if(n)for(s=s||y(t),i=i||y(a),o=0;null!=(r=s[o]);o++)D(r,i[o]);else D(t,a);return i=y(a,"script"),i.length>0&&b(i,!l&&y(t,"script")),i=s=r=null,a},cleanData:function(t,e){for(var n,i,r,a,o=0,s=vt.expando,l=vt.cache,c=mt.attributes,u=vt.event.special;null!=(n=t[o]);o++)if((e||Ht(n))&&(r=n[s],a=r&&l[r])){if(a.events)for(i in a.events)u[i]?vt.event.remove(n,i):vt.removeEvent(n,i,a.handle);l[r]&&(delete l[r],c||void 0===n.removeAttribute?n[s]=void 0:n.removeAttribute(s),ot.push(r))}}}),vt.fn.extend({domManip:j,detach:function(t){return L(this,t,!0)},remove:function(t){return L(this,t)},text:function(t){return Bt(this,function(t){return void 0===t?vt.text(this):this.empty().append((this[0]&&this[0].ownerDocument||st).createTextNode(t))},null,t,arguments.length)},append:function(){return j(this,arguments,function(t){if(1===this.nodeType||11===this.nodeType||9===this.nodeType){k(this,t).appendChild(t)}})},prepend:function(){return j(this,arguments,function(t){if(1===this.nodeType||11===this.nodeType||9===this.nodeType){var e=k(this,t);e.insertBefore(t,e.firstChild)}})},before:function(){return j(this,arguments,function(t){this.parentNode&&this.parentNode.insertBefore(t,this)})},after:function(){return j(this,arguments,function(t){this.parentNode&&this.parentNode.insertBefore(t,this.nextSibling)})},empty:function(){for(var t,e=0;null!=(t=this[e]);e++){for(1===t.nodeType&&vt.cleanData(y(t,!1));t.firstChild;)t.removeChild(t.firstChild);t.options&&vt.nodeName(t,"select")&&(t.options.length=0)}return this},clone:function(t,e){return t=null!=t&&t,e=null==e?t:e,this.map(function(){return vt.clone(this,t,e)})},html:function(t){return Bt(this,function(t){var e=this[0]||{},n=0,i=this.length;if(void 0===t)return 1===e.nodeType?e.innerHTML.replace(ee,""):void 0;if("string"==typeof t&&!re.test(t)&&(mt.htmlSerialize||!ne.test(t))&&(mt.leadingWhitespace||!Xt.test(t))&&!Yt[(Wt.exec(t)||["",""])[1].toLowerCase()]){t=vt.htmlPrefilter(t);try{for(;n<i;n++)e=this[n]||{},1===e.nodeType&&(vt.cleanData(y(e,!1)),e.innerHTML=t);e=0}catch(t){}}e&&this.empty().append(t)},null,t,arguments.length)},replaceWith:function(){var t=[];return j(this,arguments,function(e){var n=this.parentNode;vt.inArray(this,t)<0&&(vt.cleanData(y(this)),n&&n.replaceChild(e,this))},t)}}),vt.each({appendTo:"append",prependTo:"prepend",insertBefore:"before",insertAfter:"after",replaceAll:"replaceWith"},function(t,e){vt.fn[t]=function(t){for(var n,i=0,r=[],a=vt(t),o=a.length-1;i<=o;i++)n=i===o?this:this.clone(!0),vt(a[i])[e](n),ut.apply(r,n.get());return this.pushStack(r)}});var ue,de={HTML:"block",BODY:"block"},pe=/^margin/,fe=new RegExp("^("+Rt+")(?!px)[a-z%]+$","i"),he=function(t,e,n,i){var r,a,o={};for(a in e)o[a]=t.style[a],t.style[a]=e[a];r=n.apply(t,i||[]);for(a in e)t.style[a]=o[a];return r},me=st.documentElement;!function(){function t(){var t,u,d=st.documentElement;d.appendChild(l),c.style.cssText="-webkit-box-sizing:border-box;box-sizing:border-box;position:relative;display:block;margin:auto;border:1px;padding:1px;top:1%;width:50%",e=r=s=!1,i=o=!0,n.getComputedStyle&&(u=n.getComputedStyle(c),e="1%"!==(u||{}).top,s="2px"===(u||{}).marginLeft,r="4px"===(u||{width:"4px"}).width,c.style.marginRight="50%",i="4px"===(u||{marginRight:"4px"}).marginRight,t=c.appendChild(st.createElement("div")),t.style.cssText=c.style.cssText="-webkit-box-sizing:content-box;-moz-box-sizing:content-box;box-sizing:content-box;display:block;margin:0;border:0;padding:0",t.style.marginRight=t.style.width="0",c.style.width="1px",o=!parseFloat((n.getComputedStyle(t)||{}).marginRight),c.removeChild(t)),c.style.display="none",a=0===c.getClientRects().length,a&&(c.style.display="",c.innerHTML="<table><tr><td></td><td>t</td></tr></table>",c.childNodes[0].style.borderCollapse="separate",t=c.getElementsByTagName("td"),t[0].style.cssText="margin:0;border:0;padding:0;display:none",(a=0===t[0].offsetHeight)&&(t[0].style.display="",t[1].style.display="none",a=0===t[0].offsetHeight)),d.removeChild(l)}var e,i,r,a,o,s,l=st.createElement("div"),c=st.createElement("div");c.style&&(c.style.cssText="float:left;opacity:.5",mt.opacity="0.5"===c.style.opacity,mt.cssFloat=!!c.style.cssFloat,c.style.backgroundClip="content-box",c.cloneNode(!0).style.backgroundClip="",mt.clearCloneStyle="content-box"===c.style.backgroundClip,l=st.createElement("div"),l.style.cssText="border:0;width:8px;height:0;top:0;left:-9999px;padding:0;margin-top:1px;position:absolute",c.innerHTML="",l.appendChild(c),mt.boxSizing=""===c.style.boxSizing||""===c.style.MozBoxSizing||""===c.style.WebkitBoxSizing,vt.extend(mt,{reliableHiddenOffsets:function(){return null==e&&t(),a},boxSizingReliable:function(){return null==e&&t(),r},pixelMarginRight:function(){return null==e&&t(),i},pixelPosition:function(){return null==e&&t(),e},reliableMarginRight:function(){return null==e&&t(),o},reliableMarginLeft:function(){return null==e&&t(),s}}))}();var ve,ge,ye=/^(top|right|bottom|left)$/;n.getComputedStyle?(ve=function(t){var e=t.ownerDocument.defaultView;return e&&e.opener||(e=n),e.getComputedStyle(t)},ge=function(t,e,n){var i,r,a,o,s=t.style;return n=n||ve(t),o=n?n.getPropertyValue(e)||n[e]:void 0,""!==o&&void 0!==o||vt.contains(t.ownerDocument,t)||(o=vt.style(t,e)),n&&!mt.pixelMarginRight()&&fe.test(o)&&pe.test(e)&&(i=s.width,r=s.minWidth,a=s.maxWidth,s.minWidth=s.maxWidth=s.width=o,o=n.width,s.width=i,s.minWidth=r,s.maxWidth=a),void 0===o?o:o+""}):me.currentStyle&&(ve=function(t){return t.currentStyle},ge=function(t,e,n){var i,r,a,o,s=t.style;return n=n||ve(t),o=n?n[e]:void 0,null==o&&s&&s[e]&&(o=s[e]),fe.test(o)&&!ye.test(e)&&(i=s.left,r=t.runtimeStyle,a=r&&r.left,a&&(r.left=t.currentStyle.left),s.left="fontSize"===e?"1em":o,o=s.pixelLeft+"px",s.left=i,a&&(r.left=a)),void 0===o?o:o+""||"auto"});var be=/alpha\([^)]*\)/i,xe=/opacity\s*=\s*([^)]*)/i,_e=/^(none|table(?!-c[ea]).+)/,Ce=new RegExp("^("+Rt+")(.*)$","i"),we={position:"absolute",visibility:"hidden",display:"block"},Se={letterSpacing:"0",fontWeight:"400"},Te=["Webkit","O","Moz","ms"],ke=st.createElement("div").style;vt.extend({cssHooks:{opacity:{get:function(t,e){if(e){var n=ge(t,"opacity");return""===n?"1":n}}}},cssNumber:{animationIterationCount:!0,columnCount:!0,fillOpacity:!0,flexGrow:!0,flexShrink:!0,fontWeight:!0,lineHeight:!0,opacity:!0,order:!0,orphans:!0,widows:!0,zIndex:!0,zoom:!0},cssProps:{float:mt.cssFloat?"cssFloat":"styleFloat"},style:function(t,e,n,i){if(t&&3!==t.nodeType&&8!==t.nodeType&&t.style){var r,a,o,s=vt.camelCase(e),l=t.style;if(e=vt.cssProps[s]||(vt.cssProps[s]=F(s)||s),o=vt.cssHooks[e]||vt.cssHooks[s],void 0===n)return o&&"get"in o&&void 0!==(r=o.get(t,!1,i))?r:l[e];if(a=typeof n,"string"===a&&(r=Pt.exec(n))&&r[1]&&(n=v(t,e,r),a="number"),null!=n&&n===n&&("number"===a&&(n+=r&&r[3]||(vt.cssNumber[s]?"":"px")),mt.clearCloneStyle||""!==n||0!==e.indexOf("background")||(l[e]="inherit"),!(o&&"set"in o&&void 0===(n=o.set(t,n,i)))))try{l[e]=n}catch(t){}}},css:function(t,e,n,i){var r,a,o,s=vt.camelCase(e);return e=vt.cssProps[s]||(vt.cssProps[s]=F(s)||s),o=vt.cssHooks[e]||vt.cssHooks[s],o&&"get"in o&&(a=o.get(t,!0,n)),void 0===a&&(a=ge(t,e,i)),"normal"===a&&e in Se&&(a=Se[e]),""===n||n?(r=parseFloat(a),!0===n||isFinite(r)?r||0:a):a}}),vt.each(["height","width"],function(t,e){vt.cssHooks[e]={get:function(t,n,i){if(n)return _e.test(vt.css(t,"display"))&&0===t.offsetWidth?he(t,we,function(){return I(t,e,i)}):I(t,e,i)},set:function(t,n,i){var r=i&&ve(t);return P(t,n,i?M(t,e,i,mt.boxSizing&&"border-box"===vt.css(t,"boxSizing",!1,r),r):0)}}}),mt.opacity||(vt.cssHooks.opacity={get:function(t,e){return xe.test((e&&t.currentStyle?t.currentStyle.filter:t.style.filter)||"")?.01*parseFloat(RegExp.$1)+"":e?"1":""},set:function(t,e){var n=t.style,i=t.currentStyle,r=vt.isNumeric(e)?"alpha(opacity="+100*e+")":"",a=i&&i.filter||n.filter||"";n.zoom=1,(e>=1||""===e)&&""===vt.trim(a.replace(be,""))&&n.removeAttribute&&(n.removeAttribute("filter"),""===e||i&&!i.filter)||(n.filter=be.test(a)?a.replace(be,r):a+" "+r)}}),vt.cssHooks.marginRight=q(mt.reliableMarginRight,function(t,e){if(e)return he(t,{display:"inline-block"},ge,[t,"marginRight"])}),vt.cssHooks.marginLeft=q(mt.reliableMarginLeft,function(t,e){if(e)return(parseFloat(ge(t,"marginLeft"))||(vt.contains(t.ownerDocument,t)?t.getBoundingClientRect().left-he(t,{marginLeft:0},function(){return t.getBoundingClientRect().left}):0))+"px"}),vt.each({margin:"",padding:"",border:"Width"},function(t,e){vt.cssHooks[t+e]={expand:function(n){for(var i=0,r={},a="string"==typeof n?n.split(" "):[n];i<4;i++)r[t+Mt[i]+e]=a[i]||a[i-2]||a[0];return r}},pe.test(t)||(vt.cssHooks[t+e].set=P)}),vt.fn.extend({css:function(t,e){return Bt(this,function(t,e,n){var i,r,a={},o=0;if(vt.isArray(e)){for(i=ve(t),r=e.length;o<r;o++)a[e[o]]=vt.css(t,e[o],!1,i);return a}return void 0!==n?vt.style(t,e,n):vt.css(t,e)},t,e,arguments.length>1)},show:function(){return R(this,!0)},hide:function(){return R(this)},toggle:function(t){return"boolean"==typeof t?t?this.show():this.hide():this.each(function(){It(this)?vt(this).show():vt(this).hide()})}}),vt.Tween=B,B.prototype={constructor:B,init:function(t,e,n,i,r,a){this.elem=t,this.prop=n,this.easing=r||vt.easing._default,this.options=e,this.start=this.now=this.cur(),this.end=i,this.unit=a||(vt.cssNumber[n]?"":"px")},cur:function(){var t=B.propHooks[this.prop];return t&&t.get?t.get(this):B.propHooks._default.get(this)},run:function(t){var e,n=B.propHooks[this.prop];return this.options.duration?this.pos=e=vt.easing[this.easing](t,this.options.duration*t,0,1,this.options.duration):this.pos=e=t,this.now=(this.end-this.start)*e+this.start,this.options.step&&this.options.step.call(this.elem,this.now,this),n&&n.set?n.set(this):B.propHooks._default.set(this),this}},B.prototype.init.prototype=B.prototype,B.propHooks={_default:{get:function(t){var e;return 1!==t.elem.nodeType||null!=t.elem[t.prop]&&null==t.elem.style[t.prop]?t.elem[t.prop]:(e=vt.css(t.elem,t.prop,""),e&&"auto"!==e?e:0)},set:function(t){vt.fx.step[t.prop]?vt.fx.step[t.prop](t):1!==t.elem.nodeType||null==t.elem.style[vt.cssProps[t.prop]]&&!vt.cssHooks[t.prop]?t.elem[t.prop]=t.now:vt.style(t.elem,t.prop,t.now+t.unit)}}},B.propHooks.scrollTop=B.propHooks.scrollLeft={set:function(t){t.elem.nodeType&&t.elem.parentNode&&(t.elem[t.prop]=t.now)}},vt.easing={linear:function(t){return t},swing:function(t){return.5-Math.cos(t*Math.PI)/2},_default:"swing"},vt.fx=B.prototype.init,vt.fx.step={};var Ee,Ne,De=/^(?:toggle|show|hide)$/,Ae=/queueHooks$/;vt.Animation=vt.extend(Y,{tweeners:{"*":[function(t,e){var n=this.createTween(t,e);return v(n.elem,t,Pt.exec(e),n),n}]},tweener:function(t,e){vt.isFunction(t)?(e=t,t=["*"]):t=t.match(jt);for(var n,i=0,r=t.length;i<r;i++)n=t[i],Y.tweeners[n]=Y.tweeners[n]||[],Y.tweeners[n].unshift(e)},prefilters:[X],prefilter:function(t,e){e?Y.prefilters.unshift(t):Y.prefilters.push(t)}}),vt.speed=function(t,e,n){var i=t&&"object"==typeof t?vt.extend({},t):{complete:n||!n&&e||vt.isFunction(t)&&t,duration:t,easing:n&&e||e&&!vt.isFunction(e)&&e};return i.duration=vt.fx.off?0:"number"==typeof i.duration?i.duration:i.duration in vt.fx.speeds?vt.fx.speeds[i.duration]:vt.fx.speeds._default,null!=i.queue&&!0!==i.queue||(i.queue="fx"),i.old=i.complete,i.complete=function(){vt.isFunction(i.old)&&i.old.call(this),i.queue&&vt.dequeue(this,i.queue)},i},vt.fn.extend({fadeTo:function(t,e,n,i){return this.filter(It).css("opacity",0).show().end().animate({opacity:e},t,n,i)},animate:function(t,e,n,i){var r=vt.isEmptyObject(t),a=vt.speed(e,n,i),o=function(){var e=Y(this,vt.extend({},t),a);(r||vt._data(this,"finish"))&&e.stop(!0)};return o.finish=o,r||!1===a.queue?this.each(o):this.queue(a.queue,o)},stop:function(t,e,n){var i=function(t){var e=t.stop;delete t.stop,e(n)};return"string"!=typeof t&&(n=e,e=t,t=void 0),e&&!1!==t&&this.queue(t||"fx",[]),this.each(function(){var e=!0,r=null!=t&&t+"queueHooks",a=vt.timers,o=vt._data(this);if(r)o[r]&&o[r].stop&&i(o[r]);else for(r in o)o[r]&&o[r].stop&&Ae.test(r)&&i(o[r]);for(r=a.length;r--;)a[r].elem!==this||null!=t&&a[r].queue!==t||(a[r].anim.stop(n),e=!1,a.splice(r,1));!e&&n||vt.dequeue(this,t)})},finish:function(t){return!1!==t&&(t=t||"fx"),this.each(function(){var e,n=vt._data(this),i=n[t+"queue"],r=n[t+"queueHooks"],a=vt.timers,o=i?i.length:0;for(n.finish=!0,vt.queue(this,t,[]),r&&r.stop&&r.stop.call(this,!0),e=a.length;e--;)a[e].elem===this&&a[e].queue===t&&(a[e].anim.stop(!0),a.splice(e,1));for(e=0;e<o;e++)i[e]&&i[e].finish&&i[e].finish.call(this);delete n.finish})}}),vt.each(["toggle","show","hide"],function(t,e){var n=vt.fn[e];vt.fn[e]=function(t,i,r){return null==t||"boolean"==typeof t?n.apply(this,arguments):this.animate(W(e,!0),t,i,r)}}),vt.each({slideDown:W("show"),slideUp:W("hide"),slideToggle:W("toggle"),fadeIn:{opacity:"show"},fadeOut:{opacity:"hide"},fadeToggle:{opacity:"toggle"}},function(t,e){vt.fn[t]=function(t,n,i){return this.animate(e,t,n,i)}}),vt.timers=[],vt.fx.tick=function(){var t,e=vt.timers,n=0;for(Ee=vt.now();n<e.length;n++)(t=e[n])()||e[n]!==t||e.splice(n--,1);e.length||vt.fx.stop(),Ee=void 0},vt.fx.timer=function(t){vt.timers.push(t),t()?vt.fx.start():vt.timers.pop()},vt.fx.interval=13,vt.fx.start=function(){Ne||(Ne=n.setInterval(vt.fx.tick,vt.fx.interval))},vt.fx.stop=function(){n.clearInterval(Ne),Ne=null},vt.fx.speeds={slow:600,fast:200,_default:400},vt.fn.delay=function(t,e){return t=vt.fx?vt.fx.speeds[t]||t:t,e=e||"fx",this.queue(e,function(e,i){var r=n.setTimeout(e,t);i.stop=function(){n.clearTimeout(r)}})},function(){var t,e=st.createElement("input"),n=st.createElement("div"),i=st.createElement("select"),r=i.appendChild(st.createElement("option"));n=st.createElement("div"),n.setAttribute("className","t"),n.innerHTML="  <link/><table></table><a href='/a'>a</a><input type='checkbox'/>",t=n.getElementsByTagName("a")[0],e.setAttribute("type","checkbox"),n.appendChild(e),t=n.getElementsByTagName("a")[0],t.style.cssText="top:1px",mt.getSetAttribute="t"!==n.className,mt.style=/top/.test(t.getAttribute("style")),mt.hrefNormalized="/a"===t.getAttribute("href"),mt.checkOn=!!e.value,mt.optSelected=r.selected,mt.enctype=!!st.createElement("form").enctype,i.disabled=!0,mt.optDisabled=!r.disabled,e=st.createElement("input"),e.setAttribute("value",""),mt.input=""===e.getAttribute("value"),e.value="t",e.setAttribute("type","radio"),mt.radioValue="t"===e.value}();var je=/\r/g,Le=/[\x20\t\r\n\f]+/g;vt.fn.extend({val:function(t){var e,n,i,r=this[0];{if(arguments.length)return i=vt.isFunction(t),this.each(function(n){var r;1===this.nodeType&&(r=i?t.call(this,n,vt(this).val()):t,null==r?r="":"number"==typeof r?r+="":vt.isArray(r)&&(r=vt.map(r,function(t){return null==t?"":t+""})),(e=vt.valHooks[this.type]||vt.valHooks[this.nodeName.toLowerCase()])&&"set"in e&&void 0!==e.set(this,r,"value")||(this.value=r))});if(r)return(e=vt.valHooks[r.type]||vt.valHooks[r.nodeName.toLowerCase()])&&"get"in e&&void 0!==(n=e.get(r,"value"))?n:(n=r.value,"string"==typeof n?n.replace(je,""):null==n?"":n)}}}),vt.extend({valHooks:{option:{get:function(t){var e=vt.find.attr(t,"value");return null!=e?e:vt.trim(vt.text(t)).replace(Le," ")}},select:{get:function(t){for(var e,n,i=t.options,r=t.selectedIndex,a="select-one"===t.type||r<0,o=a?null:[],s=a?r+1:i.length,l=r<0?s:a?r:0;l<s;l++)if(n=i[l],(n.selected||l===r)&&(mt.optDisabled?!n.disabled:null===n.getAttribute("disabled"))&&(!n.parentNode.disabled||!vt.nodeName(n.parentNode,"optgroup"))){if(e=vt(n).val(),a)return e;o.push(e)}return o},set:function(t,e){for(var n,i,r=t.options,a=vt.makeArray(e),o=r.length;o--;)if(i=r[o],vt.inArray(vt.valHooks.option.get(i),a)>-1)try{i.selected=n=!0}catch(t){i.scrollHeight}else i.selected=!1;return n||(t.selectedIndex=-1),r}}}}),vt.each(["radio","checkbox"],function(){vt.valHooks[this]={set:function(t,e){if(vt.isArray(e))return t.checked=vt.inArray(vt(t).val(),e)>-1}},mt.checkOn||(vt.valHooks[this].get=function(t){return null===t.getAttribute("value")?"on":t.value})});var Oe,He,qe=vt.expr.attrHandle,Fe=/^(?:checked|selected)$/i,Re=mt.getSetAttribute,Pe=mt.input;vt.fn.extend({attr:function(t,e){return Bt(this,vt.attr,t,e,arguments.length>1)},removeAttr:function(t){return this.each(function(){vt.removeAttr(this,t)})}}),vt.extend({attr:function(t,e,n){var i,r,a=t.nodeType;if(3!==a&&8!==a&&2!==a)return void 0===t.getAttribute?vt.prop(t,e,n):(1===a&&vt.isXMLDoc(t)||(e=e.toLowerCase(),r=vt.attrHooks[e]||(vt.expr.match.bool.test(e)?He:Oe)),void 0!==n?null===n?void vt.removeAttr(t,e):r&&"set"in r&&void 0!==(i=r.set(t,n,e))?i:(t.setAttribute(e,n+""),n):r&&"get"in r&&null!==(i=r.get(t,e))?i:(i=vt.find.attr(t,e),null==i?void 0:i))},attrHooks:{type:{set:function(t,e){if(!mt.radioValue&&"radio"===e&&vt.nodeName(t,"input")){var n=t.value;return t.setAttribute("type",e),n&&(t.value=n),e}}}},removeAttr:function(t,e){var n,i,r=0,a=e&&e.match(jt);if(a&&1===t.nodeType)for(;n=a[r++];)i=vt.propFix[n]||n,vt.expr.match.bool.test(n)?Pe&&Re||!Fe.test(n)?t[i]=!1:t[vt.camelCase("default-"+n)]=t[i]=!1:vt.attr(t,n,""),t.removeAttribute(Re?n:i)}}),He={set:function(t,e,n){return!1===e?vt.removeAttr(t,n):Pe&&Re||!Fe.test(n)?t.setAttribute(!Re&&vt.propFix[n]||n,n):t[vt.camelCase("default-"+n)]=t[n]=!0,n}},vt.each(vt.expr.match.bool.source.match(/\w+/g),function(t,e){var n=qe[e]||vt.find.attr;Pe&&Re||!Fe.test(e)?qe[e]=function(t,e,i){var r,a;return i||(a=qe[e],qe[e]=r,r=null!=n(t,e,i)?e.toLowerCase():null,qe[e]=a),r}:qe[e]=function(t,e,n){if(!n)return t[vt.camelCase("default-"+e)]?e.toLowerCase():null}}),Pe&&Re||(vt.attrHooks.value={set:function(t,e,n){if(!vt.nodeName(t,"input"))return Oe&&Oe.set(t,e,n);t.defaultValue=e}}),Re||(Oe={set:function(t,e,n){var i=t.getAttributeNode(n);if(i||t.setAttributeNode(i=t.ownerDocument.createAttribute(n)),i.value=e+="","value"===n||e===t.getAttribute(n))return e}},qe.id=qe.name=qe.coords=function(t,e,n){var i;if(!n)return(i=t.getAttributeNode(e))&&""!==i.value?i.value:null},vt.valHooks.button={get:function(t,e){var n=t.getAttributeNode(e);if(n&&n.specified)return n.value},set:Oe.set},vt.attrHooks.contenteditable={set:function(t,e,n){Oe.set(t,""!==e&&e,n)}},vt.each(["width","height"],function(t,e){vt.attrHooks[e]={set:function(t,n){if(""===n)return t.setAttribute(e,"auto"),n}}})),mt.style||(vt.attrHooks.style={get:function(t){return t.style.cssText||void 0},set:function(t,e){return t.style.cssText=e+""}});var Me=/^(?:input|select|textarea|button|object)$/i,Ie=/^(?:a|area)$/i;vt.fn.extend({prop:function(t,e){return Bt(this,vt.prop,t,e,arguments.length>1)},removeProp:function(t){return t=vt.propFix[t]||t,this.each(function(){try{this[t]=void 0,delete this[t]}catch(t){}})}}),vt.extend({prop:function(t,e,n){var i,r,a=t.nodeType;if(3!==a&&8!==a&&2!==a)return 1===a&&vt.isXMLDoc(t)||(e=vt.propFix[e]||e,r=vt.propHooks[e]),void 0!==n?r&&"set"in r&&void 0!==(i=r.set(t,n,e))?i:t[e]=n:r&&"get"in r&&null!==(i=r.get(t,e))?i:t[e]},propHooks:{tabIndex:{get:function(t){var e=vt.find.attr(t,"tabindex");return e?parseInt(e,10):Me.test(t.nodeName)||Ie.test(t.nodeName)&&t.href?0:-1}}},propFix:{for:"htmlFor",class:"className"}}),mt.hrefNormalized||vt.each(["href","src"],function(t,e){vt.propHooks[e]={get:function(t){return t.getAttribute(e,4)}}}),mt.optSelected||(vt.propHooks.selected={get:function(t){var e=t.parentNode;return e&&(e.selectedIndex,e.parentNode&&e.parentNode.selectedIndex),null},set:function(t){var e=t.parentNode;e&&(e.selectedIndex,e.parentNode&&e.parentNode.selectedIndex)}}),vt.each(["tabIndex","readOnly","maxLength","cellSpacing","cellPadding","rowSpan","colSpan","useMap","frameBorder","contentEditable"],function(){vt.propFix[this.toLowerCase()]=this}),mt.enctype||(vt.propFix.enctype="encoding");var Be=/[\t\r\n\f]/g;vt.fn.extend({addClass:function(t){var e,n,i,r,a,o,s,l=0;if(vt.isFunction(t))return this.each(function(e){vt(this).addClass(t.call(this,e,G(this)))});if("string"==typeof t&&t)for(e=t.match(jt)||[];n=this[l++];)if(r=G(n),i=1===n.nodeType&&(" "+r+" ").replace(Be," ")){for(o=0;a=e[o++];)i.indexOf(" "+a+" ")<0&&(i+=a+" ");s=vt.trim(i),r!==s&&vt.attr(n,"class",s)}return this},removeClass:function(t){var e,n,i,r,a,o,s,l=0;if(vt.isFunction(t))return this.each(function(e){vt(this).removeClass(t.call(this,e,G(this)))});if(!arguments.length)return this.attr("class","");if("string"==typeof t&&t)for(e=t.match(jt)||[];n=this[l++];)if(r=G(n),i=1===n.nodeType&&(" "+r+" ").replace(Be," ")){for(o=0;a=e[o++];)for(;i.indexOf(" "+a+" ")>-1;)i=i.replace(" "+a+" "," ");s=vt.trim(i),r!==s&&vt.attr(n,"class",s)}return this},toggleClass:function(t,e){var n=typeof t;return"boolean"==typeof e&&"string"===n?e?this.addClass(t):this.removeClass(t):vt.isFunction(t)?this.each(function(n){vt(this).toggleClass(t.call(this,n,G(this),e),e)}):this.each(function(){var e,i,r,a;if("string"===n)for(i=0,r=vt(this),a=t.match(jt)||[];e=a[i++];)r.hasClass(e)?r.removeClass(e):r.addClass(e);else void 0!==t&&"boolean"!==n||(e=G(this),e&&vt._data(this,"__className__",e),vt.attr(this,"class",e||!1===t?"":vt._data(this,"__className__")||""))})},hasClass:function(t){var e,n,i=0;for(e=" "+t+" ";n=this[i++];)if(1===n.nodeType&&(" "+G(n)+" ").replace(Be," ").indexOf(e)>-1)return!0;return!1}}),vt.each("blur focus focusin focusout load resize scroll unload click dblclick mousedown mouseup mousemove mouseover mouseout mouseenter mouseleave change select submit keydown keypress keyup error contextmenu".split(" "),function(t,e){vt.fn[e]=function(t,n){return arguments.length>0?this.on(e,null,t,n):this.trigger(e)}}),vt.fn.extend({hover:function(t,e){return this.mouseenter(t).mouseleave(e||t)}});var $e=n.location,We=vt.now(),ze=/\?/,Xe=/(,)|(\[|{)|(}|])|"(?:[^"\\\r\n]|\\["\\\/bfnrt]|\\u[\da-fA-F]{4})*"\s*:?|true|false|null|-?(?!0\d)\d+(?:\.\d+|)(?:[eE][+-]?\d+|)/g;vt.parseJSON=function(t){if(n.JSON&&n.JSON.parse)return n.JSON.parse(t+"");var e,i=null,r=vt.trim(t+"");return r&&!vt.trim(r.replace(Xe,function(t,n,r,a){return e&&n&&(i=0),0===i?t:(e=r||n,i+=!a-!r,"")}))?Function("return "+r)():vt.error("Invalid JSON: "+t)},vt.parseXML=function(t){var e,i;if(!t||"string"!=typeof t)return null;try{n.DOMParser?(i=new n.DOMParser,e=i.parseFromString(t,"text/xml")):(e=new n.ActiveXObject("Microsoft.XMLDOM"),e.async="false",e.loadXML(t))}catch(t){e=void 0}return e&&e.documentElement&&!e.getElementsByTagName("parsererror").length||vt.error("Invalid XML: "+t),e};var Ue=/#.*$/,Ye=/([?&])_=[^&]*/,Ge=/^(.*?):[ \t]*([^\r\n]*)\r?$/gm,Ve=/^(?:about|app|app-storage|.+-extension|file|res|widget):$/,Je=/^(?:GET|HEAD)$/,Ke=/^\/\//,Qe=/^([\w.+-]+:)(?:\/\/(?:[^\/?#]*@|)([^\/?#:]*)(?::(\d+)|)|)/,Ze={},tn={},en="*/".concat("*"),nn=$e.href,rn=Qe.exec(nn.toLowerCase())||[];vt.extend({active:0,lastModified:{},etag:{},ajaxSettings:{url:nn,type:"GET",isLocal:Ve.test(rn[1]),global:!0,processData:!0,async:!0,contentType:"application/x-www-form-urlencoded; charset=UTF-8",accepts:{"*":en,text:"text/plain",html:"text/html",xml:"application/xml, text/xml",json:"application/json, text/javascript"},contents:{xml:/\bxml\b/,html:/\bhtml/,json:/\bjson\b/},responseFields:{xml:"responseXML",text:"responseText",json:"responseJSON"},converters:{"* text":String,"text html":!0,"text json":vt.parseJSON,"text xml":vt.parseXML},flatOptions:{url:!0,context:!0}},ajaxSetup:function(t,e){return e?K(K(t,vt.ajaxSettings),e):K(vt.ajaxSettings,t)},ajaxPrefilter:V(Ze),ajaxTransport:V(tn),ajax:function(t,e){function i(t,e,i,r){var a,d,y,b,_,w=e;2!==x&&(x=2,l&&n.clearTimeout(l),u=void 0,s=r||"",C.readyState=t>0?4:0,a=t>=200&&t<300||304===t,i&&(b=Q(p,C,i)),b=Z(p,b,C,a),a?(p.ifModified&&(_=C.getResponseHeader("Last-Modified"),_&&(vt.lastModified[o]=_),(_=C.getResponseHeader("etag"))&&(vt.etag[o]=_)),204===t||"HEAD"===p.type?w="nocontent":304===t?w="notmodified":(w=b.state,d=b.data,y=b.error,a=!y)):(y=w,!t&&w||(w="error",t<0&&(t=0))),C.status=t,C.statusText=(e||w)+"",a?m.resolveWith(f,[d,w,C]):m.rejectWith(f,[C,w,y]),C.statusCode(g),g=void 0,c&&h.trigger(a?"ajaxSuccess":"ajaxError",[C,p,a?d:y]),v.fireWith(f,[C,w]),c&&(h.trigger("ajaxComplete",[C,p]),--vt.active||vt.event.trigger("ajaxStop")))}"object"==typeof t&&(e=t,t=void 0),e=e||{};var r,a,o,s,l,c,u,d,p=vt.ajaxSetup({},e),f=p.context||p,h=p.context&&(f.nodeType||f.jquery)?vt(f):vt.event,m=vt.Deferred(),v=vt.Callbacks("once memory"),g=p.statusCode||{},y={},b={},x=0,_="canceled",C={readyState:0,getResponseHeader:function(t){var e;if(2===x){if(!d)for(d={};e=Ge.exec(s);)d[e[1].toLowerCase()]=e[2];e=d[t.toLowerCase()]}return null==e?null:e},getAllResponseHeaders:function(){return 2===x?s:null},setRequestHeader:function(t,e){var n=t.toLowerCase();return x||(t=b[n]=b[n]||t,y[t]=e),this},overrideMimeType:function(t){return x||(p.mimeType=t),this},statusCode:function(t){var e;if(t)if(x<2)for(e in t)g[e]=[g[e],t[e]];else C.always(t[C.status]);return this},abort:function(t){var e=t||_;return u&&u.abort(e),i(0,e),this}};if(m.promise(C).complete=v.add,C.success=C.done,C.error=C.fail,p.url=((t||p.url||nn)+"").replace(Ue,"").replace(Ke,rn[1]+"//"),p.type=e.method||e.type||p.method||p.type,p.dataTypes=vt.trim(p.dataType||"*").toLowerCase().match(jt)||[""],null==p.crossDomain&&(r=Qe.exec(p.url.toLowerCase()),p.crossDomain=!(!r||r[1]===rn[1]&&r[2]===rn[2]&&(r[3]||("http:"===r[1]?"80":"443"))===(rn[3]||("http:"===rn[1]?"80":"443")))),p.data&&p.processData&&"string"!=typeof p.data&&(p.data=vt.param(p.data,p.traditional)),J(Ze,p,e,C),2===x)return C;c=vt.event&&p.global,c&&0==vt.active++&&vt.event.trigger("ajaxStart"),p.type=p.type.toUpperCase(),p.hasContent=!Je.test(p.type),o=p.url,p.hasContent||(p.data&&(o=p.url+=(ze.test(o)?"&":"?")+p.data,delete p.data),!1===p.cache&&(p.url=Ye.test(o)?o.replace(Ye,"$1_="+We++):o+(ze.test(o)?"&":"?")+"_="+We++)),p.ifModified&&(vt.lastModified[o]&&C.setRequestHeader("If-Modified-Since",vt.lastModified[o]),vt.etag[o]&&C.setRequestHeader("If-None-Match",vt.etag[o])),(p.data&&p.hasContent&&!1!==p.contentType||e.contentType)&&C.setRequestHeader("Content-Type",p.contentType),C.setRequestHeader("Accept",p.dataTypes[0]&&p.accepts[p.dataTypes[0]]?p.accepts[p.dataTypes[0]]+("*"!==p.dataTypes[0]?", "+en+"; q=0.01":""):p.accepts["*"]);for(a in p.headers)C.setRequestHeader(a,p.headers[a]);if(p.beforeSend&&(!1===p.beforeSend.call(f,C,p)||2===x))return C.abort();_="abort";for(a in{success:1,error:1,complete:1})C[a](p[a]);if(u=J(tn,p,e,C)){if(C.readyState=1,c&&h.trigger("ajaxSend",[C,p]),2===x)return C;p.async&&p.timeout>0&&(l=n.setTimeout(function(){C.abort("timeout")},p.timeout));try{x=1,u.send(y,i)}catch(t){if(!(x<2))throw t;i(-1,t)}}else i(-1,"No Transport");return C},getJSON:function(t,e,n){return vt.get(t,e,n,"json")},getScript:function(t,e){return vt.get(t,void 0,e,"script")}}),vt.each(["get","post"],function(t,e){vt[e]=function(t,n,i,r){return vt.isFunction(n)&&(r=r||i,i=n,n=void 0),vt.ajax(vt.extend({url:t,type:e,dataType:r,data:n,success:i},vt.isPlainObject(t)&&t))}}),vt._evalUrl=function(t){return vt.ajax({url:t,type:"GET",dataType:"script",cache:!0,async:!1,global:!1,throws:!0})},vt.fn.extend({wrapAll:function(t){if(vt.isFunction(t))return this.each(function(e){vt(this).wrapAll(t.call(this,e))});if(this[0]){var e=vt(t,this[0].ownerDocument).eq(0).clone(!0);this[0].parentNode&&e.insertBefore(this[0]),e.map(function(){for(var t=this;t.firstChild&&1===t.firstChild.nodeType;)t=t.firstChild;return t}).append(this)}return this},wrapInner:function(t){return vt.isFunction(t)?this.each(function(e){vt(this).wrapInner(t.call(this,e))}):this.each(function(){var e=vt(this),n=e.contents();n.length?n.wrapAll(t):e.append(t)})},wrap:function(t){var e=vt.isFunction(t);return this.each(function(n){vt(this).wrapAll(e?t.call(this,n):t)})},unwrap:function(){return this.parent().each(function(){vt.nodeName(this,"body")||vt(this).replaceWith(this.childNodes)}).end()}}),vt.expr.filters.hidden=function(t){return mt.reliableHiddenOffsets()?t.offsetWidth<=0&&t.offsetHeight<=0&&!t.getClientRects().length:et(t)},vt.expr.filters.visible=function(t){return!vt.expr.filters.hidden(t)};var an=/%20/g,on=/\[\]$/,sn=/\r?\n/g,ln=/^(?:submit|button|image|reset|file)$/i,cn=/^(?:input|select|textarea|keygen)/i;vt.param=function(t,e){var n,i=[],r=function(t,e){e=vt.isFunction(e)?e():null==e?"":e,i[i.length]=encodeURIComponent(t)+"="+encodeURIComponent(e)};if(void 0===e&&(e=vt.ajaxSettings&&vt.ajaxSettings.traditional),vt.isArray(t)||t.jquery&&!vt.isPlainObject(t))vt.each(t,function(){r(this.name,this.value)});else for(n in t)nt(n,t[n],e,r);return i.join("&").replace(an,"+")},vt.fn.extend({serialize:function(){return vt.param(this.serializeArray())},serializeArray:function(){return this.map(function(){var t=vt.prop(this,"elements");return t?vt.makeArray(t):this}).filter(function(){var t=this.type;return this.name&&!vt(this).is(":disabled")&&cn.test(this.nodeName)&&!ln.test(t)&&(this.checked||!$t.test(t))}).map(function(t,e){var n=vt(this).val();return null==n?null:vt.isArray(n)?vt.map(n,function(t){return{name:e.name,value:t.replace(sn,"\r\n")}}):{name:e.name,value:n.replace(sn,"\r\n")}}).get()}}),vt.ajaxSettings.xhr=void 0!==n.ActiveXObject?function(){return this.isLocal?rt():st.documentMode>8?it():/^(get|post|head|put|delete|options)$/i.test(this.type)&&it()||rt()}:it;var un=0,dn={},pn=vt.ajaxSettings.xhr();n.attachEvent&&n.attachEvent("onunload",function(){for(var t in dn)dn[t](void 0,!0)}),mt.cors=!!pn&&"withCredentials"in pn,pn=mt.ajax=!!pn,pn&&vt.ajaxTransport(function(t){if(!t.crossDomain||mt.cors){var e;return{send:function(i,r){var a,o=t.xhr(),s=++un;if(o.open(t.type,t.url,t.async,t.username,t.password),t.xhrFields)for(a in t.xhrFields)o[a]=t.xhrFields[a];t.mimeType&&o.overrideMimeType&&o.overrideMimeType(t.mimeType),t.crossDomain||i["X-Requested-With"]||(i["X-Requested-With"]="XMLHttpRequest");for(a in i)void 0!==i[a]&&o.setRequestHeader(a,i[a]+"");o.send(t.hasContent&&t.data||null),e=function(n,i){var a,l,c;if(e&&(i||4===o.readyState))if(delete dn[s],e=void 0,o.onreadystatechange=vt.noop,i)4!==o.readyState&&o.abort();else{c={},a=o.status,"string"==typeof o.responseText&&(c.text=o.responseText);try{l=o.statusText}catch(t){l=""}a||!t.isLocal||t.crossDomain?1223===a&&(a=204):a=c.text?200:404}c&&r(a,l,c,o.getAllResponseHeaders())},t.async?4===o.readyState?n.setTimeout(e):o.onreadystatechange=dn[s]=e:e()},abort:function(){e&&e(void 0,!0)}}}}),vt.ajaxSetup({accepts:{script:"text/javascript, application/javascript, application/ecmascript, application/x-ecmascript"},contents:{script:/\b(?:java|ecma)script\b/},converters:{"text script":function(t){return vt.globalEval(t),t}}}),vt.ajaxPrefilter("script",function(t){void 0===t.cache&&(t.cache=!1),t.crossDomain&&(t.type="GET",t.global=!1)}),vt.ajaxTransport("script",function(t){if(t.crossDomain){var e,n=st.head||vt("head")[0]||st.documentElement;return{send:function(i,r){e=st.createElement("script"),e.async=!0,t.scriptCharset&&(e.charset=t.scriptCharset),e.src=t.url,e.onload=e.onreadystatechange=function(t,n){(n||!e.readyState||/loaded|complete/.test(e.readyState))&&(e.onload=e.onreadystatechange=null,e.parentNode&&e.parentNode.removeChild(e),e=null,n||r(200,"success"))},n.insertBefore(e,n.firstChild)},abort:function(){e&&e.onload(void 0,!0)}}}});var fn=[],hn=/(=)\?(?=&|$)|\?\?/;vt.ajaxSetup({jsonp:"callback",jsonpCallback:function(){var t=fn.pop()||vt.expando+"_"+We++;return this[t]=!0,t}}),vt.ajaxPrefilter("json jsonp",function(t,e,i){var r,a,o,s=!1!==t.jsonp&&(hn.test(t.url)?"url":"string"==typeof t.data&&0===(t.contentType||"").indexOf("application/x-www-form-urlencoded")&&hn.test(t.data)&&"data");if(s||"jsonp"===t.dataTypes[0])return r=t.jsonpCallback=vt.isFunction(t.jsonpCallback)?t.jsonpCallback():t.jsonpCallback,s?t[s]=t[s].replace(hn,"$1"+r):!1!==t.jsonp&&(t.url+=(ze.test(t.url)?"&":"?")+t.jsonp+"="+r),t.converters["script json"]=function(){return o||vt.error(r+" was not called"),o[0]},t.dataTypes[0]="json",a=n[r],n[r]=function(){o=arguments},i.always(function(){void 0===a?vt(n).removeProp(r):n[r]=a,t[r]&&(t.jsonpCallback=e.jsonpCallback,fn.push(r)),o&&vt.isFunction(a)&&a(o[0]),o=a=void 0}),"script"}),vt.parseHTML=function(t,e,n){if(!t||"string"!=typeof t)return null;"boolean"==typeof e&&(n=e,e=!1),e=e||st;var i=Tt.exec(t),r=!n&&[];return i?[e.createElement(i[1])]:(i=_([t],e,r),r&&r.length&&vt(r).remove(),vt.merge([],i.childNodes))};var mn=vt.fn.load;vt.fn.load=function(t,e,n){if("string"!=typeof t&&mn)return mn.apply(this,arguments);var i,r,a,o=this,s=t.indexOf(" ");return s>-1&&(i=vt.trim(t.slice(s,t.length)),t=t.slice(0,s)),vt.isFunction(e)?(n=e,e=void 0):e&&"object"==typeof e&&(r="POST"),o.length>0&&vt.ajax({url:t,type:r||"GET",dataType:"html",data:e}).done(function(t){a=arguments,o.html(i?vt("<div>").append(vt.parseHTML(t)).find(i):t)}).always(n&&function(t,e){o.each(function(){n.apply(this,a||[t.responseText,e,t])})}),this},vt.each(["ajaxStart","ajaxStop","ajaxComplete","ajaxError","ajaxSuccess","ajaxSend"],function(t,e){vt.fn[e]=function(t){return this.on(e,t)}}),vt.expr.filters.animated=function(t){return vt.grep(vt.timers,function(e){return t===e.elem}).length},vt.offset={setOffset:function(t,e,n){var i,r,a,o,s,l,c,u=vt.css(t,"position"),d=vt(t),p={};"static"===u&&(t.style.position="relative"),s=d.offset(),a=vt.css(t,"top"),l=vt.css(t,"left"),c=("absolute"===u||"fixed"===u)&&vt.inArray("auto",[a,l])>-1,c?(i=d.position(),o=i.top,r=i.left):(o=parseFloat(a)||0,r=parseFloat(l)||0),vt.isFunction(e)&&(e=e.call(t,n,vt.extend({},s))),null!=e.top&&(p.top=e.top-s.top+o),null!=e.left&&(p.left=e.left-s.left+r),"using"in e?e.using.call(t,p):d.css(p)}},vt.fn.extend({offset:function(t){if(arguments.length)return void 0===t?this:this.each(function(e){vt.offset.setOffset(this,t,e)});var e,n,i={top:0,left:0},r=this[0],a=r&&r.ownerDocument;if(a)return e=a.documentElement,vt.contains(e,r)?(void 0!==r.getBoundingClientRect&&(i=r.getBoundingClientRect()),n=at(a),{top:i.top+(n.pageYOffset||e.scrollTop)-(e.clientTop||0),left:i.left+(n.pageXOffset||e.scrollLeft)-(e.clientLeft||0)}):i},position:function(){if(this[0]){var t,e,n={top:0,left:0},i=this[0];return"fixed"===vt.css(i,"position")?e=i.getBoundingClientRect():(t=this.offsetParent(),e=this.offset(),vt.nodeName(t[0],"html")||(n=t.offset()),n.top+=vt.css(t[0],"borderTopWidth",!0),n.left+=vt.css(t[0],"borderLeftWidth",!0)),{top:e.top-n.top-vt.css(i,"marginTop",!0),left:e.left-n.left-vt.css(i,"marginLeft",!0)}}},offsetParent:function(){return this.map(function(){for(var t=this.offsetParent;t&&!vt.nodeName(t,"html")&&"static"===vt.css(t,"position");)t=t.offsetParent;return t||me})}}),vt.each({scrollLeft:"pageXOffset",scrollTop:"pageYOffset"},function(t,e){var n=/Y/.test(e);vt.fn[t]=function(i){return Bt(this,function(t,i,r){var a=at(t);if(void 0===r)return a?e in a?a[e]:a.document.documentElement[i]:t[i];a?a.scrollTo(n?vt(a).scrollLeft():r,n?r:vt(a).scrollTop()):t[i]=r},t,i,arguments.length,null)}}),vt.each(["top","left"],function(t,e){vt.cssHooks[e]=q(mt.pixelPosition,function(t,n){if(n)return n=ge(t,e),fe.test(n)?vt(t).position()[e]+"px":n})}),vt.each({Height:"height",Width:"width"},function(t,e){vt.each({padding:"inner"+t,content:e,"":"outer"+t},function(n,i){vt.fn[i]=function(i,r){var a=arguments.length&&(n||"boolean"!=typeof i),o=n||(!0===i||!0===r?"margin":"border");return Bt(this,function(e,n,i){var r;return vt.isWindow(e)?e.document.documentElement["client"+t]:9===e.nodeType?(r=e.documentElement,Math.max(e.body["scroll"+t],r["scroll"+t],e.body["offset"+t],r["offset"+t],r["client"+t])):void 0===i?vt.css(e,n,o):vt.style(e,n,i,o)},e,a?i:void 0,a,null)}})}),vt.fn.extend({bind:function(t,e,n){return this.on(t,null,e,n)},unbind:function(t,e){return this.off(t,null,e)},delegate:function(t,e,n,i){return this.on(e,t,n,i)},undelegate:function(t,e,n){return 1===arguments.length?this.off(t,"**"):this.off(e,t||"**",n)}}),vt.fn.size=function(){return this.length},vt.fn.andSelf=vt.fn.addBack,i=[],void 0!==(r=function(){return vt}.apply(e,i))&&(t.exports=r);var vn=n.jQuery,gn=n.$;return vt.noConflict=function(t){return n.$===vt&&(n.$=gn),t&&n.jQuery===vt&&(n.jQuery=vn),vt},a||(n.jQuery=n.$=vt),vt})},746:function(t,e){t.exports={render:function(){var t=this,e=t.$createElement,n=t._self._c||e;return n("div",{staticClass:"page-panel wechat-payment-container"},[t._m(0),t._v(" "),t._m(1),t._v(" "),t._m(2),t._v(" "),t._m(3),t._v(" "),t._m(4),t._v(" "),t._m(5),t._v(" "),t._m(6),t._v(" "),t._m(7),t._v(" "),t._m(8),t._v(" "),t._m(9),t._v(" "),t._m(10),t._v(" "),t._m(11),t._v(" "),n("div",{staticClass:"container-wrp"},[n("div",{staticClass:"container wrap"},[t._m(12),t._v(" "),n("div",{staticClass:"content",attrs:{id:"panel-body"}},[n("div",{staticClass:"cms-area",attrs:{id:"cmshint_51004"}}),t._v(" "),n("div",{attrs:{id:"app"}},[t._m(13),t._v(" "),n("div",{staticClass:"batch-detail-info",attrs:{"data-v-ed128e8c":""}},[t._m(14),t._v(" "),n("div",{staticClass:"content-bd",attrs:{"data-v-ed128e8c":""}},[n("div",{staticClass:"panel-box with-padding",attrs:{"data-v-ed128e8c":""}},[n("form",{staticClass:"el-form form-items",attrs:{"data-v-ed128e8c":""}},[n("div",{staticClass:"el-form-item el-form-item--mini",attrs:{"data-v-ed128e8c":""}},[n("label",{staticClass:"el-form-item__label",staticStyle:{width:"120px"}},[t._v("转账状态")]),t._v(" "),n("div",{staticClass:"el-form-item__content",staticStyle:{"margin-left":"120px"}},[n("span",{staticClass:"black",attrs:{"data-v-ed128e8c":""}},[t._v(t._s(t.orderData.transferStatus))])])]),t._v(" "),n("div",{staticClass:"el-form-item el-form-item--mini",attrs:{"data-v-ed128e8c":""}},[n("label",{staticClass:"el-form-item__label",staticStyle:{width:"120px"}},[t._v("转账金额(元)")]),t._v(" "),n("div",{staticClass:"el-form-item__content",staticStyle:{"margin-left":"120px"}},[n("strong",{attrs:{"data-v-ed128e8c":""}},[t._v(t._s(t.orderData.transferAmount))])])]),t._v(" "),n("div",{staticClass:"el-form-item el-form-item--mini",attrs:{"data-v-ed128e8c":""}},[n("label",{staticClass:"el-form-item__label",staticStyle:{width:"120px"}},[t._v("转账备注")]),t._v(" "),n("div",{staticClass:"el-form-item__content",staticStyle:{"margin-left":"120px"}},[t._v("\n                        "+t._s(t.orderData.transferRemark)+"\n                      ")])])]),t._v(" "),n("form",{staticClass:"el-form form-items",attrs:{"data-v-ed128e8c":""}},[n("div",{staticClass:"el-form-item el-form-item--mini",attrs:{"data-v-ed128e8c":""}},[n("label",{staticClass:"el-form-item__label",staticStyle:{width:"120px"}},[t._v("微信明细单号")]),t._v(" "),n("div",{staticClass:"el-form-item__content",staticStyle:{"margin-left":"120px"}},[t._v("\n                        "+t._s(t.orderData.wechatDetailOrderNo)+"\n                      ")])]),t._v(" "),n("div",{staticClass:"el-form-item el-form-item--mini",attrs:{"data-v-ed128e8c":""}},[n("label",{staticClass:"el-form-item__label",staticStyle:{width:"120px"}},[t._v("商家明细单号")]),t._v(" "),n("div",{staticClass:"el-form-item__content",staticStyle:{"margin-left":"120px"}},[t._v("\n                        "+t._s(t.orderData.merchantDetailOrderNo)+"\n                      ")])]),t._v(" "),n("div",{staticClass:"el-form-item el-form-item--mini",attrs:{"data-v-ed128e8c":""}},[n("label",{staticClass:"el-form-item__label",staticStyle:{width:"120px"}},[t._v("收款用户openId")]),t._v(" "),n("div",{staticClass:"el-form-item__content",staticStyle:{"margin-left":"120px"}},[t._v("\n                        "+t._s(t.orderData.receiverOpenId)+"\n                      ")])]),t._v(" "),n("div",{staticClass:"el-form-item el-form-item--mini",attrs:{"data-v-ed128e8c":""}},[n("label",{staticClass:"el-form-item__label",staticStyle:{width:"120px"}},[t._v("收款用户姓名")]),t._v(" "),n("div",{staticClass:"el-form-item__content",staticStyle:{"margin-left":"120px"}},[t._v("\n                        "+t._s(t.orderData.receiverName||"")+"\n                      ")])]),t._v(" "),n("div",{staticClass:"el-form-item el-form-item--mini",attrs:{"data-v-ed128e8c":""}},[n("label",{staticClass:"el-form-item__label",staticStyle:{width:"120px"}},[t._v("收款用户身份证")]),t._v(" "),n("div",{staticClass:"el-form-item__content",staticStyle:{"margin-left":"120px"}},[t._v("\n                        "+t._s(t.orderData.receiverIdCard||"-")+"\n                      ")])])]),t._v(" "),n("form",{staticClass:"el-form",attrs:{"data-v-ed128e8c":""}},[n("div",{staticClass:"el-form-item el-form-item--mini",attrs:{"data-v-ed128e8c":""}},[n("label",{staticClass:"el-form-item__label",staticStyle:{width:"120px"}},[t._v("AppID")]),t._v(" "),n("div",{staticClass:"el-form-item__content",staticStyle:{"margin-left":"120px"}},[t._v("\n                        "+t._s(t.orderData.appId)+"\n                      ")])]),t._v(" "),n("div",{staticClass:"el-form-item el-form-item--mini",attrs:{"data-v-ed128e8c":""}},[n("label",{staticClass:"el-form-item__label",staticStyle:{width:"120px"}},[t._v("APP名称")]),t._v(" "),n("div",{staticClass:"el-form-item__content",staticStyle:{"margin-left":"120px"}},[t._v("\n                        "+t._s(t.orderData.appName)+"\n                      ")])]),t._v(" "),n("div",{staticClass:"el-form-item el-form-item--mini",attrs:{"data-v-ed128e8c":""}},[n("label",{staticClass:"el-form-item__label",staticStyle:{width:"120px"}},[t._v("执行转账人员")]),t._v(" "),n("div",{staticClass:"el-form-item__content",staticStyle:{"margin-left":"120px"}},[t._v("\n                        "+t._s(t.orderData.executeUser)+"\n                      ")])]),t._v(" "),n("div",{staticClass:"el-form-item el-form-item--mini",attrs:{"data-v-ed128e8c":""}},[n("label",{staticClass:"el-form-item__label",staticStyle:{width:"120px"}},[t._v("创建时间")]),t._v(" "),n("div",{staticClass:"el-form-item__content",staticStyle:{"margin-left":"120px"}},[t._v("\n                        "+t._s(t.orderData.createTime)+"\n                      ")])]),t._v(" "),n("div",{staticClass:"el-form-item el-form-item--mini",attrs:{"data-v-ed128e8c":""}},[n("label",{staticClass:"el-form-item__label",staticStyle:{width:"120px"}},[t._v("更新时间")]),t._v(" "),n("div",{staticClass:"el-form-item__content",staticStyle:{"margin-left":"120px"}},[t._v("\n                        "+t._s(t.orderData.updateTime)+"\n                      ")])])]),t._v(" "),n("form",{staticClass:"el-form",attrs:{"data-v-ed128e8c":""}},[n("div",{staticClass:"el-form-item el-form-item--button el-form-item--large",attrs:{"data-v-ed128e8c":""}},[n("div",{staticClass:"el-form-item__content",staticStyle:{"margin-left":"130px"}},[n("button",{staticClass:"el-button el-button--primary el-button--large",attrs:{"data-v-ed128e8c":"",type:"button",disabled:t.loading},on:{click:t.applyReceipt}},[n("span",[t._v(t._s(t.loading?"申请中...":"申请明细回单"))])])])])])]),t._v(" "),n("div",{directives:[{name:"show",rawName:"v-show",value:t.loading,expression:"loading"}],staticClass:"el-loading-mask"},[n("div",{staticClass:"el-loading-spinner"},[n("svg",{staticClass:"circular",attrs:{viewBox:"25 25 50 50"}},[n("circle",{staticClass:"path",attrs:{cx:"50",cy:"50",r:"20",fill:"none"}})])])])])])])])]),t._v(" "),n("input",{attrs:{id:"seed",type:"hidden",name:"time_seed",value:""}}),t._v(" "),n("input",{attrs:{type:"hidden",id:"token",name:"ecc_csrf_token",value:"434bfbf7523d39ffd45ec1ffff0fc5a7"}})]),t._v(" "),t._m(15)])},staticRenderFns:[function(){var t=this,e=t.$createElement,n=t._self._c||e;return n("div",{staticClass:"topbar",attrs:{id:"gotop"}},[n("div",{staticClass:"wrap"},[n("ul",[n("li",[n("a",{attrs:{href:"/index.php/extend/cms/inner_message_list"}},[n("i",{staticClass:"ico-news"}),t._v("消息中心"),n("span",{staticClass:"num-feed hide"},[t._v("99")])])]),t._v(" "),n("li",[n("a",{attrs:{href:"/index.php/partner/public/search",target:"_blank"}},[n("i",{staticClass:"ico-search"}),t._v("合作伙伴搜索")])]),t._v(" "),n("li",[n("a",{attrs:{href:"http://kf.qq.com/product/wechatpaymentmerchant.html",target:"_blank"}},[n("i",{staticClass:"ico-help"}),t._v("帮助中心")])]),t._v(" "),n("li",[n("div",{staticClass:"profile-box"},[n("a",{staticClass:"profile-hd",attrs:{href:"javascript:;"}},[n("i",{staticClass:"ico-docu"}),t._v("我的账号"),n("i",{staticClass:"arrow"})]),t._v(" "),n("div",{staticClass:"profile-bd"},[n("ul",[n("li",[n("a",{staticClass:"username",attrs:{href:"/index.php/extend/employee",id:"username"}},[t._v("piaoshenzhe@**********")])]),t._v(" "),n("li",[n("a",{staticClass:"switch",attrs:{href:"/index.php/core/account/switchMchAccount"}},[t._v("切换账号")])]),t._v(" "),n("li",[n("a",{staticClass:"quit",attrs:{href:"/index.php/core/home/<USER>"}},[t._v("退出登录")])])])])])])])])])},function(){var t=this,e=t.$createElement,n=t._self._c||e;return n("div",{staticClass:"header"},[n("div",{staticClass:"wrap"},[n("div",{staticClass:"logo"},[n("h1",{staticClass:"main-logo"},[n("a",{attrs:{href:"/index.php/core/home"}},[t._v("微信支付商户平台")])]),t._v(" "),n("div",{staticClass:"sub-logo"})]),t._v(" "),n("div",{staticClass:"nav"},[n("ul",[n("li",[n("a",{attrs:{href:"/index.php/core/home/<USER>"}},[t._v("首页")])]),t._v(" "),n("li",{staticClass:"selected"},[n("a",{attrs:{href:"/index.php/core/home/<USER>"}},[t._v("交易中心")])]),t._v(" "),n("li",[n("a",{attrs:{href:"/index.php/core/home/<USER>"}},[t._v("账户中心")])]),t._v(" "),n("li",[n("a",{attrs:{href:"/index.php/core/home/<USER>"}},[t._v("营销中心")])]),t._v(" "),n("li",[n("a",{attrs:{href:"/index.php/core/home/<USER>"}},[t._v("产品中心")])]),t._v(" "),n("li",[n("a",{attrs:{href:"/index.php/core/home/<USER>"}},[t._v("数据中心")])])])])])])},function(){var t=this,e=t.$createElement,n=t._self._c||e;return n("div",{staticClass:"dialog hide",attrs:{id:"NoAuthDG"}},[n("div",{staticClass:"dialog-hd"},[n("h3",[t._v("温馨提示")]),t._v(" "),n("a",{staticClass:"ico-cls close-dialog JSCloseDG",attrs:{href:"javascript:;"}},[t._v("关闭")])]),t._v(" "),n("div",{staticClass:"dialog-bd"},[n("div",{staticClass:"page-msg align-center"},[n("div",{staticClass:"inner"},[n("div",{staticClass:"msg-ico"},[n("i",{staticClass:"ico-msg warn"})]),t._v(" "),n("div",{staticClass:"msg-cnt"},[n("h4",[t._v("你没有操作此功能的权限，请联系本商户员工管理员修改权限")]),t._v(" "),n("p",[t._v("当前角色："),n("span",{attrs:{id:"idRoleName"}}),n("br"),t._v("所需权限："),n("span",{attrs:{id:"idAuthName"}})])])])])]),t._v(" "),n("div",{staticClass:"dialog-ft"},[n("a",{staticClass:"btn btn-primary close-dialog JSCloseDG",attrs:{href:"javascript:;"}},[t._v("确认")])])])},function(){var t=this,e=t.$createElement,n=t._self._c||e;return n("div",{staticClass:"dialog hide",attrs:{id:"ErrDG"}},[n("div",{staticClass:"dialog-hd"},[n("h3",[t._v("温馨提示")]),t._v(" "),n("a",{staticClass:"ico-cls close-dialog JSCloseDG",attrs:{href:"javascript:;"}},[t._v("关闭")])]),t._v(" "),n("div",{staticClass:"dialog-bd"},[n("div",{staticClass:"page-msg align-center align-middle"},[n("div",{staticClass:"inner"},[n("div",{staticClass:"msg-ico"},[n("i",{staticClass:"ico-msg warn"})]),t._v(" "),n("div",{staticClass:"msg-cnt"},[n("h4",{attrs:{id:"JSErrDGText"}})])])])]),t._v(" "),n("div",{staticClass:"dialog-ft"},[n("a",{staticClass:"btn btn-primary close-dialog JSCloseDG",attrs:{href:"javascript:;"}},[t._v("确认")])])])},function(){var t=this,e=t.$createElement,n=t._self._c||e;return n("div",{staticClass:"dialog hide",attrs:{id:"RiskInfoDg"}},[n("div",{staticClass:"dialog-hd"},[n("h3",[t._v("温馨提示")]),t._v(" "),n("a",{staticClass:"ico-cls close-dialog jsCloseInfoDg",attrs:{href:"javascript:;"}},[t._v("关闭")])]),t._v(" "),n("div",{staticClass:"dialog-bd"},[n("div",{staticClass:"page-msg align-center align-middle"},[n("div",{staticClass:"inner"},[n("div",{staticClass:"msg-ico"},[n("i",{staticClass:"ico-msg succ"})]),t._v(" "),n("div",{staticClass:"msg-cnt"},[n("h4",{attrs:{id:"headerInfoDgText"}},[t._v("操作成功")])])])])])])},function(){var t=this,e=t.$createElement,n=t._self._c||e;return n("div",{staticClass:"dialog hide",attrs:{id:"ErrDg1"}},[n("div",{staticClass:"dialog-hd"},[n("h3",[t._v("温馨提示")]),t._v(" "),n("a",{staticClass:"ico-cls close-dialog jsCloseErrDg1",attrs:{href:"javascript:;"}},[t._v("关闭")])]),t._v(" "),n("div",{staticClass:"dialog-bd"},[n("div",{staticClass:"page-msg align-center align-middle"},[n("div",{staticClass:"inner"},[n("div",{staticClass:"msg-ico"},[n("i",{staticClass:"ico-msg warn",attrs:{id:"ID_ERR_DG1_ICON"}})]),t._v(" "),n("div",{staticClass:"msg-cnt"},[n("h4",{staticClass:"jsErrorText"},[t._v("系统繁忙，请稍后重试")])])])])])])},function(){var t=this,e=t.$createElement,n=t._self._c||e;return n("div",{staticClass:"dialog hide",attrs:{id:"NoAuthDg"}},[n("div",{staticClass:"dialog-hd"},[n("h3",[t._v("温馨提示")]),t._v(" "),n("a",{staticClass:"ico-cls jsCloseNoAuthDg close-dialog",attrs:{href:"javascript:;"}},[t._v("关闭")])]),t._v(" "),n("div",{staticClass:"dialog-bd"},[n("div",{staticClass:"page-msg align-center"},[n("div",{staticClass:"inner"},[n("div",{staticClass:"msg-ico"},[n("i",{staticClass:"ico-msg warn"})]),t._v(" "),n("div",{staticClass:"msg-cnt"},[n("h4",[t._v("你没有此页面的查看操作权限，如有业务需要，请联系你的内部管理员协助开通。")]),t._v(" "),n("p",{staticClass:"jsNoAuthDgP"},[t._v("账户设置 - 密码安全 - API安全")])])])])]),t._v(" "),n("div",{staticClass:"dialog-ft"},[n("a",{staticClass:"btn btn-primary",attrs:{href:"/index.php/core/account"}},[t._v("进入账户概况")])])])},function(){var t=this,e=t.$createElement,n=t._self._c||e;return n("div",{staticClass:"dialog hide",attrs:{id:"RiskCtrlDialog"}},[n("div",{staticClass:"dialog-hd"},[n("h3",[t._v("安全验证")]),t._v(" "),n("a",{staticClass:"ico-cls close-dialog",attrs:{id:"closeRiskCtrlDialog",href:"javascript:;"}},[t._v("关闭")])]),t._v(" "),n("div",{staticClass:"dialog-bd"},[n("div",{attrs:{id:"RiskCtrlDialogSecFactor"}})])])},function(){var t=this,e=t.$createElement,n=t._self._c||e;return n("div",{staticClass:"dialog hide",attrs:{id:"NotAdminDg"}},[n("div",{staticClass:"dialog-hd"},[n("h3",[t._v("温馨提示")]),t._v(" "),n("a",{staticClass:"ico-cls jsCloseNotAdminDg close-dialog",attrs:{href:"javascript:;"}},[t._v("关闭")])]),t._v(" "),n("div",{staticClass:"dialog-bd"},[n("div",{staticClass:"page-msg align-center align-middle"},[n("div",{staticClass:"inner"},[n("div",{staticClass:"msg-ico"},[n("i",{staticClass:"ico-msg warn"})]),t._v(" "),n("div",{staticClass:"msg-cnt"},[n("h4",[t._v("你不是超级管理员，请用超级管理员帐号登录执行此操作。")])])])])]),t._v(" "),n("div",{staticClass:"dialog-ft"},[n("a",{staticClass:"btn btn-primary jsCloseNotAdminDg",attrs:{href:"javascript:;"}},[t._v("确定")])])])},function(){var t=this,e=t.$createElement,n=t._self._c||e;return n("div",{staticClass:"dialog hide",attrs:{id:"MchProtocolDG"}},[n("div",{staticClass:"dialog-hd"},[n("h3",[t._v("使用协议")])]),t._v(" "),n("div",{staticClass:"dialog-bd"},[n("div",{staticClass:"page-msg align-center"},[n("div",{staticClass:"inner"},[n("div",{staticClass:"msg-ico"},[n("i",{staticClass:"ico-msg info"})]),t._v(" "),n("div",{staticClass:"msg-cnt"},[n("h4",[t._v("欢迎使用微信支付商户平台，为了保障你的权益，请先仔细阅读《微信支付商户平台使用协议》")]),t._v(" "),n("p",[n("label",{staticClass:"cbx-label nm selected",attrs:{for:""}},[n("i",{staticClass:"ico-cbx",attrs:{id:"MchProtocolCbx"}}),t._v("我已阅读"),n("a",{attrs:{href:"/index.php/core/home/<USER>",target:"_blank"}},[t._v("《微信支付商户平台使用协议》")])])])])])])]),t._v(" "),n("div",{staticClass:"dialog-ft"},[n("a",{staticClass:"btn btn-primary",attrs:{href:"javascript:;",id:"MchProtocolBn"}},[t._v("进入商户平台")])])])},function(){var t=this,e=t.$createElement,n=t._self._c||e;return n("div",{staticClass:"dialog-wrp hide",attrs:{id:"SuppProtocolDG"}},[n("div",{staticClass:"dialog"},[n("div",{staticClass:"dialog-hd"},[n("h3",{staticClass:"tl"},[t._v("温馨提示")])]),t._v(" "),n("div",{staticClass:"dialog-bd"},[n("div",{staticClass:"page-msg align-center"},[n("div",{staticClass:"inner",staticStyle:{"text-indent":"28px"}},[n("h4",[t._v("为了符合监管要求，我司将对金融机构商户号的部分功能进行调整，将商户号调整为在途资金账户。在途资金账户，亦称内部过渡账户、内部记账账户、中间账户等，不属于支付账户，您不能凭以发起支付指令。")]),t._v(" "),n("p",[t._v("关于以上变更的详细调整方案详见"),n("a",{attrs:{href:"/index.php/core/merchant/show_supplemental_protocol",target:"_blank"}},[t._v("《支付服务补充协议》")]),t._v("，您需要完成签约该协议才能继续使用商户平台的其他功能。")])])])]),t._v(" "),n("div",{staticClass:"dialog-ft"},[n("a",{staticClass:"btn btn-primary",attrs:{href:"javascript:;",id:"SuppProtocolBn"}},[t._v("确定签约")])])]),t._v(" "),n("div",{staticClass:"mask-layer"})])},function(){var t=this,e=t.$createElement,n=t._self._c||e;return n("div",{staticClass:"dialog-wrp hide",attrs:{id:"FinishProtocolDG"}},[n("div",{staticClass:"dialog"},[n("div",{staticClass:"dialog-hd"},[n("h3",{staticClass:"tl"},[t._v("温馨提示")])]),t._v(" "),n("div",{staticClass:"dialog-bd"},[n("div",{staticClass:"page-msg large icon-center"},[n("div",{staticClass:"inner"},[n("div",{staticClass:"msg-ico"},[n("i",{staticClass:"ico-msg-l succ"})]),t._v(" "),n("div",{staticClass:"msg-cnt"},[n("h4",[t._v("协议签约完成！")])])])])]),t._v(" "),n("div",{staticClass:"dialog-ft"},[n("a",{staticClass:"btn btn-primary",attrs:{href:"javascript:;",id:"FinishProtocolBn"}},[t._v("完成")])])]),t._v(" "),n("div",{staticClass:"mask-layer"})])},function(){var t=this,e=t.$createElement,n=t._self._c||e;return n("div",{staticClass:"sidebar"},[n("div",{staticClass:"menu"},[n("dl",[n("dt",[n("i",{staticClass:"type-ico ico-trade"}),t._v("交易管理")]),t._v(" "),n("dd",{},[n("a",{attrs:{href:"/index.php/core/trade/search_new"}},[t._v("交易订单")])]),t._v(" "),n("dd",{},[n("a",{attrs:{href:"/index.php/core/refundquery"}},[t._v("退款管理")])])]),t._v(" "),n("dl",[n("dt",[n("i",{staticClass:"type-ico ico-settle"}),t._v("结算管理")]),t._v(" "),n("dd",{},[n("a",{attrs:{href:"/index.php/xphp/cquery_new_settlement_info/query_settlement_info_page"}},[t._v("已结算查询")])])]),t._v(" "),n("dl",[n("dt",[n("i",{staticClass:"type-ico ico-bill"}),t._v("账单管理")]),t._v(" "),n("dd",{},[n("a",{attrs:{href:"/index.php/extend/bills#/"}},[t._v("交易账单")])]),t._v(" "),n("dd",{},[n("a",{attrs:{href:"/index.php/xphp/cfund_bill_nc/funds_bill_nc"}},[t._v("资金账单")])])]),t._v(" "),n("dl",[n("dt",[n("i",{staticClass:"type-ico ico-fund"}),t._v("资金管理")]),t._v(" "),n("dd",{},[n("a",{attrs:{href:"/index.php/core/mktfundin"}},[t._v("充值/转入")])]),t._v(" "),n("dd",{},[n("a",{attrs:{href:"/index.php/core/mktfundout"}},[t._v("提现/转出")])]),t._v(" "),n("dd",{},[n("a",{attrs:{href:"/index.php/extend/mktfundrecord"}},[t._v("转入/转出记录")])]),t._v(" "),n("dd",{},[n("a",{attrs:{href:"/index.php/xphp/cmktfundflow/capital_flow"}},[t._v("资金流水")])])]),t._v(" "),n("dl",[n("dt",[n("i",{staticClass:"type-ico ico-senior"}),t._v("高级业务")]),t._v(" "),n("dd",{staticClass:"selected"},[n("a",{attrs:{href:"/xdc/mchtranstemplate/index.php/xphp/cgi/page/batch"}},[t._v("商家转账到零钱")])])]),t._v(" "),n("dl",[n("dt",[n("i",{staticClass:"type-ico ico-voucher"}),t._v("凭证管理")]),t._v(" "),n("dd",{},[n("a",{attrs:{href:"/index.php/xphp/ccertificate_down/electronic_voucher"}},[t._v("电子凭证")])])])]),t._v(" "),n("input",{attrs:{type:"hidden",name:"ecc_csrf_token",value:"434bfbf7523d39ffd45ec1ffff0fc5a7",id:"CsrfInput"}})])},function(){var t=this,e=t.$createElement,n=t._self._c||e;return n("div",{staticClass:"el-breadcrumb",attrs:{"aria-label":"Breadcrumb",role:"navigation"}},[n("span",{staticClass:"el-breadcrumb__item"},[n("span",{staticClass:"el-breadcrumb__inner",attrs:{role:"link"}},[n("a",{staticClass:"router-link-active",attrs:{href:"/xdc/mchtranstemplate/index.php/xphp/cgi/page/batch"}},[t._v("商家转账记录")])]),t._v(" "),n("i",{staticClass:"el-breadcrumb__separator el-icon-arrow-right"})]),t._v(" "),n("span",{staticClass:"el-breadcrumb__item"},[n("span",{staticClass:"el-breadcrumb__inner",attrs:{role:"link"}},[n("a",{staticClass:"router-link-active",attrs:{href:"/xdc/mchtranstemplate/index.php/xphp/cgi/page/batch/131000305063501396179052025062417909470630"}},[t._v("批次详情")])]),t._v(" "),n("i",{staticClass:"el-breadcrumb__separator el-icon-arrow-right"})]),t._v(" "),n("span",{staticClass:"el-breadcrumb__item"},[n("span",{staticClass:"el-breadcrumb__inner",attrs:{role:"link"}},[n("a",{staticClass:"router-link-active",attrs:{href:"/xdc/mchtranstemplate/index.php/xphp/cgi/page/batch/131000305063501396179052025062417909470630/detail"}},[t._v("批次明细")])]),t._v(" "),n("i",{staticClass:"el-breadcrumb__separator el-icon-arrow-right"})]),t._v(" "),n("span",{staticClass:"el-breadcrumb__item",attrs:{"aria-current":"page"}},[n("span",{staticClass:"el-breadcrumb__inner",attrs:{role:"link"}},[n("span",[t._v("明细详情")])]),t._v(" "),n("i",{staticClass:"el-breadcrumb__separator el-icon-arrow-right"})])])},function(){var t=this,e=t.$createElement,n=t._self._c||e;return n("div",{staticClass:"content-hd",attrs:{"data-v-ed128e8c":""}},[n("h2",{attrs:{"data-v-ed128e8c":""}},[t._v("明细详情")])])},function(){var t=this,e=t.$createElement,n=t._self._c||e;return n("div",{staticClass:"footer"},[n("div",{staticClass:"wrap"},[n("div",{staticClass:"footer-service"},[n("dl",{staticClass:"service-about"},[n("dt",[t._v("关于我们")]),t._v(" "),n("dd",[n("a",{attrs:{href:"http://kf.qq.com/faq/181012y6bUNR181012nMFnMr.html",target:"_blank",onclick:"pgvSendClick({hottag:'PAY.FOOTER.LINK.ABOUT_US'});"}},[t._v("关于微信支付")])]),t._v(" "),n("dd",[n("a",{attrs:{href:"/index.php/core/home/<USER>",target:"_blank",onclick:"pgvSendClick({hottag:'PAY.FOOTER.LINK.PLATFORM_PROTOCOL'});"}},[t._v("平台使用协议")])]),t._v(" "),n("dd",[n("a",{attrs:{href:"/index.php/public/apply_sign/protocol_v2",target:"_blank",onclick:"pgvSendClick({hottag:'PAY.FOOTER.LINK.PAYMENT_PROTOCOL'});"}},[t._v("支付服务协议")])]),t._v(" "),n("dd",[n("a",{attrs:{href:"https://www.tencent.com/zh-cn/index.html",target:"_blank",onclick:"pgvSendClick({hottag:'PAY.FOOTER.LINK.CONTACT_US'});"}},[t._v("联系我们")])])]),t._v(" "),n("dl",{staticClass:"service-support"},[n("dt",[t._v("服务支持")]),t._v(" "),n("dd",[n("a",{attrs:{href:"/doc/v3/merchant/4012062524",target:"_blank",onclick:"pgvSendClick({hottag:'PAY.FOOTER.LINK.DEV_DOC_MCH'});"}},[t._v("开发文档（商户）")])]),t._v(" "),n("dd",[n("a",{attrs:{href:"/doc/v3/partner/4012069852",target:"_blank",onclick:"pgvSendClick({hottag:'PAY.FOOTER.LINK.DEV_DOC_PARTNER'});"}},[t._v("开发文档（合作伙伴）")])]),t._v(" "),n("dd",[n("a",{attrs:{href:"/static/material/brand.shtml",target:"_blank",onclick:"pgvSendClick({hottag:'PAY.FOOTER.LINK.MATERIAL_DOWNLOAD'});"}},[t._v("物料下载")])])]),t._v(" "),n("dl",{staticClass:"service-friend"},[n("dt",[t._v("友情链接")]),t._v(" "),n("dd",[n("a",{attrs:{href:"https://open.weixin.qq.com/",target:"_blank",onclick:"pgvSendClick({hottag:'PAY.FOOTER.LINK.WEIXIN_OPEN'});"}},[t._v("微信开放平台")])]),t._v(" "),n("dd",[n("a",{attrs:{href:"https://mp.weixin.qq.com/",target:"_blank",onclick:"pgvSendClick({hottag:'PAY.FOOTER.LINK.WEIXIN_MP'});"}},[t._v("微信公众平台")])]),t._v(" "),n("dd",[n("a",{attrs:{href:"https://work.weixin.qq.com/",target:"_blank",onclick:"pgvSendClick({hottag:'PAY.FOOTER.LINK.WEIXIN_QIYE'});"}},[t._v("企业微信")])])]),t._v(" "),n("dl",{staticClass:"service-help"},[n("dt",[t._v("客服帮助")]),t._v(" "),n("dd",[n("a",{attrs:{href:"http://kf.qq.com/product/wechatpaymentmerchant.html",target:"_blank",onclick:"pgvSendClick({hottag:'PAY.FOOTER.LINK.SELF_SERVICE'});"}},[t._v("自助服务专区")])]),t._v(" "),n("dd",[t._v("客服：95017-2")]),t._v(" "),n("dd",[t._v("（工作时间：09:00-22:00）")])]),t._v(" "),n("dl",{staticClass:"service-qrcode",staticStyle:{display:"none"}},[n("dt",[t._v("关注微信支付商家助手公众号")]),t._v(" "),n("dd",[n("img",{staticClass:"img-qrcode",attrs:{src:"https://gtimg.wechatpay.cn/pay/img/common/qrcode_shanghuzhushou.jpg",alt:""}})])])]),t._v(" "),n("div",{staticClass:"footer-copyright"},[t._v("Powered By Tencent & Tenpay　Copyright 2005-2025 Tenpay All Rights Reserved.")])])])}]}},763:function(t,e,n){var i=n(460);"string"==typeof i&&(i=[[t.i,i,""]]),i.locals&&(t.exports=i.locals);n(90)("14326599",i,!0)},764:function(t,e,n){var i=n(461);"string"==typeof i&&(i=[[t.i,i,""]]),i.locals&&(t.exports=i.locals);n(90)("05036cf8",i,!0)}});