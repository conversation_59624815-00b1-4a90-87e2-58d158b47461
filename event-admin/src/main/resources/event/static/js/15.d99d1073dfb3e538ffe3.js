webpackJsonp([15],{220:function(e,t,r){var s=r(89)(r(447),r(735),null,null);e.exports=s.exports},447:function(e,t,r){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.default={data:function(){return{queryForm:{userId:"",appId:""},queryResultForm:{userId:"",appId:"",riskLevel:"0",click:"",exposure:""},queryUserEventResultForm:{userId:"",withdrawRate:"",rewardRate:"",videoLimit:"",reason:"",restrictUser:""},updateForm:{userId:"",appId:"",riskLevel:"0"},updateUserEventForm:{userId:"",appId:"",rewardRate:"",withdrawRate:"",videoLimit:""},isShowQueryUserRisk:!1,isShowQueryUserRiskResult:!1,isShowUpdateUserRisk:!1,isShowQueryUserEvent:!1,isShowQueryUserEventResult:!1,isShowUpdateUserEvent:!1}},created:function(){this.checkIsLogin()},methods:{checkIsLogin:function(){var e=this;e.$axios.post("api/check",{},{}).then(function(t){0!==t.data.code&&e.$message.error(t.data.message)})},queryUserRisk:function(){var e=this;e.$axios.post("risk/query",{},{params:this.queryForm}).then(function(t){if(200===t.status){var r=t.data;0===r.code?(e.queryResultForm=r.result,e.isShowQueryUserRiskResult=!0):e.$message.error(r.result)}else e.$message.error("服务器异常！")})},queryUserEvent:function(){var e=this;e.$axios.post("risk/queryEvent",{},{params:this.queryForm}).then(function(t){if(200===t.status){var r=t.data;0===r.code?(e.queryUserEventResultForm=r.result,e.isShowQueryUserEventResult=!0):e.$message.error(r.result)}else e.$message.error("服务器异常！")})},updateUserEvent:function(){var e=this;e.$axios.post("risk/updateEvent",{},{params:this.updateUserEventForm}).then(function(t){if(200===t.status){var r=t.data;0===r.code?(e.$message.success(r.message),e.isShowUpdateUserEvent=!1):e.$message.error(r.result)}else e.$message.error("服务器异常！")})},updateUserRisk:function(){var e=this;e.$axios.post("risk/update",{},{params:this.updateForm}).then(function(t){if(200===t.status){var r=t.data;0===r.code?(e.$message.success(r.message),e.isShowUpdateUserRisk=!1):e.$message.error(r.result)}else e.$message.error("服务器异常！")})}}}},735:function(e,t){e.exports={render:function(){var e=this,t=e.$createElement,r=e._self._c||t;return r("div",{staticClass:"table"},[r("div",{staticClass:"crumbs"},[r("el-breadcrumb",{attrs:{separator:"/"}},[r("el-breadcrumb-item",[r("i",{staticClass:"el-icon-menu"}),e._v(" 风险等级查询")]),e._v(" "),r("el-breadcrumb-item",[e._v("风险等级")])],1),e._v(" "),r("el-divider",{attrs:{"content-position":"left"}},[e._v("可执行操作")]),e._v(" "),r("el-dialog",{attrs:{title:"查询用户风险等级",visible:e.isShowQueryUserRisk},on:{"update:visible":function(t){e.isShowQueryUserRisk=t}}},[r("el-dialog",{attrs:{title:"查询结果",visible:e.isShowQueryUserRiskResult,"append-to-body":"true"},on:{"update:visible":function(t){e.isShowQueryUserRiskResult=t}}},[r("el-form",{ref:"queryForm",attrs:{model:e.queryResultForm,"label-width":"200px",rules:e.rules}},[r("el-form-item",{attrs:{label:"产品APP_Id",prop:"appId"}},[r("el-input",{attrs:{disabled:"true"},model:{value:e.queryResultForm.appId,callback:function(t){e.$set(e.queryResultForm,"appId",t)},expression:"queryResultForm.appId"}})],1),e._v(" "),r("el-form-item",{attrs:{label:"用户Id",prop:"userId"}},[r("el-input",{attrs:{disabled:"true"},model:{value:e.queryResultForm.userId,callback:function(t){e.$set(e.queryResultForm,"userId",t)},expression:"queryResultForm.userId"}})],1),e._v(" "),r("el-form-item",{attrs:{label:"点击",prop:"userId"}},[r("el-input",{attrs:{disabled:"true"},model:{value:e.queryResultForm.click,callback:function(t){e.$set(e.queryResultForm,"click",t)},expression:"queryResultForm.click"}})],1),e._v(" "),r("el-form-item",{attrs:{label:"曝光",prop:"userId"}},[r("el-input",{attrs:{disabled:"true"},model:{value:e.queryResultForm.exposure,callback:function(t){e.$set(e.queryResultForm,"exposure",t)},expression:"queryResultForm.exposure"}})],1),e._v(" "),r("el-form-item",{attrs:{label:"风险等级",prop:"userId"}},[r("el-select",{staticClass:"select-150",attrs:{filterable:"",placeholder:"请选择风险等级",disabled:"true"},model:{value:e.queryResultForm.riskLevel,callback:function(t){e.$set(e.queryResultForm,"riskLevel",t)},expression:"queryResultForm.riskLevel"}},[r("el-option",{attrs:{label:"低风险",value:"0"}}),e._v(" "),r("el-option",{attrs:{label:"中风险-B",value:"8"}}),e._v(" "),r("el-option",{attrs:{label:"高风险-A",value:"9"}})],1)],1)],1),e._v(" "),r("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[r("el-button",{on:{click:function(t){e.isShowQueryUserRiskResult=!1}}},[e._v("取 消")])],1)],1),e._v(" "),r("el-form",{ref:"queryForm",attrs:{model:e.queryForm,"label-width":"200px",rules:e.rules}},[r("el-form-item",{attrs:{label:"产品APP_Id",prop:"appId"}},[r("el-input",{model:{value:e.queryForm.appId,callback:function(t){e.$set(e.queryForm,"appId",t)},expression:"queryForm.appId"}})],1),e._v(" "),r("el-form-item",{attrs:{label:"用户Id",prop:"userId"}},[r("el-input",{model:{value:e.queryForm.userId,callback:function(t){e.$set(e.queryForm,"userId",t)},expression:"queryForm.userId"}})],1)],1),e._v(" "),r("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[r("el-button",{on:{click:function(t){e.isShowQueryUserRisk=!1}}},[e._v("取 消")]),e._v(" "),r("el-button",{attrs:{type:"primary"},on:{click:function(t){return e.queryUserRisk()}}},[e._v("确 定")])],1)],1),e._v(" "),r("el-dialog",{attrs:{title:"查询用户是否命中自定义配置",visible:e.isShowQueryUserEvent},on:{"update:visible":function(t){e.isShowQueryUserEvent=t}}},[r("el-dialog",{attrs:{title:"查询结果",visible:e.isShowQueryUserEventResult,"append-to-body":"true"},on:{"update:visible":function(t){e.isShowQueryUserEventResult=t}}},[r("el-form",{ref:"queryForm",attrs:{model:e.queryUserEventResultForm,"label-width":"200px",rules:e.rules}},[r("el-form-item",{attrs:{label:"用户Id",prop:"userId"}},[r("el-input",{attrs:{disabled:"true"},model:{value:e.queryUserEventResultForm.userId,callback:function(t){e.$set(e.queryUserEventResultForm,"userId",t)},expression:"queryUserEventResultForm.userId"}})],1),e._v(" "),r("el-form-item",{attrs:{label:"提现比率",prop:"withdrawRate"}},[r("el-input",{attrs:{disabled:"true"},model:{value:e.queryUserEventResultForm.withdrawRate,callback:function(t){e.$set(e.queryUserEventResultForm,"withdrawRate",t)},expression:"queryUserEventResultForm.withdrawRate"}})],1),e._v(" "),r("el-form-item",{attrs:{label:"奖励比率",prop:"rewardRate"}},[r("el-input",{attrs:{disabled:"true"},model:{value:e.queryUserEventResultForm.rewardRate,callback:function(t){e.$set(e.queryUserEventResultForm,"rewardRate",t)},expression:"queryUserEventResultForm.rewardRate"}})],1),e._v(" "),r("el-form-item",{attrs:{label:"视频限制次数",prop:"videoLimit"}},[r("el-input",{attrs:{disabled:"true"},model:{value:e.queryUserEventResultForm.videoLimit,callback:function(t){e.$set(e.queryUserEventResultForm,"videoLimit",t)},expression:"queryUserEventResultForm.videoLimit"}})],1),e._v(" "),r("el-form-item",{attrs:{label:"命中原因",prop:"reason"}},[r("el-input",{attrs:{disabled:"true"},model:{value:e.queryUserEventResultForm.reason,callback:function(t){e.$set(e.queryUserEventResultForm,"reason",t)},expression:"queryUserEventResultForm.reason"}})],1)],1),e._v(" "),r("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[r("el-button",{on:{click:function(t){e.isShowQueryUserEventResult=!1}}},[e._v("取 消")])],1)],1),e._v(" "),r("el-form",{ref:"queryForm",attrs:{model:e.queryForm,"label-width":"200px",rules:e.rules}},[r("el-form-item",{attrs:{label:"产品APP_Id",prop:"appId"}},[r("el-input",{model:{value:e.queryForm.appId,callback:function(t){e.$set(e.queryForm,"appId",t)},expression:"queryForm.appId"}})],1),e._v(" "),r("el-form-item",{attrs:{label:"用户Id",prop:"userId"}},[r("el-input",{model:{value:e.queryForm.userId,callback:function(t){e.$set(e.queryForm,"userId",t)},expression:"queryForm.userId"}})],1)],1),e._v(" "),r("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[r("el-button",{on:{click:function(t){e.isShowQueryUserEvent=!1}}},[e._v("取 消")]),e._v(" "),r("el-button",{attrs:{type:"primary"},on:{click:function(t){return e.queryUserEvent()}}},[e._v("查 询")])],1)],1),e._v(" "),r("el-dialog",{attrs:{title:"修改用户风险等级",visible:e.isShowUpdateUserRisk},on:{"update:visible":function(t){e.isShowUpdateUserRisk=t}}},[r("el-form",{ref:"queryForm",attrs:{model:e.updateForm,"label-width":"200px",rules:e.rules}},[r("el-form-item",{attrs:{label:"产品APP_Id",prop:"appId"}},[r("el-input",{model:{value:e.updateForm.appId,callback:function(t){e.$set(e.updateForm,"appId",t)},expression:"updateForm.appId"}})],1),e._v(" "),r("el-form-item",{attrs:{label:"用户Id",prop:"userId"}},[r("el-input",{model:{value:e.updateForm.userId,callback:function(t){e.$set(e.updateForm,"userId",t)},expression:"updateForm.userId"}})],1),e._v(" "),r("el-form-item",{attrs:{label:"风险等级",prop:"riskLevel"}},[r("el-select",{staticClass:"select-150",attrs:{filterable:"",placeholder:"请选择风险等级"},model:{value:e.updateForm.riskLevel,callback:function(t){e.$set(e.updateForm,"riskLevel",t)},expression:"updateForm.riskLevel"}},[r("el-option",{attrs:{label:"低风险",value:"0"}}),e._v(" "),r("el-option",{attrs:{label:"中风险-B",value:"8"}}),e._v(" "),r("el-option",{attrs:{label:"高风险-A",value:"9"}})],1)],1)],1),e._v(" "),r("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[r("el-button",{on:{click:function(t){e.isShowUpdateUserRisk=!1}}},[e._v("取 消")]),e._v(" "),r("el-button",{attrs:{type:"primary"},on:{click:function(t){return e.updateUserRisk()}}},[e._v("更 新")])],1)],1),e._v(" "),r("el-dialog",{attrs:{title:"修改用户自定义配置命中内容",visible:e.isShowUpdateUserEvent},on:{"update:visible":function(t){e.isShowUpdateUserEvent=t}}},[r("el-form",{attrs:{model:e.updateUserEventForm,"label-width":"200px"}},[r("el-form-item",{attrs:{label:"产品APP_Id",prop:"appId"}},[r("el-input",{model:{value:e.updateUserEventForm.appId,callback:function(t){e.$set(e.updateUserEventForm,"appId",t)},expression:"updateUserEventForm.appId"}})],1),e._v(" "),r("el-form-item",{attrs:{label:"用户Id",prop:"userId"}},[r("el-input",{model:{value:e.updateUserEventForm.userId,callback:function(t){e.$set(e.updateUserEventForm,"userId",t)},expression:"updateUserEventForm.userId"}})],1),e._v(" "),r("el-form-item",{attrs:{label:"提现比率",prop:"withdrawRate"}},[r("el-input",{model:{value:e.updateUserEventForm.withdrawRate,callback:function(t){e.$set(e.updateUserEventForm,"withdrawRate",t)},expression:"updateUserEventForm.withdrawRate"}})],1),e._v(" "),r("el-form-item",{attrs:{label:"奖励比率",prop:"rewardRate"}},[r("el-input",{model:{value:e.updateUserEventForm.rewardRate,callback:function(t){e.$set(e.updateUserEventForm,"rewardRate",t)},expression:"updateUserEventForm.rewardRate"}})],1),e._v(" "),r("el-form-item",{attrs:{label:"视频播放次数",prop:"videoLimit"}},[r("el-input",{model:{value:e.updateUserEventForm.videoLimit,callback:function(t){e.$set(e.updateUserEventForm,"videoLimit",t)},expression:"updateUserEventForm.videoLimit"}})],1)],1),e._v(" "),r("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[r("el-button",{on:{click:function(t){e.isShowUpdateUserEvent=!1}}},[e._v("取 消")]),e._v(" "),r("el-button",{attrs:{type:"primary"},on:{click:function(t){return e.updateUserEvent()}}},[e._v("更 新")])],1)],1),e._v(" "),r("el-form",{attrs:{inline:!0}},[r("el-form-item",{staticStyle:{float:"left"}},[r("el-button",{attrs:{type:"primary",round:""},on:{click:function(t){e.isShowQueryUserRisk=!0}}},[e._v("查询用户风险等级")]),e._v(" "),r("el-button",{attrs:{type:"primary",round:""},on:{click:function(t){e.isShowUpdateUserRisk=!0}}},[e._v("修改用户风险等级")]),e._v(" "),r("el-button",{attrs:{type:"primary",round:""},on:{click:function(t){e.isShowQueryUserEvent=!0}}},[e._v("查询用户是否命中自定义配置")]),e._v(" "),r("el-button",{attrs:{type:"primary",round:""},on:{click:function(t){e.isShowUpdateUserEvent=!0}}},[e._v("修改用户自定义配置命中内容")])],1)],1)],1)])},staticRenderFns:[]}}});