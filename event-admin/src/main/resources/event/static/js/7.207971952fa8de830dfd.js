webpackJsonp([7],{216:function(e,t,r){r(761);var s=r(89)(r(443),r(744),null,null);e.exports=s.exports},443:function(e,t,r){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.default={data:function(){return{appList:[],isShowUserAdSwitch:!1,isShowUpdateUserAdSwitch:!1,isShowUpdateUserAdExposure:!1,isShowUserAdSwitchResult:!1,isShowUserAdPlatform:!1,isShowUserAdPlatformResult:!1,isShowUpdateUserAdPlatform:!1,isShowUserAbGroup:!1,isShowDelUserAccessKey:!1,disableUserCount:20,disableProduct:"我的饭店",queryUserForm:{appId:"",userId:""},queryUserResultForm:{appId:"",userId:"",status:"0"},queryUserAdPlatformResult:{appId:"",userId:"",platformList:[],biddingSKipList:[],isBiddingEnable:"",noCacheStg:"",isLockedArea:""},updateUserAdPlatform:{appId:"",userId:"",platformList:[],biddingSKipList:[],isBiddingEnable:"1",noCacheStg:"1",isLockedArea:""},updateUserAbGroup:{appId:"",userId:"",groupId:""},updateUserForm:{appId:"",userId:"",status:"0"},updateUserExposureForm:{appId:"",userId:"",time:""},delUserAccessKeyForm:{appId:"",userId:"",delUser:"0",os:"0"}}},created:function(){this.checkIsLogin(),this.getAppList()},methods:{getAppList:function(){var e=this;e.$axios.post("dist/appList",{},{}).then(function(t){if(200===t.status){var r=t.data;0===r.code?e.appList=r.result:e.$message.error(r.result)}else e.$message.error("服务器异常！")})},checkIsLogin:function(){var e=this;e.$axios.post("api/check",{},{}).then(function(t){0!==t.data.code&&e.$message.error(t.data.message)})},queryUserAdSwitch:function(){var e=this;e.$axios.post("ad/query",{},{params:this.queryUserForm}).then(function(t){if(200===t.status){var r=t.data;0===r.code?(e.queryUserResultForm=r.result,e.isShowUserAdSwitchResult=!0):e.$message.error(r.result)}else e.$message.error("服务器异常！")})},queryUserAdPlatformResultRt:function(){var e=this;e.$axios.post("ad/getUserSkipAdPlat",{},{params:this.queryUserForm}).then(function(t){if(200===t.status){var r=t.data;0===r.code?(e.queryUserAdPlatformResult=r.result,e.isShowUserAdPlatformResult=!0):e.$message.error(r.result)}else e.$message.error("服务器异常！")})},updateUserAdSwitch:function(){var e=this;e.$axios.post("ad/update",{},{params:this.updateUserForm}).then(function(t){if(200===t.status){var r=t.data;0===r.code?(e.$message.success(r.message),e.isShowUpdateUserAdSwitch=!1):e.$message.error(r.result)}else e.$message.error("服务器异常！")})},updateUserAbGroupGt:function(){var e=this;e.$axios.post("ad/updateUserAb",{},{params:this.updateUserAbGroup}).then(function(t){if(200===t.status){var r=t.data;0===r.code?(e.$message.success(r.message),e.isShowUserAbGroup=!1):e.$message.error(r.result)}else e.$message.error("服务器异常！")})},updateUserSkipPlat:function(){var e=this;e.$axios.post("ad/updateSkipAdPlat",e.updateUserAdPlatform,{}).then(function(t){if(200===t.status){var r=t.data;0===r.code?(e.$message.success(r.message),e.isShowUpdateUserAdPlatform=!1):e.$message.error(r.result)}else e.$message.error("服务器异常！")})},updateUserAdExposure:function(){var e=this;e.$axios.post("ad/updateExposure",{},{params:this.updateUserExposureForm}).then(function(t){if(200===t.status){var r=t.data;0===r.code?(e.$message.success(r.message),e.isShowUpdateUserAdExposure=!1):e.$message.error(r.result)}else e.$message.error("服务器异常！")})},delUserAccessKey:function(){var e=this;e.$axios.post("ad/delUserAccess",{},{params:this.delUserAccessKeyForm}).then(function(t){if(200===t.status){var r=t.data;0===r.code?(e.$message.success(r.message),e.isShowDelUserAccessKey=!1):e.$message.error(r.result)}else e.$message.error("服务器异常！")})}}}},458:function(e,t,r){t=e.exports=r(29)(),t.push([e.i,".transition-box{margin-bottom:10px;width:80%;height:100px;border-radius:4px;background-color:#64b0ff;text-align:center;color:#fff;padding:40px 20px;box-sizing:border-box;margin-right:20px}",""])},744:function(e,t){e.exports={render:function(){var e=this,t=e.$createElement,r=e._self._c||t;return r("div",{staticClass:"table"},[r("div",{staticClass:"crumbs"},[r("el-breadcrumb",{attrs:{separator:"/"}},[r("el-breadcrumb-item",[r("i",{staticClass:"el-icon-menu"}),e._v(" 广告禁用查询")]),e._v(" "),r("el-breadcrumb-item",[e._v("广告禁用")])],1)],1),e._v(" "),r("el-divider",{attrs:{"content-position":"left"}},[e._v("可执行操作")]),e._v(" "),r("el-dialog",{attrs:{title:"查询用户广告封禁状态",visible:e.isShowUserAdSwitch},on:{"update:visible":function(t){e.isShowUserAdSwitch=t}}},[r("el-dialog",{attrs:{title:"查询结果",visible:e.isShowUserAdSwitchResult,"append-to-body":"true"},on:{"update:visible":function(t){e.isShowUserAdSwitchResult=t}}},[r("el-form",{ref:"queryForm",attrs:{model:e.queryUserResultForm,"label-width":"200px",rules:e.rules}},[r("el-form-item",{attrs:{label:"产品APP_ID",prop:"appId"}},[r("el-input",{attrs:{disabled:"true"},model:{value:e.queryUserResultForm.appId,callback:function(t){e.$set(e.queryUserResultForm,"appId",t)},expression:"queryUserResultForm.appId"}})],1),e._v(" "),r("el-form-item",{attrs:{label:"用户ID",prop:"userId"}},[r("el-input",{attrs:{disabled:"true"},model:{value:e.queryUserResultForm.userId,callback:function(t){e.$set(e.queryUserResultForm,"userId",t)},expression:"queryUserResultForm.userId"}})],1),e._v(" "),r("el-form-item",{attrs:{label:"开关",prop:"status"}},[r("el-select",{staticClass:"select-150",attrs:{filterable:"",placeholder:"请选择",disabled:"true"},model:{value:e.queryUserResultForm.status,callback:function(t){e.$set(e.queryUserResultForm,"status",t)},expression:"queryUserResultForm.status"}},[r("el-option",{attrs:{label:"开启观看",value:"0"}}),e._v(" "),r("el-option",{attrs:{label:"禁止观看",value:"1"}})],1)],1)],1),e._v(" "),r("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[r("el-button",{on:{click:function(t){e.isShowUserAdSwitchResult=!1}}},[e._v("取 消")])],1)],1),e._v(" "),r("el-form",{ref:"queryForm",attrs:{model:e.queryUserForm,"label-width":"200px",rules:e.rules}},[r("el-form-item",{attrs:{label:"产品APP_ID",prop:"appId"}},[r("el-input",{model:{value:e.queryUserForm.appId,callback:function(t){e.$set(e.queryUserForm,"appId",t)},expression:"queryUserForm.appId"}})],1),e._v(" "),r("el-form-item",{attrs:{label:"用户ID",prop:"userId"}},[r("el-input",{model:{value:e.queryUserForm.userId,callback:function(t){e.$set(e.queryUserForm,"userId",t)},expression:"queryUserForm.userId"}})],1)],1),e._v(" "),r("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[r("el-button",{on:{click:function(t){e.isShowUserAdSwitch=!1}}},[e._v("取 消")]),e._v(" "),r("el-button",{attrs:{type:"primary"},on:{click:function(t){return e.queryUserAdSwitch()}}},[e._v("确 定")])],1)],1),e._v(" "),r("el-dialog",{attrs:{title:"查询用户可观看视频平台",visible:e.isShowUserAdPlatform},on:{"update:visible":function(t){e.isShowUserAdPlatform=t}}},[r("el-dialog",{attrs:{title:"查询结果",visible:e.isShowUserAdPlatformResult,"append-to-body":"true"},on:{"update:visible":function(t){e.isShowUserAdPlatformResult=t}}},[r("el-form",{ref:"queryForm",attrs:{model:e.queryUserResultForm,"label-width":"200px",rules:e.rules}},[r("el-form-item",{attrs:{label:"产品APP_ID",prop:"appId"}},[r("el-select",{staticClass:"select-150",attrs:{placeholder:"产品",filterable:"",disabled:"true"},model:{value:e.queryUserAdPlatformResult.appId,callback:function(t){e.$set(e.queryUserAdPlatformResult,"appId",t)},expression:"queryUserAdPlatformResult.appId"}},e._l(e.appList,function(e){return r("el-option",{attrs:{label:e.label,value:e.value}})}),1)],1),e._v(" "),r("el-form-item",{attrs:{label:"用户ID",prop:"userId"}},[r("el-input",{model:{value:e.queryUserAdPlatformResult.userId,callback:function(t){e.$set(e.queryUserAdPlatformResult,"userId",t)},expression:"queryUserAdPlatformResult.userId"}})],1),e._v(" "),r("el-form-item",{attrs:{label:"跳过不观看的平台",prop:"platformList"}},[r("el-checkbox-group",{model:{value:e.queryUserAdPlatformResult.platformList,callback:function(t){e.$set(e.queryUserAdPlatformResult,"platformList",t)},expression:"queryUserAdPlatformResult.platformList"}},[r("el-checkbox",{attrs:{label:"穿山甲",border:"",disabled:""}}),e._v(" "),r("el-checkbox",{attrs:{label:"广点通",border:"",disabled:""}}),e._v(" "),r("el-checkbox",{attrs:{label:"快手",border:"",disabled:""}}),e._v(" "),r("el-checkbox",{attrs:{label:"VIVO",border:"",disabled:""}}),e._v(" "),r("el-checkbox",{attrs:{label:"OPPO",border:"",disabled:""}}),e._v(" "),r("el-checkbox",{attrs:{label:"百度",border:"",disabled:""}}),e._v(" "),r("el-checkbox",{attrs:{label:"小米",border:"",disabled:""}}),e._v(" "),r("el-checkbox",{attrs:{label:"华为",border:"",disabled:""}}),e._v(" "),r("el-checkbox",{attrs:{label:"阿里",border:"",disabled:""}}),e._v(" "),r("el-checkbox",{attrs:{label:"爱奇艺",border:"",disabled:""}}),e._v(" "),r("el-checkbox",{attrs:{label:"simob",border:"",disabled:""}})],1)],1),e._v(" "),r("el-form-item",{attrs:{label:"跳过不观看的Bidding平台",prop:"biddingSKipList"}},[r("el-checkbox-group",{model:{value:e.queryUserAdPlatformResult.biddingSKipList,callback:function(t){e.$set(e.queryUserAdPlatformResult,"biddingSKipList",t)},expression:"queryUserAdPlatformResult.biddingSKipList"}},[r("el-checkbox",{attrs:{label:"穿山甲",border:"",disabled:""}}),e._v(" "),r("el-checkbox",{attrs:{label:"广点通",border:"",disabled:""}}),e._v(" "),r("el-checkbox",{attrs:{label:"快手",border:"",disabled:""}}),e._v(" "),r("el-checkbox",{attrs:{label:"VIVO",border:"",disabled:""}}),e._v(" "),r("el-checkbox",{attrs:{label:"OPPO",border:"",disabled:""}}),e._v(" "),r("el-checkbox",{attrs:{label:"百度",border:"",disabled:""}}),e._v(" "),r("el-checkbox",{attrs:{label:"阿里",border:"",disabled:""}}),e._v(" "),r("el-checkbox",{attrs:{label:"爱奇艺",border:"",disabled:""}}),e._v(" "),r("el-checkbox",{attrs:{label:"simob",border:"",disabled:""}})],1)],1),e._v(" "),r("el-form-item",{attrs:{label:"Bidding是否可播放",prop:"isBiddingEnable"}},[r("el-radio",{attrs:{label:"1",border:"",disabled:""},model:{value:e.queryUserAdPlatformResult.isBiddingEnable,callback:function(t){e.$set(e.queryUserAdPlatformResult,"isBiddingEnable",t)},expression:"queryUserAdPlatformResult.isBiddingEnable"}},[e._v("可播放bidding")]),e._v(" "),r("el-radio",{attrs:{label:"2",border:"",disabled:""},model:{value:e.queryUserAdPlatformResult.isBiddingEnable,callback:function(t){e.$set(e.queryUserAdPlatformResult,"isBiddingEnable",t)},expression:"queryUserAdPlatformResult.isBiddingEnable"}},[e._v("不可播放bidding")])],1),e._v(" "),r("el-form-item",{attrs:{label:"Bidding是否作为打底策略",prop:"platformList"}},[r("el-radio",{attrs:{label:"1",border:"",disabled:""},model:{value:e.queryUserAdPlatformResult.noCacheStg,callback:function(t){e.$set(e.queryUserAdPlatformResult,"noCacheStg",t)},expression:"queryUserAdPlatformResult.noCacheStg"}},[e._v("填充打底层")]),e._v(" "),r("el-radio",{attrs:{label:"2",border:"",disabled:""},model:{value:e.queryUserAdPlatformResult.noCacheStg,callback:function(t){e.$set(e.queryUserAdPlatformResult,"noCacheStg",t)},expression:"queryUserAdPlatformResult.noCacheStg"}},[e._v("不填充打底层")])],1)],1),e._v(" "),r("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[r("el-button",{on:{click:function(t){e.isShowUserAdPlatformResult=!1}}},[e._v("取 消")])],1)],1),e._v(" "),r("el-form",{ref:"queryForm",attrs:{model:e.queryUserForm,"label-width":"200px",rules:e.rules}},[r("el-form-item",{attrs:{label:"产品APP_ID",prop:"appId"}},[r("el-select",{staticClass:"select-150",attrs:{placeholder:"产品",filterable:""},model:{value:e.queryUserForm.appId,callback:function(t){e.$set(e.queryUserForm,"appId",t)},expression:"queryUserForm.appId"}},e._l(e.appList,function(e){return r("el-option",{attrs:{label:e.label,value:e.value}})}),1)],1),e._v(" "),r("el-form-item",{attrs:{label:"用户ID",prop:"userId"}},[r("el-input",{model:{value:e.queryUserForm.userId,callback:function(t){e.$set(e.queryUserForm,"userId",t)},expression:"queryUserForm.userId"}})],1)],1),e._v(" "),r("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[r("el-button",{on:{click:function(t){e.isShowUserAdPlatform=!1}}},[e._v("取 消")]),e._v(" "),r("el-button",{attrs:{type:"primary"},on:{click:function(t){return e.queryUserAdPlatformResultRt()}}},[e._v("确 定")])],1)],1),e._v(" "),r("el-dialog",{attrs:{title:"修改用户AB分组",visible:e.isShowUserAbGroup},on:{"update:visible":function(t){e.isShowUserAbGroup=t}}},[r("el-form",{ref:"queryForm",attrs:{model:e.updateUserAbGroup,"label-width":"200px",rules:e.rules}},[r("el-form-item",{attrs:{label:"产品APP_Id",prop:"appId"}},[r("el-select",{staticClass:"select-150",attrs:{placeholder:"产品",filterable:""},model:{value:e.updateUserAbGroup.appId,callback:function(t){e.$set(e.updateUserAbGroup,"appId",t)},expression:"updateUserAbGroup.appId"}},e._l(e.appList,function(e){return r("el-option",{attrs:{label:e.label,value:e.value}})}),1)],1),e._v(" "),r("el-form-item",{attrs:{label:"用户Id",prop:"userId"}},[r("el-input",{model:{value:e.updateUserAbGroup.userId,callback:function(t){e.$set(e.updateUserAbGroup,"userId",t)},expression:"updateUserAbGroup.userId"}})],1),e._v(" "),r("el-form-item",{attrs:{label:"用户分组ID",prop:"ab"}},[r("el-input",{model:{value:e.updateUserAbGroup.groupId,callback:function(t){e.$set(e.updateUserAbGroup,"groupId",t)},expression:"updateUserAbGroup.groupId"}})],1)],1),e._v(" "),r("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[r("el-button",{on:{click:function(t){e.isShowUserAbGroup=!1}}},[e._v("取 消")]),e._v(" "),r("el-button",{attrs:{type:"primary"},on:{click:function(t){return e.updateUserAbGroupGt()}}},[e._v("更 新")])],1)],1),e._v(" "),r("el-dialog",{attrs:{title:"修改用户可观看视频平台",visible:e.isShowUpdateUserAdPlatform},on:{"update:visible":function(t){e.isShowUpdateUserAdPlatform=t}}},[r("el-form",{ref:"queryForm",attrs:{model:e.updateUserAdPlatform,"label-width":"200px",rules:e.rules}},[r("el-form-item",{attrs:{label:"产品APP_Id",prop:"appId"}},[r("el-select",{staticClass:"select-150",attrs:{placeholder:"产品",filterable:""},model:{value:e.updateUserAdPlatform.appId,callback:function(t){e.$set(e.updateUserAdPlatform,"appId",t)},expression:"updateUserAdPlatform.appId"}},e._l(e.appList,function(e){return r("el-option",{attrs:{label:e.label,value:e.value}})}),1)],1),e._v(" "),r("el-form-item",{attrs:{label:"用户Id",prop:"userId"}},[r("el-input",{model:{value:e.updateUserAdPlatform.userId,callback:function(t){e.$set(e.updateUserAdPlatform,"userId",t)},expression:"updateUserAdPlatform.userId"}})],1),e._v(" "),r("el-form-item",{attrs:{label:"跳过不观看的平台",prop:"platformList"}},[r("el-checkbox-group",{model:{value:e.updateUserAdPlatform.platformList,callback:function(t){e.$set(e.updateUserAdPlatform,"platformList",t)},expression:"updateUserAdPlatform.platformList"}},[r("el-checkbox",{attrs:{label:"穿山甲",border:""}}),e._v(" "),r("el-checkbox",{attrs:{label:"广点通",border:""}}),e._v(" "),r("el-checkbox",{attrs:{label:"快手",border:""}}),e._v(" "),r("el-checkbox",{attrs:{label:"VIVO",border:""}}),e._v(" "),r("el-checkbox",{attrs:{label:"OPPO",border:""}}),e._v(" "),r("el-checkbox",{attrs:{label:"百度",border:""}}),e._v(" "),r("el-checkbox",{attrs:{label:"小米",border:""}}),e._v(" "),r("el-checkbox",{attrs:{label:"华为",border:""}}),e._v(" "),r("el-checkbox",{attrs:{label:"阿里",border:""}}),e._v(" "),r("el-checkbox",{attrs:{label:"爱奇艺",border:""}}),e._v(" "),r("el-checkbox",{attrs:{label:"simob",border:""}})],1)],1),e._v(" "),r("el-form-item",{attrs:{label:"跳过不观看的Bidding平台",prop:"biddingSKipList"}},[r("el-checkbox-group",{model:{value:e.updateUserAdPlatform.biddingSKipList,callback:function(t){e.$set(e.updateUserAdPlatform,"biddingSKipList",t)},expression:"updateUserAdPlatform.biddingSKipList"}},[r("el-checkbox",{attrs:{label:"穿山甲",border:""}}),e._v(" "),r("el-checkbox",{attrs:{label:"广点通",border:""}}),e._v(" "),r("el-checkbox",{attrs:{label:"快手",border:""}}),e._v(" "),r("el-checkbox",{attrs:{label:"VIVO",border:""}}),e._v(" "),r("el-checkbox",{attrs:{label:"OPPO",border:""}}),e._v(" "),r("el-checkbox",{attrs:{label:"百度",border:""}}),e._v(" "),r("el-checkbox",{attrs:{label:"阿里",border:""}}),e._v(" "),r("el-checkbox",{attrs:{label:"爱奇艺",border:""}}),e._v(" "),r("el-checkbox",{attrs:{label:"simob",border:""}})],1)],1),e._v(" "),r("el-form-item",{attrs:{label:"Bidding是否可播放",prop:"isBiddingEnable"}},[r("el-radio",{attrs:{label:"1",border:""},model:{value:e.updateUserAdPlatform.isBiddingEnable,callback:function(t){e.$set(e.updateUserAdPlatform,"isBiddingEnable",t)},expression:"updateUserAdPlatform.isBiddingEnable"}},[e._v("可播放bidding")]),e._v(" "),r("el-radio",{attrs:{label:"2",border:""},model:{value:e.updateUserAdPlatform.isBiddingEnable,callback:function(t){e.$set(e.updateUserAdPlatform,"isBiddingEnable",t)},expression:"updateUserAdPlatform.isBiddingEnable"}},[e._v("不可播放bidding")])],1),e._v(" "),r("el-form-item",{attrs:{label:"Bidding是否作为打底策略",prop:"platformList"}},[r("el-radio",{attrs:{label:"1",border:""},model:{value:e.updateUserAdPlatform.noCacheStg,callback:function(t){e.$set(e.updateUserAdPlatform,"noCacheStg",t)},expression:"updateUserAdPlatform.noCacheStg"}},[e._v("填充打底层")]),e._v(" "),r("el-radio",{attrs:{label:"2",border:""},model:{value:e.updateUserAdPlatform.noCacheStg,callback:function(t){e.$set(e.updateUserAdPlatform,"noCacheStg",t)},expression:"updateUserAdPlatform.noCacheStg"}},[e._v("不填充打底层")])],1)],1),e._v(" "),r("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[r("el-button",{on:{click:function(t){e.isShowUpdateUserAdPlatform=!1}}},[e._v("取 消")]),e._v(" "),r("el-button",{attrs:{type:"primary"},on:{click:function(t){return e.updateUserSkipPlat()}}},[e._v("更 新")])],1)],1),e._v(" "),r("el-dialog",{attrs:{title:"修改用户广告封禁状态",visible:e.isShowUpdateUserAdSwitch},on:{"update:visible":function(t){e.isShowUpdateUserAdSwitch=t}}},[r("el-form",{ref:"queryForm",attrs:{model:e.updateUserForm,"label-width":"200px",rules:e.rules}},[r("el-form-item",{attrs:{label:"产品APP_Id",prop:"appId"}},[r("el-input",{model:{value:e.updateUserForm.appId,callback:function(t){e.$set(e.updateUserForm,"appId",t)},expression:"updateUserForm.appId"}})],1),e._v(" "),r("el-form-item",{attrs:{label:"用户Id",prop:"userId"}},[r("el-input",{model:{value:e.updateUserForm.userId,callback:function(t){e.$set(e.updateUserForm,"userId",t)},expression:"updateUserForm.userId"}})],1),e._v(" "),r("el-form-item",{attrs:{label:"开关",prop:"status"}},[r("el-select",{staticClass:"select-150",attrs:{filterable:"",placeholder:"请选择"},model:{value:e.updateUserForm.status,callback:function(t){e.$set(e.updateUserForm,"status",t)},expression:"updateUserForm.status"}},[r("el-option",{attrs:{label:"开启观看",value:"0"}}),e._v(" "),r("el-option",{attrs:{label:"禁止观看",value:"1"}})],1)],1)],1),e._v(" "),r("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[r("el-button",{on:{click:function(t){e.isShowUpdateUserAdSwitch=!1}}},[e._v("取 消")]),e._v(" "),r("el-button",{attrs:{type:"primary"},on:{click:function(t){return e.updateUserAdSwitch()}}},[e._v("更 新")])],1)],1),e._v(" "),r("el-dialog",{attrs:{title:"修改用户视频曝光次数",visible:e.isShowUpdateUserAdExposure},on:{"update:visible":function(t){e.isShowUpdateUserAdExposure=t}}},[r("el-form",{ref:"queryForm",attrs:{model:e.updateUserExposureForm,"label-width":"200px",rules:e.rules}},[r("el-form-item",{attrs:{label:"产品APP_ID",prop:"appId"}},[r("el-input",{model:{value:e.updateUserExposureForm.appId,callback:function(t){e.$set(e.updateUserExposureForm,"appId",t)},expression:"updateUserExposureForm.appId"}})],1),e._v(" "),r("el-form-item",{attrs:{label:"用户ID",prop:"userId"}},[r("el-input",{model:{value:e.updateUserExposureForm.userId,callback:function(t){e.$set(e.updateUserExposureForm,"userId",t)},expression:"updateUserExposureForm.userId"}})],1),e._v(" "),r("el-form-item",{attrs:{label:"观看次数",prop:"userId"}},[r("el-input",{model:{value:e.updateUserExposureForm.time,callback:function(t){e.$set(e.updateUserExposureForm,"time",t)},expression:"updateUserExposureForm.time"}})],1)],1),e._v(" "),r("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[r("el-button",{on:{click:function(t){e.isShowUpdateUserAdExposure=!1}}},[e._v("取 消")]),e._v(" "),r("el-button",{attrs:{type:"primary"},on:{click:function(t){return e.updateUserAdExposure()}}},[e._v("更 新")])],1)],1),e._v(" "),r("el-dialog",{attrs:{title:"删除用户AccessKey",visible:e.isShowDelUserAccessKey},on:{"update:visible":function(t){e.isShowDelUserAccessKey=t}}},[r("el-form",{ref:"queryForm",attrs:{model:e.delUserAccessKeyForm,"label-width":"200px",rules:e.rules}},[r("el-form-item",{attrs:{label:"产品",prop:"appId"}},[r("el-select",{staticClass:"select-150",attrs:{placeholder:"产品",filterable:""},model:{value:e.delUserAccessKeyForm.appId,callback:function(t){e.$set(e.delUserAccessKeyForm,"appId",t)},expression:"delUserAccessKeyForm.appId"}},e._l(e.appList,function(e){return r("el-option",{attrs:{label:e.label,value:e.value}})}),1)],1),e._v(" "),r("el-form-item",{attrs:{label:"用户ID",prop:"userId"}},[r("el-input",{model:{value:e.delUserAccessKeyForm.userId,callback:function(t){e.$set(e.delUserAccessKeyForm,"userId",t)},expression:"delUserAccessKeyForm.userId"}})],1),e._v(" "),r("el-form-item",{attrs:{label:"是否清除用户信息",prop:"delUser"}},[r("el-select",{staticClass:"select-150",attrs:{placeholder:"产品",filterable:""},model:{value:e.delUserAccessKeyForm.delUser,callback:function(t){e.$set(e.delUserAccessKeyForm,"delUser",t)},expression:"delUserAccessKeyForm.delUser"}},[r("el-option",{attrs:{label:"保留用户信息",value:"0"}}),e._v(" "),r("el-option",{attrs:{label:"清除用户信息",value:"1"}})],1)],1),e._v(" "),r("el-form-item",{attrs:{label:"系统",prop:"status"}},[r("el-select",{staticClass:"select-150",attrs:{filterable:"",placeholder:"请选择"},model:{value:e.delUserAccessKeyForm.os,callback:function(t){e.$set(e.delUserAccessKeyForm,"os",t)},expression:"delUserAccessKeyForm.os"}},[r("el-option",{attrs:{label:"Android",value:"0"}}),e._v(" "),r("el-option",{attrs:{label:"iOS",value:"1"}})],1)],1)],1),e._v(" "),r("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[r("el-button",{on:{click:function(t){e.isShowDelUserAccessKey=!1}}},[e._v("取 消")]),e._v(" "),r("el-button",{attrs:{type:"primary"},on:{click:function(t){return e.delUserAccessKey()}}},[e._v("删 除")])],1)],1),e._v(" "),r("el-row",[r("el-form",{attrs:{inline:!0}},[r("el-form-item",{staticStyle:{float:"left"}},[r("el-button",{attrs:{type:"primary",round:""},on:{click:function(t){e.isShowUserAdSwitch=!0}}},[e._v("查询用户广告封禁状态")]),e._v(" "),r("el-button",{attrs:{type:"primary",round:""},on:{click:function(t){e.isShowUpdateUserAdSwitch=!0}}},[e._v("修改用户广告封禁状态")]),e._v(" "),r("el-button",{attrs:{type:"primary",round:""},on:{click:function(t){e.isShowUpdateUserAdExposure=!0}}},[e._v("修改用户视频曝光次数")]),e._v(" "),r("el-button",{attrs:{type:"primary",round:""},on:{click:function(t){e.isShowUserAdPlatform=!0}}},[e._v("查询用户可观看视频平台")]),e._v(" "),r("el-button",{attrs:{type:"primary",round:""},on:{click:function(t){e.isShowUpdateUserAdPlatform=!0}}},[e._v("修改用户可观看视频平台")]),e._v(" "),r("el-button",{attrs:{type:"primary",round:""},on:{click:function(t){e.isShowUserAbGroup=!0}}},[e._v("修改用户AB分组")]),e._v(" "),r("el-button",{attrs:{type:"primary",round:""},on:{click:function(t){e.isShowDelUserAccessKey=!0}}},[e._v("删除用户AccessKey/用户信息")])],1)],1)],1)],1)},staticRenderFns:[]}},761:function(e,t,r){var s=r(458);"string"==typeof s&&(s=[[e.i,s,""]]),s.locals&&(e.exports=s.locals);r(90)("2a4c5340",s,!0)}});