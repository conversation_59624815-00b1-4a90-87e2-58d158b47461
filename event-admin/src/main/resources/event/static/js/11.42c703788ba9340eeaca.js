webpackJsonp([11],{208:function(e,t,s){s(754);var i=s(89)(s(435),s(736),null,null);e.exports=i.exports},435:function(e,t,s){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.default={data:function(){return{appList:[],configList:[],configListB:[],addExUserConfigForm:{dist:"",name:""},isShowAddExUserConfigForm:!1,isShowAddExUserConfigFormB:!1}},created:function(){this.getAppList(),this.getConfigList()},methods:{handleCurrentChangeForPlan:function(e){this.getConfigList()},getAppList:function(){var e=this;e.$axios.post("dist/appDistList",{},{}).then(function(t){if(200===t.status){var s=t.data;0===s.code?e.appList=s.result:e.$message.error(s.result)}else e.$message.error("服务器异常！")})},getConfigList:function(){var e=this;e.$axios.post("adWeakRule/query",{},{params:e.queryParam}).then(function(t){if(200===t.status){var s=t.data;0===s.code?(e.configList=s.result[0],e.configListB=s.result[1]):e.$message.error(s.result)}else e.$message.error("服务器异常！")})},addExUserConfigPre:function(){this.clearExUserConfigAddForm(),this.isShowAddExUserConfigForm=!0},addExUserConfigPreB:function(){this.clearExUserConfigAddForm(),this.isShowAddExUserConfigFormB=!0},clearExUserConfigAddForm:function(){this.addExUserConfigForm.dist="",this.addExUserConfigForm.name=""},updateExUserConfigPre:function(e){this.editExUserConfigForm.dist=e.dist;var t=this;t.$axios.post("adWeakRule/delete",t.addExUserConfigForm.dist,{}).then(function(e){if(200===e.status){0===e.data.code?(t.$message({message:"添加成功",type:"success"}),t.isShowAddExUserConfigForm=!1,t.getConfigList()):t.$message.error("更新状态失败")}else t.$message.error("服务器异常！")})},updateExUserConfigPreB:function(e){this.editExUserConfigForm.dist=e.dist,this.editExUserConfigForm.dist=e.dist;var t=this;t.$axios.post("adWeakRule/deleteB",t.addExUserConfigForm.dist,{}).then(function(e){if(200===e.status){0===e.data.code?(t.$message({message:"添加成功",type:"success"}),t.isShowAddExUserConfigForm=!1,t.getConfigList()):t.$message.error("更新状态失败")}else t.$message.error("服务器异常！")})},addExUserConfig:function(){var e=this;e.$axios.post("adWeakRule/insert",e.addExUserConfigForm.dist,{}).then(function(t){if(200===t.status){0===t.data.code?(e.$message({message:"添加成功",type:"success"}),e.isShowAddExUserConfigForm=!1,e.getConfigList()):e.$message.error("更新状态失败")}else e.$message.error("服务器异常！")})},addExUserConfigB:function(){var e=this;e.$axios.post("adWeakRule/insertB",e.addExUserConfigForm.dist,{}).then(function(t){if(200===t.status){0===t.data.code?(e.$message({message:"添加成功",type:"success"}),e.isShowAddExUserConfigFormB=!1,e.getConfigList()):e.$message.error("更新状态失败")}else e.$message.error("服务器异常！")})}}}},451:function(e,t,s){t=e.exports=s(29)(),t.push([e.i,".width-100{width:100px}.select-150 .el-input{width:150px}.inline-block{display:inline-block}",""])},736:function(e,t){e.exports={render:function(){var e=this,t=e.$createElement,s=e._self._c||t;return s("div",{staticClass:"table"},[s("div",{staticClass:"crumbs"},[s("el-breadcrumb",{attrs:{separator:"/"}},[s("el-breadcrumb-item",[s("i",{staticClass:"el-icon-menu"}),e._v(" 产品风险控制管理")]),e._v(" "),s("el-breadcrumb-item",[e._v("广告衰减配置")])],1)],1),e._v(" "),s("el-dialog",{attrs:{title:"广告衰减配置A",visible:e.isShowAddExUserConfigForm},on:{"update:visible":function(t){e.isShowAddExUserConfigForm=t}}},[s("el-form",{ref:"addExUserConfigForm",attrs:{model:e.addExUserConfigForm,"label-width":"200px"}},[s("el-form-item",{attrs:{label:"产品"}},[s("el-select",{staticClass:"select-150",attrs:{placeholder:"配置类型",filterable:""},model:{value:e.addExUserConfigForm.dist,callback:function(t){e.$set(e.addExUserConfigForm,"dist",t)},expression:"addExUserConfigForm.dist"}},e._l(e.appList,function(e){return s("el-option",{attrs:{label:e.name,value:e.dist}})}),1)],1)],1),e._v(" "),s("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[s("el-button",{on:{click:function(t){e.isShowAddExUserConfigForm=!1}}},[e._v("取 消")]),e._v(" "),s("el-button",{attrs:{type:"primary"},on:{click:function(t){return e.addExUserConfig()}}},[e._v("确 定")])],1)],1),e._v(" "),s("el-dialog",{attrs:{title:"广告衰减配置B",visible:e.isShowAddExUserConfigFormB},on:{"update:visible":function(t){e.isShowAddExUserConfigFormB=t}}},[s("el-form",{ref:"addExUserConfigForm",attrs:{model:e.addExUserConfigForm,"label-width":"200px"}},[s("el-form-item",{attrs:{label:"产品"}},[s("el-select",{staticClass:"select-150",attrs:{placeholder:"配置类型",filterable:""},model:{value:e.addExUserConfigForm.dist,callback:function(t){e.$set(e.addExUserConfigForm,"dist",t)},expression:"addExUserConfigForm.dist"}},e._l(e.appList,function(e){return s("el-option",{attrs:{label:e.name,value:e.dist}})}),1)],1)],1),e._v(" "),s("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[s("el-button",{on:{click:function(t){e.isShowAddExUserConfigFormB=!1}}},[e._v("取 消")]),e._v(" "),s("el-button",{attrs:{type:"primary"},on:{click:function(t){return e.addExUserConfigB()}}},[e._v("确 定")])],1)],1),e._v(" "),s("el-row",[s("el-col",{attrs:{span:24}},[s("el-form",{attrs:{model:e.queryParam,size:"small",inline:""}},[s("el-form-item",{staticStyle:{float:"right"}},[s("el-button",{attrs:{type:"warning"},on:{click:function(t){return e.getConfigList()}}},[e._v("查询")]),e._v(" "),s("el-button",{attrs:{type:"primary",round:""},on:{click:function(t){return e.addExUserConfigPre()}}},[e._v("广告衰减配置A")]),e._v(" "),s("el-button",{attrs:{type:"primary",round:""},on:{click:function(t){return e.addExUserConfigPreB()}}},[e._v("广告衰减配置B")])],1)],1)],1)],1),e._v(" "),s("el-divider",{attrs:{"content-position":"left"}},[e._v("广告衰减配置A")]),e._v(" "),s("el-row",[s("el-col",{attrs:{span:24}},[s("el-table",{staticStyle:{width:"100%"},attrs:{data:e.configList,stripe:"","highlight-current-row":""}},[s("el-table-column",{attrs:{prop:"dist",label:"dist",width:"100px"}}),e._v(" "),s("el-table-column",{attrs:{prop:"name",label:"产品名称"}}),e._v(" "),s("el-table-column",{attrs:{label:"删除",width:"200px"},scopedSlots:e._u([{key:"default",fn:function(t){return[s("el-button",{attrs:{size:"small"},on:{click:function(s){return e.updateExUserConfigPre(t.row)}}},[e._v("删除")])]}}])})],1)],1)],1),e._v(" "),s("el-divider",{attrs:{"content-position":"left"}},[e._v("广告衰减配置B")]),e._v(" "),s("el-row",[s("el-col",{attrs:{span:24}},[s("el-table",{staticStyle:{width:"100%"},attrs:{data:e.configListB,stripe:"","highlight-current-row":""}},[s("el-table-column",{attrs:{prop:"dist",label:"dist",width:"100px"}}),e._v(" "),s("el-table-column",{attrs:{prop:"name",label:"产品名称"}}),e._v(" "),s("el-table-column",{attrs:{label:"删除",width:"200px"},scopedSlots:e._u([{key:"default",fn:function(t){return[s("el-button",{attrs:{size:"small"},on:{click:function(s){return e.updateExUserConfigPreB(t.row)}}},[e._v("删除")])]}}])})],1)],1)],1)],1)},staticRenderFns:[]}},754:function(e,t,s){var i=s(451);"string"==typeof i&&(i=[[e.i,i,""]]),i.locals&&(e.exports=i.locals);s(90)("624e4009",i,!0)}});