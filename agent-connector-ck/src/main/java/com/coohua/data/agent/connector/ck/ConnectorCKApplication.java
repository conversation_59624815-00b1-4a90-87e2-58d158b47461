package com.coohua.data.agent.connector.ck;

import com.coohua.data.agent.biz.db.service.EventTestService;
import com.coohua.data.agent.connector.ck.config.Log4j2ContextInitializer;
import com.ctrip.framework.apollo.spring.annotation.EnableApolloConfig;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.context.ConfigurableApplicationContext;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.context.annotation.ComponentScans;
//import top.zrbcool.pepper.boot.mybatis.EnableDataSource;


@SpringBootApplication
//@EnableCaching
//@EnableSwagger2
//@EnableSwaggerBootstrapUI
@ComponentScans(value = { @ComponentScan(value = "com.coohua.data.agent"),
        @ComponentScan(value = "com.coohua.data.agent.biz.db.service") })
public class ConnectorCKApplication {

    private final static Logger logger = LoggerFactory.getLogger(ConnectorCKApplication.class);

    public static void main(String[] args) {
        SpringApplication springApplication = new SpringApplication(ConnectorCKApplication.class);
        springApplication.addInitializers(new Log4j2ContextInitializer());
        ConfigurableApplicationContext configurableApplicationContext = springApplication.run();

        logger.info("success start ");
    }
}
