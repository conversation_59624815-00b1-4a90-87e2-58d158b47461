package com.coohua.data.agent.connector.ck.service.fus;

import lombok.extern.slf4j.Slf4j;

import java.util.List;
import java.util.concurrent.Future;
import java.util.concurrent.TimeUnit;

@Slf4j
public class FutureInfoPrinter {
    public static void printFuterInfo(List<Future<String>> resultList){
        //遍历任务的结果
        int completeNum = 0;
        int sleepNum = 0;
        try {
//            log.info(resultList.size()+" sleep 50 milseconds");
            TimeUnit.MILLISECONDS.sleep(50);
        }catch (Exception e){
            log.error("",e);
        }

        for (Future<String> fs : resultList){
            try{
                //40分钟
                while(!fs.isDone() && sleepNum<(40*60)/3){
                    TimeUnit.MILLISECONDS.sleep(30);
                    sleepNum++;
//                    log.info("fs 执行完成 睡眠 "+sleepNum+" 次");
                };//Future返回如果没有完成，则一直循环等待，直到Future返回完成
                completeNum++;
                log.info("fs 执行完成 剩余"+(resultList.size()-completeNum)+" 完成"+completeNum);   //打印各个线程（任务）执行的结果
            }catch(Exception e){
                log.error("",e);
            }
        }
    }
}
