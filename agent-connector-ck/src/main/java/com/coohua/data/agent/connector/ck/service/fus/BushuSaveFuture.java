package com.coohua.data.agent.connector.ck.service.fus;

import com.alibaba.fastjson.JSON;
import com.coohua.data.agent.biz.db.service.EventTestService;
import com.coohua.data.agent.biz.dto.req.CoreEventReq;
import com.coohua.data.agent.biz.dto.CoreEventDomain;
import com.coohua.data.agent.connector.ck.repository.CkRepositoryInf;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.Callable;
import java.util.concurrent.atomic.AtomicLong;

@Slf4j
public class BushuSaveFuture implements Callable<String> {

    private CkRepositoryInf bushuRepository;
    private AtomicLong count;
    private List<String> flist;
    EventTestService eventTestService;
    Map<String, Map<Integer,Integer>> deviceOldCheck;
    Map<String, Map<Integer,Integer>> userOldCheck;
    private boolean isTest;
    public BushuSaveFuture(CkRepositoryInf bushuRepository,EventTestService eventTestService,List<String> flist,AtomicLong count
            ,Map<String, Map<Integer,Integer>> deviceOldCheck
            ,Map<String, Map<Integer,Integer>> userOldCheck
                , boolean isTest){
        this.bushuRepository = bushuRepository;
        this.flist = flist;
        this.count = count;
        this.eventTestService = eventTestService;
        this.deviceOldCheck = deviceOldCheck;
        this.userOldCheck = userOldCheck;
        this.isTest = isTest;

    }
    public static final HashMap lockerEventSkipMap = new HashMap();
    static {
        lockerEventSkipMap.put("$AppStart","");
        lockerEventSkipMap.put("$AppEnd","");
        lockerEventSkipMap.put("login","");
        lockerEventSkipMap.put("register","");
        lockerEventSkipMap.put("jump_register","");
    }

    public static final List<String> adActionList = new ArrayList<String>(){{
        add("cosEr");
    }};

    @Override
    public String call() {
        try {
            List<CoreEventDomain> dlist = new ArrayList<>();
            List<CoreEventDomain> dRequestList = new ArrayList<>();
            for (String message : flist) {
                count.incrementAndGet();
                if (StringUtils.isNotEmpty(message)) {
                    CoreEventReq coreEventReq = JSON.parseObject(message, CoreEventReq.class);
                    CoreEventDomain coreEventDomain = new CoreEventDomain().fromCoreEventReq(coreEventReq,deviceOldCheck,userOldCheck,isTest);
                    boolean isSkip = "kaixincanting".equalsIgnoreCase(coreEventDomain.getProduct()) && "request".equalsIgnoreCase(coreEventDomain.getAd_action());
                    if (!isSkip && !lockerEventSkipMap.containsKey(coreEventDomain.getEvent())){
                        if ("AdData".equals(coreEventDomain.getEvent()) && adActionList.contains(coreEventDomain.getAd_action())) {
                            dRequestList.add(coreEventDomain);
                            dlist.add(coreEventDomain);
                        }else {
                            dlist.add(coreEventDomain);
                        }
                    }
                }
            }
            long dtime = System.currentTimeMillis();
            if (dlist.size() > 0) {
                bushuRepository.batchSave(dlist);
            }
            if (dRequestList.size() > 0) {
                eventTestService.saveByThread(dRequestList);
            }
            log.info(Thread.currentThread().getName()+"  "+dlist.size()+" success save cost "+(System.currentTimeMillis()-dtime));
        }catch (Exception e){
            log.error("",e);
        }
        return "";
    }
}
