package com.coohua.data.agent.connector.ck.config;

import de.siegmar.fastcsv.reader.CsvContainer;
import de.siegmar.fastcsv.reader.CsvReader;
import de.siegmar.fastcsv.reader.CsvRow;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.io.File;
import java.io.FileNotFoundException;
import java.nio.charset.StandardCharsets;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2024/10/30
 * @description TODO
 */
@Slf4j
public class GlobalOldUser {

    public Map<String, Map<Integer,Integer>> globalDeviceOldValue;
    public Map<String, Map<Integer,Integer>> globalUserOldValue;

    public GlobalOldUser() {
        try {
            readDeviceCsv();
            readUserCsv();
        } catch (Exception e) {
            log.error("GlobalOldUser-加载globalDeviceOldValue失败:{}", e.getMessage());
        }
        log.info("GlobalOldUser-加载globalDeviceOldValue成功，globalDeviceOldValue："+globalDeviceOldValue.size());
        log.info("GlobalOldUser-加载globalUserOldValue成功，globalUserOldValue："+globalUserOldValue.size());
    }

    public Map<String, Map<Integer,Integer>> getGlobalDeviceOldValue() {
        return globalDeviceOldValue;
    }

    public Map<String, Map<Integer,Integer>> getGlobalUserOldValue() {
        return globalUserOldValue;
    }

    public String getPath(String fileName) throws FileNotFoundException {
        String path = this.getClass().getClassLoader().getResource("").getPath();
        String filePath = path + fileName;
        return filePath;
    }

    public void readDeviceCsv() throws Exception{
        globalDeviceOldValue = new HashMap<>();
        log.debug("readCsv-开始时间"+new Date());
        //String path = new GlobalOldUser-().getPath("output_all_manu_device.csv");
        //测试使用本地
        //String path = getPath("output_all_manu_device.csv");
        String path = "/data/coohua/data-agentck/output_all_manu_device.csv";

        File file = new File(path);
        CsvReader csvReader = new CsvReader();
        CsvContainer csv = csvReader.read(file, StandardCharsets.UTF_8);
        for (CsvRow row : csv.getRows()) {
            if (row.getOriginalLineNumber() != 1) {
                String product = row.getField(0);
                String os = row.getField(1);
                String size = row.getField(2);
                String[] deviceHashArr = row.getField(3).replace("[", "").replace("]", "").split(",");
                Map<Integer,Integer> deviceMap = new HashMap<>();
                for(String str:deviceHashArr){
                    deviceMap.put(Integer.valueOf(str),0);
                }
                globalDeviceOldValue.put(product+"_"+os,deviceMap);
            }
        }
        log.debug("readCsv-结束时间"+new Date());
        log.debug("readCsv-globalDeviceOldValue:"+globalDeviceOldValue.size());
    }

    public void readUserCsv() throws Exception{
        globalUserOldValue = new HashMap<>();
        log.debug("readUserCsv-开始时间"+new Date());
        //String path = new GlobalOldUser().getPath("output_test.csv");
        //测试使用本地
        //String path = getPath("output_all_old_user.csv");
        String path = "/data/coohua/data-agentck/output_all_old_user.csv";

        File file = new File(path);
        CsvReader csvReader = new CsvReader();
        CsvContainer csv = csvReader.read(file, StandardCharsets.UTF_8);
        for (CsvRow row : csv.getRows()) {
            if (row.getOriginalLineNumber() != 1) {
                String product = row.getField(0);
                String os = row.getField(1);
                String size = row.getField(2);
                String[] deviceHashArr = row.getField(3).replace("[", "").replace("]", "").split(",");
                Map<Integer,Integer> userMap = new HashMap<>();
                for(String str:deviceHashArr){
                    userMap.put(Integer.valueOf(str),0);
                }
                globalUserOldValue.put(product+"_"+os,userMap);
            }
        }
        log.debug("readUserCsv-结束时间"+new Date());
        log.debug("readUserCsv-globalUserOldValue:"+globalUserOldValue.size());
    }

}
