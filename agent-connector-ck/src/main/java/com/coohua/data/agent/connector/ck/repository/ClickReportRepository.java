package com.coohua.data.agent.connector.ck.repository;

import com.coohua.data.agent.connector.ck.entity.ToutiaoClickCkEntity;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.stereotype.Component;
import ru.yandex.clickhouse.ClickHouseDataSource;
import ru.yandex.clickhouse.settings.ClickHouseProperties;

import javax.annotation.PreDestroy;
import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.SQLException;
import java.sql.Timestamp;
import java.util.List;
import java.util.Optional;

/**
 * <AUTHOR>
 * @since 2021/6/22
 */

@Slf4j
@Component
public class ClickReportRepository implements InitializingBean {

    private static final String USER = "default";
    private static final String PASSWORD = "Phw7a7A4";
    private static final String URL = "*********************************";
    private ClickHouseDataSource dataSource;
    private Connection connection;
    private PreparedStatement ps;
    public int batchCounter = 0;
    private static final String INSERT_AD_POINT
            = "INSERT INTO ods.toutiao_click_local (logday,id,dsp,product,os,account_id,ocpc_device_id,ts,account_name,callback_url,cid,gid,pid,oaid,aid_name,group_name,cid_name,activate_count,create_time,update_time,mac,pkg_channel,union_site,android_id,ip,ua,model,open_id,w_app_id,caid) values (?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)";

    @PreDestroy
    public void destroy() {
        if (ps != null) {
            try {
                ps.close();
            } catch (SQLException throwables) {
                log.error("", throwables);
            }
        }

        if (connection != null) {
            try {
                connection.close();
            } catch (SQLException throwables) {
                log.error("", throwables);
            }
        }

    }

    @Override
    public void afterPropertiesSet() throws Exception {
        Class.forName("ru.yandex.clickhouse.ClickHouseDriver");
        ClickHouseProperties properties = new ClickHouseProperties();
        properties.setUser(USER);
        properties.setPassword(PASSWORD);
        dataSource = new ClickHouseDataSource(URL, properties);
    }

    public void batchSave(List<ToutiaoClickCkEntity> toutiaoClickCkEntities) throws SQLException {
        if (connection == null) {
            connection = dataSource.getConnection();
        }
        if (ps == null) {
            ps = connection.prepareStatement(INSERT_AD_POINT);
        }
        for (ToutiaoClickCkEntity toutiaoClickCkEntity : toutiaoClickCkEntities) {
            addToBatch(toutiaoClickCkEntity);
        }
        long begin = System.currentTimeMillis();
        ps.executeBatch();
        log.info("[CK]保存数据成功! batchSize:{}, cost:{}mms", batchCounter, System.currentTimeMillis() - begin);
        batchCounter = 0;
    }

    public synchronized void executeBatch() throws SQLException {
        if (ps != null) {
            long begin = System.currentTimeMillis();
            ps.executeBatch();
            log.info("[CK-ToutiaoClick]保存数据成功! batchSize:{}, cost:{}mms", batchCounter, System.currentTimeMillis() - begin);
            batchCounter = 0;
        }
    }

    public synchronized  void addToBatch(ToutiaoClickCkEntity toutiaoClickCkEntity) throws SQLException {
        if (connection == null) {
            connection = dataSource.getConnection();
        }
        if (ps == null) {
            ps = connection.prepareStatement(INSERT_AD_POINT);
        }
        int colIndex = 1;
        ps.setDate(colIndex++, toutiaoClickCkEntity.getLogday());
        ps.setLong(colIndex++, toutiaoClickCkEntity.getId());
        ps.setString(colIndex++, toutiaoClickCkEntity.getDsp());
        ps.setString(colIndex++, toutiaoClickCkEntity.getProduct());
        ps.setString(colIndex++, toutiaoClickCkEntity.getOs());
        ps.setString(colIndex++, toutiaoClickCkEntity.getAccountId());
        ps.setString(colIndex++, toutiaoClickCkEntity.getOcpcDeviceId());
        ps.setString(colIndex++, toutiaoClickCkEntity.getTs());
        ps.setString(colIndex++, toutiaoClickCkEntity.getAccountName());
        ps.setString(colIndex++, toutiaoClickCkEntity.getCallbackUrl());
        ps.setString(colIndex++, toutiaoClickCkEntity.getCid());
        ps.setString(colIndex++, toutiaoClickCkEntity.getGid());
        ps.setString(colIndex++, toutiaoClickCkEntity.getPid());
        ps.setString(colIndex++, toutiaoClickCkEntity.getOaid());
        ps.setString(colIndex++, toutiaoClickCkEntity.getAidName());
        ps.setString(colIndex++, toutiaoClickCkEntity.getGroupName());
        ps.setString(colIndex++, toutiaoClickCkEntity.getCidName());
        ps.setInt(colIndex++, Optional.ofNullable(toutiaoClickCkEntity.getActivateCount()).orElse(0));
        ps.setTimestamp(colIndex++, new Timestamp(toutiaoClickCkEntity.getCreateTime().getTime()));
        ps.setTimestamp(colIndex++, new Timestamp(toutiaoClickCkEntity.getUpdateTime().getTime()));
        ps.setString(colIndex++, toutiaoClickCkEntity.getMac());
        ps.setString(colIndex++, toutiaoClickCkEntity.getPkgChannel());
        ps.setString(colIndex++, toutiaoClickCkEntity.getUnionSite());
        ps.setString(colIndex++, toutiaoClickCkEntity.getAndroidId());
        ps.setString(colIndex++, toutiaoClickCkEntity.getIp());
        ps.setString(colIndex++, toutiaoClickCkEntity.getUa());
        ps.setString(colIndex++, toutiaoClickCkEntity.getModel());
        ps.setString(colIndex++, toutiaoClickCkEntity.getOpenId());
        ps.setString(colIndex++, toutiaoClickCkEntity.getWAppId());
        ps.setString(colIndex++, toutiaoClickCkEntity.getCaid());
        ps.addBatch();
        batchCounter++;
    }
}
