package com.coohua.data.agent.connector.ck.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import java.util.Date;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import java.io.Serializable;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import org.apache.commons.lang3.StringUtils;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR>
 * @since 2020-07-13
 */
@Data
public class ToutiaoClick implements Serializable {

    private static final long serialVersionUID = 1L;

    private Long id;

    private String dsp;

    private String product;

    private String os;

    private String accountId;

    private String ocpcDeviceId;

    private String ts;

    private String accountName;

    private String callbackUrl;

    private String cid;

    private String gid;

    private String pid;

    private String oaid;

    private String aidName;

    private String groupName;

    private String cidName;

    private Integer activateCount;

    private Date createTime;

    private Date updateTime;

    private String mac;

    private String pkgChannel;

    private String unionSite;

    private String androidId;

    /**
     * 媒体投放系统获取的用户终端的公共IP地址
     */
    private String ip;

    /**
     * 用户代理(User Agent)，一个特殊字符串头，使得服务器能够识别客户使用的操作系统及版本、CPU类型、浏览器及版本、浏览器渲染引擎、浏览器语言、浏览器插件等。
     */
    private String ua;

    /**
     * 手机型号
     */
    private String model;
    private String openId;
    private String wAppId;
    private String caid;

}
