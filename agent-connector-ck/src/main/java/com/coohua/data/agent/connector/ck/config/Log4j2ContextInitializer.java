package com.coohua.data.agent.connector.ck.config;

import com.aliyun.openservices.log.log4j2.LoghubAppender;
import org.apache.logging.log4j.core.Appender;
import org.apache.logging.log4j.core.LoggerContext;
import org.apache.logging.log4j.core.config.Configuration;
import org.apache.logging.log4j.core.config.ConfigurationFactory;
import org.apache.logging.log4j.core.config.ConfigurationSource;
import org.springframework.beans.factory.config.YamlPropertiesFactoryBean;
import org.springframework.context.ApplicationContextInitializer;
import org.springframework.context.ConfigurableApplicationContext;
import org.springframework.core.io.ClassPathResource;
import org.springframework.util.ClassUtils;
import org.springframework.util.ResourceUtils;
import org.springframework.util.StringUtils;

import java.io.FileNotFoundException;
import java.io.IOException;
import java.io.InputStream;
import java.net.URL;
import java.util.Map;
import java.util.Properties;

/**
 * Created by <PERSON><PERSON> <PERSON>ian<PERSON> on 2018/8/16.
 */
public class Log4j2ContextInitializer implements ApplicationContextInitializer<ConfigurableApplicationContext> {
    private static final String FILE_PROTOCOL = "file";
    private static final String DEFAULT_LOG_LEVEL = "info";

    private ConfigurableApplicationContext applicationContext;

    private final String LOG_PATH = "app.logging.path";

    @Override
    public void initialize(ConfigurableApplicationContext configurableApplicationContext) {
        this.applicationContext = configurableApplicationContext;
        String path = this.applicationContext.getEnvironment().getProperty(LOG_PATH);
        if (StringUtils.isEmpty(path)) {
            YamlPropertiesFactoryBean yamlPropertiesFactoryBean = new YamlPropertiesFactoryBean();
            yamlPropertiesFactoryBean.setResources(new ClassPathResource("application.yml"));
            Properties properties = yamlPropertiesFactoryBean.getObject();
            if ( properties!= null && properties.getProperty(LOG_PATH, null) != null) {
                path =  properties.getProperty(LOG_PATH, null);
            }
        }
        if (StringUtils.isEmpty(path)) {
            System.out.println("no log path specialed.the log won't be printed.");
            return;
        }

        String level = this.applicationContext.getEnvironment().getProperty("app.logging.level");
        if (StringUtils.isEmpty(level)) {
            level = DEFAULT_LOG_LEVEL;
        }
        System.setProperty("app.logging.path", path);
        System.setProperty("app.logging.level", level);

        // caf log4j 配置文件
        String location = getPackagedConfigFile("log4j2-caf-pro.xml");
        if (!StringUtils.endsWithIgnoreCase(System.getProperty("env"), "pro")) {
            location = getPackagedConfigFile("log4j2-caf-test.xml");
        }

        // 优先使用 自定义 log4j2 配置文件
        try {
            if (StringUtils.endsWithIgnoreCase(System.getProperty("env"), "pro")) {
                String logConfigFilePath = "classpath:log4j2-caf-pro.xml";
                ResourceUtils.getURL(logConfigFilePath);
                location = logConfigFilePath;
                System.out.println("using custom log4j2 配置文件：log4j2-caf-pro.xml");
            } else {
                String logConfigFilePath = "classpath:log4j2-caf-test.xml";
                ResourceUtils.getURL(logConfigFilePath);
                location = logConfigFilePath;
                System.out.println("using custom log4j2 配置文件：log4j2-caf-test.xml");
            }
        } catch (FileNotFoundException ex) {
            System.out.println("no custom log4j2 config file [log4j2-caf-pro.xml or log4j2-caf-test.xml], using the caf default.");
        }

        try {
            LoggerContext loggerContext = LoggerContext.getContext(false);
            URL url = ResourceUtils.getURL(location);
            ConfigurationSource source = getConfigurationSource(url);
            Configuration logConfiguration = ConfigurationFactory.getInstance().getConfiguration(loggerContext, source);
            loggerContext.start(logConfiguration);
            Map<String, Appender> appenderMap = logConfiguration.getAppenders();
            for (Appender appender : appenderMap.values()) {
                if (appender instanceof LoghubAppender) {
                    LoghubAppender loghubAppender = (LoghubAppender) appender;
                    loghubAppender.setSource(System.getProperty("app.id"));
                }
            }
        } catch (Exception ex) {
            throw new IllegalStateException("Could not initialize Log4J2 logging from " + location, ex);
        }
    }

    protected final String getPackagedConfigFile(String fileName) {
        String defaultPath = ClassUtils.getPackageName(getClass());
        defaultPath = defaultPath.replace('.', '/');
        defaultPath = defaultPath + "/" + fileName;
        defaultPath = "classpath:" + defaultPath;
        return defaultPath;
    }

    private ConfigurationSource getConfigurationSource(URL url) throws IOException {
        InputStream stream = url.openStream();
        if (FILE_PROTOCOL.equals(url.getProtocol())) {
            return new ConfigurationSource(stream, ResourceUtils.getFile(url));
        }
        return new ConfigurationSource(stream, url);
    }
}