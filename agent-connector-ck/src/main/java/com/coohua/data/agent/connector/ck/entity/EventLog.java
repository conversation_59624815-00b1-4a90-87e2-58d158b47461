package com.coohua.data.agent.connector.ck.entity;

import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.util.Date;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR>
 * @since 2020-10-27
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class EventLog implements Serializable {

    private static final long serialVersionUID = 1L;

    private Date logday;

    private String product;

    private String event;

    private Integer hour;

    private Date sdTime;

    private String deviceId;

    private String userid;

    private String distinctId;

    private String nocache;

    private String lib;

    private String libMethod;

    private String libVersion;

    private String appVersion;

    private Integer isFirstDay;

    private String latestReferrer;

    private String latestReferrerHost;

    private String latestSearchKeyword;

    private String latestTrafficSourceType;

    private String os;

    private Integer screenHeight;

    private Integer screenWidth;

    private String channel;

    private String dpId;

    private String elementName;

    private String elementPage;

    private String elementUri;

    private String oaid;

    private String pageUrl;

    private String productPart;

    private Date sendTime;

    private String type;

    private String brand;

    private Date eventTime;

    private String gps;

    private String osVersion;

    private String jstest;

    private Integer minusSec;

    private String adType;

    private String adAction;

    private String pageName;

    private String adId;

    private String manufacturer;

    private String carrier;

    private String model;

    private Integer idDel;

    private String imei;

    private String adPage;


}
