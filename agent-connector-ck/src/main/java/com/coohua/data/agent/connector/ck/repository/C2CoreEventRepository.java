package com.coohua.data.agent.connector.ck.repository;

import com.coohua.data.agent.biz.dto.CoreEventDomain;
import com.coohua.data.agent.biz.util.ExposureCkUtil;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import ru.yandex.clickhouse.BalancedClickhouseDataSource;
import ru.yandex.clickhouse.settings.ClickHouseProperties;

import javax.annotation.PreDestroy;
import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.SQLException;
import java.util.List;

/**
 * 暂时不支持并发
 */
@Slf4j
@Component
public class C2CoreEventRepository implements InitializingBean {
//    private static final String TABLE_NAME = "ods.event_dist";
    private static final String USER = "default";
    private static final String PASSWORD = "Phw7a7A4";
//    private static final String URL = "*********************************";
    private static final String URL = "************************************,*************:8123,*************:8123,*************:8123,*************:8123,*************:8123";
    private static final String INSERT_SQL = "INSERT INTO ods.event_test (logday, product, event, hour, time, device_id, userid, distinct_id, nocache, lib, lib_method, lib_version, app_version, is_first_day, latest_referrer, latest_referrer_host, latest_search_keyword, latest_traffic_source_type, os, screen_height, screen_width, channel, dp_id, element_name, element_page, element_uri, oaid,android_id,mac, page_url, product_part, sendTime, type, brand, eventtime, gps, os_version,jstest,ad_action,ad_type,page_name,ad_id,manufacturer,carrier,model,minus,imei,ad_page,timestampClient,pos_id,strategy_id,sdk_version,ip,extend1,extend2,extend3,extend4,extend5,extend6,extend7,extend8,caid) values(?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)";
    private BalancedClickhouseDataSource dataSource;
    private Connection connection;
    private PreparedStatement ps;
    private int batchCounter = 0;
    private final int BATCH_SIZE = 20000;

    @PreDestroy
    public void destroy() {
        if (ps != null) {
            try {
                ps.close();
            } catch (SQLException throwables) {
                log.error("", throwables);
            }
        }

        if (connection != null) {
            try {
                connection.close();
            } catch (SQLException throwables) {
                log.error("", throwables);
            }
        }

    }

    @Override
    public void afterPropertiesSet() throws Exception {
        Class.forName("ru.yandex.clickhouse.ClickHouseDriver");
        ClickHouseProperties properties = new ClickHouseProperties();
        properties.setUser(USER);
        properties.setPassword(PASSWORD);
        dataSource = new BalancedClickhouseDataSource(URL, properties);
    }

    public void batchSave(List<CoreEventDomain> finalDomainList) throws SQLException {
        if (connection == null) {
            connection = dataSource.getConnection();
        }
        if (ps == null) {
            ps = connection.prepareStatement(INSERT_SQL);
        }
        for (CoreEventDomain eventDomain : finalDomainList) {
            addToBatch(eventDomain);
        }
        ps.executeBatch();
    }

    public synchronized void executeBatch() throws SQLException {
        if (ps != null && batchCounter > BATCH_SIZE) {
            long begin = System.currentTimeMillis();
            ps.executeBatch();
            log.info("C2 保存数据成功! batchSize:{}, cost:{}mms", batchCounter, System.currentTimeMillis() - begin);
            batchCounter = 0;
        }
    }

    public synchronized  void addToBatch(CoreEventDomain eventDomain) throws SQLException {
        if (connection == null) {
            connection = dataSource.getConnection();
        }
        if (ps == null) {
            ps = connection.prepareStatement(INSERT_SQL);
        }
        int colIndex = 1;
        ps.setDate(colIndex++, eventDomain.getLogday());
        ps.setString(colIndex++, eventDomain.getProduct());
        ps.setString(colIndex++, eventDomain.getEvent());
        ps.setInt(colIndex++, eventDomain.getHour());
        ps.setTimestamp(colIndex++, eventDomain.getTime());
        ps.setString(colIndex++, eventDomain.getDevice_id());
        ps.setString(colIndex++, eventDomain.getUserid());
        ps.setString(colIndex++, eventDomain.getDistinct_id());
        ps.setString(colIndex++, eventDomain.getNocache());
        ps.setString(colIndex++, eventDomain.getLib());
        ps.setString(colIndex++, eventDomain.getLib_method());
        ps.setString(colIndex++, eventDomain.getLib_version());
        ps.setString(colIndex++, eventDomain.getApp_version());
        ps.setInt(colIndex++, eventDomain.getIs_first_day());
        ps.setString(colIndex++, eventDomain.getLatest_referrer());
        ps.setString(colIndex++, eventDomain.getLatest_referrer_host());
        ps.setString(colIndex++, eventDomain.getLatest_search_keyword());
        ps.setString(colIndex++, eventDomain.getLatest_traffic_source_type());
        ps.setString(colIndex++, eventDomain.getOs());
        ps.setInt(colIndex++, eventDomain.getScreen_height());
        ps.setInt(colIndex++, eventDomain.getScreen_width());
        ps.setString(colIndex++, eventDomain.getChannel());
        ps.setString(colIndex++, eventDomain.getDp_id());
        ps.setString(colIndex++, eventDomain.getElement_name());
        ps.setString(colIndex++, eventDomain.getElement_page());
        ps.setString(colIndex++, eventDomain.getElement_uri());
        ps.setString(colIndex++, eventDomain.getOaid());
        ps.setString(colIndex++, eventDomain.getAndroid_id());
        ps.setString(colIndex++, eventDomain.getMac());
        ps.setString(colIndex++, eventDomain.getPage_url());
        ps.setString(colIndex++, eventDomain.getProduct_part());
        ps.setLong(colIndex++, eventDomain.getSendTime());
        ps.setString(colIndex++, eventDomain.getType());
        ps.setString(colIndex++, eventDomain.getBrand());
        ps.setLong(colIndex++, eventDomain.getEventtime());
        ps.setString(colIndex++, eventDomain.getGps());
        ps.setString(colIndex++, eventDomain.getOs_version());
        ps.setString(colIndex++, eventDomain.getJstest());
        ps.setString(colIndex++,eventDomain.getAd_action());
        ps.setString(colIndex++,eventDomain.getAd_type());
        ps.setString(colIndex++,eventDomain.getPage_name());
        ps.setString(colIndex++,eventDomain.getAd_id());
        ps.setString(colIndex++, eventDomain.getManufacturer());
        ps.setString(colIndex++, eventDomain.getCarrier());
        ps.setString(colIndex++, eventDomain.getModel());
        if(eventDomain.getMinus()!=null){
            ps.setString(colIndex++, eventDomain.getMinus().toString());
        }else{
            ps.setString(colIndex++, "0");
        }
        ps.setString(colIndex++, eventDomain.getImei());
        ps.setString(colIndex++, eventDomain.getAd_page());
        ps.setLong(colIndex++, eventDomain.getTimestampClient());
        ps.setString(colIndex++, eventDomain.getPos_id());
        ps.setInt(colIndex++, eventDomain.getStrategyId());
        ps.setString(colIndex++, eventDomain.getSdkVersion());
        ps.setString(colIndex++, eventDomain.getIp());
        ps.setString(colIndex++, eventDomain.getExtend1());
        ps.setString(colIndex++, eventDomain.getExtend2());
        ps.setString(colIndex++, eventDomain.getExtend3());
        ps.setString(colIndex++, eventDomain.getExtend4());
        ps.setString(colIndex++, eventDomain.getExtend5());
        ps.setString(colIndex++, eventDomain.getExtend6());
        ps.setString(colIndex++, eventDomain.getExtend7());
        ps.setString(colIndex++, eventDomain.getExtend8());
        ps.setString(colIndex++, eventDomain.getCaid());
        ps.addBatch();
        batchCounter++;
    }

    public static void main(String[] args) {
        C2CoreEventRepository repository = new C2CoreEventRepository();
        try {
            repository.afterPropertiesSet();
            repository.batchSave(Lists.newArrayList());
        } catch (Exception e) {
            log.error("",e);
        }

    }
}
