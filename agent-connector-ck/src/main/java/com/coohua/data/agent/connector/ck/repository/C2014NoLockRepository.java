package com.coohua.data.agent.connector.ck.repository;

import com.coohua.data.agent.biz.dto.CoreEventDomain;
import com.coohua.data.agent.biz.util.ExposureCkUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import ru.yandex.clickhouse.ClickHouseDataSource;
import ru.yandex.clickhouse.settings.ClickHouseProperties;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.SQLException;
import java.util.List;

@Slf4j
@Component
public class C2014NoLockRepository implements CkRepositoryInf {

    private static final String USER = "default";
    private static final String PASSWORD = "Phw7a7A4";
    //***********************************************************************
    private static final String URL = "***********************************************************************";
    public static final String INSERT_SQL = "INSERT INTO ods.event_test (logday, product, event, hour, time, device_id, userid, distinct_id, nocache, lib, lib_method, lib_version, app_version, is_first_day, latest_referrer, latest_referrer_host, latest_search_keyword, latest_traffic_source_type, os, screen_height, screen_width, channel, dp_id, element_name, element_page, element_uri, oaid,android_id,mac, page_url, product_part, sendTime, type, brand, eventtime, gps, os_version,jstest,ad_action,ad_type,page_name,ad_id,manufacturer,carrier,model,minus,imei,ad_page,timestampClient,pos_id,strategy_id,sdk_version,ip,extend1,extend2,extend3,extend4,extend5,extend6,extend7,extend8,caid,device_type) values(?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)";
    private static ClickHouseDataSource dataSource;
    private final int BATCH_SIZE = 5000;

    @Override
    public void batchSave(List<CoreEventDomain> finalDomainList) throws SQLException {
        long ctime = System.currentTimeMillis();
//        List<EventLog> dlist = new ArrayList();
//        if(finalDomainList.size()>0){
//            eventLogService.saveBatch(getEventLogs(finalDomainList));
//        }
        int batchCounter = 0;
        Connection connection = getConnection();
        PreparedStatement ps = null;
        try {
            ps = connection.prepareStatement(INSERT_SQL);
            for (CoreEventDomain eventDomain : finalDomainList) {
                setExposureFlag(eventDomain);
                addToBatch(eventDomain,connection,ps);
                batchCounter++;
            }
            long begin = System.currentTimeMillis();
            ps.executeBatch();
            log.info("保存数据成功! batchSize:{}, cost:{}mms 总共{}", batchCounter, System.currentTimeMillis() - begin, System.currentTimeMillis() - ctime);
        }catch (Exception e){
            log.error("",e);
        }finally {
            if(ps!=null){
                ps.close();
            }
            if(connection!=null){
                connection.close();
            }
        }
    }

    @Value("${ad.exposer.checkflag.log:true}")
    private Boolean checkLogFlag;

    public void setExposureFlag(CoreEventDomain eventDomain){
        try {
            if(StringUtils.equalsIgnoreCase("exposure",eventDomain.getAd_action()) && StringUtils.equalsIgnoreCase("android",eventDomain.getOs()) ){
                if(!StringUtils.isAllBlank(eventDomain.getExtend6(),eventDomain.getExtend7())){
                    Long ctime = Long.parseLong(eventDomain.getExtend6());
                    String mdsReq = eventDomain.getExtend7();


                    String e6 = ExposureCkUtil.getAbc(ctime);
                    String mds = ExposureCkUtil.getM(ctime,e6,Double.parseDouble(eventDomain.getExtend1()),eventDomain.getExtend5());
                    boolean ieq = StringUtils.equalsIgnoreCase(mds,mdsReq);
                    if(checkLogFlag && !ieq){
                        log.info("data校验数据 extend6:"+eventDomain.getExtend6() +" mdsReq:"+mdsReq+" e6:"+e6+" mds:"+mds+" ieq数据-"+ieq);
                    }
                    eventDomain.setExtend8(ieq+"");
                }
            }
        }catch (Exception e){
            log.error("",e);
        }
    }

    private Connection getConnection(){
        try {
            if(dataSource==null){
                synchronized (this.getClass()){
                    if(dataSource==null){
                        Class.forName("ru.yandex.clickhouse.ClickHouseDriver");
                        ClickHouseProperties properties = new ClickHouseProperties();
                        properties.setUser(USER);
                        properties.setPassword(PASSWORD);
                        dataSource = new ClickHouseDataSource(URL, properties);
                    }
                }
            }
            Connection conn= dataSource.getConnection();
            return  conn;
        }catch (Exception e){
            log.error("",e);
        }
        return null;
    }
    public void addToBatch(CoreEventDomain eventDomain, Connection connection, PreparedStatement ps) throws SQLException {
        try {
            int colIndex = 1;
            ps.setDate(colIndex++, eventDomain.getLogday());
            ps.setString(colIndex++, eventDomain.getProduct());
            ps.setString(colIndex++, eventDomain.getEvent());
            ps.setInt(colIndex++, eventDomain.getHour());
            ps.setTimestamp(colIndex++, eventDomain.getTime());
            ps.setString(colIndex++, eventDomain.getDevice_id());
            ps.setString(colIndex++, eventDomain.getUserid());
            ps.setString(colIndex++, eventDomain.getDistinct_id());
            ps.setString(colIndex++, eventDomain.getNocache());
            ps.setString(colIndex++, eventDomain.getLib());
            ps.setString(colIndex++, eventDomain.getLib_method());
            ps.setString(colIndex++, eventDomain.getLib_version());
            ps.setString(colIndex++, eventDomain.getApp_version());
            ps.setInt(colIndex++, eventDomain.getIs_first_day());
            ps.setString(colIndex++, eventDomain.getLatest_referrer());
            ps.setString(colIndex++, eventDomain.getLatest_referrer_host());
            ps.setString(colIndex++, eventDomain.getLatest_search_keyword());
            ps.setString(colIndex++, eventDomain.getLatest_traffic_source_type());
            ps.setString(colIndex++, eventDomain.getOs());
            ps.setInt(colIndex++, eventDomain.getScreen_height());
            ps.setInt(colIndex++, eventDomain.getScreen_width());
            ps.setString(colIndex++, eventDomain.getChannel());
            ps.setString(colIndex++, eventDomain.getDp_id());
            ps.setString(colIndex++, eventDomain.getElement_name());
            ps.setString(colIndex++, eventDomain.getElement_page());
            ps.setString(colIndex++, eventDomain.getElement_uri());
            ps.setString(colIndex++, eventDomain.getOaid());
            ps.setString(colIndex++, eventDomain.getAndroid_id());
            ps.setString(colIndex++, eventDomain.getMac());
            ps.setString(colIndex++, eventDomain.getPage_url());
            ps.setString(colIndex++, eventDomain.getProduct_part());
            ps.setLong(colIndex++, eventDomain.getSendTime());
            ps.setString(colIndex++, eventDomain.getType());
            ps.setString(colIndex++, eventDomain.getBrand());
            ps.setLong(colIndex++, eventDomain.getEventtime());
            ps.setString(colIndex++, eventDomain.getGps());
            ps.setString(colIndex++, eventDomain.getOs_version());
            ps.setString(colIndex++, eventDomain.getJstest());
            ps.setString(colIndex++,eventDomain.getAd_action());
            ps.setString(colIndex++,eventDomain.getAd_type());
            ps.setString(colIndex++,eventDomain.getPage_name());
            ps.setString(colIndex++,eventDomain.getAd_id());
            ps.setString(colIndex++, eventDomain.getManufacturer());
            ps.setString(colIndex++, eventDomain.getCarrier());
            ps.setString(colIndex++, eventDomain.getModel());
            if(eventDomain.getMinus()!=null){
                ps.setString(colIndex++, eventDomain.getMinus().toString());
            }else{
                ps.setString(colIndex++, "0");
            }
            ps.setString(colIndex++, eventDomain.getImei());
            ps.setString(colIndex++, eventDomain.getAd_page());
            ps.setLong(colIndex++, eventDomain.getTimestampClient());
            ps.setString(colIndex++, eventDomain.getPos_id());
            ps.setInt(colIndex++, eventDomain.getStrategyId());
            ps.setString(colIndex++, eventDomain.getSdkVersion());
            ps.setString(colIndex++, eventDomain.getIp());
            ps.setString(colIndex++, eventDomain.getExtend1());
            ps.setString(colIndex++, eventDomain.getExtend2());
            ps.setString(colIndex++, eventDomain.getExtend3());
            ps.setString(colIndex++, eventDomain.getExtend4());
            ps.setString(colIndex++, eventDomain.getExtend5());
            ps.setString(colIndex++, eventDomain.getExtend6());
            ps.setString(colIndex++, eventDomain.getExtend7());
            ps.setString(colIndex++, eventDomain.getExtend8());
            ps.setString(colIndex++, eventDomain.getCaid());
            ps.setInt(colIndex++, eventDomain.getDevice_type());
            ps.addBatch();
        }catch (Exception e){
            log.error("",e);
        }
    }
}
