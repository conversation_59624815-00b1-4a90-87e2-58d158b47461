package com.coohua.data.agent.connector.ck.mapper;

import com.coohua.data.agent.biz.dto.CoreEventDomain;
//import org.apache.ibatis.annotations.Insert;
//import org.apache.ibatis.annotations.InsertProvider;
//import org.apache.ibatis.annotations.Param;

@Deprecated
public interface CoreEventMapper {
//    @Insert("insert into `test_one1` (`product`,`event`,`nocache`,`distinct_id`,`lib`,`lib_method`," +
//            "`lib_version`,`app_version`,`device_id`,`is_first_day`,`latest_referrer`,`latest_referrer_host`," +
//            "`latest_search_keyword`,`latest_traffic_source_type`,`os`,`screen_height`,`screen_width`,`channel`,`dp_id`," +
//            "`element_name`,`element_page`,`element_uri`,`oaid`,`page_url`,`product_part`,`userid`,`sendTime`,`type`) values " +
//            "(#{product},#{event},#{nocache},#{distinct_id},#{lib},#{lib_method},#{lib_version},#{app_version}," +
//            "#{device_id},#{is_first_day},#{latest_referrer},#{latest_traffic_source_type},#{latest_search_keyword}," +
//            "#{latest_referrer_host},#{os},#{screen_height},#{screen_width},#{channel},#{dp_id},#{element_name}," +
//            "#{element_page},#{element_uri},#{oaid},#{page_url},#{product_part},#{userid},#{sendTime},#{type}")
//    @Insert("insert into `event_dist` (" +
//            "`product`,`event`,`nocache`,`distinct_id`,`lib`,`lib_method`," +
//            "`lib_version`,`app_version`,`device_id`,`is_first_day`,`latest_referrer`,`latest_referrer_host`," +
//            "`latest_search_keyword`,`latest_traffic_source_type`,`os`,`screen_height`,`screen_width`,`channel`,`dp_id`," +
//            "`element_name`,`element_page`,`element_uri`,`oaid`,`page_url`,`product_part`,`userid`,`sendTime`," +
//            "`brand`,`gps`,`os_version`,`hour`,`eventtime`,`logday`,`time`,`type`) values (" +
//            "#{product},#{event},#{nocache},#{distinct_id},#{lib},#{lib_method}," +
//            "#{lib_version},#{app_version},#{device_id},#{is_first_day},#{latest_referrer},#{latest_referrer_host}," +
//            "#{latest_search_keyword},#{latest_traffic_source_type},#{os},#{screen_height},#{screen_width},#{channel},#{dp_id}," +
//            "#{element_name},#{element_page},#{element_uri},#{oaid},#{page_url},#{product_part},#{userid},#{sendTime}," +
//            "#{brand},#{gps},#{os_version},#{hour},#{eventtime},#{logday},#{time},#{type})")
    int save(CoreEventDomain coreEventDomain);

//    @Insert("<script>"  +
//            "insert into `event_dist` (" +
//            "`product`,`event`,`nocache`,`distinct_id`,`lib`,`lib_method`," +
//            "`lib_version`,`app_version`,`device_id`,`is_first_day`,`latest_referrer`,`latest_referrer_host`," +
//            "`latest_search_keyword`,`latest_traffic_source_type`,`os`,`screen_height`,`screen_width`,`channel`,`dp_id`," +
//            "`element_name`,`element_page`,`element_uri`,`oaid`,`page_url`,`product_part`,`userid`,`sendTime`," +
//            "`brand`,`gps`,`os_version`,`hour`,`eventtime`,`logday`,`time`,`type`) values " +
//            "<foreach collection=\"list\" item=\"it\" index=\"index\" separator=\",\">" +
//            "(#{it.product},#{it.event},#{it.nocache},#{it.distinct_id},#{it.lib},#{it.lib_method}," +
//            "#{it.lib_version},#{it.app_version},#{it.device_id},#{it.is_first_day},#{it.latest_referrer},#{it.latest_referrer_host}," +
//            "#{it.latest_search_keyword},#{it.latest_traffic_source_type},#{it.os},#{it.screen_height},#{it.screen_width},#{it.channel},#{it.dp_id}," +
//            "#{it.element_name},#{it.element_page},#{it.element_uri},#{it.oaid},#{it.page_url},#{it.product_part},#{it.userid},#{it.sendTime}," +
//            "#{it.brand},#{it.gps},#{it.os_version},#{it.hour},#{it.eventtime},#{it.logday},#{it.time},#{it.type})" +
//            "</foreach>" +
//            "</script>")
//    int batchSave(@Param("list") List<CoreEventDomain> domains);

}
