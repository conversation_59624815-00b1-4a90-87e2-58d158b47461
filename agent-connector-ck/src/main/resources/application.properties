server.port = 8080
app.logging.path=logs/connector-ck
management.metrics.export.prometheus.pushgateway.enabled=true
management.metrics.export.prometheus.pushgateway.base-url=172.16.40.119:9091
management.metrics.export.prometheus.pushgateway.job=agent-customer
management.metrics.export.enabled=false
management.endpoints.web.exposure.include=*
management.metrics.export.jmx.enabled=true
management.metrics.tags.application=agent-customer
management.security.enabled=false

spring.datasource.driverClass = com.mysql.jdbc.Driver
spring.datasource.url = *************************************************************************? useUnicode=true&characterEncoding=utf8&useSSL=false&serverTimezone=CTT
spring.datasource.username = dispenseus
spring.datasource.password = yI2gbNL1PneDLscj
spring.datasource.type = com.alibaba.druid.pool.DruidDataSource
spring.datasource.maxActive = 1000
spring.datasource.initialSize = 20
spring.datasource.minIdle = 8
spring.datasource.maxWait = 2000
spring.datasource.timeBetween = 60000
spring.datasource.minEvictableIdle = 300000
spring.datasource.validationQuery = SELECT 'x'
spring.datasource.testWhileIdle = true
spring.datasource.keepAlive = true
spring.datasource.testOnBorrow = true
spring.datasource.testOnReturn = true
spring.datasource.poolPreparedStatements = true
spring.datasource.maxPoolPreparedStatementPerConnectionSize = 20
spring.datasource.removeAbandoned = false
spring.datasource.removeAbandonedTime = 18000
mybatis-plus.typeAliasesPackage = com.coohua.core.event.biz.db.mapper
mybatis-plus.configuration.log-impl = org.apache.ibatis.logging.stdout.StdOutImpl