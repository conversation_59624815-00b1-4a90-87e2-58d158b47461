server.port = 8080
app.logging.path=logs/connector-ck
#============== kafka ===================
spring.kafka.bootstrap-servers = alikafka-pre-cn-zvp2j2vhf007-1-vpc.alikafka.aliyuncs.com:9092,alikafka-pre-cn-zvp2j2vhf007-2-vpc.alikafka.aliyuncs.com:9092,alikafka-pre-cn-zvp2j2vhf007-3-vpc.alikafka.aliyuncs.com:9092

#=============== provider  =======================
spring.kafka.producer.retries = 2
spring.kafka.producer.batch-size = 16384
spring.kafka.producer.buffer-memory = 33554432

spring.kafka.producer.key-serializer = org.apache.kafka.common.serialization.StringSerializer
spring.kafka.producer.value-serializer = org.apache.kafka.common.serialization.StringSerializer

#=============== consumer  =======================
spring.kafka.consumer.group-id = data_agent_group

spring.kafka.consumer.auto-offset-reset = earliest
spring.kafka.consumer.enable-auto-commit = true
spring.kafka.consumer.auto-commit-interval = 100

spring.kafka.consumer.key-deserializer = org.apache.kafka.common.serialization.StringDeserializer
spring.kafka.consumer.value-deserializer = org.apache.kafka.common.serialization.StringDeserializer

app.db.ck-s1r1.data-source.stat.enabled = true
app.db.ck-s1r1.data-source.stat.logSlowSql = true
app.db.ck-s1r1.data-source.stat.slowSqlMillis = 10
app.db.ck-s1r1.data-source.url = ****************************************
app.db.ck-s1r1.data-source.username = default
app.db.ck-s1r1.data-source.password = Phw7a7A4
app.db.ck-s1r1.data-source.initial-size = 1
app.db.ck-s1r1.data-source.max-active = 1
app.db.ck-s1r1.data-source.min-idle = 1
app.db.ck-s1r1.data-source.max-wait = 6000
app.db.ck-s1r1.data-source.time-between-eviction-runs-millis = 60000
app.db.ck-s1r1.data-source.min-evictable-idle-time-millis = 300000
app.db.ck-s1r1.data-source.time-between-log-stats-millis = 300000
app.db.ck-s1r1.data-source.validation-query = SELECT 'x'
app.db.ck-s1r1.data-source.test-while-idle = true
app.db.ck-s1r1.data-source.test-on-borrow = false
app.db.ck-s1r1.data-source.test-on-return = false
app.db.ck-s1r1.type-aliases-package = com.coohua.data.agent.connector.ck.entity
app.db.ck-s1r1.data-source.filters = config
app.db.ck-s1r1.data-source.driverClassName = ru.yandex.clickhouse.ClickHouseDriver
