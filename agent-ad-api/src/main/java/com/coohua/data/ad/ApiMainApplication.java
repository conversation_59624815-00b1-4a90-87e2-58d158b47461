package com.coohua.data.ad;

import com.coohua.data.ad.config.Log4j2ContextInitializer;
import com.ctrip.framework.apollo.spring.annotation.EnableApolloConfig;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;


@SpringBootApplication
//@EnableCaching
//@EnableSwagger2
//@EnableSwaggerBootstrapUI
@EnableApolloConfig(value = {
        "application"
})
public class ApiMainApplication {
    private final static Logger logger = LoggerFactory.getLogger(ApiMainApplication.class);

    public static void main(String[] args) {
        SpringApplication springApplication = new SpringApplication(ApiMainApplication.class);
        springApplication.addInitializers(new Log4j2ContextInitializer());
        springApplication.run();
        logger.info("success start ");
    }
}
