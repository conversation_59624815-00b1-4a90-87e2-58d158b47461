package com.coohua.data.ad.api.controller;

import com.coohua.data.agent.biz.dto.rsp.ReturnResult;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.servlet.http.HttpServletRequest;
import java.util.Map;

@Controller
@Slf4j
public class DataController {
    @Autowired
    CoreEventController coreEventController;

    @RequestMapping(value = "/data/v1?project=mvp")
    @ResponseBody
    public ReturnResult upEventGet(@RequestParam Map<String, String> params, HttpServletRequest request) {
        return coreEventController.upEvent(params,request);
    }

    @RequestMapping(value = "/data/v1")
    @ResponseBody
    public ReturnResult dataupd(@RequestParam Map<String, String> params,HttpServletRequest request) {
        return coreEventController.upEvent(params,request);
    }

    @RequestMapping(value = "/ip")
    @ResponseBody
    public ReturnResult ip(@RequestParam Map<String, String> params,HttpServletRequest request) {
        return coreEventController.upEvent(params,request);
    }

}
