package com.coohua.data.ad.api.controller;

import com.coohua.data.ad.api.kafka.KafkaSender;
import com.coohua.data.agent.biz.dto.req.BusiAdReq;
import com.coohua.data.agent.biz.dto.rsp.ReturnResult;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.servlet.http.HttpServletRequest;

@Controller
@Slf4j
public class BusiAdController {
    @Autowired
    KafkaSender kafkaSender;
    @RequestMapping(value = "/data/busi/ad")
    @ResponseBody
    public ReturnResult upEventGet(BusiAdReq busiAdReq, HttpServletRequest request) {
        ReturnResult returnResult = new ReturnResult();
        kafkaSender.sendBusiEvent(busiAdReq);
        return returnResult;
    }
}
