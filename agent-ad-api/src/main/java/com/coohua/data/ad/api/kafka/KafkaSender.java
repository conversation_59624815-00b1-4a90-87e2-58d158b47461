package com.coohua.data.ad.api.kafka;

import com.alibaba.fastjson.JSON;
import com.coohua.data.agent.biz.dto.req.BusiAdReq;
import com.coohua.data.agent.biz.dto.req.CoreEventReq;
import com.pepper.metrics.integration.custom.Profile;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.kafka.clients.producer.KafkaProducer;
import org.apache.kafka.clients.producer.ProducerConfig;
import org.apache.kafka.clients.producer.ProducerRecord;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.stereotype.Component;

import java.util.Date;
import java.util.Properties;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicLong;
import java.util.regex.Matcher;
import java.util.regex.Pattern;


@Slf4j
@Component
public class KafkaSender implements InitializingBean {
    private KafkaProducer<String, String> producer;

    private AtomicLong counter = new AtomicLong(0);
    private AtomicLong counter2 = new AtomicLong(0);
    private String topic = "exposure";
    public static final String IOS_OS = "ios";

    public static Pattern pattern = Pattern.compile("[\u4e00-\u9fa5]");
    @Profile
    public void sendEvent(CoreEventReq coreEventReq){
        coreEventReq.setSendTime(new Date());
        /**
         * 过滤event为空的
         */
        if (StringUtils.isEmpty(coreEventReq.getEvent())) {
            return;
        }
        /**
         * product 去除中文
         */
        if (StringUtils.isNotBlank(coreEventReq.getProperties().getProduct())) {
            Matcher matcher = pattern.matcher(coreEventReq.getProperties().getProduct());
            String product = matcher.replaceAll("").replaceAll(" ", "");
            coreEventReq.getProperties().setProduct(product);
        }
        if (StringUtils.isNotBlank(coreEventReq.getProperties().$os)) {
            String lowerOs = coreEventReq.getProperties().$os.toLowerCase();
            if(lowerOs.contains(IOS_OS)){
                lowerOs = IOS_OS;
            }
            coreEventReq.getProperties().$os = lowerOs;
        }
        String jsonString = JSON.toJSONString(coreEventReq);
        try {
            ProducerRecord<String, String>  kafkaMessage = new ProducerRecord<>(topic, jsonString);
            producer.send(kafkaMessage, (recordMetadata, e) -> counter.incrementAndGet());
        }catch (Exception e){
            log.error("",e);
        }
    }


    @Profile
    public void sendBusiEvent(BusiAdReq busiAdReq){
        busiAdReq.setSendTime(new Date());
        String jsonString = JSON.toJSONString(busiAdReq);

        try {
            ProducerRecord<String, String>  kafkaMessage = new ProducerRecord<>("busi_ad", jsonString);
            producer.send(kafkaMessage, (recordMetadata, e) -> counter2.incrementAndGet());
        }catch (Exception e){
            log.error("",e);
        }
    }


    @Override
    public void afterPropertiesSet() throws Exception {
        Properties props = new Properties();
        //设置接入点，请通过控制台获取对应Topic的接入点。
        props.put(ProducerConfig.BOOTSTRAP_SERVERS_CONFIG, "alikafka-pre-cn-tl32lya4k00m-1-vpc.alikafka.aliyuncs.com:9092,alikafka-pre-cn-tl32lya4k00m-2-vpc.alikafka.aliyuncs.com:9092,alikafka-pre-cn-tl32lya4k00m-3-vpc.alikafka.aliyuncs.com:9092");
        //消息队列Kafka版消息的序列化方式。
        props.put(ProducerConfig.KEY_SERIALIZER_CLASS_CONFIG, "org.apache.kafka.common.serialization.StringSerializer");
        props.put(ProducerConfig.VALUE_SERIALIZER_CLASS_CONFIG, "org.apache.kafka.common.serialization.StringSerializer");
        //请求的最长等待时间。
        props.put(ProducerConfig.MAX_BLOCK_MS_CONFIG, 30 * 1000);
        props.put(ProducerConfig.RETRIES_CONFIG, 5);

        props.put(ProducerConfig.BATCH_SIZE_CONFIG, 80*1024);// 160kb
        props.put(ProducerConfig.LINGER_MS_CONFIG, 1000);// 当linger.ms>0时，延时性会增加，但会提高吞吐量，因为会减少消息发送频率
//        props.put(ProducerConfig.SEND_BUFFER_CONFIG, 5*128*1024);// 320kb
//        props.put(ProducerConfig.ACKS_CONFIG, "1");
        //构造Producer对象，注意，该对象是线程安全的。
        //一般来说，一个进程内一个Producer对象即可。如果想提高性能，可构造多个对象，但最好不要超过5个。
        this.producer = new KafkaProducer<>(props);

        new Thread(new Runnable() {
            @Override
            public void run() {
                while (true) {
                    try {
                        TimeUnit.SECONDS.sleep(20);
                        long sec = counter.getAndSet(0);
                        long sec2 = counter2.getAndSet(0);
                        log.info("MSG.SEC={} busiSec {}", sec,sec2);
                    } catch (InterruptedException e) {
                        log.error("", e);
                    }
                }
            }
        }).start();
    }
}
