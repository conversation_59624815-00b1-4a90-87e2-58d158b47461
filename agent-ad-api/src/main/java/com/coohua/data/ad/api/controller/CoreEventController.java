package com.coohua.data.ad.api.controller;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.coohua.data.ad.api.kafka.*;
import com.coohua.data.agent.biz.dto.req.CoreEventReq;
import com.coohua.data.agent.biz.dto.rsp.ReturnResult;
import com.coohua.data.agent.biz.service.MessageFormatUtils;
import com.ctrip.framework.apollo.spring.annotation.ApolloJsonValue;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;

@Controller
@RequestMapping("/data/agent")
@Slf4j
public class CoreEventController {

    @Value("${enable.log:false}")
    private Boolean enableLog;

    @Value("${enable.json.save:false}")
    private Boolean jsonSave;
    @ApolloJsonValue("${enable.user.log.list:[\"520937198\"]}")
    private List<String> userRequestList;


    @ApolloJsonValue("${enable.project.list:[\"ttddx\"]}")
    private List<String> projectsJsTest;
    @ApolloJsonValue("${enable.request.upload.list:[\"jiangnannongjia_1.0.3\"]}")
    private List<String> enableRequestList;
    @Autowired
    KafkaSender kafkaSender;

    @RequestMapping(value = "/upload", method = RequestMethod.HEAD)
    @ResponseBody
    public ReturnResult upEventHead() {
        return new ReturnResult();
    }


    @RequestMapping(value = "/upload", method = RequestMethod.GET)
    @ResponseBody
    public ReturnResult upEventGet(@RequestParam Map<String, String> params, HttpServletRequest request) {
        try {
            String data = params.get("data");
            if (StringUtils.isEmpty(data)) {
                return new ReturnResult();
            }
            String jsonTr = MessageFormatUtils.formatMessage(data);

            CoreEventReq coreEventReq = JSON.parseObject(jsonTr, CoreEventReq.class);
            coreEventReq.setProject(params.get("project"));
            String ip = getReomteIp(request);
            if(StringUtils.isNotBlank(ip)){
                coreEventReq.getProperties().setIp(ip);
            }
            if (enableLog) {
                coreEventReq.setJstest(JSONObject.toJSONString(params));
            }else if(jsonSave){
                coreEventReq.setJstest(jsonTr);
            }

            sendToKafKa(Objects.requireNonNull(coreEventReq));
        }catch (Exception e){
            log.error("",e);
        }
        return new ReturnResult();
    }


    @RequestMapping(value = "/upload", method = RequestMethod.POST)
    @ResponseBody
    public ReturnResult upEvent(@RequestParam Map<String, String> params, HttpServletRequest request) {
        try {
            String jsonTr = MessageNewFormatUtils.formatMessageForGizp(params.get("data_list"));
            if(StringUtils.isEmpty(jsonTr)){
                return new ReturnResult();
            }
            String ip = getReomteIp(request);
            List<CoreEventReq> coreEventReqs = JSONObject.parseArray(jsonTr, CoreEventReq.class);

            for (CoreEventReq coreEventReq : coreEventReqs) {
                coreEventReq.setProject(params.get("project"));
                if (coreEventReq.getProperties()!=null && projectsJsTest.contains(coreEventReq.getProperties().getProduct())) {
                    coreEventReq.setJstest(JSONObject.toJSONString(params));
                }
                if(coreEventReq.getProperties()!=null && StringUtils.isNotBlank(coreEventReq.getProperties().getUserId()) && userRequestList.contains(coreEventReq.getProperties().getUserId())){
                    log.info(JSON.toJSONString(coreEventReq));
                }
                if(StringUtils.isNotBlank(ip)){
                    coreEventReq.getProperties().setIp(ip);
                }
                sendToKafKa(Objects.requireNonNull(coreEventReq));
            }
        }catch (Exception e){
            log.error("",e);
        }
        return new ReturnResult();
    }

    public static String getReomteIp(HttpServletRequest request) {
        try {
            String ip = request.getHeader("x-forwarded-for");
            if (StringUtils.isNotBlank(ip) && !"unknown".equalsIgnoreCase(ip)) {
                //取第一个IP，即真实客户端 IP
                int index = ip.indexOf(",");
                if (index != -1) {
                    log.info("新域名获取的IP: {}", ip);
                    ip = ip.substring(0, index).trim();
                }
                return ip;
            }
            if (ip == null || ip.length() == 0 || "unknown".equalsIgnoreCase(ip)) {
                ip = request.getHeader("Proxy-Client-IP");
            }
            if (ip == null || ip.length() == 0 || "unknown".equalsIgnoreCase(ip)) {
                ip = request.getHeader("WL-Proxy-Client-IP");
            }
            if (ip == null || ip.length() == 0 || "unknown".equalsIgnoreCase(ip)) {
                ip = request.getRemoteAddr();
            }
            return ip;
        } catch (Exception e) {
            log.warn("Invoke ip exp:" + e);
        }
        return null;
    }


    private static final List<String> notSendList = new ArrayList<String>(){{
        add("request");
        add("config_cache_video_load_finish_fail");
        add("config_cache_splash_load_finish_fail");
        add("config_cache_image_load_finish_fail");
    }};

    private final static List<String> needProcessAdActionList = new ArrayList<String>(){{
        add("reward");
        add("click");
        add("exposure");
    }};

    private void sendToKafKa(CoreEventReq coreEventReq){
        long dtime = System.currentTimeMillis();
        if((System.currentTimeMillis()-dtime)>500){
            log.info("updateCounter cost "+(System.currentTimeMillis()-dtime));
        }
        // 过滤空
        if (StringUtils.isEmpty(coreEventReq.getEvent())){
            return;
        }

        if ("AdData".equals(coreEventReq.getEvent()) && notSendList.contains(coreEventReq.getProperties().getAd_action())){
            try {
                if (!"request".equals(coreEventReq.getProperties().getAd_action())){
                    return;
                }
                if (enableRequestList == null || enableRequestList.size() == 0){
                    return;
                }
                String product = coreEventReq.getProperties().getProduct();
                String appVer = coreEventReq.getProperties().$app_version;
                if (StringUtils.isNotEmpty(product) && StringUtils.isNotEmpty(appVer)){
                    String key = product + "_" + appVer;
                    if (!enableRequestList.contains(key)){
                        return;
                    }
                }else {
                    return;
                }
            }catch (Exception e){
                log.error("solve err:",e);
                return;
            }
        }

        kafkaSender.sendEvent(coreEventReq);
    }
}
