package com.coohua.data.ad.api.controller;

import com.coohua.data.agent.biz.dto.rsp.ReturnResult;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;

@Controller
@Slf4j
public class HealthController {
    @RequestMapping(value = "/health", method = RequestMethod.GET)
    @ResponseBody
    public ReturnResult health() {
        return new ReturnResult();
    }
    @RequestMapping(value = "/")
    @ResponseBody
    public ReturnResult healthNg() {
        return new ReturnResult();
    }
}
