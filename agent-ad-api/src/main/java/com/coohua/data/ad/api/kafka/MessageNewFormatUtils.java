package com.coohua.data.ad.api.kafka;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.codec.binary.Base64InputStream;
import org.apache.commons.io.IOUtils;
import org.apache.commons.lang3.StringUtils;


import java.io.ByteArrayInputStream;
import java.util.zip.GZIPInputStream;
import java.util.zip.ZipException;

/**
 * 解析原始信息，原始信息在nginx+lua入到kafka时，做了相关的压缩和base64，需要根据规则解析出来
 * 目前规则信息如下：
 * 1. nginx收到的由客户端或服务端上报过来的数据，都做了base64编码，但是不一定做压缩编码。 所以数据为 [gzip|not gzip] + base64
 * 2. 收到nginx数据后，lua 做了将上报数据拼接上头信息，然后整体做base64。规则为： base64(headersize+header+上报数据)
 * <p>
 * 所以数据最后的格式为 ： base64(headersize+header+base64([gizp|not gzip]))，解码的时候从外层到里层逐步解析就可以了
 * （panchao desc）
 */
@Slf4j
public class MessageNewFormatUtils {


    /**
     * 将message字符串封装成message对象
     *
     * @param msgOrigin
     * @return
     */
    public static String formatMessage(String msgOrigin) {

        GZIPInputStream unzipInput = null;
        Base64InputStream originBase64Input = null;
        try {
            ByteArrayInputStream originByteInput = new ByteArrayInputStream(msgOrigin.getBytes());
            originBase64Input = new Base64InputStream(originByteInput);
            //对原始message进行解码

            byte[] originByte = IOUtils.toByteArray(originBase64Input);
            //原始messge
            String originStr = new String(originByte);

            //header length
            Integer headerlength = Integer.valueOf(originStr.substring(0, 8));
            //header + body
            String exludeHeaderStr = originStr.substring(8);
            log.info("header + body : " + exludeHeaderStr);
            //header 数组
            byte[] headerBytes = new byte[headerlength];
            System.arraycopy(exludeHeaderStr.getBytes(), 0, headerBytes, 0, headerlength);
            //header数据
            String headerStr = new String(headerBytes);
            log.info("header : " + headerStr);

            //body数据,有可能是gzip压缩的,也有可能没有压缩,需要分别处理
            String bodyStr = originStr.substring(8 + headerStr.length());
            log.info("body : " + bodyStr);

            byte[] unzipBodyByte = null;
            Base64InputStream originBodyBase64NozipInput = null;
            try { // 假定数据为做了gzip
                ByteArrayInputStream originBodyInput = new ByteArrayInputStream(bodyStr.getBytes());
                //base64 解码
                Base64InputStream originBodyBase64GzipInput = new Base64InputStream(originBodyInput);
                // gzip 解压缩
                unzipInput = new GZIPInputStream(originBodyBase64GzipInput);
                //解压缩后的body数据
                unzipBodyByte = IOUtils.toByteArray(unzipInput);
            } catch (ZipException e) {//不是gzip格式
                try {
                    log.warn(bodyStr + " : " + e.getMessage());
                    ByteArrayInputStream originBodyInput = new ByteArrayInputStream(bodyStr.getBytes());
                    originBodyBase64NozipInput = new Base64InputStream(originBodyInput);
                    unzipBodyByte = IOUtils.toByteArray(originBodyBase64NozipInput);
                } catch (Exception ex) {//不是base64
                    log.error(bodyStr + " : 非gzip格式，不是BASE64格式!", ex);
                }
            } catch (Exception ex) {//不是base64的gzip格式
                log.error(bodyStr + " : 解析base64异常，不是BASE64格式!", ex);
            } finally {
                IOUtils.closeQuietly(originBodyBase64NozipInput);
                IOUtils.closeQuietly(unzipInput);

            }
            //原始body数据
            if (unzipBodyByte != null) {
                String unzipBodyStr = new String(unzipBodyByte);
                log.info("unzipBodyStr : " + unzipBodyStr);
                return unzipBodyStr;
            }
        } catch (Exception e) {
            log.error(e.getMessage(), e);
        } finally {
            IOUtils.closeQuietly(originBase64Input);

        }
        return null;
    }

    /**
     * 先base64解码，后zip解码
     * @param msg
     * @return
     */
    public static String formatMessageForGizp(String msg) {
    	if(StringUtils.isEmpty(msg)){
    		return null;
	    }
        byte[] unzipBodyByte = null;
        ByteArrayInputStream originBodyInput = null;
        Base64InputStream originBodyBase64GzipInput = null;
        GZIPInputStream gzipInputStream = null;
        Base64InputStream originBodyBase64NozipInput = null;
        try { // 假定数据为做了gzip
            originBodyInput = new ByteArrayInputStream(msg.getBytes());
            //base64 解码
            originBodyBase64GzipInput = new Base64InputStream(originBodyInput);
            // gzip 解压缩
            gzipInputStream = new GZIPInputStream(originBodyBase64GzipInput);
            //解压缩后的body数据
            unzipBodyByte = IOUtils.toByteArray(gzipInputStream);
        } catch (ZipException e) {//不是gzip格式
            try {
                originBodyInput = new ByteArrayInputStream(msg.getBytes());
                originBodyBase64NozipInput = new Base64InputStream(originBodyInput);
                unzipBodyByte = IOUtils.toByteArray(originBodyBase64NozipInput);
            } catch (Exception ex) {//不是base64
                log.error("formatMessageForGizp {}: 非gzip格式，不是BASE64格式!", msg, ex);
                return null;
            }
        } catch (Exception e) {
            log.error("formatMessageForGizp Exception msg {}",msg, e);
            return null;
        } finally {
            IOUtils.closeQuietly(gzipInputStream);
            IOUtils.closeQuietly(originBodyBase64GzipInput);
            IOUtils.closeQuietly(originBodyInput);
            IOUtils.closeQuietly(originBodyBase64NozipInput);

        }
        return new String(unzipBodyByte);
    }

    public static void main(String[] args){
        String ss = "H4sIAAAAAAAAAO1VzW7jNhB+F3mPXoEURf3dHGcD+LRBU6CHohAYkYpZy5RAUkm8ixzaSxc9tedeelhg28Neiy3avk29i75Fh3ScyImTB2gDCLA183E4P998+vJ1UFrNqkUpeVDkWZalCUnGgZVLERQ4iVOK8wxjhCMwrjowBh4fjAMujZWqsv5okMap4CTC/JQiRCgBAFOtWi3b3jyIaORpULwOnsFvuRR23jpc1XIBvmfeGUwU1y2c3xjKc6GNbBU4aBiFOIycg3XdwIHBjLZ4LiyTjY+6DI1QptWGM8tCplizsrIy8M/fEBq+CCenxpVnTzbIQ0BOjmejka/5xblQdqas0HB2NDppe12JI9mI0SiPgqtxIBzA5czdQUih020ntJXC+DIrprUUGhB/f3i//umvT+/+WH//i0u1NcMCsDMtoQ0u8ePDk88R2oB2+2EqLYQqLyS3cxgWytCtcS7k2RxyiWKEHu3QbkguzmUlHpyXj6OY40awfvsrPJ++/R2ej7+9eXRAjk6vWiXKtq6NgLSex9k2LcmvpzNnUjFleGgt55e+BUz1NUyj175pL4+PXzrzhaxlUFjdC3hRwl60elFek/OL2dHMUY+XcG6Th7jsWgMhvLlzTUQe4C7GUQYM94PifeVmt70cAB07cyE//vzn+v0P/3z34/rDN9eO1shNcIgkTcnbC9W0zMXz/k0usDTwupSqN4NVQnlOgSmNWAJXtle4YWxN1+2tWWNcylzUrG9s6aLDbe5A2WtHjCzNKU0B0huhZ+COaRbHcCvAPFXYzVyhzw5AYzrYSh+vlg3w+TNxti3nTj99r128wWCJS/cuz3CCb4w7jDwXxtVZLXylewcN7JD7CeeIYyxbdtNG+t0aSBLKxkE1Z0r5NanM1yvjA0asw2i1emVd3szTC+Nogg4m0xilOI4iMiHTg8mLbHJAYnqQkkOUZ1FNYkQYE7yu4lNSU5pDEqhOSZ26fXS6YMXZytPGEWjT3AdXZckqN6NpQeIC4WKKiqNpQQ9dJL4YNDMKURiHBHYl2a7QPSUEitVSQxs5WwWFZwaoTVk3vZmXu1Id0SyhV+MdWX8exTGlKCbpPWEnGJEnYX8S9v+esJMkTZPHhZ3uUfOBejt9uaPTwa2e5/jW22snX7dLhbL8vqA7y6UViuMhNsry7MYTDXR/YyF74t75BCTk//oJIChPnz4Bez8BydVX/wL/lJSM2wsAAA==";
        System.out.println(formatMessageForGizp(ss));
    }
}
