package com.shinet.core.alert.clickhouse.service;

import com.alibaba.excel.util.CollectionUtils;
import com.alibaba.excel.util.StringUtils;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Maps;
import com.shinet.core.alert.clickhouse.ProductRoiMinute;
import com.shinet.core.alert.clickhouse.dto.AdvertiserArpuInfo;
import com.shinet.core.alert.clickhouse.dto.ProductArpuAdjustInfo;
import com.shinet.core.alert.clickhouse.entity.*;
import com.shinet.core.alert.clickhouse.mapper.ck1.AdIncomePvQueryMapper;
import com.shinet.core.alert.clickhouse.mapper.ck1.RealtimeRiskMapper;
import com.shinet.core.alert.clickhouse.mapper.ck4.ClickHouse4DwdMapper;
import com.shinet.core.alert.coreservice.entity.AlertRecord;
import com.shinet.core.alert.coreservice.enums.*;
import com.shinet.core.alert.coreservice.service.AlertDelayJobService;
import com.shinet.core.alert.coreservice.service.AlertRecordService;
import com.shinet.core.alert.dingtalk.DingTailService;
import com.shinet.core.alert.dsp.entity.AuthToutiaoAdvertiser;
import com.shinet.core.alert.dsp.service.AuthToutiaoAdvertiserService;
import com.shinet.core.alert.dsp.service.ProductService;
import com.shinet.core.alert.util.DateUtils;
import com.xxl.job.core.log.XxlJobLogger;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import redis.clients.jedis.JedisCluster;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2022/12/8
 */
@Slf4j
@Service
public class RealtimeRiskService {

    @Resource
    private RealtimeRiskMapper realtimeRiskMapper;
    @Autowired
    private AlertRecordService alertRecordService;
    @Autowired
    private AlertDelayJobService alertDelayJobService;
    @Autowired
    private ClickHouse4DwdMapper clickHouse4DwdMapper;
    @Autowired
    private AdIncomePvQueryMapper adIncomePvQueryMapper;
    @Resource(name = "apJedisCluster")
    private JedisCluster apJedisCluster;
    @Autowired
    private AuthToutiaoAdvertiserService authToutiaoAdvertiserService;
    @Autowired
    private ProductService productService;

    private static final int PRODUCT_ARPU_ADJUST_EXPIRE = 60 * 60 * 24 * 3;;

    public void checkKsCallBackCount() {
        List<KsCallBackRate> filterBeanList = realtimeRiskMapper.queryKsCallBackRate();
        List<KsCallBackRate> callBackRateList = filterBeanList.stream()
                .filter(r -> alertDelayJobService.canSend(AlertJobDelayModel.AD_KS_CALL_BACK.getJobId() + "_" + r.getProduct()))
                .collect(Collectors.toList());
        if (callBackRateList.size() > 0) {
            List<String> productList = callBackRateList.stream().map(KsCallBackRate::getProduct).collect(Collectors.toList());
            String alert = callBackRateList.stream()
                    .map(r -> String.format("产品:%s,AdId：**%s**,ecpm:**%s**,三方回调:%s,我方:%s," +
                                    "GAP:**%s**", r.getProduct(), r.getAdId(), r.getMaxEcpm(),
                            r.getCallBack(), r.getOwnReward(), Math.floor(r.getNotCallBackRate())) + "%")
                    .collect(Collectors.joining(";\n\n"));
            AlertRecord alertRecord = alertRecordService.insertAlertRecord(AlertJobDelayModel.AD_KS_CALL_BACK.getJobId(),
                    AlertJobDelayModel.AD_KS_CALL_BACK.getJobName(), AlertModel.CALLBACK, alert
                    , AlertStatus.INIT, DingTailService.bsData, AlertType.DINGDING);
            DingTailService.sendMarkdownMsgDelay(alertRecord, AlertRoute.BUSINESS.value, AlertJobDelayModel.AD_KS_CALL_BACK, productList);
        }
    }

    public void checkChannelArpuAndCpa() {
        List<ProductChannelArpuCpa> productChannelArpuCpas = realtimeRiskMapper.queryArpuAndCpa();
        if (productChannelArpuCpas != null && productChannelArpuCpas.size() > 0) {
            List<String> productList = productChannelArpuCpas.stream().map(ProductChannelArpuCpa::getProduct).collect(Collectors.toList());
            String alert = productChannelArpuCpas.stream()
                    .map(r -> String.format("产品:%s,渠道：**%s**,arpu:**%s**,dnu:%s,cpa:%s", r.getProduct(), r.getChannel(), Math.floor(r.getArpu()),
                            r.getDnu(), Math.floor(r.getCpa())))
                    .collect(Collectors.joining(";\n\n"));
            AlertRecord alertRecord = alertRecordService.insertAlertRecord(AlertJobDelayModel.AD_ARPU_CAP.getJobId(),
                    AlertJobDelayModel.AD_ARPU_CAP.getJobName(), AlertModel.REALTIMECHANNEL, alert
                    , AlertStatus.INIT, DingTailService.bsData, AlertType.DINGDING);
            DingTailService.sendMarkdownMsgDelay(alertRecord, AlertRoute.BUSINESS.value, AlertJobDelayModel.AD_ARPU_CAP, productList);
        }
    }

    public void checkCpaLast() {
        List<ProductCpaHour> filterBeanList = realtimeRiskMapper.queryCpaLastHour();
        List<ProductCpaHour> callBackRateList = filterBeanList.stream()
                .filter(r -> alertDelayJobService.canSend(AlertJobDelayModel.CPA_HOUR_REALTIME.getJobId() + "_" + r.getProduct()))
                .collect(Collectors.toList());
        if (callBackRateList.size() > 0) {
            long pageSize = 4;
            for (int page = 0; page <= callBackRateList.size() / pageSize; page++) {
                buildAndSend(callBackRateList.stream().skip(page * pageSize).limit(pageSize).collect(Collectors.toList()));
            }
        }
    }

    private void buildAndSend(List<ProductCpaHour> callBackRateList) {
        if (callBackRateList == null || callBackRateList.size() == 0) {
            return;
        }
        List<String> productList = callBackRateList.stream().map(ProductCpaHour::getProduct).collect(Collectors.toList());
        String alert = callBackRateList.stream()
                .map(r -> String.format("产品:%s(**%s**-**%s**),前一个小时 \n\n" +
                                "今日Active:**%s**,Cost:**%s**,CPA:**%s**,ARPU:**%s**\n\n" +
                                "昨日Active:**%s**,Cost:**%s**,CPA:**%s**,ARPU:**%s**\n\n" +
                                "Active波动**%s**%%,CPA波动**%s**%%,ARPU波动**%s**%%",
                        r.getProduct(), r.getOs(), r.getDsp(),
                        r.getTodayActive(), format(r.getTodayCost()), format(r.getTodayCpa()), format(r.getTodayArpu()),
                        r.getYesterdayActive(), format(r.getYesterdayCost()), format(r.getYesterdayCpa()), format(r.getYesterdayArpu()),
                        format(r.getActiveRate() * 100), format(r.getCpaRate() * 100), format(r.getArpuRate() * 100)
                ))
                .collect(Collectors.joining(";\n\n"));
        Set<String> pushData = callBackRateList.stream().flatMap(r -> DingTailService.zengzhangPushMap.get(r.getProductGroup()).stream()).collect(Collectors.toSet());
        AlertRecord alertRecord = alertRecordService.insertAlertRecord(AlertJobDelayModel.CPA_HOUR_REALTIME.getJobId(),
                AlertJobDelayModel.CPA_HOUR_REALTIME.getJobName(), AlertModel.CPA_RATE, alert
                , AlertStatus.INIT, pushData, AlertType.DINGDING);
        DingTailService.sendMarkdownMsgDelay(alertRecord, AlertRoute.ZENGZHANG.value, AlertJobDelayModel.CPA_HOUR_REALTIME, productList);
    }

    public void checkAdvertiserArpuAndCpa() {
        List<ProductChannelArpuCpa> rts = realtimeRiskMapper.queryAdvertiserCpa();
        // ARPU 4以上
        List<ProductChannelArpuCpa> productChannelArpuCpas = rts.stream()
                .filter(r -> r.getIncome() / r.getActive() > 4.0d)
                .filter(r -> alertDelayJobService.canSend(AlertJobDelayModel.AD_ARPU_CAP.getJobId() + "_" + r.getProduct()))
                .collect(Collectors.toList());
        if (productChannelArpuCpas.size() > 0) {
            List<String> productList = productChannelArpuCpas.stream().map(ProductChannelArpuCpa::getProduct).collect(Collectors.toList());
            String alert = productChannelArpuCpas.stream()
                    .map(r -> String.format("产品:%s,账户：**%s**(%s),arpu:**%s**,active:%s,cpa:%s", r.getProduct(), r.getAdvertiserId(), r.getDsp(),
                            format(r.getIncome() / r.getActive()),
                            r.getActive(), format(r.getCost() / r.getActive())))
                    .collect(Collectors.joining(";\n\n"));
            AlertRecord alertRecord = alertRecordService.insertAlertRecord(AlertJobDelayModel.AD_ARPU_CAP.getJobId(),
                    AlertJobDelayModel.AD_ARPU_CAP.getJobName(), AlertModel.REALTIMECHANNEL, alert
                    , AlertStatus.INIT, DingTailService.bsData, AlertType.DINGDING);
            DingTailService.sendMarkdownMsgDelay(alertRecord, AlertRoute.BUSINESS.value, AlertJobDelayModel.AD_ARPU_CAP, productList);
        }
    }

    public void checkLockedRateRecord() {
        List<LockedRate> lockedRates = realtimeRiskMapper.queryYesterdayLockArea();
        if (lockedRates.size() > 0) {
            String alert = lockedRates.stream()
                    .map(r -> String.format("产品:%s,%s,昨日新增:%s,锁区命中ip个数:**%s**,锁区ip占比:%s", r.getProduct(), r.getOs(), r.getDnu(),
                            r.getLockedIp(), format(r.getLockedRate())) + "%")
                    .collect(Collectors.joining(";\n\n"));
            AlertRecord alertRecord = alertRecordService.insertAlertRecord("check-lock-rate", "锁区占比异常检查", AlertModel.LOCKED_RATE,
                    alert,
                    AlertStatus.INIT, DingTailService.bsData, AlertType.DINGDING);
            DingTailService.sendMarkdownMsgBs(alertRecord);
        }
    }

    public void queryTodayRealtimeLockIp() {
        List<LockedChannel> rts = realtimeRiskMapper.queryTodayRealtimeLockIp();
        List<LockedChannel> lockedChannels = rts.stream()
                .filter(r -> alertDelayJobService.canSend(AlertJobDelayModel.LOCKED_IP_RATE.getJobId() + "_" + r.getProduct()))
                .collect(Collectors.toList());
        if (lockedChannels.size() > 0) {
            List<String> productList = lockedChannels.stream().map(LockedChannel::getProduct).collect(Collectors.toList());
            String alert = lockedChannels.stream()
                    .map(r -> String.format("产品:%s,%s,渠道:%s,今日累积命中锁区IP:%s", r.getProduct(), r.getProductName(), r.getChannel(),
                            r.getLockedIp()))
                    .collect(Collectors.joining(";\n\n"));
            AlertRecord alertRecord = alertRecordService.insertAlertRecord(AlertJobDelayModel.LOCKED_IP_RATE.getJobId(),
                    AlertJobDelayModel.LOCKED_IP_RATE.getJobName(), AlertModel.LOCKED_RATE, alert
                    , AlertStatus.INIT, DingTailService.bsData, AlertType.DINGDING);
            DingTailService.sendMarkdownMsgDelay(alertRecord, AlertRoute.SUOQU.value, AlertJobDelayModel.LOCKED_IP_RATE, productList);
        }
    }

    public void checkVideoFailedCount() {
        List<String> productSkipList = new ArrayList<>();
        productSkipList.add("waxxx2");
        productSkipList.add("zmcash");
        productSkipList.add("qztyrj2");
        productSkipList.add("kxxxx2");
        productSkipList.add("qzwztyygj");
        List<FailedRequestCount> failedRequestCounts = realtimeRiskMapper.queryLast60MinusFailedRequestCount();
        if (failedRequestCounts != null && failedRequestCounts.size() > 0) {
            List<FailedRequestCount> endList = failedRequestCounts.stream()
                    .filter(r -> !productSkipList.contains(r.getProduct()))
                    .filter(r -> alertDelayJobService.canSend(AlertJobDelayModel.FAILED_REQUEST.getJobId() + "_" + r.getProduct()))
                    .collect(Collectors.toList());
            if (endList.size() > 0) {
                List<String> productList = endList.stream().map(FailedRequestCount::getProduct).collect(Collectors.toList());
                String alert = endList.stream()
//                        .map(r -> String.format("产品:%s(%s),过去三小时 失败设备数:**%s**,锁区设备数:**%s**，锁区用户数:**%s**"
                        .map(r -> String.format("产品:%s(%s),os:**%s**,过去三小时 dau:**%s**, 视频总请求总次数:**%s**,成功总次数:**%s**,失败总次数:**%s**,失败占总请求次数占比:**%s**,失败总设备数:**%s**, 锁区设备数:**%s**，锁区用户数:**%s**"
                                , r.getProductName(), r.getProduct(), r.getOs(),
                                r.getDau(), r.getAllCount(), r.getSucCount(), r.getFailCount(), r.getCeilP(r.getRate()), r.getFailUv(),
                                r.getLockedDau(), r.getLockFailUserids()))
                        .collect(Collectors.joining(";\n\n"));
                AlertRecord alertRecord = alertRecordService.insertAlertRecord(AlertJobDelayModel.FAILED_REQUEST.getJobId(),
                        AlertJobDelayModel.FAILED_REQUEST.getJobName(), AlertModel.REALTIME_REQUEST_FAILED, alert
                        , AlertStatus.INIT, DingTailService.bsData, AlertType.DINGDING);
                DingTailService.sendMarkdownMsgDelay(alertRecord, AlertRoute.BUSINESS.value, AlertJobDelayModel.FAILED_REQUEST, productList);

                List<FailedRequestCount> endListRs = endList.stream()
                        .filter(failedRequestCount -> failedRequestCount.getRate() > 8.5d)
                        .collect(Collectors.toList());
                if (endListRs.size() > 0) {
                    alertRecord.setAlertPhone(DingTailService.dset.stream().collect(Collectors.joining(",")));
                    DingTailService.sendMarkdownMsg(alertRecord);
                }
            }
        }
    }

    public void chekNewUserChannelSource() {
        List<UserActiveChannel> userActiveChannelList = realtimeRiskMapper.queryUserActiveChannel();
        if (userActiveChannelList != null && userActiveChannelList.size() > 0) {
            List<UserActiveChannel> endList = userActiveChannelList.stream()
                    .filter(r -> {
                        if (r.getChannel().startsWith("gdt")) {
                            return r.getNtfRate() > 50;
                        }
                        return true;
                    })
                    .filter(r -> alertDelayJobService.canSend(AlertJobDelayModel.USER_ACTIVE.getJobId() + "_" + r.getProduct()))
                    .collect(Collectors.toList());
            if (endList.size() > 0) {
                List<String> productList = endList.stream().map(UserActiveChannel::getProduct).collect(Collectors.toList());
                String alert = endList.stream()
                        .map(r -> String.format("产品:%s(%s),渠道:**%s**,累计自然量新增占比过高:**%s**,自然量:**%s**,累计新增:**%s**", r.getProductName(), r.getProduct(), r.getChannel(),
                                format(r.getNtfRate()), r.getNtfUser(), r.getDnu()))
                        .collect(Collectors.joining(";\n\n"));
                AlertRecord alertRecord = alertRecordService.insertAlertRecord(AlertJobDelayModel.USER_ACTIVE.getJobId(),
                        AlertJobDelayModel.USER_ACTIVE.getJobName(), AlertModel.NORMAL, alert
                        , AlertStatus.INIT, DingTailService.bsData, AlertType.DINGDING);
                DingTailService.sendMarkdownMsgDelay(alertRecord, AlertRoute.BUSINESS.value, AlertJobDelayModel.USER_ACTIVE, productList);
            }
        }
    }

    public void checkUserActiveNum() {
        List<UserActiveNum> userActiveNumList = realtimeRiskMapper.queryUserActiveNum();
        if (userActiveNumList != null && userActiveNumList.size() > 0) {
            List<UserActiveNum> endList = userActiveNumList.stream()
                    .filter(r -> !"yyaccbyte".equals(r.getProduct()))
                    .filter(r -> alertDelayJobService.canSend(AlertJobDelayModel.USER_ACTIVE_NUM.getJobId() + "_" + r.getProduct()))
                    .collect(Collectors.toList());
            if (endList.size() > 0) {
                List<String> productList = endList.stream().map(UserActiveNum::getProduct).collect(Collectors.toList());
                String alert = endList.stream()
                        .map(r -> String.format("产品:%s(%s-%s),昨日埋点新增:**%s**,微信注册:**%s**", r.getProductName(), r.getProduct(), r.getOs(), r.getMdDnu(), r.getWechatDnu()))
                        .collect(Collectors.joining(";\n\n"));
                AlertRecord alertRecord = alertRecordService.insertAlertRecord(AlertJobDelayModel.USER_ACTIVE_NUM.getJobId(),
                        AlertJobDelayModel.USER_ACTIVE_NUM.getJobName(), AlertModel.NORMAL_USER_ACTIVE_NUM, alert
                        , AlertStatus.INIT, DingTailService.bsData, AlertType.DINGDING);
                DingTailService.sendMarkdownMsgDelay(alertRecord, AlertRoute.BUSINESS.value, AlertJobDelayModel.USER_ACTIVE_NUM, productList);
            }
        }
    }

    public void checkAliyunChannelEx() {
        List<AliyunChannelExBean> channelExBeans = realtimeRiskMapper.queryAliyunChannelEx();
        if (channelExBeans != null && channelExBeans.size() > 0) {
            List<AliyunChannelExBean> endList = channelExBeans.stream()
                    .filter(r -> alertDelayJobService.canSend(AlertJobDelayModel.USER_ACTIVE_ALI.getJobId() + "_" + r.getProduct()))
                    .collect(Collectors.toList());
            if (endList.size() > 0) {
                List<String> productList = endList.stream().map(AliyunChannelExBean::getProduct).collect(Collectors.toList());
                String alert = endList.stream()
                        .map(r -> String.format("产品:%s(%s-%s),今日Aliyun渠道活跃设备:**%s**", r.getProductName(), r.getProduct(), r.getProductGroup(), r.getRc()))
                        .collect(Collectors.joining(";\n\n"));
                AlertRecord alertRecord = alertRecordService.insertAlertRecord(AlertJobDelayModel.USER_ACTIVE_ALI.getJobId(),
                        AlertJobDelayModel.USER_ACTIVE_ALI.getJobName(), AlertModel.NORMAL, alert
                        , AlertStatus.INIT, DingTailService.bsData, AlertType.DINGDING);
                DingTailService.sendMarkdownMsgDelay(alertRecord, AlertRoute.BUSINESS.value, AlertJobDelayModel.USER_ACTIVE_ALI, productList);
            }
        }
    }

    private static String format(Double value) {
        BigDecimal bd = new BigDecimal(value);
        bd = bd.setScale(2, RoundingMode.HALF_UP);
        return bd.toString();
    }

    public void checkGroup9Payment() {
        String jobName = "IOS支付占比异常";
        List<PaymentRate> paymentRates = realtimeRiskMapper.queryPaymentRate();
        if (paymentRates != null && paymentRates.size() > 0) {
            String dmsg = paymentRates.stream()
                    .map(r -> String.format("产品:%s(%s),今日支付:**%s**,IOS支付:**%s**,占比:**%s**", r.getProductName(), r.getProduct(),
                            format(r.getPay()),
                            format(r.getIosPay()),
                            format(r.getIosRate() * 100)) + "%")
                    .collect(Collectors.joining(";\n\n"));
            AlertRecord alertRecord = alertRecordService.insertAlertRecord(jobName,
                    jobName, AlertModel.PAYMENT_RATE, dmsg, AlertStatus.DONT_SOLVE, DingTailService.group9, AlertType.DINGDING);
            DingTailService.sendMarkdownMsg(alertRecord, AlertRoute.GROUP9_PAY.value);
        }
    }

    public void checkRoiLast() {
        //查询需要报警的数据
        List<ProductRoiMinute> productRoiMinuteList = realtimeRiskMapper.queryProductRoiMinute();
        log.info("roi-realtime-hour-alert 查询结果 productRoiMinuteList : {}", JSONObject.toJSONString(productRoiMinuteList));
        if (productRoiMinuteList.isEmpty()) {
            return;
        }
        //按照group进行拆分
        Map<String, List<ProductRoiMinute>> groupByProductMap = productRoiMinuteList.stream().collect(Collectors.groupingBy(ProductRoiMinute::getProductGroup));
        if (groupByProductMap.isEmpty()) {
            return;
        }
        Set<Map.Entry<String, List<ProductRoiMinute>>> entries = groupByProductMap.entrySet();
        for (Map.Entry<String, List<ProductRoiMinute>> entry : entries) {
            buildAndSend4Roi(entry.getValue());
        }


    }


    private void buildAndSend4Roi(List<ProductRoiMinute> callBackRateList) {
        if (callBackRateList == null || callBackRateList.isEmpty()) {
            return;
        }
        List<String> productList = callBackRateList.stream().map(ProductRoiMinute::getProduct).collect(Collectors.toList());
        String alert = callBackRateList.stream()
                .map(r -> String.format("产品:%s(**%s**-**%s**) \n\n" +
                                "今日Active:**%s**,Cost:**%s**,CPA:**%s**,ARPU:**%s**,首日ROI:**%s**\n\n" +
                                "昨日Active:**%s**,Cost:**%s**,CPA:**%s**,ARPU:**%s**,首日ROI:**%s**\n\n" +
                                "Active波动**%s**%%,CPA波动**%s**%%,ARPU波动**%s**%%，ROI波动**%s**%%",
                        r.getProduct(), r.getOs(), r.getDsp(),
                        r.getTodayActive(), format(r.getTodayCost()), format(r.getTodayCpa()), format(r.getTodayArpu()), format(r.getTodayRoi()),
                        r.getYesterdayActive(), format(r.getYesterdayCost()), format(r.getYesterdayCpa()), format(r.getYesterdayArpu()), format(r.getYesterdayRoi()),
                        format((Double.valueOf(r.getTodayActive()) - Double.valueOf(r.getYesterdayActive())) / Double.valueOf(r.getYesterdayActive()) * 100),
                        format((r.getTodayCpa() - r.getYesterdayCpa()) / r.getYesterdayCpa() * 100),
                        format((r.getTodayArpu() - r.getYesterdayArpu()) / r.getYesterdayArpu() * 100),
                        format((r.getTodayRoi() - r.getYesterdayRoi()) / r.getYesterdayRoi() * 100)
                ))
                .collect(Collectors.joining(";\n\n"));
        Set<String> pushData = callBackRateList.stream().flatMap(r -> DingTailService.roiAlertPushMap.get(r.getProductGroup()).stream()).collect(Collectors.toSet());
        AlertRecordService alertRecordService1 = new AlertRecordService();
        AlertRecord alertRecord = alertRecordService1.insertAlertRecord(AlertJobDelayModel.ROI_FLUCTUATE_EXCEPTION.getJobId(),
                AlertJobDelayModel.ROI_FLUCTUATE_EXCEPTION.getJobName(), AlertModel.ROI_FLUCTUATE_ALERT, alert
                , AlertStatus.INIT, pushData, AlertType.DINGDING);
        DingTailService.sendMarkdownMsgDelay(alertRecord, AlertRoute.ZENGZHANG.value, AlertJobDelayModel.ROI_FLUCTUATE_EXCEPTION, productList);
    }

    public void checkCostHourLast() {
        Integer beforeHour = DateUtils.getBeforeHour(-1);

        //查询需要报警的数据
        List<ProductCostHour> productCostHoursList = realtimeRiskMapper.queryProductCostHour(beforeHour);
        log.info("realtime-hour-cost 查询结果 productCostHoursList : {}", JSONObject.toJSONString(productCostHoursList));
        if (productCostHoursList.isEmpty()) {
            return;
        }
        if (productCostHoursList.size() > 0) {
            long pageSize = 4;
            for (int page = 0; page <= productCostHoursList.size() / pageSize; page++) {
                buildAndSendCostHour(productCostHoursList.stream().skip(page * pageSize).limit(pageSize).collect(Collectors.toList()));
            }
        }

    }

    private void buildAndSendCostHour(List<ProductCostHour> callBackRateList) {
        if (callBackRateList == null || callBackRateList.size() == 0) {
            return;
        }
        List<String> productList = callBackRateList.stream().map(ProductCostHour::getProduct).collect(Collectors.toList());
        String alert = callBackRateList.stream()
                .map(r -> String.format("(%s)产品:%s(**%s**) \n\n" +
                                "今日消耗:**%s**,昨日消耗:**%s**,掉量百分比:**%s**,昨日总消耗:**%s**",
                        r.getProductGroup(), r.getProduct(), r.getDsp()
                        , format(r.getTodayCost()), format(r.getYestodayCost()), format(r.getRate() * 100), format(r.getYestodayAllCost())
                ))
                .collect(Collectors.joining(";\n\n"));
        Set<String> pushData = callBackRateList.stream().flatMap(r -> DingTailService.zengzhangPushMap.get(r.getProductGroup()).stream()).collect(Collectors.toSet());
        AlertRecord alertRecord = alertRecordService.insertAlertRecord(AlertJobDelayModel.REALTIME_COST_HOUR.getJobId(),
                AlertJobDelayModel.REALTIME_COST_HOUR.getJobName(), AlertModel.REALTIME_COST_HOUR, alert
                , AlertStatus.INIT, pushData, AlertType.DINGDING);
        DingTailService.sendMarkdownMsgDelay(alertRecord, AlertRoute.ZENGZHANG.value, AlertJobDelayModel.REALTIME_COST_HOUR, productList);
        //DingTailService.sendMarkdownMsgDelay(alertRecord, AlertRoute.SJWT.value,AlertJobDelayModel.REALTIME_COST_HOUR,productList);
    }


    //        @PostConstruct
    public void grayUserDeviceSumHourAlert() {
        try {
            List<GrayUserDeviceSum> userDeviceSumHourList = clickHouse4DwdMapper.queryGrayUserHour();
            XxlJobLogger.log("今日新增数据结果：", JSON.toJSON(userDeviceSumHourList));

            if (userDeviceSumHourList.size() > 0) {

                String alert = userDeviceSumHourList.stream()
                        .map(r -> String.format("今日拉灰用户总数：**%s** \n\n" +
                                                "上小时拉灰用户数：**%s** \n\n" +
                                                "今日拉黑设备总数：**%s** \n\n" +
                                                "上小时拉黑设备数：**%s** \n\n" +
                                                "今日拉黑用户总数：**%s** \n\n" +
                                                "上小时拉黑用户数：**%s** \n\n" +
                                                "上小时拉黑数超20的规则： **%s**  ",
                                        r.getUserHuiToday(), r.getUserHuiHour(), r.getDeviceNumToday(), r.getDeviceNumHour(), r.getUserNumToday(), r.getUserNumHour(), r.getRemarksHour()
                                )
                        )
                        .collect(Collectors.joining(";\n\n"));

                AlertRecord alertRecord = alertRecordService.insertAlertRecord(AlertJobDelayModel.TODAY_GRAY_USER_IP_SUM_ALERT.getJobId(),
                        AlertJobDelayModel.TODAY_GRAY_USER_IP_SUM_ALERT.getJobName(), AlertModel.GRAY_EX_SUM, alert
                        , AlertStatus.INIT, DingTailService.bsData, AlertType.DINGDING);
                //生产
                DingTailService.sendMarkdownMsg(alertRecord, AlertRoute.FZB.value);
            }
        } catch (Exception e) {
            XxlJobLogger.log("CheckEx:", e);
        }
    }


    /**
     * 获取昨天晚上23时的数据
     */
    public void grayUserDeviceSumHourYesAlert() {
        try {
            List<GrayUserDeviceSum> userDeviceSumHourList = clickHouse4DwdMapper.queryGrayUserHourYes();
            XxlJobLogger.log("今日新增数据结果：", JSON.toJSON(userDeviceSumHourList));

            if (userDeviceSumHourList.size() > 0) {

                String alert = userDeviceSumHourList.stream()
                        .map(r -> String.format("昨日拉灰用户总数：**%s** \n\n" +
                                                "上小时拉灰用户数：**%s** \n\n" +
                                                "昨日拉黑设备总数：**%s** \n\n" +
                                                "上小时拉黑设备数：**%s** \n\n" +
                                                "昨日拉黑用户总数：**%s** \n\n" +
                                                "上小时拉黑用户数：**%s** \n\n" +
                                                "上小时拉黑数超20的规则： **%s**  ",
                                        r.getUserHuiToday(), r.getUserHuiHour(), r.getDeviceNumToday(), r.getDeviceNumHour(), r.getUserNumToday(), r.getUserNumHour(), r.getRemarksHour()
                                )
                        )
                        .collect(Collectors.joining(";\n\n"));

                AlertRecord alertRecord = alertRecordService.insertAlertRecord(AlertJobDelayModel.TODAY_GRAY_USER_IP_SUM_ALERT.getJobId(),
                        AlertJobDelayModel.TODAY_GRAY_USER_IP_SUM_ALERT.getJobName(), AlertModel.GRAY_EX_SUM, alert
                        , AlertStatus.INIT, DingTailService.bsData, AlertType.DINGDING);
                //生产
                DingTailService.sendMarkdownMsg(alertRecord, AlertRoute.FZB.value);
            }
        } catch (Exception e) {
            XxlJobLogger.log("CheckEx:", e);
        }
    }


    //    @PostConstruct
    public void productEcpmAlert() {
        try {
            List<ProductEcpm> productEcpmsList = realtimeRiskMapper.queryProductEcpm();
            XxlJobLogger.log("今日新增数据结果：", JSON.toJSON(productEcpmsList));
            //redis
            int expireTime = 40 * 60;
            if (productEcpmsList.size() > 0) {
                productEcpmsList.forEach(
                        r -> {
                            String key = "need:correct:ecpm:" + r.getProduct() + ":" + r.getOs();
                            String value = String.valueOf(System.currentTimeMillis());
                            apJedisCluster.setex(key, expireTime, value);
                        }
                );
            }

            //dingding
            if (productEcpmsList.size() > 0) {
                List<ProductEcpm> checkBeanList = productEcpmsList.stream()
                        .filter(r -> alertDelayJobService.canSend(AlertJobDelayModel.TODAY_PRODUCT_ECPM_HIGN_ALERT.getJobId() + "_" + r.getProduct()))
                        .collect(Collectors.toList());
                if (checkBeanList.size() > 0) {
                    List<String> productList = checkBeanList.stream().map(ProductEcpm::getProduct).collect(Collectors.toList());
                    String alert = checkBeanList.stream()
                            .map(r -> String.format("产品 **%s**(%s)-**%s**,: " +
                                                    "当日瀑布流ECPM：%s," +
                                                    "当日bidding ECPM：%s," +
                                                    "当日瀑布流收入：%s," +
                                                    "当日bidding收入：%s," +
                                                    "近十分钟瀑布流ECPM：%s," +
                                                    "近十分钟bidding ECPM：%s",
                                            r.getProductName(), r.getProduct(), r.getOs(), r.getNonBiddingEcpm(), r.getBiddingEcpm(), r.getNonBiddingRevenue(), r.getBiddingRevenue(), r.getNonBiddingEcpm10min(), r.getBiddingEcpm10min()
                                    )
                            )
                            .collect(Collectors.joining(";\n\n"));

                    AlertRecord alertRecord = alertRecordService.insertAlertRecord(AlertJobDelayModel.TODAY_PRODUCT_ECPM_HIGN_ALERT.getJobId(),
                            AlertJobDelayModel.TODAY_PRODUCT_ECPM_HIGN_ALERT.getJobName(), AlertModel.BIDDING_ECPM_HIGH, alert
                            , AlertStatus.INIT, DingTailService.bsData, AlertType.DINGDING);
                    DingTailService.sendMarkdownMsgDelay(alertRecord, AlertRoute.BUSINESS.value, AlertJobDelayModel.TODAY_PRODUCT_ECPM_HIGN_ALERT, productList);
                }

            }
        } catch (Exception e) {
            XxlJobLogger.log("CheckEx:", e);
        }
    }

    //    @PostConstruct
    public void todayEcpmAlert() {
        try {
            List<TodayEcpm> todayEcpmList = clickHouse4DwdMapper.queryTodayEcpm();
            XxlJobLogger.log("今日新增数据结果：", JSON.toJSON(todayEcpmList));

            if (todayEcpmList.size() > 0) {
//                List<TodayEcpm> callBackEcpmList = todayEcpmList.stream()
//                        .filter(r -> alertDelayJobService.canSend(AlertJobDelayModel.TODAY_OS_ECPM_ALERT.getJobId() + "_" + r.getOs()))
//                        .collect(Collectors.toList());
//
//                if (callBackEcpmList.size() > 0) {
//                    List<String> productList = callBackEcpmList.stream().map(TodayEcpm::getOs).collect(Collectors.toList());

                String alert = todayEcpmList.stream()
                        .map(r -> String.format("**os** : **%s**,\n\n" +
                                                " 过去10分钟新用户ecpm : **%s**,\n\n" +
                                                " 过去10分钟的前1小时新用户ecpm : **%s**,\n\n" +
                                                " 过去10分钟老用户ecpm : **%s**,\n\n" +
                                                " 过去10分钟的前1小时老用户ecpm : **%s** ",
                                        r.getOs(),
                                        r.getNewEcpmTenMin(), r.getNewEcpmOneH(),
                                        r.getOldEcpmTenMin(), r.getOldEcpmOneH()
                                )
                        )
                        .collect(Collectors.joining(";\n\n"));

                AlertRecord alertRecord = alertRecordService.insertAlertRecord(AlertJobDelayModel.TODAY_OS_ECPM_ALERT.getJobId(),
                        AlertJobDelayModel.TODAY_OS_ECPM_ALERT.getJobName(), AlertModel.TODAY_CSJ_ECPM_ALERT, alert
                        , AlertStatus.INIT, DingTailService.hxzbData, AlertType.DINGDING);
                //生产
                DingTailService.sendMarkdownMsg(alertRecord, AlertRoute.CESHIGSHXZB.value);
                //生产
//                    DingTailService.sendMarkdownMsgDelay(alertRecord, AlertRoute.GSHXZB.value, AlertJobDelayModel.TODAY_OS_ECPM_ALERT, productList);

//                }
            }
        } catch (Exception e) {
            XxlJobLogger.log("CheckEx:", e);
        }
    }


    //        @PostConstruct
    public void productArpuDownAlert() {
        try {
            List<ProductArpuDown> productArpuDownList = clickHouse4DwdMapper.queryProductArpuDown();
            XxlJobLogger.log("今日新增数据结果：", JSON.toJSON(productArpuDownList));
            //redis

//            if (productArpuDownList.size() > 0) {
//                List<ProductArpuDown> callBackRateList = productArpuDownList.stream()
//                        .filter(r -> alertDelayJobService.canSend(AlertJobDelayModel.TODAY_PRODUCT_ARPU_DOWN_ALERT.getJobId() + "_" + r.getOs() + "_" + r.getProducts()))
//                        .collect(Collectors.toList());
//
//                if (callBackRateList.size() > 0) {
//                    List<String> productList = callBackRateList.stream().map(ProductArpuDown::getOs).collect(Collectors.toList());
//
//                    String alert = productArpuDownList.stream()
//                            .map(r -> String.format("**%s** - **%s** - **%s** :  \n\n" +
//                                                    " ecpm_12a:**%s**,avg_pv_12a:**%s**,avg_arpu_12a:**%s** \n\n" +
//                                                    " ecpm_step12:**%s**,pv_step12:**%s**,arpu_step12:**%s** \n\n" +
//                                                    " ecpm_6a:**%s**,avg_pv_6a:**%s**,avg_arpu_6a:**%s** \n\n" +
//                                                    " ecpm_step6:**%s**,pv_step6:**%s**,arpu_step6:**%s** \n\n" +
//                                                    " ecpm_3a:**%s**,avg_pv_3a:**%s**,avg_arpu_3a:**%s** \n\n" +
//                                                    " ecpm_step3:**%s**,pv_step3:**%s**,arpu_step3:**%s**   ",
//                                            r.getGroup(), r.getOs(), r.getProducts(),
//                                        r.getEcpmTwelveA(), r.getAvgPvTwelveA(), r.getAvgArpuTwelveA(),
//                                        r.getEcpmStepTwelve(), r.getPvStepTwelve(), r.getArpuStepTwelve(),
//
//                                        r.getEcpmSixA(), r.getAvgPvSixA(), r.getAvgArpuSixA(),
//                                        r.getEcpmStepSix(), r.getPvStepSix(), r.getArpuStepSix(),
//
//                                        r.getEcpmThreeA(), r.getAvgPvThreeA(), r.getAvgArpuThreeA(),
//                                        r.getEcpmStepThree(), r.getPvStepThree(), r.getArpuStepThree()
//                                    )
//                            )
//                            .collect(Collectors.joining(";\n\n"));
//
//                    AlertRecord alertRecord = alertRecordService.insertAlertRecord(AlertJobDelayModel.TODAY_PRODUCT_ARPU_DOWN_ALERT.getJobId(),
//                            AlertJobDelayModel.TODAY_PRODUCT_ARPU_DOWN_ALERT.getJobName(), AlertModel.PRODUCT_ARPU_DOWN, alert
//                            , AlertStatus.INIT, DingTailService.zcyData, AlertType.DINGDING);
//                    //生产
//                    DingTailService.sendMarkdownMsgDelay(alertRecord, AlertRoute.GSHXZB.value, AlertJobDelayModel.TODAY_PRODUCT_ARPU_DOWN_ALERT, productList);
//
//                }
//            }

            if (productArpuDownList.size() > 0) {

                String alert = productArpuDownList.stream()
                        .map(r -> String.format("**%s** - **%s** - **%s** :  \n\n" +
                                                " arpu_step24 : **%s**, arpu_step12 : **%s**, \n\n" +
                                                " arpu_step6 : **%s**, arpu_step3 : **%s**,  \n\n" +
                                                " arpu_step1 : **%s**, \n\n" +
                                                " dau_24a : **%s**, dau_24b : **%s**,  \n\n" +
                                                " dau_12a : **%s**, dau_12b : **%s**,  \n\n" +
                                                " dau_6a : **%s**, dau_6b : **%s**,  \n\n" +
                                                " dau_3a : **%s**, dau_3b : **%s**,  \n\n" +
                                                " dau_1a : **%s**, dau_1b : **%s** ",
                                        r.getGroup(), r.getOs(), r.getProducts(),
                                        r.getArpuStep24(), r.getArpuStep12(),
                                        r.getArpuStep6(), r.getArpuStep3(),
                                        r.getArpuStep1(),
                                        r.getDau24a(), r.getDau24b(),
                                        r.getDau12a(), r.getDau12b(),
                                        r.getDau6a(), r.getDau6b(),
                                        r.getDau3a(), r.getDau3b(),
                                        r.getDau1a(), r.getDau1b()
                                )
                        )
                        .collect(Collectors.joining(";\n\n"));

                AlertRecord alertRecord = alertRecordService.insertAlertRecord(AlertJobDelayModel.TODAY_PRODUCT_ARPU_DOWN_ALERT.getJobId(),
                        AlertJobDelayModel.TODAY_PRODUCT_ARPU_DOWN_ALERT.getJobName(), AlertModel.PRODUCT_ARPU_DOWN, alert
                        , AlertStatus.INIT, DingTailService.zcyData, AlertType.DINGDING);
                //生产
                DingTailService.sendMarkdownMsg(alertRecord, AlertRoute.ZCY.value);

            }
        } catch (Exception e) {
            XxlJobLogger.log("CheckEx:", e);
        }
    }


//    @PostConstruct
    public void realtimeOsProductEcpmCostAlert() {
        try {
            List<OsProductEcpmCostBean> realtimeOsProductEcpmCostList = adIncomePvQueryMapper.queryRealtimeOsProductEcpmCost();
            XxlJobLogger.log("今日数据结果：", JSON.toJSON(realtimeOsProductEcpmCostList));

            if (realtimeOsProductEcpmCostList.size() > 0) {
//                List<TodayEcpm> callBackEcpmList = todayEcpmList.stream()
//                        .filter(r -> alertDelayJobService.canSend(AlertJobDelayModel.TODAY_OS_ECPM_ALERT.getJobId() + "_" + r.getOs()))
//                        .collect(Collectors.toList());
//
//                if (callBackEcpmList.size() > 0) {
//                    List<String> productList = callBackEcpmList.stream().map(TodayEcpm::getOs).collect(Collectors.toList());

                String alert = realtimeOsProductEcpmCostList.stream()
                        .map(r -> {
                            String dingding=String.format("产品名: **%s**(**%s**), **os** : **%s**,\n\n" +
                                                " 产品昨日消耗 : **%s**,\n\n" +
                                                " 过去10分钟ecpm : **%s**,\n\n" +
                                                " 过去10分钟的前1小时ecpm : **%s**,\n\n" +
                                                " 平均ecpm较前一小时涨幅 : **%s**",
                                        r.getProductName(), r.getProduct(), r.getOs(),
                                        r.getCostYes(),
                                        r.getEcpmTenMin(), r.getEcpmOneH(),
                                        r.getCeilP(r.getRate())
                            );

                            return  adjustArpu(r,dingding);
                        }
                        )
                        .collect(Collectors.joining(";\n\n"));

                AlertModel alertModel= AlertModel.TODAY_CSJ_ECPM_ALERT;
                if (isWithinTimeRange(LocalTime.of(0, 0), LocalTime.of(6, 0))) {
                   alertModel=AlertModel.TODAY_CSJ_ECPM_ADJUSTED_ALERT;
                }
                AlertRecord alertRecord = alertRecordService.insertAlertRecord(AlertJobDelayModel.REALTIME_OS_PRODUCT_ECPM_ALERT.getJobId(),
                        AlertJobDelayModel.REALTIME_OS_PRODUCT_ECPM_ALERT.getJobName(), alertModel, alert
                        , AlertStatus.INIT, DingTailService.zphData, AlertType.DINGDING);

                //生产
                DingTailService.sendMarkdownMsg(alertRecord, AlertRoute.ZPH.value);

            }
        } catch (Exception e) {
            XxlJobLogger.log("CheckEx:", e);
        }
    }

    /**
     * 修改arpu的值
     *
     * @param osProductEcpmCostBean
     * @param dingding
     */
    private String adjustArpu(OsProductEcpmCostBean osProductEcpmCostBean, String dingding) {

        // 1. 时间范围检查
        if (!isWithinTimeRange(LocalTime.of(0, 0), LocalTime.of(6, 0))) {
            return dingding;
        }

        if (!StringUtils.isEmpty(apJedisCluster.get(buildProductKey(osProductEcpmCostBean.getProductName(), osProductEcpmCostBean.getOs())))) {
            return dingding;
        }

        try {
            // 2. 查询广告主列表
            List<AuthToutiaoAdvertiser> authToutiaoAdvertiserList = authToutiaoAdvertiserService.lambdaQuery()
                    .eq(AuthToutiaoAdvertiser::getProductName, osProductEcpmCostBean.getProductName())
                    .eq(AuthToutiaoAdvertiser::getPutType, osProductEcpmCostBean.getOs())
                    .eq(AuthToutiaoAdvertiser::getDelFlag, 0)
                    .eq(AuthToutiaoAdvertiser::getEventTypes, "1,2")
                    .eq(AuthToutiaoAdvertiser::getEnable, 0)
                    .list();

            if (CollectionUtils.isEmpty(authToutiaoAdvertiserList)) {
                XxlJobLogger.log("product:{} not exists valid advertisers", osProductEcpmCostBean.getProductName());
                log.info("product:{} not exists valid advertisers", osProductEcpmCostBean.getProductName());
                return dingding;
            }


            // 3. 更新ARPUs
            Map<Double, Double> arpuChanges = Maps.newHashMap();
            ProductArpuAdjustInfo productArpuAdjustInfo = new ProductArpuAdjustInfo();
            productArpuAdjustInfo.setProductName(osProductEcpmCostBean.getProductName());
            productArpuAdjustInfo.setPutType(osProductEcpmCostBean.getOs());
            productArpuAdjustInfo.setAdvertiserArpuInfoList(new ArrayList<>());

            boolean updateSuccess = updateAdvertisersArpu(authToutiaoAdvertiserList, productArpuAdjustInfo, arpuChanges);

            return updateSuccess ? buildSuccessMessage(dingding, arpuChanges) : dingding;
        } catch (Exception e) {
            XxlJobLogger.log("product:{} modify arpu failed", osProductEcpmCostBean.getProductName(), e);
            log.error("product:{} modify arpu failed", osProductEcpmCostBean.getProductName(), e);
            String alert = String.format("产品:%s modify arpu failed，please check!", osProductEcpmCostBean.getProductName());
            AlertRecord alertRecord = alertRecordService.insertAlertRecord(AlertJobDelayModel.REALTIME_OS_PRODUCT_ECPM_ALERT.getJobId(),
                    AlertJobDelayModel.REALTIME_OS_PRODUCT_ECPM_ALERT.getJobName(), AlertModel.TODAY_CSJ_ECPM_ADJUSTED_ALERT, alert
                    , AlertStatus.INIT, DingTailService.zphData, AlertType.DINGDING);
            DingTailService.sendMarkdownMsg(alertRecord, AlertRoute.ZPH.value);
            return dingding;
        }
    }


    /**
     * 是否在规定时间
     *
     * @param start
     * @param end
     * @return
     */
    private boolean isWithinTimeRange(LocalTime start, LocalTime end) {
        LocalTime now = LocalTime.now();
        return !now.isBefore(start) && !now.isAfter(end);
    }


    /**
     * 修改产品下 广告主的arpu
     *
     * @param advertisers
     * @param productArpuAdjustInfo
     * @param arpuChanges
     * @return
     */
    private boolean updateAdvertisersArpu(List<AuthToutiaoAdvertiser> advertisers,
                                          ProductArpuAdjustInfo productArpuAdjustInfo, Map<Double, Double> arpuChanges) {

        advertisers.forEach(advertiser -> {
            AdvertiserArpuInfo advertiserArpuInfo = new AdvertiserArpuInfo();
            advertiserArpuInfo.setAdvertiserId(String.valueOf(advertiser.getAdvertiserId()));
            productArpuAdjustInfo.getAdvertiserArpuInfoList().add(advertiserArpuInfo);
            updateSingleAdvertiserArpu(advertiser, advertiserArpuInfo, arpuChanges);
            if (!StringUtils.isEmpty(advertiserArpuInfo.getAdjustEventValues())) {
                advertiser.setEventValues(advertiserArpuInfo.getAdjustEventValues());
                advertiser.setUpdateTime(new Date());
            }
        });

        apJedisCluster.setex(buildProductKey(productArpuAdjustInfo.getProductName(),productArpuAdjustInfo.getPutType()), PRODUCT_ARPU_ADJUST_EXPIRE,
                JSONObject.toJSONString(productArpuAdjustInfo));

        return authToutiaoAdvertiserService.updateBatchById(advertisers);
    }


    private void updateSingleAdvertiserArpu(AuthToutiaoAdvertiser advertiser,
                                            AdvertiserArpuInfo advertiserArpuInfo, Map<Double, Double> arpuChanges) {
        String eventValues = advertiser.getEventValues();
        if (StringUtils.isEmpty(eventValues) || !eventValues.contains(",")) {
            logInvalidFormat(advertiser);

        }

        String[] parts = eventValues.split(",");
        if (parts.length != 2) {
            logInvalidFormat(advertiser);
        }

        try {
            advertiserArpuInfo.setOriginEventValues(eventValues);
            BigDecimal original = new BigDecimal(parts[1]);
            double adjusted = original.multiply(new BigDecimal("1.02")).doubleValue();
            arpuChanges.putIfAbsent(original.doubleValue(), adjusted);
            advertiserArpuInfo.setAdjustEventValues(parts[0] + "," + adjusted);

        } catch (NumberFormatException e) {
            logInvalidFormat(advertiser);
        }
    }

    private String buildSuccessMessage(String original, Map<Double, Double> changes) {
        StringBuilder builder = new StringBuilder(original)
                .append("\n\n回传arpu要求已调整：回传arpu要求 +2%\n\n");
        return builder.toString();
    }

    private void logInvalidFormat(AuthToutiaoAdvertiser advertiser) {
        String message = String.format("产品%s的广告主%s eventValues格式不正确, 跳过该条记录",
                advertiser.getProductName(),
                advertiser.getAdvertiserId());
        XxlJobLogger.log(message);
        log.error(message);
    }


    private String buildProductKey(String productName, String putType) {
        return String.format("adjust:arpu:date:%s:%s:%s", DateUtils.formatDateForYMDSTR(new Date()), productName, putType);
    }


    /**
     * 恢复初始arpu
     */
    public void recoverOriginArpu() {
        List<ProductArpuAdjustInfo> productArpuAdjustInfoList = new ArrayList<>();
        try {
            List<String> productNameList = productService.getAllProductCnNames();

            productNameList.forEach(productName -> {
                String json = apJedisCluster.get(buildProductKey(productName, "android"));
                if (!StringUtils.isEmpty(json)) {
                    productArpuAdjustInfoList.add(JSONObject.parseObject(json, ProductArpuAdjustInfo.class));
                }

                String iosJson = apJedisCluster.get(buildProductKey(productName, "ios"));
                if (!StringUtils.isEmpty(iosJson)) {
                    productArpuAdjustInfoList.add(JSONObject.parseObject(iosJson, ProductArpuAdjustInfo.class));
                }
            });

            if (productArpuAdjustInfoList.isEmpty()) {
                return;
            }
            StringBuilder alterMsg = new StringBuilder();
            productArpuAdjustInfoList.forEach(productArpuAdjustInfo -> {
                alterMsg.append(String.format("产品名: %s, os: %s,\n\n回传arpu要求已调回\n\n",
                        productArpuAdjustInfo.getProductName(), productArpuAdjustInfo.getPutType()));
                productArpuAdjustInfo.getAdvertiserArpuInfoList().forEach(advertiserArpuInfo -> {
                    authToutiaoAdvertiserService.lambdaUpdate()
                            .set(AuthToutiaoAdvertiser::getEventValues, advertiserArpuInfo.getOriginEventValues())
                            .eq(AuthToutiaoAdvertiser::getAdvertiserId, Long.parseLong(advertiserArpuInfo.getAdvertiserId()))
                            .eq(AuthToutiaoAdvertiser::getProductName, productArpuAdjustInfo.getProductName())
                            .eq(AuthToutiaoAdvertiser::getPutType, productArpuAdjustInfo.getPutType())
                            .eq(AuthToutiaoAdvertiser::getEventTypes, "1,2")
                            .update();
                });
            });

            AlertRecord record = alertRecordService.insertAlertRecord(
                    AlertJobDelayModel.REALTIME_OS_PRODUCT_ARPU_ADJUST_ALERT.getJobId(),
                    AlertJobDelayModel.REALTIME_OS_PRODUCT_ARPU_ADJUST_ALERT.getJobName(),
                    AlertModel.TODAY_CSJ_ECPM_ADJUSTED_ALERT,
                    alterMsg.toString(),
                    AlertStatus.INIT,
                    DingTailService.zphData,
                    AlertType.DINGDING);

            DingTailService.sendMarkdownMsg(record, AlertRoute.ZPH.value);
        } catch (Exception e) {
            String productNames = productArpuAdjustInfoList.stream().map(ProductArpuAdjustInfo::getProductName).collect(Collectors.joining(", "));
            XxlJobLogger.log("product:{} modify arpu failed", productNames, e);
            log.error("product:{} modify arpu failed", productNames, e);
            String alert = String.format("产品:%s modify arpu failed，please check!", productNames);
            AlertRecord alertRecord = alertRecordService.insertAlertRecord(AlertJobDelayModel.REALTIME_OS_PRODUCT_ARPU_ADJUST_ALERT.getJobId(),
                    AlertJobDelayModel.REALTIME_OS_PRODUCT_ARPU_ADJUST_ALERT.getJobName(), AlertModel.TODAY_CSJ_ECPM_ADJUSTED_ALERT, alert
                    , AlertStatus.INIT, DingTailService.zphData, AlertType.DINGDING);
            DingTailService.sendMarkdownMsg(alertRecord, AlertRoute.ZPH.value);
        }
    }
}
