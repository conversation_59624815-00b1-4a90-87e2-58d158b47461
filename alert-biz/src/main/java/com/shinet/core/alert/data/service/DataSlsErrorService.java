package com.shinet.core.alert.data.service;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.shinet.core.alert.coreservice.entity.AlertRecord;
import com.shinet.core.alert.coreservice.enums.AlertModel;
import com.shinet.core.alert.coreservice.enums.AlertStatus;
import com.shinet.core.alert.coreservice.enums.AlertType;
import com.shinet.core.alert.coreservice.service.AlertRecordService;
import com.shinet.core.alert.data.entity.DataSlsError;
import com.shinet.core.alert.data.mapper.DataSlsErrorMapper;
import com.shinet.core.alert.dingtalk.DingTailService;
import com.shinet.core.alert.dingtalk.SLSService;
import com.shinet.core.alert.util.BlackProjUtils;
import com.xxl.job.core.log.XxlJobLogger;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.util.Calendar;
import java.util.List;
import java.util.stream.Collectors;

/**
* <p>
    *  服务实现类
    * </p>
*
* <AUTHOR>
* @since 2021-12-09
*/
@Service
@Slf4j
public class DataSlsErrorService extends ServiceImpl<DataSlsErrorMapper, DataSlsError> {

    @Value("${skip.sls.err.list:[\"core-gpt\"]}")
    private List<String> skipServiceList;
    @Autowired
    AlertRecordService alertRecordService;
    public void insertSlsError(int minnum){
        try {
            List<DataSlsError> errorSlsLogBeans = SLSService.queryLogs(minnum);
            List<DataSlsError> saveErrorSlsLogBeans =errorSlsLogBeans.stream()
                    .filter(esb -> !BlackProjUtils.isInBlack(esb.getServiceName()))
                    .filter(esb -> !skipServiceList.contains(esb.getServiceName()))
                    .collect(Collectors.toList());

            String emsg = "";
            for(DataSlsError dataSlsError : saveErrorSlsLogBeans){
                if(dataSlsError.getErrorNum()>150){
                    emsg = emsg +" "+dataSlsError.getServiceName()+"-"+ dataSlsError.getErrorNum()+"\r\n";
                }
            }


            if(StringUtils.isNotBlank(emsg) && saveErrorSlsLogBeans.size()>0){
                AlertRecord alertRecord = alertRecordService.insertAlertRecord("alert-slsError","sls报错", AlertModel.SLS,
                        emsg,0d,0d,0,0,
                        AlertStatus.INIT,DingTailService.dset, AlertType.DINGPHONE);

                DingTailService.sendMarkdownMsg(alertRecord);
                saveBatch(saveErrorSlsLogBeans);
            }
        }catch (Exception e){
            log.error("",e);
        }
    }


    public void countIosLoginErrorAlert(double rate) {
        int todayEnd = (int)(System.currentTimeMillis() / 1000), yesterdayEnd = todayEnd - 86400;
        Calendar calendar = Calendar.getInstance();
        calendar.set(Calendar.HOUR_OF_DAY, 0);
        calendar.set(Calendar.MINUTE, 0);
        calendar.set(Calendar.SECOND, 0);
        int todayStart = (int)(calendar.getTimeInMillis() / 1000), yesterdayStart = todayStart - 86400;
        try {
            long yesterdayCount = SLSService.queryIosUserErrorCount(yesterdayStart, yesterdayEnd);
            long todayCount = SLSService.queryIosUserErrorCount(todayStart, todayEnd);
            XxlJobLogger.log("昨日数量:{}, 今日数量:{}", yesterdayCount, todayCount);
            double countRate = (todayCount - yesterdayCount) * 1d/ yesterdayCount;
            if(countRate > rate) {
                String alertMsg = ""; //TODO 错误预警
                AlertRecord alertRecord = alertRecordService.insertAlertRecord("alert-ios-user-login-error","ios用户登录报错", AlertModel.SLS,
                        "",rate,countRate,Long.valueOf(todayCount).intValue(),Long.valueOf(yesterdayCount).intValue(),
                        AlertStatus.INIT,DingTailService.dset, AlertType.DINGPHONE);

                DingTailService.sendMarkdownMsg(alertRecord);
            }
        }catch (Exception ex) {
            log.error("查询用户登录错误日志报错", ex);
        }
    }
}
