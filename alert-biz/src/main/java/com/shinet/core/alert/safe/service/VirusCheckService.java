package com.shinet.core.alert.safe.service;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.shinet.core.alert.coreservice.entity.AlertRecord;
import com.shinet.core.alert.coreservice.enums.AlertModel;
import com.shinet.core.alert.coreservice.enums.AlertStatus;
import com.shinet.core.alert.coreservice.enums.AlertType;
import com.shinet.core.alert.coreservice.service.AlertRecordService;
import com.shinet.core.alert.dingtalk.DingTailService;
import com.shinet.core.alert.dingtalk.VedioAlertService;
import com.shinet.core.alert.dsp.entity.AdPlanConfig;
import com.shinet.core.alert.dsp.service.ProductService;
import com.shinet.core.alert.safe.entity.VirusCheck;
import com.shinet.core.alert.safe.mapper.VirusCheckMapper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.http.NameValuePair;
import org.apache.http.client.entity.UrlEncodedFormEntity;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.message.BasicNameValuePair;
import org.apache.http.params.BasicHttpParams;
import org.apache.http.params.HttpParams;
import org.apache.http.util.EntityUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
* <p>
    *  服务实现类
    * </p>
*
* <AUTHOR>
* @since 2023-11-16
*/
@Slf4j
@Service
public class VirusCheckService extends ServiceImpl<VirusCheckMapper, VirusCheck> {

    @Autowired
    private AlertRecordService alertRecordService;

    @Autowired
    private ProductService productService;

    public void checkNoVirusAppDiff() {
        /*List<String> names = productService.diffGroupDownurl();
        HashSet<String> phones = new HashSet<>();
        String az = "腾讯管家检测APP没有缺失";
        if (CollectionUtils.isNotEmpty(names)) {
            az = "腾讯管家检测APP缺失 : " + (names.stream().collect(Collectors.joining(",")));
        }
        phones.add("18898133265");

        AlertRecord alertRecord = alertRecordService.insertAlertRecord("App-Diff-Check", "App-Diff-Check", AlertModel.AppDiffCheck,
                az, AlertStatus.INIT, phones
                , AlertType.DINGDING);
        DingTailService.sendMarkdownMsg(alertRecord);*/
    }

    public void checkNoVirus(){
        List<AdPlanConfig> waitCheckList = productService.getGroupDownurl();
//        List<VirusCheck> checkUrlList = lambdaQuery().list();
//        List<VirusCheck> waitCheckList = checkUrlList.stream()
//                .filter(r -> Strings.isNotEmpty(r.getUrl()))
//                //下架状态 不检测
//                .filter(r -> !Integer.valueOf(0).equals(r.getUpStatus()))
//                .collect(Collectors.toList());
        HashSet<String> phones = new HashSet<>();

        AlertType alertType = AlertType.DINGDING;
        if (waitCheckList.size() > 0){
            List<AdPlanConfig> tempRes = batchHandler(waitCheckList);
            String az = "腾讯管家检测全部通过";
            if (tempRes.size() > 0){
                try {
                    Thread.sleep(5 * 60 * 1000);
                } catch (InterruptedException e) {
                    log.error("", e);
                }
                tempRes = batchHandler(tempRes);
                if(CollectionUtils.isNotEmpty(tempRes)){
                    az = tempRes.stream().map(AdPlanConfig::getAppName).collect(Collectors.joining(","))+"腾讯管家检测未通过";
                    //phones.add("15652694117");
                    phones.add("18898133265");
                    alertType = AlertType.PHONE;
                }else{
                    phones.add("18898133265");
                }
            }
            az = az + "\n 通过的有" + (waitCheckList.size() - tempRes.size()) + "个";

            AlertRecord alertRecord = alertRecordService.insertAlertRecord("Tencent-Security-Check","Tencent-Security-Check", AlertModel.tencentMobileSecurity,
                    az, AlertStatus.INIT, phones
                    , alertType);
            DingTailService.sendMarkdownMsg(alertRecord);
            if(Objects.equals(alertType, AlertType.PHONE)){
                // VedioAlertService.sendVocMsg(AlertModel.tencentMobileSecurity.toString(), az, phones);
            }
        }
    }

    private static List<AdPlanConfig> batchHandler(List<AdPlanConfig> waitCheckList) {
        List<AdPlanConfig> tempRes = new ArrayList<>();
        for (AdPlanConfig iosCheckUrl : waitCheckList){
            try {
                if(!checkNoVirus(iosCheckUrl.getDownloadUrl(), 0)){
                    tempRes.add(iosCheckUrl);
                }
                Thread.sleep(5000);
            }catch (Exception e){
                log.warn("VirusCheckService checkOne warn iosCheckUrl : {} ", iosCheckUrl, e);
                tempRes.add(iosCheckUrl);
            }
        }
        return tempRes;
    }

    public static void main(String[] args) {
        System.out.println((((int)(Math.random() * 5)) * 1000));
//        System.out.println(checkNoVirus("https://toufang.shinet.cn/app/klldsh/update/update.apk", 0));
//        System.out.println(checkDu("https://apps.bytesfield.com/download/extend/cur/ab7565159683a275963c67e6c116f070836c0d87/ttznjyqcz12b", 0));
//        System.out.println(checkDu("https://apps.bytesfield.com/download/extend/cur/09a6a051223f8d1af73d3322c476513bab3f0292/ttznzzdwj12b", 0));
//        System.out.println(checkDu("https://toufang.shinet.cn/app/jyqcz/bdjyqcz12c01/bdjyqcz12c01.apk", 0));
    }

    private static boolean checkNoVirus(String apkUrl, int i) {
        if(i > 6){
            log.info("检测超时 apkUrl : {}", apkUrl);
            return false;
        }
        CloseableHttpResponse response = null;
        CloseableHttpClient httpClient = null;
        String url = "https://m.qq.com/security_lab/check_result_json.jsp";
        try {
            httpClient = HttpClients.createDefault();
            HttpPost http = new HttpPost(url);

            List<NameValuePair> paramList = new ArrayList<>();
            HttpParams httpParams = new BasicHttpParams();

            paramList.add(new BasicNameValuePair("type", "url"));
            httpParams.setParameter("type", "url");

//            paramList.add(new BasicNameValuePair("data", "https://toufang.shinet.cn/app/klldsh/update/update.apk"));
//            httpParams.setParameter("data", "https://toufang.shinet.cn/app/klldsh/update/update.apk");

            paramList.add(new BasicNameValuePair("data", apkUrl));
            httpParams.setParameter("data", apkUrl);

            http.setParams(httpParams);
            UrlEncodedFormEntity entity = new UrlEncodedFormEntity(paramList);
            http.setEntity(entity);

            response = httpClient.execute(http);
            int statusCode = response.getStatusLine().getStatusCode();
            String tempR = null;
            if (statusCode == 200){
                tempR = EntityUtils.toString(response.getEntity(), StandardCharsets.UTF_8);
                JSONObject jsonObject = JSON.parseObject(tempR);
//                String res = jsonObject.getObject("info", JSONObject.class)
//                        .getString("conclusion");
                Integer warm = jsonObject.getObject("info", JSONObject.class)
                        .getInteger("warningLevel");
                if(warm != null){
                    if(warm == 3){
                        log.info("检测通过 apkUrl : {}", apkUrl);
                        return true;
                    }
                    if(warm == 4){
                        Thread.sleep((i + 1) * 5000);
                        log.info("检测重试 apkUrl : {}  jsonObject : {}", apkUrl, jsonObject);
                        return checkNoVirus(apkUrl, i + 1);
                    }
                }
            }
            log.info("检测失败 apkUrl : {} tempR : {}", apkUrl, tempR);
            Thread.sleep((i + 1) * 5000);
            return checkNoVirus(apkUrl, i + 1);
        }catch (Exception e){
            log.warn("Request Er:",e);
            log.warn("检测失败 apkUrl : {}", apkUrl);
            try {
                Thread.sleep((i + 1) * 5000);
            } catch (InterruptedException ex) {
            }
            return checkNoVirus(apkUrl, i + 1);
        }finally {
            try {
                response.close();
            } catch (IOException e) {
                log.error(" response.close ", e);
            }
            try {
                httpClient.close();
            } catch (IOException e) {
                log.error(" httpClient.close ", e);
            }
        }
    }
}
