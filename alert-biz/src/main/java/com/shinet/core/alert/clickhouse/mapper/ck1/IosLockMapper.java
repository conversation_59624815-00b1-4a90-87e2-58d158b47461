package com.shinet.core.alert.clickhouse.mapper.ck1;


import com.shinet.core.alert.clickhouse.entity.IosLockBean;
import com.shinet.core.alert.safe.entity.AndoridLockLook;
import org.apache.ibatis.annotations.Select;

import java.util.List;

public interface IosLockMapper {
    @Select({"select product,sum(if(extend1='true',1,0)) as lockNum,sum(if(extend1='false',1,0)) as unlockNum,(sum(if(extend1='true',1,0))/count(1)) *100 as baifenbi,count(1) as allNum from (\n" +
            "    select product,extend1,caid,userid,count(1) as cd from ods.event_dist where logday=yesterday() and os='ios' and event='Startup' and extend1 in ('true','false')  group by product,extend1,caid,userid\n" +
            ") as dtable  group by product having  allNum>500 order by product;"})
    public List<IosLockBean> getIosLockRst( );

    List<AndoridLockLook> getAndroidLockInfo(Integer minLockNum);

}
