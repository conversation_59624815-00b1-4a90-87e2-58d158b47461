package com.shinet.core.alert.dingtalk;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.shinet.core.alert.coreservice.entity.AlertRecord;
import com.shinet.core.alert.coreservice.enums.*;
import lombok.extern.slf4j.Slf4j;
import okhttp3.*;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.io.IOException;
import java.util.*;
import java.util.stream.Collectors;

@Service
@Slf4j
public class DingTailService {
    //https://oapi.dingtalk.com/robot/send?access_token=6acc208cc52c0b8c12f8db155a6de4f72c325fb7b9f65fbf36b8f2029c5437b6
    private static final String DINGTALKURL_PRE = "https://oapi.dingtalk.com/robot/send";
    public static final String plan_access_token = "6acc208cc52c0b8c12f8db155a6de4f72c325fb7b9f65fbf36b8f2029c5437b6"; //中台业务监控

    public static final String adAcsTokn = "20bc744956838969fe2457785e3acb425720b6f210119307b777a9adb68830ea";
    public static final String jishuAcsTokn = "3f8c4c2d80005db19a1f08cba572b4f4e62a49ca3d4b48b3adaaab2d807ca991";//技术群
    //https://oapi.dingtalk.com/robot/send?access_token=cb9e2ae549aa6b9fc00e2fc140949782eb159f5bb4da361674586d350496ca3c
    public static final String adworkAckToke = "cb9e2ae549aa6b9fc00e2fc140949782eb159f5bb4da361674586d350496ca3c";

    public static final String businessAckToke = "a14c44794724ef708282965602c1958a33055878eb2886cf17d979222c505431";
    public static final String shujuxuqiu = "eb5937c535987400453981530821b46b36d93c7f1412371daa249ffc7b256ee6";
    public static final String celv = "0d466c772b867dac09349e4934f3a8a53449d566f2b5beee3d0b76b8458b1b6a";
    public static final String device = "bb1b47af540aeee96021459d523eaa03d6464fc776c01e08a5570429fa79a0ed";
    public static final String ztxq = "1ce4004a35231211557375a40211ed48c47ec039931e6dc111d91733c9409820";
    public static final String test = "7ab29bad2a5bb96a1986feac7c42930e51e50dcf9b78a37195e1d19a0a8bb16f";
    public static  final  String erhaoAlert = "84d4b3408ff639e43bc1d82badbc905f63af38dbf198e127ef2b9dbe86575237";
    public static  final  String group9Pay = "df38fbf999a36b19f5d6528bac866e7921939f6b94577b18c8e866796a57b650";
    public static  final  String sjxt = "180904c1c5d1efe26a7af8b0be14d67e22278cbccf723c4b67b88a2bf720e0e6";
    public static  final  String suoqualert = "e9463cfa0a170ecc286aa1eb6d8d12c5db4cbd9444d7fbda88f0d156a782794e";
    public static  final  String shujuwenti = "9e1a4ff755a66af38f4bbb6b62c224686255f27af58bdbf64ef04d27228d39b6";
    public static  final  String jinjishujuyujing = "25a9f59823f8b76a2811509901bf0fd7cdeff7e5f6524e51b0df83dfcb7e7c2e";
    public static  final  String roiAlter = "SEC127fb4ea6005f9b16f615d7f002ab12a28f2fe930d456983ef884cc0915d4a17";
    public static final String zengzhangzhoulihui = "db686d3c370639e7f6cff1f7cbf0a154a49467d65381daa62d4271ad17d07f25";
    public static final String fanzuobi = "85be67fb0d8604102edc0a59012a836aaa960346e6bd34edd47004f33c4f5a7e";
    public static final String gongsihexinzhibiao = "d061748ece790a19544e4cc0c5417660b43b5f2ee76cd631e96b2b6f836e859e";
    //ceshi
    public static final String ceshigongsihexinzhibiao = "ab4ee80de6a45afb673846a8b6ca59591e54fcb38b593e31799d5363efbf1dda";
    public static final String fanzuobihexin = "7d532c9c5ddbe8d7477d95b9815bc445e3b379c74a72bd1ea4d10bdcb06aef72";
    public static final String jinriecpm = "6d5f215fa0112843f78cad9af81e61289902ca52fc396c02f88b6e46af974ec6";

    public static final String wzzdhtfyj = "81931f913d56c9488d6559853a000da38d556956bdff643fb24231be5d519f47";

    public static final String zph = "d86f097266795cde8ccf9cc04e3dbc406d5124b5cacacb9fb6d093f331964e93";

    public static final String zcy = "273ea7206bf5e31178b1059b43b7231b8b9425b4776d11d9d3d80be9ea7a75c5";
    public static final String guiyinhuichuanhexin = "16002e50ee3f0c690629ec1b2daf011601b0674954f63c7334d51ba2d73f8c33";
    // 技术线
    public static Set<String> dset = new HashSet<>();
    //技术线排除老毕
    public static Set<String> dset1 = new HashSet<>();
    public static Set<String> dset2 = new HashSet<>();
    public static Set<String> dsetData = new HashSet<>();
    public static Set<String> dailyData = new HashSet<>();
    public static Set<String> shujuxuqiuData = new HashSet<>();
    public static Set<String> bsData = new HashSet<>();
    public static Set<String> hxzbData = new HashSet<>();
    public static Set<String> bsData1 = new HashSet<>();
    public static Set<String> group9 = new HashSet<>();
    public static Set<String> ttCompanyData = new HashSet<>();
    public static Set<String> ecpmData = new HashSet<>();
    public static Set<String> zcyData = new HashSet<>();
    public static Set<String> hxzbPhoneData = new HashSet<>();
    public static Set<String> zphData = new HashSet<>();

    public static Set<String> syqun = new HashSet<>();

    public static Set<String> merchantWithdrawAlarmData = new HashSet<>();

    public static Set<String> businessAlarmData = new HashSet<>();
    // 锁区
    public static Set<String> lockAlarmData = new HashSet<>();
    // 技术线 商店全锁
    public static Set<String> lockAlarmStoreDataForJSX = new HashSet<>();
    //归因
    public static Set<String> ocpcAlarmData = new HashSet<>();

    public static Set<String> user = new HashSet<>();

    public static Map<String,List<String>> zengzhangPushMap = new HashMap<String,List<String>>(){{
        // 杨璐
        put("项目一组",Arrays.asList("***********"));
        // 曾子俊 谢志豪 王海涛
        put("项目五组", Arrays.asList("***********", "***********", "***********"));
        // 陈振宇 张倩
        put("项目七组", Arrays.asList("***********","***********"));
        // 周明京
        put("项目十组", Arrays.asList("***********","***********"));
    }};

    public static Map<String,List<String>> roiAlertPushMap = new HashMap<String,List<String>>(){{
        // 杨璐
        put("1组",Arrays.asList("***********"));
        // 曾子俊 谢志豪 王海涛
        put("5组", Arrays.asList("***********", "***********", "***********"));
        // 陈振宇 张倩
        put("7组", Arrays.asList("***********","***********"));
        // 周明京
        put("10组", Arrays.asList("***********","***********"));
    }};

    static {
        //dset.add("***********");
        dset.add("18600046769");
        // 李飞
        lockAlarmData.add("15047807924");
        lockAlarmStoreDataForJSX.add("15047807924");
        lockAlarmStoreDataForJSX.add("15386069313");
        lockAlarmStoreDataForJSX.add("13813375877");

        //dset1.add("***********");

        //dset2.add("***********");
        dset2.add("15510551905");
        // dset2.add("***********");
        dset2.add("***********");
        dset2.add("***********");

        dsetData.add("18035741227");
        dsetData.add("13895604908");

        //日报预警
        dailyData.add("18035741227");
        dailyData.add("13895604908");

        //核心指标群报警电话
        hxzbPhoneData.add("18035741227");//kxq
        hxzbPhoneData.add("13910666139");//v姐
        hxzbPhoneData.add("***********");//yz
        hxzbPhoneData.add("18600655825");//wwj
        hxzbPhoneData.add("17611265922");//zw
        hxzbPhoneData.add("17306396520");//myz
        hxzbPhoneData.add("***********");//lx
        hxzbPhoneData.add("13895604908");//bor
        hxzbPhoneData.add("18600046769");//bir
        hxzbPhoneData.add("13811409318");//yl

        //防空洞
        bsData.add("***********");
        //bsData.add("***********");
        //bsData.add("15028170815");
        //bsData.add("18353209427");
        bsData.add("18311025152");
        //防空洞
        bsData1.add("***********");
        bsData1.add("***********");
        bsData1.add("***********");
        bsData1.add("15822339517"); //杨贝贝
        //核心指标
        hxzbData.add("***********");

        //ecpm
        ecpmData.add("***********");
        ecpmData.add("***********");

        //zph
        zphData.add("***********");
        zphData.add("***********");
        zphData.add("***********");

        //zcy
        zcyData.add("18*********");

        syqun.add("***********");
        syqun.add("***********");
        syqun.add("***********");
        //syqun.add("***********");
        syqun.add("***********");

        businessAlarmData.add("***********");
        businessAlarmData.add("***********");



        merchantWithdrawAlarmData.add("***********");
        group9.add("***********");

        shujuxuqiuData.add("***********");
        shujuxuqiuData.add("***********");
        shujuxuqiuData.add("***********");

        ttCompanyData.add("***********");

        ocpcAlarmData.add("***********");

        // user.add("***********");
        user.add("***********");
        user.add("***********");
    }

//    public static String sendMsg(String key, String msg) {
//        log.info("钉钉发送消息 key={},msg={}", key, msg);
//        String url = DINGTALKURL_PRE + "?access_token=" + plan_access_token;
//        Headers headers = new Headers.Builder().add("Content-Type", "application/json").build();
//        JSONObject jsonObject = new JSONObject();
//        JSONObject content = new JSONObject();
//        JSONObject atMobiles = new JSONObject();
//        atMobiles.put("atMobiles", dset);
//        jsonObject.put("msgtype", "text");
//        StringBuilder stringBuilder = new StringBuilder();
//        for (String phone : dset) {
//            if (StringUtils.isNotBlank(phone)) {
//                stringBuilder.append("@" + phone + " ");
//            } else {
//                stringBuilder.append(phone + " ");
//            }
//        }
//
//        String s = "Alert [%s] \n" + "- %s 请查看: \n" + "%s";
//        String sendMsg = String.format(s, key, msg, stringBuilder.toString());
//
//        content.put("content", sendMsg);
//
//        jsonObject.put("at", atMobiles);
//        jsonObject.put("text", content);
//        RequestBody requestBody = RequestBody.create(null, jsonObject.toJSONString());
//        return post(url, headers, requestBody);
//    }


    public static void sendMarkdownMsg(AlertRecord alertRecord) {
        String sendMsg = DingMsgService.getDingMarkdownMsg(alertRecord);
        String dmsg = sendMarkdownMsg(alertRecord.getModelName(),sendMsg, Lists.newArrayList(alertRecord.getAlertPhone().split(",")),AlertRoute.JISHU.value);
        log.info(dmsg);
    }

    public static void sendMarkdownMsgBs(AlertRecord alertRecord) {
        String sendMsg = DingMsgService.getDingMarkdownMsg(alertRecord);
        String dmsg = sendMarkdownMsg(alertRecord.getModelName(),sendMsg, Lists.newArrayList(alertRecord.getAlertPhone().split(",")),AlertRoute.BUSINESS.value);
        log.info(dmsg);
    }

    public static String sendMarkdownMsg(String title, String msg, List<String> atMobilesList,Integer alertRoute) {
        log.info("钉钉发送消息 title={},msg={}", title, msg);
        String token = jishuAcsTokn;
        if (AlertRoute.ZHONGTAI.value.equals(alertRoute)){
            token = plan_access_token;
        }else if (AlertRoute.BUSINESS.value.equals(alertRoute)){
            token = businessAckToke;
        }else if (AlertRoute.TEST.value.equals(alertRoute)) {
            token = test;
        }else if(AlertRoute.ERHAOALERT.value.equals(alertRoute)){
            token = erhaoAlert;
        }else if(AlertRoute.GROUP9_PAY.value.equals(alertRoute)){
            token = group9Pay;
        }else if(AlertRoute.ZENGZHANG.value.equals(alertRoute)){
            token = shujuxuqiu;
        }else if(AlertRoute.LIULIANG.value.equals(alertRoute)){
            token = celv;
        }else if (AlertRoute.DEVICE.value.equals(alertRoute)){
            token = device;
        }else if (AlertRoute.ZT_XQ.value.equals(alertRoute)){
            token = ztxq;
        }else if (AlertRoute.SJXT.value.equals(alertRoute)){
            token = sjxt;
        }else if (AlertRoute.SUOQU.value.equals(alertRoute)){
            token = suoqualert;
        }else if (AlertRoute.SJWT.value.equals(alertRoute)){
            token = shujuwenti;
        } else if (AlertRoute.JJSYJ.value.equals(alertRoute)){
            token = jinjishujuyujing;
        } else if (AlertRoute.ZZZLH.value.equals(alertRoute)){
            token = zengzhangzhoulihui;
        } else if (AlertRoute.FZB.value.equals(alertRoute)){
            token = fanzuobi;
        } else if (AlertRoute.GSHXZB.value.equals(alertRoute)){
            token = gongsihexinzhibiao;
        }
        else if (AlertRoute.FZBHX.value.equals(alertRoute)){
            token = fanzuobihexin;
        }else if (AlertRoute.JRECPM.value.equals(alertRoute)){
            token = jinriecpm;
        }else if (AlertRoute.ZCY.value.equals(alertRoute)){
            token = zcy;
        }else if (AlertRoute.GYHCHXQ.value.equals(alertRoute)){
            token = guiyinhuichuanhexin;
        }else if (AlertRoute.CESHIGSHXZB.value.equals(alertRoute)){
            token = ceshigongsihexinzhibiao;
        }else if (AlertRoute.WZZDHTFYJQ.value.equals(alertRoute)){
            token = wzzdhtfyj;
        }else if (AlertRoute.ZPH.value.equals(alertRoute)){
            token = zph;
        }
        String url = DINGTALKURL_PRE + "?access_token=" + token;
        Headers headers = new Headers.Builder().add("Content-Type", "application/json").build();
        JSONObject jsonObject = new JSONObject();
        JSONObject content = new JSONObject();

        content.put("title",title);
        content.put("text",msg);

        JSONObject atMobiles = new JSONObject();
        atMobiles.put("atMobiles", atMobilesList);

        jsonObject.put("msgtype", "markdown");
        jsonObject.put("markdown", content);
        jsonObject.put("at", atMobiles);


        log.info(jsonObject.toJSONString());
        RequestBody requestBody = RequestBody.create(null, jsonObject.toJSONString());
        return post(url, headers, requestBody);
    }


    public static void sendMarkdownMsg(AlertRecord alertRecord,Integer alertRoute) {
        String sendMsg = DingMsgService.getDingMarkdownMsg(alertRecord);
        String dmsg = sendMarkdownMsg(alertRecord.getModelName(),sendMsg, Lists.newArrayList(alertRecord.getAlertPhone().split(",")), alertRoute);
        log.info(dmsg);
    }

    public static void sendMarkdownMsgDelay(AlertRecord alertRecord, Integer alertRoute,AlertJobDelayModel alertJobDelayModel,List<String> productList){
        String sendMsg = DingMsgService.getDingMarkdownWithDelayTime(alertRecord,alertJobDelayModel,productList);
        String dmsg = sendMarkdownMsg(alertRecord.getModelName(),sendMsg,
                Lists.newArrayList(alertRecord.getAlertPhone().split(",")), alertRoute);
        log.info(dmsg);
    }

    public static String sendCardMsg(String key, String msg) {
        log.info("钉钉发送消息 key={},msg={}", key, msg);
        String url = DINGTALKURL_PRE + "?access_token=" + adworkAckToke;
        Headers headers = new Headers.Builder().add("Content-Type", "application/json").build();
        JSONObject jsonObject = new JSONObject();
        JSONObject atMobiles = new JSONObject();

        JSONObject cardObject = new JSONObject();

        atMobiles.put("atMobiles", dset);
        jsonObject.put("msgtype", "action_card");
        cardObject.put("title", "商业预警测试");
        JSONArray jsonArray = new JSONArray();

        JSONObject startChuliBut = new JSONObject();
//        title action
        startChuliBut.put("title","开始处理");
        startChuliBut.put("action_url","wwww.baidu.com");

        jsonArray.add(startChuliBut);

        JSONObject yiChuliBut = new JSONObject();
//        title action
        yiChuliBut.put("title","已处理");
        yiChuliBut.put("action_url","wwww.baidu.com");

        jsonArray.add(yiChuliBut);


        cardObject.put("btn_json_list", jsonArray);
        cardObject.put("btn_orientation", "1");


        StringBuilder stringBuilder = new StringBuilder();
        for (String phone : dset) {
            if (StringUtils.isNotBlank(phone)) {
                stringBuilder.append("@" + phone + " ");
            } else {
                stringBuilder.append(phone + " ");
            }
        }

        String s = "work [%s] \n" + "- %s 请处理: \n" + "%s";
        String sendMsg = String.format(s, key, msg, stringBuilder.toString());

        cardObject.put("markdown", sendMsg);

//        jsonObject.put("at", atMobiles);
        jsonObject.put("action_card", cardObject);

        log.info(jsonObject.toJSONString());
        RequestBody requestBody = RequestBody.create(null, jsonObject.toJSONString());
        return post(url, headers, requestBody);
    }

    public static void sendMsg(String accessToken, String key, String msg) {

        String url = DINGTALKURL_PRE + "?access_token=" + accessToken;
        Headers headers = new Headers.Builder().add("Content-Type", "application/json").build();
        JSONObject jsonObject = new JSONObject();
        JSONObject content = new JSONObject();
        JSONObject atMobiles = new JSONObject();
        jsonObject.put("msgtype", "text");
        String s = "[%s] \n" + "- %s ";
        String sendMsg = String.format(s, key, msg);

        content.put("content", sendMsg);

        jsonObject.put("at", atMobiles);
        jsonObject.put("text", content);
        RequestBody requestBody = RequestBody.create(null, jsonObject.toJSONString());
        post(url, headers, requestBody);
    }

    private static OkHttpClient client = new OkHttpClient.Builder().build();

    public static String get(String url) throws IOException {
        Request request = new Request.Builder().url(url).build();
        try {
            Response response = client.newCall(request).execute();
            return response.body().string();
        } catch (IOException e) {
            e.printStackTrace();
        }
        return null;
    }

    public static String post(String url, Headers headers, okhttp3.RequestBody requestBody) {
        Request request = new Request.Builder().url(url).post(requestBody).headers(headers).build();
        try {
            Response response = client.newCall(request).execute();
            return response.body().string();
        } catch (IOException e) {
            e.printStackTrace();
        }
        return null;
    }

    public static void sendMsgAndPhone(String accessToken, String key, String msg, String downLoadUrl, Collection<String> phoneArray) {
        String url = DINGTALKURL_PRE + "?access_token=" + accessToken;
        Headers headers = new Headers.Builder().add("Content-Type", "application/json").build();
        JSONObject param = new JSONObject();
        JSONObject actionCard = new JSONObject();
        JSONObject bntContent = new JSONObject();
        if(StringUtils.isNotBlank(downLoadUrl)){
            List<JSONObject> btnsContent = new ArrayList<>();
            bntContent.put("title","下载详情");
            bntContent.put("actionURL",downLoadUrl);
            btnsContent.add(bntContent);
            actionCard.put("btns", btnsContent.toArray());
        }
        actionCard.put("title", key);
        String s = "## **%s**\n\r%s";
        String sendMsg = String.format(s, key, msg);
        if (CollectionUtils.isNotEmpty(phoneArray)) {
            String atPhones = phoneArray.stream().filter(k -> StringUtils.isNotBlank(k)).map(k -> "@" + k).collect(Collectors.joining());
            sendMsg += "\n\r### " + atPhones;
        }
        actionCard.put("text", sendMsg);
        actionCard.put("btnOrientation", "0");

        param.put("title", key);
        param.put("msgtype", "actionCard");
        param.put("actionCard", actionCard);
        param.put("at", new JSONObject()
                .fluentPut("atMobiles", phoneArray)
                .fluentPut("isAtAll", false));
        RequestBody requestBody = RequestBody.create(null, param.toJSONString());

        post(url, headers, requestBody);
    }
}
