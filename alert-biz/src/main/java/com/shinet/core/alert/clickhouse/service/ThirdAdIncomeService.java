package com.shinet.core.alert.clickhouse.service;

import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateUtil;
import com.shinet.core.alert.adb.mapper.DailyResultMapper;
import com.shinet.core.alert.clickhouse.enums.Task;
import com.shinet.core.alert.clickhouse.mapper.ck1.AdIncomePvQueryMapper;
import com.shinet.core.alert.coreservice.entity.AlertRecord;
import com.shinet.core.alert.coreservice.enums.*;
import com.shinet.core.alert.coreservice.service.AlertRecordService;
import com.shinet.core.alert.dingtalk.DingTailService;
import com.shinet.core.alert.dingtalk.VedioAlertService;
import com.shinet.core.alert.dsp.entity.IncomeDetailBean;
import com.shinet.core.alert.dsp.entity.ThirdIncomeBean;
import com.shinet.core.alert.dsp.mapper.ThirdIncomeMapper;
import com.xxl.job.core.log.XxlJobLogger;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2023/11/7
 */
@Slf4j
@Service
public class ThirdAdIncomeService {

    @Resource
    private ThirdIncomeMapper thirdIncomeMapper;
    @Resource
    private AdIncomePvQueryMapper adIncomePvQueryMapper;
    @Autowired
    AlertRecordService alertRecordService;
    @Resource
    private DailyResultMapper dailyResultMapper;


//    @PostConstruct
//    public void test() {
//        checkDayIncome("daily_result");
//    }

    public void checkDayIncome(String checkTask){
        Task task = Task.getByTask(checkTask);
        if (task == null){
            return;
        }

        int gapLower = 2;
        String logday = DateUtil.format(DateUtil.yesterday(), DatePattern.NORM_DATE_FORMAT);

        //取TAG看看要不要使用备用
        Integer count = dailyResultMapper.getTag(logday);
        Double thirdInAl = adIncomePvQueryMapper.getAdUploadIncome(logday);
        if (count == 1){
            XxlJobLogger.log("收入已全出,使用真实值替换");
            List<ThirdIncomeBean> thirdIncomeBeanList = thirdIncomeMapper.queryDayThirdIncome(logday);
            thirdInAl = thirdIncomeBeanList.stream().mapToDouble(ThirdIncomeBean::getIncome).sum();
        }
        switch (task){
            case DAILY_RESULT:
                Double dailyIn = adIncomePvQueryMapper.getDailyResultIncome(logday);
                if (Math.abs(thirdInAl - dailyIn) > gapLower){
                    List<IncomeDetailBean> dailyResultDetail = adIncomePvQueryMapper.getDailyResultDetail(logday);
                    sendDingTalk(logday,task,thirdInAl,dailyIn,dailyResultDetail);
                }
                break;
            case DAILY_RESULT_CHANNEL:
                //看能不能和三方对
                Double dailyChannelIn = adIncomePvQueryMapper.getDailyResultChannelIncome(logday);
                if (Math.abs(thirdInAl - dailyChannelIn) > gapLower){
                    sendDingTalk(logday,task,thirdInAl,dailyChannelIn,null);
                }
                break;
            case REALTIME_RESULT:
                break;
            case DAILY_RESULT_ACC:
                break;
            default:
                break;
        }
    }

    private void sendDingTalk(String logday,Task task,Double inThird,Double inThisTable,List<IncomeDetailBean> detailList){
        StringBuffer detail = new StringBuffer();
        for (IncomeDetailBean detailData:detailList){
            detail.append("**"+detailData.getAdSource()+"** : "+detailData.getIncome()+" , "+detailData.getThirdIncome()+" , **gap**: **"+detailData.getGap()+"**  \n");
        }

        String alert = String.format("[%s]%s 统计到 %s 三方底表收入为:%s GAP:%s 请注意查看~",
                logday,task.getTask(),format(inThisTable),format(inThird),format(inThird-inThisTable));
        if (null != detailList){
            alert = String.format("[%s]%s 统计到 **%s** 三方底表收入为:**%s** GAP:**%s** 详细收入如下：\n\n %s ",
                    logday,task.getTask(),format(inThisTable),format(inThird),format(inThird-inThisTable),detail);
        }
        log.info("消息提示："+alert);
        AlertRecord alertRecord = alertRecordService.insertAlertRecord(AlertJobDelayModel.CHECK_INCOME.getJobId(),
                AlertJobDelayModel.CHECK_INCOME.getJobName(), AlertModel.DAILY_INCOME,alert
                , AlertStatus.INIT, DingTailService.dailyData, AlertType.DINGDING);
        DingTailService.sendMarkdownMsg(alertRecord, AlertRoute.SJXT.value);
        if (inThird-inThisTable > 5000){
            DingTailService.sendMarkdownMsg(alertRecord, AlertRoute.JJSYJ.value);
        }
    }

    private static String format(Double value){
        BigDecimal bd = new BigDecimal(value);
        bd = bd.setScale(2, RoundingMode.HALF_UP);
        return bd.toString();
    }
}
