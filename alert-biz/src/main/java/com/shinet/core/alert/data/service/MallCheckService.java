package com.shinet.core.alert.data.service;

import com.shinet.core.alert.coreservice.entity.AlertRecord;
import com.shinet.core.alert.coreservice.enums.AlertModel;
import com.shinet.core.alert.coreservice.enums.AlertStatus;
import com.shinet.core.alert.coreservice.enums.AlertType;
import com.shinet.core.alert.coreservice.service.AlertRecordService;
import com.shinet.core.alert.data.entity.AppMallDataCheck;
import com.shinet.core.alert.data.entity.MallSucCheck;
import com.shinet.core.alert.data.enums.OrderStatusEnum;
import com.shinet.core.alert.dingtalk.DingTailService;
import com.shinet.core.alert.dingtalk.VedioAlertService;
import com.shinet.core.alert.mall.mapper.MallMapper;
import com.shinet.core.alert.util.DateUtils;
import com.shinet.core.alert.util.DoubleUtil;
import com.xxl.job.core.log.XxlJobLogger;
import lombok.extern.slf4j.Slf4j;
import org.joda.time.DateTimeConstants;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.List;

@Component
@Slf4j
public class MallCheckService {
    @Autowired
    MallMapper mallMapper;
    @Autowired
    AlertRecordService alertRecordService;
    public void checkMallErrorAlert(String jobName,int maxErrorNum) {
        Date startDate = new Date(System.currentTimeMillis() - DateTimeConstants.MILLIS_PER_HOUR);
        List<AppMallDataCheck> dataCheckList = mallMapper.queryMallError(DateUtils.formatDate(startDate), DateUtils.formatDate(new Date()));

        for (AppMallDataCheck appMallDataCheck : dataCheckList) {
            try {
                String appName = appMallDataCheck.getAppName();
                int errorNum = appMallDataCheck.getCnum();
                String wechatMessage = appMallDataCheck.getWechatMessage();
                String erMsg = appName + " " + wechatMessage + " 报错数字为：" + errorNum + " 上限为 " + maxErrorNum;
                if (errorNum > maxErrorNum) {
                    VedioAlertService.sendVocMsg(" 提现 ", erMsg);
                    AlertRecord alertRecord = alertRecordService.insertAlertRecord(jobName,"提现报错", AlertModel.MALL,erMsg,maxErrorNum*1.0d,0d,0,errorNum,
                            AlertStatus.INIT,DingTailService.dset, AlertType.DINGPHONE);
                    DingTailService.sendMarkdownMsg(alertRecord);
                }
                log.info(erMsg);
                XxlJobLogger.log(appName + " " + wechatMessage + " 报错数字为：" + errorNum + " 上限为 " + maxErrorNum);
            } catch (Exception e) {
                log.error("", e);
            }
        }
    }

    static SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyyMMdd");

    public List<MallSucCheck> getMallSuc(Date date, int jianeMin) {
        Date startDate = new Date(date.getTime() - jianeMin * DateTimeConstants.MILLIS_PER_MINUTE);
        String tend = simpleDateFormat.format(date);
        log.info("start: " + DateUtils.formatDate(startDate) + " end:" + DateUtils.formatDate(date));
        XxlJobLogger.log("start: " + DateUtils.formatDate(startDate) + " end:" + DateUtils.formatDate(date));
        List<MallSucCheck> mallSucCheckList = mallMapper.queryMallSuc(DateUtils.formatDate(startDate), DateUtils.formatDate(date), tend);
        return mallSucCheckList;
    }

    public MallSucCheck getMallSucSig(Date date, int jianeMin) {
        Date startDate = new Date(date.getTime() - jianeMin * DateTimeConstants.MILLIS_PER_MINUTE);
        String tend = simpleDateFormat.format(date);
        log.info("start: " + DateUtils.formatDate(startDate) + " end:" + DateUtils.formatDate(date));
        XxlJobLogger.log("start: " + DateUtils.formatDate(startDate) + " end:" + DateUtils.formatDate(date));
        List<MallSucCheck> mallSucCheckList = mallMapper.queryMallSuc(DateUtils.formatDate(startDate), DateUtils.formatDate(date), tend);
        return mallSucCheckList.stream().filter(mallSucCheck -> mallSucCheck.getOrderStatus()==OrderStatusEnum.WITHDRAW_SUCCESS.getValue()).findFirst().get();
    }

    public void checkMallSucAlert(String jobName,float chaLv) {
        int min = 5;

        Date date = new Date();
        if (date.getHours() < 1 && date.getMinutes() < 41) {
            //24点不做预警
            return;
        }
        List<MallSucCheck> mallSucCheckList = getMallSuc(date, min);
        MallSucCheck mallSucCheck = mallSucCheckList.stream().filter(mallSucCheck1 -> OrderStatusEnum.WITHDRAW_SUCCESS.getValue()==mallSucCheck1.getOrderStatus()).findFirst().get();
        long allFailedNum = mallSucCheckList.stream().mapToInt(mallSucCheck1->{
            if(mallSucCheck1.getOrderStatus()!=OrderStatusEnum.WITHDRAW_SUCCESS.getValue()){
                return mallSucCheck1.getSucNum();
            }else {
                return 0;
            }
        }).sum();
        String amsg = "订单发放积压 已经积压"+allFailedNum+"";
        XxlJobLogger.log(amsg);
        if(allFailedNum>5000){
            VedioAlertService.sendVocMsg(" 提现 ", amsg);
            AlertRecord alertRecord = alertRecordService.insertAlertRecord(jobName,"订单发放积压", AlertModel.MALL,amsg,0d,0d,0,(int)allFailedNum,
                    AlertStatus.INIT,DingTailService.dset, AlertType.DINGPHONE);
            DingTailService.sendMarkdownMsg(alertRecord);
        }
        Date dateLastTenMin = new Date(date.getTime() - min * DateTimeConstants.MILLIS_PER_MINUTE);
        List<MallSucCheck> mallSucCheckLastTenList = getMallSuc(dateLastTenMin, min);
        MallSucCheck mallSucCheckLastTen = mallSucCheckLastTenList.stream().filter(mallSucCheck1 -> OrderStatusEnum.WITHDRAW_SUCCESS.getValue()==mallSucCheck1.getOrderStatus()).findFirst().get();
        long allFaileLastdNum = mallSucCheckLastTenList.stream().mapToInt(mallSucCheck1->{
            if(mallSucCheck1.getOrderStatus()!=OrderStatusEnum.WITHDRAW_SUCCESS.getValue()){
                return mallSucCheck1.getSucNum();
            }else {
                return 0;
            }
        }).count();
        double sucRate = DoubleUtil.divideDouble(mallSucCheck.getSucNum(), (int) (mallSucCheck.getSucNum()+allFailedNum));

        double dff = DoubleUtil.getDoubleByTwo(1 - (mallSucCheck.getSucNum() * 1.0d) / (mallSucCheckLastTen.getSucNum() * 1.0d));
        String msg = min+"支付->{" + mallSucCheck.getSucNum() + " : " + mallSucCheckLastTen.getSucNum()+"} failed->{"+allFailedNum+","+allFaileLastdNum+"} sucRate->"+sucRate+" lastminrate->"+dff;
        XxlJobLogger.log(msg);
        log.info(msg);

//        if(mallSucCheck.getSucNum()<200){
//            VedioAlertService.sendVocMsg(" 提现 ", msg);
//            DingTailService.sendMsg(" 提现 ", msg);
//        }
        if (mallSucCheck.getSucNum() < mallSucCheckLastTen.getSucNum()) {
            if ((1-sucRate)>chaLv || (dff > chaLv && (1-sucRate)>chaLv)) {
                VedioAlertService.sendVocMsg(" 提现 ", msg);
                AlertRecord alertRecord = alertRecordService.insertAlertRecord(jobName,"提现报错", AlertModel.MALL,msg,chaLv*0.1d,(1-sucRate),mallSucCheck.getSucNum(),0,
                        AlertStatus.INIT,DingTailService.dset,AlertType.DINGPHONE);
                DingTailService.sendMarkdownMsg(alertRecord);
                XxlJobLogger.log("预警： " + msg);
            } else {
                msg = msg + " 波动率为： " + dff;
                XxlJobLogger.log(msg);
            }
        }
    }


    /**
     * 昨天同时间20min 环比
     *
     * @param chaLv
     */
    public void checkMallDoubleDaySucAlert(String jobName,float chaLv) {
        int min = 20;

        Date date = new Date();
        MallSucCheck mallSucCheck = getMallSucSig(date, min);

        Date dateLastTenMin = new Date(date.getTime() - DateTimeConstants.MILLIS_PER_DAY);
        MallSucCheck mallSucCheckLastTen = getMallSucSig(dateLastTenMin, min);

        String msg = "支付成功订单为： " + mallSucCheck.getSucNum() + " 前" + min + "min 为 " + mallSucCheckLastTen.getSucNum();
        XxlJobLogger.log(msg);
        log.info(msg);

        if (mallSucCheck.getSucNum() < mallSucCheckLastTen.getSucNum()) {
            float dff = 1 - (mallSucCheck.getSucNum() * 1.0f) / (mallSucCheckLastTen.getSucNum() * 1.0f);
            if (dff > chaLv) {
                msg = msg + " 波动率为： " + dff;
                VedioAlertService.sendVocMsg(" 提现 ", msg);
                AlertRecord alertRecord =alertRecordService.insertAlertRecord(jobName,"提现", AlertModel.MALL,msg,chaLv*0.1d,dff*1.0d,mallSucCheck.getSucNum(),0,
                        AlertStatus.INIT,DingTailService.dset1,AlertType.DINGPHONE);
                DingTailService.sendMarkdownMsg(alertRecord);
                XxlJobLogger.log("预警： " + msg);
            } else {
                msg = msg + " 波动率为： " + dff;
                XxlJobLogger.log(msg);
            }
        }
    }
}
