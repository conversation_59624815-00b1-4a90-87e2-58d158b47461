package com.shinet.core.alert.dingtalk;


import com.alibaba.fastjson.JSON;
import com.aliyun.dyvmsapi20170525.models.SingleCallByTtsRequest;
import com.aliyun.dyvmsapi20170525.models.SingleCallByTtsResponse;
import com.aliyun.tea.TeaConverter;
import com.aliyun.tea.TeaPair;
import com.aliyun.teaopenapi.models.Config;
import lombok.extern.slf4j.Slf4j;

import java.util.Set;

@Slf4j
public class VedioAlertService {

    private static String regionId = "cn-beijing";
    public static final String AcesskeyId = "LTAI5tNtC1rEPb8xpDzBoQW5";
    public static final String secertKey = "******************************";
    /**
     * 使用AK&SK初始化账号Client
     * @param accessKeyId
     * @param accessKeySecret
     * @param regionId
     * @return Client
     * @throws Exception
     */
    public static com.aliyun.dyvmsapi20170525.Client createClient(String accessKeyId, String accessKeySecret, String regionId) throws Exception {
        Config config = new Config();
        // 您的AccessKey ID
        config.accessKeyId = accessKeyId;
        // 您的AccessKey Secret
        config.accessKeySecret = accessKeySecret;
        // 您的可用区ID
        config.regionId = regionId;
        return new com.aliyun.dyvmsapi20170525.Client(config);
    }


    public static void sendVocMsg(String name,String msg){
        msg = msg.replace(".","");
        for(String phone : DingTailService.dset){
            call(phone,name,msg);
        }
    }

    public static void sendVocMsg(String name,String msg,String phone){
        msg = msg.replace(".","");
        call(phone,name,msg);
    }

    public static void sendVocMsg(String name, String msg, Set<String> set){
        msg = msg.replace(".","");
        for(String phone : set){
            call(phone,name,msg);
        }
    }

    public static void sendVocMsgWithBiRui(String name, String msg, Set<String> set){
        msg = msg.replace(".","");
        for(String phone : set){
            callWithBiRui(phone,name,msg);
        }
    }

    private static void callWithBiRui(String phone,String name,String msg){

        try {
            com.aliyun.dyvmsapi20170525.Client client = createClient(AcesskeyId, secertKey, regionId);
            SingleCallByTtsRequest request = SingleCallByTtsRequest.build(TeaConverter.buildMap(
                    // 被叫显号，若您使用的模板为公共号池号码外呼模板，则该字段值必须为空；
                    // 若您使用的模板为专属号码外呼模板，则必须传入已购买的号码，仅支持一个号码，您可以在语音服务控制台上查看已购买的号码。
//                new TeaPair("calledShowNumber", "15652694117"),
                    // 被叫号码。仅支持中国内地号码。一次请求仅支持一个被叫号。
                    new TeaPair("calledNumber", phone),
                    new TeaPair("TtsParam", "{\"project\":\""+name+"\",\"dio\":\""+msg+"\"}"),
                    // 语音文件的语音ID。
                    new TeaPair("TtsCode", "TTS_222310502")
            ));
            SingleCallByTtsResponse response = client.singleCallByTts(request);
            log.info(JSON.toJSONString(response.getBody()));
        }catch (Exception e){
            log.error("",e);
        }
    }

    private static void call(String phone,String name,String msg){
        //毕锐不再打电话
        if(phone.equals("18600046769")) {
            return;
        }
        try {
            com.aliyun.dyvmsapi20170525.Client client = createClient(AcesskeyId, secertKey, regionId);
            SingleCallByTtsRequest request = SingleCallByTtsRequest.build(TeaConverter.buildMap(
                    // 被叫显号，若您使用的模板为公共号池号码外呼模板，则该字段值必须为空；
                    // 若您使用的模板为专属号码外呼模板，则必须传入已购买的号码，仅支持一个号码，您可以在语音服务控制台上查看已购买的号码。
//                new TeaPair("calledShowNumber", "15652694117"),
                    // 被叫号码。仅支持中国内地号码。一次请求仅支持一个被叫号。
                    new TeaPair("calledNumber", phone),
                    new TeaPair("TtsParam", "{\"project\":\""+name+"\",\"dio\":\""+msg+"\"}"),
                    // 语音文件的语音ID。
                    new TeaPair("TtsCode", "TTS_222310502")
            ));
            SingleCallByTtsResponse response = client.singleCallByTts(request);
            log.info(JSON.toJSONString(response.getBody()));
        }catch (Exception e){
            log.error("",e);
        }
    }
    /**
     * @param args
     * @throws Exception
     */
    public static void main(String[] args) throws Exception {
        sendVocMsg("商业","for test ");
    }
}
