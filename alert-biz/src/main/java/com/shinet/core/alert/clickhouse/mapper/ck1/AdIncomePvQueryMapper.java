package com.shinet.core.alert.clickhouse.mapper.ck1;

import com.shinet.core.alert.clickhouse.entity.AdIncomePvEntity;
import com.shinet.core.alert.clickhouse.entity.OsProductEcpmCostBean;
import com.shinet.core.alert.clickhouse.entity.RealtimeProductGap;
import com.shinet.core.alert.dsp.entity.IncomeDetailBean;
import com.shinet.core.alert.dsp.entity.ThirdIncomeBean;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2022/9/13
 */
public interface AdIncomePvQueryMapper {

    @Select({"<script>",
            "select product, product_name,os, platform, " +
                    "       t1.pos_id as pos_id, " +
                    "       ifNull(pv_third,0) as pv_third, " +
                    "       ifNull(t1.income,0) as income_third, " +
                    "       t2.pv as pv, " +
                    "       if(t2.ecpm &lt;= 10 and t1.pos_name not like '%bidding%',t1.income,t2.income) as income, " +
                    "       (pv-pv_third)/pv_third *100 as pv_rate, " +
                    "       (income-income_third)/income_third * 100 as income_rate " +
                    "       from ( " +
                    "                  select * " +
                    "                  from old_mysql_ads.business_ad_cp_info " +
                    "                  where logday = toDate(#{logday}) and type_name in ('视频','插屏','新插屏') and platform in " +
                    " <foreach item='item' index='index' collection='platformList' open='(' separator=',' close=')'>" +
                    " #{item} </foreach> " +
                    "                  ) t1 left join ( " +
                    "    select r2.pos_id as ad_id, r1.pv as pv, ifNull(r4.ecpm,0) as ecpm, ecpm * r1.pv / 1000 as income " +
                    "    from ( " +
                    "             select ad_id, count() as pv " +
                    "             from ods.event_exposure_dist " +
                    "             where logday = toDate(#{logday}) " +
                    "               and ad_action = 'exposure' " +
                    "               and ad_id global not in (select toString(ad_id) from dwd.tb_ap_bidding_dist where del_flag = 1) " +
                    "               and ad_id != '' " +
                    "             group by ad_id " +
                    "             ) r1 " +
                    "             left join " +
                    "         (select ad_id, pos_id from dwd.product_ad_conf_dist group by ad_id, pos_id) r2 on r1.ad_id = r2.ad_id " +
                    "             left join (select * from dwd.pos_ecpm_dist where logday = toDate(#{logday}) -1 ) r3 on r2.pos_id = r3.pos_id " +
                    "             left join (select * from dwd.tb_ap_ad_budget_dist) r4 on r1.ad_id = toString(r4.id) " +
                    "    union all " +
                    "    select pos_id, count() as pv, avg(toFloat64OrZero(extend1)) as ecpm, sum(toFloat64OrZero(extend1) / 1000) as income " +
                    "    from ods.event_exposure_dist " +
                    "    where logday = toDate(#{logday}) " +
                    "      and ad_action = 'exposure' " +
                    "      and ad_id global in (select toString(ad_id) from dwd.tb_ap_bidding_dist where del_flag = 1) " +
                    "      and ad_id != '' " +
                    "    group by pos_id " +
                    "    ) t2 on toString(t1.pos_id) = t2.ad_id " +
                    " where income &gt; 500 and pv &gt; 5000 " +
                    " and (abs(pv_rate) &gt; 50 or abs(income_rate) &gt; 50);",
            "</script>"
    })
    List<AdIncomePvEntity> queryPosIdGap(@Param("logday")String logday, @Param("platformList")List<String> platformList);


    @Select({"<script>",
            "select product, product_name, os, pvs as pv, pv_third, income, ra  " +
                    "from (  " +
                    "      select product,  " +
                    "             product_name,  " +
                    "             os,  " +
                    "             sum(pv)                                                  as pvs,  " +
                    "             sum(ifNull(pv_third, 0))                                 as pv_third,  " +
                    "             sum(ifNull(income, 0))                                   as income,  " +
                    "             sum(ifNull(r2.ecpm, 0) * pv / 1000)                      as income_own,  " +
                    "             if(pv_third = 0, 100, (pvs - pv_third) / pv_third * 100) as ra  " +
                    "      from old_mysql_ads.business_ad_cp_info r1  " +
                    "               left join ods_mysql.tb_ap_ad_budget r2 on r1.ad_id = r2.id  " +
                    "      where logday = #{logday}  and platform in " +
                    " <foreach item='item' index='index' collection='platformList' open='(' separator=',' close=')'>" +
                    " #{item}" +
                    " </foreach> " +
                    "        and product != ''  " +
                    "      group by product, product_name, os  " +
                    "      having income_own &gt; 300  " +
                    "         )  " +
                    "where ra &gt; 30  " +
                    "   or ra &lt; -30  " +
                    "order by product, ra;",
            "</script>"
    })
    List<AdIncomePvEntity> queryProductGap(@Param("logday")String logday, @Param("platformList")List<String> platformList);

    @Select({"<script>",
            "select platform,  " +
                    "       sum(ifNull(t1.income,0)) as income_third,  " +
                    "       sum(if(t1.pos_name like '%000%',t1.income,t2.income)) as income,  " +
                    "       (income-income_third)/income_third * 100 as income_rate  " +
                    "       from (  " +
                    "                  select *  " +
                    "                  from old_mysql_ads.business_ad_cp_info  " +
                    "                  where logday = toDate(#{logday}) and type_name in ('视频','插屏','新插屏')  " +
                    "                    and platform in  " +
                    " <foreach item='item' index='index' collection='platformList' open='(' separator=',' close=')'> " +
                    " #{item} </foreach>  " +
                    "                  ) t1 left join (  " +
                    "    select r2.pos_id as ad_id, r1.pv as pv, ifNull(r4.ecpm,0) as ecpm, ecpm * r1.pv / 1000 as income  " +
                    "    from (  " +
                    "             select ad_id, count() as pv  " +
                    "             from ods.event_exposure_dist  " +
                    "             where logday = toDate(#{logday})  " +
                    "               and ad_action = 'exposure'  " +
                    "               and ad_id global not in (select toString(ad_id) from dwd.tb_ap_bidding_dist where del_flag = 1)  " +
                    "               and ad_id != ''  " +
                    "             group by ad_id  " +
                    "             ) r1  " +
                    "             left join  " +
                    "         (select ad_id, pos_id from dwd.product_ad_conf_dist group by ad_id, pos_id) r2 on r1.ad_id = r2.ad_id  " +
                    "             left join (select * from dwd.pos_ecpm_dist where logday = toDate(#{logday}) -1 ) r3 on r2.pos_id = r3.pos_id  " +
                    "             left join (select * from dwd.tb_ap_ad_budget_dist) r4 on r1.ad_id = toString(r4.id)  " +
                    "    union all  " +
                    "    select pos_id, count() as pv, avg(toFloat64OrZero(extend1)) as ecpm, sum(toFloat64OrZero(extend1) / 1000) as income  " +
                    "    from ods.event_exposure_dist  " +
                    "    where logday = toDate(#{logday})  " +
                    "      and ad_action = 'exposure'  " +
                    "      and ad_id global in (select toString(ad_id) from dwd.tb_ap_bidding_dist where del_flag = 1)  " +
                    "      and ad_id != ''  " +
                    "    group by pos_id  " +
                    "    ) t2 on toString(t1.pos_id) = t2.ad_id  " +
                    " group by platform " +
                    " having abs(income_rate) &gt; 5" +
                    " order by platform",
            "</script>"
    })
    List<AdIncomePvEntity> queryPlatformGap(@Param("logday")String logday, @Param("platformList")List<String> platformList);

    @Select({"<script>",
            "select product, product_name,os,  " +
                    "       sum(ifNull(t1.income,0)) as income_third,  " +
                    "       sum(t2.income) as income,  " +
                    "       (income-income_third)/income_third * 100 as income_rate  " +
                    "       from (  " +
                    "                  select *  " +
                    "                  from old_mysql_ads.business_ad_cp_info  " +
                    "                  where logday = toDate(#{logday}) " +
                    "                    and product global in ( " +
                    "                        select product from ( " +
                    "                                             select product, uniqExact(device_id) as dau " +
                    "                                             from dwd.au_device_dist " +
                    "                                             where logday = toDate(#{logday}) " +
                    "                                             group by product " +
                    "                                             having dau &gt; 100000 " +
                    "                                                ) " +
                    "                        ) " +
                    "                    and platform in  " +
                    " <foreach item='item' index='index' collection='platformList' open='(' separator=',' close=')'> " +
                    " #{item} </foreach>  " +
                    "                  ) t1 left join (  " +
                    "    select r2.pos_id as ad_id, r1.pv as pv, ifNull(r3.ecpm, r4.ecpm) as ecpm, ecpm * r1.pv / 1000 as income  " +
                    "    from (  " +
                    "             select ad_id, count() as pv  " +
                    "             from ods.event_exposure_dist  " +
                    "             where logday = toDate(#{logday})  " +
                    "               and ad_action = 'exposure'  " +
                    "               and ad_id global not in (select toString(ad_id) from dwd.tb_ap_bidding_dist where del_flag = 1)  " +
                    "               and ad_id != ''  " +
                    "             group by ad_id  " +
                    "             ) r1  " +
                    "             left join  " +
                    "         (select ad_id, pos_id from dwd.product_ad_conf_dist group by ad_id, pos_id) r2 on r1.ad_id = r2.ad_id  " +
                    "             left join (select * from dwd.pos_ecpm_dist where logday = toDate(#{logday}) -1 ) r3 on r2.pos_id = r3.pos_id  " +
                    "             left join (select * from dwd.tb_ap_ad_budget_dist) r4 on r1.ad_id = toString(r4.id)  " +
                    "    union all  " +
                    "    select pos_id, count() as pv, avg(toFloat64OrZero(extend1)) as ecpm, sum(toFloat64OrZero(extend1) / 1000) as income  " +
                    "    from ods.event_exposure_dist  " +
                    "    where logday = toDate(#{logday})  " +
                    "      and ad_action = 'exposure'  " +
                    "      and ad_id global in (select toString(ad_id) from dwd.tb_ap_bidding_dist where del_flag = 1)  " +
                    "      and ad_id != ''  " +
                    "    group by pos_id  " +
                    "    ) t2 on toString(t1.pos_id) = t2.ad_id  " +
                    " group by product,product_name,os ",
            " having income_rate &gt; 5",
            "</script>"
    })
    List<AdIncomePvEntity> queryProductGapMorethan10WDau(@Param("logday")String logday, @Param("platformList")List<String> platformList);


    @Select({"select (  " +
            "        select sum(CAST(${d1},'Float64')) from dwd.${d2} where toDate(${d3}) = '${d4}'  " +
            "           )/ (  " +
            "        select sum(CAST(${d1},'Float64')) from dwd.${d2} where toDate(${d3}) = toDate('${d4}') -1  " +
            "    ) > 0.8 as checker"})
    Integer checkIncome(@Param("d1")String d1,@Param("d2")String d2,@Param("d3")String d3,@Param("d4")String d4);

    @Select({"select (  " +
            "        select sum(CAST(${d1},'Float64')) from ods_mysql.${d2} where toDate(${d3}) = '${d4}'  " +
            "           )/ (  " +
            "        select sum(CAST(${d1},'Float64')) from ods_mysql.${d2} where toDate(${d3}) = toDate('${d4}') -1  " +
            "    ) > 0.8 as checker"})
    Integer checkIncomeOds(@Param("d1")String d1,@Param("d2")String d2,@Param("d3")String d3,@Param("d4")String d4);

    @Select("select (  " +
            "        select sum(CAST(revenue,'Float64')) from dwd.tencent_ad_report_data where toDate(date) = '${d4}' and toInt64OrZero(placement_id) > 0  " +
            "           )/ (  " +
            "        select sum(CAST(revenue,'Float64')) from dwd.tencent_ad_report_data where toDate(date) = toDate('${d4}') -1 and toInt64OrZero(placement_id) > 0  " +
            "    ) > 0.8 as checker")
    Integer checkIncomeGdt(@Param("d4")String d4);


    @Select(" select product, " +
            "       product_name, " +
            "       os, " +
            "       sum(income)                                            as own_income, " +
            "       sum(third_income)                                      as third_income, " +
            "       sum(pv)                                                as own_pv, " +
            "       sum(third_pv)                                          as third_pv, " +
            "       own_income / own_pv * 1000                             as own_avg_ecpm, " +
            "       third_income / third_pv * 1000                         as third_avg_ecpm, " +
            "       (own_avg_ecpm - third_avg_ecpm) / own_avg_ecpm * 100 as rate_ecpm, " +
            "       (own_pv - third_pv) / own_pv * 100 as rate_pv, " +
            "       (own_income - third_income) / own_income * 100 as rate_income " +
            " from ( " +
            "         select a1.ad_id                                  as ad_id, " +
            "                a2.pos_id                                 as pos_id, " +
            "                ifNull(a3.ecpm, a4.ecpm)                  as ecpm, " +
            "                a1.pv                                     as pv, " +
            "                (a1.pv * ifNull(a3.ecpm, a4.ecpm)) / 1000 as income " +
            "         from ( " +
            "                  select ad_id, count() as pv " +
            "                  from ods.event_exposure_dist " +
            "                  where logday = today() " +
            "                    and ad_action = 'exposure' " +
            "                    and hour <= toHour(toDateTime(now())) - 2 " +
            "                    and ad_type global in " +
            "                        (select toString(ad_type) from dwd.ad_type_basic_dist where source_name = '广点通') " +
            "                    and ad_id global not in ( " +
            "                      select toString(ad_id) " +
            "                      from ods_mysql.tb_ap_bidding " +
            "                      where del_flag = 1 " +
            "                  ) " +
            "                  group by ad_id " +
            "                  ) a1 " +
            "                  left join (select ad_id, pos_id from dwd.product_ad_conf_dist group by pos_id, ad_id) a2 " +
            "                            on a1.ad_id = a2.ad_id " +
            "                  left join (select * from dwd.pos_ecpm_dist where logday = yesterday() ) a3 on a2.pos_id = a3.pos_id " +
            "                  left join ods_mysql.tb_ap_ad_budget a4 on a1.ad_id = toString(a4.id) " +
            "         union all " +
            "         select ad_id, " +
            "                pos_id, " +
            "                avg(toFloat64OrZero(extend1)) as ecpm, " +
            "                count()                       as pv, " +
            "                sum(toFloat64OrZero(extend1)/1000) as income " +
            "         from ods.event_exposure_dist " +
            "         where logday = today() " +
            "           and ad_action = 'exposure' " +
            "           and hour <= toHour(toDateTime(now())) - 2 " +
            "           and ad_type global in " +
            "               (select toString(ad_type) from dwd.ad_type_basic_dist where source_name = '广点通') " +
            "           and ad_id global in ( " +
            "             select toString(ad_id) " +
            "             from ods_mysql.tb_ap_bidding " +
            "             where del_flag = 1 " +
            "         ) " +
            "         group by ad_id, pos_id " +
            "         ) t1 " +
            "         left join ( " +
            "    select placement_id, " +
            "           placement_name, " +
            "           ifNull(sum(toFloat64OrZero(pv)), 0) as third_pv, " +
            "           ifNull(sum(revenue) as income, 0)   as third_income, " +
            "           avg(ifNull(ecpm, 0))                as third_ecpm " +
            "    from ods_mysql.tencent_ad_report_data_hour " +
            "    where toDate(date) = today() " +
            "      and hour <= toHour(toDateTime(now())) - 2 " +
            "    group by placement_id, placement_name " +
            "    ) t2 on t1.pos_id = toString(t2.placement_id) " +
            "         left join (select product,product_name, os, pos_id from dwd.product_ad_conf_dist group by product, os, pos_id,product_name) t3 " +
            "                   on t1.pos_id = t3.pos_id " +
            "where t3.pos_id != '' " +
            "group by product,product_name, os " +
            "having own_income > 500 ")
    List<RealtimeProductGap> queryTodayGdtGap();


    @Select(" select product,  " +
            "       product_name,  " +
            "       os,  " +
            "       sum(income)                                            as own_income,  " +
            "       sum(third_income)                                      as third_income,  " +
            "       sum(pv)                                                as own_pv,  " +
            "       sum(third_pv)                                          as third_pv,  " +
            "       own_income / own_pv * 1000                             as own_avg_ecpm,  " +
            "       third_income / third_pv * 1000                         as third_avg_ecpm,  " +
            "       (own_avg_ecpm - third_avg_ecpm) / own_avg_ecpm * 100 as rate_ecpm,  " +
            "       (own_pv - third_pv) / own_pv * 100 as rate_pv,  " +
            "       (own_income - third_income) / own_income * 100 as rate_income  " +
            " from (  " +
            "         select a1.ad_id                                  as ad_id,  " +
            "                a2.pos_id                                 as pos_id,  " +
            "                ifNull(a3.ecpm, a4.ecpm)                  as ecpm,  " +
            "                a1.pv                                     as pv,  " +
            "                (a1.pv * ifNull(a3.ecpm, a4.ecpm)) / 1000 as income  " +
            "         from (  " +
            "                  select ad_id, count() as pv  " +
            "                  from ods.event_exposure_dist  " +
            "                  where logday = today()  " +
            "                    and ad_action = 'exposure'  " +
            "                    and hour <= toHour(toDateTime(now())) - 2  " +
            "                    and ad_type global in  " +
            "                        (select toString(ad_type) from dwd.ad_type_basic_dist where source_name = '穿山甲')  " +
            "                    and ad_id global not in (  " +
            "                      select toString(ad_id)  " +
            "                      from ods_mysql.tb_ap_bidding  " +
            "                      where del_flag = 1  " +
            "                  )  " +
            "                  group by ad_id  " +
            "                  ) a1  " +
            "                  left join (select ad_id, pos_id from dwd.product_ad_conf_dist group by pos_id, ad_id) a2  " +
            "                            on a1.ad_id = a2.ad_id  " +
            "                  left join (select * from dwd.pos_ecpm_dist where logday = yesterday() ) a3 on a2.pos_id = a3.pos_id  " +
            "                  left join ods_mysql.tb_ap_ad_budget a4 on a1.ad_id = toString(a4.id)  " +
            "         union all  " +
            "         select ad_id,  " +
            "                pos_id,  " +
            "                avg(toFloat64OrZero(extend1)) as ecpm,  " +
            "                count()                       as pv,  " +
            "                sum(toFloat64OrZero(extend1)/1000) as income  " +
            "         from ods.event_exposure_dist  " +
            "         where logday = today()  " +
            "           and ad_action = 'exposure'  " +
            "           and hour <= toHour(toDateTime(now())) - 2  " +
            "           and ad_type global in  " +
            "               (select toString(ad_type) from dwd.ad_type_basic_dist where source_name = '穿山甲')  " +
            "           and ad_id global in (  " +
            "             select toString(ad_id)  " +
            "             from ods_mysql.tb_ap_bidding  " +
            "             where del_flag = 1  " +
            "         )  " +
            "         group by ad_id, pos_id  " +
            "         ) t1  " +
            "         left join (  " +
            "    select ad_slot_id,  " +
            "           ifNull(sum(show_num), 0) as third_pv,  " +
            "           ifNull(sum(revenue) as income, 0)   as third_income,  " +
            "           avg(ifNull(ecpm, 0))                as third_ecpm  " +
            "    from ods_mysql.toutiao_ad_report_data_code_v2  " +
            "    where toDate(date) = today()  " +
            "    group by ad_slot_id  " +
            "             having third_ecpm > 0  " +
            "    ) t2 on t1.pos_id = toString(t2.ad_slot_id)  " +
            "         left join (select product,product_name, os, pos_id from dwd.product_ad_conf_dist group by product, os, pos_id,product_name) t3  " +
            "                   on t1.pos_id = t3.pos_id  " +
            " where t3.pos_id != ''  " +
            " group by product,product_name, os  " +
            " having own_income > 500;")
    List<RealtimeProductGap> queryTodayCsjGap();


    @Select({
            "<script>",
            " select product,product_name,os,platform,sum(ifNull(income,0)) as income, " +
                    "       sum(ifNull(third_pv, 0))               as pv_third, " +
                    "       sum(ifNull(pv, 0))                     as pv, " +
                    "       sum(third_income)                      as income_third, " +
                    "       (income - income_third) / income_third * 100 as income_rate, " +
                    "       (pv - pv_third) / pv_third * 100             as pv_rate " +
                    " from old_mysql_ads.daily_ad_income " +
                    " where logday =toDate(#{logday}) and platform in " +
                    " <foreach item='item' index='index' collection='platformList' open='(' separator=',' close=')'>" +
                    " #{item} </foreach> ",
            " group by product, product_name, os, platform " ,
            " having income  &gt;  5000 and (abs(income_rate) &gt; 5 or abs(pv_rate) &gt; 5) ",
            "</script>"
    })
    List<AdIncomePvEntity> queryIncomeOver5KGap5(@Param("logday")String logday, @Param("platformList")List<String> platformList);

    @Select({"select product, os, sum(pv) as pv, sum(third_pv) as pv_third, " +
            "       sum(income) as income, sum(third_income) as income_third, " +
            "       (pv-pv_third)/pv * 100 as pv_rate, " +
            "       (income-income_third)/income * 100 as income_rate " +
            " from old_mysql_ads.realtime_ad_income " +
            " where logday = today() " +
            " group by product, os " +
            " having income > 5000 and  abs(income_rate) >5"})
    List<AdIncomePvEntity> queryRealtimeProductGap();

    @Select({" select product, os,pos_name, sum(pv) as pv, sum(third_pv) as pv_third, " +
            "       sum(income) as income, sum(third_income) as income_third, " +
            "       (pv-pv_third)/pv * 100 as pv_rate, " +
            "       (income-income_third)/income * 100 as income_rate " +
            " from old_mysql_ads.realtime_ad_income " +
            " where logday = today() " +
            " group by product, os, pos_name " +
            " having income > 1000 and  abs(income_rate) >20"})
    List<AdIncomePvEntity> queryRealtimePosIdGap();

    @Select({"select pos_id,pos_name from old_mysql_ads.business_ad_cp_info where logday = #{logday}"})
    List<AdIncomePvEntity> queryPosIdMap(String logday);


    @Select("select logday,platform as dsp,sum(income) as income from dwd.third_ad_data_dist where logday= #{logday} " +
            "and app_id !='0' " +
            "group by logday,platform")
    List<ThirdIncomeBean> queryThirdDataDist(@Param("logday")String logday);

    @Select("select sum(total_income) from ads.daily_result where logday=#{logday} ")
    Double getDailyResultIncome(@Param("logday")String logday);

    @Select("select sum(income) from dwd.ad_upload where logday=#{logday} ")
    Double getAdUploadIncome(@Param("logday")String logday);

    @Select("select sum(au_income) from ads.daily_result_channel where logday=#{logday} ")
    Double getDailyResultChannelIncome(@Param("logday")String logday);

    @Select("select sum(income) from dwd.ad_device_exposure_income_dist where logday=#{logday}")
    Double getMapIncome(@Param("logday")String logday);

    @Select("select\n" +
            "ad_source,\n" +
            "round(income,2) as income,\n" +
            "case\n" +
            "when ad_source like '%爱奇艺%' then round(ck_aqy,2)\n" +
            "when ad_source like '%VIVO%' then round(ck_vivo,2)\n" +
            "when ad_source like '%快手%' then round(ck_kuaishou,2)\n" +
            "when ad_source like '%穿山甲%' then round(ck_chuanshanjia,2)\n" +
            "when ad_source like '%百度%' then round(ck_baidu,2)\n" +
            "when ad_source like '%华为%' then round(ck_huawei,2)\n" +
            "when ad_source like '%广点通%' then round(ck_guangdiantong,2)\n" +
            "when ad_source like '%OPPO%' then round(ck_oppo,2)\n" +
            "when ad_source like '%阿里%' then round(ck_ali,2)\n" +
            "else 0 end as third_income,\n" +
            "round(income-third_income,2) as gap\n" +
            "from\n" +
            "(\n" +
            "select #{logday} as logday2 ,ad_source,sum(income) as income from dwd.ad_upload\n" +
            "where logday = #{logday}\n" +
            "group by ad_source\n" +
            ") as a1\n" +
            "left join (\n" +
            "select #{logday} as logday2\n" +
            ",sum(platform_income) as ck_income\n" +
            ",sum(platform_chuanshanjia_income) as ck_chuanshanjia\n" +
            ",sum(platform_guangdiantong_income) as ck_guangdiantong\n" +
            ",sum(platform_kuaishou_income) as ck_kuaishou\n" +
            ",sum(platform_baidu_income) as ck_baidu\n" +
            ",sum(platform_aqy_income) as ck_aqy\n" +
            ",sum(platform_ali_income) as ck_ali\n" +
            " ,sum(platform_vivo_income) as ck_vivo\n" +
            ",sum(platform_huawei_income) as ck_huawei\n" +
            ",sum(platform_oppo_income) as ck_oppo\n" +
            "from old_mysql_ads.daily_result\n" +
            "where logday = #{logday}\n" +
            "group by logday\n" +
            ") as b1 on a1.logday2 = b1.logday2\n" +
            "where ad_source not like '%SIGMOB%';\n")
    List<IncomeDetailBean> getDailyResultDetail(@Param("logday")String logday);

    @Select({"select a.os as os,\n" +
            "       a.product as product,\n" +
            "       b.product_name as product_name,\n" +
            "       b.product_group as product_group,\n" +
            "       c.cost_yes as cost_yes,\n" +
            "       round(sum(if(a.sendTime >= (toUInt64(now()) * 1000 - 70 * 60 * 1000) and\n" +
            "                    a.sendTime < (toUInt64(now()) * 1000 - 10 * 60 * 1000),\n" +
            "                    toFloat32OrZero(a.extend1) / 1000, 0)) /\n" +
            "             sum(if(a.sendTime >= (toUInt64(now()) * 1000 - 70 * 60 * 1000) and\n" +
            "                    a.sendTime < (toUInt64(now()) * 1000 - 10 * 60 * 1000), 1, 0)) * 1000,\n" +
            "             2)                          as ecpm_one_h,\n" +
            "       round(sum(if(a.sendTime >= (toUInt64(now()) * 1000 - 10 * 60 * 1000),\n" +
            "                    toFloat32OrZero(a.extend1) / 1000, 0)) /\n" +
            "             sum(if(a.sendTime >= (toUInt64(now()) * 1000 - 10 * 60 * 1000), 1, 0)) * 1000,\n" +
            "             2)                          as ecpm_ten_min,\n" +
            "       (ecpm_ten_min-ecpm_one_h) * 100 / ecpm_one_h as rate\n" +
            "from ods.event_exposure_dist a\n" +
            "         global\n" +
            "         inner join\n" +
            "     dwd.product_map_dist b\n" +
            "     on a.product = b.product\n" +
            "         and b.product_group = '项目五组'\n" +
            "         global\n" +
            "         inner join\n" +
            "     (select product_name, product_group, os, round(sum(cost),2) as cost_yes\n" +
            "      from dwd.product_hour_cost\n" +
            "      where logday = yesterday()\n" +
            "        and product_group = '项目五组'\n" +
            "        and hour = 23\n" +
            "      group by product_name, product_group, os) c\n" +
            "     on b.product_name = c.product_name\n" +
            "         and a.os = c.os\n" +
            "         global\n" +
            "         inner join\n" +
            "     (select product_name, product_group, os, sum(cost) as cost_today\n" +
            "      from dwd.product_hour_cost_hour\n" +
            "      where logday = today()\n" +
            "        and product_group = '项目五组'\n" +
            "        and hour = toHour(now()) - 2\n" +
            "      group by product_name, product_group, os\n" +
            "      having cost_today > 1000) d\n" +
            "     on b.product_name = d.product_name\n" +
            "         and a.os = d.os\n" +
            "where a.logday = today()\n" +
            "  and a.hour >= toHour(now()) - 2\n" +
            "  and a.event = 'AdData'\n" +
            "  and a.ad_action = 'exposure'\n" +
            "  and a.os in ('android', 'ios')\n" +
            "  and a.ad_type global in (select toString(ad_type)\n" +
            "                           from dwd.ad_type_basic_dist\n" +
            "                           where source_name = '穿山甲')\n" +
            "  and a.sendTime >= (toUInt64(now()) * 1000 - 70 * 60 * 1000)\n" +
            "  and toFloat32OrZero(a.extend1) < 100000\n" +
            "  and toFloat32OrZero(a.extend1) > 0\n" +
            "group by a.os,\n" +
            "         a.product,\n" +
            "         b.product_name,\n" +
            "         b.product_group,\n" +
            "         c.cost_yes\n" +
            "having rate >= 30;"})
    List<OsProductEcpmCostBean> queryRealtimeOsProductEcpmCost();
}
