package com.shinet.core.alert.clickhouse.entity;

import lombok.Data;

/**
 * <AUTHOR>
 * @since 2025/04/29
 */
@Data
public class TodayOldAuIncomeBean {

    private String logday;
    private Integer hour;
    private String os;
    private Integer oldAuToday;
    private Double oldAuIncomeToday;
    private Double oldAuIncomeTodayHour;
    private Integer oldAuYes;
    private Double oldAuIncomeYes;
    private Double oldAuIncomeYesHour;
    private Double oldAuRateYes;
    private Double oldAuIncomeRateYes;
    private Double oldAuIncomeRateYesHour;

    public String getCeilP(Double p){
        return Math.ceil(p) +"%";
    }

    public String getFloorP(Double p) {
        return Math.round(Math.floor(p)) + "%";
    }


}
