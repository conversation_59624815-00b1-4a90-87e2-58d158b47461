package com.shinet.core.alert.config;

import com.aliyun.oss.OSS;
import com.aliyun.oss.model.GetObjectRequest;
import com.aliyun.oss.model.PutObjectRequest;
import com.aliyun.oss.model.PutObjectResult;
import com.google.common.cache.CacheBuilder;
import com.google.common.cache.CacheLoader;
import com.google.common.cache.LoadingCache;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.io.File;
import java.io.InputStream;
import java.util.HashMap;
import java.util.concurrent.TimeUnit;
@Service
@Slf4j
public class OssService {
    @Value("coohua-video-manager")
    private String videoBucket;

    @Resource(name="videoManagerOss")
    private OSS videoOss;

    /**
     * 公网域名 暂时下掉
     */
    public static  final String OSS_VIDEO_URL ="https://video-manager.shinet.cn";

    /**
     * 视频管理上传文件
     * @param objectName  表示上传文件到OSS时需要指定包含文件后缀在内的完整路径，例如abc/efg/123.jpg
     * @param file
     */
    public PutObjectResult uploadFileForVideoManager(String objectName, InputStream file) {
        PutObjectRequest putObjectRequest = new PutObjectRequest(videoBucket, objectName, file);
        HashMap<String, String> map = new HashMap<>();
        map.put("Content-Disposition", "attachment;");
        putObjectRequest.setHeaders(map);
        PutObjectResult putObjectResult = videoOss.putObject(putObjectRequest);
        return putObjectResult;
    }


}
