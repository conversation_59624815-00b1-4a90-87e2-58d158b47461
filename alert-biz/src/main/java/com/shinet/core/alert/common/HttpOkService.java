package com.shinet.core.alert.common;

import lombok.extern.slf4j.Slf4j;
import okhttp3.OkHttpClient;
import okhttp3.Request;
import okhttp3.Response;

import java.io.IOException;

@Slf4j
public class HttpOkService {
    private static OkHttpClient client = new OkHttpClient.Builder().build();

    public static String reqGet(String reqUrl){
        Request request = new Request.Builder().url(reqUrl).get().build();
        try {
            Response response = client.newCall(request).execute();
            return response.body().string();
        } catch (IOException e) {
            log.error("",e);
        }
        return null;
    }
}
