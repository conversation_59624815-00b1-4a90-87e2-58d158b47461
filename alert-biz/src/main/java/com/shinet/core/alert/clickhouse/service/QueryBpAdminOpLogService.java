package com.shinet.core.alert.clickhouse.service;

import com.shinet.core.alert.bpap.mapper.BpAdminMapper;
import com.shinet.core.alert.util.DateUtils;
import com.xxl.job.core.log.XxlJobLogger;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;

/**
 * <AUTHOR>
 * @since 2022/8/31
 */
@Slf4j
@Service
public class QueryBpAdminOpLogService {

    @Resource
    private BpAdminMapper bpAdminMapper;

    // 产品是否有广告配置变更
    public Boolean updateAdConfig(Integer appId){
        Date now = new Date();
//        if (now.getHours() >=8 && now.getHours() <=23){
//            return true;
//        }
        Date updateTime = DateUtils.addTime(new Date(),- 1000 * 60 * 60 * 4);
        log.info("QueryTime ==>{}",DateUtils.formatDate(updateTime));
        XxlJobLogger.log("QueryTime ==>{}",DateUtils.formatDate(updateTime));
        Integer count = bpAdminMapper.countAd(appId,updateTime);
        XxlJobLogger.log("Ad UpdateCount ==>{}",count);
        if (count >= 2){
            // Check 是否系统替换
            count = bpAdminMapper.countAdSystem(appId,updateTime);
            if (count > 0){
                return true;
            }
        }
        count = bpAdminMapper.countConfigBase(appId,updateTime);
        XxlJobLogger.log("CONFIG FILTER UpdateCount ==>{}",count);
        if (count > 0){
            return true;
        }
        count = bpAdminMapper.countConfigBaseOrigin(appId,updateTime);
        XxlJobLogger.log("CONFIG UpdateCount ==>{}",count);
        return count > 0;
    }
}
