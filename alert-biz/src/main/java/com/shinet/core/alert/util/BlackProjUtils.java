package com.shinet.core.alert.util;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import java.util.HashSet;
import java.util.Set;

@Slf4j
public class BlackProjUtils {
    private static Set<String> blackProjectNames = new HashSet<>();

    private static Set<String> v2MoveProjectNames = new HashSet<>();
    static {
        blackProjectNames.add("shua");
        blackProjectNames.add("bubuduo");
        blackProjectNames.add("job");
        blackProjectNames.add("g8");
        blackProjectNames.add("g7");
        blackProjectNames.add("web");
        blackProjectNames.add("bp-data-retrieve");
        blackProjectNames.add("ad-common-impl");
        blackProjectNames.add("ab-test-dispatcher");
        blackProjectNames.add("ad-filter-impl");

        blackProjectNames.add("bp-ap-admin");
        blackProjectNames.add("dsp");
        blackProjectNames.add("bp-ap-business-modules");
        blackProjectNames.add("core-exposure");
        blackProjectNames.add("data-exposure");
        blackProjectNames.add("core-img-service");
        blackProjectNames.add("bp-ap-business-gateway");
        blackProjectNames.add("ks-api");
        blackProjectNames.add("opt-timer");
        blackProjectNames.add("core-gpt");
        blackProjectNames.add("rta-clickhouse");
        blackProjectNames.add("g9");
        blackProjectNames.add("deflogs");
        blackProjectNames.add("g10");
        blackProjectNames.add("core-rta-tx");
        blackProjectNames.add("core-rta-byte");
    }

    public static boolean isV2InMove(String prjName){
        for(String bpj:v2MoveProjectNames){
            if(StringUtils.isNotBlank(prjName) && (prjName.contains(bpj) || prjName.equalsIgnoreCase(bpj))){
                return true;
            }
        }
        return false;
    }
    public static boolean isInBlack(String prjName){
        if(StringUtils.isBlank(prjName)){
            log.warn("dfs" + prjName);
            return true;
        }
        for(String bpj:blackProjectNames){
            if(StringUtils.isNotBlank(prjName) && (prjName.contains(bpj) || prjName.equalsIgnoreCase(bpj))){
                return true;
            }
        }
        return false;
    }
}
