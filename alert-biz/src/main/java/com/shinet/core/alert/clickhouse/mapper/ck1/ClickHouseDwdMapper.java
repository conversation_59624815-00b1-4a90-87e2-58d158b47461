package com.shinet.core.alert.clickhouse.mapper.ck1;

import com.shinet.core.alert.adb.entity.DailyResultPlatformIncomeCheck;
import com.shinet.core.alert.adb.entity.ProductDau;
import com.shinet.core.alert.clickhouse.entity.*;
import com.shinet.core.alert.dsp.entity.*;
import org.apache.ibatis.annotations.Insert;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;


public interface ClickHouseDwdMapper {

    /**
     * description: 以下为商户余额报警
     * date: 2021/11/5 14:36
     */
    @Select({"select logday, merchant_name, merchant_id, withdraw_limit, withdraw_amount," +
            " round(withdraw_amount / withdraw_limit,3) as per" +
            "  from dwd.withdraw_merchant" +
            "  where logday = #{logday}" +
            "  and hour in (" +
            "    select max(hour)" +
            "    from dwd.withdraw_merchant" +
            "    where logday = #{logday}" +
            "            );"})
    List<WithdrawMerchant> queryWithdrawMerchantPerList(@Param("logday")String logday);

    @Select({"select logday,merchant_name,merchant_id,per_type,number from " +
            " dwd.withdraw_merchant_alarm where logday = #{logday}"})
    List<WithdrawMerchantAlarm> queryWithdrawMerchantAlarmList(@Param("logday")String logday);

    @Insert({"<script>",
            "insert into dwd.withdraw_merchant_alarm (logday, merchant_name, merchant_id, per_type, number",
            ") values ",
            "<foreach collection = 'list' item = 'item' separator = ','>",
            "(#{item.logday}, #{item.merchantName}, #{item.merchantId}, #{item.perType}, #{item.number})",
            "</foreach>",
            "</script>"})
    void saveWithdrawMerchantAlarm(List<WithdrawMerchantAlarm> alarms);



    @Select({"select product,  " +
            "       product_name,  " +
            "       os,  " +
            "       channel,  " +
            "       sum(tf_hnu)                      as ocpc_count,  " +
            "       sum(hnu)                         as all_count,  " +
            "       round(ocpc_count / all_count, 4) as ocpc_rate,  " +
            "       round(1 - ocpc_rate, 4)          as no_ocpc_rate  " +
            "from old_mysql_ads.realtime_nu_channel_tf  " +
            "where logday = today()  " +
            "  and (channel like 'csj%'  " +
            "    or channel like 'ttzz%'  " +
            "    or channel like 'ks%'  " +
            "    or channel like 'KS%'  " +
            "    or channel like 'tx%'  " +
            "    or channel like 'gdt%')  " +
            "group by product, product_name, os, channel  " +
            "having all_count > #{deviceCount}" +
            "   and ocpc_rate < #{ocpcRate} ;"})
    List<NuDeviceChannelOcpcRate> queryNuDeviceChannelOcpcRateList(@Param("deviceCount") Integer deviceCount,@Param("ocpcRate") Double ocpcRate);


    @Select({"select product, " +
            "       product_name, " +
            "       hour, " +
            "       os, " +
            "       channel, " +
            "       sum(tf_hnu)                      as ocpc_count, " +
            "       sum(hnu)                         as all_count, " +
            "       round(ocpc_count / all_count, 4) as ocpc_rate, " +
            "       round(1 - ocpc_rate, 4)          as no_ocpc_rate " +
            "from old_mysql_ads.realtime_nu_channel_tf " +
            "where logday = today() " +
            "  and hour = toHour(now())-2 " +
            "  and (channel like 'csj%' " +
            "    or channel like 'ttzz%' " +
            "    or channel like 'ks%' " +
            "    or channel like 'KS%' " +
            "    or channel like 'tx%' " +
            "    or channel like 'gdt%') " +
            "group by product, product_name,hour,os, channel " +
            "having all_count > #{deviceCount} " +
            "   and ocpc_rate < #{ocpcRate} ; "})
    List<NuDeviceChannelOcpcRate> queryNuDeviceChannelOcpcHourRateList(@Param("deviceCount") Integer deviceCount,@Param("ocpcRate") Double ocpcRate);


    @Select({
            "select r1.product as product,r3.product_name as product_name, r1.os as os, dau, withdraw " +
                    " from ( " +
                    "         select product, os, uniqExact(device_id) as dau " +
                    "         from ods.event_exposure_dist " +
                    "         where logday >= yesterday() " +
                    "           and time > addHours(now(), - 6) " +
                    "           and product != '' " +
                    "         group by product, os " +
                    "         having dau <= 3000 " +
                    "         ) r1 " +
                    "         left join ( " +
                    "    select product, os, sum(amount / 100) as withdraw " +
                    "    from dwd.withdraw_dist " +
                    "    where create_time / 1000 > addHours(now(), - 6) " +
                    "      and logday >= yesterday() " +
                    "      and status = 5 " +
                    "    group by product, os " +
                    "    ) r2 on r1.product = r2.product and r1.os = r2.os " +
                    "    left join dwd.product_map_dist r3 on r1.product = r3.product" +
                    " where withdraw > 3000;"
    })
    List<WithdrawAlertBean> queryWithdrawDauLess();

    @Select({
            "select r1.product as product,r4.product_name as  product_name,r1.os as os, dau, withdraw,income, " +
                    "  toDecimal32(if(isNaN(withdraw / income) = 1 or isInfinite(withdraw / income) = 1,0,withdraw / income) * 100, 2) as withDrawRate  " +
                    " from (  " +
                    "         select product, os, uniqExact(device_id) as dau  " +
                    "         from ods.event_exposure_dist  " +
                    "         where logday >= yesterday()  " +
                    "           and time > addHours(now(), - 6)  " +
                    "           and product != ''  " +
                    "         group by product, os  " +
                    "         having dau > 3000  " +
                    "         ) r1  " +
                    "         left join (  " +
                    "    select product, os, sum(amount / 100) as withdraw  " +
                    "    from dwd.withdraw_dist  " +
                    "    where create_time / 1000 > addHours(now(), - 6)  " +
                    "      and logday >= yesterday()  " +
                    "      and status = 5  " +
                    "    group by product, os  " +
                    "    ) r2 on r1.product = r2.product and r1.os = r2.os  " +
                    "         left join (  " +
                    "    select t2.product, t1.os, t1.income  " +
                    "    from (  " +
                    "             select app_id, os, sum(toFloat64(price) / 1000) as income  " +
                    "             from ods.ad_point_dist  " +
                    "             where call_time > addHours(now(), - 6)  " +
                    "               and logday >= yesterday()  " +
                    "               and uri = '/ap-data-consumer/upload'  " +
                    "             group by app_id, os  " +
                    "             ) t1  " +
                    "             left join dwd.app_mapping_dist t2 on toInt64(t1.app_id) = t2.id  " +
                    "    ) r3 on r1.product = r3.product and r1.os = r3.os  " +
                    "    left join dwd.product_map_dist r4 on r1.product = r4.product" +
                    " where ( product not in ('yyacc','zzkp','wdjy2','zzly') and withDrawRate >= #{rate} and income > 0 and withdraw > 0) " +
                    " or (product  in ('yyacc','zzkp','wdjy2','zzly')and withDrawRate >= toDecimal32(100,2) and income > 0 and withdraw > 0)" +
                    ";"})
    List<WithdrawAlertBean> queryWithdrawDauMore(@Param("rate") Integer rate);


    @Select({"select t1.product as product,r3.product_name as product_name,t1.os as os,t1.income as incomeT,t2.income as incomeY, " +
            "       t1.ecpmx as dt1,t2.ecpmx as dt2, " +
            " (t1.ecpmx-t2.ecpmx) / t2.ecpmx *100 as subRate, " +
            " t1.dau as dau " +
            " from ( " +
            "         select product, os, sum(ix) income, sum(dt) as dau, sum(vx) as pv, income / pv * 1000 as ecpmx " +
            "         from ( " +
            "               select logday, " +
            "                      product, " +
            "                      channel, " +
            "                      os, " +
            "                      sum(income) as ix, " +
            "                      sum(pv)     as vx, " +
            "                      max(dau)    as dt " +
            "               from old_mysql_ads.ad_arpu_real_sum_hour " +
            "               where logday = today() " +
            "                 and hour = toHour(now()) - 1 " +
            "               group by logday, product, channel, os " +
            "                  ) " +
            "         group by product, os " +
            "         having (os ='android' and dau > 3000) or (os='ios' and dau > 500) " +
            "     ) t1 left join (select product, os, sum(ix) income, sum(dt) as dau, sum(vx) as pv, income / pv * 1000 as ecpmx " +
            "         from ( " +
            "               select logday, " +
            "                      product, " +
            "                      channel, " +
            "                      os, " +
            "                      sum(income) as ix, " +
            "                      sum(pv)     as vx, " +
            "                      max(dau)    as dt " +
            "               from old_mysql_ads.ad_arpu_real_sum_hour " +
            "               where logday = yesterday() " +
            "                 and hour = toHour(now()) - 1 " +
            "               group by logday, product, channel, os " +
            "                  ) " +
            "         group by product, os " +
            "         ) t2 on t1.product = t2.product and t1.os =t2.os " +
            "    left join dwd.product_map_dist r3 on t1.product = r3.product " +
            " where subRate < -30 or subRate > 30;"})
    List<ArpuCheckBean> queryEcpm();

    @Select(""
            + "                      select       source   as ad_source      , t1.income                              as incomeT         , t2.income                              as incomeY         , t1.ecpmx                               as dt1         , t2.ecpmx                               as dt2         , (t1.ecpmx - t2.ecpmx) / t2.ecpmx * 100 as subRate         , t1.dau                                 as dau      from (           select                source                  , sum(ix)     income                  , sum(dt)  as dau                  , sum(vx)  as pv                  , income / pv * 1000 as ecpmx                 from (                    select logday                         ,case                                        when ad_type_name like '%头条%' then '穿山甲'                                        when ad_type_name like '%穿山甲%' then '穿山甲'                                        when ad_type_name like '%百度%' then '百度'                                        when ad_type_name like '%快手%' then '快手'                                        when ad_type_name like '%广点通%' then '广点通'                                        when ad_type_name like '%OPPO%' then 'OPPO'                                        when ad_type_name like '%VIVO%' then 'VIVO'                                        else '其他' end as source                           , product                           , os                           , sum(income) as ix                           , sum(pv)     as vx                           , max(dau)    as dt                        from (                             select t2.logday        as logday,                                      t2.product       as product,                                      t3.product_name  as product_name,                                      t3.product_group as product_group,                                      t2.os            as os,                                      t2.hour          as hour,                                      t4.type_name     as type_name,                                      t1.ad_type       as ad_type,                                      t1.ad_type_name  as ad_type_name,                                      t1.pos_name      as pos_name,                                      t1.pv1           as pv,                                      t1.income        as income,                                      t2.key_value     as dau                                 from (                                      select logday,                                               toString(toHour(now()) - 1) as chour,                                               product,                                               product_group,                                               product_name,                                               os,                                               max(ad_type_base)           as ad_type_base,                                               ad_type,                                               ad_type_name,                                               max(pos_name)               as pos_name,                                               sum(pv)                     as pv1,                                               sum(income)                 as income                                          from dwd.ad_arpu_dist                                        where logday = today()                                          and hour = toHour(now()) - 1                                          and is_real = 0                                        group by logday, product, product_group, product_name, os, ad_type, ad_type_name) t1                                        right join                                    (                                      select logday, product, os, hour, sum(key_value) as key_value                                        from old_mysql_ads.realtime_au_nu_channel                                        where logday = today()                                          and key_name = 'au'                                          and hour = toHour(now()) - 1                                        group by logday, product, os, hour                                      ) t2                                  on t1.product = t2.product and toInt32(t1.chour) = t2.hour                                      and t1.os = t2.os                                        left join dwd.product_map_dist t3 on t2.product = t3.product                                        left join dwd.ad_type_basic_dist t4 on t1.ad_type = toString(t4.ad_type)                               order by hour                             )                        where logday = today()                        and hour = toHour(now()) - 1                      group by logday, product, os,source                    )             group by source           ) t1             left join (        select           source             , sum(ix)     income             , sum(dt)  as dau             , sum(vx)  as pv             , income / pv * 1000 as ecpmx          from (               select logday                   ,case                                        when ad_type_name like '%头条%' then '穿山甲'                                        when ad_type_name like '%穿山甲%' then '穿山甲'                                        when ad_type_name like '%百度%' then '百度'                                        when ad_type_name like '%快手%' then '快手'                                        when ad_type_name like '%广点通%' then '广点通'                                        when ad_type_name like '%OPPO%' then 'OPPO'                                        when ad_type_name like '%VIVO%' then 'VIVO'                                        else '其他' end as source                      , product                      , os                      , sum(income) as ix                      , sum(pv)     as vx                      , max(dau)    as dt                   from (                        select t2.logday        as logday,                                 t2.product       as product,                                 t3.product_name  as product_name,                                 t3.product_group as product_group,                                 t2.os            as os,                                 t2.hour          as hour,                                 t4.type_name     as type_name,                                 t1.ad_type       as ad_type,                                 t1.ad_type_name  as ad_type_name,                                 t1.pos_name      as pos_name,                                 t1.pv1           as pv,                                 t1.income        as income,                                 t2.key_value     as dau                            from (                                 select logday,                                          toString(toHour(now()) - 1) as chour,                                          product,                                          product_group,                                          product_name,                                          os,                                          max(ad_type_base)           as ad_type_base,                                          ad_type,                                          ad_type_name,                                          max(pos_name)               as pos_name,                                          sum(pv)                     as pv1,                                          sum(income)                 as income                                     from dwd.ad_arpu_dist                                   where logday = yesterday()                                     and hour = toHour(now()) - 1                                     and is_real = 1                                   group by logday, product, product_group, product_name, os, ad_type, ad_type_name) t1                                   right join                               (select logday, product, os, hour, sum(key_value) as key_value                                from old_mysql_ads.realtime_au_nu_channel                                where logday = yesterday()                                  and key_name = 'au'                                  and hour = toHour(now()) - 1                                group by logday, product, os, hour                                 ) t2                             on t1.product = t2.product and toInt32(t1.chour) = t2.hour                                 and t1.os = t2.os                                   left join dwd.product_map_dist t3 on t2.product = t3.product                                   left join dwd.ad_type_basic_dist t4 on t1.ad_type = toString(t4.ad_type)                          order by hour                        )                   where logday = yesterday()                   and hour = toHour(now()) - 1                 group by logday, product, os,source               )        group by source      ) t2                     on t1.source = t2.source      where ((subRate < -30 or subRate > 30))    and dt2 > 0  ;                                                                                                                           "
    )
    List<ArpuCheckBean> queryAdSourceEcpm();

    @Select({ ""
            + "                     select t1.product                                                              as product         , r3.product_name                                                         as product_name         , t1.os                                                                   as os         , t1.income                                                               as incomeT         , t2.income                                                               as incomeY         , t1.ecpmx                                                                as dt1         , t2.ecpmx                                                                as dt2         , (t1.ecpmx - t2.ecpmx) / t2.ecpmx * 100                                  as subRate         , t1.dau                                                                  as dau      from (             select product                  , os                  , sum(ix)     income                  , sum(dt)  as dau                  , sum(vx)  as pv                  , pv / dau as ecpmx               from (                      select logday                           , product                           , os                           , sum(income) as        ix                           , sum(pv)     as        vx                           , max(dau)    as        dt                        from (                               select t2.logday           as logday,                                      t2.product          as product,                                      t3.product_name     as product_name,                                      t3.product_group    as product_group,                                      t2.os               as os,                                      t2.hour             as hour,                                      t4.type_name        as type_name,                                      t1.ad_type          as ad_type,                                      t1.ad_type_name     as ad_type_name,                                      t1.pos_name         as pos_name,                                      t1.pv1              as pv,                                      t1.income           as income,                                      t2.key_value        as dau                                   from (                                        select logday,                                               toString(toHour(now()) - 1)                 as chour,                                               product,                                               product_group,                                               product_name,                                               os,                                               max(ad_type_base)                           as ad_type_base,                                               ad_type,                                               ad_type_name,                                               max(pos_name)                               as pos_name,                                               sum(pv)                                     as pv1,                                               sum(income)                                 as income                                            from dwd.ad_arpu_dist                                        where logday = today()                                          and hour = toHour(now()) - 1                                          and is_real = 0                                        group by logday, product, product_group, product_name, os, ad_type, ad_type_name) t1                                        right join                                    (                                        select logday, product, os, hour, sum(key_value) as key_value                                        from old_mysql_ads.realtime_au_nu_channel                                        where logday = today()                                          and key_name = 'au'                                          and hour = toHour(now()) - 1                                        group by logday, product, os, hour                                        ) t2                                    on t1.product = t2.product and toInt32(t1.chour) = t2.hour                                        and t1.os = t2.os                                        left join dwd.product_map_dist t3 on t2.product = t3.product                                        left join dwd.ad_type_basic_dist t4 on t1.ad_type = toString(t4.ad_type)                               order by hour                               ) where logday = today()                        and hour = toHour(now()) - 1                      group by logday, product, os                      )             group by product, os             having (os = 'android' and dau > 3000)                 or (os = 'ios' and dau > 500)             ) t1             left join (        select product             , os             , sum(ix)     income             , sum(dt)  as dau             , sum(vx)  as pv             , pv / dau as ecpmx              from (                 select logday                      , product                      , os                      , sum(income) as        ix                      , sum(pv)     as        vx                      , max(dau)    as        dt                     from (                          select t2.logday        as logday,                                 t2.product       as product,                                 t3.product_name  as product_name,                                 t3.product_group as product_group,                                 t2.os            as os,                                 t2.hour          as hour,                                 t4.type_name     as type_name,                                 t1.ad_type       as ad_type,                                 t1.ad_type_name  as ad_type_name,                                 t1.pos_name      as pos_name,                                 t1.pv1           as pv,                                 t1.income        as income,                                 t2.key_value     as dau                            from (                                   select logday,                                          toString(toHour(now()) - 1)                 as chour,                                          product,                                          product_group,                                          product_name,                                          os,                                          max(ad_type_base)                           as ad_type_base,                                          ad_type,                                          ad_type_name,                                          max(pos_name)                               as pos_name,                                          sum(pv)                                     as pv1,                                          sum(income)                                 as income                                           from dwd.ad_arpu_dist                                   where logday = yesterday()                                     and hour = toHour(now()) - 1                                     and is_real = 0                                   group by logday, product, product_group, product_name, os, ad_type, ad_type_name) t1                                   right join                               (select logday, product, os, hour, sum(key_value) as key_value                                from old_mysql_ads.realtime_au_nu_channel                                where logday = yesterday()                                  and key_name = 'au'                                  and hour = toHour(now()) - 1                                group by logday, product, os, hour                                   ) t2                               on t1.product = t2.product and toInt32(t1.chour) = t2.hour                                   and t1.os = t2.os                                   left join dwd.product_map_dist t3 on t2.product = t3.product                                   left join dwd.ad_type_basic_dist t4 on t1.ad_type = toString(t4.ad_type)                          order by hour                          )                     where logday = yesterday()                   and hour = toHour(now()) - 1                 group by logday, product, os                 )        group by product, os        ) t2                       on t1.product = t2.product and t1.os = t2.os             left join dwd.product_map_dist r3 on t1.product = r3.product                                                                                                                          "
//            用于单元测试
//            + "                     limit 1                                                                                                                            "
            + "                     where ((subRate < -30 or subRate > 30) )  and dt2 > 0                                                                                                                             "
    })
    List<ArpuCheckBean> queryPv();

    @Select({ ""
            + "           select        t1.income                                                               as incomeT       , t2.income                                                               as incomeY       , t1.ecpmx                                                                as dt1       , t2.ecpmx                                                                as dt2       , (t1.ecpmx - t2.ecpmx) / t2.ecpmx * 100                                  as subRate       , t1.dau                                                                  as dau       , ifNotFinite((t1.toutiao_pv - t2.toutiao_pv) / t2.toutiao_pv,0) * 100                   as toutiaosubRate       , ifNotFinite((t1.kuaishou_pv - t2.kuaishou_pv) / t2.kuaishou_pv ,0)* 100                as kuaishousubRate       , ifNotFinite((t1.guangdiantong_pv - t2.guangdiantong_pv) / t2.guangdiantong_pv,0) * 100 as guangdiantongsubRate       , ifNotFinite((t1.baidu_pv - t2.baidu_pv) / t2.baidu_pv,0) * 100                         as baidusubRate       , ifNotFinite((t1.ali_pv - t2.ali_pv) / t2.ali_pv,0) * 100                               as alisubRate       , ifNotFinite((t1.aqy_pv - t2.aqy_pv) / t2.aqy_pv,0) * 100                               as aqysubRate       , ifNotFinite((t1.sigmob_pv - t2.sigmob_pv) / t2.sigmob_pv ,0)* 100                      as sigmobsubRate       , ifNotFinite((t1.vivo_pv - t2.vivo_pv) / t2.vivo_pv ,0)* 100                            as vivosubRate       , ifNotFinite((t1.oppo_pv - t2.oppo_pv) / t2.oppo_pv ,0)* 100                            as opposubRate       , ifNotFinite((t1.unknown_pv - t2.unknown_pv) / t2.unknown_pv ,0)* 100                   as unknownsubRate       , ifNotFinite((t1.qita_pv - t2.qita_pv) / t2.qita_pv,0) * 100                            as qitasubRate  from (           select 1 as key               ,sum(ix)     income                , sum(dt)  as dau                , sum(vx)  as pv                , pv / dau as ecpmx                , sum(toutiao_pv)  as toutiao_pv                , sum(kuaishou_pv)  as kuaishou_pv                , sum(guangdiantong_pv)  as guangdiantong_pv                , sum(baidu_pv)  as baidu_pv                , sum(ali_pv)  as ali_pv                , sum(aqy_pv)  as aqy_pv                , sum(sigmob_pv)  as sigmob_pv                , sum(vivo_pv)  as vivo_pv                , sum(oppo_pv)  as oppo_pv                , sum(unknown_pv)  as unknown_pv                , sum(qita_pv)  as qita_pv           from (                    select                          sum(income) as        ix                         , sum(pv)     as        vx                         , max(dau)    as        dt                         , sum(toutiao_pv)       toutiao_pv                         , sum(kuaishou_pv)      kuaishou_pv                         , sum(guangdiantong_pv) guangdiantong_pv                         , sum(baidu_pv)         baidu_pv                         , sum(ali_pv)           ali_pv                         , sum(aqy_pv)           aqy_pv                         , sum(sigmob_pv)        sigmob_pv                         , sum(vivo_pv)          vivo_pv                         , sum(oppo_pv)          oppo_pv                         , sum(unknown_pv)       unknown_pv                         , sum(qita_pv)          qita_pv                    from (                             select t2.logday           as logday,                                    t2.product          as product,                                    t3.product_name     as product_name,                                    t3.product_group    as product_group,                                    t2.os               as os,                                    t2.hour             as hour,                                    t4.type_name        as type_name,                                    t1.ad_type          as ad_type,                                    t1.ad_type_name     as ad_type_name,                                    t1.pos_name         as pos_name,                                    t1.pv1              as pv,                                    t1.income           as income,                                    t2.key_value        as dau                                     ,                                    t1.toutiao_pv       as toutiao_pv                                     ,                                    t1.kuaishou_pv      as kuaishou_pv                                     ,                                    t1.guangdiantong_pv as guangdiantong_pv                                     ,                                    t1.baidu_pv         as baidu_pv                                     ,                                    t1.ali_pv           as ali_pv                                     ,                                    t1.aqy_pv           as aqy_pv,                                    t1.sigmob_pv        as sigmob_pv                                     ,                                    t1.vivo_pv          as vivo_pv                                     ,                                    t1.oppo_pv          as oppo_pv                                     ,                                    t1.unknown_pv       as unknown_pv                                     ,                                    t1.qita_pv          as qita_pv                             from (                                      select logday,                                             toString(toHour(now()) - 1)                 as chour,                                             product,                                             product_group,                                             product_name,                                             os,                                             max(ad_type_base)                           as ad_type_base,                                             ad_type,                                             ad_type_name,                                             max(pos_name)                               as pos_name,                                             sum(pv)                                     as pv1,                                             sum(income)                                 as income,                                             sum(if(ad_type_name like '头条%' or ad_type_name like '穿山甲%' or                                                    ad_type_name like '抖音%', pv, 0))   as toutiao_pv,                                             sum(if(ad_type_name like '快手%', pv, 0))   as kuaishou_pv,                                             sum(if(ad_type_name like '广点通%', pv, 0)) as guangdiantong_pv,                                             sum(if(ad_type_name like '百度%', pv, 0))   as baidu_pv,                                             sum(if(ad_type_name like '阿里%', pv, 0))   as ali_pv,                                             sum(if(ad_type_name like '爱奇艺%', pv, 0)) as aqy_pv,                                             sum(if(ad_type_name like 'SIGMOB%', pv, 0)) as sigmob_pv,                                             sum(if(ad_type_name like 'vivo%', pv, 0))   as vivo_pv,                                             sum(if(ad_type_name like 'oppo%', pv, 0))   as oppo_pv,                                             sum(if(ad_type_name = '未知', pv, 0))       as unknown_pv                                              ,                                             sum(if(ad_type_name not like '头条%' and ad_type_name not like '穿山甲%' and                                                    ad_type_name not like '抖音%'                                                        and ad_type_name not like '快手%' and                                                    ad_type_name not like '百度%' and                                                    ad_type_name not like '广点通%'                                                        and ad_type_name not like '阿里%' and                                                    ad_type_name not like 'oppo%'                                                        and ad_type_name not like '爱奇艺%' and                                                    ad_type_name not like 'SIGMOB%' and ad_type_name not like 'vivo%', pv,                                                    0))                                  as qita_pv                                      from dwd.ad_arpu_dist                                      where logday = today()                                        and hour = toHour(now()) - 1                                        and is_real = 0                                      group by logday, product, product_group, product_name, os, ad_type, ad_type_name) t1                                      right join                                  (                                      select logday, product, os, hour, sum(key_value) as key_value                                      from old_mysql_ads.realtime_au_nu_channel                                      where logday = today()                                        and key_name = 'au'                                        and hour = toHour(now()) - 1                                      group by logday, product, os, hour                                      ) t2                                  on t1.product = t2.product and toInt32(t1.chour) = t2.hour                                      and t1.os = t2.os                                      left join dwd.product_map_dist t3 on t2.product = t3.product                                      left join dwd.ad_type_basic_dist t4 on t1.ad_type = toString(t4.ad_type)                             order by hour                             )                      where logday = today()                      and hour = toHour(now()) - 1                    group by logday, product, os                    )           ) t1           left join (      select            1 as key           ,sum(ix)     income           , sum(dt)  as dau           , sum(vx)  as pv           , pv / dau as ecpmx           , sum(toutiao_pv)  as toutiao_pv                , sum(kuaishou_pv)  as kuaishou_pv                , sum(guangdiantong_pv)  as guangdiantong_pv                , sum(baidu_pv)  as baidu_pv                , sum(ali_pv)  as ali_pv                , sum(aqy_pv)  as aqy_pv                , sum(sigmob_pv)  as sigmob_pv                , sum(vivo_pv)  as vivo_pv                , sum(oppo_pv)  as oppo_pv                , sum(unknown_pv)  as unknown_pv                , sum(qita_pv)  as qita_pv        from (               select logday                      , sum(income) as        ix                    , sum(pv)     as        vx                    , max(dau)    as        dt                    , sum(toutiao_pv)       toutiao_pv                    , sum(kuaishou_pv)      kuaishou_pv                    , sum(guangdiantong_pv) guangdiantong_pv                    , sum(baidu_pv)         baidu_pv                    , sum(ali_pv)           ali_pv                    , sum(aqy_pv)           aqy_pv                    , sum(sigmob_pv)        sigmob_pv                    , sum(vivo_pv)          vivo_pv                    , sum(oppo_pv)          oppo_pv                    , sum(unknown_pv)       unknown_pv                    , sum(qita_pv)          qita_pv               from (                        select t2.logday        as logday,                               t2.product       as product,                               t3.product_name  as product_name,                               t3.product_group as product_group,                               t2.os            as os,                               t2.hour          as hour,                               t4.type_name     as type_name,                               t1.ad_type       as ad_type,                               t1.ad_type_name  as ad_type_name,                               t1.pos_name      as pos_name,                               t1.pv1           as pv,                               t1.income        as income,                               t2.key_value     as dau                               ,                                    t1.toutiao_pv       as toutiao_pv                                     ,                                    t1.kuaishou_pv      as kuaishou_pv                                     ,                                    t1.guangdiantong_pv as guangdiantong_pv                                     ,                                    t1.baidu_pv         as baidu_pv                                     ,                                    t1.ali_pv           as ali_pv                                     ,                                    t1.aqy_pv           as aqy_pv,                                    t1.sigmob_pv        as sigmob_pv                                     ,                                    t1.vivo_pv          as vivo_pv                                     ,                                    t1.oppo_pv          as oppo_pv                                     ,                                    t1.unknown_pv       as unknown_pv                                     ,                                    t1.qita_pv          as qita_pv                        from (                                 select logday,                                        toString(toHour(now()) - 1)                 as chour,                                        product,                                        product_group,                                        product_name,                                        os,                                        max(ad_type_base)                           as ad_type_base,                                        ad_type,                                        ad_type_name,                                        max(pos_name)                               as pos_name,                                        sum(pv)                                     as pv1,                                        sum(income)                                 as income,                                        sum(if(ad_type_name like '头条%' or ad_type_name like '穿山甲%' or                                               ad_type_name like '抖音%', pv, 0))   as toutiao_pv,                                        sum(if(ad_type_name like '快手%', pv, 0))   as kuaishou_pv,                                        sum(if(ad_type_name like '广点通%', pv, 0)) as guangdiantong_pv,                                        sum(if(ad_type_name like '百度%', pv, 0))   as baidu_pv,                                        sum(if(ad_type_name like '阿里%', pv, 0))   as ali_pv,                                        sum(if(ad_type_name like '爱奇艺%', pv, 0)) as aqy_pv,                                        sum(if(ad_type_name like 'SIGMOB%', pv, 0)) as sigmob_pv,                                        sum(if(ad_type_name like 'vivo%', pv, 0))   as vivo_pv,                                        sum(if(ad_type_name like 'oppo%', pv, 0))   as oppo_pv,                                        sum(if(ad_type_name = '未知', pv, 0))       as unknown_pv                                         ,                                        sum(if(ad_type_name not like '头条%' and ad_type_name not like '穿山甲%' and                                               ad_type_name not like '抖音%'                                                   and ad_type_name not like '快手%' and ad_type_name not like '百度%' and                                               ad_type_name not like '广点通%'                                                   and ad_type_name not like '阿里%' and ad_type_name not like 'oppo%'                                                   and ad_type_name not like '爱奇艺%' and                                               ad_type_name not like 'SIGMOB%' and ad_type_name not like 'vivo%', pv,                                               0))                                  as qita_pv                                     from dwd.ad_arpu_dist                                 where logday = yesterday()                                   and hour = toHour(now()) - 1                                   and is_real = 0                                 group by logday, product, product_group, product_name, os, ad_type, ad_type_name) t1                                 right join                             (select logday, product, os, hour, sum(key_value) as key_value                              from old_mysql_ads.realtime_au_nu_channel                              where logday = yesterday()                                and key_name = 'au'                                and hour = toHour(now()) - 1                              group by logday, product, os, hour                                 ) t2                             on t1.product = t2.product and toInt32(t1.chour) = t2.hour                                 and t1.os = t2.os                                 left join dwd.product_map_dist t3 on t2.product = t3.product                                 left join dwd.ad_type_basic_dist t4 on t1.ad_type = toString(t4.ad_type)                        order by hour                        )                 where logday = yesterday()                 and hour = toHour(now()) - 1               group by logday, product, os               )      ) t2                     on t1.key=t2.key  where ((subRate < -30 or subRate > 30) or (toutiaosubRate < -30 or toutiaosubRate > 30) or (kuaishousubRate < -30 or kuaishousubRate > 30) or         (guangdiantongsubRate < -30 or guangdiantongsubRate > 30) or         (baidusubRate < -30 or baidusubRate > 30) or (alisubRate < -30 or alisubRate > 30) or (aqysubRate < -30 or aqysubRate > 30) or         (sigmobsubRate < -30 or sigmobsubRate > 30) or         (vivosubRate < -30 or vivosubRate > 30) or (opposubRate < -30 or opposubRate > 30) or (unknownsubRate < -30 or unknownsubRate > 30) or         (qitasubRate < -30 or qitasubRate > 30) )    and dt2 > 0                                                                                                                                     "
    })
    List<ArpuCheckBean> queryPvAllDev();


    @Select({" select product, os,product_name, sum(ix) income, sum(dt) as dau, sum(vx) as pv, income/dau as arpu " +
            " from ( " +
            "       select logday, " +
            "              product, " +
            "              product_name, " +
            "              channel, " +
            "              os, " +
            "              sum(income) as ix, " +
            "              sum(pv)     as vx, " +
            "              max(dau)    as dt " +
            "       from old_mysql_ads.ad_arpu_real_sum_hour " +
            "       where logday = today() " +
            "         and hour = toHour(now()) - 1 " +
            "       group by logday, product,product_name, channel, os " +
            "          ) " +
            " group by product,product_name, os " +
            " having dau > 100 and dau < 3000 and arpu > 5;"})
    List<ArpuExpBaen> queryArpu();

    @Select({"select r1.product                                  as product, " +
            "             r3.product_name                               as product_name, " +
            "             r1.os                                         as os, " +
            "             r1.pos_name                                   as third_pos_name, " +
            "             r1.pos_id                                     as pos_id, " +
            "             r1.ad_id                                      as ad_id, " +
            "             r1.pv                                         as pv, " +
            "             r1.income                                     as income, " +
            "             bitmapAndCardinality(user_bit, tf_userbit)    as tfView, " +
            "             bitmapCardinality(user_bit)                   as userView, " +
            "             100 - toDecimal32(tfView / userView * 100, 2) as ntfRate " +
            "        from ( " +
            "               select product, os, a1.ad_id as ad_id, pv, user_bit, a2.pos_id as pos_id, pos_name,a4.ecpm * pv / 1000 as income " +
            "               from ( " +
            "                        select product, " +
            "                               os, " +
            "                               ad_id, " +
            "                               count()                                  as pv, " +
            "                               groupBitmapState(toUInt32OrZero(userid)) as user_bit " +
            "                        from ods.event_exposure_dist " +
            "                        where logday = today() " +
            "                          and ad_action = 'exposure' " +
            "                          and ad_type global in " +
            "                              (select toString(ad_type) from dwd.ad_type_basic_dist where type_name in ('视频','插屏','新插屏') and source_name ='穿山甲') " +
            "                        group by product, os, ad_id " +
            "                        having pv >= 1000 " +
            "                        ) a1 " +
            "                        left join (select ad_id, pos_id " +
            "                                   from dwd.product_ad_conf_dist " +
            "                                   group by ad_id, pos_id) a2 on a1.ad_id = a2.ad_id " +
            "                        left join (select * from ods_mysql.tb_ap_ad_budget) a4 on a1.ad_id = toString(a4.id) " +
            "                        left join (select pos_id, pos_name " +
            "                                   from dwd.product_pos_ecpm_dist " +
            "                                   where logday = today() - 2 " +
            "                                   group by pos_id, pos_name) a3 " +
            "                                  on a2.pos_id = a3.pos_id " +
            "               ) r1 " +
            "               left join( " +
            "          select product, os, groupBitmapState(toUInt32(ifNull(user_id,0))) as tf_userbit " +
            "          from dwd.user_active_dist " +
            "          where source in ('toutiao', 'kuaishou', 'guangdiantong') " +
            "          group by product, os " +
            "          ) r2 on r1.product = r2.product and r1.os = r2.os " +
            "               left join dwd.product_map_dist r3 on r1.product = r3.product " +
            "        where ntfRate >= 65 and income > 200 " +
            "        order by product "})
    List<UserFilterAdBean> queryExInfo();

    @Select({"SELECT" +
            " amd.id AS id," +
            " pmd.product AS product," +
            " pmd.product_name AS product_name," +
            " pmd.product_group AS product_group" +
            " FROM " +
            " dwd.app_mapping_dist amd " +
            " INNER JOIN dwd.product_map_dist pmd ON amd.product = pmd.product"})
    List<ProductEntity> queryProductMap();

    @Select("select (x1.allAmount)                                                                 as todayWithdraw,    " +
            "                   x1.hau                                                                                       as todayHau,    " +
            "                   todayWithdraw / todayHau                                                                     as todayRate,    " +
            "                   (x2.allAmount)                                                                 as yesterdayWithdraw,    " +
            "                   x2.hau                                                                                       as yesterdayHau,    " +
            "                   yesterdayWithdraw / yesterdayHau                                                             as yesterdayRate,    " +
            "                   ((x1.allAmount - x2.allAmount) / if(x2.allAmount <= 0, 1, x2.allAmount) * 100) as rate,    " +
            "                   x1.os                                                                                        as os,    " +
            "                   x1.product_name                                                                              as productName,  " +
            "                   x1.hWithdra                                                                    as hWithdra,  " +
            "                   x2.hWithdra                                                                    as yesHWithdra, " +
            "                   ((x1.hWithdra - x2.hWithdra) / if(x2.hWithdra <= 0, 1, x2.hWithdra) * 100)     as rateWithdra  " +
            "            from (    " +
            "                " +
            "                     select os, product_name, sum(withdraw) as allAmount, sum(hau) as hau, allAmount / hau as hWithdra    " +
            "                     from ads.realtime_withdraw_dau    " +
            "                     where logday = today()    " +
            "                       and hour = toHour(now()) - 1    " +
            "                     group by os, product_name    " +
            "                     having hau > 100    " +
            "                        and allAmount > 100    " +
            "                     ) x1    " +
            "                     left join (    " +
            "                select os, product_name, sum(withdraw) as allAmount, sum(hau) as hau, allAmount / hau as hWithdra    " +
            "                from ads.realtime_withdraw_dau    " +
            "                where logday = yesterday()    " +
            "                  and hour = toHour(now()) - 1    " +
            "                group by os, product_name    " +
            "                having hau > 100    " +
            "                   and allAmount > 100    " +
            "                ) x2 on x1.product_name = x2.product_name and x1.os = x2.os    " +
            "            where rateWithdra > 100    " +
            "              and yesterdayWithdraw > 100    " +
//            "              and todayWithdraw > 1000    " +
            "            order by x1.product_name;")
    List<WithdrawCheckBean> queryWithdrawLastHour();


    @Select({" select product,product_name,os,user_id,count(order_no) as order_time, " +
            "       sum(amount / 100) as withdraw " +
            " from dwd.withdraw_dist " +
            " where logday = today() " +
            "  and toHour(toDateTime(create_time / 1000)) = toHour(now()) - 1 " +
            "  and status = 5 " +
            " group by product, product_name, os, user_id " +
            " having withdraw > #{limitWithdraw}"})
    List<WithdrawBigAmountBean> queryWithdrawBigAmount(@Param("limitWithdraw") Integer limitWithdraw);

    @Select({"select t3.product      as product, " +
            "       t1.os           as os, " +
            "       t3.product_name as product_name, " +
            "       t1.channel      as channel, " +
            "       t1.withdraws    as withdraw_today, " +
            "       t1.haus         as hau_today, " +
            "       t1.withdrawRate as withdrawRate_today, " +
            "       t2.haus         as hau_yesterday, " +
            "       t2.withdraws    as withdraw_yesterday, " +
            "       t2.withdrawRate as withdrawRate_yesterday " +
            "from ( " +
            "         select product_name, " +
            "                os, " +
            "                channel, " +
            "                sum(withdraw)                             as withdraws, " +
            "                sum(hau)                                  as haus, " +
            "                if(haus > 0, withdraws / haus, withdraws) as withdrawRate " +
            "         from ads.realtime_withdraw_dau " +
            "         where logday = today() " +
            "           and hour = toHour(now()) - 1 " +
            "         group by product_name, os, channel " +
            "         having haus > 5 " +
            "         ) t1 " +
            "         left join ( " +
            "    select product_name, " +
            "           os, " +
            "           channel, " +
            "           sum(withdraw)                             as withdraws, " +
            "           sum(hau)                                  as haus, " +
            "           if(haus > 0, withdraws / haus, withdraws) as withdrawRate " +
            "    from ads.realtime_withdraw_dau " +
            "    where logday = yesterday() " +
            "      and hour = toHour(now()) - 1 " +
            "    group by product_name, os, channel " +
            "    having haus > 5 " +
            "    ) t2 on t1.product_name = t2.product_name and t1.os = t2.os and t1.channel = t2.channel " +
            "         left join dwd.product_map_dist t3 on t1.product_name = t3.product_name " +
            "where (withdrawRate_today - withdrawRate_yesterday) / withdrawRate_yesterday > 1 " +
            "  and withdraw_today > 500 " +
            "  and withdrawRate_today > 0.25 " +
            "order by product, os, channel"})
    List<WithdrawChannelPartBean> queryWithdrawPartChannel();


    @Select(" select s1.product       as product,  " +
            "       s2.product_name  as product_name,  " +
            "       s2.product_group as product_group,  " +
            "       distinct_count,  " +
            "       device_count,  " +
            "       oa_count  " +
            " from (  " +
            "         select product,  " +
            "                uniqExact(distinct_id) as distinct_count,  " +
            "                uniqExact(device_id)   as device_count,  " +
            "                uniqExact(oaid)        as oa_count  " +
            "         from ods.event_dist  " +
            "         where logday = yesterday()  " +
            "           and product global in (  " +
            "             select t3.product  " +
            "             from (  " +
            "                      select product, min(activeDay) as firstDay  " +
            "                      from (  " +
            "                            select toDate(logday) as activeDay, product, activate_device  " +
            "                            from ads.daily_result  " +
            "                            where active_device > 1000  " +
            "                              and os = 'android'  " +
            "                               )  " +
            "                      group by product  " +
            "                      having firstDay + 365 < today()  " +
            "                      ) t1  " +
            "                      full join (  " +
            "                 select product  " +
            "                 from (  " +
            "                       select toDate(logday) as activeDay, product  " +
            "                       from ads.daily_result  " +
            "                       where active_device > 100  " +
            "                         and activeDay >= today() - 15  " +
            "                         and os = 'android'  " +
            "                          )  " +
            "                 group by product  " +
            "                 ) t2 on t1.product = t2.product  " +
            "                      left join dwd.product_map_dist t3 on t1.product = t3.product_name  " +
            "             where t1.product != ''  " +
            "               and t2.product != ''  " +
            "         )  " +
            "           and os = 'android'  " +
            "           and toInt64OrZero(replaceAll(substring(os_version, 1, 2), '.', '')) >= 10  " +
            "         group by product  " +
            "         having (distinct_count - device_count) / device_count > 1  " +
            "            and distinct_count > 1000  " +
            "         ) s1  " +
            " left join dwd.product_map_dist s2 on s1.product = s2.product;")
    List<ProductDeviceExBean> queryProductExDevice();


    @Select(" select s1.product as product, " +
            "       s4.product_name as product_name, " +
            "       s3.channel as channel, " +
            "       sum(s1.withdraw)      as all_amount, " +
            "       uniqExact(s1.user_id) as withdraw_dnu, " +
            "       sum(c)                as withdraw_count, " +
            "       all_amount/withdraw_dnu as amount " +
            " from ( " +
            "         select product, user_id, sum(amount / 100) as withdraw, count() as c " +
            "         from dwd.withdraw_dist " +
            "         where status = 5 " +
            "           and logday = today() " +
            "           and toHour(toDateTime(create_time / 1000)) = toHour(now()) - 1 " +
            "         group by product, user_id " +
            "         ) s1 " +
            "         left join ( " +
            "    select userid, product " +
            "    from dwd.au_device_dist " +
            "    where logday = today() " +
            "    group by product, userid " +
            "    ) s2 on s1.product = s2.product and toString(s1.user_id) = s2.userid " +
            "         left join ( " +
            "    select product, user_id, channel " +
            "    from dwd.user_active_dist " +
            "    where user_id global in ( " +
            "        select user_id " +
            "        from dwd.withdraw_dist " +
            "        where status = 5 " +
            "          and logday = today() " +
            "          and toHour(toDateTime(create_time / 1000)) = toHour(now()) - 1 " +
            "    ) " +
            "    group by product, user_id, channel " +
            " " +
            "    ) s3 on s1.user_id = s3.user_id and s1.product = s3.product " +
            "         left join dwd.product_map_dist s4 on s1.product = s4.product " +
            " where s2.product = '' " +
            " group by s1.product, s3.channel, s4.product_name " +
            " having amount > 4 and withdraw_dnu > 20 ")
    List<NoPointExUserWithdrawBean> queryLastHourNoPointWithdrawUser();


    @Select(" select product,product_name, channel, sum(amounts/100) all_amount,sum(withdraw_amount/100) success_amount, count() withdraw_dnu, avg(withdraw_amount/100) amount " +
            "from ( " +
            "      select t1.product,t1.product_name, t1.user_id, t2.channel, t1.amounts,t1.withdraw_amount " +
            "      from ( " +
            "               select product,product_name, user_id " +
            "                       ,sum(if(status = 5,amount,0)) withdraw_amount " +
            "                    ,   sum(amount) amounts " +
            "               from dwd.withdraw_dist " +
            "               where logday = today() " +
            "                 and (product, user_id) global in " +
            "                     (select product, user_id from dwd.user_active_dist where toDate(create_time) = today()) " +
            "               group by product,product_name, user_id " +
            "               ) t1 " +
            "               left join ( " +
            "          select * from dwd.user_active_dist where toDate(create_time) = today() " +
            "          ) t2 on t1.product = t2.product and t1.user_id = t2.user_id " +
            "     )" +
            "group by product, product_name, channel " +
            "having withdraw_dnu > 18 and amount > 4")
    List<NoPointExUserWithdrawBean> checkUserAvgBigWithdrawUser();


    @Select(" select r1.product                                                                as product, " +
            "       r3.product_name                                                           as product_name, " +
            "       r3.product_group                                                          as product_group, " +
            "       r1.withdraw                                                               as today_withdraw, " +
            "       r2.withdraw                                                               as yesterday_withdraw, " +
            "       r1.withdraw / r1.uc                                                       as today_uc, " +
            "       r2.withdraw / r2.uc                                                       as yesterday_uc, " +
            "       if(r2.withdraw = 0, 100, (r1.withdraw - r2.withdraw) / r2.withdraw * 100) as rate_al, " +
            "       if(r2.uc = 0, 100, (today_uc - yesterday_uc) / yesterday_uc * 100)        as rate_uc " +
            " from ( " +
            "         select product, sum(amount / 100) as withdraw, uniqExact(user_id) as uc " +
            "         from dwd.withdraw_dist " +
            "         where logday = today() " +
            "           and status = 5 " +
            "           and toHour(toDateTime(create_time / 1000)) = toHour(now()) - 1 " +
            "         group by product " +
            "         ) r1 " +
            "         left join ( " +
            "    select product, sum(amount / 100) as withdraw, uniqExact(user_id) as uc " +
            "    from dwd.withdraw_dist " +
            "    where logday = yesterday() " +
            "      and status = 5 " +
            "      and toHour(toDateTime(create_time / 1000)) = toHour(now()) - 1 " +
            "    group by product " +
            "    ) r2 on r1. product = r2.product " +
            "         left join dwd.product_map_dist r3 on r1.product = r3.product " +
            " where rate_uc > 300 " +
            "  and r1.withdraw > 1000;")
    List<LastHourWithdrawBean> queryLastHourWithdrawFl();

    @Select(" select pv as  num\n" +
            "from (\n" +
            "         select count() as pv\n" +
            "         from dwd.withdraw_dist\n" +
            "         where logday = yesterday()\n" +
            "         )\n" +
            "where pv > 6500000 or pv < 1000000;")
    List<withdrawNumYesBean> queryWithdrawNumYes();

    @Select("select logday, " +
            "       product_group, " +
            "       product_name, " +
            "       (case " +
            "            when unit_type = 104 then '激活' " +
            "            when unit_type = 105 then '行为数' " +
            "            else toString(unit_type) end)   as tf_type, " +
            "       uniqExact(supplement_order_id)       as orderCount, " +
            "       sum(cost)                            as consumeAmount, " +
            "       sum(active)                          as conversion, " +
            "       round(consumeAmount / conversion, 2) as cpc, " +
            "       round(avg(bid), 2)                   as cap_bid, " +
            "       round(cpc / cap_bid, 2)                 as gap " +
            " from ( " +
            "      select toDate(closing_time)                logday, " +
            "             product, " +
            "             product_group, " +
            "             product_name, " +
            "             unit_type, " +
            "             advertiser_id, " +
            "             task_id, " +
            "             order_id, " +
            "             supplement_order_id, " +
            "             cpa / 1000                       as cpc, " +
            "             sum(consume_amount / 1000)       as cost, " +
            "             sum(conversion)                  as active, " +
            "             if(active > 0, cost / active, 0) as cpa_cl, " +
            "             unit_price / 1000                as bid " +
            "      from ads.kuaishou_star_supplement_order_process " +
            "      where logday = #{logday} " +
            "        and status = 2 " +
            "      group by product, product_group, product_name, order_id, logday, supplement_order_id, cpc, unit_type, unit_price, " +
            "               advertiser_id, task_id " +
            "      having cpc > bid * 3.5 " +
            "         and cost > 500 " +
            "         ) " +
            "group by product_group, product_name, tf_type, logday;")
    List<KuaishouDrCostBean> quureCostEr(@Param("logday") String logday);

    @Select(" select date, dsp, cost_fund, cost_result, (cost_fund / cost_result - 1)*100 as gap_rate " +
            " from (select date, dsp, sum(cost) cost_fund " +
            "      from (SELECT date, " +
            "                   '头条'                                          dsp, " +
            "                   sum(toutiao_cost) + sum(return_goods_cost) + sum(company_wallet_cost) as cost " +
            "            FROM ods_mysql.toutiao_fund " +
            "            WHERE toDate(date) = #{logday}  " +
            "            GROUP BY date " +
            "            union all " +
            "            select date, " +
            "                   '快手'              dsp, " +
            "                   sum(daily_charge) cost " +
            "            FROM ods_mysql.kuaishou_fund " +
            "            WHERE toDate(date) = #{logday}  " +
            "            GROUP BY date " +
            "            union all " +
            "            SELECT data_date, '广点通' dsp, SUM(paid) / 100 cost " +
            "            FROM ods_mysql.tencent_daily_balance_report_gr " +
            "            WHERE toDate(data_date) = #{logday}  " +
            "            group by data_date " +
            "            union all " +
            "            SELECT crawl_date, '聚星' dsp, SUM(cost) / 1000 cost " +
            "            FROM ods_mysql.kuaishou_star_order_daily " +
            "            WHERE toDate(crawl_date) = #{logday}  " +
            "            group by crawl_date " +
            "            union all " +
            "            SELECT logday, '百度' dsp1, SUM(cost) cost " +
            "            FROM old_mysql_ads.realtime_advertiser_cost " +
            "            WHERE toDate(logday) = #{logday}  " +
            "              and dsp = 'baidu' " +
            "            group by logday) " +
            "      group by date, dsp) a1 " +
            "         left join " +
            "     (select logday, " +
            "             (case " +
            "                  when channel_name = 'baidu_feed' then '百度' " +
            "                  when channel_name in ('csj', 'ttzz','ttzx') then '头条' " +
            "                  when channel_name = 'ksdrrw' then '聚星' " +
            "                  when channel_name = 'kuaishou' then '快手' " +
            "                  when channel_name = 'guangdiantong' then '广点通' " +
            "                  else channel_name end)   dsp, " +
            "             sum(toFloat64OrNull(cods)) as cost_result " +
            "      from old_mysql_ads.toufang_all_offline " +
            "      where logday = #{logday}  " +
            "      group by logday, dsp) a2 " +
            "     on a1.date = a2.logday and a1.dsp = a2.dsp " +
            " where abs(gap_rate) >= 5" +
            " order by cost_fund desc;")
    List<ChannelCostBean> queryChannelCostGap(@Param("logday")String logday);


    @Select(" select logday,product_group,channel_name,product_name,os, toufang_flag,sum(dnu) as nu from old_mysql_ads.realtime_store_product_report " +
            " where logday=yesterday() " +
            " and toufang_flag='非投放' " +
            " and os='android' " +
            " group by logday,product_group,channel_name, product_name, os, toufang_flag " +
            " having nu>=3000;")
    List<AppActiveBean> queryActiveStore();


    @Select(" select logday, dsp, cost_api, cost_result, cost_api / cost_result - 1 as gap  " +
            " from (select logday, '助推' dsp, sum(toFloat64OrNull(cods)) cost_result  " +
            "      from old_mysql_ads.toufang_all_offline  " +
            "      where channel_name = 'ksdrzt'  " +
            "        and logday = yesterday()  " +
            "      group by logday, dsp) a1  " +
            "         left join  " +
            "     (select toDate(closing_time) logday, '助推' dsp, sum(consume_amount / 1000) cost_api  " +
            "      from ods_mysql.kuaishou_star_supplement_order_yesterday  " +
            "      where status = 2  " +
            "        and toDate(closing_time) = yesterday()  " +
            "      group by logday, dsp) a2  " +
            "     on a1.logday = a2.logday" +
            " where abs(gap*100) > 20;")
    List<KsdrGapBean> queryYesterdayKsdrzt();


    @Select(" select dsp, sum(cost_reault) resultxh, sum(cost_api) apixh, apixh / resultxh - 1 as gap  " +
            " from (select logday, '助推' dsp, sum(toFloat64OrNull(cods)) cost_reault  " +
            "      from old_mysql_ads.toufang_all_offline  " +
            "      where channel_name = 'ksdrzt'  " +
            "        and logday between yesterday() - 2 and yesterday()  " +
            "      group by logday, dsp) a1  " +
            "         left join  " +
            "     (select toDate(closing_time) logday, '助推' dsp, sum(consume_amount / 1000) as cost_api  " +
            "      from ods_mysql.kuaishou_star_supplement_order_yesterday  " +
            "      where status = 2  " +
            "        and toDate(closing_time) between yesterday() - 2 and yesterday()  " +
            "      group by logday, dsp) a2  " +
            "     on a1.logday = a2.logday  " +
            " group by dsp" +
            " having abs(gap*100) > 10;")
    List<KsdrGapSubBean> queryYesterdayKddrSub();


    @Select(" select '助推' as dsp, api_cost as apixh, result as resultxh, (api_cost - result) / result as gap  " +
            " from (  " +
            "         select yesterday() as logday, todayClose - lastdayRunning + todayRunning as api_cost  " +
            "         from (  " +
            "                  select 'cost' as key, sum(consume_amount / 1000) as lastdayRunning  " +
            "                  from back_up.kuaishou_star_sup_order_bak  " +
            "                  where logday = yesterday()-1  " +
            "                    and status = 1  " +
            "                    and closing_time is null  " +
            "                    and toDate(promotion_begin_time) = yesterday() - 1  " +
            "                  ) s1  " +
            "                  left join (  " +
            "             select 'cost' as key, sum(consume_amount / 1000) as todayClose  " +
            "             from back_up.kuaishou_star_sup_order_bak  " +
            "             where logday = yesterday()  " +
            "               and status = 2  " +
            "               and toDate(closing_time) = yesterday()  " +
            "             ) s2 on s1.key = s2.key  " +
            "                  left join (  " +
            "             select 'cost' as key, sum(consume_amount / 1000) as todayRunning  " +
            "             from back_up.kuaishou_star_sup_order_bak  " +
            "             where logday = yesterday()  " +
            "               and status = 1  " +
            "               and closing_time is null  " +
            "               and toDate(promotion_begin_time) = yesterday()  " +
            "             ) s3 on s1.key = s3.key  " +
            "         ) k1  " +
            "         left join (  " +
            "    select logday, sum(toFloat64OrZero(cods)) result  " +
            "    from old_mysql_ads.toufang_all_offline  " +
            "    where logday = yesterday()  " +
            "      and channel_name = 'ksdrzt'  " +
            "    group by logday  " +
            "    ) k2 on k1.logday = k2.logday" +
            " where abs(gap*100) > 10;")
    List<KsdrGapSubBean> queryCutTableData();

    @Select(" select s1.product as product, " +
            "       s3.product_name as product_name, " +
            "       s3.product_group as product_group, " +
            "       s1.call_back_count                                                                    as count_today, " +
            "       s2.call_back_count                                                                    as count_yesterday, " +
            "       if(count_yesterday = 0, 100, (count_today - count_yesterday) / count_yesterday * 100) as rate_count " +
            " from ( " +
            "         select product, count() as call_back_count " +
            "         from ods.toutiao_click_dist " +
            "         where logday = today() " +
            "           and create_time between addMinutes(now(),-10) and now() " +
            "           and dsp in ('baidufeed', " +
            "                       'kuaishou', " +
            "                       'toutiao', " +
            "                       'guangdiantong' " +
            "             ) " +
            "         group by product " +
            "         ) s1 " +
            "         left join " +
            "     ( " +
            "         select product, count() as call_back_count " +
            "         from ods.toutiao_click_dist " +
            "         where logday = yesterday() " +
            "           and create_time between addMinutes(addDays(now(),-1),-10) and addDays(now(),-1) " +
            "           and dsp in ('baidufeed', " +
            "                       'kuaishou', " +
            "                       'toutiao', " +
            "                       'guangdiantong' " +
            "             ) " +
            "         group by product " +
            "         ) s2 on s1.product = s2.product " +
            "         left join dwd.product_map_dist s3 on replaceAll(s1.product, 'bd', '') = s3.product " +
            " where abs(rate_count) > 1000 and count_today > 10000 and count_yesterday > 10000 ")
    List<ClickCheckBean> queryClick();

    @Select("select * from ( " +
            "select " +
            "       a.product ,p.product_name,p.product_group, count(1) pv1 " +
            "     ,count(case when gy_type='sdk' then 1 end) pv2,pv2/pv1*100 as gap " +
            "from ods_mysql_realtime.ocpc_event as a " +
            "left join dwd.product_map_dist as p on a.product = p.product " +
            "where toDate(create_time) = toDate(today()) " +
            "and dsp = 'toutiao' " +
            "and event_type = 0 " +
            "group by a.product,p.product_name,p.product_group " +
            "having pv1>=500 " +
            ") as aa " +
            "where gap > 15;")
    List<SdkReturnBean> querySdkReturn();

    @Select("select wd.channel as channel,wd.product as product,p.product_name as product_name\n" +
            "                    ,p.product_group as product_group\n" +
            "                   ,count(distinct wd.device_id) nu,\n" +
            "                   count(distinct if(a1.user_id is not null,wd.device_id,null)) nnu,\n" +
            "                    nnu / nu as proportion\n" +
            "            from dwd.device_dist wd\n" +
            "            global left join(\n" +
            "                     select distinct user_id,channel, source\n" +
            "                     from dwd.user_active_dist\n" +
            "                     where source = '自然量'\n" +
            "                       and toDate(create_time)=today()\n" +
            "            ) a1 on toInt64OrNull(wd.userid) = a1.user_id\n" +
            "            left join dwd.product_map_dist as p on wd.product = p.product\n" +
            "            where wd.logday = today() and product not like '%wmin%'\n" +
            "            group by wd.channel,wd.product,p.product_name,p.product_group\n" +
            "            having (\n" +
            "                    wd.product not in ('wgls2','wgcz','wxkl','xfxz2','cjzcw','qmxsg','qzlgdxg','wydfd','wydfd','hyytl','wgls','djly','kldd','klldsh','jyqcz','ttdls','jfcx','qzmcahc','lsjyg','qzfbdzz')\n" +
            "                        and nnu / nu >=if(wd.channel like '%ksdr%' or wd.channel like 'AppStore' , 0.15,0.1)   and nu >= 300\n" +
            "                   )\n" +
            "                    or\n" +
            "                   (\n" +
            "                    wd.product  in ('wgcz','wxkl','xfxz2','cjzcw','qmxsg','qzlgdxg','wydfd','wydfd','hyytl','wgls','djly','kldd','klldsh','jyqcz','ttdls','jfcx','qzmcahc','lsjyg','qzfbdzz')\n" +
            "                    and nnu / nu >= 0.15   and nu >= 300\n" +
            "                    )\n" +
            "                  or\n" +
            "                (\n" +
            "                    wd.product ='wgls2' and wd.channel='AppStore' and nu >= 300 and nnu / nu >= 0.4\n" +
            "                    )\n" +
            "                   or\n" +
            "                (\n" +
            "                   wd.product ='wgls2' and wd.channel<>'AppStore' and nu >= 300\n" +
            "                     and nnu / nu >=if(wd.channel like '%ksdr%' or wd.channel like 'AppStore' , 0.15,0.1)\n" +
            "                    )\n" +
            "            ;")
    List<ChannelNaturalProportionBean> queryChannelNaturalProPortion();


    @Select("select aa.channel, aa.product, aa.product_name, aa.product_group\n" +
            "       , round(aa.total_dau/4) as channel_dnu, round(aa.total_pv/4), aa.avg_pv as channel_avg_pv\n" +
            "       , round(bb.total_dau/4) as total_dnu, round(bb.total_pv/4), bb.avg_pv as avg_pv\n" +
            "      , if(bb.avg_pv > 0, channel_avg_pv / bb.avg_pv, 0) as gap\n" +
            "     --,aa.total_dau, aa.total_pv, aa.avg_pv\n" +
            "     --,bb.total_dau,  bb.total_pv, bb.avg_pv\n" +
            "    --,if(bb.avg_pv > 0, aa.avg_pv / bb.avg_pv, 0) as gap\n" +
            "from (\n" +
            "    select channel,\n" +
            "             product,\n" +
            "             product_name,\n" +
            "             product_group,\n" +
            "             sum(total_dau)           as total_dau,\n" +
            "             sum(total_pv)            as total_pv,\n" +
            "             total_pv / total_dau as avg_pv\n" +
            "    from (\n" +
            "          select channel,\n" +
            "                 hour,\n" +
            "                 product,\n" +
            "                 product_name,\n" +
            "                 product_group,\n" +
            "                 max(dau)           as total_dau,\n" +
            "                 sum(pv)            as total_pv,\n" +
            "                 sum(pv) / max(dau) as avg_pv\n" +
            "          from old_mysql_ads.ad_arpu_real_nu_sum\n" +
            "          where logday = today()\n" +
            "            and hour >= toInt32(formatDateTime(now(), '%H')) - 3\n" +
            "            and (channel like '%honor%' or channel like '%huawei%'\n" +
            "              or channel like '%vivo%'\n" +
            "              or channel like '%oppo%'\n" +
            "              or channel like '%xiaomi%'\n" +
            "              or channel like '%mi%')\n" +
            "          group by channel, hour, product, product_name, product_group\n" +
            "    ) as al\n" +
            "    group by channel, product, product_name, product_group\n" +
            ") as aa\n" +
            "left join (\n" +
            "        select  product,product_name,product_group,\n" +
            "         sum(total_dau)           as total_dau,\n" +
            "        sum(total_pv)            as total_pv,\n" +
            "        total_pv / total_dau as avg_pv\n" +
            "from (\n" +
            "    select\n" +
            "            channel,hour,product,product_name,product_group,\n" +
            "            max(dau)           as total_dau,\n" +
            "            sum(pv)            as total_pv,\n" +
            "            sum(pv) / max(dau) as avg_pv\n" +
            "    from old_mysql_ads.ad_arpu_real_nu_sum\n" +
            "    where logday = today()\n" +
            "           and hour >= toInt32(formatDateTime(now(), '%H')) - 3\n" +
            "    group by channel, hour, product, product_name, product_group\n" +
            "    ) as al\n" +
            "    group by product, product_name, product_group\n" +
            ") as bb on aa.product = bb.product\n" +
            "where 1=1\n" +
            "      -- bb.avg_pv > 10\n" +
            "  and gap != 0\n" +
            "  and aa.total_dau > 100\n" +
            "  and toInt32(formatDateTime(now(), '%H')) >= 4\n" +
            "  -- and aa.avg_pv < 10\n" +
            "  and gap < 0.4\n" +
            "order by gap asc\n" +
            ";\n" +
            "\n")
    List<ChannelHourPv> queryChannelHourPv();

    @Select("select dd.product                    as                                                       product\n" +
            "     , p.product_name                as                                                       product_name\n" +
            "     , p.product_group               as                                                       product_group\n" +
            "     , dd.os                         as                                                       os\n" +
            "     , count(distinct dd.device_id)                                                           nu\n" +
            "     , count(distinct if(b.videoexposure = 0 or b.videoexposure is null, dd.device_id, null)) zinu\n" +
            "     , count(distinct if(b.exposure = 0 or b.exposure is null, dd.device_id, null))           zinu2\n" +
            "     , zinu / nu                     as                                                       proportion\n" +
            "     , zinu2 / nu                    as                                                       exposure_proportion\n" +
            "     , round(sum(tt.ecpm) / 1000, 2) as                                                       income\n" +
            "from dwd.device_dist dd\n" +
            "         global\n" +
            "         left join (\n" +
            "    select product,\n" +
            "           os,\n" +
            "           if(os = 'ios' and product = 'msyq', distinct_id, device_id)                   as deviceid,\n" +
            "           count(if(ad_type global in (select toString(ad_type)\n" +
            "                                       from dwd.ad_type_basic_dist\n" +
            "                                       where ad_type_name like '%视频%'), userid, null)) as videoexposure,\n" +
            "           count(userid)                                                                 as exposure\n" +
            "    from ods.event_exposure_dist\n" +
            "    where logday = today()\n" +
            "      and product_part = 'mvp'\n" +
            "      and event = 'AdData'\n" +
            "      and ad_action = 'exposure'\n" +
            "--                  and ad_type global in (\n" +
            "--                    select toString(ad_type) from  dwd.ad_type_basic_dist\n" +
            "--                         where ad_type_name like '%视频%'\n" +
            "--                  )\n" +
            "      and userid != ''\n" +
            "    group by product, os, if(os = 'ios' and product = 'msyq', distinct_id, device_id)\n" +
            "    ) b\n" +
            "                   on dd.device_id = b.deviceid\n" +
            "                       and dd.product = b.product\n" +
            "                       and dd.os = b.os\n" +
            "         left join dwd.product_map_dist p on dd.product = p.product\n" +
            "    global\n" +
            "         left join\n" +
            "     (select device_id, os, product, sum(ecpm) as ecpm\n" +
            "      from dwd.product_ad_exposure_sub_realtime\n" +
            "      where logday = today()\n" +
            "      group by device_id, os, product\n" +
            "         ) tt\n" +
            "     on dd.device_id = tt.device_id and dd.os = tt.os and dd.product = tt.product\n" +
            "where dd.logday = today()\n" +
            "  and dd.product not like '%wmin%'\n" +
            "group by dd.product, p.product_name, p.product_group, dd.os\n" +
            "having (\n" +
            "           (nu between 50 and 1000 and if(nu > 0, zinu / nu, 0) > 0.5)\n" +
            "               or (nu > 1000 and if(nu > 0, zinu / nu, 0) > 0.3)\n" +
            "           );")
    List<ProductNoExposurePropBean> queryProductNoExposureProPortion();


    @Select(" select date, dsp, cost_fund, cost_result, cost_fund - cost_result as gap " +
            " from (select date, dsp, sum(cost) cost_fund " +
            "      from (SELECT date, " +
            "                   '头条'                                          dsp, " +
            "                   sum(toutiao_cost) + sum(return_goods_cost) + sum(company_wallet_cost) as cost " +
            "            FROM ods_mysql.toutiao_fund_gr " +
            "            WHERE toDate(date) = yesterday() " +
            "            GROUP BY date " +
            "            union all " +
            "            select date, " +
            "                   '快手'              dsp, " +
            "                   sum(daily_charge) cost " +
            "            FROM ods_mysql.kuaishou_fund " +
            "            WHERE toDate(date) = yesterday() " +
            "            GROUP BY date " +
            "            union all " +
            "            SELECT data_date, '广点通' dsp, SUM(paid) / 100 cost " +
            "            FROM ods_mysql.tencent_daily_balance_report_gr " +
            "            WHERE toDate(data_date) = yesterday() " +
            "            group by data_date " +
            "            union all " +
            "            SELECT logday, '聚星' dsp, SUM(cost) cost " +
            "            FROM ads.realtime_kuaishou_talent_report " +
            "            WHERE toDate(logday) = yesterday() " +
            "              and is_flow_boost != '助推' " +
            "            group by logday " +
            "            union all " +
            "            SELECT logday, '流量助推' dsp, SUM(cost) cost " +
            "            FROM ads.realtime_kuaishou_talent_report " +
            "            WHERE toDate(logday) = yesterday() " +
            "              and is_flow_boost = '助推' " +
            "            group by logday " +
            "            union all " +
            "            SELECT logday, '百度' dsp1, SUM(cost) cost " +
            "            FROM old_mysql_ads.realtime_advertiser_cost " +
            "            WHERE toDate(logday) = yesterday() " +
            "              and dsp = 'baidu' " +
            "            group by logday) " +
            "      group by date, dsp) a1 " +
            "         left join " +
            "     (select logday, " +
            "             (case " +
            "                  when channel_name = 'baidu_feed' then '百度' " +
            "                  when channel_name in ('csj', 'ttzz') then '头条' " +
            "                  when channel_name = 'ksdrrw' then '聚星' " +
            "                  when channel_name = 'kuaishou' then '快手' " +
            "                  when channel_name = 'ksdrzt' then '流量助推' " +
            "                  when channel_name = 'guangdiantong' then '广点通' " +
            "                  else channel_name end)   dsp, " +
            "             sum(toFloat64OrNull(cods)) as cost_result " +
            "      from old_mysql_ads.toufang_all_offline " +
            "      where logday = yesterday() " +
            "      group by logday, dsp) a2 " +
            "     on a1.date = a2.logday and a1.dsp = a2.dsp " +
            " having abs(gap) > 1 " +
            " order by dsp, logday;")
    List<CostGapBean> queryCostGap1();


    @Select(" select logday, " +
            "       sum(toFloat64(xhdate)) as cost_date, " +
            "       sum(toFloat64(xhhour)) as cost_hour, " +
            "       sum(toFloat64(xhdate)) - sum(toFloat64(xhhour)) as gap " +
            " from (select logday, " +
            "             supplement_order_id, " +
            "             sum(ceil(toDecimal32(consume_amount / 1000, 4), 2)) as xhdate, " +
            "             sum(consume_amount / 1000)                          as xh " +
            "      from ods_mysql.kuaishou_supplement_order_realtime_today " +
            "      where toDate(logday) = today() " +
            "      group by supplement_order_id, logday) a2 " +
            "         left join " +
            "     (select supplement_order_id, sum(ceil(toDecimal32(consume_amount / 1000, 4), 2)) as xhhour " +
            "      from ods_mysql.kuaishou_supplement_order_realtime_hour " +
            "      where toDate(logday) = today() " +
            "      group by supplement_order_id) a3 " +
            "     on a2.supplement_order_id = a3.supplement_order_id " +
            " group by logday " +
            " having abs(gap) > 1500;")
    List<KuaishouZtCostBaen> queryKuaishouZtRealtimeGap();


    @Select("select logday," +
            "       sum(toFloat64(xhdate)) as cost_date, " +
            "       sum(toFloat64(xhreal)) as cost_real, " +
            "       sum(toFloat64(xhhour)) as cost_hour, " +
            "       sum(toFloat64(xhdate)) - sum(toFloat64(xhhour)) as gap, " +
            "       sum(toFloat64(xhdate)) - sum(toFloat64(xhreal)) as gap1 " +
            " from (select toDate(closing_time) as logday,supplement_order_id, " +
            "             sum(ceil(toDecimal32(consume_amount / 1000, 3), 2)) as xhdate " +
            "      from back_up.kuaishou_star_sup_order_bak " +
            "      where toDate(closing_time) = yesterday() " +
            "      group by supplement_order_id,logday) a2 " +
            "         left join " +
            "     (select supplement_order_id, ceil(toDecimal32(sum(consume_amount) / 1000, 3), 2) as xhreal " +
            "      from back_up.kuaishou_supplement_order_realtime " +
            "      where logday <= yesterday() " +
            "      group by supplement_order_id) a3 " +
            "     on a2.supplement_order_id = a3.supplement_order_id " +
            "         left join " +
            "     (select supplement_order_id, ceil(toDecimal32(sum(consume_amount) / 1000, 3), 2) as xhhour " +
            "      from back_up.kuaishou_supplement_order_realtime_hour " +
            "      where logday <= yesterday() " +
            "      group by supplement_order_id) a4 " +
            "     on a2.supplement_order_id = a4.supplement_order_id" +
            " group by logday having abs(gap) > 1 or abs(gap1) > 1"
    )
    List<KuaishouZtCostBaen> queryKuaishouZtLogdayGap();

    @Select(" select data_date,  " +
            "       uniqExact(ad_id)                            as cost_ad,  " +
            "       uniqExact(if(cpa_bid is null, ad_id, null)) as no_cpa_ad,  " +
            "       no_cpa_ad / cost_ad * 100                        as rate  " +
            " from ads.toutiao_report_tf_new_ad_process  " +
            " where data_date =today() and cost>0  " +
            " group by data_date  " +
            " having rate > 5")
    List<TTCpaBidBean> queryTTCpaBidTask();



    @Select(" select s1.product       as product, " +
            "       s2.product_name  as product_name, " +
            "       s2.product_group as product_group, " +
            "       s1.ds_count      as user_count, " +
            "       s1.device_count  as device_count, " +
            "       s1.empty_pv, " +
            "       s1.empty_pv_rate, " +
            "       s1.pv, " +
            "       s1.rate          as rate " +
            "from ( " +
            "         select product, " +
            "                os, " +
            "                uniqExact(userid)                                          as ds_count, " +
            "                uniqExact(device_id)                                       as device_count, " +
            "                sum(if(device_id = '', 1, 0))                              as empty_pv, " +
            "                count()                                                    as pv, " +
            "                toDecimal32(empty_pv / pv * 100, 2)                        as empty_pv_rate, " +
            "                toDecimal32((ds_count - device_count) / ds_count * 100, 2) as rate " +
            "         from ods.event_exposure_dist " +
            "         where logday = yesterday() " +
            "           and product != 'dszg' " +
            "           and ad_action = 'exposure' " +
            "           and ad_type global in ( " +
            "             select toString(ad_type) from dwd.ad_type_basic_dist where type_name = '视频' " +
            "         ) " +
            "         group by product, os " +
            "         having ds_count > 1000 " +
            "            and rate > 10 " +
            "         ) s1 " +
            "         left join dwd.product_map_dist s2 on s1.product = s2.product " +
            "order by empty_pv_rate desc ")
    List<ExposureNullBean> queryExposureDeviceNull();



    @Select(" select order_id,sum(consume_amount/1000)* 0.03 as fee from ads.kuaishou_star_supplement_order_process where " +
            " promotion_begin_time>='2024-01-17 16:00:00' " +
            " group by order_id " +
            " having fee > 20000 ")
    List<OrderFeeBean> queryOrderFeeLimit();


    @Select(" select product,  " +
            "       product_group,  " +
            "       product_name,  " +
            "       group_id,  " +
            "       toDecimal32(sum(cost),2)    as cost,  " +
            "       sum(active_convert) as convert,  " +
            "       toDecimal32(avg(cpa_bid),2)       as cpa_bid  " +
            " from ads.realtime_toutiao_talent_report  " +
            " where logday = today()  " +
            "  and product global in (  " +
            "    select product  " +
            "    from (  " +
            "          select product, sum(rebate_cost) as cost  " +
            "          from ads.realtime_toutiao_talent_report  " +
            "          where logday = today()  " +
            "          group by product, product_group, product_name  " +
            "          having cost > 30000  " +
            "             )  " +
            "    union all  " +
            "    select product  " +
            "    from (select product, product_group, product_name, group_id, sum(rebate_cost) as cost  " +
            "          from ads.realtime_toutiao_talent_report  " +
            "          where logday = today()  " +
            "          group by product, product_group, product_name, group_id  " +
            "          having cost > 10000  " +
            "             )  " +
            " )  " +
            " group by product, product_group, product_name, group_id")
    List<ToutiaoDrCostBean> queryToutiaoGroupIdCost();


    @Select("select s1.dsp                                         as dsp,   " +
            "       s1.cost                                        as cost_yes,   " +
            "       s1.dnu_third                                   as dnu_third_yes,   " +
            "       s1.avg_cpa                                     as cpa_yes,   " +
            "       s1.dnu_own                                     as dnu_own_yes,   " +
            "       s2.cost                                        as cost_ty,   " +
            "       s2.dnu_third                                   as dnu_third_ty,   " +
            "       s2.avg_cpa                                     as cpa_ty,   " +
            "       s2.dnu_own                                     as dnu_own_ty,   " +
            "       (dnu_own_ty - dnu_third_ty) / dnu_own_ty * 100 as dnu_gap,   " +
            "       (cpa_ty - cpa_yes) / cpa_ty * 100              as cpa_gap   " +
            " from (   " +
            "   " +
            "         select k1.dsp           as dsp,   " +
            "                k1.cost          as cost,   " +
            "                k1.dnu_third     as dnu_third,   " +
            "                cost / dnu_third as avg_cpa,   " +
            "                k2.dnu_own       as dnu_own   " +
            "         from (   " +
            "                  select dsp, sum(cost) as cost, sum(active) as dnu_third   " +
            "                  from ads.realtime_advertiser_cost   " +
            "                  where logday = yesterday()   " +
            "                    and hour = toHour(now()) - 1   " +
            "                  group by dsp   " +
            "                  ) k1   " +
            "                  left join (   " +
            "             select dsp, sum(active) as dnu_own   " +
            "             from (   " +
            "                   select product,   " +
            "                          case   " +
            "                              when dsp = 'baidufeed' then 'baidu'   " +
            "                              when dsp = 'kuaishou' and account_id global in (   " +
            "                                  select toString(advertiser_id)   " +
            "                                  from config.auth_kuaishou_app_dist   " +
            "                                  where account_type = 2   " +
            "                              ) then 'kuaishoudaren'   " +
            "                              else dsp end  as dsp,   " +
            "                          uniqExact(userId) as active   " +
            "                   from dwd.user_event_dist   " +
            "                   where toDate(create_time) = yesterday()   " +
            "                     and toHour(create_time) = toHour(now()) - 1   " +
            "                     and event_type = 0   " +
            "                   group by product, dsp   " +
            "                      )   " +
            "             group by dsp   " +
            "             ) k2 on k1.dsp = k2.dsp   " +
            "         ) s1   " +
            "         left join (   " +
            "    select k1.dsp           as dsp,   " +
            "           k1.cost          as cost,   " +
            "           k1.dnu_third     as dnu_third,   " +
            "           cost / dnu_third as avg_cpa,   " +
            "           k2.dnu_own       as dnu_own   " +
            "    from (   " +
            "             select dsp, sum(cost) as cost, sum(active) as dnu_third   " +
            "             from ads.realtime_advertiser_cost   " +
            "             where logday = today()   " +
            "               and hour = toHour(now()) - 1   " +
            "             group by dsp   " +
            "             ) k1   " +
            "             left join (   " +
            "        select dsp, sum(active) as dnu_own   " +
            "        from (   " +
            "              select product,   " +
            "                     case   " +
            "                         when dsp = 'baidufeed' then 'baidu'   " +
            "                         when dsp = 'kuaishou' and account_id global in (   " +
            "                             select toString(advertiser_id) from config.auth_kuaishou_app_dist where account_type = 2   " +
            "                         ) then 'kuaishoudaren'   " +
            "                         else dsp end  as dsp,   " +
            "                     uniqExact(userId) as active   " +
            "              from dwd.user_event_dist   " +
            "              where toDate(create_time) = today()   " +
            "                and toHour(create_time) = toHour(now()) - 1   " +
            "                and event_type = 0   " +
            "              group by product, dsp   " +
            "                 )   " +
            "        group by dsp   " +
            "        ) k2 on k1.dsp = k2.dsp   " +
            "    ) s2 on s1.dsp = s2.dsp   " +
            " where s1.dsp != 'oppo'   " +
            "  and abs(dnu_gap) > #{gap}   ")
    List<RealtimeCostHourBean> queryRealHourCostEx(@Param("gap") Integer gapRate);


    /*
    * ip异常
    *   数据：
    *   ***********,308.**************,"[[**********, 小熊吃水果, 300.29], [1867805653, 天宫杂货铺, 7.69], [1891061838, 麻友解压馆, 0.36], [1829459765, 农场对对碰, 0.35]]"
        *************,306.92,"[[**********, 小熊吃水果, 300.29], [1863239098, 消消大师, 4.51], [1777799510, 小土豆快上车, 1.38], [1902788778, 解压爱消消, 0.52], [1716599908, 开心落落消, 0.22]]"

    * */
    @Select({"select ip1 as ip,round(amountsum,2) as sum_withdraw,sortmap as map\n" +
            "from(\n" +
            "    select  ip1,\n" +
            "        sum(amountf) amountsum,\n" +
            "        groupArray(toString(userid)) guid,\n" +
            "        groupArray(toString(productname)) gpn,\n" +
            "        groupArray(toString(product)) gp,\n" +
            "        groupArray(round(amountf,2)) ga,\n" +
            "        arrayMap((x, y, z,l) -> (x, y, z,l), guid, gpn, ga,gp) rsmap,\n" +
            "        arrayReverseSort(x->tupleElement(x, 3), rsmap) sortmap\n" +
            "    from (\n" +
            "             select a1.ip as ip1\n" +
            "                  , user_id as userid\n" +
            "                  , wd.product_name as productname\n" +
            "                  , wd.product as product\n" +
            "                  , sum(ifNull(amount, 0) / 100) as amountf\n" +
            "             from dwd.withdraw_dist wd\n" +
            "             global left join (\n" +
            "                       select distinct userid, ip from ods.event_dist\n" +
            "                              where logday >= today() - 3 and ip is not null\n" +
            "    and userid global in (select cast(user_id as String) from  dwd.withdraw_dist wd   where wd.logday = today() and wd.status = 5 group by user_id)\n" +
            "             ) a1 on wd.user_id = toInt64OrNull(a1.userid)\n" +
            "             where wd.logday = today() and wd.status = 5 and a1.ip not in ('************')\n" +
            "             group by ip1, userid, productname,product\n" +
            "    )\n" +
            "    group by ip1 having  amountsum > 300\n" +
            "    order by amountsum desc\n" +
            ")\n" +
            ";"})
    List<WithDrawIpNewBean> queryWithDrawIP();

    @Select({"select product,product_name,product_group,os,round(sum(income-cost-withdraw+pre_kf_cost),2) as predict,sum(dau) as dau\n" +
            "from old_mysql_ads.realtime_result\n" +
            "where logday=today() and hour=${hour}\n" +
            "group by product,product_name,product_group,os having predict < -1000\n" +
            "order by predict desc;"})
    List<ProfitRealtimeHourAlertBean> profitRealtimeHourAlert(@Param("hour") int hour);



    //新用户占比
    @Select({"select\n" +
            "    wd.os as os,wd.product as product,pr.product_name as product_name,\n" +
            "    count(distinct device_id) as count_device,count(distinct wd.userid) as count_all,\n" +
            "    count(distinct if(a1.source='自然量',wd.userid,null)) as count_natural\n" +
            "from dwd.device_dist wd\n" +
            "global left join(\n" +
            "                    select distinct user_id,source\n" +
            "                    from dwd.user_active_dist\n" +
            "                    where source = '自然量' and toDate(create_time) between today() - 365 and today()\n" +
            ") a1 on toInt64OrNull(wd.userid) = a1.user_id and a1.product = wd.product\n" +
            "left join dwd.product_map_dist as pr on wd.product = pr.product\n" +
            "where wd.logday = today()\n" +
            "group by wd.os, wd.product,pr.product_name\n" +
            "having count_natural/count_all >= 0.25 and count_all >= 500\n" +
            ";"})
    List<DeviceNewBean> queryDeviceNew();

    //自然量提现异常
    @Select({"select wd.os                                                                                    as os,\n" +
            "       wd.product_name                                                                          as product_name,\n" +
            "       round(sum(ifNull(wd.amount, 0) / 100), 2)                                                as sum_all_all,\n" +
            "       round(sum(ifNull(if(wd.status = 5, wd.amount, 0), 0) / 100), 2)                          as sum_all,\n" +
            "       round(sum(ifNull(if(wd.status = 5 and wd.update_time / 1000 > toUnixTimestamp(now()) - 60 * 60, wd.amount, 0),\n" +
            "                        0) / 100), 2)                                                           as sum_hour_all,\n" +
            "       round(sum(ifNull(if(wd.status = 5 and a1.source = '自然量', wd.amount, 0), 0) / 100), 2) as sum_natural,\n" +
            "       round(sum(ifNull(if(wd.status = 5 and wd.update_time / 1000 > toUnixTimestamp(now()) - 60 * 60 and\n" +
            "                           a1.source = '自然量', wd.amount, 0), 0) / 100), 2)                   as sum_hour_natural,\n" +
            "       round(sum(ifNull(if(wd.status = 5 and wd.update_time / 1000 > toUnixTimestamp(now()) - 10 * 60 and\n" +
            "                           a1.source = '自然量', wd.amount, 0), 0) / 100), 2)                   as sum_minus_natural\n" +
            "from dwd.withdraw_dist wd\n" +
            "         global\n" +
            "         left join (\n" +
            "    select os, toInt64OrZero(userId) as user_id, product, '自然量' as source\n" +
            "    from ods_mysql_realtime.ocpc_event\n" +
            "    where toDate(create_time) = today()\n" +
            "      and event_type = 0\n" +
            "      and (gy_type is null or gy_type = '' or gy_type = 'packageGy')\n" +
            "      and userId != ''\n" +
            "      and concat(userId, product) global not in (select concat(user_id, product)\n" +
            "                                                 from dwd.caid_mapping_gy_data_dist\n" +
            "                                                 where logday = today())\n" +
            "    group by os, userId, product\n" +
            "    ) a1\n" +
            "                   on wd.user_id = a1.user_id\n" +
            "                       and wd.product = a1.product\n" +
            "                       and wd.os = a1.os\n" +
            "where wd.logday = today()\n" +
            "-- and wd.status = 5\n" +
            "group by wd.os, wd.product_name\n" +
            "having sum_natural / sum_all >= 0.15\n" +
            "   and sum_all >= 100\n" +
            "   and sum_hour_natural >= 10\n" +
            "   and sum_minus_natural > 0;"})
    List<WithDrawNaturalBean> queryWithDrawNatural();


    /**
     *
     */
    @Select({"select count() from (select if(ck_income > 0 ,yes_income/ck_income,0) gap_income\n" +
            "     , if(ck_chuanshanjia > 0 ,yes_chuanshanjia/ck_chuanshanjia,0) gap_chuanshanjia\n" +
            "     , if(ck_guangdiantong > 0 ,yes_guangdiantong/ck_guangdiantong,0) gap_guangdiantong\n" +
            "     , if(ck_kuaishou > 0 ,yes_kuaishou/ck_kuaishou,0) gap_kuaishou\n" +
            "     , if(ck_baidu > 0 ,yes_baidu/ck_baidu,0) gap_baidu, if(ck_ali > 0 ,yes_ali/ck_ali,0) gap_ali\n" +
            "from (\n" +
            "    select #{yesLogday} as logday2 ,sum(platform_income) as yes_income\n" +
            "         ,sum(platform_chuanshanjia_income) as yes_chuanshanjia ,sum(platform_guangdiantong_income) as yes_guangdiantong\n" +
            "         ,sum(platform_kuaishou_income) as yes_kuaishou ,sum(platform_baidu_income) as yes_baidu\n" +
            "         ,sum(platform_ali_income) as yes_ali\n" +
            "    from ads.daily_result where logday = #{yesLogday} group by logday\n" +
            "    ) as yes left join (\n" +
            "        select #{yesLogday} as logday2 ,sum(platform_income) as ck_income\n" +
            "             ,sum(platform_chuanshanjia_income) as ck_chuanshanjia ,sum(platform_guangdiantong_income) as ck_guangdiantong\n" +
            "             ,sum(platform_kuaishou_income) as ck_kuaishou ,sum(platform_baidu_income) as ck_baidu\n" +
            "             ,sum(platform_ali_income) as ck_ali from old_mysql_ads.daily_result\n" +
            "        where logday = #{beforeYesLogday} group by logday ) as check on yes.logday2 = check.logday2\n" +
            "    where gap_income < 0.7 or gap_chuanshanjia < 0.7 or gap_guangdiantong < 0.7\n" +
            "    or yes_kuaishou < 0.7 or  yes_baidu < 0.7 or yes_ali < 0.7);"})
    Integer startDailyResultCheck(@Param("yesLogday")String yesLogday,@Param("beforeYesLogday")String beforeYesLogday);

    /**
     * description: 实时日报预警 收入比较 如果两者相差超过百分之四十，就预警（无论是今日的收入还是昨日的）
     */
    @Select({"select count() from\n" +
            "(\n" +
            "select hour,today_income,yes_income,if(yes_income > 0 and today_income > 0 ,today_income/yes_income,0.4) as gap1,if(today_income > 0 and yes_income > 0,yes_income/today_income,0.4) as gap2 from\n" +
            "(\n" +
            "    select hour,sum(income) as yes_income from old_mysql_ads.realtime_result\n" +
            "where logday = #{yesLogday}\n" +
            "group by hour\n" +
            "\n" +
            "    ) as today\n" +
            "left join (\n" +
            "select hour,sum(income) as today_income from old_mysql_ads.realtime_result\n" +
            "where logday = #{today}\n" +
            "group by hour\n" +
            ") as yes on today.hour = yes.hour\n" +
            "where gap1 < 0.4 or gap2 < 0.4\n" +
            ")\n" +
            "as check;"})
    Integer realtimeResultHourIncomeCheck(@Param("today")String today,@Param("yesLogday")String yesLogday);


    @Select("  select\n" +
            "       '总收入' as income,ck_income,yes_income,round(if(ck_income > 0 ,yes_income/ck_income,0),2) gap_income,\n" +
            "       '穿山甲' as chuanshanjia,ck_chuanshanjia,yes_chuanshanjia,round(if(ck_chuanshanjia > 0 ,yes_chuanshanjia/ck_chuanshanjia,0),2) gap_chuanshanjia,\n" +
            "       '广点通' as guangdiantong,ck_guangdiantong,yes_guangdiantong,round(if(ck_guangdiantong > 0 ,yes_guangdiantong/ck_guangdiantong,0),2) gap_guangdiantong,\n" +
            "       '快手收入' as kuaishou,ck_kuaishou,yes_kuaishou,round(if(ck_kuaishou > 0 ,yes_kuaishou/ck_kuaishou,0),2) gap_kuaishou,\n" +
            "       '百度收入' as baidu,ck_baidu,yes_baidu,round(if(ck_baidu > 0 ,yes_baidu/ck_baidu,0),2) gap_baidu,\n" +
            "       '阿里收入' as ali,ck_ali,yes_ali,round(if(ck_ali > 0 ,yes_ali/ck_ali,0),2) gap_ali\n" +
            "       from (\n" +
            "            select\n" +
            "                    #{yesLogday} as logday2\n" +
            "                    ,sum(platform_income) as yes_income\n" +
            "                    ,sum(platform_chuanshanjia_income) as yes_chuanshanjia\n" +
            "                    ,sum(platform_guangdiantong_income) as yes_guangdiantong\n" +
            "                    ,sum(platform_kuaishou_income) as yes_kuaishou\n" +
            "                    ,sum(platform_baidu_income) as yes_baidu\n" +
            "                    ,sum(platform_ali_income) as yes_ali\n" +
            "            from ads.daily_result\n" +
            "            where logday = #{yesLogday}\n" +
            "           group by logday\n" +
            ") as yes\n" +
            " left join (\n" +
            "    select\n" +
            "            #{yesLogday} as logday2\n" +
            "            ,sum(platform_income) as ck_income\n" +
            "            ,sum(platform_chuanshanjia_income) as ck_chuanshanjia\n" +
            "            ,sum(platform_guangdiantong_income) as ck_guangdiantong\n" +
            "            ,sum(platform_kuaishou_income) as ck_kuaishou\n" +
            "            ,sum(platform_baidu_income) as ck_baidu\n" +
            "            ,sum(platform_ali_income) as ck_ali\n" +
            "    from old_mysql_ads.daily_result\n" +
            "    where logday = #{beforeYesLogday}\n" +
            "     group by logday\n" +
            ") as check on yes.logday2 = check.logday2\n" +
            ";")
    DailyResultPlatformIncomeCheck dailyResultChannelIncomeCheck(@Param("yesLogday")String yesLogday, @Param("beforeYesLogday")String beforeYesLogday);


    @Select("<script>select product, uniqExact(device_id) as dau from ods.event_dist where product in (" +
            "<foreach collection='productList' separator=',' item='product'> " +
            "#{product} " +
            "</foreach> " +
            ") and os ='android' and logday = #{logday} group by product</script>")
    List<ProductDau> getProductDauList(@Param("productList") List<String> productList, @Param("logday") String logday);

    @Select("select concat(od.product,'-',od.channel,'-',od.sdk_version) as product,od.product as product_code,pro.product_name as product_name,pro.product_group as product_group\n" +
            "      ,od.os as os ,od.channel as channel,od.sdk_version as sdk_version\n" +
            "      ,dau,pv,pv_bid, pv-pv_bid as today_wookmark_pv\n" +
            "      ,if(pv<=0,0,floor(100*(pv-pv_bid)/pv,2)) as today_wookmark_pv_prop\n" +
            "from\n" +
            "(select product,os,channel,sdk_version,count(distinct device_id) as dau from ods.event_dist\n" +
            "where logday = today() and product != '' and sdk_version != ''\n" +
            "group by product,os,channel,sdk_version having dau >= 1000\n" +
            ") as od\n" +
            "left join (\n" +
            "    select product,os,channel,sdk_version\n" +
            "         ,count() as pv\n" +
            "        ,sum(if(toInt64(bid.del_flag)=1,toInt64(1),0)) as pv_bid\n" +
            "    from ods.event_exposure_dist as od\n" +
            "    global left join (\n" +
            "        select ad_id,del_flag from dwd.tb_ap_bidding_dist\n" +
            "        group by ad_id,del_flag\n" +
            "    )  as bid on toString(bid.ad_id)=od.ad_id\n" +
            "    where logday = today() and product != '' and sdk_version != ''\n" +
            "    and ad_action = 'exposure' and left(ad_type,4) in ('1099','1018','1015','1008')\n" +
            "    -- and bid.del_flag = 1\n" +
            "    and ad_id global in (select ad_id from dwd.product_ad_conf_dist where type_name = '视频' group by ad_id)\n" +
            "    group by product,os,channel,sdk_version\n" +
            ") as bid on od.product = bid.product and od.os = bid.os and od.channel = bid.channel\n" +
            "        and od.sdk_version = bid.sdk_version\n" +
            "left join dwd.product_map_dist as pro on pro.product = od.product\n" +
            "where ((today_wookmark_pv_prop < 20 and channel not like 'bd%') or (today_wookmark_pv_prop <= 10 and channel like 'bd%') )\n" +
            " and product not in ('dcdy-oppo1220-2.0.6.1.9.6','qgcz-oppo-2.0.6.1.9.4','qzljyk-qzljykhuawei-2.0.6.1.9.4','qzqssk-qzqsskhuawei-2.0.6.1.9.4','kcynf-kssxhkcynfjh-2.0.6.1.9.5'\n" +
            "                     ,'qzqssk-qzqsskvivo-2.0.6.1.9.4','qzqssk-qzqsskxiaomi-2.0.6.1.9.4','kcynf-csjjlkcynf12d-2.0.6.1.9.5')\n" +
            " and od.channel != 'ksmin'\n" +
            " and od.channel != 'cbdpzds12b'\n" +
            "order by dau desc\n" +
            ";")
    List<WookmarkPvBean> wookmarkScreenPv();


    @Select("select company_name,ifNull(all_cost,0) as all_cost,total_valid_grants,cost_ad_counts\n" +
            "from (\n" +
            "         select ad.customer_id as customer_id,cus.company_name as company_name,sum(cost_son) as all_cost\n" +
            "              ,sum(rebate_cost_son) as rebate_costs,max(cash.valid_grants) as total_valid_grants\n" +
            "              ,count(distinct process.advertiser_id) as cost_ad_counts,max(cash.ad_all_counts) as ad_all_counts2\n" +
            "                ,total_valid_grants/ad_all_counts2 as avg_grants\n" +
            "        from ods_mysql.auth_toutiao_customer as cus\n" +
            "        left join ods_mysql.auth_toutiao_advertiser_today as ad on ad.customer_id = cus.id\n" +
            "        left join (\n" +
            "                select advertiser_id,sum(cost) as cost_son,sum(rebate_cost) as rebate_cost_son\n" +
            "                 from ads.toutiao_report_tf_new_ad_process\n" +
            "                where logday between  yesterday() - 2 and yesterday()\n" +
            "                group by advertiser_id\n" +
            "            )\n" +
            "             as process on process.advertiser_id = toString(ad.advertiser_id)\n" +
            "        left join(\n" +
            "                select  ad2.customer_id as customer_id2\n" +
            "                        ,sum(valid_grant2) as valid_grants,count(distinct cash.advertiser_id) as ad_all_counts\n" +
            "                from (\n" +
            "                    select advertiser_id,argMax(valid_grant,update_time) as  valid_grant2\n" +
            "                    from ods_mysql.toutiao_report_cash_all\n" +
            "                    group by advertiser_id\n" +
            "                )\n" +
            "                as cash\n" +
            "                left join ods_mysql.auth_toutiao_advertiser_today as ad2 on cash.advertiser_id = toString(ad2.advertiser_id)\n" +
            "                left join ods_mysql.auth_toutiao_customer as cus2 on ad2.customer_id = cus2.id\n" +
            "                -- where advertiser_id in ('1826374323325002','1826371035194908','1825927724902731')\n" +
            "                group by ad2.customer_id\n" +
            "        )\n" +
            "        as cash on cash.customer_id2 = ad.customer_id\n" +
            "        where  cus.del_flag = 0\n" +
            "        group by ad.customer_id,cus.company_name having max(cash.valid_grants) is not NULL and max(cash.valid_grants) > 0\n" +
            "        -- order by total_valid_grants desc\n" +
            ")\n" +
            "where all_cost <= 10000 and total_valid_grants > 100000 and company_name not in ('海南耀天-黄历','海南邵赢-授权3','北京酷赚技术有限公司','海南耀天-授权3','海南耀世','北京臻盛-授权2','北京心诚致远网络技术有限公司')\n" +
            "    -- and arrayExists( x -> x= yesterday() - 2,logdays) = 1\n" +
            ";")
    List<ToutiaoCompanyCostBean> checkTTCost();


    //当日截至目前新增用户及收入 同比环比
    @Select({"select today()                                                                 AS logday,\n" +
            "       toHour(now()) - 1                                                       AS hour,\n" +
            "       t1.os                                                                   AS os,\n" +
            "       max(t1.new_au_today)                                                    AS new_au_today,\n" +
            "       round(max(t1.new_au_income_today), 2)                                   AS new_au_income_today,\n" +
            "       max(t1.new_au_today_hour)                                               AS new_au_today_hour,\n" +
            "       max(t1.new_au_yes)                                                      AS new_au_yes,\n" +
            "       round(max(t1.new_au_income_yes), 2)                                     AS new_au_income_yes,\n" +
            "       max(t1.new_au_yes_hour)                                                 AS new_au_yes_hour,\n" +
            "       round((new_au_today - new_au_yes) * 100 / new_au_yes, 2)                AS new_au_rate_yes,\n" +
            "       round((new_au_income_today - new_au_income_yes) * 100 / new_au_income_yes,\n" +
            "             2)                                                                AS new_au_income_rate_yes,\n" +
            "       round((new_au_today_hour - new_au_yes_hour) * 100 / new_au_yes_hour, 2) AS new_au_rate_yes_hour\n" +
            "from (\n" +
            "         select a.os,\n" +
            "                a.dau_today               as new_au_today,\n" +
            "                a.income_today            as new_au_income_today,\n" +
            "                a.dau_today - b.dau_today as new_au_today_hour,\n" +
            "                0                         as new_au_yes,\n" +
            "                0                         as new_au_income_yes,\n" +
            "                0                         as new_au_yes_hour\n" +
            "         from (\n" +
            "                  select os, sum(dau) as dau_today, sum(income) as income_today\n" +
            "                  from ads.realtime_result\n" +
            "                  where logday = today()\n" +
            "                    and os in ('android', 'ios')\n" +
            "                    and hour = toInt16(toHour(now()) - 1)\n" +
            "                    and key_name = 'user_new'\n" +
            "                  group by os\n" +
            "                  ) a\n" +
            "                  left join\n" +
            "              (\n" +
            "                  select os,\n" +
            "                         sum(dau)    as dau_today,\n" +
            "                         sum(income) as income_today\n" +
            "                  from ads.realtime_result\n" +
            "                  where logday = today()\n" +
            "                    and os in ('android', 'ios')\n" +
            "                    and hour = toInt16(toHour(now()) - 2)\n" +
            "                    and key_name = 'user_new'\n" +
            "                  group by os\n" +
            "                  ) b\n" +
            "              on a.os = b.os\n" +
            "         union all\n" +
            "         select a.os,\n" +
            "                0                     as new_au_today,\n" +
            "                0                     as new_au_income_today,\n" +
            "                0                     as new_au_today_hour,\n" +
            "                a.dau_yes             as new_au_yes,\n" +
            "                a.income_yes          as new_au_income_yes,\n" +
            "                a.dau_yes - b.dau_yes as new_au_yes_hour\n" +
            "         from (\n" +
            "                  select os, sum(dau) as dau_yes, sum(income) as income_yes\n" +
            "                  from ads.realtime_result\n" +
            "                  where logday = yesterday()\n" +
            "                    and os in ('android', 'ios')\n" +
            "                    and hour = toInt16(toHour(now()) - 1)\n" +
            "                    and key_name = 'user_new'\n" +
            "                  group by os\n" +
            "                  ) a\n" +
            "                  left join\n" +
            "              (\n" +
            "                  select os, sum(dau) as dau_yes, sum(income) as income_yes\n" +
            "                  from ads.realtime_result\n" +
            "                  where logday = yesterday()\n" +
            "                    and os in ('android', 'ios')\n" +
            "                    and hour = toInt16(toHour(now()) - 2)\n" +
            "                    and key_name = 'user_new'\n" +
            "                  group by os\n" +
            "                  ) b\n" +
            "              on a.os = b.os) t1\n" +
            "group by t1.os\n" +
            "having new_au_today_hour >0 and  (new_au_rate_yes >= 5\n" +
            "    or new_au_rate_yes <= -5\n" +
            "    or new_au_income_rate_yes >= 5\n" +
            "    or new_au_income_rate_yes = -5\n" +
            "    or new_au_rate_yes_hour >= 5\n" +
            "    or new_au_rate_yes_hour <= -5);"})
    List<TodayNewAuIncomeBean> queryTodayNewAuIncome();


    //当日截至目前新增用户及收入 同比环比
    @Select({"select today()                                                  AS logday,\n" +
            "       toHour(now()) - 1                                        AS hour,\n" +
            "       t1.os                                                    AS os,\n" +
            "       max(t1.old_au_today)                                     AS old_au_today,\n" +
            "       round(max(t1.old_au_income_today), 2)                    AS old_au_income_today,\n" +
            "       round(max(t1.old_au_income_today_hour), 2)               AS old_au_income_today_hour,\n" +
            "       max(t1.old_au_yes)                                       AS old_au_yes,\n" +
            "       round(max(t1.old_au_income_yes), 2)                      AS old_au_income_yes,\n" +
            "       round(max(t1.old_au_income_yes_hour), 2)                 AS old_au_income_yes_hour,\n" +
            "       round((old_au_today - old_au_yes) * 100 / old_au_yes, 2) AS old_au_rate_yes,\n" +
            "       round((old_au_income_today - old_au_income_yes) * 100 / old_au_income_yes,\n" +
            "             2)                                                 AS old_au_income_rate_yes,\n" +
            "       round((old_au_income_today_hour - old_au_income_yes_hour) * 100 / old_au_income_yes_hour,\n" +
            "             2)                                                 AS old_au_income_rate_yes_hour\n" +
            "from (\n" +
            "         select a.os,\n" +
            "                a.dau_today               as old_au_today,\n" +
            "                a.income_today            as old_au_income_today,\n" +
            "                a.income_today - b.income_today as old_au_income_today_hour,\n" +
            "                0                         as old_au_yes,\n" +
            "                0                         as old_au_income_yes,\n" +
            "                0                         as old_au_income_yes_hour\n" +
            "         from (\n" +
            "                  select os, sum(dau) as dau_today, sum(income) as income_today\n" +
            "                  from ads.realtime_result\n" +
            "                  where logday = today()\n" +
            "                    and os in ('android', 'ios')\n" +
            "                    and hour = toInt16(toHour(now()) - 1)\n" +
            "                    and key_name = 'user_old'\n" +
            "                  group by os\n" +
            "                  ) a\n" +
            "                  left join\n" +
            "              (\n" +
            "                  select os,\n" +
            "                         sum(dau)    as dau_today,\n" +
            "                         sum(income) as income_today\n" +
            "                  from ads.realtime_result\n" +
            "                  where logday = today()\n" +
            "                    and os in ('android', 'ios')\n" +
            "                    and hour = toInt16(toHour(now()) - 2)\n" +
            "                    and key_name = 'user_old'\n" +
            "                  group by os\n" +
            "                  ) b\n" +
            "              on a.os = b.os\n" +
            "         union all\n" +
            "         select a.os,\n" +
            "                0                     as old_au_today,\n" +
            "                0                     as old_au_income_today,\n" +
            "                0                     as old_au_income_today_hour,\n" +
            "                a.dau_yes             as old_au_yes,\n" +
            "                a.income_yes          as old_au_income_yes,\n" +
            "                a.income_yes - b.income_yes as old_au_income_yes_hour\n" +
            "         from (\n" +
            "                  select os, sum(dau) as dau_yes, sum(income) as income_yes\n" +
            "                  from ads.realtime_result\n" +
            "                  where logday = yesterday()\n" +
            "                    and os in ('android', 'ios')\n" +
            "                    and hour = toInt16(toHour(now()) - 1)\n" +
            "                    and key_name = 'user_old'\n" +
            "                  group by os\n" +
            "                  ) a\n" +
            "                  left join\n" +
            "              (\n" +
            "                  select os, sum(dau) as dau_yes, sum(income) as income_yes\n" +
            "                  from ads.realtime_result\n" +
            "                  where logday = yesterday()\n" +
            "                    and os in ('android', 'ios')\n" +
            "                    and hour = toInt16(toHour(now()) - 2)\n" +
            "                    and key_name = 'user_old'\n" +
            "                  group by os\n" +
            "                  ) b\n" +
            "              on a.os = b.os) t1\n" +
            "group by  t1.os\n" +
            "having old_au_today > 0 and (old_au_rate_yes >= 5\n" +
            "    or old_au_rate_yes <= -5\n" +
            "    or old_au_income_rate_yes >= 5\n" +
            "    or old_au_income_rate_yes <= -5\n" +
            "    or old_au_income_rate_yes_hour >= 5\n" +
            "    or old_au_income_rate_yes_hour <= -5);"})
    List<TodayOldAuIncomeBean> queryTodayOldAuIncome();


    //昨日截至24时目前新增用户及收入 同比环比
    @Select({"select today() - 1                                                             AS logday,\n" +
            "       23                                                                      AS hour,\n" +
            "       t1.os                                                                   AS os,\n" +
            "       max(t1.new_au_today)                                                    AS new_au_today,\n" +
            "       round(max(t1.new_au_income_today), 2)                                   AS new_au_income_today,\n" +
            "       max(t1.new_au_today_hour)                                               AS new_au_today_hour,\n" +
            "       max(t1.new_au_yes)                                                      AS new_au_yes,\n" +
            "       round(max(t1.new_au_income_yes), 2)                                     AS new_au_income_yes,\n" +
            "       max(t1.new_au_yes_hour)                                                 AS new_au_yes_hour,\n" +
            "       round((new_au_today - new_au_yes) * 100 / new_au_yes, 2)                AS new_au_rate_yes,\n" +
            "       round((new_au_income_today - new_au_income_yes) * 100 / new_au_income_yes,\n" +
            "             2)                                                                AS new_au_income_rate_yes,\n" +
            "       round((new_au_today_hour - new_au_yes_hour) * 100 / new_au_yes_hour, 2) AS new_au_rate_yes_hour\n" +
            "from (\n" +
            "         select a.os,\n" +
            "                a.dau_today               as new_au_today,\n" +
            "                a.income_today            as new_au_income_today,\n" +
            "                a.dau_today - b.dau_today as new_au_today_hour,\n" +
            "                0                         as new_au_yes,\n" +
            "                0                         as new_au_income_yes,\n" +
            "                0                         as new_au_yes_hour\n" +
            "         from (\n" +
            "                  select os, sum(dau) as dau_today, sum(income) as income_today\n" +
            "                  from ads.realtime_result\n" +
            "                  where logday = today() - 1\n" +
            "                    and os in ('android', 'ios')\n" +
            "                    and hour = 23\n" +
            "                    and key_name = 'user_new'\n" +
            "                  group by os\n" +
            "                  ) a\n" +
            "                  left join\n" +
            "              (\n" +
            "                  select os,\n" +
            "                         sum(dau)    as dau_today,\n" +
            "                         sum(income) as income_today\n" +
            "                  from ads.realtime_result\n" +
            "                  where logday = today() - 1\n" +
            "                    and os in ('android', 'ios')\n" +
            "                    and hour = 22\n" +
            "                    and key_name = 'user_new'\n" +
            "                  group by os\n" +
            "                  ) b\n" +
            "              on a.os = b.os\n" +
            "         union all\n" +
            "         select a.os,\n" +
            "                0                     as new_au_today,\n" +
            "                0                     as new_au_income_today,\n" +
            "                0                     as new_au_today_hour,\n" +
            "                a.dau_yes             as new_au_yes,\n" +
            "                a.income_yes          as new_au_income_yes,\n" +
            "                a.dau_yes - b.dau_yes as new_au_yes_hour\n" +
            "         from (\n" +
            "                  select os, sum(dau) as dau_yes, sum(income) as income_yes\n" +
            "                  from ads.realtime_result\n" +
            "                  where logday = yesterday() - 1\n" +
            "                    and os in ('android', 'ios')\n" +
            "                    and hour = 23\n" +
            "                    and key_name = 'user_new'\n" +
            "                  group by os\n" +
            "                  ) a\n" +
            "                  left join\n" +
            "              (\n" +
            "                  select os, sum(dau) as dau_yes, sum(income) as income_yes\n" +
            "                  from ads.realtime_result\n" +
            "                  where logday = yesterday() - 1\n" +
            "                    and os in ('android', 'ios')\n" +
            "                    and hour = 22\n" +
            "                    and key_name = 'user_new'\n" +
            "                  group by os\n" +
            "                  ) b\n" +
            "              on a.os = b.os) t1\n" +
            "group by t1.os\n" +
            "having new_au_today_hour >0 and  (new_au_rate_yes >= 5\n" +
            "    or new_au_rate_yes <= -5\n" +
            "    or new_au_income_rate_yes >= 5\n" +
            "    or new_au_income_rate_yes = -5\n" +
            "    or new_au_rate_yes_hour >= 5\n" +
            "    or new_au_rate_yes_hour <= -5);"})
    List<TodayNewAuIncomeBean> queryYesNewAuIncome();


    //昨日截至24时新增用户及收入 同比环比
    @Select({"select today()-1                                               AS logday,\n" +
            "      23                                                        AS hour,\n" +
            "       t1.os                                                    AS os,\n" +
            "       max(t1.old_au_today)                                     AS old_au_today,\n" +
            "       round(max(t1.old_au_income_today), 2)                    AS old_au_income_today,\n" +
            "       round(max(t1.old_au_income_today_hour), 2)               AS old_au_income_today_hour,\n" +
            "       max(t1.old_au_yes)                                       AS old_au_yes,\n" +
            "       round(max(t1.old_au_income_yes), 2)                      AS old_au_income_yes,\n" +
            "       round(max(t1.old_au_income_yes_hour), 2)                 AS old_au_income_yes_hour,\n" +
            "       round((old_au_today - old_au_yes) * 100 / old_au_yes, 2) AS old_au_rate_yes,\n" +
            "       round((old_au_income_today - old_au_income_yes) * 100 / old_au_income_yes,\n" +
            "             2)                                                 AS old_au_income_rate_yes,\n" +
            "       round((old_au_income_today_hour - old_au_income_yes_hour) * 100 / old_au_income_yes_hour,\n" +
            "             2)                                                 AS old_au_income_rate_yes_hour\n" +
            "from (\n" +
            "         select a.os,\n" +
            "                a.dau_today               as old_au_today,\n" +
            "                a.income_today            as old_au_income_today,\n" +
            "                a.income_today - b.income_today as old_au_income_today_hour,\n" +
            "                0                         as old_au_yes,\n" +
            "                0                         as old_au_income_yes,\n" +
            "                0                         as old_au_income_yes_hour\n" +
            "         from (\n" +
            "                  select os, sum(dau) as dau_today, sum(income) as income_today\n" +
            "                  from ads.realtime_result\n" +
            "                  where logday = today()-1\n" +
            "                    and os in ('android', 'ios')\n" +
            "                    and hour = 23\n" +
            "                    and key_name = 'user_old'\n" +
            "                  group by os\n" +
            "                  ) a\n" +
            "                  left join\n" +
            "              (\n" +
            "                  select os,\n" +
            "                         sum(dau)    as dau_today,\n" +
            "                         sum(income) as income_today\n" +
            "                  from ads.realtime_result\n" +
            "                  where logday = today()-1\n" +
            "                    and os in ('android', 'ios')\n" +
            "                    and hour = 22\n" +
            "                    and key_name = 'user_old'\n" +
            "                  group by os\n" +
            "                  ) b\n" +
            "              on a.os = b.os\n" +
            "         union all\n" +
            "         select a.os,\n" +
            "                0                     as old_au_today,\n" +
            "                0                     as old_au_income_today,\n" +
            "                0                     as old_au_income_today_hour,\n" +
            "                a.dau_yes             as old_au_yes,\n" +
            "                a.income_yes          as old_au_income_yes,\n" +
            "                a.income_yes - b.income_yes as old_au_income_yes_hour\n" +
            "         from (\n" +
            "                  select os, sum(dau) as dau_yes, sum(income) as income_yes\n" +
            "                  from ads.realtime_result\n" +
            "                  where logday = yesterday() -1\n" +
            "                    and os in ('android', 'ios')\n" +
            "                    and hour = 23\n" +
            "                    and key_name = 'user_old'\n" +
            "                  group by os\n" +
            "                  ) a\n" +
            "                  left join\n" +
            "              (\n" +
            "                  select os, sum(dau) as dau_yes, sum(income) as income_yes\n" +
            "                  from ads.realtime_result\n" +
            "                  where logday = yesterday()-1\n" +
            "                    and os in ('android', 'ios')\n" +
            "                    and hour = 22\n" +
            "                    and key_name = 'user_old'\n" +
            "                  group by os\n" +
            "                  ) b\n" +
            "              on a.os = b.os) t1\n" +
            "group by t1.os\n" +
            "having old_au_today > 0 and (old_au_rate_yes >= 5\n" +
            "    or old_au_rate_yes <= -5\n" +
            "    or old_au_income_rate_yes >= 5\n" +
            "    or old_au_income_rate_yes <= -5\n" +
            "    or old_au_income_rate_yes_hour >= 5\n" +
            "    or old_au_income_rate_yes_hour <= -5);"})
    List<TodayOldAuIncomeBean> queryYesOldAuIncome();


}
