package com.shinet.core.alert.dsp.service;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.shinet.core.alert.coreservice.entity.AlertRecord;
import com.shinet.core.alert.coreservice.enums.AlertModel;
import com.shinet.core.alert.coreservice.enums.AlertStatus;
import com.shinet.core.alert.coreservice.enums.AlertType;
import com.shinet.core.alert.coreservice.service.AlertRecordService;
import com.shinet.core.alert.dingtalk.DingTailService;
import com.shinet.core.alert.dingtalk.VedioAlertService;
import com.shinet.core.alert.dsp.entity.UserEvent;
import com.shinet.core.alert.dsp.entity.VideoPlanConfigMaterial;
import com.shinet.core.alert.dsp.mapper.VideoPlanConfigMaterialMapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.shinet.core.alert.util.DateUtils;
import com.xxl.job.core.log.XxlJobLogger;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;

/**
* <p>
    *  服务实现类
    * </p>
*
* <AUTHOR>
* @since 2022-09-21
*/
@Service
public class VideoPlanConfigMaterialService extends ServiceImpl<VideoPlanConfigMaterialMapper, VideoPlanConfigMaterial> {
    @Autowired
    AlertRecordService alertRecordService;
    public void alertVideoPlan(int maxRFnum){
        Date date = new Date();
        Date startDate = DateUtils.getDayBeginDate(date);
        QueryWrapper<VideoPlanConfigMaterial> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().ge(VideoPlanConfigMaterial::getCreateTime,startDate).eq(VideoPlanConfigMaterial::getUploadStatus,0);

        int initNum = countCd(0,startDate);

        int  failNum  = countCd(-1,startDate);
        int  runningNum  = countCd(2,startDate)+countCd(1,startDate);
        int allCompleteNum = countCd(3,startDate);


        if((initNum+runningNum+failNum)>maxRFnum){
            String alertMsg = "开始预警 视频上传积压 "+(initNum+runningNum+failNum)+" 阈值为："+maxRFnum+" 已上传 "+allCompleteNum;
            XxlJobLogger.log(alertMsg);

            AlertRecord alertRecord = alertRecordService.insertAlertRecord("alert-videoUpload","视频积压", AlertModel.VIDEOUPLOAD,
                    "视频积压异常 （video_plan_config_material）"+alertMsg, AlertStatus.INIT, DingTailService.dset, AlertType.DINGDING);
            DingTailService.sendMarkdownMsg(alertRecord);

            VedioAlertService.sendVocMsg("视频积压 ",alertMsg);
        }else {
            String alertMsg = "数据正常 视频上传积压 "+(initNum+runningNum+failNum)+" 阈值为："+maxRFnum+" 已上传 "+allCompleteNum;
            XxlJobLogger.log(alertMsg);
        }
    }

    private int countCd(int status,Date startDate){
        QueryWrapper<VideoPlanConfigMaterial> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().ge(VideoPlanConfigMaterial::getCreateTime,startDate).eq(VideoPlanConfigMaterial::getUploadStatus,status);
        return baseMapper.selectCount(queryWrapper);
    }
}
