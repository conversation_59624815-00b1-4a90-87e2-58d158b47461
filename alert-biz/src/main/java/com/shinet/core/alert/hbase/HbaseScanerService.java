package com.shinet.core.alert.hbase;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.shinet.core.alert.util.DateUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.hadoop.hbase.TableName;
import org.apache.hadoop.hbase.client.*;
import org.apache.hadoop.hbase.util.Bytes;
import org.joda.time.DateTimeConstants;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.io.IOException;
import java.util.Date;
import java.util.Iterator;
import java.util.LinkedList;

@Component
@Slf4j
public class HbaseScanerService {
    static String toutiaoClickLong = "ToutiaoClickLong";
    @Resource
    private Connection toutiaoClickConnection;
    public void scand(){
        try (Table table = toutiaoClickConnection.getTable(TableName.valueOf(toutiaoClickLong))) {
            long startTime = System.currentTimeMillis()- DateTimeConstants.MILLIS_PER_DAY;
            Scan scan = new Scan();
            scan.setMaxResultSize(100);
            scan.setColumnFamilyTimeRange(Bytes.toBytes("family"),startTime,System.currentTimeMillis());
            ResultScanner resultScanner = table.getScanner(scan);

            // ck:did:%s:%s:%s product, os
            scan.setStartRow(Bytes.toBytes("ck:did:\"yydxny\":\"android\""));
            Iterator<Result> iterator = resultScanner.iterator();
            iterator.forEachRemaining(result -> {
                System.out.println(new String(result.getRow()));
                String dstr = new String(result.getValue(Bytes.toBytes("family"), Bytes.toBytes("qualifier")));
                JSONObject jsonObject = JSON.parseObject(dstr);
                String dstrf = jsonObject.getString("ts").replace("\"","");
                System.out.println(dstrf);
                Date date = new Date(Long.parseLong(dstrf));
                System.out.println(DateUtils.formatDate(date));
            });

            resultScanner.close();
        } catch (IOException e) {
            e.printStackTrace();
        }
    }
}
