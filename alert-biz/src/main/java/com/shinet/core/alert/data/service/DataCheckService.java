package com.shinet.core.alert.data.service;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.shinet.core.alert.coreservice.entity.AlertRecord;
import com.shinet.core.alert.coreservice.enums.AlertModel;
import com.shinet.core.alert.coreservice.enums.AlertStatus;
import com.shinet.core.alert.coreservice.enums.AlertType;
import com.shinet.core.alert.coreservice.service.AlertRecordService;
import com.shinet.core.alert.data.entity.DataCheck;
import com.shinet.core.alert.data.mapper.DataCheckMapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.shinet.core.alert.dingtalk.DingTailService;
import com.shinet.core.alert.dingtalk.VedioAlertService;
import com.shinet.core.alert.util.DateUtils;
import com.shinet.core.alert.util.DoubleUtil;
import com.xxl.job.core.log.XxlJobLogger;
import lombok.extern.slf4j.Slf4j;
import org.joda.time.DateTime;
import org.joda.time.DateTimeConstants;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;
import java.util.Map;

/**
* <p>
    *  服务实现类
    * </p>
*
* <AUTHOR>
* @since 2021-08-03
*/
@Service
@Slf4j
public class DataCheckService extends ServiceImpl<DataCheckMapper, DataCheck> {
    @Autowired
    AlertRecordService alertRecordService;
    public void expourseAlert(String jobName,double drate){
        Date date = new Date();
        DataCheck dataCheck = getDataCheckByDateHour(date);
        if(date.getMinutes()<=8){
            return;
        }
        if(dataCheck==null && date.getMinutes()>10){
            XxlJobLogger.log("已触发预警： 曝光数据异常，dataCheck悟空");
            AlertRecord alertRecord = alertRecordService.insertAlertRecord(jobName,"曝光数据异常", AlertModel.ADARPU,
                    "曝光数据异常，dataCheck为空", AlertStatus.INIT,DingTailService.dset, AlertType.DINGDING);
            DingTailService.sendMarkdownMsg(alertRecord);
        }
        if(dataCheck!=null){
            DataCheck dataCheckYestd = getDataCheckByDateHour(new Date(System.currentTimeMillis()- DateTimeConstants.MILLIS_PER_DAY));
            Long yesExposureNum = dataCheckYestd.getExposureNum()==null?0:dataCheckYestd.getExposureNum();
            Long yesEventNum =  dataCheckYestd.getEventNum()==null?0:dataCheckYestd.getEventNum();

            double minutesRate = date.getMinutes()/60d;
            double yesExposureLvNum = yesExposureNum * minutesRate;
            double yesEventLvNum = yesEventNum * minutesRate;

            double checkRate = DoubleUtil.getDoubleByTwo(Math.abs(1.0d-dataCheck.getExposureNum()/yesExposureLvNum*1.0d));
            String msg = "曝光数据异常，今："+dataCheck.getExposureNum()+"昨："+yesExposureNum+" 率："+checkRate;
            XxlJobLogger.log(msg);
            if(dataCheck.getExposureNum()<yesExposureLvNum && Math.abs(checkRate)>=drate){
                VedioAlertService.sendVocMsg(" 商业 ", msg);
                XxlJobLogger.log(" 商业 ", msg);

                AlertRecord alertRecord = alertRecordService.insertAlertRecord(jobName,"曝光数据异常", AlertModel.ADARPU,msg, AlertStatus.INIT,DingTailService.dset, AlertType.DINGPHONE);
                DingTailService.sendMarkdownMsg(alertRecord);
            }
            double crate = DoubleUtil.getDoubleByTwo(Math.abs((1.0d-dataCheck.getEventNum()/yesEventLvNum*1.0d)));
            String eventMsg = "核心事件异常，今："+dataCheck.getEventNum()+"昨："+yesEventLvNum+" 率："+crate;
            if(dataCheck.getEventNum()<yesEventLvNum && crate>=drate){
                VedioAlertService.sendVocMsg(" 商业 ", eventMsg);
                XxlJobLogger.log(eventMsg);
                AlertRecord alertRecord = alertRecordService.insertAlertRecord(jobName,"核心事件异常", AlertModel.ADARPU,eventMsg, AlertStatus.INIT,DingTailService.dset, AlertType.DINGPHONE);
                DingTailService.sendMarkdownMsg(alertRecord);
            }

        }
    }

    private DataCheck getDataCheckByDateHour( Date date){
        QueryWrapper<DataCheck> queryWrapper = new QueryWrapper<>();
        queryWrapper.select(" hour,sum(exposure_num) as exposure_num ,sum(event_num) as event_num ");
        queryWrapper.lambda().eq(DataCheck::getHour, date.getHours());
        queryWrapper.lambda().eq(DataCheck::getLogday, DateUtils.getDayBeginDate(date));
        queryWrapper.last("  GROUP BY `hour`  " );
        DataCheck dataCheck = getOne(queryWrapper,true);
        XxlJobLogger.log("获取"+DateUtils.getDayBeginDate(date)+" "+date.getHours()+" 的数据");
        return dataCheck;
    }

}
