package com.shinet.core.alert.clickhouse.config;

import com.alibaba.druid.pool.DruidDataSource;
import com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean;
import lombok.extern.slf4j.Slf4j;
import org.apache.ibatis.session.SqlSessionFactory;
import org.mybatis.spring.annotation.MapperScan;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.autoconfigure.condition.ConditionalOnBean;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.io.ClassPathResource;
import org.springframework.core.io.support.PathMatchingResourcePatternResolver;

import javax.sql.DataSource;
import java.sql.SQLException;

@Slf4j
@Configuration
@MapperScan(basePackages = {"com.shinet.core.alert.clickhouse.mapper.ck1"}, sqlSessionFactoryRef = "clickHouseSqlSessionFactory1")
public class Click1DataSourceConfig {


    @Value("${spring.click.datasource.username}")
    private String userName;

    @Value("${spring.click.datasource.password}")
    private String passWord;

    @Value("${spring.click.datasource.url}")
    private String url;

    @Value("${spring.click.datasource.driverClass}")
    private String driverClass;

    @Value("${spring.click.datasource.maxActive}")
    private Integer maxActive;

    @Value("${spring.click.datasource.maxWait}")
    private Integer maxWait;

    @Value("${spring.click.datasource.initialSize}")
    private Integer initialSize;

    @Value("${spring.click.datasource.minIdle}")
    private Integer minIdle;

    @Value("${spring.click.datasource.keepAlive}")
    private Boolean keepAlive;

    @Value("${spring.click.datasource.timeBetweenEvictionRunsMillis}")
    private Integer timeBetween;

    @Value("${spring.click.datasource.minEvictableIdleTimeMillis}")
    private Integer minEvictableIdle;


    @Value("${spring.click.datasource.removeAbandoned}")
    private Boolean removeAbandoned;

    @Value("${spring.click.datasource.removeAbandonedTime}")
    private Integer removeAbandonedTime;

    @Value("${spring.click.datasource.testWhileIdle}")
    private Boolean testWhileIdle;



    @Bean(name = "datasourceClickHouse1")
    public DataSource dataSource() throws SQLException{
        DruidDataSource druidDataSource = new DruidDataSource();
        druidDataSource.setUsername(userName);
        druidDataSource.setPassword(passWord);
        druidDataSource.setUrl(url);
        druidDataSource.setDriverClassName(driverClass);
        druidDataSource.setMaxActive(maxActive);
        // 配置从连接池获取连接等待超时的时间
        druidDataSource.setMaxWait(maxWait);
        druidDataSource.setInitialSize(initialSize);
        druidDataSource.setMinIdle(minIdle);
        // 配置间隔多久启动一次DestroyThread，对连接池内的连接才进行一次检测，单位是毫秒。
        // 检测时:1.如果连接空闲并且超过minIdle以外的连接，如果空闲时间超过minEvictableIdleTimeMillis设置的值则直接物理关闭。2.在minIdle以内的不处理。
        druidDataSource.setTimeBetweenEvictionRunsMillis(timeBetween);
        // 配置一个连接在池中最大空闲时间，单位是毫秒
        druidDataSource.setMinEvictableIdleTimeMillis(minEvictableIdle);
        // 设置从连接池获取连接时是否检查连接有效性，true时，如果连接空闲时间超过minEvictableIdleTimeMillis进行检查，否则不检查;false时，不检查
        druidDataSource.setTestWhileIdle(testWhileIdle);
        // 打开后，增强timeBetweenEvictionRunsMillis的周期性连接检查，minIdle内的空闲连接，每次检查强制验证连接有效性. 参考：https://github.com/alibaba/druid/wiki/KeepAlive_cn
        druidDataSource.setKeepAlive(keepAlive);
        // 连接泄露检查，打开removeAbandoned功能 , 连接从连接池借出后，长时间不归还，将触发强制回连接。回收周期随timeBetweenEvictionRunsMillis进行，如果连接为从连接池借出状态，并且未执行任何sql，并且从借出时间起已超过removeAbandonedTimeout时间，则强制归还连接到连接池中。
        druidDataSource.setRemoveAbandoned(removeAbandoned);
        // 超时时间，秒
        druidDataSource.setRemoveAbandonedTimeout(removeAbandonedTime);
        druidDataSource.init();
        return druidDataSource;
    }

    @Bean(name="clickHouseSqlSessionFactory1")
    @ConditionalOnBean(name = "datasourceClickHouse1")
    public SqlSessionFactory sqlSessionFactoryBean(@Qualifier("datasourceClickHouse1") DataSource dataSource) throws Exception {
        MybatisSqlSessionFactoryBean mybatisSqlSessionFactoryBean = new MybatisSqlSessionFactoryBean();
        mybatisSqlSessionFactoryBean.setDataSource(dataSource);
        mybatisSqlSessionFactoryBean.setConfigLocation(new ClassPathResource("config/mybatis_config.xml"));
        mybatisSqlSessionFactoryBean.setMapperLocations(new PathMatchingResourcePatternResolver().getResources("classpath*:mapper/ck1/*.xml"));
        return mybatisSqlSessionFactoryBean.getObject();
    }

}
