package com.shinet.core.alert.clickhouse.service;

import com.alibaba.fastjson.JSON;
import com.shinet.core.alert.clickhouse.entity.AdIncomePvEntity;
import com.shinet.core.alert.clickhouse.entity.RealtimeProductGap;
import com.shinet.core.alert.clickhouse.mapper.ck1.AdIncomePvQueryMapper;
import com.shinet.core.alert.coreservice.entity.AlertRecord;
import com.shinet.core.alert.coreservice.enums.*;
import com.shinet.core.alert.coreservice.service.AlertDelayJobService;
import com.shinet.core.alert.coreservice.service.AlertRecordService;
import com.shinet.core.alert.dingtalk.DingTailService;
import com.shinet.core.alert.dingtalk.VedioAlertService;
import com.shinet.core.alert.util.DateUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.directory.api.util.Strings;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2022/9/13
 */
@Slf4j
@Service
public class AdIncomePvGapCheckService {

    @Resource
    private AdIncomePvQueryMapper adIncomePvQueryMapper;
    @Autowired
    private AlertRecordService alertRecordService;
    @Autowired
    private AlertDelayJobService alertDelayJobService;

    public void checkProductAndPosId(){
        Date now = new Date();
        if (now.getHours() > 12 || now.getHours() < 10){
            return;
        }
        Date yesterday = DateUtils.addTime(now,-1000 * 60 * 60 *24);
        String logday = DateUtils.formatDateForYMD(yesterday);
        // 查询平台收入是否已出完整
        List<String> queryPlatformList = new ArrayList<>();
        int checkR1 = adIncomePvQueryMapper.checkIncome("cost","toutiao_ad_report_data_code","stat_datetime",logday);
        if (checkR1 == 1){
            queryPlatformList.add("chuanshanjia");
        }

        checkR1 = adIncomePvQueryMapper.checkIncomeGdt(logday);
        if (checkR1 == 1){
            queryPlatformList.add("guangdiantong");
        }

        checkR1 = adIncomePvQueryMapper.checkIncome("share","kuaishou_report_data_ad","date",logday);
        if (checkR1 == 1){
            queryPlatformList.add("kuaishou");
        }

        checkR1 = adIncomePvQueryMapper.checkIncome("income","vivo_report_data_ad","date",logday);
        if (checkR1 == 1){
            queryPlatformList.add("vivo");
        }
        checkR1 = adIncomePvQueryMapper.checkIncome("income","oppo_report_data_ad","time",logday);
        if (checkR1 == 1){
            queryPlatformList.add("oppo");
        }

        checkR1 = adIncomePvQueryMapper.checkIncome("income","baidu_report_data_ad","data_date",logday);
        if (checkR1 == 1){
            queryPlatformList.add("baidu");
        }

        checkR1 = adIncomePvQueryMapper.checkIncome("revenue","sigmob_report_data_ad","date",logday);
        if (checkR1 == 1){
            queryPlatformList.add("sigmob");
        }
        checkR1 = adIncomePvQueryMapper.checkIncomeOds("revenue","aqy_report_data_ad","logday",logday);
        if (checkR1 == 1){
            queryPlatformList.add("aqy");
        }

        // 查询产品维度
        List<AdIncomePvEntity> productGapList = adIncomePvQueryMapper.queryProductGap(logday,queryPlatformList);
        if (productGapList != null && productGapList.size() > 0){
            buildAndSend(productGapList,true);
        }

        Map<String,String> posIdMap = adIncomePvQueryMapper.queryPosIdMap(logday)
                .stream()
                .filter(r->StringUtils.isNotEmpty(r.getPosName()) && StringUtils.isNotEmpty(r.getPosId()))
                .collect(Collectors.toMap(AdIncomePvEntity::getPosId,AdIncomePvEntity::getPosName,(r1,r2)->r1));
        // 查询posId维度
        List<AdIncomePvEntity> queryPosIdGap = adIncomePvQueryMapper.queryPosIdGap(logday,queryPlatformList);
        queryPosIdGap = queryPosIdGap.stream().filter(r -> {
            String posName = posIdMap.get(r.getPosId());
            if (posName == null){
                return true;
            }
            if (posName.toLowerCase().contains("bidding")){
                return true;
            }
            return !posName.endsWith("-0") && !posName.endsWith("激励000") && !posName.endsWith("插屏000") && !posName.endsWith(")000");
        }).collect(Collectors.toList());
        long pageSize = 15;
        if (queryPosIdGap.size() > 0){
            for (int page = 0; page <= queryPosIdGap.size()/pageSize; page++){
                buildAndSend(queryPosIdGap.stream().skip(page *pageSize).limit(pageSize).collect(Collectors.toList()), false);
            }
        }

        // 查询收入占比
        List<AdIncomePvEntity> queryIncomeProduct = adIncomePvQueryMapper.queryProductGapMorethan10WDau(logday,queryPlatformList);
        if (queryIncomeProduct != null && queryIncomeProduct.size() > 0){
            buildAndSendIncome(queryIncomeProduct,false,false);

        }

        // 平台纬度
        List<AdIncomePvEntity> queryIncomePlatform = adIncomePvQueryMapper.queryPlatformGap(logday,queryPlatformList);
        if (queryIncomePlatform != null && queryIncomePlatform.size() > 0){
            buildAndSendIncome(queryIncomePlatform,false,true);
        }

        List<String> dontQueryList = Arrays.asList("vivo","oppo","baidu");
        queryPlatformList = queryPlatformList.stream().filter(r -> !dontQueryList.contains(r)).collect(Collectors.toList());
        List<AdIncomePvEntity> queryIncomeProduct5k = adIncomePvQueryMapper.queryIncomeOver5KGap5(logday,queryPlatformList);
        if (queryIncomeProduct5k != null && queryIncomeProduct5k.size() > 0){
            buildAndSendIncome(queryIncomeProduct5k,true,false);
        }
    }

    private void buildAndSendIncome(List<AdIncomePvEntity> list,Boolean productV,Boolean sendPhone){
        String alert = list.stream()
                .filter(r -> alertDelayJobService.canSend(AlertJobDelayModel.AD_PV_GAP.getJobId()+"_"+r.getProduct()))
                .filter(r -> r.getIncome() > 0 && r.getIncomeThird() > 0)
                .map(adIncomePvEntity -> {
                    String desc = "";
                    if (productV){
                        desc = String.format("> %s(%s) os:%s \n\n" +
                                        " >> 我方->PV:%s In:**%s** \n\n" +
                                        " >> 三方->PV:%s In:**%s** \n\n" +
                                        " >> GAP->PV:**%s**%% In:**%s**%%",
                                adIncomePvEntity.getProductName(),
                                adIncomePvEntity.getProduct(),
                                adIncomePvEntity.getOs(),
                                adIncomePvEntity.getPv(),
                                Math.floor(adIncomePvEntity.getIncome()),
                                adIncomePvEntity.getPvThird(),
                                Math.floor(adIncomePvEntity.getIncomeThird()),
                                Math.round(adIncomePvEntity.getPvRate() * 100)/100,
                                Math.round(adIncomePvEntity.getIncomeRate() * 100)/100
                        );
                    }else {
                        String pr = adIncomePvEntity.getProduct();
                        String os = adIncomePvEntity.getOs();
                        if (Strings.isEmpty(pr)){
                            pr = adIncomePvEntity.getPlatform();
                            os = pr;
                        }
                        desc = String.format("> %s %s\n\n" +
                                        " >> 我方In:**%s** \n\n" +
                                        " >> 三方In:**%s** 差异过大",
                                pr,
                                os,
                                Math.floor(adIncomePvEntity.getIncome()),
                                Math.floor(adIncomePvEntity.getIncomeThird()));
                    }
                    return desc;
                }).collect(Collectors.joining("\n\n"));

        if (StringUtils.isEmpty(alert)){
            return;
        }

        Set<String> pr = list.stream().map(AdIncomePvEntity::getProduct).collect(Collectors.toSet());
        AlertRecord alertRecord = alertRecordService.insertAlertRecord(AlertJobDelayModel.AD_PV_GAP.getJobId(),
                AlertJobDelayModel.AD_PV_GAP.getJobName(), AlertModel.ADPVGAP, alert
                , AlertStatus.INIT, DingTailService.bsData, AlertType.DINGPHONE);

        DingTailService.sendMarkdownMsgDelay(alertRecord, AlertRoute.BUSINESS.value,
                AlertJobDelayModel.AD_PV_GAP,new ArrayList<>(pr));

        if (sendPhone) {
            VedioAlertService.sendVocMsg("商业数据", alert, DingTailService.dset);
        }
    }

    private void buildAndSend(List<AdIncomePvEntity> list,Boolean productV){
        String alert = list.stream()
                .filter(r -> alertDelayJobService.canSend(AlertJobDelayModel.AD_PV_GAP.getJobId()+"_"+r.getProduct()))
                .map(adIncomePvEntity -> {
                    String desc = "";
                    if (productV){
                        desc = String.format("%s(%s)- %s,我方PV %s 三方PV %s 三方收入%s 差异过大",
                                adIncomePvEntity.getProductName(),
                                adIncomePvEntity.getProduct(),
                                adIncomePvEntity.getOs(),
                                adIncomePvEntity.getPv(),
                                adIncomePvEntity.getPvThird(),
                                adIncomePvEntity.getIncome());
                    }else {
                        desc = String.format("> %s(%s),PosId:%s \n\n" +
                                        " >> 我方->PV:%s In:**%s** \n\n" +
                                        " >> 三方->PV:%s In:**%s** 差异过大",
                                adIncomePvEntity.getProductName(),
                                adIncomePvEntity.getProduct(),
                                adIncomePvEntity.getPosId(),
                                adIncomePvEntity.getPv(),
                                adIncomePvEntity.getIncome(),
                                adIncomePvEntity.getPvThird(),
                                Math.floor(adIncomePvEntity.getIncomeThird()));
                    }
                    return desc;
                }).collect(Collectors.joining("\n\n"));

        if (StringUtils.isEmpty(alert)){
            return;
        }

        Set<String> pr = list.stream().map(AdIncomePvEntity::getProduct).collect(Collectors.toSet());
        AlertRecord alertRecord = alertRecordService.insertAlertRecord(AlertJobDelayModel.AD_PV_GAP.getJobId(),
                AlertJobDelayModel.AD_PV_GAP.getJobName(), AlertModel.ADPVGAP, alert
                , AlertStatus.INIT, DingTailService.bsData, AlertType.DINGPHONE);
        if (productV) {
            DingTailService.sendMarkdownMsg(alertRecord);
            VedioAlertService.sendVocMsg("商业数据",alert,DingTailService.dset);
        }
        DingTailService.sendMarkdownMsgDelay(alertRecord,
                AlertRoute.BUSINESS.value,
                AlertJobDelayModel.AD_PV_GAP,new ArrayList<>(pr));
    }

    public void checkProductGap(){
        log.info(">>> 开始[CSJ/GDT]实时数据核对...");
        // 先查询广点通
//        List<RealtimeProductGap> gdtGapProductList = adIncomePvQueryMapper.queryTodayGdtGap();
//        gdtGapProductList = checkNeedSend(gdtGapProductList);
//        buildAndSend(gdtGapProductList,"GDT");
        // 穿山甲
//        List<RealtimeProductGap> csjGapProductList = adIncomePvQueryMapper.queryTodayCsjGap();
//        csjGapProductList = checkNeedSend(csjGapProductList);
//        buildAndSend(csjGapProductList,"CSJ");
        Date now = new Date();
        if (now.getHours() < 2){
            return;
        }
        checkProductRealtime();
        checkPosIdRealtime();
        log.info(">>> 完成[CSJ/GDT]实时数据核对...");
    }


    // 查看实时产品维度
    private void checkProductRealtime(){
        List<AdIncomePvEntity> productGap = adIncomePvQueryMapper.queryRealtimeProductGap();
        if (productGap != null && productGap.size() > 0){
            buildAndSendSLA(productGap,true);
        }
    }

    // 查看实时广告位维度
    private void checkPosIdRealtime(){
        List<AdIncomePvEntity> posGap = adIncomePvQueryMapper.queryRealtimePosIdGap();
        if (posGap != null && posGap.size() > 0){
            buildAndSendSLA(posGap,false);
        }
    }

    private List<RealtimeProductGap> checkNeedSend(List<RealtimeProductGap> targetList){
        return targetList.stream()
                .filter(realtimeProductGap -> Math.abs(realtimeProductGap.getRateEcpm()) > 70 ||
                        Math.abs(realtimeProductGap.getRateIncome()) > 70 ||
                        Math.abs(realtimeProductGap.getRatePv()) > 70)
                .collect(Collectors.toList());
    }

    private void buildAndSendSLA(List<AdIncomePvEntity> targetList,boolean isProduct){
        String alert = targetList.stream()
                .filter(r -> alertDelayJobService.canSend(AlertJobDelayModel.AD_PV_GAP_REALTIME.getJobId()+"_"+r.getProduct()))
                .map(r -> {
                    String desc = "";
                    if (isProduct) {
                        desc =  "> **" + r.getProductName() + "**(" + r.getProduct() + ")-" + r.getOs()
                                + ",今日累计截止两小时前 \n>> "
                                + "产品维度:[PV:" + r.getPvThird() + ",收入:" + Math.floor(r.getIncomeThird()) + "]"
                                + " \n\n >> OWN:[PV:" + r.getPv() + ",收入:" + Math.floor(r.getIncome())
                                + "] \n\n >> RATE:[PV:**" + Math.floor(r.getPvRate()) + "%**,收入:**" + Math.floor(r.getIncomeRate()) + "%**]";
                    }else {
                        desc =  "> **" + r.getProductName() + "**(" + r.getProduct() + ")-" + r.getOs()
                                + ",广告位名称:"+r.getPosId()+" \n>> "
                                + "广告位维度:[PV:" + r.getPvThird() + ",收入:" + Math.floor(r.getIncomeThird()) + "]"
                                + " \n\n >> OWN:[PV:" + r.getPv() + ",收入:" + Math.floor(r.getIncome())
                                + "] \n\n >> RATE:[PV:**" + Math.floor(r.getPvRate()) + "%**,收入:**" + Math.floor(r.getIncomeRate()) + "%**]";
                    }
                    return desc;
                })
                .collect(Collectors.joining("\n\n\n"));
        if (StringUtils.isEmpty(alert)){
            return;
        }

        Set<String> pr = targetList.stream().map(AdIncomePvEntity::getProduct).collect(Collectors.toSet());
        AlertRecord alertRecord = alertRecordService.insertAlertRecord(AlertJobDelayModel.AD_PV_GAP_REALTIME.getJobId(),
                AlertJobDelayModel.AD_PV_GAP_REALTIME.getJobName(), AlertModel.ADPVGAP_REALTIME, alert
                , AlertStatus.INIT, DingTailService.bsData, AlertType.DINGPHONE);
        DingTailService.sendMarkdownMsgDelay(alertRecord,
                AlertRoute.BUSINESS.value,
                AlertJobDelayModel.AD_PV_GAP_REALTIME,new ArrayList<>(pr));
    }
}
