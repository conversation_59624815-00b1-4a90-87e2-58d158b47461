package com.shinet.core.alert.clickhouse.service;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.shinet.core.alert.adb.entity.ProductDau;
import com.shinet.core.alert.adb.entity.ProductOrderCount;
import com.shinet.core.alert.adb.entity.RealtimeWithdrawTimeDistribution;
import com.shinet.core.alert.adb.mapper.RealtimeWithdrawTimeDistributionMapper;
import com.shinet.core.alert.clickhouse.entity.*;
import com.shinet.core.alert.clickhouse.mapper.ck1.ClickHouseDwdMapper;
import com.shinet.core.alert.coreservice.entity.AlertRecord;
import com.shinet.core.alert.coreservice.enums.*;
import com.shinet.core.alert.coreservice.service.AlertDelayJobService;
import com.shinet.core.alert.coreservice.service.AlertRecordService;
import com.shinet.core.alert.dingtalk.DingTailService;
import com.shinet.core.alert.dingtalk.VedioAlertService;
import com.shinet.core.alert.dsp.entity.DspCostAlertBean;
import com.shinet.core.alert.util.DateUtils;
import com.xxl.job.core.log.XxlJobLogger;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.DecimalFormat;
import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static java.math.BigDecimal.ROUND_HALF_UP;

@Slf4j
@Service
public class WithdrawService extends ServiceImpl<RealtimeWithdrawTimeDistributionMapper, RealtimeWithdrawTimeDistribution> {

    @Resource
    private ClickHouseDwdMapper clickHouseDwdMapper;
    @Autowired
    AlertRecordService alertRecordService;

    /**
     * description:
     * 提现报警
     * 对每个商户号进行提现监测，若某商户号已提现金额达到报警线，立即进行报警，报警线为80%，85%，90%，95%，100%，每达到一个新的报警线报警一次，无需持续报警
     * 报警信息格式如下：
     * 海南耀天 商户号1592456251 已提现80%
     */
    public void alertMerchantWithdraw() {
        XxlJobLogger.log("提现报警任务开始");
        Date date = new Date();
        String logday = DateUtils.formatDateForYMD(date);

        List<WithdrawMerchant> withdrawMerchantList = clickHouseDwdMapper.queryWithdrawMerchantPerList(logday);
        List<WithdrawMerchantAlarm> withdrawMerchantAlarmList = clickHouseDwdMapper.queryWithdrawMerchantAlarmList(logday);
        Map<String, Integer> alarmMap = withdrawMerchantAlarmList.stream().
                collect(Collectors.toMap(alarm -> alarm.getMerchantId() + "|" + alarm.getPerType(), alarm -> alarm.getNumber(), (a1, a2) -> a2));
        StringBuffer alertMsg = new StringBuffer("");
        List<WithdrawMerchantAlarm> dataList = new ArrayList<>();
        for (WithdrawMerchant withdrawMerchant : withdrawMerchantList) {
            String merchantName = withdrawMerchant.getMerchantName();
            String merchantId = withdrawMerchant.getMerchantId();
            Double withdrawLimit = withdrawMerchant.getWithdrawLimit();
            Double withdrawAmount = withdrawMerchant.getWithdrawAmount();
            Double per = withdrawMerchant.getPer();
            String perType = "";
            if (per.compareTo(0.8d) < 0) {
                continue;
            }
            if (per.compareTo(0.8d) >= 0 && per.compareTo(0.85d) < 0) {
                perType = "80";
            } else if (per.compareTo(0.85d) >= 0 && per.compareTo(0.9d) < 0) {
                perType = "85";
            } else if (per.compareTo(0.9d) >= 0 && per.compareTo(0.95d) < 0) {
                perType = "90";
            } else if (per.compareTo(0.95d) >= 0 && per.compareTo(1d) < 0) {
                perType = "95";
            } else if (per.compareTo(1d) >= 0) {
                perType = "100";
            }
            String key = merchantId + "|" + perType;
            Integer alarmNumber = alarmMap.get(key);
            if (alarmNumber != null && alarmNumber > 0) {
                //这个报警线已经报警,跳过
                continue;
            }
            WithdrawMerchantAlarm alarm = new WithdrawMerchantAlarm();
            alarm.setLogday(date);
            alarm.setMerchantId(merchantId);
            alarm.setMerchantName(merchantName);
            alarm.setPerType(perType);
            alarm.setNumber(1);
            dataList.add(alarm);
            String msg = "- %s 商户号:%s 额度:%s 已提现:%s \n";
            String withdrawLimitStr = new BigDecimal(withdrawLimit).divide(new BigDecimal(10000), 0, ROUND_HALF_UP).toString() + "万";
            String perStr = new BigDecimal(per).multiply(new BigDecimal(100)).setScale(2, RoundingMode.HALF_UP).toString() + "%";
            msg = String.format(msg, merchantName, merchantId, withdrawLimitStr, perStr);
            alertMsg.append(msg);
        }
        if (dataList.size() > 0) {
            clickHouseDwdMapper.saveWithdrawMerchantAlarm(dataList);
            XxlJobLogger.log("提现报警开始预警");
            AlertRecord alertRecord = alertRecordService.insertAlertRecord("商户提现预警", "MerchantWithdrawAlarm", AlertModel.MALL,
                    alertMsg.toString(),
                    AlertStatus.INIT, DingTailService.merchantWithdrawAlarmData, AlertType.DINGDING);
            DingTailService.sendMarkdownMsg(alertRecord);
        }
        XxlJobLogger.log("提现报警任务结束");

    }

    @Autowired
    private AlertDelayJobService alertDelayJobService;

    public void checkWithdraw() {
        // 提现3000dau 提现超过3000
        List<WithdrawAlertBean> withdrawAlertBeanList = clickHouseDwdMapper.queryWithdrawDauLess();
        if (withdrawAlertBeanList != null && withdrawAlertBeanList.size() > 0) {
            List<WithdrawAlertBean> checkBeanList = withdrawAlertBeanList.stream()
                    .filter(r -> alertDelayJobService.canSend(AlertJobDelayModel.WITHDRAW_CK.getJobId() + "_" + r.getProduct()))
                    .collect(Collectors.toList());
            if (checkBeanList.size() > 0) {
                List<String> productList = checkBeanList.stream().map(WithdrawAlertBean::getProduct).collect(Collectors.toList());
                String alert = checkBeanList.stream()
                        .map(r -> String.format("Dau 3000以下产品：**%s**(%s),os:%s,dau:%s,提现:**%s**", r.getProductName(),
                                r.getProduct(),
                                r.getOs(), r.getDau(), r.getWithdraw()))
                        .collect(Collectors.joining(";\n\n"));
                AlertRecord alertRecord = alertRecordService.insertAlertRecord("最近6h提现过高",
                        "最近6h提现过高", AlertModel.MALL_CK, alert
                        , AlertStatus.INIT, DingTailService.bsData, AlertType.DINGDING);
                DingTailService.sendMarkdownMsgDelay(alertRecord, AlertRoute.BUSINESS.value, AlertJobDelayModel.WITHDRAW_CK, productList);
            }
        }

        int rate = 60;
        Date now = new Date();
        if (now.getHours() <= 6) {
            rate = 70;
        }

        List<String> skipProduct = new ArrayList<>();
        if (now.getHours() <= 8) {
            skipProduct.add("wdnc3");
            skipProduct.add("wdnc4");
        }
        XxlJobLogger.log("当前提现Rate:" + rate);
        List<WithdrawAlertBean> withdrawAlertBeans = clickHouseDwdMapper.queryWithdrawDauMore(rate);
        if (withdrawAlertBeans != null && withdrawAlertBeans.size() > 0) {
            List<WithdrawAlertBean> checkBeanList = withdrawAlertBeans.stream()
                    .filter(r -> !skipProduct.contains(r.getProduct()))
                    .filter(r -> alertDelayJobService.canSend(AlertJobDelayModel.WITHDRAW_CK.getJobId() + "_" + r.getProduct()))
                    .filter(r -> {
                        if ("xmzcl".equals(r.getProduct())) {
                            return r.getWithDrawRate() > 100;
                        }
                        if ("wdnc4".equals(r.getProduct()) || "wdnc3".equals(r.getProduct())) {
                            return r.getWithDrawRate() > 190;
                        }
                        if ("tnxny2".equals(r.getProduct()) || "tnxny".equals(r.getProduct())) {
                            return r.getWithDrawRate() > 130;
                        }
                        return true;
                    })
                    .collect(Collectors.toList());

            if (checkBeanList.size() > 0) {
                List<String> productList = checkBeanList.stream().map(WithdrawAlertBean::getProduct).collect(Collectors.toList());
                String alert = checkBeanList.stream()
                        .map(r -> String.format("产品：**%s**(%s),os:**%s**,dau:%s,提现：%s,收入：%s,提现占比:**%s**", r.getProductName(),
                                r.getProduct(),
                                r.getOs(), r.getDau(), format(r.getWithdraw()), format(r.getIncome()), r.getWithDrawRate()))
                        .collect(Collectors.joining(";\n\n"));
                AlertRecord alertRecord = alertRecordService.insertAlertRecord("最近6h提现占比过高",
                        "最近6h提现占比过高", AlertModel.MALL_CK, alert
                        , AlertStatus.INIT, DingTailService.bsData, AlertType.DINGDING);
                DingTailService.sendMarkdownMsgDelay(alertRecord, AlertRoute.BUSINESS.value, AlertJobDelayModel.WITHDRAW_CK, productList);
            } else {
                log.info("静默周期 不通知..");
            }
        }

        // 换群换规则
        if (withdrawAlertBeans.size() > 0) {
            List<WithdrawAlertBean> checkBeanList = withdrawAlertBeans.stream()
                    .filter(r -> !skipProduct.contains(r.getProduct()))
                    .filter(r -> alertDelayJobService.canSend(AlertJobDelayModel.WITHDRAW_CK.getJobId() + "_" + r.getProduct()))
                    .filter(r -> r.getWithDrawRate() >= 110)
                    .filter(r -> {
                        if ("wdnc4".equals(r.getProduct()) || "wdnc3".equals(r.getProduct())) {
                            return r.getWithDrawRate() > 190;
                        }
                        if ("tnxny2".equals(r.getProduct()) || "tnxny".equals(r.getProduct())) {
                            return r.getWithDrawRate() > 150;
                        }
                        return true;
                    })
                    .collect(Collectors.toList());

            if (checkBeanList.size() > 0) {
                List<String> productList = checkBeanList.stream().map(WithdrawAlertBean::getProduct).collect(Collectors.toList());
                String alert = checkBeanList.stream()
                        .map(r -> String.format("产品：**%s**(%s),os:**%s**,dau:%s,提现：%s,收入：%s,提现占比:**%s**", r.getProductName(),
                                r.getProduct(),
                                r.getOs(), r.getDau(), format(r.getWithdraw()), format(r.getIncome()), r.getWithDrawRate()))
                        .collect(Collectors.joining(";\n\n"));
                AlertRecord alertRecord = alertRecordService.insertAlertRecord("最近6h提现占比过高",
                        "最近6h提现占比过高", AlertModel.MALL_CK, alert
                        , AlertStatus.INIT, DingTailService.dset, AlertType.DINGDING);
                DingTailService.sendMarkdownMsgDelay(alertRecord, AlertRoute.JISHU.value, AlertJobDelayModel.WITHDRAW_CK, productList);
            } else {
                log.info("静默周期 不通知..");
            }
        }
    }

    public void checkWithdrawCrossYesterday() {
        Integer hour = new Date().getHours();
        List<WithdrawCheckBean> withdrawAlertBeanList = clickHouseDwdMapper.queryWithdrawLastHour();
        if (withdrawAlertBeanList != null && withdrawAlertBeanList.size() > 0) {
            List<WithdrawCheckBean> checkBeanList = withdrawAlertBeanList.stream()
                    .filter(r -> alertDelayJobService.canSend(AlertJobDelayModel.WITHDRAW_ALERT.getJobId() + "_" + r.getProductName()))
                    .collect(Collectors.toList());

            if (checkBeanList.size() > 0) {
                List<String> productList = checkBeanList.stream().map(WithdrawCheckBean::getProductName).collect(Collectors.toList());
                String alert = checkBeanList.stream()
                        .map(r -> String.format("> 产品:**%s**,**%s**,最近小时提现:**%s**,Hau:**%s**,人均:**%s**" +
                                        "昨日提现:%s,昨日Hau:%s,昨日人均:%s,人均提现波动：<font face=\"微软雅黑\" color=#FF0000 >**%s**</font>", r.getProductName(),
                                r.getOs(), format(r.getTodayWithdraw()), r.getTodayHau(), format(r.getHWithdra()), format(r.getYesterdayWithdraw()), r.getYesterdayHau(), format(r.getYesHWithdra()),format(r.getRateWithdra())))
                        .collect(Collectors.joining("%;\n\n")) + "%;";

                AlertRecord alertRecord = null;
                if (checkBeanList.stream().allMatch(r -> r.getRate() > 2000D)) {
                    alertRecord = alertRecordService.insertAlertRecord("alert-withdraw-check-float",
                            "alert-withdraw-check-float", AlertModel.WITHDRAW_RATE, alert
                            , AlertStatus.INIT, DingTailService.bsData1, AlertType.DINGPHONE);
                } else {
                    alertRecord = alertRecordService.insertAlertRecord("alert-withdraw-check-float",
                            "alert-withdraw-check-float", AlertModel.WITHDRAW_RATE, alert
                            , AlertStatus.INIT, DingTailService.bsData, AlertType.DINGDING);
                }
                DingTailService.sendMarkdownMsgDelay(alertRecord, AlertRoute.FZB.value, AlertJobDelayModel.WITHDRAW_ALERT, productList);
            }
        }
    }

    public void checkWithdrawBigAmount() {
        List<WithdrawBigAmountBean> withdrawAlertBeanList = clickHouseDwdMapper.queryWithdrawBigAmount(200);
        if (withdrawAlertBeanList != null && withdrawAlertBeanList.size() > 0) {
            List<WithdrawBigAmountBean> checkBeanList = withdrawAlertBeanList.stream()
                    .filter(r -> alertDelayJobService.canSend(AlertJobDelayModel.WITHDRAW_AMOUNT.getJobId() + "_" + r.getProduct()))
                    .collect(Collectors.toList());
            if (checkBeanList.size() > 0) {
                long pageSize = 8;
                for (int page = 0; page <= checkBeanList.size() / pageSize; page++) {
                    buildAndSendBigAmount(checkBeanList.stream().skip(page * pageSize).limit(pageSize).collect(Collectors.toList()));
                }
            }
        }
    }

    private void buildAndSendBigAmount(List<WithdrawBigAmountBean> checkBeanList) {
        if (checkBeanList.size() == 0) {
            return;
        }
        List<String> productList = checkBeanList.stream().map(WithdrawBigAmountBean::getProduct).collect(Collectors.toList());
        String alert = checkBeanList.stream()
                .map(r -> String.format("> 产品:**%s**(%s),**%s**,用户:**%s**,过去1h提现:**%s**笔," +
                                "累计提现金额：<font face=\"微软雅黑\" color=#FF0000 >**%s**</font>元", r.getProductName(),
                        r.getProduct(),
                        r.getOs(), r.getUserId(), r.getOrderTime(), format(r.getWithdraw())))
                .collect(Collectors.joining(";\n\n"));
        AlertRecord alertRecord = alertRecordService.insertAlertRecord("alert-withdraw-check-bigAmount",
                "alert-withdraw-check-bigAmount", AlertModel.WITHDRAW_BASIC, alert
                , AlertStatus.INIT, DingTailService.bsData, AlertType.DINGDING);
        DingTailService.sendMarkdownMsgDelay(alertRecord, AlertRoute.BUSINESS.value, AlertJobDelayModel.WITHDRAW_AMOUNT, productList);
    }

    public void checkWithdrawChannelPart() {
        List<WithdrawChannelPartBean> withdrawAlertBeanList = clickHouseDwdMapper.queryWithdrawPartChannel();
        if (withdrawAlertBeanList != null && withdrawAlertBeanList.size() > 0) {
            List<WithdrawChannelPartBean> checkBeanList = withdrawAlertBeanList.stream()
                    .filter(r -> alertDelayJobService.canSend(AlertJobDelayModel.WITHDRAW_CHANNEL.getJobId() + "_" + r.getProduct()))
                    .collect(Collectors.toList());
            if (checkBeanList.size() > 0) {
                List<String> productList = checkBeanList.stream().map(WithdrawChannelPartBean::getProduct).collect(Collectors.toList());
                String alert = checkBeanList.stream()
                        .map(r -> String.format("> 产品:**%s**(%s),**%s**,渠道:**%s**,过去1h提现:**%s**元," +
                                        "人均提现:**%s**,波动率：<font face=\"微软雅黑\" color=#FF0000 >**%s**</font>", r.getProductName(),
                                r.getProduct(),
                                r.getOs(), r.getChannel(), format(r.getWithdrawToday()), format(r.getWithdrawRateToday()),
                                format((r.getWithdrawRateToday() - r.getWithdrawRateYesterday()) / r.getWithdrawRateYesterday() * 100)
                        ))
                        .collect(Collectors.joining("%;\n\n")) + "%;";
                AlertRecord alertRecord = alertRecordService.insertAlertRecord(AlertJobDelayModel.WITHDRAW_CHANNEL.getJobId(),
                        AlertJobDelayModel.WITHDRAW_CHANNEL.getJobId(), AlertModel.WITHDRAW_BASIC, alert
                        , AlertStatus.INIT, DingTailService.bsData, AlertType.DINGDING);
                DingTailService.sendMarkdownMsgDelay(alertRecord, AlertRoute.FZB.value, AlertJobDelayModel.WITHDRAW_CHANNEL, productList);
            }
        }
    }

    public void checkExPointChannelUserWithdraw() {
        List<NoPointExUserWithdrawBean> withdrawAlertBeanList = clickHouseDwdMapper.queryLastHourNoPointWithdrawUser();
        if (withdrawAlertBeanList != null && withdrawAlertBeanList.size() > 0) {
            List<NoPointExUserWithdrawBean> checkBeanList = withdrawAlertBeanList.stream()
                    .filter(r -> alertDelayJobService.canSend(AlertJobDelayModel.WITHDRAW_CHANNEL_NO_POINT.getJobId() + "_" + r.getProduct()))
                    .collect(Collectors.toList());
            if (checkBeanList.size() > 0) {
                List<String> productList = checkBeanList.stream().map(NoPointExUserWithdrawBean::getProduct).collect(Collectors.toList());
                String alert = checkBeanList.stream()
                        .map(r -> String.format("> 产品:**%s**(%s),渠道:**%s**,埋点异常用户过去1h提现:**%s**元," +
                                        "人均提现:**%s**,提现人数：<font face=\"微软雅黑\" color=#FF0000 >**%s**</font>", r.getProductName(),
                                r.getProduct(), r.getChannel(), format(r.getAllAmount()), format(r.getAmount()), r.getWithdrawDnu()
                        ))
                        .collect(Collectors.joining("\n\n"));
                AlertRecord alertRecord = alertRecordService.insertAlertRecord(AlertJobDelayModel.WITHDRAW_CHANNEL_NO_POINT.getJobId(),
                        AlertJobDelayModel.WITHDRAW_CHANNEL_NO_POINT.getJobId(), AlertModel.WITHDRAW_BASIC, alert
                        , AlertStatus.INIT, DingTailService.bsData, AlertType.DINGDING);
                DingTailService.sendMarkdownMsgDelay(alertRecord, AlertRoute.BUSINESS.value, AlertJobDelayModel.WITHDRAW_CHANNEL_NO_POINT, productList);
            }
        }
    }

    public void checkUserAvgBigWithdraw() {
        List<NoPointExUserWithdrawBean> withdrawAlertBeanList = clickHouseDwdMapper.checkUserAvgBigWithdrawUser();
        if (withdrawAlertBeanList != null && withdrawAlertBeanList.size() > 0) {
            List<NoPointExUserWithdrawBean> checkBeanList = withdrawAlertBeanList.stream()
                    .filter(r -> alertDelayJobService.canSend(AlertJobDelayModel.WITHDRAW_CHANNEL_BIG_CHECK.getJobId() + "_" + r.getProduct()))
                    .collect(Collectors.toList());
            if (checkBeanList.size() > 0) {
                List<String> productList = checkBeanList.stream().map(NoPointExUserWithdrawBean::getProduct).collect(Collectors.toList());
                String alert = checkBeanList.stream()
                        .map(r -> String.format("> 产品:**%s**(%s),渠道:**%s**,新增用户创建订单:**%s**元,实际提现:**%s**元," +
                                        "人均提现:**%s**,提现人数：<font face=\"微软雅黑\" color=#FF0000 >**%s**</font>", r.getProductName(),
                                r.getProduct(), r.getChannel(), format(r.getAllAmount()), format(r.getSuccessAmount()), format(r.getAmount()), r.getWithdrawDnu()
                        ))
                        .collect(Collectors.joining("\n\n"));
                AlertRecord alertRecord = alertRecordService.insertAlertRecord(AlertJobDelayModel.WITHDRAW_CHANNEL_BIG_CHECK.getJobId(),
                        AlertJobDelayModel.WITHDRAW_CHANNEL_BIG_CHECK.getJobId(), AlertModel.WITHDRAW_BASIC, alert
                        , AlertStatus.INIT, DingTailService.bsData, AlertType.DINGDING);
                DingTailService.sendMarkdownMsgDelay(alertRecord, AlertRoute.FZB.value, AlertJobDelayModel.WITHDRAW_CHANNEL_BIG_CHECK, productList);
                //DingTailService.sendMarkdownMsgDelay(alertRecord, AlertRoute.SJWT.value, AlertJobDelayModel.WITHDRAW_CHANNEL_BIG_CHECK, productList);
            }
        }
    }

    public void phoneWithDrawPl() {
        Date now = new Date();
        if (now.getHours() <= 9) {
            return;
        }
        List<LastHourWithdrawBean> lastHourWithdrawBeanList = clickHouseDwdMapper.queryLastHourWithdrawFl();
        if (lastHourWithdrawBeanList != null && lastHourWithdrawBeanList.size() > 0) {
            List<LastHourWithdrawBean> checkBeanList = lastHourWithdrawBeanList.stream()
                    .filter(r -> alertDelayJobService.canSend(AlertJobDelayModel.WITHDRAW_FLOAT_POINT.getJobId() + "_" + r.getProduct()))
                    .collect(Collectors.toList());
            if (checkBeanList.size() > 0) {
                List<String> productList = checkBeanList.stream().map(LastHourWithdrawBean::getProduct).collect(Collectors.toList());
                String alert = checkBeanList.stream()
                        .map(r -> String.format("> 产品:**%s**(%s),过去1h提现:**%s**元," +
                                        "人均提现:**%s**,对比昨日人均:**%s** 波动率：<font face=\"微软雅黑\" color=#FF0000 >**%s%%**</font>", r.getProductName(),
                                r.getProduct(), format(r.getTodayWithdraw()), format(r.getTodayUc()), format(r.getYesterdayUc()), format(r.getRateUc())
                        )).collect(Collectors.joining("\n\n"));

                String phone = checkBeanList.stream()
                        .map(r -> String.format("产品%s,过去1h提现:%s元,人均提现%s,波动率%s", r.getProductName()
                                , format(r.getTodayWithdraw()), format(r.getTodayUc()), format(r.getRateUc())
                        )).collect(Collectors.joining(";"));
                AlertRecord alertRecord = alertRecordService.insertAlertRecord(AlertJobDelayModel.WITHDRAW_FLOAT_POINT.getJobId(),
                        AlertJobDelayModel.WITHDRAW_FLOAT_POINT.getJobId(), AlertModel.WITHDRAW_BASIC, alert
                        , AlertStatus.INIT, DingTailService.syqun, AlertType.DINGPHONE);
                DingTailService.sendMarkdownMsgDelay(alertRecord, AlertRoute.BUSINESS.value, AlertJobDelayModel.WITHDRAW_CHANNEL_NO_POINT, productList);
                VedioAlertService.sendVocMsg("提现波动异常", phone, DingTailService.syqun);
            }
        }
    }


//    @PostConstruct
    public void checkWithDrawNumYes() {
        List<withdrawNumYesBean> lastHourWithdrawBeanList = clickHouseDwdMapper.queryWithdrawNumYes();
        if (lastHourWithdrawBeanList.size() > 0) {

                String alert = lastHourWithdrawBeanList.stream()
                        .map(r -> String.format("> 昨日提现表同步数据异常," +
                                        "数据量:**%s**",
                                r.getNum()
                        )).collect(Collectors.joining("\n\n"));

                String phone = lastHourWithdrawBeanList.stream()
                        .map(r -> String.format("昨日提现表同步数据异常,当前表内昨日数据量为%s",
                                r.getNum()
                        )).collect(Collectors.joining(";"));
                AlertRecord alertRecord = alertRecordService.insertAlertRecord(AlertJobDelayModel.WITHDRAW_NUM_ABNORMAL_ALERT.getJobId(),
                        AlertJobDelayModel.WITHDRAW_NUM_ABNORMAL_ALERT.getJobId(), AlertModel.WITHDRAW_BASIC, alert
                        , AlertStatus.INIT, DingTailService.dailyData, AlertType.DINGPHONE);
                DingTailService.sendMarkdownMsg(alertRecord, AlertRoute.JJSYJ.value);
                VedioAlertService.sendVocMsg("昨日提现表同步数据异常", phone, DingTailService.dailyData);

        }
    }


    @Resource
    private RealtimeWithdrawTimeDistributionMapper realtimeWithdrawTimeDistributionMapper;

    public void checkWithdrawNoSendAlert() {
        Date now = new Date();
        String today = DateUtils.formatDateForYMD(now) + " 00:00:00";
        Integer count = realtimeWithdrawTimeDistributionMapper.countTodayNotSendOrder(today);

        if (count >= 3000) {
            String alert = "提现订单审核未通过超过3000,请注意查看";
            AlertRecord alertRecord = alertRecordService.insertAlertRecord(AlertJobDelayModel.WITHDRAW_CHANNEL.getJobId(),
                    AlertJobDelayModel.WITHDRAW_CHANNEL.getJobId(), AlertModel.WITHDRAW_BASIC, alert
                    , AlertStatus.INIT, DingTailService.dset, AlertType.DINGDING);
            VedioAlertService.sendVocMsg("Withdraw", alert, DingTailService.syqun);
            DingTailService.sendMarkdownMsg(alertRecord, AlertRoute.JISHU.value);
        }

        Integer countUser = realtimeWithdrawTimeDistributionMapper.countTodayNotSendOrderUser(today);

        if (countUser >= 100) {
            String alert = "提现订单审核未通过影响人数超过100,请注意查看";
            AlertRecord alertRecord = alertRecordService.insertAlertRecord(AlertJobDelayModel.WITHDRAW_CHANNEL.getJobId(),
                    AlertJobDelayModel.WITHDRAW_CHANNEL.getJobId(), AlertModel.WITHDRAW_BASIC, alert
                    , AlertStatus.INIT, DingTailService.dset, AlertType.DINGDING);
            VedioAlertService.sendVocMsg("Withdraw", alert, DingTailService.syqun);
            DingTailService.sendMarkdownMsg(alertRecord, AlertRoute.JISHU.value);
        }
    }

    //预估毛利预警
    public void profitRealtimeHourAlert() {
        try {

            List<ProfitRealtimeHourAlertBean> hourList = clickHouseDwdMapper.profitRealtimeHourAlert(DateUtils.getBeforeHour(-1));
            XxlJobLogger.log("预估毛利低于-1000预警：", JSON.toJSON(hourList));

            List<String> productList = new ArrayList<>();
            Set<String> teamSet = new HashSet<>();

            List<String> costAlertList = new ArrayList<>();

            if (hourList.size() > 0) {
                productList.addAll(hourList.stream().map(ProfitRealtimeHourAlertBean::getProductName).collect(Collectors.toList()));
                teamSet.addAll(hourList.stream().map(ProfitRealtimeHourAlertBean::getProductGroup).filter(Objects::nonNull).collect(Collectors.toSet()));
                String alert = hourList.stream()
                        .map(r -> String.format("**%s**,\r 产品:**%s**,\r dau:%s,\r今日截止前一小时累计毛利:**%s**\r\n",
                                r.getProductGroup(), r.getProductName() + "_" + r.getOs(), r.getDau(), r.getPredict()))
                        .collect(Collectors.joining(";\n\n _ _ _ \n "));
                costAlertList.add(alert);
            }

            String alert = StringUtils.join(costAlertList, ";\n\n _ _ _ \n ");
            Set<String> pushData = teamSet.stream().flatMap(team -> DingTailService.zengzhangPushMap.get(team).stream()).collect(Collectors.toSet());
            if (StringUtils.isNotBlank(alert)) {
                AlertRecord alertRecord = alertRecordService.insertAlertRecord(AlertJobDelayModel.PROFIT_REALTIME_HOUR_ALERT.getJobId(),
                        AlertJobDelayModel.PROFIT_REALTIME_HOUR_ALERT.getJobName(), AlertModel.PROFIT_REALTIME_HOUR_ALERT, alert
                        , AlertStatus.INIT, pushData, AlertType.DINGDING);
                //正式
                DingTailService.sendMarkdownMsgDelay(alertRecord, AlertRoute.ZENGZHANG.value, AlertJobDelayModel.PROFIT_REALTIME_HOUR_ALERT, productList);
                //测试
                //DingTailService.sendMarkdownMsgDelay(alertRecord, AlertRoute.SJWT.value, AlertJobDelayModel.PROFIT_REALTIME_HOUR_ALERT, productList);
            }

        } catch (Exception e) {
            XxlJobLogger.log("profitRealtimeHourAlert报错：CheckEx:", e);
        }

    }

    //IP提现预警
    public void checkWithdrawIPAlert() {
        try {
            List<WithDrawIpNewBean> withDrawIpBeanList = clickHouseDwdMapper.queryWithDrawIP();
            XxlJobLogger.log("IP提现预警结果：", JSON.toJSON(withDrawIpBeanList));
            checkWithdrawIPAlertForSum(withDrawIpBeanList);

            /*String alert = "";
            List<String> productList = new ArrayList<>();
            if (withDrawIpBeanList.size() > 0) {
                for (WithDrawIpNewBean bean : withDrawIpBeanList) {
                    System.out.println(JSON.toJSON(bean));
                    JSONArray jsonArray = (JSONArray) JSON.toJSON(bean.getMap());

                    if (jsonArray.size() % 3 > 0) {
                        XxlJobLogger.log("数据长度有误：", jsonArray);
                    }

                    alert += "IP：**" + bean.getIp() + "**,提现总金额：**" + bean.getSumWithdraw() + "**";
                    for (int i = 0; i < jsonArray.size(); i += 4) {
                        if (Double.valueOf(jsonArray.get(i + 2).toString().replace(")", "")) >= 0.3) {
                            alert += "\n\n	用户id：**" + jsonArray.get(i).toString().replace("(", "")
                                    + "**，产品：**" + jsonArray.get(i + 1) + "**, 提现金额：**" + jsonArray.get(i + 2).toString().replace(")", "") + "**" + ";\n\n";
                            productList.add(jsonArray.get(i + 1).toString());
                        }
                        if (alert.length() > 2000){
                            XxlJobLogger.log("checkWithdrawIPAlert消息超长：分批次发送");
                            AlertRecord alertRecord = alertRecordService.insertAlertRecord(AlertJobDelayModel.WITHDRAW_IP_ALERT.getJobId(),
                                    AlertJobDelayModel.WITHDRAW_IP_ALERT.getJobName(), AlertModel.WITHDRAW_IP, alert
                                    , AlertStatus.INIT, DingTailService.bsData, AlertType.DINGDING);

                            //正式
                            //DingTailService.sendMarkdownMsgDelay(alertRecord, AlertRoute.BUSINESS.value, AlertJobDelayModel.WITHDRAW_IP_ALERT, productList);
                            //测试
                            //DingTailService.sendMarkdownMsgDelay(alertRecord, AlertRoute.SJWT.value, AlertJobDelayModel.WITHDRAW_IP_ALERT, productList);
                            DingTailService.sendMarkdownMsg(alertRecord, AlertRoute.SJWT.value);
                            alert += "IP：**" + bean.getIp() + "**,提现总金额：**" + bean.getSumWithdraw() + "**";
                        }
                    }

                }

                AlertRecord alertRecord = alertRecordService.insertAlertRecord(AlertJobDelayModel.WITHDRAW_IP_ALERT.getJobId(),
                        AlertJobDelayModel.WITHDRAW_IP_ALERT.getJobName(), AlertModel.WITHDRAW_IP, alert
                        , AlertStatus.INIT, DingTailService.bsData, AlertType.DINGDING);

                //正式
                //DingTailService.sendMarkdownMsgDelay(alertRecord, AlertRoute.BUSINESS.value, AlertJobDelayModel.WITHDRAW_IP_ALERT, productList);
                //测试
                //DingTailService.sendMarkdownMsgDelay(alertRecord, AlertRoute.SJWT.value, AlertJobDelayModel.WITHDRAW_IP_ALERT, productList);
                DingTailService.sendMarkdownMsg(alertRecord, AlertRoute.SJWT.value);

            }*/
        } catch (Exception e) {
            XxlJobLogger.log("checkWithdrawIPAlert报错：CheckEx:", e);
        }

    }

    //IP提现预警
    public void checkWithdrawIPAlertForSum(List<WithDrawIpNewBean> withDrawIpBeanList) {
        try {

            XxlJobLogger.log("IP提现预警结果用户汇总：{}", JSON.toJSON(withDrawIpBeanList));
            String alert = "";

            if (withDrawIpBeanList.size() > 0) {
                List<String> productList = new ArrayList<>();
                for (WithDrawIpNewBean bean : withDrawIpBeanList) {
                    System.out.println(JSON.toJSON(bean));
                    JSONArray jsonArray = (JSONArray) JSON.toJSON(bean.getMap());

                    if (jsonArray.size() % 4 > 0) {
                        XxlJobLogger.log("数据长度有误：{}", jsonArray);
                    }

                    alert += "IP：**" + bean.getIp() + "**,提现总金额：**" + bean.getSumWithdraw() + "**";
                    Map<String, Double> productTotalWithdraw = new HashMap<>();
                    Map<String, String> productMap = new HashMap<>();
                    Map<String, Integer> productTotalUser = new HashMap<>();
                    for (int i = 0; i < jsonArray.size(); i += 4) {
                        String productName = jsonArray.getString(i + 1);
                        Double withdraw = Double.valueOf(jsonArray.getString(i + 2).replace(")", ""));
                        if (productTotalWithdraw.containsKey(productName)) {
                            productTotalWithdraw.put(productName, productTotalWithdraw.get(productName) + withdraw);
                        } else {
                            productTotalWithdraw.put(productName, withdraw);
                        }

                        if (productTotalUser.containsKey(productName)) {
                            productTotalUser.put(productName, productTotalUser.get(productName) + 1);
                        } else {
                            productTotalUser.put(productName, 1);
                        }
                        if (!productMap.containsKey(productName)) {
                            productMap.put(productName, jsonArray.getString(i + 3).replace(")", "").replace("'", ""));
                        }
                    }

                    for (String key : productTotalWithdraw.keySet()) {
                        Double totalWithdraw = productTotalWithdraw.get(key);
                        Integer totalUser = productTotalUser.get(key);
                        String product = productMap.get(key);

                        String avg = totalUser > 0 ? new BigDecimal(totalWithdraw).divide(new BigDecimal(totalUser), 2, RoundingMode.HALF_UP).toString() : "0";
                        DecimalFormat df = new DecimalFormat("#.00");
                        if (alertDelayJobService.canSend(AlertJobDelayModel.WITHDRAW_IP_ALERT_PRODUCT.getJobId() + "_" + product)) {
                            alert += "\n\n	产品：**" + key
                                    + "(" + product + ")**，用户提现：**" + df.format(totalWithdraw) + "**, 人均提现：**" + avg + "**" + ", 提现人数：**" + totalUser + "**" + ";\n\n";
                            productList.add(product);
                        }

                        //长度较长时，先预警一部分数据
                        if (alert.length() > 500) {
                            XxlJobLogger.log("数据太长，分别预警：{}", alert);
                            AlertRecord alertRecord = alertRecordService.insertAlertRecord(AlertJobDelayModel.WITHDRAW_IP_ALERT_PRODUCT.getJobId(),
                                    AlertJobDelayModel.WITHDRAW_IP_ALERT_PRODUCT.getJobName(), AlertModel.WITHDRAW_IP_PRODUCT, alert
                                    , AlertStatus.INIT, DingTailService.bsData, AlertType.DINGDING);

                            DingTailService.sendMarkdownMsgDelay(alertRecord, AlertRoute.FZB.value, AlertJobDelayModel.WITHDRAW_IP_ALERT_PRODUCT, productList);
                            //重新赋值
                            productList = new ArrayList<>();
                            alert = "IP：**" + bean.getIp() + "**,提现总金额：**" + bean.getSumWithdraw() + "**";
                        }
                    }
                    ;

                }

                //预警产品未被禁止
                if (alert.contains("产品") || alert.contains("用户提现")) {

                    AlertRecord alertRecord = alertRecordService.insertAlertRecord(AlertJobDelayModel.WITHDRAW_IP_ALERT_PRODUCT.getJobId(),
                            AlertJobDelayModel.WITHDRAW_IP_ALERT_PRODUCT.getJobName(), AlertModel.WITHDRAW_IP_PRODUCT, alert
                            , AlertStatus.INIT, DingTailService.bsData, AlertType.DINGDING);

                    //正式
                    //DingTailService.sendMarkdownMsg(alertRecord, AlertRoute.BUSINESS.value);
                    DingTailService.sendMarkdownMsgDelay(alertRecord, AlertRoute.FZB.value, AlertJobDelayModel.WITHDRAW_IP_ALERT_PRODUCT, productList);
                    //测试
                    //DingTailService.sendMarkdownMsgDelay(alertRecord, AlertRoute.SJWT.value, AlertJobDelayModel.WITHDRAW_IP_ALERT_PRODUCT, productList);
                    //DingTailService.sendMarkdownMsg(alertRecord, AlertRoute.SJWT.value);
                }


            }
        } catch (Exception e) {
            XxlJobLogger.log("checkWithdrawIPAlertForSum报错：CheckEx:", e);
        }

    }


    //提现预警
    public void checkDeviceNewAlert() {
        try {
            List<DeviceNewBean> deviceNewBeanList = clickHouseDwdMapper.queryDeviceNew();
            XxlJobLogger.log("设备提现预警结果：", JSON.toJSON(deviceNewBeanList));

            if (deviceNewBeanList.size() > 0) {

                List<DeviceNewBean> callBackRateList = deviceNewBeanList.stream()
                        .filter(r -> alertDelayJobService.canSend(AlertJobDelayModel.DEVICE_NEW_ALERT.getJobId() + "_" + r.getProductName()))
                        .collect(Collectors.toList());

                List<String> productList = callBackRateList.stream().map(DeviceNewBean::getProductName).collect(Collectors.toList());

                String alert = callBackRateList.stream()
                        .map(r -> String.format("OS:**%s**,产品:**%s**, 总设备数：**%s**, 总用户数：**%s**, 自然量总用户数：**%s**，自然量占比：**5%**",
                                r.getOs(), r.getProductName(), r.getCountDevice().toString(), r.getCountAll().toString(), r.getCountNatural().toString()
                                , r.getCeilP(r.getCountNatural() / r.getCountAll() * 100).toString()))
                        .collect(Collectors.joining(";\n\n"));

                AlertRecord alertRecord = alertRecordService.insertAlertRecord(AlertJobDelayModel.DEVICE_NEW_ALERT.getJobId(),
                        AlertJobDelayModel.DEVICE_NEW_ALERT.getJobName(), AlertModel.DEVICE_NEW, alert
                        , AlertStatus.INIT, DingTailService.bsData, AlertType.DINGDING);

                //生产
                DingTailService.sendMarkdownMsgDelay(alertRecord, AlertRoute.BUSINESS.value, AlertJobDelayModel.DEVICE_NEW_ALERT, productList);
                //测试
                //DingTailService.sendMarkdownMsgDelay(alertRecord, AlertRoute.SJWT.value, AlertJobDelayModel.DEVICE_NEW_ALERT, productList);
            }
        } catch (Exception e) {
            XxlJobLogger.log("CheckEx:", e);
        }

    }

    //自然量提现预警
    public void checkWithdrawNaturalAlert() {
        try {
            List<WithDrawNaturalBean> withDrawNaturalBeanList = clickHouseDwdMapper.queryWithDrawNatural();
            XxlJobLogger.log("自然量提现预警结果：", JSON.toJSON(withDrawNaturalBeanList));

            if (withDrawNaturalBeanList.size() > 0) {

                List<WithDrawNaturalBean> callBackRateList = withDrawNaturalBeanList.stream()
                        .filter(r -> alertDelayJobService.canSend(AlertJobDelayModel.WITHDRAW_NATURAL_ALERT.getJobId() + "_" + r.getProductName()))
                        .collect(Collectors.toList());

                List<String> productList = callBackRateList.stream().map(WithDrawNaturalBean::getProductName).collect(Collectors.toList());

                String alert = callBackRateList.stream()
                        .map(r -> String.format("OS:**%s**,产品:**%s**, 总提现提交数：**%s**, 总提现成功数：**%s**,  自然量用户提现成功数：**%s**，自然量成功占比：**%s**,  近1小时自然量用户提现成功数：**%s**，近1小时自然量成功占比：**%s**",
                                        r.getOs(), r.getProductName(), r.getSumAllAll().toString(), r.getSumAll().toString(), r.getSumNatural().toString()
//								, r.getCeilP(r.getSumNatural() / r.getSumAll() * 100).toString()))
                                        , r.getCeilP(r.getSumAll() == 0 ? 0 : r.getSumNatural() / (double) r.getSumAll() * 100).toString()
                                        , r.getSumHourNatural().toString()
                                        , r.getCeilP(r.getSumHourAll() == 0 ? 0 : r.getSumHourNatural() / (double) r.getSumHourAll() * 100).toString()
                                )
                        )
                        .collect(Collectors.joining(";\n\n"));

                AlertRecord alertRecord = alertRecordService.insertAlertRecord(AlertJobDelayModel.WITHDRAW_NATURAL_ALERT.getJobId(),
                        AlertJobDelayModel.WITHDRAW_NATURAL_ALERT.getJobName(), AlertModel.WITHDRAW_NATURAL, alert
                        , AlertStatus.INIT, DingTailService.bsData, AlertType.DINGDING);

                //生产
                DingTailService.sendMarkdownMsgDelay(alertRecord, AlertRoute.FZB.value, AlertJobDelayModel.WITHDRAW_NATURAL_ALERT, productList);
                //测试
                //DingTailService.sendMarkdownMsgDelay(alertRecord, AlertRoute.SJWT.value, AlertJobDelayModel.WITHDRAW_NATURAL_ALERT, productList);
            }
        } catch (Exception e) {
            XxlJobLogger.log("CheckEx:", e);
        }
    }


    private static String format(Double value) {
        BigDecimal bd = new BigDecimal(value);
        bd = bd.setScale(2, RoundingMode.HALF_UP);
        return bd.toString();
    }

    public void checkWithdrawNaturalChannelLimitAlert(int alertCount) {
        Date now = new Date();
        String day = DateUtils.formatDateForYMD(now);
        String today = day + " 00:00:00";
        try {
            List<ProductOrderCount> channelLimitList = realtimeWithdrawTimeDistributionMapper.getChannelLimitProductCount(today, alertCount);
            if (!channelLimitList.isEmpty()) {
                List<String> productList = channelLimitList.stream().filter(r -> alertDelayJobService.canSend(AlertJobDelayModel.NATUREAL_WITHDRAW_CHANNEL_LIMIT_CHECK.getJobId() + "_" + r.getProduct()))
                        .map(ProductOrderCount::getProduct).collect(Collectors.toList());
                if (productList.isEmpty()) {
                    return;
                }
                XxlJobLogger.log("query dau product list: {}", productList);
                List<ProductDau> productDauList = clickHouseDwdMapper.getProductDauList(productList, day);
                //查询dau
                Map<String, Integer> productDauMap = productDauList.stream().collect(Collectors.toMap(ProductDau::getProduct, ProductDau::getDau));
                String alert = channelLimitList.stream()
                        .filter(r -> productList.contains(r.getProduct()))
                        //产品：我的家园2(wdjy2),os:android,dau:xxxx,拦截提现：xxxx,拦截订单数：xxxx,订单人均提现：xxxx;13:47
                        .map(r -> String.format("产品：%s(%s),os:android,dau:%d,拦截提现：%s,拦截订单数：%d,订单人均提现：%s",
                                r.getProductName(), r.getProduct(), MapUtils.getIntValue(productDauMap, r.getProduct()), String.format("%.2f", r.getAmountSum()), r.getOrderCount(), String.format("%.2f", r.getAmountSum() / r.getOrderCount())))
                        .collect(Collectors.joining(";\n\n"));

                AlertRecord alertRecord = alertRecordService.insertAlertRecord(AlertJobDelayModel.NATUREAL_WITHDRAW_CHANNEL_LIMIT_CHECK.getJobId(),
                        AlertJobDelayModel.NATUREAL_WITHDRAW_CHANNEL_LIMIT_CHECK.getJobName(), AlertModel.NATURAL_WITHDRAW_CHANNEL_LIMIT, alert
                        , AlertStatus.INIT, DingTailService.bsData, AlertType.DINGDING);
                //生产
                DingTailService.sendMarkdownMsgDelay(alertRecord, AlertRoute.BUSINESS.value, AlertJobDelayModel.NATUREAL_WITHDRAW_CHANNEL_LIMIT_CHECK, productList);
                //DingTailService.sendMarkdownMsgDelay(alertRecord, AlertRoute.SJWT.value, AlertJobDelayModel.NATUREAL_WITHDRAW_CHANNEL_LIMIT_CHECK, productList);
            }
        } catch (Exception e) {
            XxlJobLogger.log("CheckEx:", e);
        }
    }
}
