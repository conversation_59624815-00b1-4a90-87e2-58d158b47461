package com.shinet.core.alert.config;

import com.aliyun.oss.OSS;
import com.aliyun.oss.OSSClientBuilder;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

@Configuration
@Slf4j
public class OssConfiguration {

    @Value("oss-cn-beijing-internal.aliyuncs.com")
    private String videoEndpoint;
    //TODO先用外网配置
//	@Value("${ali.oss.video.endpoint:oss-cn-beijing.aliyuncs.com}")
//	private String videoEndpoint;

    @Value("LTAI4G7Lq5677x5MSCmUL9VW")
    private String videoAccessKeyId;

    @Value("******************************")
    private String videoAccessKeySecret;



    @Bean(name = "videoManagerOss")
    public OSS ossClientFactory() {
        return new OSSClientBuilder().build(videoEndpoint, videoAccessKeyId, videoAccessKeySecret);
    }
}
