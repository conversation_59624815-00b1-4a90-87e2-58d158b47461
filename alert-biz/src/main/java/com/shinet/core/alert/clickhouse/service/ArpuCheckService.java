package com.shinet.core.alert.clickhouse.service;

import com.google.common.collect.Lists;
import com.shinet.core.alert.clickhouse.entity.ArpuCheckBean;
import com.shinet.core.alert.clickhouse.entity.ArpuExpBaen;
import com.shinet.core.alert.clickhouse.entity.ProductEntity;
import com.shinet.core.alert.clickhouse.entity.UserFilterAdBean;
import com.shinet.core.alert.clickhouse.mapper.ck1.ClickHouseDwdMapper;
import com.shinet.core.alert.coreservice.entity.AlertRecord;
import com.shinet.core.alert.coreservice.enums.*;
import com.shinet.core.alert.coreservice.service.AlertDelayJobService;
import com.shinet.core.alert.coreservice.service.AlertRecordService;
import com.shinet.core.alert.dingtalk.DingTailService;
import com.shinet.core.alert.dingtalk.VedioAlertService;
import com.xxl.job.core.log.XxlJobLogger;
import lombok.extern.slf4j.Slf4j;
import org.apache.hbase.thirdparty.com.google.common.collect.Sets;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2022/4/21
 */
@Slf4j
@Service
public class ArpuCheckService {

    @Resource
    private ClickHouseDwdMapper clickHouseDwdMapper;
    @Autowired
    private AlertDelayJobService alertDelayJobService;
    @Autowired
    private AlertRecordService alertRecordService;
    @Autowired
    private QueryBpAdminOpLogService queryBpAdminOpLogService;


    public void checkArpu(){
        try {
            List<ArpuExpBaen> arpuExpBaenList =  clickHouseDwdMapper.queryArpu();
            if (arpuExpBaenList.size() > 0){
                List<ArpuExpBaen> checkBeanList = arpuExpBaenList.stream()
                        .filter(r -> alertDelayJobService.canSend(AlertJobDelayModel.AD_CK_ARPU.getJobId()+"_"+r.getProduct()))
                        .collect(Collectors.toList());
                if (checkBeanList.size() > 0) {
                    List<String> productList = checkBeanList.stream().map(ArpuExpBaen::getProduct).collect(Collectors.toList());
                    String alert = checkBeanList.stream()
                            .map(r -> String.format("产品：**%s**(%s),os:**%s**,dau:%s,Arpu异常:**%s**",r.getProductName(), r.getProduct(),
                                    r.getOs(), r.getDau(), r.getArpu()))
                            .collect(Collectors.joining(";\n\n"));
                    AlertRecord alertRecord = alertRecordService.insertAlertRecord(AlertJobDelayModel.AD_CK_ARPU.getJobId(),
                            AlertJobDelayModel.AD_CK_ARPU.getJobName(), AlertModel.ARPU_CK, alert
                            , AlertStatus.INIT, DingTailService.bsData, AlertType.DINGDING);
                    DingTailService.sendMarkdownMsgDelay(alertRecord, AlertRoute.BUSINESS.value, AlertJobDelayModel.AD_CK_ARPU, productList);
                }
            }
        }catch (Exception e){
            XxlJobLogger.log("CheckEx:",e);
        }

    }

    public void checkChannelECPM(){
        try {
            List<ArpuCheckBean> arpuExpBaenList = clickHouseDwdMapper.queryAdSourceEcpm();
            if (arpuExpBaenList.size() > 0) {
                List<ArpuCheckBean> checkBeanList = arpuExpBaenList.stream()
                        .filter(r -> !"其他".equals(r.getAdSource()))
                        .filter(r -> alertDelayJobService.canSend(AlertJobDelayModel.AD_CK_ECPM.getJobId() + "_" + r.getAdSource()))
                        .collect(Collectors.toList());
                if (checkBeanList.size() > 0) {
                    List<String> productList = checkBeanList.stream().map(ArpuCheckBean::getAdSource).collect(Collectors.toList());
                    String alert = checkBeanList.stream()
                            .map(r -> String.format("平台：**%s**,dau:%s,前一小时ECPM：%s," +
                                    "昨日同时段ECPM：%s,波动率:**%s**", r.getAdSource(), r.getDau(), format(r.getDt1()), format(r.getDt2()), format(r.getSubRate())) + "%")
                            .collect(Collectors.joining(";\n\n"));
                    AlertRecord alertRecord = alertRecordService.insertAlertRecord(AlertJobDelayModel.AD_CK_ECPM.getJobId(),
                            AlertJobDelayModel.AD_CK_ECPM.getJobName(), AlertModel.ARPU_CK, alert
                            , AlertStatus.INIT, DingTailService.bsData, AlertType.DINGDING);
                    DingTailService.sendMarkdownMsgDelay(alertRecord, AlertRoute.BUSINESS.value, AlertJobDelayModel.AD_CK_ECPM, productList);
                }
            }
        }catch (Exception e){
            log.warn("Query Ex:",e);
        }
    }

    public void checkECPM(){
        try {
            List<ArpuCheckBean> arpuExpBaenList = clickHouseDwdMapper.queryEcpm();
            List<ArpuCheckBean> filterBeanList = arpuExpBaenList.stream().
                    filter(arpuCheckBean -> {
                        if (arpuCheckBean.getDau() >= 5000){
                            return arpuCheckBean.getSubRate() >= 30 || arpuCheckBean.getSubRate() <= -30;
                        }else {
                            return arpuCheckBean.getSubRate() >= 40 || arpuCheckBean.getSubRate() <= -40;
                        }
                    }).collect(Collectors.toList());
            if (filterBeanList.size() > 0) {
                List<ArpuCheckBean> checkBeanList = filterBeanList.stream()
                        .filter(r -> alertDelayJobService.canSend(AlertJobDelayModel.AD_CK_ECPM.getJobId()+"_"+r.getProduct()))
                        .collect(Collectors.toList());
                if (checkBeanList.size() > 0) {
                    List<String> productList = checkBeanList.stream().map(ArpuCheckBean::getProduct).collect(Collectors.toList());
                    String alert = checkBeanList.stream()
                            .map(r -> String.format("产品：**%s**(%s),os:**%s**,dau:%s,前一小时ECPM：%s," +
                                            "昨日同时段ECPM：%s,波动率:**%s**",r.getProductName(),r.getProduct(),
                                    r.getOs(), r.getDau(),  format(r.getDt1()),  format(r.getDt2()), format(r.getSubRate())) + "%")
                            .collect(Collectors.joining(";\n\n"));
                    AlertRecord alertRecord = alertRecordService.insertAlertRecord(AlertJobDelayModel.AD_CK_ECPM.getJobId(),
                            AlertJobDelayModel.AD_CK_ECPM.getJobName(), AlertModel.ARPU_CK, alert
                            , AlertStatus.INIT, DingTailService.bsData, AlertType.DINGDING);
                    DingTailService.sendMarkdownMsgDelay(alertRecord, AlertRoute.BUSINESS.value, AlertJobDelayModel.AD_CK_ECPM, productList);
                }
            }

            // 换群换规则
            if (arpuExpBaenList.size() > 0){
                List<ArpuCheckBean> checkBeanList = arpuExpBaenList.stream()
                        .filter(arpuCheckBean -> {
                            if (arpuCheckBean.getDau() >= 5000){
                                return arpuCheckBean.getSubRate() <= -40;
                            }else {
                                return arpuCheckBean.getSubRate() <= -60;
                            }
                        })
                        .collect(Collectors.toList());
                if (checkBeanList.size() > 0) {
                    String alert = checkBeanList.stream()
                            .map(r -> String.format("产品：**%s**(%s),os:**%s**,dau:%s,前一小时ECPM：%s," +
                                            "昨日同时段ECPM：%s,波动率:**%s**",r.getProductName(),r.getProduct(),
                                    r.getOs(), r.getDau(), r.getDt1(), r.getDt2(), r.getSubRate()) + "%")
                            .collect(Collectors.joining(";\n\n"));
                    long voc = checkBeanList.stream().filter(r -> {
                        ProductEntity pr = AppConfig.productEnMap.getOrDefault(r.getProduct(),new ProductEntity(){{setId(0);}});
                        return queryBpAdminOpLogService.updateAdConfig(pr.getId());
                    }).count();
                    if (voc > 0L) {
                        VedioAlertService.sendVocMsg("商业广告 ", alert);
                    }
                    AlertRecord alertRecord = alertRecordService.insertAlertRecord(AlertJobDelayModel.AD_CK_ECPM.getJobId(),
                            AlertJobDelayModel.AD_CK_ECPM.getJobName(), AlertModel.ARPU_CK, alert
                            , AlertStatus.INIT, DingTailService.dset, AlertType.DINGPHONE);
                    DingTailService.sendMarkdownMsg(alertRecord);
                }
            }
        }catch (Exception e){
            XxlJobLogger.log("CheckEx:",e);
        }
    }

    public void checkPV(){
        try {
            List<ArpuCheckBean> arpuExpBaenList =  clickHouseDwdMapper.queryPv();
            List<ArpuCheckBean> filterBeanList = arpuExpBaenList.stream()
                    .filter(arpuCheckBean -> {
                        if (arpuCheckBean.getDau() >= 10000){
                            return arpuCheckBean.getSubRate() >= 30 || arpuCheckBean.getSubRate() <= -30;
                        }else {
                            return arpuCheckBean.getSubRate() >= 40 || arpuCheckBean.getSubRate() <= -40;
                        }
                    })
                    .collect(Collectors.toList());
            if (filterBeanList.size() > 0){
                List<ArpuCheckBean> checkBeanList = filterBeanList.stream()
                        .filter(r -> alertDelayJobService.canSend(AlertJobDelayModel.AD_CK_PV.getJobId()+"_"+r.getProduct()))
                        .collect(Collectors.toList());
                if (checkBeanList.size() > 0){
                    List<String> productList = checkBeanList.stream().map(ArpuCheckBean::getProduct).collect(Collectors.toList());
                    String alert = checkBeanList.stream()
                            .map(r -> String.format("产品：**%s**(%s),os:**%s**,dau:%s,前一小时人均PV：%s," +
                                            "昨日同时段人均PV：%s,波动率:**%s**",r.getProductName(), r.getProduct(),
                                    r.getOs(),r.getDau(), format(r.getDt1()), format(r.getDt2()), format(r.getSubRate())) +"%")
                            .collect(Collectors.joining(";\n\n"));
                    AlertRecord alertRecord = alertRecordService.insertAlertRecord(AlertJobDelayModel.AD_CK_PV.getJobId(),
                            AlertJobDelayModel.AD_CK_PV.getJobName(), AlertModel.ARPU_CK,alert
                            , AlertStatus.INIT, DingTailService.bsData, AlertType.DINGDING);
                    DingTailService.sendMarkdownMsgDelay(alertRecord, AlertRoute.BUSINESS.value,AlertJobDelayModel.AD_CK_PV,productList);
//                    DingTailService.sendMarkdownMsgDelay(alertRecord, AlertRoute.SJWT.value,AlertJobDelayModel.AD_CK_PV,productList);
                }
            }

            // 换群换规则
            if (arpuExpBaenList.size() > 0){
                List<ArpuCheckBean> checkBeanList = arpuExpBaenList.stream()
                        .filter(arpuCheckBean -> {
                            if (arpuCheckBean.getDau() >= 5000){
                                return arpuCheckBean.getSubRate() <= -50;
                            }else {
                                return arpuCheckBean.getSubRate() <= -70;
                            }
                        })
                        .collect(Collectors.toList());
                if (checkBeanList.size() > 0) {
                    String alert = checkBeanList.stream()
                            .map(r -> String.format("产品：**%s**(%s),os:**%s**,dau:%s,前一小时人均PV：%s," +
                                            "昨日同时段人均PV：%s,波动率:**%s**",r.getProductName(), r.getProduct(),
                                    r.getOs(),r.getDau(), format(r.getDt1()), format(r.getDt2()), format(r.getSubRate())) +"%")
                            .collect(Collectors.joining(";\n\n"));
                    long voc = checkBeanList.stream().filter(r -> {
                        ProductEntity pr = AppConfig.productEnMap.getOrDefault(r.getProduct(),new ProductEntity(){{setId(0);}});
                        return queryBpAdminOpLogService.updateAdConfig(pr.getId());
                    }).count();
                    if (voc > 0L) {
                        VedioAlertService.sendVocMsg("商业广告 ", alert);
                    }
                    AlertRecord alertRecord = alertRecordService.insertAlertRecord(AlertJobDelayModel.AD_CK_ECPM.getJobId(),
                            AlertJobDelayModel.AD_CK_ECPM.getJobName(), AlertModel.ARPU_CK, alert
                            , AlertStatus.INIT, DingTailService.dset, AlertType.DINGPHONE);
                    DingTailService.sendMarkdownMsg(alertRecord);
//                    DingTailService.sendMarkdownMsg(alertRecord, AlertRoute.SJWT.value);
                }
            }
        }catch (Exception e){
            XxlJobLogger.log("CheckEx:",e);
        }

    }

    private static String format(Double value){
        BigDecimal bd = new BigDecimal(value);
        bd = bd.setScale(2, RoundingMode.HALF_UP);
        return bd.toString();
    }

    public void checkPVAllDev() {
        try {
            List<ArpuCheckBean> arpuExpBaenList =  clickHouseDwdMapper.queryPvAllDev();
            List<ArpuCheckBean> filterBeanList = arpuExpBaenList.stream().
                    filter(arpuCheckBean -> {
                        if (arpuCheckBean.getDau() >= 10000){
                            return arpuCheckBean.check(30);
                        }else {
                            return arpuCheckBean.check(40);
                        }
                    }).collect(Collectors.toList());
            if (filterBeanList.size() > 0){
                List<ArpuCheckBean> checkBeanList = filterBeanList.stream()
                        .filter(r -> alertDelayJobService.canSend(AlertJobDelayModel.AD_CK_PV_ALL_DEV.getJobId()+"_"+r.getProduct()))
                        .collect(Collectors.toList());
                //toutiaosubRate 头条波动率
                //kuaishousubRate 快手波动率
                //guangdiantongsubRate 广点通波动率
                //baidusubRate 百度波动率
                //alisubRate 阿里波动率
                //aqysubRate 爱奇艺波动率
                //sigmobsubRate sigmob波动率
                //vivosubRate vivo波动率
                //opposubRate oppo波动率
                //unknownsubRate 未知广告样式波动率
                //qitasubRate 其他波动率
                List<String> productList = checkBeanList.stream().map(ArpuCheckBean::getProduct).collect(Collectors.toList());
//                String alert = checkBeanList.stream()
//                        .map(r -> String.format("产品(全部设备)：**%s**(%s),os:**%s**,dau:%s,前一小时人均PV：%s," +
//                                                "昨日同时段人均PV：%s,波动率:**%s**,头条波动率:**%s**,快手波动率:**%s**,广点通波动率:**%s**,百度波动率:**%s**,阿里波动率:**%s**," +
//                                                "爱奇艺波动率:**%s**,sigmob波动率:**%s**,vivo波动率:**%s**,oppo波动率:**%s**,未知广告样式波动率:**%s**,其他波动率:**%s**,",r.getProductName(), r.getProduct(),
//                                        r.getOs(),r.getDau(), format(r.getDt1()), format(r.getDt2()), r.getCeilP(r.getSubRate())
//                                        , r.getCeilP(r.getToutiaosubRate()), r.getCeilP(r.getKuaishousubRate()), r.getCeilP(r.getGuangdiantongsubRate()), r.getCeilP(r.getBaidusubRate()), r.getCeilP(r.getAlisubRate())
//                                        , r.getCeilP(r.getAqysubRate()), r.getCeilP(r.getSigmobsubRate()), r.getCeilP(r.getVivosubRate()), r.getCeilP(r.getOpposubRate()), r.getCeilP(r.getUnknownsubRate()), r.getCeilP(r.getQitasubRate())
//                                )
//                        )
//                        .collect(Collectors.joining(";\n\n"));
                for (int i = 0; i < checkBeanList.size(); i++){
                    ArpuCheckBean r = checkBeanList.get(i);
                    String alert = String.format("变现平台：dau:%s,前一小时人均PV：%s," +
                                    "昨日同时段人均PV：%s,波动率:**%s**,头条波动率:**%s**,快手波动率:**%s**,广点通波动率:**%s**,百度波动率:**%s**,阿里波动率:**%s**," +
                                    "爱奇艺波动率:**%s**,sigmob波动率:**%s**,vivo波动率:**%s**,oppo波动率:**%s**,未知广告样式波动率:**%s**,其他波动率:**%s**,",r.getDau(), format(r.getDt1()), format(r.getDt2()), r.getCeilP(r.getSubRate())
                            , r.getCeilP(r.getToutiaosubRate()), r.getCeilP(r.getKuaishousubRate()), r.getCeilP(r.getGuangdiantongsubRate()), r.getCeilP(r.getBaidusubRate()), r.getCeilP(r.getAlisubRate())
                            , r.getCeilP(r.getAqysubRate()), r.getCeilP(r.getSigmobsubRate()), r.getCeilP(r.getVivosubRate()), r.getCeilP(r.getOpposubRate()), r.getCeilP(r.getUnknownsubRate()), r.getCeilP(r.getQitasubRate()));
                            //Sets.newHashSet() 暂时不打电话
                    AlertRecord alertRecord = alertRecordService.insertAlertRecord(AlertJobDelayModel.AD_CK_PV_ALL_DEV.getJobId(),
                            AlertJobDelayModel.AD_CK_PV_ALL_DEV.getJobName(), AlertModel.ARPU_CK,alert
                            , AlertStatus.INIT, DingTailService.bsData, AlertType.DINGDING);

                    DingTailService.sendMarkdownMsgDelay(alertRecord, AlertRoute.SJWT.value,AlertJobDelayModel.AD_CK_PV_ALL_DEV, Lists.newArrayList(productList.get(i)));
                }
            }
        }catch (Exception e){
            XxlJobLogger.log("CheckEx:",e);
        }
    }

    public void checkFilter(){
        try {
            List<UserFilterAdBean> userFilterAdBeans =  clickHouseDwdMapper.queryExInfo();
            List<UserFilterAdBean> rsList =  userFilterAdBeans.stream()
                    .filter(res -> {
                        if ("android".equals(res.getOs())){
                            if (res.getPv() < 5000){
                                return res.getNtfRate() > 80d;
                            }else {
                                return res.getNtfRate() > 65d;
                            }
                        }else {
                            return res.getNtfRate() > 85d;
                        }
                    }).collect(Collectors.toList());
            if (rsList.size() > 0){
                List<UserFilterAdBean> checkBeanList = rsList.stream()
                        .filter(r -> alertDelayJobService.canSend(AlertJobDelayModel.FILTER_CK.getJobId()+"_"+r.getProduct() + "_" + r.getAdId()))
                        .filter(r -> alertDelayJobService.canSend(AlertJobDelayModel.FILTER_CK.getJobId()+"_"+r.getProduct()))
                        .filter(r -> !"zmcash".equals(r.getProduct()) && !"ios".equals(r.getOs()))
                        .collect(Collectors.toList());
                if (checkBeanList.size() > 0){
                    List<String> productList = checkBeanList.stream().map(r->r.getProduct() + "_" + r.getAdId()).collect(Collectors.toList());
                    productList.addAll(checkBeanList.stream().map(UserFilterAdBean::getProduct).collect(Collectors.toList()));
                    String alert = checkBeanList.stream()
                            .map(r -> String.format("> 产品:**%s**(%s),**%s**,广告位:**%s**,PosId:**%s**,AdId：%s,Pv:%s," +
                                            "Income:%s 投放用户数:%s,用户数：%s,非投放用户占比：" +
                                            "**<font face=\"微软雅黑\" color=#FF0000 >%s</font>**",r.getProductName(),r.getProduct(),r.getOs(),
                                    r.getThirdPosName(),r.getPosId(),r.getAdId(),r.getPv(),r.getIncome(),r.getTfView(),r.getUserView(),r.getNtfRate()))
                            .collect(Collectors.joining(";\n\n"));
                    AlertRecord alertRecord = alertRecordService.insertAlertRecord(AlertJobDelayModel.FILTER_CK.getJobId(),
                            AlertJobDelayModel.FILTER_CK.getJobName(), AlertModel.NORMAL,alert
                            , AlertStatus.INIT, DingTailService.bsData, AlertType.DINGDING);
                    DingTailService.sendMarkdownMsgDelay(alertRecord, AlertRoute.BUSINESS.value,AlertJobDelayModel.FILTER_CK,productList);
                }
            }
        }catch (Exception e){
            XxlJobLogger.log("CheckEx:",e);
        }
    }

    public void checkPosECPM(){

    }
}
