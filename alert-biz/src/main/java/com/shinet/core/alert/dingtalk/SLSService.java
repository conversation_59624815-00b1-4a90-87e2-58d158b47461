package com.shinet.core.alert.dingtalk;

import com.alibaba.fastjson.JSONObject;
import com.aliyun.openservices.log.Client;
import com.aliyun.openservices.log.common.LogContent;
import com.aliyun.openservices.log.common.QueriedLog;
import com.aliyun.openservices.log.exception.LogException;
import com.aliyun.openservices.log.response.GetLogsResponse;
import com.shinet.core.alert.data.entity.DataSlsError;
import com.shinet.core.alert.dingtalk.rsp.ErrorSlsLogBean;
import org.apache.commons.lang3.StringUtils;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

public class SLSService {
    //配置AccessKey、服务入口、Project名称、Logstore名称等相关信息。
    //阿里云访问密钥AccessKey。更多信息，请参见访问密钥。阿里云账号AccessKey拥有所有API的访问权限，风险很高。强烈建议您创建并使用RAM用户进行API访问或日常运维。
    static String accessId = "LTAI5tNtC1rEPb8xpDzBoQW5";
    static String accessKey = "******************************";
    //日志服务的服务入口。更多信息，请参见服务入口。
    //此处以杭州为例，其它地域请根据实际情况填写。
    static String host = "cn-beijing.log.aliyuncs.com";
    //创建日志服务Client。 
    static Client client = new Client(host, accessId, accessKey);
    //Project名称。
    static String projectName = "service-log4j2";
    //Logstore名称。
    static String logstoreName = "pro";
    //查询语句。
    static String query = "level: ERROR | SELECT __source__ as service, count(*) as count  from " + logstoreName + " group by __source__ order by count DESC";


    static String queryProjectError = "level: ERROR and __source__:";

    public static List<String> queryLogs(int minNum,String projectName) throws LogException {
        List<String> dlist = new ArrayList<>();
        if(StringUtils.isBlank(projectName)){
            return dlist;
        }
        int fromTime = (int) (System.currentTimeMillis() / 1000 - minNum*60);
        int toTime = fromTime + minNum*60;
        String tqueryError = queryProjectError+projectName;
        GetLogsResponse getLogsResponse = client.GetLogs(projectName, logstoreName, fromTime, toTime, "", tqueryError);
        for (QueriedLog log : getLogsResponse.getLogs()) {
            for (LogContent mContent : log.mLogItem.mContents) {
                if("service".equalsIgnoreCase(mContent.mKey)){
                }

                if("count".equalsIgnoreCase(mContent.mKey)){
                }
                System.out.println(mContent.mKey + " : " + mContent.mValue);
            }
        }
        return dlist;
    }

    //通过SQL查询日志。
    public static List<DataSlsError> queryLogs(int minNum) throws LogException {
        List<DataSlsError> errorSlsLogBeans = new ArrayList<>();
        System.out.println(String.format("ready to query logs from %s", logstoreName));
        //fromTime和toTime表示查询日志的时间范围，Unix时间戳格式。

        int fromTime = (int) (System.currentTimeMillis() / 1000 - minNum*60);
        int toTime = fromTime + minNum*60;
        GetLogsResponse getLogsResponse = client.GetLogs(projectName, logstoreName, fromTime, toTime, "", query);
        for (QueriedLog log : getLogsResponse.getLogs()) {
            DataSlsError dataSlsError = new DataSlsError();
            for (LogContent mContent : log.mLogItem.mContents) {
                if("service".equalsIgnoreCase(mContent.mKey)){
                    dataSlsError.setServiceName(mContent.mValue);
                }

                if("count".equalsIgnoreCase(mContent.mKey)){
                    dataSlsError.setErrorNum(Integer.parseInt(mContent.mValue));
                }
                System.out.println(mContent.mKey + " : " + mContent.mValue);
            }
            dataSlsError.setStartTime(new Date(fromTime*1000L));
            dataSlsError.setEndTime(new Date(toTime*1000L));
            dataSlsError.setCreateTime(new Date());
            dataSlsError.setUpdateTime(new Date());
            errorSlsLogBeans.add(dataSlsError);
            System.out.println("********************");
        }
        return errorSlsLogBeans;
    }

    private static final String queryIosUserErrorCount_Sql = "__source__:bp-user-service and (message:\"系统繁忙，当前无法登录，技术小哥正在努力维护中 iOS\" or message:\"IOS 用户触发锁区- 登录返回\" or message:\"网络链接不稳定，请检查网络后重试 ios\" or message:\"全局拉黑 入口拦截 AppStore\") | select count(*)";

    public static long queryIosUserErrorCount(int fromTime, int toTime) throws LogException {
        GetLogsResponse getLogsResponse = client.GetLogs(projectName, logstoreName, fromTime, toTime, "", queryIosUserErrorCount_Sql);
        if (getLogsResponse == null || getLogsResponse.GetLogs().isEmpty()){
            return 0;
        }
        return getLogsResponse.getProcessedRow();
    }
}