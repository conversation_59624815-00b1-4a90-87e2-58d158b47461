package com.shinet.core.alert.coreservice.service;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.google.common.util.concurrent.SimpleTimeLimiter;
import com.shinet.core.alert.common.HttpClientService;
import com.shinet.core.alert.coreservice.entity.AlertRecord;
import com.shinet.core.alert.coreservice.enums.AlertModel;
import com.shinet.core.alert.coreservice.enums.AlertStatus;
import com.shinet.core.alert.coreservice.enums.AlertType;
import com.shinet.core.alert.dingtalk.DingTailService;
import com.shinet.core.alert.dingtalk.VedioAlertService;
import com.xxl.job.core.log.XxlJobLogger;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.joda.time.DateTimeConstants;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.io.IOException;
import java.util.concurrent.ArrayBlockingQueue;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicReference;

@Component
@Slf4j
public class BpApServiceCheck {
    SimpleTimeLimiter simpleTimeLimiter = SimpleTimeLimiter.create(new ThreadPoolExecutor(
            1,
            1,
            DateTimeConstants.SECONDS_PER_MINUTE * 10,
            TimeUnit.SECONDS,
            new ArrayBlockingQueue(1)
    ));
    int timeOutSeconds = 10;
    @Autowired
    HttpClientService httpClientService;
    @Autowired
    AlertRecordService alertRecordService;
    public void checkBpAp(String jobName){
        AtomicReference<String> alertMsg = new AtomicReference<>("");
        Long dtime = System.currentTimeMillis();
        try {
            simpleTimeLimiter.runWithTimeout(()-> requestBpAp(alertMsg),timeOutSeconds,TimeUnit.SECONDS);
        }catch (Exception e){
            XxlJobLogger.log(e);
            requestBpAp(alertMsg);
        }

        if(StringUtils.isNotBlank(alertMsg.get())){
            XxlJobLogger.log("广告服务异常 "+alertMsg.get());
            VedioAlertService.sendVocMsg("广告 ",alertMsg.get());
            AlertRecord alertRecord = alertRecordService.insertAlertRecord(jobName,"广告health get请求", AlertModel.SERVICEREQ,alertMsg.get(),0d,0d,0,0, AlertStatus.INIT,DingTailService.dset, AlertType.DINGPHONE);
            DingTailService.sendMarkdownMsg(alertRecord);
        }
    }

    public void requestBpAp(AtomicReference<String> alertMsg){
        String reqAuthUrl = "https://bp-ap.shinet.cn/ap-gateway/health/info";
        String rspStr = null;
        try {
            rspStr = httpClientService.sendReq(reqAuthUrl,null);
        } catch (IOException e) {
            log.error("",e);
            alertMsg.set("广告服务异常"+e.getMessage());
        }
        if("-1".equalsIgnoreCase(rspStr)){
            alertMsg.set("广告服务异常"+rspStr);
        }else{
            JSONObject jsonObject = JSON.parseObject(rspStr);
            Integer isAck = jsonObject.getInteger("code");
            log.info("rspStr  "+rspStr);
            if(isAck!=0){
                alertMsg.set("广告服务异常"+rspStr);
            }
        }
    }

}
