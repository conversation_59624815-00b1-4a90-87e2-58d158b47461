package com.shinet.core.alert.clickhouse.mapper.ck1;

import com.shinet.core.alert.adb.entity.AdArpuRealSum;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.shinet.core.alert.adb.entity.AdDauSum;
import com.shinet.core.alert.adb.entity.ProductSrPosEt;
import com.shinet.core.alert.adb.entity.ProductToufangWithdraw;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.math.BigInteger;
import java.util.List;

/**
 * <p>
 *  Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2021-08-03
 */
public interface AdArpuRealSumMapper extends BaseMapper<AdArpuRealSum> {

    @Select("select product, os,logday, `hour`, sum(daux) as dau, sum(incomex) / sum(daux) as arpu,sum(pv)/ sum(daux) as pv " +
            "from ( " +
            "      select product, os, channel,logday, `hour`, sum(income) as incomex, max(dau) as daux ,sum(pv) as pv" +
            "      from old_mysql_ads.ad_arpu_real_sum " +
                "      where logday = '${logday}'  and `hour` = ${dhour} " +
            "      group by product, os, channel,logday, `hour`" +
            "         ) as c " +
            "group by product, os,logday, `hour` order by dau desc limit 15 ")
    List<AdArpuRealSum> queryAdArpu(@Param("logday") String logday, @Param("dhour") String dhour);

    //select hour,sum(key_value) from ads.realtime_au_nu_channel where key_name='nu' and logday='2021-09-10' GROUP BY `hour`;
    @Select("select hour,sum(key_value) as sumVal from old_mysql_ads.realtime_au_nu_channel where key_name='${keyVal}' and logday = '${logday}'  and `hour` = ${dhour}  GROUP BY `hour` ")
    AdDauSum queryAdVal(@Param("logday") String logday, @Param("dhour") String dhour,@Param("keyVal") String keyVal);

    //logday='${logday}'
    @Select("select ff.*,\n" +
            "       posNumT.posNum                                              posNum,\n" +
            "       arrayCount(posNumT.lary)                                    laquCi,\n" +
            "       arrayCount(arrayFilter(x1->x1 <= posNum / 3, posNumT.lary)) minSnum,\n" +
            "       nuNum\n" +
            "from (\n" +
            "         select f.product_name,\n" +
            "                os,\n" +
            "                f.product,\n" +
            "                sum(sr)                as srd,\n" +
            "                (srd / sum(pv)) * 1000 as ecpm,\n" +
            "                arrayMap((x1, x2, x3, x4, x5, x6)->(concat(x1, '@', x2, '@', x3, '@', x4, '@', x5, '@', x6)),\n" +
            "                         groupArray(ifNull(pos_id, '')),\n" +
            "                         groupArray(toString(ifNull(pv, 0))), groupArray(toString(ifNull(adType, ''))),\n" +
            "                         groupArray(toString(ifNull(sr, 0))),\n" +
            "                         groupArray(toString(ifNull(pnum, 0))),\n" +
            "                         groupArray(toString(ifNull(projectNum, 0)))\n" +
            "                    )                  as apl\n" +
            "\n" +
            "         from (\n" +
            "                  select product_name,\n" +
            "                         a.product         product,\n" +
            "                         a.os              os,\n" +
            "                         a.pos_id          pos_id,\n" +
            "                         pv,\n" +
            "                         b.ad_type_name as adType,\n" +
            "                         sr,\n" +
            "                         p.pnum            pnum,\n" +
            "                         p.projectNum      projectNum,\n" +
            "                         pv\n" +
            "                  from (\n" +
            "                           select product, pos_id, os, count(1) as pv, sum(toFloat32OrZero(extend1)) / 1000 as sr\n" +
            "                           from ods.event_exposure_dist\n" +
            "                           where logday = '${logday}'\n" +
            "                             and ad_action = 'exposure'\n" +
            "                             and product global in (select product from dwd.product_map_dist where id > 800)\n" +
            "                           group by product, os, pos_id\n" +
            "                           having sr > 0\n" +
            "                           ) a\n" +
            "                           left join (select pos_id, count(1) as pnum, count(distinct c.product) as projectNum\n" +
            "                                      from dwd.product_ad_conf_dist c\n" +
            "                                      group by pos_id) p on p.pos_id = a.pos_id\n" +
            "                           left join dwd.product_ad_conf_dist b on a.pos_id = b.pos_id and a.product = b.product\n" +
            "\n" +
            "                  ) f\n" +
            "         group by f.product_name, os, f.product\n" +
            "         order by srd desc\n" +
            "         ) ff\n" +
            "         left join (select product,\n" +
            "                           os,\n" +
            "                           avg(toUInt8OrZero(element_uri)) as     posNum,\n" +
            "                           count(1)                        as     cnum,\n" +
            "                           groupArray(toUInt8OrZero(element_uri)) lary\n" +
            "                    from ods.event_dist\n" +
            "                    where logday = today()\n" +
            "                      and ad_action = 'video_cache_finish'\n" +
            "                      and element_name = '0'\n" +
            "                    group by product, os\n" +
            "    ) posNumT on posNumT.product = ff.product and posNumT.os = ff.os\n" +
            "         left join (\n" +
            "    select product, os, uniqExact(device_id) as nuNum\n" +
            "    from dwd.nu_device_dist\n" +
            "    where logday = '${logday}'\n" +
            "    group by product, os\n" +
            "    ) df on posNumT.product = df.product and posNumT.os = df.os\n" +
            ";")
    List<ProductSrPosEt> queryPosSr(@Param("logday") String logday);


    //logday='${logday}'
    @Select("select  a.product_name product_name, a.os os, a.logday logday, a.toufang_cost toufang_cost, c.withdraw_cost withdraw_cost\n" +
            "from (\n" +
            "         select product_name, os, sum(cost) as toufang_cost, logday\n" +
            "         from ads.realtime_advertiser_cost\n" +
            "         where logday = '${logday}'\n" +
            "         group by product_name, logday, os\n" +
            "         order by toufang_cost desc\n" +
            "         ) a\n" +
            "         left join\n" +
            "     (select product, product_name, os, logday, sum(amount) / 100 as withdraw_cost\n" +
            "      from dwd.withdraw_dist\n" +
            "      where logday = '${logday}'\n" +
            "      group by product, product_name, logday, os) c\n" +
            "     on a.product_name = c.product_name and a.os = c.os;")
    List<ProductToufangWithdraw> queryToufangWithdraw(@Param("logday") String logday);
}
