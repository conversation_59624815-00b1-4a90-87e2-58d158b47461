package com.shinet.core.alert.clickhouse.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import java.util.Date;
import com.baomidou.mybatisplus.annotation.TableId;
import java.io.Serializable;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR>
 * @since 2020-07-13
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value="ToutiaoClick对象", description="")
public class ToutiaoClick implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    @ApiModelProperty(value = "xiaoxiaole")
    private String dsp;

    @ApiModelProperty(value = "toutiao")
    private String product;

    @ApiModelProperty(value = "ios android")
    private String os;

    @ApiModelProperty(value = "账户ID")
    private String accountId;

    @ApiModelProperty(value = "ocpcid")
    private String ocpcDeviceId;

    @ApiModelProperty(value = "时间戳")
    private String ts;

    @ApiModelProperty(value = "账户名称")
    private String accountName;

    private String callbackUrl;

    @ApiModelProperty(value = "创意ID")
    private String cid;

    @ApiModelProperty(value = "广告组ID")
    private String gid;

    @ApiModelProperty(value = "计划ID")
    private String pid;

    @ApiModelProperty(value = "Android Q及更高版本的设备号")
    private String oaid;

    @ApiModelProperty(value = "oaid加md5")
    private String oaid2;

    @ApiModelProperty(value = "计划名称")
    private String aidName;

    @ApiModelProperty(value = "分组名称")
    private String groupName;

    @ApiModelProperty(value = "创意名称")
    private String cidName;

    private Integer activateCount;

    private Date createTime;

    private Date updateTime;

    /**
     *
     对 MAC 去除分隔符之后进行 MD5
     */
    @ApiModelProperty(value = "mac地址")
    private String mac;

    @ApiModelProperty(value = "渠道包")
    private String pkgChannel;

    @ApiModelProperty(value = "广告位编码")
    private String unionSite;

    private String androidId;
}
