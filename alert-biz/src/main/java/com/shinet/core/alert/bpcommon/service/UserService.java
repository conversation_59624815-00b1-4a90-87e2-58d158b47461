package com.shinet.core.alert.bpcommon.service;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.shinet.core.alert.bpcommon.entity.User;
import com.shinet.core.alert.bpcommon.mapper.UserMapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.shinet.core.alert.dataagent.entity.UserDel;
import com.shinet.core.alert.dataagent.entity.UserMetaDel;
import com.shinet.core.alert.dataagent.service.UserDelService;
import lombok.extern.slf4j.Slf4j;
import org.joda.time.DateTimeConstants;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
* <p>
    * 用户基础信息表 服务实现类
    * </p>
*
* <AUTHOR>
* @since 2021-11-26
*/
@Service
@Slf4j
public class UserService extends ServiceImpl<UserMapper, User> {
    @Autowired
    UserDelService userDelService;
    @Autowired
    WechatAppService wechatAppService;
    @Autowired
    UserMetaService userMetaService;
    public int removeAndSaveUser(List<Long> userIdFSet, int pkgId,boolean isDelStart) {
        if(userIdFSet==null || userIdFSet.size()==0){
            return 0;
        }
        List<Long>  userIdSet = userMetaService.getNotContainUsers(userIdFSet,pkgId);
        if(userIdSet.size()==0){
            return 0;
        }
        QueryWrapper<User> userQueryWrapper = new QueryWrapper<User>();
        userQueryWrapper.lambda().in(User::getId, userIdSet);
        List<User> userList = list(userQueryWrapper);


        List<User> userListRemove = userList.stream().filter(user -> user.getCreateTime() < (System.currentTimeMillis() - DateTimeConstants.MILLIS_PER_DAY * 60)).collect(Collectors.toList());
        if (userListRemove.size() > 0) {
            List<UserDel> userDelListSave = userListRemove.stream().map(user -> {
                UserDel userDel = new UserDel();
                BeanUtils.copyProperties(user, userDel);
                return userDel;
            }).collect(Collectors.toList());
            if (userDelListSave.size() > 0) {
                QueryWrapper<UserDel> delSaveWrapper = new QueryWrapper<UserDel>();
                delSaveWrapper.lambda().in(UserDel::getId, userIdSet);
                log.info("开始保存user "+userDelListSave.size());
                boolean isSaveSuc = false;
                int saveNum = userDelListSave.size();
                if(userDelService.count(delSaveWrapper)==0){
                    isSaveSuc = userDelService.saveBatch(userDelListSave);
                }else{
                    List<UserDel> dbUserDelList = userDelService.list(delSaveWrapper);
                    Map<Long,UserDel> delMap = dbUserDelList.stream().collect(Collectors.toMap(UserDel::getId, Function.identity(),(t1, t2)->t1));
                    List<UserDel> userDelSaveList = userDelListSave.stream().filter(userDel -> delMap.get(userDel.getId())==null).collect(Collectors.toList());
                    isSaveSuc = userDelService.saveBatch(userDelSaveList);
                    saveNum = userDelSaveList.size();
                }
                log.info("保存user 完成 "+saveNum);
                if (isSaveSuc) {
                    List<Long> uiddelList = userDelListSave.stream().map(UserDel::getId).collect(Collectors.toList());
                    if(isDelStart && uiddelList.size()<10000 && uiddelList.size()>0){
                        removeByIds(uiddelList);
                        log.info("已经删除 "+uiddelList.size()+" 条user数据");
                    }
                    return uiddelList.size();
                }
            }
        }else{
            log.info("user数据已经迁移完成，直接忽略");
        }
        return 0;
    }
}
