package com.shinet.core.alert.safe.entity;

import lombok.Data;

/**
 * iOS锁区统计数据VO
 * 包含CAID和IP两个维度的锁定统计信息
 */
@Data
public class IosLockStatsVo {

    /** 产品名称 */
    private String product;

    /** 锁定的CAID数量 */
    private Long lockCaidNum;

    /** 总CAID数量 */
    private Long totalCaidNum;

    /** 锁定的IP数量 */
    private Long lockIpNum;

    /** 总IP数量 */
    private Long totalIpNum;

    /**
     * 计算CAID锁定比率
     * @return 锁定比率，保留2位小数
     */
    public Double getCaidLockRate() {
        if (totalCaidNum == null || totalCaidNum == 0) {
            return 0.0;
        }
        double rate = (lockCaidNum != null ? lockCaidNum : 0) * 100.0 / totalCaidNum;
        return Math.round(rate * 100.0) / 100.0; // 保留2位小数
    }

    /**
     * 计算IP锁定比率
     * @return 锁定比率，保留2位小数
     */
    public Double getIpLockRate() {
        if (totalIpNum == null || totalIpNum == 0) {
            return 0.0;
        }
        double rate = (lockIpNum != null ? lockIpNum : 0) * 100.0 / totalIpNum;
        return Math.round(rate * 100.0) / 100.0; // 保留2位小数
    }
}
