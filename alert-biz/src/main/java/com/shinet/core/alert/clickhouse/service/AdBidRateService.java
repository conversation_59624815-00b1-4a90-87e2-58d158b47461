package com.shinet.core.alert.clickhouse.service;

import com.shinet.core.alert.clickhouse.entity.AdBidRate;
import com.shinet.core.alert.clickhouse.mapper.ck1.AdBidRateMapper;
import com.shinet.core.alert.coreservice.entity.AlertRecord;
import com.shinet.core.alert.coreservice.enums.AlertModel;
import com.shinet.core.alert.coreservice.enums.AlertRoute;
import com.shinet.core.alert.coreservice.enums.AlertStatus;
import com.shinet.core.alert.coreservice.enums.AlertType;
import com.shinet.core.alert.coreservice.service.AlertRecordService;
import com.shinet.core.alert.dingtalk.DingTailService;
import com.shinet.core.alert.util.DateUtils;
import com.shinet.core.alert.util.DoubleUtil;
import com.xxl.job.core.log.XxlJobLogger;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

@Service
@Slf4j
public class AdBidRateService {
    @Autowired
    AdBidRateMapper adBidRateMapper;
    @Autowired
    AlertRecordService alertRecordService;
    @Value("${bid.income.no.check.products:[\"悠悠庭院\"]}")
    List noCheckProducts;

    Set<String> dset = new HashSet<>();
    {
        dset.add("悠悠庭院");
    }
    public void alertBidRate(String jobName,Double iosLimitRate,Double androidLimitRate){
        String tdate = DateUtils.formatDateForYMD(new Date());
        List<AdBidRate>  adBidRateList = adBidRateMapper.queryAdArpu(tdate);

        String altMsg = "";
        for(AdBidRate adBidRate : adBidRateList){
            if(noCheckProducts.contains(adBidRate.getProductName())){
                continue;
            }

            Double lrate = 0d;
            if(StringUtils.equalsIgnoreCase("android",adBidRate.getOs())){
                lrate = androidLimitRate;
            }else{
                lrate = iosLimitRate;
            }
            if(adBidRate.getBidRate()<lrate && !dset.contains(adBidRate.getProductName())){
                altMsg = altMsg +","+adBidRate.getProductName()+" "+
                        adBidRate.getOs()+" "+
                        "("+DoubleUtil.getNoFloat(adBidRate.getBidincom())+"/"+DoubleUtil.getNoFloat(adBidRate.getAllincom())+")="+ DoubleUtil.getDoubleByTwo(adBidRate.getBidRate());
            }
        }

        if(StringUtils.isNotBlank(altMsg)){
            AlertRecord alertRecord = alertRecordService.insertAlertRecord(jobName,
                    jobName, AlertModel.bidRateLimit, altMsg
                    , AlertStatus.INIT, DingTailService.dset, AlertType.DINGDING);
            DingTailService.sendMarkdownMsg(alertRecord, AlertRoute.JISHU.value);

            XxlJobLogger.log("bid收入占比异常  "+altMsg);
        }
    }
}
