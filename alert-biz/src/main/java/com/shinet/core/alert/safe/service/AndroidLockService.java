package com.shinet.core.alert.safe.service;

import cn.hutool.core.collection.CollUtil;
import com.alibaba.fastjson.JSON;
import com.shinet.core.alert.clickhouse.mapper.ck1.IosLockMapper;
import com.shinet.core.alert.coreservice.entity.AlertRecord;
import com.shinet.core.alert.coreservice.enums.AlertModel;
import com.shinet.core.alert.coreservice.enums.AlertRoute;
import com.shinet.core.alert.coreservice.enums.AlertStatus;
import com.shinet.core.alert.coreservice.enums.AlertType;
import com.shinet.core.alert.coreservice.service.AlertRecordService;
import com.shinet.core.alert.dingtalk.DingTailService;
import com.shinet.core.alert.dsp.service.ProductService;
import com.shinet.core.alert.safe.entity.AndoridALock;
import com.shinet.core.alert.safe.entity.AndoridLockLook;
import com.shinet.core.alert.safe.mapper.IosCheckUrlMapper;
import com.shinet.core.alert.util.DoubleUtil;
import com.shinet.core.alert.util.IntegerUtils;
import com.xxl.job.core.log.XxlJobLogger;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.util.List;

@Slf4j
@Component
public class AndroidLockService {
    @Autowired
    IosCheckUrlMapper iosCheckUrlMapper;
    @Autowired
    AlertRecordService alertRecordService;

    @Autowired
    ProductService productService;

    @Resource
    private IosLockMapper ck1LockMapper;

    public void alertStoreAb(String jobName,Integer maxLockNum){
        List<AndoridLockLook> andoridLockLooks = iosCheckUrlMapper.getLockOaidNums();

        String oaidIpNumMsg = "";
        for(AndoridLockLook andoridLockLook : andoridLockLooks){
            String storeName = andoridLockLook.getProduct();
            if(andoridLockLook.getLockUnum()>maxLockNum){
                oaidIpNumMsg = oaidIpNumMsg+"("+storeName+"-"+andoridLockLook.getLockUnum()+"-"+andoridLockLook.getLockOaidNum()+"-"+andoridLockLook.getLockIpNum()+")";
            }
        }
        if(StringUtils.isNotBlank(oaidIpNumMsg)){
            oaidIpNumMsg = "商店锁过多 lockNum-oaid-ip \r\n "+oaidIpNumMsg;


            AlertRecord alertRecord = alertRecordService.insertAlertRecord(jobName,jobName, AlertModel.androidlock, oaidIpNumMsg, AlertStatus.INIT, DingTailService.dset, AlertType.DINGDING);
            DingTailService.sendMarkdownMsg(alertRecord, AlertRoute.JISHU.value);
            XxlJobLogger.log(oaidIpNumMsg);
        }

        XxlJobLogger.log("锁数据为 "+ JSON.toJSONString(andoridLockLooks));

    }


    public void onlineStoreAlt(String jobName,Integer maxLockNum){
        AndoridALock andoridLockLook = iosCheckUrlMapper.getAlLockAlt();

        String oaidIpNumMsg = "";
        String product = andoridLockLook.getProduct();
        if(andoridLockLook.getLockIpNum()>maxLockNum){
            oaidIpNumMsg = oaidIpNumMsg+"("+product+"-"+andoridLockLook.getLockIpNum()+"-"+andoridLockLook.getApp_version()+"-"+andoridLockLook.getChannel()+")";
        }
        if(StringUtils.isNotBlank(oaidIpNumMsg)){
            oaidIpNumMsg = "商店已上架 需要关锁区 \r\n "+oaidIpNumMsg;


            AlertRecord alertRecord = alertRecordService.insertAlertRecord(jobName,jobName, AlertModel.androidlock, oaidIpNumMsg, AlertStatus.INIT, DingTailService.dset, AlertType.DINGDING);
            DingTailService.sendMarkdownMsg(alertRecord, AlertRoute.JISHU.value);
            XxlJobLogger.log(oaidIpNumMsg);
        }

        XxlJobLogger.log("商店已上架 需要关锁区 "+ JSON.toJSONString(andoridLockLook));

    }
    public void altAndroidLock(String jobName,Integer maxLockNum,Integer minLockNum,Double lockRateBj){
        List<AndoridLockLook> andoridLockLooks = iosCheckUrlMapper.getLockInfos();

        boolean isSendDl = false;
        for(AndoridLockLook andoridLockLook : andoridLockLooks){
            String oaidIpNumMsg = "";
            String lockNumMsg = "";
            String lockRateMsg = "";
            String product = andoridLockLook.getProduct();

            Integer lockIpNum = IntegerUtils.add(andoridLockLook.getLockIpNum(),0);
            Integer lockOaidNum = IntegerUtils.add(andoridLockLook.getLockOaidNum(),0);
            Integer lockUnum = IntegerUtils.add(andoridLockLook.getLockUnum(),0);
            Integer unlockIpNum = IntegerUtils.add(andoridLockLook.getUnlockIpNum(),0);
            Integer unlockOaidNum = IntegerUtils.add(andoridLockLook.getUnlockOaidNum(),0);
            Integer unlockUnum = IntegerUtils.add(andoridLockLook.getUnlockUnum(),0);

            Integer oaidNum = IntegerUtils.add(andoridLockLook.getUnlockOaidNum(),andoridLockLook.getLockOaidNum());
            Integer ipNum = IntegerUtils.add(andoridLockLook.getLockIpNum(),andoridLockLook.getUnlockIpNum());
//            if(oaidNum<ipNum && (ipNum>1000 || oaidNum>1000)){
//                Double queRate = DoubleUtil.divideDouble(IntegerUtils.add(ipNum,-oaidNum)*1d,ipNum*1d);
//                if(queRate>0.1){
//                    oaidIpNumMsg = oaidIpNumMsg+"("+productService.getProductRemarkByName(product) + ":" + product+"-"+oaidNum+"-"+ipNum+")";
//                }
//            }

//            lockNumMsg = lockNumMsg+"("+productService.getProductRemarkByName(product) + ":" + product+"-"+lockIpNum+"/"+unlockIpNum+"-"+lockOaidNum+"/"+unlockOaidNum+"-"+lockUnum+"/"+unlockUnum+")";
//            if(lockIpNum>maxLockNum || lockOaidNum>maxLockNum || lockUnum>maxLockNum){
//                if(lockIpNum>1000 || lockOaidNum>1000|| lockUnum>1000){
//                    isSendDl = true;
//                }
//            }

            Double lockRate = DoubleUtil.divideDouble(lockOaidNum,oaidNum);
            if (lockRate > lockRateBj && lockOaidNum > minLockNum) {
                lockRateMsg = lockRateMsg + "(" + productService.getProductRemarkByName(product) + ":" + product + "-" + lockRate + "-ip:" + lockIpNum + "/" + unlockIpNum + "-oaid:" + lockOaidNum + "/" + unlockOaidNum + "-userId:" + lockUnum + "/" + unlockUnum + ")";
            }
            boolean isSend = false;
            if(StringUtils.isNotBlank(oaidIpNumMsg)){
                oaidIpNumMsg = "oaid ip个数不一致"+oaidIpNumMsg;
//            isSend = isSend || true;
            }
            if(StringUtils.isNotBlank(lockNumMsg)){
                lockNumMsg = "lock用户太多"+lockNumMsg;
//            isSend = isSend || true;
            }
            if(StringUtils.isNotBlank(lockRateMsg)){
                lockRateMsg = "lock率太高: "+lockRateMsg;
                isSend = true;
                isSendDl = true;
            }

            if(isSend){
                XxlJobLogger.log("开始发送锁区预警 "+lockRateMsg+oaidIpNumMsg+lockNumMsg);

                String altMsg = lockRateMsg+"\r\n"+oaidIpNumMsg+"\r\n"+lockNumMsg;
                AlertRecord alertRecord = alertRecordService.insertAlertRecord(jobName,jobName, AlertModel.androidlock, altMsg, AlertStatus.INIT, DingTailService.dset, AlertType.DINGDING);

                DingTailService.sendMarkdownMsg(alertRecord, AlertRoute.JISHU.value);
                if(isSendDl){
                    DingTailService.sendMarkdownMsg(alertRecord, AlertRoute.SUOQU.value);
                }
            }
        }

        System.out.println(andoridLockLooks);
    }

    public void altAndroidLockForNewUser() {
//    public void altAndroidLockForNewUser(String jobName, Integer maxLockNum, Integer minLockNum, Double lockRateBj) {
        Integer minLockNum = 500;
        Double lockRateBj = 0.05;
        String jobName = "android-lock-alert";

        List<AndoridLockLook> androidLocks = ck1LockMapper.getAndroidLockInfo(minLockNum);

        if (CollUtil.isEmpty(androidLocks)) {
            XxlJobLogger.log("无锁区报警");
            return;
        }

        for (AndoridLockLook andoridLockLook : androidLocks) {

            String product = andoridLockLook.getProduct();
            Integer lockIpNum = IntegerUtils.add(andoridLockLook.getLockIpNum(), 0);
            Integer ipNum = IntegerUtils.add(andoridLockLook.getIpNum(), 0);
            Integer lockOaidNum = IntegerUtils.add(andoridLockLook.getLockOaidNum(), 0);
            Integer oaidNum = IntegerUtils.add(andoridLockLook.getOaidNum(), 0);
            Integer lockUnum = IntegerUtils.add(andoridLockLook.getLockUnum(), 0);
            Integer userNum = IntegerUtils.add(andoridLockLook.getUserNum(), 0);

            Double lockRate = DoubleUtil.divideDouble(lockOaidNum, oaidNum);
            if (lockRate > lockRateBj && lockOaidNum > minLockNum) {
                String lockRateMsg = "lock率太高: " + "(" + productService.getProductRemarkByName(product) + ":" + product + "-" + lockRate + "-ip:" + lockIpNum + "/" + ipNum + "-oaid:" + lockOaidNum + "/" + oaidNum + "-userId:" + lockUnum + "/" + userNum + ")";
                AlertRecord alertRecord = alertRecordService.insertAlertRecord(jobName, jobName, AlertModel.androidlockNewUser, lockRateMsg, AlertStatus.INIT, DingTailService.dset, AlertType.DINGDING);

                DingTailService.sendMarkdownMsg(alertRecord, AlertRoute.JISHU.value);
                DingTailService.sendMarkdownMsg(alertRecord, AlertRoute.SUOQU.value);
            }
        }
    }
}
