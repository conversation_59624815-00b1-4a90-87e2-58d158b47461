package com.shinet.core.alert.dsp.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.shinet.core.alert.dsp.entity.KuaishouStarTask;
import com.shinet.core.alert.dsp.entity.KuaishouSupplementOrderRealtime;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.math.BigInteger;
import java.util.List;
import java.util.Set;

public interface KuaishouSupplementOrderRealtimeMapper extends BaseMapper<KuaishouSupplementOrderRealtime> {

    @Select("<script>" +
            "SELECT task_id, SUM(consume_amount) / 1000 AS sum_cost " +
            "FROM kuaishou_supplement_order_realtime " +
            "WHERE advertiser_id = #{advertiserId} " +
            "  AND logday BETWEEN #{preDay} AND #{nowDay} " +
            "  <if test='taskIdList != null and !taskIdList.isEmpty()'> " +
            "    AND task_id IN " +
            "    <foreach item='item' collection='taskIdList' open='(' separator=',' close=')'> " +
            "      #{item} " +
            "    </foreach> " +
            "  </if> " +
            "GROUP BY task_id " +
            "HAVING sum_cost > 0" +
            "</script>")
    List<KuaishouSupplementOrderRealtime> queryTaskCost(
            @Param("advertiserId") String advertiserId,
            @Param("taskIdList") Set<BigInteger> taskIdList,
            @Param("preDay") String preDay,
            @Param("nowDay") String nowDay
    );


    @Select("<script>" +
            "SELECT " +
            "  COALESCE(q.star_user_id, w.user_id) AS star_user_id, " +  // 统一字段名
            "  COALESCE(q.star_name, w.star_name) AS star_name, " +
            "  (COALESCE(q.sum_cost, 0) + COALESCE(w.sum_cost, 0)) AS sum_cost " +
            "FROM (" +
            "  SELECT " +
            "    star_user_id, " +
            "    star_name, " +
            "    SUM(consume_amount) / 1000 AS sum_cost " +
            "  FROM kuaishou_supplement_order_realtime " +
            "  WHERE advertiser_id = #{advertiserId} " +
            "    AND logday BETWEEN #{preDay} AND #{nowDay} " +
            "    AND task_id IN " +
            "    <foreach collection='taskIdList' item='taskId' open='(' separator=',' close=')'>" +  // 动态生成IN子句
            "      #{taskId}" +
            "    </foreach>" +
            "  GROUP BY star_user_id, star_name" +
            ") AS q " +
            "LEFT JOIN (" +  // 改用LEFT JOIN保留全量数据
            "  SELECT " +
            "    a.user_id, " +
            "    a.star_name, " +
            "    (COALESCE(a.sum_amount, 0) - COALESCE(b.sum_amount, 0)) / 1000 AS sum_cost " +  // 处理NULL值
            "  FROM (" +
            "    SELECT " +
            "      user_id, " +
            "      star_name, " +
            "      SUM(amount) AS sum_amount " +
            "    FROM kuaishou_star_order " +
            "    WHERE advertiser_id = #{advertiserId} " +
            "      AND order_type = 1 " +
            "      AND task_id IN " +
            "      <foreach collection='taskIdList' item='taskId' open='(' separator=',' close=')'>" +  // 动态生成IN子句
            "        #{taskId}" +
            "      </foreach>" +
            "      AND crawl_date BETWEEN #{preDay} AND #{nowDay} " +
            "    GROUP BY user_id, star_name" +
            "  ) AS a " +
            "  LEFT JOIN (" +
            "    SELECT " +
            "      user_id, " +
            "      star_name, " +
            "      SUM(amount) AS sum_amount " +
            "    FROM kuaishou_star_order " +
            "    WHERE advertiser_id = #{advertiserId} " +
            "      AND order_type = 1 " +
            "      AND task_id IN " +
            "      <foreach collection='taskIdList' item='taskId' open='(' separator=',' close=')'>" +
            "        #{taskId}" +
            "      </foreach>" +
            "      AND crawl_date BETWEEN #{preDay} AND #{nowDay} " +  // 修正日期范围为preDay之前
            "    GROUP BY user_id, star_name" +
            "  ) AS b ON a.user_id = b.user_id AND a.star_name = b.star_name" +
            ") AS w " +
            "ON q.star_user_id = w.user_id AND q.star_name = w.star_name " +
            "ORDER BY sum_cost DESC " +
            "LIMIT 5" +
            "</script>")
    List<KuaishouSupplementOrderRealtime> queryUserCost(
            @Param("advertiserId") String advertiserId,
            @Param("taskIdList") Set<BigInteger> taskIdList,  // 参数名修正为taskIdList
            @Param("preDay") String preDay,
            @Param("nowDay") String nowDay
    );


    @Select("<script>" +
            "SELECT a.user_id as star_user_id, a.star_name, (a.sum_cost + IFNULL(b.sum_cost, 0)) AS sum_cost " +
            "FROM (SELECT user_id, star_name, SUM(max_cost) AS sum_cost " +
            "      FROM (SELECT user_id, star_name, MAX(amount) / 1000 AS max_cost " +
            "            FROM `coo-dispense`.kuaishou_star_order " +
            "            WHERE advertiser_id = #{advertiserId} " +
            "              AND task_id IN " +
            "             <foreach collection='taskIdList' item='taskId' open='(' separator=',' close=')'>" +
            "               #{taskId} " +
            "             </foreach>" +
            "            GROUP BY order_id, user_id, star_name) AS c " +
            "      GROUP BY user_id, star_name) AS a " +
            "LEFT JOIN (SELECT star_user_id, star_name, SUM(consume_amount) / 1000 AS sum_cost " +
            "           FROM `coo-dispense`.kuaishou_supplement_order_realtime " +
            "           WHERE logday > #{preDay} " +
            "             AND advertiser_id = #{advertiserId} " +
            "             AND task_id IN " +
            "             <foreach collection='taskIdList' item='taskId' open='(' separator=',' close=')'>" +
            "               #{taskId} " +
            "             </foreach>" +
            "           GROUP BY star_user_id, star_name) AS b " +
            "ON a.user_id = b.star_user_id AND a.star_name = b.star_name" +
            "</script>")
    List<KuaishouSupplementOrderRealtime> queryUserCost2(
            @Param("advertiserId") String advertiserId, // 注意参数名拼写是否一致
            @Param("taskIdList") Set<BigInteger> taskIdList,
            @Param("preDay") String preDay
    );
}
