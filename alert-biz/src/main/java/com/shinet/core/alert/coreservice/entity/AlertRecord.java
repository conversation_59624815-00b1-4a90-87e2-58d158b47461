package com.shinet.core.alert.coreservice.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import java.util.Date;
import com.baomidou.mybatisplus.annotation.TableId;
import java.io.Serializable;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR>
 * @since 2021-10-27
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value="AlertRecord对象", description="")
public class AlertRecord implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    private String jobName;

    private String jobNameCn;

    private String modelName;

    private Integer alertStatus;

    private Integer hour;

    private String logday;

    private Integer successNum;

    private Integer failedNum;

    private Double failRate;

    private Double setRate;

    private String msgType;

    private String alertMsg;

    private String alertPhone;

    private String alertName;

    private Date createTime;

    private Date updateTime;


}
