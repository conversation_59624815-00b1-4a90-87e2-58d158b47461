package com.shinet.core.alert.dsp.service;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.google.common.collect.Lists;
import com.shinet.core.alert.coreservice.entity.AlertRecord;
import com.shinet.core.alert.coreservice.enums.AlertModel;
import com.shinet.core.alert.coreservice.enums.AlertStatus;
import com.shinet.core.alert.coreservice.enums.AlertType;
import com.shinet.core.alert.coreservice.service.AlertRecordService;
import com.shinet.core.alert.dingtalk.DingTailService;
import com.shinet.core.alert.dsp.entity.UserEvent;
import com.shinet.core.alert.dsp.entity.VideoMaterialItem;
import com.shinet.core.alert.dsp.mapper.VideoMaterialItemMapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.shinet.core.alert.util.DateUtils;
import com.shinet.core.alert.util.DoubleUtil;
import com.xxl.job.core.log.XxlJobLogger;
import org.joda.time.DateTimeConstants;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.Date;

/**
* <p>
    *  服务实现类
    * </p>
*
* <AUTHOR>
* @since 2022-09-23
*/
@Service
public class VideoMaterialItemService extends ServiceImpl<VideoMaterialItemMapper, VideoMaterialItem> {
    @Autowired
    AlertRecordService alertRecordService;

    public void alertMaterialItem(int lowCreateNum,int lv,int lowHourOpt){
        Date startDate = DateUtils.getDayBeginDate(new Date());

        int tipsVideosNum = baseMapper.selectCount(getQw(true,startDate));
        int toTipsNum = baseMapper.selectCount(getQw(false,startDate));


        Date last30dATE = new Date(System.currentTimeMillis()-30* DateTimeConstants.MILLIS_PER_MINUTE);
        int last30Num = baseMapper.selectCount(getGtQw(true,last30dATE));
        Double realLv = DoubleUtil.getDoubleByTwo((toTipsNum*1.0d)/(tipsVideosNum*1.0d)*100d);
        String msg = "视频提示语拼接 积压"+toTipsNum+" 已完成 "+tipsVideosNum+" 最近30min完成"+last30Num;

        if(last30Num>lowHourOpt){
            XxlJobLogger.log("30min内处理条数大于"+lowHourOpt+"条 不预警"+msg);
        }

        if((toTipsNum>lowCreateNum || realLv>lv) && last30Num<lowHourOpt){
            AlertRecord alertRecord = alertRecordService.insertAlertRecord("alert-videoTips","视频提示语", AlertModel.VIDEOTIPSEXP,
                    msg, AlertStatus.INIT, DingTailService.dset, AlertType.DINGDING);
            DingTailService.sendMarkdownMsg(alertRecord);
//            VedioAlertService.sendVocMsg("新建计划积压 ",alertMsg);

            XxlJobLogger.log("预警开始 "+msg);
        }else{
            XxlJobLogger.log(msg);
        }
    }

    private QueryWrapper getGtQw(boolean isComplete,Date startDate){
        QueryWrapper<VideoMaterialItem> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda()
                .gt(VideoMaterialItem::getLastUploadTime,startDate)
                .in(VideoMaterialItem::getVideoType, Lists.asList(2,new Integer[]{4,6,7,8,9}))
                .isNotNull(VideoMaterialItem::getOriginVideoUrl).eq(VideoMaterialItem::getAddTipsSwitch,1);

        if(isComplete){
            queryWrapper.lambda().ne(VideoMaterialItem::getVideoUrl,"");
        }else{
            queryWrapper.lambda().eq(VideoMaterialItem::getVideoUrl,"");
        }
        return queryWrapper;
    }

    private QueryWrapper getQw(boolean isComplete,Date startDate){
        QueryWrapper<VideoMaterialItem> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda()
                .ge(VideoMaterialItem::getLastUploadTime,startDate)
                .in(VideoMaterialItem::getVideoType, Lists.asList(2,new Integer[]{4,6,7,8,9}))
                .isNotNull(VideoMaterialItem::getOriginVideoUrl).eq(VideoMaterialItem::getAddTipsSwitch,1);

        if(isComplete){
            queryWrapper.lambda().ne(VideoMaterialItem::getVideoUrl,"");
        }else{
            queryWrapper.lambda().eq(VideoMaterialItem::getVideoUrl,"");
        }
        return queryWrapper;
    }
}
