package com.shinet.core.alert.coreservice.service;

import com.baomidou.mybatisplus.extension.service.additional.query.impl.LambdaQueryChainWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.shinet.core.alert.coreservice.entity.AlertMall;
import com.shinet.core.alert.coreservice.entity.AlertRecord;
import com.shinet.core.alert.coreservice.enums.AlertModel;
import com.shinet.core.alert.coreservice.enums.AlertStatus;
import com.shinet.core.alert.coreservice.enums.AlertType;
import com.shinet.core.alert.coreservice.mapper.AlertMallMapper;
import com.shinet.core.alert.dingtalk.DingTailService;
import com.shinet.core.alert.dingtalk.VedioAlertService;
import com.shinet.core.alert.mall.mapper.MallMapper;
import com.shinet.core.alert.util.DateUtils;
import com.xxl.job.core.log.XxlJobLogger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * <p>
 * 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-09-23
 */
@Service
public class AlertMallService extends ServiceImpl<AlertMallMapper, AlertMall> {
    @Autowired
    MallMapper mallMapper;
    @Autowired
    AlertRecordService alertRecordService;
    public void insertMallAlertChart(String jobName,double errorFloat ) {
        SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyyMMdd");
        Date date = new Date();
        String tend = simpleDateFormat.format(date);
        List<AlertMall> alertMalls = mallMapper.queryAlertMallSuc(tend);

        if (alertMalls.size() > 0) {
            LambdaQueryChainWrapper<AlertMall> lambdaQueryChainWrapper = this.lambdaQuery().eq(AlertMall::getLogday, DateUtils.formatDateForYMD(date));
            Set<Integer> idSet = lambdaQueryChainWrapper.list().stream().map(AlertMall::getId).collect(Collectors.toSet());
            if(idSet.size()>0){
                this.removeByIds(idSet);
                XxlJobLogger.log("删除 " + idSet.size() + " 历史数据成功");
            }
            alertMalls.forEach(alertMall -> {
                try {
                    alertMall.setCreateTime(date);
                    alertMall.setUpdateTime(date);
                    alertMall.setHour(Integer.parseInt(alertMall.getLogday().split(" ")[1]));
                    alertMall.setLogday(alertMall.getLogday().split(" ")[0]);

                    if(date.getHours()==alertMall.getHour()){
                        if(alertMall.getFailRate()>errorFloat){
                            String alertMsg = "提现失败率大于"+errorFloat+" -> "+alertMall.getFailRate()+"成功为："+alertMall.getSuccessNum() +"失败为："+alertMall.getFailedNum();
                            VedioAlertService.sendVocMsg("提现 ",alertMsg);
                            AlertRecord alertRecord = alertRecordService.insertAlertRecord(jobName,"提现失败延迟", AlertModel.MALL,alertMsg,errorFloat,
                                    alertMall.getFailRate(),alertMall.getSuccessNum(),alertMall.getFailedNum(), AlertStatus.INIT,DingTailService.dset, AlertType.DINGPHONE);
                            DingTailService.sendMarkdownMsg(alertRecord);
                        }
                    }
                    this.baseMapper.insert(alertMall);
                } catch (Exception e) {
                    log.error("", e);
                }
            });
            XxlJobLogger.log("插入 " + alertMalls.size() + " 到alert_mall表");

        }
    }
}
