package com.shinet.core.alert.promethus;

import com.shinet.core.alert.common.HttpClientService;
import com.shinet.core.alert.coreservice.entity.AlertRecord;
import com.shinet.core.alert.coreservice.enums.AlertModel;
import com.shinet.core.alert.coreservice.enums.AlertStatus;
import com.shinet.core.alert.coreservice.enums.AlertType;
import com.shinet.core.alert.coreservice.service.AlertRecordService;
import com.shinet.core.alert.dingtalk.DingTailService;
import com.shinet.core.alert.dingtalk.VedioAlertService;
import com.shinet.core.alert.util.BlackProjUtils;
import com.shinet.core.alert.util.DoubleUtil;
import com.xxl.job.core.log.XxlJobLogger;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.joda.time.DateTimeConstants;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Service
@Slf4j
public class MotanQpsService {
    @Autowired
    HttpClientService httpClientService;
    public static String reqBaseUrl = "http://172.16.38.235:30762/api/v1/query_range?query=";
    @Autowired
    CoreQpsService coreQpsService;
    @Autowired
    AlertRecordService alertRecordService;
    int minTm = 10;
    /**
     *
     * @param maxQps 最大qps
     * @param gtNumForAlert 连续发生多少次
     */
    public void checkMaxQps(String jobName,int maxQps,int gtNumForAlert,boolean isV2){
        String promethusReqUrl = reqBaseUrl+"sum%20by(app)%20(rate(pepper_summary_motan_in_duration_seconds_count%5B3m%5D))%20%3E0";
        if(isV2){
            promethusReqUrl = reqBaseV2Url+"sum%20by(app)%20(rate(pepper_summary_motan_in_duration_seconds_count%5B3m%5D))%20%3E0";
        }
        long endSeconds = System.currentTimeMillis()/ 1000L;
        long startTimeSeconds = (System.currentTimeMillis()- DateTimeConstants.MILLIS_PER_MINUTE*minTm) / 1000L;

        promethusReqUrl = promethusReqUrl+"&start="+startTimeSeconds+"&end="+endSeconds+"&step=14&_="+(System.currentTimeMillis()/ 1000L);
        try {
            String alertMsg = coreQpsService.reqMsgAlert(promethusReqUrl,maxQps,gtNumForAlert);
            if(StringUtils.isNotBlank(alertMsg)){
                VedioAlertService.sendVocMsg(" motanqps  ", alertMsg);
                XxlJobLogger.log(alertMsg);

                AlertRecord alertRecord = alertRecordService.insertAlertRecord(jobName,"motanqps", AlertModel.SERVICEQPS,alertMsg,
                        gtNumForAlert*1.0d,0d,0,0, AlertStatus.INIT,DingTailService.dset, AlertType.DINGPHONE
                );
                DingTailService.sendMarkdownMsg(alertRecord);
            }
        }catch (Exception e){
            log.error("",e);
        }
    }
    public static String reqBaseV2Url = "http://172.16.11.185:9090/api/v1/query_range?query=";

    public void checkDoubleTimeQps(String jobName,float dfv,boolean isV2){
        long curMils = System.currentTimeMillis();
        String promethusReqUrl = reqBaseUrl+"sum%20by(app)%20(rate(pepper_summary_motan_in_duration_seconds_count%5B3m%5D))%20%3E0";
        if(isV2){
            promethusReqUrl = reqBaseV2Url+"sum%20by(app)%20(rate(pepper_summary_motan_in_duration_seconds_count%5B3m%5D))%20%3E0";
        }
        long endSeconds = curMils/ 1000L;
        long startTimeSeconds = (curMils- DateTimeConstants.MILLIS_PER_MINUTE*minTm) / 1000L;

        String promethusReqUrlStr = promethusReqUrl+"&start="+startTimeSeconds+"&end="+endSeconds+"&step=14&_="+(System.currentTimeMillis()/ 1000L);

        Map<String,Double> startDoubleMap = coreQpsService.reqMsgToMap(promethusReqUrlStr);


        long lastDayEndSeconds = (curMils - DateTimeConstants.MILLIS_PER_DAY)/ 1000L;
        long lastDayStartTimeSeconds = (curMils - DateTimeConstants.MILLIS_PER_DAY - DateTimeConstants.MILLIS_PER_MINUTE*minTm) / 1000L;
        String promethusReqEndUrl = promethusReqUrl+"&start="+lastDayStartTimeSeconds+"&end="+lastDayEndSeconds+"&step=14&_="+(System.currentTimeMillis()/ 1000L);
        Map<String,Double> endDoubleMap = coreQpsService.reqMsgToMap(promethusReqEndUrl);

        List<String> alertMsgList = new ArrayList<>();

        startDoubleMap.forEach((app,dvalue)->{
            if(!BlackProjUtils.isInBlack(app) && (isV2 ||(!isV2 && !BlackProjUtils.isV2InMove(app)))){
                Double lastValue = DoubleUtil.getDoubleByZero(endDoubleMap.getOrDefault(app,0D));
                dvalue = DoubleUtil.getDoubleByZero(dvalue);
                log.info(app+" 今天： "+dvalue+" 昨天： "+lastValue);
                XxlJobLogger.log(app+" 今天： "+dvalue+" 昨天： "+lastValue);
                if(lastValue==0){
                    lastValue = 1d;
                }

                if(dvalue<lastValue && (dvalue>10 || lastValue>10)){
                    Date date = new Date(curMils);
                    if(app.equalsIgnoreCase("bp-mall-service") && date.getTime()<8){
                        if(dvalue<1000 && lastValue<1000 && (1d-(dvalue/lastValue))>0.85f){
                            alertMsgList.add(app + "->{"+dvalue+","+lastValue+"}");
                        }
                    }else  if((dvalue<100 && lastValue<100) && (1d-(dvalue/lastValue))>0.5f){
                        alertMsgList.add(app + "->{"+dvalue+","+lastValue+"}");
                    }else if((dvalue>10 || lastValue>10)&&(1d-(dvalue/lastValue))>dfv){
                        alertMsgList.add(app + "->{"+dvalue+","+lastValue+"}");
                    }

                }
            }
        });

        String alertMsg = alertMsgList.stream().collect(Collectors.joining(","));

        log.info(alertMsg);

        if(StringUtils.isNotBlank(alertMsg)){
            VedioAlertService.sendVocMsg(" motanQps doubleTime  ", alertMsg);
            XxlJobLogger.log(alertMsg);
            AlertRecord alertRecord = alertRecordService.insertAlertRecord(jobName,"motanQps", AlertModel.SERVICEQPS,alertMsg,
                    dfv*1.0d,0d,0,0, AlertStatus.INIT,DingTailService.dset,AlertType.DINGPHONE
            );
            DingTailService.sendMarkdownMsg(alertRecord);
        }
    }
    /**
     *
     * @param maxCurrent
     * @param gtNumForAlert
     */
    public void checkMotanCurReq(String jobName,int maxCurrent,int gtNumForAlert,boolean isV2){
        String promethusReqUrl = reqBaseUrl+"sum%20(pepper_gauge_motan_in_concurrent%20offset%201m%20)%20by%20(app)";
        if(isV2){
            promethusReqUrl = reqBaseV2Url+"sum%20(pepper_gauge_motan_in_concurrent%20offset%201m%20)%20by%20(app)";
        }
        long endSeconds = System.currentTimeMillis()/ 1000L;
        long startTimeSeconds = (System.currentTimeMillis()- DateTimeConstants.MILLIS_PER_MINUTE*minTm) / 1000L;

        promethusReqUrl = promethusReqUrl+"&start="+startTimeSeconds+"&end="+endSeconds+"&step=14&_="+(System.currentTimeMillis()/ 1000L);
        try {
            String alertMsg = coreQpsService.reqMsgAlert(promethusReqUrl,maxCurrent,gtNumForAlert);
            if(StringUtils.isNotBlank(alertMsg)){
                VedioAlertService.sendVocMsg(" motan_concurrent-并发  ", alertMsg);
                XxlJobLogger.log(alertMsg);
                AlertRecord alertRecord =  alertRecordService.insertAlertRecord(jobName,"motanQps", AlertModel.SERVICEQPS,alertMsg,
                        maxCurrent*1.0d,0d,0,0, AlertStatus.INIT,DingTailService.dset,AlertType.DINGPHONE
                );
                DingTailService.sendMarkdownMsg(alertRecord);
            }
        }catch (Exception e){
            log.error("",e);
        }
    }

}
