package com.shinet.core.alert.coreservice.enums;

public enum AlertRoute {
    JISHU(1,"技术群"),
    ZHONGTAI(2,"中台业务监控群"),
    BUSINESS(3,"商业防空洞群"),
    TEST(4,"TES"),
    ERHAOALERT(5,"预警二号群"),
    GROUP9_PAY(6,"九组支付群"),
    ZENGZHANG(7,"增长策略需求群"),
    LIULIANG(8,"流量助推策略群"),
    DEVICE(9,"产品设备预警"),
    ZT_XQ(10,"中台需求沟通群"),
    SJXT(11,"数据系统监控群"),
    SUOQU(12,"锁区预警群"),
    SJWT(13,"数据问题"),
    JJSYJ(14,"紧急数据预警群"),
    ZZZLH(15,"增长周例会"),
    FZB(16,"反作弊报警群"),
    GSHXZB(17,"公司核心指标报警群"),
    FZBHX(18,"反作弊核心群"),
    JRECPM(19,"今日ECPM"),
    ZCY(20,"赵重阳"),
    GYHCHXQ(21,"归因回传核心群"),
    CESHIGSHXZB(22,"公司核心指标测试群"),
    WZZDHTFYJQ(23,"五组自动化投放预警群"),
    ZPH(24,"郑鹏辉预警测试群"),
    ;
    public Integer value;
    public String name;

    AlertRoute(Integer value, String name) {
        this.value = value;
        this.name = name;
    }

    public static AlertRoute getStatus(Integer value) {
        if (value != null) {
            AlertRoute[] otypes = AlertRoute.values();
            for (AlertRoute memberType : otypes) {
                if (value.equals(memberType.value)) {
                    return memberType;
                }
            }
        }
        return null;
    }
}
