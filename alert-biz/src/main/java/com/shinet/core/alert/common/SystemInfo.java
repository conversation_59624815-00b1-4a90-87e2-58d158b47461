package com.shinet.core.alert.common;

import java.net.InetAddress;
import java.net.UnknownHostException;

public class SystemInfo {

    public static SystemType SYSTEM_TYPE = SystemType.Win;
    public static String SEPARATOR = System.getProperty("file.separator");

    /** cpu线程数 */
    public static int THREAD_NUM = Runtime.getRuntime().availableProcessors();
    /** ffmpeg使用的线程数 */
    public static int THREAD_NUM_FOR_FFMPEG = THREAD_NUM/2 - 1;

    public static boolean TEST_SWITCH = true;

    // 测试条件下，广点通，是否不真正创建计划
    public static boolean Switch_Tencent_Not_Create_Plan = false;

    // 启动时不加载建计划需要的一些缓存数据
    public static boolean NotLoadCreatePlanCache = true;

    public static String LocalIp;

    enum SystemType {
        Linux,
        Win,
        Mac
    }

    static  {
        if (System.getProperty("os.name").toLowerCase().contains("win")) {
            SYSTEM_TYPE = SystemType.Win;
        }else if (System.getProperty("os.name").toLowerCase().contains("mac os")){
            SYSTEM_TYPE = SystemType.Mac;
        } else {
            SYSTEM_TYPE = SystemType.Linux;
        }
        try {
            LocalIp = InetAddress.getLocalHost().getHostAddress();
        } catch (UnknownHostException e) {
            LocalIp = "获取本机ip异常";
        }
    }

    public static boolean isWin() {
        return SYSTEM_TYPE == SystemType.Win;
    }

    public static boolean isMac() {
        return SYSTEM_TYPE == SystemType.Mac;
    }

    public static boolean isLinux() {
        return SYSTEM_TYPE == SystemType.Linux;
    }

    /**
     * 是本地测试
     */
    public static boolean isTest() {
        return (isWin() || isMac()) && TEST_SWITCH;
    }

    /**
     * 不是本地测试
     */
    public static boolean isNotTest() {
        return !isTest();
    }


    public static void main(String[] args) {
        System.out.println(System.getProperty("os.name").toLowerCase());
    }
}
