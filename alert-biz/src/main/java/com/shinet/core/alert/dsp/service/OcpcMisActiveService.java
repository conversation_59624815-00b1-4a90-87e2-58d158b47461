package com.shinet.core.alert.dsp.service;

import com.shinet.core.alert.coreservice.entity.AlertRecord;
import com.shinet.core.alert.coreservice.enums.AlertModel;
import com.shinet.core.alert.coreservice.enums.AlertStatus;
import com.shinet.core.alert.coreservice.enums.AlertType;
import com.shinet.core.alert.coreservice.service.AlertRecordService;
import com.shinet.core.alert.dingtalk.DingTailService;
import com.shinet.core.alert.dingtalk.VedioAlertService;
import com.shinet.core.alert.dsp.entity.OcpcMisAct;
import com.shinet.core.alert.dsp.entity.OcpcMisActive;
import com.shinet.core.alert.dsp.mapper.OcpcMisActiveMapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.shinet.core.alert.util.DateUtils;
import com.shinet.core.alert.util.DoubleUtil;
import com.xxl.job.core.log.XxlJobLogger;
import org.apache.commons.lang3.StringUtils;
import org.joda.time.DateTimeConstants;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
* <p>
    *  服务实现类
    * </p>
*
* <AUTHOR>
* @since 2023-06-21
*/
@Service
public class OcpcMisActiveService extends ServiceImpl<OcpcMisActiveMapper, OcpcMisActive> {
    @Autowired
    AlertRecordService alertRecordService;
    public void getAlertRate(String jobName,String osName,int rateLimit,int pnum){
        Date stateDate = new Date(System.currentTimeMillis()- DateTimeConstants.MILLIS_PER_HOUR*2);
        String startDateStr = DateUtils.formatDate(stateDate);
        List<OcpcMisAct> dlist = baseMapper.queryProject(osName,startDateStr);

        String alMsg = "";
        String msgD = "";
        for(int i=0;i<pnum && i<dlist.size();i++){
            OcpcMisAct projectD = dlist.get(i);
            String projectName = projectD.getProduct();
            if (Arrays.asList("qzcxhj","qzbnqf","qztcnc","qzmyjyg","qmjyg").contains(projectName)){
                continue;
            }

            List<OcpcMisAct>  pdetailList = baseMapper.queryStatusCount(osName,startDateStr,projectName);

            int allNum = 0;
            int noNum = 0 ;
            for(OcpcMisAct detailOcpcMisAct : pdetailList){
                if("no".equalsIgnoreCase(detailOcpcMisAct.getDsp())){
                    noNum = noNum + detailOcpcMisAct.getCnum();
                }
                allNum = allNum+detailOcpcMisAct.getCnum();
            }

            double rateD = DoubleUtil.getDoubleByOne(noNum*1.0d/allNum*100.0d);
            if(rateD>rateLimit && allNum>1000){
                alMsg = alMsg +"{"+projectName+rateD+">"+rateLimit+" allNum="+allNum+" noNum="+noNum+"},";
            }
            msgD = msgD+"{"+projectName+rateD+">"+rateLimit+" allNum="+allNum+" noNum="+noNum+"},";
        }

        if(StringUtils.isNotBlank(alMsg)){
            XxlJobLogger.log(alMsg);
            AlertRecord alertRecord = alertRecordService.insertAlertRecord(jobName,"ocpc归因比例异常", AlertModel.OCPC,
                    "ocpc归因比例异常 "+alMsg, AlertStatus.INIT, DingTailService.dset, AlertType.DINGDING);
            DingTailService.sendMarkdownMsg(alertRecord);
            VedioAlertService.sendVocMsg(" ocpc ", alMsg);
        }else{
            XxlJobLogger.log("ocpc check完成 "+msgD);
        }
    }
}
