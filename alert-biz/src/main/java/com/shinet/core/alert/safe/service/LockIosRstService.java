package com.shinet.core.alert.safe.service;

import com.alibaba.fastjson.JSON;
import com.shinet.core.alert.coreservice.entity.AlertRecord;
import com.shinet.core.alert.coreservice.enums.*;
import com.shinet.core.alert.coreservice.service.AlertRecordService;
import com.shinet.core.alert.dingtalk.DingTailService;
import com.shinet.core.alert.dsp.service.ProductService;
import com.shinet.core.alert.safe.entity.IosIpRst;
import com.shinet.core.alert.safe.entity.IosLockRateVo;
import com.shinet.core.alert.safe.entity.IosLockStatsVo;
import com.shinet.core.alert.safe.entity.LockIosRst;
import com.shinet.core.alert.safe.mapper.IosIpRstMapper;
import com.shinet.core.alert.safe.mapper.LockIosRstMapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.shinet.core.alert.util.DateUtils;
import com.shinet.core.alert.util.IntegerUtils;
import com.xxl.job.core.log.XxlJobLogger;
import org.apache.commons.lang3.StringUtils;
import org.joda.time.DateTimeConstants;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <p>
 *  服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-11-09
 */
@Service
public class LockIosRstService extends ServiceImpl<LockIosRstMapper, LockIosRst> {
    @Autowired
    AlertRecordService alertRecordService;
    @Autowired
    IosIpRstService iosIpRstService;
    @Autowired
    ProductService productService;

    public void checkPGIos(Date startTime,String jobName){
        List<LockIosRst> appList = lambdaQuery().gt(LockIosRst::getCreateTime,startTime).like(LockIosRst::getCity,"%苹果%").orderByDesc(LockIosRst::getCreateTime).list();
        String msg = "";

        String msgBm = "";
        if(appList.size()>0){
            for(LockIosRst lockIosRst : appList){
                if(StringUtils.contains(lockIosRst.getRemark(),"wx未安装")){
                    msgBm = msgBm+ lockIosRst.getProduct()+"->"+lockIosRst.getDsp()+" "+lockIosRst.getAppVersion()+" "+ DateUtils.formatDate(lockIosRst.getCreateTime(),DateUtils.PATTERN_YHMS)+";\n\n";
                }else{
                    msg = msg+ lockIosRst.getProduct()+"->"+lockIosRst.getDsp()+" "+lockIosRst.getAppVersion()+" "+ DateUtils.formatDate(lockIosRst.getCreateTime(),DateUtils.PATTERN_YHMS)+";\n\n";
                }
            }
            if(StringUtils.isNotBlank(msg)){
                msg = "第二层预警-error "+msg;
            }
            if(StringUtils.isNotBlank(msgBm)){
                msgBm = "第0层预警- "+msgBm;
            }
            XxlJobLogger.log("执行 共"+appList.size()+" 苹果审核预警 "+msg);
            XxlJobLogger.log("执行 共"+appList.size()+" wx未安装 苹果审核预警 "+msgBm);
        }

        String msgD = "";
        List<IosIpRst> iosIpRstList = iosIpRstService.lambdaQuery().gt(IosIpRst::getCreateTime,startTime).like(IosIpRst::getCity,"%苹果%").orderByDesc(IosIpRst::getCreateTime).list();
        if(iosIpRstList.size()>0){
            for(IosIpRst iosIpRst : iosIpRstList){
                msgD = msgD+ iosIpRst.getProduct()+" "+ DateUtils.formatDate(iosIpRst.getCreateTime(),DateUtils.PATTERN_YHMS)+";\n\n";
            }
            msgD = "第一层app审核 " +msgD;
        }

        if(StringUtils.isNotBlank(msg) || StringUtils.isNotBlank(msgD) || StringUtils.isNotBlank(msgBm)){
            String algMst = msg+"\r\n"+msgD+"\r\n"+msgBm;
            AlertRecord alertRecord = alertRecordService.insertAlertRecord(jobName,jobName,AlertModel.iosapple, algMst, AlertStatus.INIT, DingTailService.dset, AlertType.DINGDING);
            DingTailService.sendMarkdownMsg(alertRecord, AlertRoute.JISHU.value);
            XxlJobLogger.log("执行 共"+appList.size()+" 苹果审核预警 "+msgD);
        }
        XxlJobLogger.log("执行完成 共"+appList.size()+" 苹果信息 ");
    }
    @Autowired
    IosIpRstMapper iosIpRstMapper;
    @Autowired
    LockIosRstMapper lockIosRstMapper;

    public void alertIosLock(String jobName,Integer alertMaxNum){
        String tdaystr = DateUtils.formatDate(new Date(System.currentTimeMillis()- DateTimeConstants.MILLIS_PER_HOUR*4));
        //第一层监控
        List<IosLockRateVo>  iosLockRateIpVos = iosIpRstMapper.queryLockIpRate(tdaystr);
        String alt1Msg = "";

        long sumLockIpNum = 0;
        long allIpNum = 0;
        for(IosLockRateVo iosLockRateVo : iosLockRateIpVos){
            sumLockIpNum = IntegerUtils.add(sumLockIpNum,iosLockRateVo.getLockNum());
            allIpNum = IntegerUtils.add(allIpNum,iosLockRateVo.getUnlockNum());

            if(iosLockRateVo.getLockNum()>100){
                alt1Msg = alt1Msg+" "+iosLockRateVo.getProduct()+"-"+iosLockRateVo.getLockNum()+"/"+iosLockRateVo.getUnlockNum();
            }
        }
        //第二层监控
        List<IosLockRateVo>  iosLockRateVos = iosIpRstMapper.queryLockRate(tdaystr);
        String alt2Msg = "";
        long sumLockNum = 0;
        long allCaNum = 0;

        for(IosLockRateVo iosLockRateVo : iosLockRateVos){
            sumLockNum = IntegerUtils.add(sumLockNum,iosLockRateVo.getLockNum());
            allCaNum = IntegerUtils.add(iosLockRateVo.getUnlockNum(),allCaNum);

            if(iosLockRateVo.getLockNum()>(alertMaxNum/4)){
                alt2Msg = alt2Msg+" "+iosLockRateVo.getProduct()+"-"+iosLockRateVo.getLockNum()+"/"+iosLockRateVo.getUnlockNum();
            }
        }

        if(sumLockIpNum>alertMaxNum*3 || sumLockNum>alertMaxNum){
            alt1Msg = "总数超过 "+alertMaxNum+" "+sumLockIpNum+" "+sumLockNum+" "+alt1Msg;
        }

        if(StringUtils.isNotBlank(alt1Msg) || StringUtils.isNotBlank(alt2Msg)){
            String algMst = alt1Msg+"\r\n"+alt2Msg;
            AlertRecord alertRecord = alertRecordService.insertAlertRecord(jobName,jobName,AlertModel.ioslock, algMst, AlertStatus.INIT, DingTailService.dset, AlertType.DINGDING);
            DingTailService.sendMarkdownMsg(alertRecord, AlertRoute.JISHU.value);
            XxlJobLogger.log("锁区总数为 ip "+sumLockIpNum+" 设备为  "+sumLockNum+" "+ JSON.toJSONString(iosLockRateVos));
        }

        XxlJobLogger.log("锁区总数为 ip "+sumLockIpNum+"/"+allIpNum+" 设备为  "+sumLockNum+"/"+allCaNum);
        XxlJobLogger.log("锁区总数为 "+ JSON.toJSONString(iosLockRateVos));
        XxlJobLogger.log("锁区IP总数为 "+ JSON.toJSONString(iosLockRateIpVos));
    }

    /**
     * iOS锁区告警检查
     *
     * @param jobName 任务名称
     * @param minLockNum 最小锁定数量阈值
     * @param lockRate 锁定比率阈值(%)
     */
    public void alertIosLock2(String jobName, Integer minLockNum, Double lockRate) {
        XxlJobLogger.log("开始执行iOS锁区告警检查，参数: minLockNum={}, lockRate={}%", minLockNum, lockRate);

        try {
            List<IosLockStatsVo> lockStatsList = queryTodayLockStats(minLockNum);
            if (lockStatsList.isEmpty()) {
                XxlJobLogger.log("今日无iOS锁区数据，跳过告警检查");
                return;
            }

            AlertSummary summary = processLockStats(lockStatsList, minLockNum, lockRate, jobName);
            logStatsSummary(summary);

            if (summary.getAlertProductCount() == 0) {
                XxlJobLogger.log("所有产品锁区率均在正常范围内，无需告警");
            }

        } catch (Exception e) {
            String errorMsg = String.format("iOS锁区告警执行失败: %s", e.getMessage());
            XxlJobLogger.log(errorMsg);
            throw new RuntimeException(errorMsg, e);
        }
    }

    /**
     * 查询今日锁区统计数据并排序
     */
    private List<IosLockStatsVo> queryTodayLockStats(Integer minLockNum) {
        List<IosLockStatsVo> result = lockIosRstMapper.queryLockStatsToday(minLockNum);
        if (result == null) {
            return Collections.emptyList();
        }

        // 按CAID锁定比率降序，然后按锁定数量降序排序
        return result.stream()
                .sorted((a, b) -> {
                    int rateCompare = Double.compare(b.getCaidLockRate(), a.getCaidLockRate());
                    if (rateCompare != 0) {
                        return rateCompare;
                    }
                    return Long.compare(
                            b.getLockCaidNum() != null ? b.getLockCaidNum() : 0L,
                            a.getLockCaidNum() != null ? a.getLockCaidNum() : 0L
                    );
                })
                .collect(Collectors.toList());
    }

    /**
     * 处理锁区统计数据，检查告警条件并直接发送告警
     */
    private AlertSummary processLockStats(List<IosLockStatsVo> lockStatsList, Integer minLockNum, Double lockRate, String jobName) {
        AlertSummary summary = new AlertSummary();

        for (IosLockStatsVo stats : lockStatsList) {
            // 累计总体统计
            summary.addStats(stats);

            // 检查告警条件
            if (shouldAlert(stats, minLockNum, lockRate)) {
                summary.incrementAlertCount();
                XxlJobLogger.log("产品 {} 触发告警: CAID锁定率 {}%, 锁定数 {}",
                    stats.getProduct(), stats.getCaidLockRate(), stats.getLockCaidNum());

                // 直接发送单个产品的告警通知
                boolean sendSuccess = sendIndividualAlert(jobName, stats);
                if (sendSuccess) {
                    summary.incrementSuccessSent();
                } else {
                    summary.incrementFailedSent();
                }
            }
        }

        return summary;
    }

    /**
     * 判断是否需要告警
     */
    private boolean shouldAlert(IosLockStatsVo stats, Integer minLockNum, Double lockRate) {
        return stats.getLockCaidNum() >= minLockNum && stats.getCaidLockRate() >= lockRate;
    }

    /**
     * 构建单个产品的告警消息
     */
    private String buildAlertMessage(IosLockStatsVo stats) {
        return String.format("%s(%s)锁区率过高-CAID:%.2f%%-%d/%d,IP:%.2f%%-%d/%d",
                productService.getProductRemarkByName(stats.getProduct()),
                stats.getProduct(),
                stats.getCaidLockRate(),
                stats.getLockCaidNum(),
                stats.getTotalCaidNum(),
                stats.getIpLockRate(),
                stats.getLockIpNum(),
                stats.getTotalIpNum());
    }

    /**
     * 记录统计摘要日志
     */
    private void logStatsSummary(AlertSummary summary) {
        XxlJobLogger.log("iOS锁区统计完成 - 总锁区CAID数: {}/{}, 总锁区IP数: {}/{}, 检查产品数: {}, 告警产品数: {}",
                summary.getTotalLockCaidNum(),
                summary.getTotalCaidNum(),
                summary.getTotalLockIpNum(),
                summary.getTotalIpNum(),
                summary.getTotalProductCount(),
                summary.getAlertProductCount());

        if (summary.getAlertProductCount() > 0) {
            XxlJobLogger.log("告警发送结果 - 成功: {}, 失败: {}",
                    summary.getSuccessSentCount(),
                    summary.getFailedSentCount());
        }
    }

    /**
     * 发送单个产品的告警通知
     *
     * @param jobName 任务名称
     * @param stats 产品锁区统计数据
     * @return 发送是否成功
     */
    private boolean sendIndividualAlert(String jobName, IosLockStatsVo stats) {
        try {
            String alertMessage = buildAlertMessage(stats);
            AlertRecord alertRecord = alertRecordService.insertAlertRecord(
                    jobName,
                    jobName,
                    AlertModel.ioslock,
                    alertMessage,
                    AlertStatus.INIT,
                    DingTailService.lockAlarmData,
                    AlertType.DINGDING);

//            DingTailService.sendMarkdownMsg(alertRecord, AlertRoute.SUOQU.value);
            XxlJobLogger.log("产品 {} iOS锁区告警发送成功", stats.getProduct());
            return true;

        } catch (Exception e) {
            XxlJobLogger.log("产品 {} iOS锁区告警发送失败: {}", stats.getProduct(), e.getMessage());
            return false;
        }
    }

    /**
     * 告警摘要数据类
     * 用于统计和管理告警相关的数据
     */
    private static class AlertSummary {
        private long totalLockCaidNum = 0;
        private long totalCaidNum = 0;
        private long totalLockIpNum = 0;
        private long totalIpNum = 0;
        private int totalProductCount = 0;
        private int alertProductCount = 0;
        private int successSentCount = 0;
        private int failedSentCount = 0;

        public void addStats(IosLockStatsVo stats) {
            totalLockCaidNum += Optional.ofNullable(stats.getLockCaidNum()).orElse(0L);
            totalCaidNum += Optional.ofNullable(stats.getTotalCaidNum()).orElse(0L);
            totalLockIpNum += Optional.ofNullable(stats.getLockIpNum()).orElse(0L);
            totalIpNum += Optional.ofNullable(stats.getTotalIpNum()).orElse(0L);
            totalProductCount++;
        }

        public void incrementAlertCount() {
            alertProductCount++;
        }

        public void incrementSuccessSent() {
            successSentCount++;
        }

        public void incrementFailedSent() {
            failedSentCount++;
        }

        // Getters
        public long getTotalLockCaidNum() { return totalLockCaidNum; }
        public long getTotalCaidNum() { return totalCaidNum; }
        public long getTotalLockIpNum() { return totalLockIpNum; }
        public long getTotalIpNum() { return totalIpNum; }
        public int getTotalProductCount() { return totalProductCount; }
        public int getAlertProductCount() { return alertProductCount; }
        public int getSuccessSentCount() { return successSentCount; }
        public int getFailedSentCount() { return failedSentCount; }
    }
}
