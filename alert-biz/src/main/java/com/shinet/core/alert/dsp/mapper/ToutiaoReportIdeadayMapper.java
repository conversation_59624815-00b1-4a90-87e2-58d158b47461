package com.shinet.core.alert.dsp.mapper;

import com.shinet.core.alert.adb.entity.AdArpuRealSum;
import com.shinet.core.alert.dsp.entity.AppActiveAlertBean;
import com.shinet.core.alert.dsp.entity.ToutiaoReportIdeaday;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * <p>
 *  Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2021-08-09
 */
public interface ToutiaoReportIdeadayMapper extends BaseMapper<ToutiaoReportIdeaday> {

    @Select("select  app_name, product,  ROUND(sum(active),0) as active, sum(m.ocpc_active) as ocpc_active,\n" +
            "ROUND(sum(cost),0) as cost,  ROUND(sum(cost)/sum(active),2) as cpa ,\n" +
            "min(mincrawl_time) as mincrawl_time , ROUND(sum(event1_type)/sum(active),2) as event1_type\n" +
            "from  " +
            "(select app_name,min(crawl_time) as mincrawl_time,max(crawl_time) as maxcrawl_time ,advertiser_id,sum(active) as active,sum(event1_type) as event1_type,sum(event2_type) as event2_type,sum(event3_type) as event3_type,sum(cost) as cost\n" +
            "from toutiao_report_ideaday where stat_datetime = curdate() and advertiser_id not in (" +
            "'1721014910389261','1725456294968328','1715196297626632','1714940382026830','1725456294067214','1720013249947661'," +
            "'1775462199317517','1774286310870093','1774286310260749','1774286309663757','1791223279216715','1791223278442522'," +
            "'1791223277691914','1791222864329738','1791222863575050','1791222862921738','1779443765712004','1779443765124103'," +
            "'1789054534236169','1789054478614538','1789054477953035','1789054477006859','1775462199317517', '1774286310870093', '1774286310260749', '1774286309663757', '1791223279216715', '1791223278442522', '1791223277691914', '1791222864329738', '1791222863575050', '1791222862921738', '1779443765712004', '1779443765124103', '1789054534236169', '1789054478614538', '1789054477953035', '1789054477006859', '1789054423394314', '1789054422503433', '1789054421728265', '1789054420913164','1791931863045210','1791931862309961','1791931861237770','1791931799816202','1789415770137819','1791931868276937','1791931865881673','1791931863827460','1791931798947849','1789863963893913','1789863963102283','1789863903411210','1789771682411593','1789771681637385','1789863969013772','1789863968278538','1789863967549513','1792015094531083','1792015058800714','1792015057883136','1792015057089610','1793576600122372','1784499991014475','1788239393070083','1794672100218906','1794672101105674','1788239393807364','1794645961219083','1794646101525610','1794488235426819','1787686255152138','1787686094071872','1784499991642123','1794646102317146','1794646102991946','1794646103995401','1794646104665162','1794646103995401','****************','****************','****************','****************','****************','****************','****************','****************','****************','****************','****************','****************','****************','****************','****************','****************','****************','****************','****************','****************','****************')" +
            " group by advertiser_id) as ideaday\n" +
            "left join " +
            "(select product,account_id,count(DISTINCT userId) as ocpc_active  from user_event where create_time >=curdate() and event_type =0 and dsp='toutiao' group by account_id  order by ocpc_active desc) as m\n" +
            "on ideaday.advertiser_id =m.account_id  " +
            "group by product " +
            "order by active desc")
    List<AppActiveAlertBean> queryActiveGap();

    @Select("select stat_date,product_name,count(DISTINCT advertiser_id) as advertiserNum,count(DISTINCT video_name) as videoNum, " +
            "    count(DISTINCT unit_id) as adIdNum, " +
            "    sum(rebate_cost) as rebeatCost,sum(activation) as active " +
            "    from kuaishou_report_data_unit " +
            "    where stat_date >=  DATE_SUB(CURDATE(),INTERVAL 0 day)  and video_name like '%PJ%' " +
            "    GROUP BY stat_date,product_name order by rebeatCost desc")
    List<AppActiveAlertBean> queryKuaishouActiveGap();
}
