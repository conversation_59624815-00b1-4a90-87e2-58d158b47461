package com.shinet.core.alert.clickhouse.entity;

import lombok.Data;

/**
 * <AUTHOR>
 * @since 2022/4/21
 */
@Data
public class ArpuCheckBean {

    private String adSource;
    private String product;
    private String productName;
    private String os;
    private Double incomeT;
    private Double incomeY;
    private Double dt1;
    private Double dt2;
    private Double subRate;

    private Double toutiaosubRate;
    private Double kuaishousubRate;
    private Double guangdiantongsubRate;
    private Double baidusubRate;
    private Double alisubRate;
    private Double aqysubRate;
    private Double sigmobsubRate;
    private Double vivosubRate;
    private Double opposubRate;
    private Double unknownsubRate;
    private Double qitasubRate;

    private Integer dau;
    public String getCeilP(Double p){
        return Math.ceil(p) +"%";
    }


    public boolean check(double limit){
        return (toutiaosubRate != null && Math.abs(toutiaosubRate) > limit)
            || (kuaishousubRate != null &&  Math.abs(kuaishousubRate) > limit)
            || (guangdiantongsubRate != null &&  Math.abs(guangdiantongsubRate) > limit)
            || (baidusubRate != null &&  Math.abs(baidusubRate) > limit)
            || (alisubRate != null &&  Math.abs(alisubRate) > limit)
            || (aqysubRate != null &&  Math.abs(aqysubRate) > limit)
            || (sigmobsubRate != null &&  Math.abs(sigmobsubRate) > limit)
            || (vivosubRate != null &&  Math.abs(vivosubRate) > limit)
            || (opposubRate != null &&  Math.abs(opposubRate) > limit)
            || (unknownsubRate != null &&  Math.abs(unknownsubRate) > limit)
            || (qitasubRate != null &&  Math.abs(qitasubRate) > limit)
                ;
    }
}
