package com.shinet.core.alert.coreservice.service;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.shinet.core.alert.coreservice.entity.AlertDelayJob;
import com.shinet.core.alert.coreservice.enums.AlertJobDelayModel;
import com.shinet.core.alert.coreservice.mapper.AlertDelayJobMapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.shinet.core.alert.util.DateUtils;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;

/**
* <p>
    *  服务实现类
    * </p>
*
* <AUTHOR>
* @since 2022-04-21
*/
@Service
public class AlertDelayJobService extends ServiceImpl<AlertDelayJobMapper, AlertDelayJob> {


    public AlertDelayJob createOrUpdateDelayJob(String jobId,Integer delayTime){
        try {
            AlertJobDelayModel model = AlertJobDelayModel.getByJobId(jobId);
            Date now = new Date();
            QueryWrapper<AlertDelayJob> wrapper = new QueryWrapper<>();
            wrapper.lambda().eq(AlertDelayJob::getJobId,jobId);
            AlertDelayJob job = getOne(wrapper);
            if (job == null) {
                job = new AlertDelayJob();
                job.setJobId(jobId);
                job.setJobName(model.getJobName());
                job.setCreateTime(now);
                job.setUpdateTime(now);
                job.setDelayTime(DateUtils.addTime(now, delayTime));
                save(job);
            }else {
                job.setUpdateTime(now);
                job.setDelayTime(DateUtils.addTime(now, delayTime));
                updateById(job);
            }
            return job;
        }catch (Exception e){
            log.error("r");
        }

        return null;
    }

    public Boolean canSend(String jobId){
        QueryWrapper<AlertDelayJob> wrapper = new QueryWrapper<>();
        wrapper.lambda().eq(AlertDelayJob::getJobId,jobId);
        AlertDelayJob job = getOne(wrapper);
        if (job == null){
            return true;
        }
        return !job.getDelayTime().after(new Date());
    }
}
