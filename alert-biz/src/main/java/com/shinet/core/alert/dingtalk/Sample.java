package com.shinet.core.alert.dingtalk;

import com.alibaba.fastjson.JSON;
import com.aliyun.tea.*;
import com.aliyun.teautil.*;
import com.aliyun.teautil.models.*;
import com.aliyun.dingtalkservice_group_1_0.*;
import com.aliyun.dingtalkservice_group_1_0.models.*;
import com.aliyun.teaopenapi.*;
import com.aliyun.teaopenapi.models.*;
import lombok.extern.slf4j.Slf4j;

@Slf4j
public class Sample {

    /**
     * 使用 Token 初始化账号Client
     * @return Client
     * @throws Exception
     */
    public static com.aliyun.dingtalkservice_group_1_0.Client createClient() throws Exception {
        Config config = new Config();
        config.protocol = "https";
        config.regionId = "central";
        return new com.aliyun.dingtalkservice_group_1_0.Client(config);
    }

    public static void main(String[] args_) throws Exception {
        java.util.List<String> args = java.util.Arrays.asList(args_);
        com.aliyun.dingtalkservice_group_1_0.Client client = Sample.createClient();
        SendServiceGroupMessageHeaders sendServiceGroupMessageHeaders = new SendServiceGroupMessageHeaders();
        sendServiceGroupMessageHeaders.xAcsDingtalkAccessToken = "4992a928ae8439f7b8bf05d34c4f2fb7";
        SendServiceGroupMessageRequest.SendServiceGroupMessageRequestBtns btns0 = new SendServiceGroupMessageRequest.SendServiceGroupMessageRequestBtns()
                .setActionURL("http://www.dingtalk.com")
                .setTitle("测试按钮");
        SendServiceGroupMessageRequest sendServiceGroupMessageRequest = new SendServiceGroupMessageRequest()
                .setTargetOpenConversationId("cidxxxxx==")
                .setTitle("服务提醒")
                .setContent("你有新的任务待审批")
                .setAtDingtalkIds(java.util.Arrays.asList(
                    "$:LWCP_v1:$xxxxxxx=="
                ))
//                .setAtUnionIds(java.util.Arrays.asList(
//                    "JuSi1Jkl"
//                ))
//                .setReceiverDingtalkIds(java.util.Arrays.asList(
//                    "$:LWCP_v1:$xxxxxxx=="
//                ))
//                .setReceiverUnionIds(java.util.Arrays.asList(
//                    "JuSi1Jkl"
//                ))
                .setMessageType("MARKDOWN")
                .setBtnOrientation("0")
                .setBtns(java.util.Arrays.asList(
                    btns0
                ));
        try {
            SendServiceGroupMessageResponse sendServiceGroupMessageResponse = client.sendServiceGroupMessageWithOptions(sendServiceGroupMessageRequest, sendServiceGroupMessageHeaders, new RuntimeOptions());
            log.info(JSON.toJSONString(sendServiceGroupMessageResponse));
        } catch (TeaException err) {
            log.error("",err);
            if (!com.aliyun.teautil.Common.empty(err.code) && !com.aliyun.teautil.Common.empty(err.message)) {
                // err 中含有 code 和 message 属性，可帮助开发定位问题
            }

        } catch (Exception _err) {
            log.error("",_err);
            TeaException err = new TeaException(_err.getMessage(), _err);
            if (!com.aliyun.teautil.Common.empty(err.code) && !com.aliyun.teautil.Common.empty(err.message)) {
                // err 中含有 code 和 message 属性，可帮助开发定位问题
            }

        }        
    }
}