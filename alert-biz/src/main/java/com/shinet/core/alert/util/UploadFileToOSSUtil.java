package com.shinet.core.alert.util;

import com.shinet.core.alert.config.OssService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;
import java.text.SimpleDateFormat;
import java.util.Date;

/**
 * 文件上传阿里云
 * <AUTHOR>
 */
@Slf4j
@Component
public class UploadFileToOSSUtil {

    @Autowired
    OssService ossService;

    public String uploadFileToOss(File file, String path) {
        try {
            String suffix = new SimpleDateFormat("yyMMddHHmmssSSS").format(new Date());
            String innerPath = path + suffix + file.getName();
            ossService.uploadFileForVideoManager(innerPath, new FileInputStream(file));
            return OssService.OSS_VIDEO_URL + "/" + innerPath;
        } catch (IOException e) {
            log.error("上传文件失败！", e);
            throw new IllegalArgumentException("上传文件失败！请重试");
        }
    }
}
