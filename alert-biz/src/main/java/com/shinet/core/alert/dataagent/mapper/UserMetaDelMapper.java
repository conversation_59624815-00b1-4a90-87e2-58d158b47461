package com.shinet.core.alert.dataagent.mapper;

import com.shinet.core.alert.dataagent.entity.UserMetaDel;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Insert;

/**
 * <p>
 * 用户 meta data Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2021-11-26
 */
public interface UserMetaDelMapper extends BaseMapper<UserMetaDel> {
    @Insert("insert into user_meta_del " +
            "(pkg_id, user_id, union_id, photo_url, nick_name, state, os, brand, channel, device_id, app_version, os_version, rom_version, create_time, update_time) " +
            "values (" +
            "#{pkgId}, #{userId}, #{unionId}, #{photoUrl}, #{nickName}, 0, #{os}, #{brand}, #{channel}, #{deviceId}, #{appVersion}, #{osVersion}, #{romVersion}, #{createTime}, #{updateTime}" +
            ")")
    int insertUserMeta(UserMetaDel userMetaEntity);
}
