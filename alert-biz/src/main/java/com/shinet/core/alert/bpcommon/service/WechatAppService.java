package com.shinet.core.alert.bpcommon.service;

import com.shinet.core.alert.bpcommon.entity.WechatApp;
import com.shinet.core.alert.bpcommon.mapper.WechatAppMapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import java.util.HashSet;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * <p>
 * 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-12-06
 */
@Service
public class WechatAppService extends ServiceImpl<WechatAppMapper, WechatApp> {
    public static Set<Long> appIdSet = new HashSet<>();

    @PostConstruct
    public void initWechatApps() {
        List<WechatApp> wechatAppList = list();
        for(WechatApp wechatApp : wechatAppList){
            appIdSet.add(wechatApp.getId());
        }
    }

    public Set<Long> getNotSet(int pkgId){
        Long pkgIdL = pkgId*1L;
        if(appIdSet.size()==0){
            List<WechatApp> wechatAppList = list();
            for(WechatApp wechatApp : wechatAppList){
                appIdSet.add(wechatApp.getId());
            }
        }
        Set<Long> dset = appIdSet.stream().filter(appId->!appId.equals(pkgIdL)).collect(Collectors.toSet());
        return dset;
    }
}
