package com.shinet.core.alert.clickhouse.mapper.ck1;

import com.shinet.core.alert.clickhouse.entity.AdDauArapBean;
import com.shinet.core.alert.clickhouse.entity.AdExposureChData;
import com.shinet.core.alert.clickhouse.entity.AdExposureChannelManfi;
import com.shinet.core.alert.clickhouse.entity.AdIdExposure;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;
import java.util.Set;

public interface ExposureOdsMapper {
    @Select({"select ad_id,ad_action,count(1) as showNum,product  from ods.event_dist " +
            " where " +
            " logday=today() and ad_action in ('exposure','close','reward') and os='android' " +
            " and sendTime>#{sendTime}  and ad_type global in (select toString(ad_type) from dwd.ad_type_basic_dist where type_name like ('视频'))  group by ad_id,ad_action,product;"})
    public List<AdIdExposure> getAdActionNums(@Param("sendTime") String sendTime);


    @Select({"select ad_id,ad_action,count(1) as showNum,product  from ods.event_dist " +
            " where " +
            " logday=today() and ad_action in ('exposure','close','reward') and os='android' " +
            " and sendTime>#{sendTime}  and ad_id=#{adId}  group by ad_id,ad_action,product;"})
    public List<AdIdExposure> getAdActionNumsByAdId(@Param("sendTime") String sendTime,@Param("adId") String adId);


    @Select({"select ad_id from ods_mysql.tb_ap_bidding;"})
    public Set<String> getAdBiddings();


    @Select({"select ad_id,count(1) as showNum  from ods.ad_point_dist left join  dwd.product_ad_conf_dist as c on  ods.ad_point_dist.ad_id=c.ad_id " +
            " where logday = today() and call_time>#{sendTime}  and uri != '/ap-data-consumer/upload' group by ad_id;"})
    public List<AdIdExposure> getAdRewardNums(@Param("sendTime") String sendTime);



    @Select({" select r1.products as product, pv, incomes as income,r2.dau as dau,r1.pv/r2.dau as pvren,r1.incomes/r2.dau as arpu,incomes/pv *1000 as ecpm from ( " +
            "               select products, sum(pvs) as pv, sum(income) as incomes " +
            "               from ( " +
            "                                 select a1.product as products, " +
            "                                        sum(a1.pv) as pvs, " +
            "                                        sum(ifNull(a3.ecpm,a4.ecpm) / 1000 * a1.pv)      as income " +
            "                                 from ( " +
            "                                          select product, " +
            "                                                 ad_id, " +
            "                                                 count() as pv " +
            "                                          from ods.event_exposure_dist " +
            "                                          where logday = #{logday} " +
            "                                            and os = 'android' " +
            "                                            and ad_action = 'exposure' " +
            "                                            and time between #{startTime} and #{endTime} " +
            "                                          group by product, ad_id " +
            "                                          ) a1 " +
            "                                          left join (select ad_id,pos_id,ad_type_name from dwd.product_ad_conf_dist group by ad_id,pos_id,ad_type_name) a2 on a1.ad_id = a2.ad_id " +
            "                                          left join (select * from dwd.pos_ecpm_dist where logday = toDate(#{logday}) - 2) a3 on a2.pos_id = a3.pos_id " +
            "                                          left join (select * from ods_mysql.tb_ap_ad_budget) a4 on a1.ad_id = toString(a4.id) " +
            "                                 group by a1.product " +
            "                        ) " +
            "               group by products " +
            "                  ) r1 left join (select product,uniqExact(device_id) as dau from ods.event_dist where logday =#{logday} and os ='android' and event ='AdData' " +
            "                  and time between #{startTime} and #{endTime} " +
            "                  group by product) r2 " +
            "on r1.products = r2.product " +
            "where r2.dau > 0 and ecpm is not null"})
    List<AdDauArapBean> queryDauArpu(@Param("startTime") String startTime, @Param("endTime") String endTime, @Param("logday") String logday);


    @Select({"select  a.product,b.product_name,b.product_group,count(distinct channel) as channelNum,count(distinct manufacturer) as manNum " +
            "from ods.event_exposure_dist a left join dwd.product_map_dist b on a.product=b.product " +
            "where logday=today() and ad_action='exposure'    group by a.product,b.product_name,b.product_group having manNum>#{manNum} or channelNum>#{channelNum} order by channelNum desc;"})
    public List<AdExposureChannelManfi> getAllExposureChannel(Integer channelNum,Integer manNum);





    @Select({"   \n " +
            " select td.product,td.channel,td.pv as tdPv,yes.pv as yesPv,td.allexposure tdexpNum,yes.allexposure yesExpNum,td.allSr tdSr,yes.allSr yesSr,td.allSr/yes.allSr as srlv,td.pv/yes.pv as pvLv \n" +
            " from (\n" +
            "\n" +
            "    select product,\n" +
            "           channel,\n" +
            "           count(1)                               as                                       allexposure,\n" +
            "           count(distinct distinct_id)            as                                       uv,\n" +
            "           count(1) / count(distinct distinct_id) as                                       pv,\n" +
            "           sum(if(toFloat32OrZero(extend1) > 3000, 3000, toFloat32OrZero(extend1))) / 1000 allSr\n" +
            "    from ods.event_exposure_dist\n" +
            "    where logday = today()\n" +
            "      and ad_action = 'exposure'\n" +
            "      and sendTime > #{startTime}\n" +
            "      and sendTime < #{endTime}\n" +
            "    group by product, channel\n" +
            "    having uv > #{minUv}\n" +
            "\n" +
            "         ) td\n" +
            "         left join (\n" +
            "   select product,\n" +
            "             channel,\n" +
            "             count(1)                               as                                       allexposure,\n" +
            "             count(distinct distinct_id)            as                                       uv,\n" +
            "             count(1) / count(distinct distinct_id) as                                       pv,\n" +
            "             sum(if(toFloat32OrZero(extend1) > 3000, 3000, toFloat32OrZero(extend1))) / 1000 allSr\n" +
            "      from ods.event_exposure_dist\n" +
            "      where logday = yesterday() \n" +
            "        and ad_action = 'exposure'\n" +
            "        and sendTime > #{yesStartTime}\n" +
            "        and sendTime < #{yesEndTime}\n" +
            "      group by product, channel\n" +
            "      having uv > #{minUv}\n" +
            "    ) yes on td.product = yes.product and td.channel = yes.channel\n" +
            ";"})
    public List<AdExposureChData> getAllChannelData(Long startTime, Long endTime, Long yesStartTime, Long yesEndTime, Integer minUv);

}
