package com.shinet.core.alert.coreservice.dto;

import lombok.Data;

import java.io.Serializable;
import java.util.HashMap;
import java.util.Map;

@Data
public class CommonHeaderDTO implements Serializable {
    private String deviceId;
    private String brand;
    private String gps;
    private String bs;
    private String appVersion;
    private String os;
    private String channel;
    private String romVersion;
    private String osVersion;
    private String accessKey;
    private String wechatId;
    private String pkgId;
    private String appId;
    private String ip;
    public Integer getIntOs(){
        return os.equals("android") ? 0 : 1;
    }

    public Map<String,String> getMap(){
        Map<String,String> headMap = new HashMap<>();
        headMap.put("deviceId",deviceId);
        headMap.put("brand",brand);
        headMap.put("gps",gps);
        headMap.put("bs",bs);
        headMap.put("appVersion",appVersion);
        headMap.put("os",os);
        headMap.put("channel",channel);
        headMap.put("romVersion",romVersion);
        headMap.put("osVersion",osVersion);
        headMap.put("accessKey",accessKey);
        headMap.put("wechatId",wechatId);
        headMap.put("pkgId",pkgId);
        headMap.put("appId",appId);
        return headMap;
    }

//    public static CommonHeaderDTO getCommHead(AliUserDevice aliUserDevice){
//        CommonHeaderDTO commonHeaderDTO = new CommonHeaderDTO();
//        commonHeaderDTO.setAccessKey(aliUserDevice.getAccessKey());
//        commonHeaderDTO.setAppId(aliUserDevice.getAppId()+"");
//        commonHeaderDTO.setDeviceId(aliUserDevice.getDeviceId());
//        commonHeaderDTO.setOs(aliUserDevice.getOs());
//
//        commonHeaderDTO.setAppVersion(aliUserDevice.getAppVersion());
//        commonHeaderDTO.setBrand(aliUserDevice.getBrand());
//        commonHeaderDTO.setGps(aliUserDevice.getGps());
//        commonHeaderDTO.setChannel(aliUserDevice.getChannel());
//        commonHeaderDTO.setOsVersion(aliUserDevice.getOsVersion());
//        commonHeaderDTO.setRomVersion(aliUserDevice.getRomVersion());
//        commonHeaderDTO.setPkgId(aliUserDevice.getPkgId());
//
//        return commonHeaderDTO;
//    }

    public Long  getUserId(){
        return  Long.parseLong(this.accessKey.split("_")[1]);
    }

    public static Long getUserId(CommonHeaderDTO commonHeaderDTO){
        return  Long.parseLong(commonHeaderDTO.getAccessKey().split("_")[1]);
    }
}
