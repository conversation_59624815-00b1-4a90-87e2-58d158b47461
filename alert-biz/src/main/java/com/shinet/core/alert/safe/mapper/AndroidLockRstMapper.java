package com.shinet.core.alert.safe.mapper;


import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.shinet.core.alert.safe.entity.*;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * <p>
 *  Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2024-03-15
 */
public interface AndroidLockRstMapper extends BaseMapper<AndroidLockRst> {

    @Select("SELECT product,channel,app_version,count(1) as id from android_lock_rst where lock_flag ='true' and is_ocpc = 1 and remark like '%全锁%' GROUP BY product,channel,app_version HAVING COUNT(1) > #{maxAllLock}")
    public List<AndroidLockRst> getAllLockNum(@Param("maxAllLock")int maxAllLock);
//    @Select("SELECT product,channel,app_version from android_lock_rst where lock_flag ='true'and is_ocpc = 1 and remark like '锁区命中%' GROUP BY product,channel,app_version HAVING COUNT(1) > #{maxAllLock}")
//    public List<AndroidLockRst> getAllLockNum(@Param("maxAllLock")int maxAllLock);


    @Select("select product,channel,app_version,count(1) as lockIpNum from android_lock_rst where lock_flag ='true' and  (remark2='android首层锁区' or remark like '%全锁%')  \n" +
            "and (channel like '%vivo%' or channel like '%oppo%' or channel like '%mi%' or channel like '%huawei%' or channel like '%honor%')\n" +
            "GROUP BY product,channel,app_version order by lockIpNum desc;")
    public List<AndoridFirstLock> getDlockNum();

    @Select({" select product,store_name,count(DISTINCT oaid) as lockOaNum,count(DISTINCT ip) as lockIpNum from android_lock_rst" +
            " where  lock_flag='true' " +
            " GROUP BY product,store_name order by lockOaNum desc;"})
    public List<AndoridProductLock> queryProductIpLockNum();

}
