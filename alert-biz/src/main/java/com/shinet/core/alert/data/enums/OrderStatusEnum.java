package com.shinet.core.alert.data.enums;

import java.io.Serializable;

public enum OrderStatusEnum implements Serializable {

    CHECK_READY(1, "待审核 - 下单成功即为待审核"),
    CHECK_PASS(2, "审核通过 - 审核通过后就可以进入打款流程"),
    CHECK_NOT_PASS_TO_PAY_BACK(3, "审核不通过 - 待退款"),
    CHECK_NOT_PASS_PAID_BACK(4, "审核不通过 - 已退款"),
    WITHDRAW_SUCCESS(5, "提现打款成功"),
    WITHDRAW_ERROR_TO_PAY_BACK(6, "提现打款失败 - 待退款"),
    WITHDRAW_ERROR_PAID_BACK(7, "提现打款失败 - 已退款");

    private Integer value;
    private String desc;

    public Integer getValue() {
        return value;
    }

    OrderStatusEnum(Integer value, String desc) {
        this.value = value;
        this.desc = desc;
    }

    public static OrderStatusEnum fromValue(Integer value){
        for(OrderStatusEnum orderStatusEnum : OrderStatusEnum.values()){
            if(orderStatusEnum.value.equals(value)){
                return orderStatusEnum;
            }
        }
        return null;
    }
}
