package com.shinet.core.alert.promethus;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.shinet.core.alert.common.HttpClientService;
import com.shinet.core.alert.coreservice.entity.AlertRecord;
import com.shinet.core.alert.coreservice.enums.AlertModel;
import com.shinet.core.alert.coreservice.enums.AlertStatus;
import com.shinet.core.alert.coreservice.enums.AlertType;
import com.shinet.core.alert.coreservice.service.AlertRecordService;
import com.shinet.core.alert.dingtalk.DingTailService;
import com.shinet.core.alert.promethus.enums.BusinessExceptionEnum;
import com.xxl.job.core.log.XxlJobLogger;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import java.math.BigDecimal;
import java.net.URLEncoder;
import java.util.HashMap;

import static java.math.BigDecimal.ROUND_HALF_UP;

/**
 * description: 商业异常监控
 * date: 2021/11/6 11:40
*/
@Service
@Slf4j
public class BusinessExceptionService {

    public static String reqBaseUrl = "http://*************:30762/api/v1/query?query=";

    @Autowired
    HttpClientService httpClientService;
    @Autowired
    AlertRecordService alertRecordService;

    /**
     * description: app错误数检测
    */
    public void checkAppCrash(Integer rateInt){
        XxlJobLogger.log("app错误数开始检测");
        String parms = "sum (record:alert:increase:1m{name=\"app_crash_event_upload\"}) by (level1_value)/(sum" +
                "(record:alert:increase:1m{name=\"app_click_event_upload\"}) by (level1_value) > 20)";
        try {
            String url = reqBaseUrl+URLEncoder.encode(parms, "UTF-8");
            String resultMsg = httpClientService.sendReq(url,new HashMap<>());
            if(StringUtils.isBlank(resultMsg)){
                return;
            }
            JSONObject result = JSONObject.parseObject(resultMsg);
            if (result.getJSONObject("data") == null){
                return;
            }
            JSONArray list = result.getJSONObject("data").getJSONArray("result");
            StringBuffer msg = new StringBuffer("");
            for (Object metricO:list) {
                JSONObject metric = (JSONObject) metricO;
                String product = metric.getJSONObject("metric").getString("level1_value");
                if ("kaixincanting".equals(product)){
                    continue;
                }
                String rateStr = JSONObject.parseArray(metric.getJSONArray("value").toJSONString(), String.class).get(1);
                BigDecimal rate = new BigDecimal(rateStr).multiply(new BigDecimal(100)).setScale(2, ROUND_HALF_UP);
                if (rate.compareTo(new BigDecimal(rateInt)) >= 0) {
                    String msg1 = "> 产品："+product+",错误率："+rate.toString()+"% \n\n";
                    msg.append(msg1);
                }
            }
            String alertMsg = msg.toString()+" ###### [查看监控看板](http://monitor-app.coohua-inc.com/d/WzGKGLNMz/appbeng-kui-ci-shu-jian-kong?orgId=1&fullscreen&panelId=2) \n" ;
            if (StringUtils.isNotBlank(msg.toString())){
                XxlJobLogger.log("app错误数检测开始预警");
                AlertRecord alertRecord = alertRecordService.insertAlertRecord("APP错误率异常","AppDataException", AlertModel.BUSINESS_EXPETION,
                        alertMsg.toString(),
                        AlertStatus.INIT,DingTailService.dsetData, AlertType.DINGDING);
                DingTailService.sendMarkdownMsg(alertRecord);
            }
            XxlJobLogger.log("app错误数结束检测");
        }catch (Exception e){
            log.error("",e);
        }
    }



    /**
     * description: app错误数检测
     */
    public void checkRequestException(Integer rateInt){
        XxlJobLogger.log("Request错误开始检测");
        String parms = "sum(record:alert:increase:1m{name=\"ApGateWayNoAdAlert\",level3_value!=\"PARAM_ERROR\"}) by" +
                "(level1_value,level3_value) >1";
        try {
            String url = reqBaseUrl+URLEncoder.encode(parms, "UTF-8");
            String resultMsg = httpClientService.sendReq(url,new HashMap<>());
            if(StringUtils.isBlank(resultMsg)){
                return;
            }
            JSONObject result = JSONObject.parseObject(resultMsg);
            if (result.getJSONObject("data") == null){
                return;
            }
            JSONArray list = result.getJSONObject("data").getJSONArray("result");
            StringBuffer msg = new StringBuffer("");
            for (Object metricO:list) {
                JSONObject metric = (JSONObject) metricO;
                Integer product = metric.getJSONObject("metric").getInteger("level1_value");
                if ("476".equals(product) || "394".equals(product) || "485".equals(product)){
                    continue;
                }
                String desc = metric.getJSONObject("metric").getString("level3_value");
                if ("EXPOSURE_LIMIT".equals(desc) || "CACHE_ERROR".equals(desc)){
                    continue;
                }
                String rateStr = JSONObject.parseArray(metric.getJSONArray("value").toJSONString(), String.class).get(1);
                BigDecimal rate = new BigDecimal(rateStr).setScale(2, ROUND_HALF_UP);
                if (rate.compareTo(new BigDecimal(rateInt)) >= 0) {
                    String msg1 = "> APPID："+product+",异常类型："+BusinessExceptionEnum.descFromValue(desc)+",当前错误次数："+rate.toString()+" \n\n";
                    msg.append(msg1);
                }
            }
            String alertMsg = msg.toString();
            if (StringUtils.isNotBlank(msg.toString())){
                XxlJobLogger.log("提现报警开始预警");
                AlertRecord alertRecord = alertRecordService.insertAlertRecord("Request错误率异常","RequestException", AlertModel.BUSINESS_EXPETION,
                        alertMsg,
                        AlertStatus.INIT,DingTailService.dsetData, AlertType.DINGDING);
                DingTailService.sendMarkdownMsg(alertRecord);
            }
            XxlJobLogger.log("Request错误结束检测");
        }catch (Exception e){
            log.error("",e);
        }


    }


}

