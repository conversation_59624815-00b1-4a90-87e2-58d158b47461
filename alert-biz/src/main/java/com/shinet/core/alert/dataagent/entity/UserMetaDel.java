package com.shinet.core.alert.dataagent.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableField;
import java.io.Serializable;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * 用户 meta data
 * </p>
 *
 * <AUTHOR>
 * @since 2021-11-26
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value="UserMetaDel对象", description="用户 meta data")
public class UserMetaDel implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "用户ID")
    @TableId(value = "user_id", type = IdType.INPUT)
    private Long userId;

    @ApiModelProperty(value = "手机号")
    private Long pkgId;

    @ApiModelProperty(value = "微信 unionId")
    @TableField("union_Id")
    private String unionId;

    @ApiModelProperty(value = "头像")
    private String photoUrl;

    @ApiModelProperty(value = "昵称")
    private String nickName;

    @ApiModelProperty(value = "账户密码，暂时不用")
    private String password;

    private Integer state;

    private Integer os;

    private String brand;

    @ApiModelProperty(value = "下载渠道")
    private String channel;

    private String deviceId;

    @ApiModelProperty(value = "1.0.0")
    private String appVersion;

    private String romVersion;

    private String osVersion;

    @ApiModelProperty(value = "创建时间")
    private Long createTime;

    @ApiModelProperty(value = "更新时间")
    private Long updateTime;


}
