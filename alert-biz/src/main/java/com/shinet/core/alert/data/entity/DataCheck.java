package com.shinet.core.alert.data.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import java.util.Date;
import com.baomidou.mybatisplus.annotation.TableId;
import java.io.Serializable;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR>
 * @since 2021-08-03
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value="DataCheck对象", description="")
public class DataCheck implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(value = "check_no", type = IdType.INPUT)
    private String checkNo;

    private Date logday;

    private String product;

    private String event;

    private String adAction;

    private Integer hour;

    @ApiModelProperty(value = "曝光数")
    private Long exposureNum;

    @ApiModelProperty(value = "总数")
    private Long eventNum;

    private Date createTime;

    private Date updateTime;


}
