package com.shinet.core.alert.coreservice.service;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.shinet.core.alert.coreservice.entity.IosCheckRecord;
import com.shinet.core.alert.coreservice.mapper.IosCheckRecordMapper;
import com.shinet.core.alert.safe.entity.IosCheckUrl;
import com.shinet.core.alert.safe.service.IosCheckUrlService;
import org.springframework.stereotype.Service;

@Service
public class IosCheckRecordService extends ServiceImpl<IosCheckRecordMapper, IosCheckRecord> {

    public void save(IosCheckUrl iosCheckUrl, IosCheckUrlService.Res res) {
        save(new IosCheckRecord(iosCheckUrl, res));
    }

    public IosCheckRecord getTopOne(IosCheckUrl iosCheckUrl) {
        return this.baseMapper.getTopOne(iosCheckUrl.getProduct());
    }

    public IosCheckRecord countHealth(IosCheckUrl iosCheckUrl) {
        return this.baseMapper.countHealth(iosCheckUrl.getProduct());
    }
}
