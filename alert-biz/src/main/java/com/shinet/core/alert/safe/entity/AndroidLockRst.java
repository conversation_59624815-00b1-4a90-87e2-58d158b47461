package com.shinet.core.alert.safe.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import io.swagger.annotations.ApiModel;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.Date;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR>
 * @since 2024-03-27
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value="AndroidLockRst对象", description="")
public class AndroidLockRst implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    private String product;

    private String os;

    private String oaid;

    private String imei;

    private String androidId;

    private String ip;

    private String city;

    private String dsp;

    private Integer isOcpc;

    private String storeName;

    private String channel;

    private String appVersion;

    private String lockFlag;

    private String userId;

    private String remark;

    private Date createTime;

    private Date updateTime;


}
