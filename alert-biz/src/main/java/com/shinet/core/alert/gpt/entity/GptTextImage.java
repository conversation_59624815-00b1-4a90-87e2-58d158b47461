package com.shinet.core.alert.gpt.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import java.util.Date;
import com.baomidou.mybatisplus.annotation.TableId;
import java.io.Serializable;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR>
 * @since 2023-04-23
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value="GptTextImage对象", description="")
public class GptTextImage implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    private String product;

    private Integer imgStatus;

    private String imgs;

    private String userId;

    private String genType;

    private String promptsSource;

    @ApiModelProperty(value = "反向提示词")
    private String promptsN;

    private Integer width;

    private Integer height;

    private String promptsEn;

    @ApiModelProperty(value = "图生图原始图片")
    private String imgsrcs;

    private Integer sendClient;

    private String mstyle;

    private String redImgs;

    private Integer imgFlag;

    private Integer redScore;

    private Integer costTime;

    private Integer batchNum;

    private Date createTime;

    private Date updateTime;


}
