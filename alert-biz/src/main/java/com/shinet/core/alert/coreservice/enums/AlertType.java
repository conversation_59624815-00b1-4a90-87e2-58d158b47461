package com.shinet.core.alert.coreservice.enums;

public enum AlertType {
    DINGDING(1,"钉钉"),
    PHONE(2,"电话"),
    DINGPHONE(3,"钉钉+电话"),
    ;
    public Integer value;
    public String name;

    AlertType(Integer value, String name) {
        this.value = value;
        this.name = name;
    }

    public static AlertType getStatus(Integer value) {
        if (value != null) {
            AlertType[] otypes = AlertType.values();
            for (AlertType memberType : otypes) {
                if (value.equals(memberType.value)) {
                    return memberType;
                }
            }
        }
        return null;
    }
}
