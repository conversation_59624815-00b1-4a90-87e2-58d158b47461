package com.shinet.core.alert.dsp.service;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.shinet.core.alert.dsp.entity.KuaishouSupplementOrderRealtime;
import com.shinet.core.alert.dsp.mapper.KuaishouSupplementOrderRealtimeMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigInteger;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.List;
import java.util.Set;

@Service
public class KuaishouSupplementOrderRealtimeService extends ServiceImpl<KuaishouSupplementOrderRealtimeMapper, KuaishouSupplementOrderRealtime> {

    @Autowired
    KuaishouSupplementOrderRealtimeMapper kuaishouSupplementOrderRealtimeMapper;

    public List<KuaishouSupplementOrderRealtime> queryUserSupplementCostByAdvertiserId(String advertiserId, Set<BigInteger> taskIdList, int day) {
        // 获取day天前的日期
        String preDay = LocalDate.now().minusDays(day).format(DateTimeFormatter.ofPattern("yyyy-MM-dd"));
        String nowDay = LocalDate.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd"));

        return kuaishouSupplementOrderRealtimeMapper.queryUserCost(advertiserId,taskIdList,preDay,nowDay);
    }


    public List<KuaishouSupplementOrderRealtime> queryTaskSupplementCostByAdvertiserId(String advertiserId, Set<BigInteger> taskIdList, int day) {
        // 获取day天前的日期
        String preDay = LocalDate.now().minusDays(day).format(DateTimeFormatter.ofPattern("yyyy-MM-dd"));
        String nowDay = LocalDate.now().minusDays(1).format(DateTimeFormatter.ofPattern("yyyy-MM-dd"));

        return kuaishouSupplementOrderRealtimeMapper.queryTaskCost(advertiserId,taskIdList,preDay,nowDay);
    }


    public List<KuaishouSupplementOrderRealtime> queryUserSupplementCostByAdvertiserId2(String advertiserId, Set<BigInteger> taskIdList, String preDay) {

        return kuaishouSupplementOrderRealtimeMapper.queryUserCost2(advertiserId,taskIdList,preDay.substring(0,10));
    }
}
