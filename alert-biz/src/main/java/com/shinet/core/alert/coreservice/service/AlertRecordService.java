package com.shinet.core.alert.coreservice.service;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.shinet.core.alert.coreservice.entity.AlertRecord;
import com.shinet.core.alert.coreservice.enums.AlertModel;
import com.shinet.core.alert.coreservice.enums.AlertStatus;
import com.shinet.core.alert.coreservice.enums.AlertType;
import com.shinet.core.alert.coreservice.mapper.AlertRecordMapper;
import com.shinet.core.alert.util.DateUtils;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.HashSet;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

/**
* <p>
    *  服务实现类
    * </p>
*
* <AUTHOR>
* @since 2021-10-09
*/
@Service
public class AlertRecordService extends ServiceImpl<AlertRecordMapper, AlertRecord> {

    public AlertRecord insertAlertRecord(String jobName, String jobNameCn, AlertModel alertModel, String alertMsg,
                                         AlertStatus alertStatus, Set<String> alertPhone, AlertType alertType){
        return insertAlertRecord(jobName,jobNameCn, alertModel, alertMsg,0D,0D,0,0,
                alertStatus, alertPhone,alertType);
    }
    public AlertRecord insertAlertRecord(String jobName, String jobNameCn, AlertModel alertModel, String alertMsg,
                             Double setRate,Double failRate,Integer sucNum,Integer failNum,
                             AlertStatus alertStatus, Set<String> alertPhone, AlertType alertType){
        Date date = new Date();
        AlertRecord alertRecord = new AlertRecord();
        alertRecord.setAlertStatus(alertStatus.value);
        alertRecord.setAlertMsg(alertMsg);
        if(alertPhone!=null && alertPhone.size()>0){
            alertRecord.setAlertPhone(alertPhone.stream().collect(Collectors.joining(",")));
        }
        alertRecord.setJobName(jobName);
        alertRecord.setJobNameCn(jobNameCn);
        alertRecord.setModelName(alertModel.name);

        alertRecord.setSetRate(setRate);
        alertRecord.setSuccessNum(sucNum);
        alertRecord.setFailedNum(failNum);
        alertRecord.setFailRate(failRate);

        alertRecord.setUpdateTime(date);
        alertRecord.setCreateTime(date);
        alertRecord.setHour(date.getHours());
        alertRecord.setLogday(DateUtils.formatDateForYMD(date));

        alertRecord.setMsgType(alertType.name);
        try{
            save(alertRecord);
        }catch (Exception e){
            log.error("",e);
        }
        return alertRecord;
    }


    public void insertNoAlertRecord(String jobName, String jobNameCn, AlertModel alertModel, String alertMsg,
                                  Double setRate,Double failRate,Integer sucNum,Integer failNum,AlertType alertType){
       insertAlertRecord(jobName, jobNameCn, alertModel, alertMsg,
               setRate, failRate, sucNum, failNum,
               AlertStatus.NOMAL, new HashSet<String>(),alertType);
    }

    public List<AlertRecord> queryAlertRecord(String jobName, String alertMsg){
        return lambdaQuery().eq(AlertRecord::getJobName, jobName).like(AlertRecord::getAlertMsg, alertMsg).list();
    }

}
