package com.shinet.core.alert.dataagent.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import java.io.Serializable;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * 用户地理位置信息
 * </p>
 *
 * <AUTHOR>
 * @since 2021-11-26
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value="UserLbsDel对象", description="用户地理位置信息")
public class UserLbsDel implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    private Long userId;

    @ApiModelProperty(value = "Lat,lng")
    private String gpsLatLng;

    @ApiModelProperty(value = "region_code")
    private String gpsCity;

    @ApiModelProperty(value = "Gps  信息更新次数，只有当城市所在地发生变化时才会发生")
    private Integer gpsUpdateCount;

    private String mobile;

    @ApiModelProperty(value = "region_code")
    private String mobileCity;

    @ApiModelProperty(value = "手机号修改次数")
    private Integer mobileUpdateCount;

    private String ip;

    private String ipCity;

    private Integer ipUpdateCount;

    @ApiModelProperty(value = "基站信息， json")
    private String bs;

    private String bsCity;

    private Integer bsUpdateCount;

    private Long createTime;

    private Long updateTime;


}
