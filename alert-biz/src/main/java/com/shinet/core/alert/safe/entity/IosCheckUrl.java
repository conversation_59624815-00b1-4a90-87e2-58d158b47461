package com.shinet.core.alert.safe.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import java.io.Serializable;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR>
 * @since 2023-11-16
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value="IosCheckUrl对象", description="")
public class IosCheckUrl implements Serializable {

    private static final long serialVersionUID = 1L;

    private String product;

    private String productName;

    private String url;

    private String appStoreUrl;

    private Integer upStatus;

    private Integer noStatus;

    private String phone;

    private String email;

    private String authCode;

    private String password;
}
