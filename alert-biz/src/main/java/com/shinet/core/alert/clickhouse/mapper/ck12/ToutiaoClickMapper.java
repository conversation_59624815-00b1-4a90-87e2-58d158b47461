package com.shinet.core.alert.clickhouse.mapper.ck12;

import com.shinet.core.alert.clickhouse.entity.ToutiaoClick;
import org.apache.ibatis.annotations.Select;

import java.util.List;

public interface ToutiaoClickMapper {
    @Select({"select * from ods.toutiao_click_local_temp where logday = '${logday}' limit ${offset},${limit}"})
    List<ToutiaoClick> queryClickByOffset(long offset, int limit, String logday);

    @Select({"select count(1) from ods.toutiao_click_local_temp where logday = '${logday}'"})
    long countLogdayClick(String logday);
}
