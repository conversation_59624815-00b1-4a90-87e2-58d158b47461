package com.shinet.core.alert.dsp.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.shinet.core.alert.dsp.entity.OcpcMisAct;
import com.shinet.core.alert.dsp.entity.OcpcMisActive;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;
import java.util.Map;

/**
 * <p>
 *  Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2023-06-21
 */
public interface OcpcMisActiveMapper extends BaseMapper<OcpcMisActive> {
    @Select("select dsp,project,count(1) as cnum from ocpc_mis_active where os='${osName}'  and create_time>'${startTime}' and channel!='neilaxin' and dsp is not null GROUP BY dsp,project order by cnum  desc;")
    List<OcpcMisAct> queryStatusCount(@Param("osName")String osName, @Param("startTime") String startTime);


    @Select("select dsp,count(1) as cnum from ocpc_mis_active where os='${osName}'  and create_time>'${startTime}' and channel!='neilaxin' and dsp is not null GROUP BY dsp order by cnum  desc;")
    List<OcpcMisAct> queryAllDsp(@Param("osName")String osName, @Param("startTime") String startTime);


    @Select("select product,count(1) as cnum from ocpc_mis_active where os='${osName}'  and create_time>'${startTime}' and channel not in ('neilaxin','ksmin') and dsp is not null GROUP BY product order by cnum  desc;")
    List<OcpcMisAct> queryProject(@Param("osName")String osName, @Param("startTime") String startTime);


    @Select("select dsp,product,count(1) as cnum from ocpc_mis_active where os='${osName}'  and create_time>'${startTime}' and product='${product}' " +
            " and channel not in ('neilaxin','ksmin')  and dsp is not null and ip!='***************' GROUP BY dsp,product order by cnum  desc;")
    List<OcpcMisAct> queryStatusCount(@Param("osName")String osName, @Param("startTime") String startTime, @Param("product") String product);
}
