package com.shinet.core.alert.bpcommon.mapper;

import com.shinet.core.alert.bpcommon.entity.WechatOpenId;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;

/**
 * <p>
 *  Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2021-11-26
 */
public interface WechatOpenIdMapper extends BaseMapper<WechatOpenId> {
    /**
     * 用户 OpenId
     * @param userId
     * @param wechatId
     * @return
     */
    @Select("select * from wechat_open_id where user_id=#{userId} and wechat_id=#{wechatId}")
    WechatOpenId findByUserIdAndWechatId(
            @Param("userId") Long userId,
            @Param("wechatId") int wechatId
    );

    @Update("delete from wechat_open_id where user_id=#{userId} and wechat_id=#{wechatId}")
    int deleteByUserIdAndWechatId(
            @Param("userId") Long userId,
            @Param("wechatId") int wechatId
    );
}
