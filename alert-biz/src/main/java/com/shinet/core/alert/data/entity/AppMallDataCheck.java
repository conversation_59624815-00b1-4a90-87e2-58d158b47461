package com.shinet.core.alert.data.entity;

import lombok.Data;

import java.util.Date;

@Data
public class AppMallDataCheck {
    /**
     * select app_name,LEFT(create_time,10) as create_time,wechat_message,count(1) as cnum from 	bp_mall.wechat_message
     * where create_time>current_date
     * and wechat_message not like '%非实名用户账号不可发放%'
     * and wechat_message not like '%存在风险%'
     * and wechat_message not like '%系统繁忙%'
     * GROUP BY LEFT(create_time,10),wechat_message,app_name ORDER BY cnum desc;
     */
    private String appName;
    private Date createTime;
    private int cnum;
    private String wechatMessage;

}
