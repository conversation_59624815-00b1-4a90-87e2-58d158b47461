package com.shinet.core.alert.dsp.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.google.common.collect.ImmutableMap;
import com.shinet.core.alert.dsp.entity.AdPlanConfig;
import com.shinet.core.alert.dsp.entity.Product;
import com.shinet.core.alert.dsp.mapper.ProductMapper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <p>
 *  服务实现类
 * </p>
 *
 * <AUTHOR>
 */
@Service
@Slf4j
public class ProductService extends ServiceImpl<ProductMapper, Product> implements IService<Product> {

    /** 产品中文名 -> Product */
    private static Map<String, Product> productCnMap = new HashMap<>();
    private static Map<String, String> productNameCnMap = new HashMap<>();
    private static Map<Integer, Product> productIdMap = new HashMap<>();

    private static Map<String, String> productTeamMap = new HashMap<>();
    /** 报警电话组别转化 */
    private static Map<String, String> teamTransMap = ImmutableMap.of("1组", "项目一组", "5组", "项目五组", "7组", "项目七组", "10组", "项目十组");



    @Scheduled(fixedDelay = 1000 * 60)
    public void reloadConfig() {
        List<Product> list = lambdaQuery().eq(Product::getDelFlag, 0).list();

        productCnMap = list.stream().collect(Collectors.toMap(k-> k.getRemark(), k->k, (oldVal, newVal)->oldVal));
        productIdMap = list.stream().collect(Collectors.toMap(Product::getAppId, k->k, (oldVal, newVal)->oldVal));
        productTeamMap = list.stream().collect(Collectors.toMap(k-> k.getRemark(), k->k.getTeamName(), (oldVal, newVal)->oldVal));
        productNameCnMap = list.stream().collect(Collectors.toMap(Product::getName, Product::getRemark, (oldVal, newVal) -> oldVal));
    }
    public Product getByCnName(String productCn) {
        return productCnMap.get(productCn);
    }

    public String getTeamByCnName(String productCn) {
        return productTeamMap.get(productCn);
    }
    public String getTransTeamByCnName(String productCn) {
        String teamName = productTeamMap.get(productCn);
        if (StringUtils.isBlank(teamName)) {
            return null;
        }
        return teamTransMap.get(teamName);
    }

    public List<AdPlanConfig> getGroupDownurl(){
        return baseMapper.getGroupDownurl();
    }

    /*public List<String> diffGroupDownurl(){
        return baseMapper.diffGroupDownurl();
    }*/

    public String getProductRemarkByName(String name) {

        if (StringUtils.isBlank(name)) {
            return null;
        }

        return productNameCnMap.get(name);
    }

    public List<String> getAllProductCnNames(){
        return new ArrayList<>(productCnMap.keySet());
    }
}
