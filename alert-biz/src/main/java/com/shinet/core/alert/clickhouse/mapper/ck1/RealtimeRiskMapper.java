package com.shinet.core.alert.clickhouse.mapper.ck1;

import com.shinet.core.alert.clickhouse.ProductRoiMinute;
import com.shinet.core.alert.clickhouse.entity.*;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.time.LocalDateTime;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2022/12/8
 */
public interface RealtimeRiskMapper {

    @Select({" select r1.ad_id, " +
            "       max_ecpm, " +
            "       own_user, " +
            "       cal_device, " +
            "       call_back, " +
            "       own_reward," +
            "       product, " +
            "       (r2.own_reward - r1.call_back) / r2.own_reward * 100 as not_call_back_rate " +
            " from ( " +
            "         select ad_id, count() as call_back, max(toFloat64OrZero(price)) as max_ecpm, uniqExact(device_id) as cal_device " +
            "         from ods.ad_point_dist " +
            "         where logday = today() " +
            "           and dsp = 'kuaishou' " +
            "           and uri != '/ap-data-consumer/upload' " +
            "         group by ad_id " +
            "         ) r1 " +
            "         left join ( " +
            "    select product,ad_id, count() as own_reward, uniqExact(device_id) as own_user " +
            "    from ods.event_dist " +
            "    where logday = today() " +
            "      and ad_action = 'reward' " +
            "    group by ad_id,product " +
            "    ) r2 on r1.ad_id = r2.ad_id " +
            " where not_call_back_rate > 50 " +
            "  and max_ecpm >= 500"})
    List<KsCallBackRate> queryKsCallBackRate();


    @Select({"select x1.product          as product, " +
            "       x1.os               as os, " +
            "       x1.channel          as channel, " +
            "       x1.sumArpu / x2.dnu as arpu, " +
            "       x2.dnu              as dnu, " +
            "       x3.cost / dnu       as cpa " +
            "from ( " +
            "         select k1.product as product, k1.os as os, k1.channel as channel, sum(arpu) as sumArpu " +
            "         from ( " +
            "                  select product, os, channel, device_id, sum(toFloat64OrZero(extend1) / 1000) as arpu " +
            "                  from ods.event_exposure_dist " +
            "                  where logday = today() " +
            "                    and ad_action = 'exposure' " +
            "                    and hour >= toHour(now()) - 1 " +
            "                    and ad_id global in (select toString(ad_id) from dwd.tb_ap_bidding_dist) " +
            "                    and device_id global in ( " +
            "                      select device_id from dwd.device_dist where logday = today() and hour >= toHour(now()) - 1 " +
            "                  ) " +
            "                  group by product, os, channel, device_id " +
            "                  union all " +
            "                  select product, " +
            "                         os, " +
            "                         channel, " +
            "                         device_id, " +
            "                         sum(if(toFloat64OrZero(extend1) > 3000, r2.ecpm / 1000, " +
            "                                toFloat64OrZero(extend1) / 1000)) as arpu " +
            "                  from ( " +
            "                           select product, os, channel, device_id, ad_id, extend1 " +
            "                           from ods.event_exposure_dist " +
            "                           where logday = today() " +
            "                             and ad_action = 'exposure' " +
            "                             and hour >= toHour(now()) - 1 " +
            "                             and ad_id global not in (select toString(ad_id) from dwd.tb_ap_bidding_dist) " +
            "                             and device_id global in ( " +
            "                               select device_id " +
            "                               from dwd.device_dist " +
            "                               where logday = today() " +
            "                                 and hour >= toHour(now()) - 1 " +
            "                           ) " +
            "                           ) r1 " +
            "                           left join dwd.tb_ap_ad_budget_dist r2 on r1.ad_id = toString(r2.id) " +
            "                  group by product, os, channel, device_id " +
            "                  ) k1 " +
            "                  left join ( select product, os, channel, device_id " +
            "                              from dwd.device_dist " +
            "                              where logday = today() " +
            "                                and hour >= toHour(now()) - 1) k2 " +
            "                            on k1.product = k2.product and k1.channel = k2.channel and k1.device_id = k2.device_id " +
            "         where k2.device_id != '' " +
            "         group by k1.product, k1.os, k1.channel " +
            "         ) x1 " +
            "         left join ( " +
            "    select product, os, channel, uniqExact(device_id) as dnu " +
            "    from dwd.device_dist " +
            "    where logday = today() " +
            "      and hour >= toHour(now()) - 1 " +
            "    group by product, os, channel " +
            "    ) x2 on x1.product = x2.product and x1.os = x2.os and x1.channel = x2.channel " +
            "         left join ( " +
            "    select a1.product as product, a1.os as os, a1.channel as channel, sum(ifNull(a2.cpa, 0)) as cost " +
            "    from ( " +
            "             select * from dwd.device_dist where logday = today() and hour >= toHour(now()) - 1 and os = 'android' " +
            "             ) a1 " +
            "             left join ( " +
            " " +
            "        select product, " +
            "               os, " +
            "               device_id, " +
            "               case " +
            "                   when t1.ocpc_channel = 'toutiao' then t2.cpa " +
            "                   when t1.ocpc_channel = 'guangdiantong' then t3.cpa / 100  " +
            "                   when t1.ocpc_channel = 'kuaishou' then t4.cpa" +
            "                   else 0 end as cpa " +
            "        from ( " +
            "                 select * from dwd.product_ocpc_activate_dist where logday = today() " +
            "                 ) t1 " +
            "                 left join (select ad_id, " +
            "                                   creative_id, " +
            "                                   advertiser_id, " +
            "                                   sum(rebate_cost) as costx, " +
            "                                   sum(active)      as actives, " +
            "                                   costx / actives  as cpa " +
            "                            from old_mysql_ads.toutiao_report_ideaday_process " +
            "                            where logday = today() " +
            "                            group by ad_id, creative_id, advertiser_id " +
            "                            having actives > 0) t2 " +
            "                           on t1.advertiser_id = t2.advertiser_id and t1.creative_id = toString(t2.creative_id) " +
            "                 left join (select creative_id, " +
            "                                   advertiser_id, " +
            "                                   sum(rebate_cost)     as costx, " +
            "                                   sum(activated_count) as actives, " +
            "                                   costx / actives      as cpa " +
            "                            from old_mysql_ads.tencent_creative_video_data_process " +
            "                            where logday = today() " +
            "                            group by creative_id, advertiser_id " +
            "                            having actives > 0) t3 on t1.advertiser_id = toString(t3.advertiser_id) and " +
            "                                                      t1.creative_id = toString(t3.creative_id) " +
            "                 left join (select creative_id, " +
            "                                   advertiser_id, " +
            "                                   sum(rebate_cost) as costx, " +
            "                                   sum(activation)  as actives, " +
            "                                   costx / actives  as cpa " +
            "                            from old_mysql_ads.kuaishou_creative_video_data_process " +
            "                            where logday = today() " +
            "                            group by creative_id, advertiser_id " +
            "                            having actives > 0) t4 on t1.advertiser_id = toString(t4.advertiser_id) and " +
            "                                                      t1.creative_id = toString(t4.creative_id) " +
            "        ) a2 on a1.product = a2.product and a1.os = a2.os and a1.device_id = a2.device_id " +
            "    group by a1.product, a1.os, a1.channel " +
            "    ) x3 on x1.product = x3.product and x1.os = x3.os and x1.channel = x3.channel " +
            "where x2.dnu > 0 " +
            "  and arpu > cpa " +
            "  and match(x1.channel, '^csj|^tt|^gdt|^ks') = 1 " +
            "  and cpa > 0 " +
            "  and arpu > 6;"})
    List<ProductChannelArpuCpa> queryArpuAndCpa();


    @Select({" select r1.product_name                                 as product, " +
            "       r1.product_group                                  as product_group, " +
            "       r1.os                                             as os, " +
            "       r1.dspx                                           as dsp, " +
            "       r1.hour, " +
            "       r1.cost                                           as todayCost, " +
            "       r1.active                                         as todayActive, " +
            "       r1.cpa                                            as todayCpa, " +
            "       r2.cost                                           as yesterdayCost, " +
            "       r2.active                                         as yesterdayActive, " +
            "       r2.cpa                                            as yesterdayCpa, " +
            "       r3.todayArpu                                      as todayArpu, " +
            "       r3.yesterdayArpu                                  as yesterdayArpu, " +
            "       (todayCpa - yesterdayCpa) / yesterdayCpa          as cpaRate, " +
            "       (todayActive - yesterdayActive) / yesterdayActive as activeRate, " +
            "       (todayArpu - yesterdayArpu) / yesterdayArpu       as arpuRate " +
            " from ( " +
            "         select product_name, " +
            "                product_group, " +
            "                os, " +
            "                hour, " +
            "                case " +
            "                    when dsp = 'toutiao' and (match(pos_name, '头条') or match(pos_name, '智选')) then '头条主站' " +
            "                    when dsp = 'toutiao' and match(pos_name, '穿山甲') then '穿山甲' " +
            "                    when dsp = 'toutiao' then '头条主站' " +
            "                    when dsp = 'kuaishou' then '快手' " +
            "                    when dsp = 'guangdiantong' then '广点通' " +
            "                    when dsp = 'baidu' then '百度' " +
            "                    else dsp end                    dspx, " +
            "                sum(cost)                        as cost, " +
            "                sum(active)                      as active, " +
            "                if(active = 0, 0, cost / active) as cpa " +
            "         from ads.realtime_advertiser_cost " +
            "         where logday = today() " +
            "           and hour = toHour(now()) - 1 " +
            "           and dsp != 'kuaishoudaren' " +
            "         group by product_name, product_group, os, hour, dspx " +
            "         ) r1 " +
            "         left join ( " +
            "    select product_name, " +
            "           product_group, " +
            "           os, " +
            "           hour, " +
            "           case " +
            "               when dsp = 'toutiao' and (match(pos_name, '头条') or match(pos_name, '智选')) then '头条主站' " +
            "               when dsp = 'toutiao' and match(pos_name, '穿山甲') then '穿山甲' " +
            "               when dsp = 'toutiao' then '头条主站' " +
            "               when dsp = 'kuaishou' then '快手' " +
            "               when dsp = 'guangdiantong' then '广点通' " +
            "               when dsp = 'baidu' then '百度' " +
            "               else dsp end                    dspx, " +
            "           sum(cost)                        as cost, " +
            "           sum(active)                      as active, " +
            "           if(active = 0, 0, cost / active) as cpa " +
            "    from ads.realtime_advertiser_cost " +
            "    where logday = yesterday() " +
            "      and hour = toHour(now()) - 1 " +
            "      and dsp != 'kuaishoudaren' " +
            "    group by product_name, product_group, os, hour, dspx " +
            "    ) r2 on r1.hour = r2.hour and r1.product_group = r2.product_group and r1.product_name = r2.product_name and " +
            "            r1.os = r2.os and r1.dspx = r2.dspx " +
            "         left join ( " +
            "    select k1.product, " +
            "           k1.product_name, " +
            "           if(k1.dsp = 'baidufeed', 'baidu', k1.dsp) as dsp, " +
            "           k1.os, " +
            "           k1.arpu                                   as todayArpu, " +
            "           k2.arpu                                   as yesterdayArpu " +
            "    from ( " +
            "             select t1.product           as product, " +
            "                    t4.tf_channel        as dsp, " +
            "                    t3.product_name      as product_name, " +
            "                    t1.os                as os, " +
            "                    sum(sumEcpm / 1000)  as inc, " +
            "                    uniqExact(t1.userId) as tv, " +
            "                    inc / tv             as arpu " +
            "             from ( " +
            "                      select * " +
            "                      from dwd.user_event_dist " +
            "                      where toDate(create_time) = today() " +
            "                        and event_type = 0 " +
            "                        and toHour(create_time) = toHour(now()) - 1 " +
            "                        and account_id global not in (select distinct toString(advertiser_id) " +
            "                                                      from config.auth_kuaishou_app_dist " +
            "                                                      where account_type = 2) " +
            "                      ) t1 " +
            "                      left join ( " +
            "                 select product, " +
            "                        userid, " +
            "                        sum(if(toFloat64OrZero(extend1) > 0 and toFloat64OrZero(extend1) < 10000, " +
            "                               toFloat64OrZero(extend1), m2.ecpm)) as sumEcpm " +
            "                 from ods.event_exposure_dist m1 " +
            "                          left join dwd.tb_ap_ad_budget_dist m2 on m1.ad_id = toString(m2.id) " +
            "                 where logday = today() " +
            "                   and ad_action = 'exposure' " +
            "                   and hour = toHour(now()) - 1 " +
            "                 group by product, userid " +
            "                 ) t2 on t1.userId = t2.userid and t1.product = t2.product " +
            "                      left join dwd.product_map_dist t3 on t1.product = t3.product " +
            "                      left join ( " +
            "                 select advertiser_id, " +
            "                        case " +
            "                            when dsp = 'toutiao' and (match(pos_name, '头条') or match(pos_name, '智选')) then '头条主站' " +
            "                            when dsp = 'toutiao' and match(pos_name, '穿山甲') then '穿山甲' " +
            "                            when dsp = 'toutiao' then '头条主站' " +
            "                            when dsp = 'kuaishou' then '快手' " +
            "                            when dsp = 'guangdiantong' then '广点通' " +
            "                            when dsp = 'baidu' then '百度' " +
            "                            else dsp end as tf_channel " +
            "                 from ads.realtime_advertiser_cost " +
            "                 where logday = today() " +
            "                 group by advertiser_id, tf_channel " +
            "                 ) t4 on t1.account_id = t4.advertiser_id " +
            "             group by t1.product, t4.tf_channel, t1.os, t3.product_name " +
            "             ) k1 " +
            "             left join( " +
            "        select t1.product           as product, " +
            "               t4.tf_channel        as dsp, " +
            "               t3.product_name      as product_name, " +
            "               t1.os                as os, " +
            "               sum(sumEcpm / 1000)  as inc, " +
            "               uniqExact(t1.userId) as tv, " +
            "               inc / tv             as arpu " +
            "        from ( " +
            "                 select * " +
            "                 from dwd.user_event_dist " +
            "                 where toDate(create_time) = yesterday() " +
            "                   and event_type = 0 " +
            "                   and toHour(create_time) = toHour(now()) - 1 " +
            "                   and account_id global not in (select distinct toString(advertiser_id) " +
            "                                                 from config.auth_kuaishou_app_dist " +
            "                                                 where account_type = 2) " +
            "                 ) t1 " +
            "                 left join ( " +
            "            select product, " +
            "                   userid, " +
            "                   sum(if(toFloat64OrZero(extend1) > 0 and toFloat64OrZero(extend1) < 10000, " +
            "                          toFloat64OrZero(extend1), m2.ecpm)) as sumEcpm " +
            "            from ods.event_exposure_dist m1 " +
            "                     left join dwd.tb_ap_ad_budget_dist m2 on m1.ad_id = toString(m2.id) " +
            "            where logday = yesterday() " +
            "              and ad_action = 'exposure' " +
            "              and hour = toHour(now()) - 1 " +
            "            group by product, userid " +
            "            ) t2 on t1.userId = t2.userid and t1.product = t2.product " +
            "                 left join dwd.product_map_dist t3 on t1.product = t3.product " +
            "                 left join ( " +
            "            select advertiser_id, " +
            "                   case " +
            "                       when dsp = 'toutiao' and (match(pos_name, '头条') or match(pos_name, '智选')) then '头条主站' " +
            "                       when dsp = 'toutiao' and match(pos_name, '穿山甲') then '穿山甲' " +
            "                       when dsp = 'toutiao' then '头条主站' " +
            "                       when dsp = 'kuaishou' then '快手' " +
            "                       when dsp = 'guangdiantong' then '广点通' " +
            "                       when dsp = 'baidu' then '百度' " +
            "                       else dsp end as tf_channel " +
            "            from ads.realtime_advertiser_cost " +
            "            where logday = yesterday() " +
            "            group by advertiser_id, tf_channel " +
            "            ) t4 on t1.account_id = t4.advertiser_id " +
            "        group by t1.product, t4.tf_channel, t1.os, t3.product_name " +
            "        ) k2 on k1.product = k2.product and k1.dsp = k2.dsp and k1.product_name = k2.product_name and k1.os = k2.os " +
            "    ) r3 on r1.product_name = r3.product_name and " +
            "            r1.os = r3.os and r1.dspx = r3.dsp " +
            " where todayActive >= 350 " +
            "  and activeRate > 0.5 " +
            "  and cpaRate > 0.2;"})
    List<ProductCpaHour> queryCpaLastHour();

    @Select({"select '头条'                    as dsp,\n" +
            "       app_name                as product,\n" +
            "       proudct_group,\n" +
            "       os,\n" +
            "       toString(advertiser_id) as advertiser_id,\n" +
            "       sum(rebate_cost)        as cost,\n" +
            "       sum(active)             as active,\n" +
            "       sum(income)             as income\n" +
            "from old_mysql_ads.toutiao_report_tf_new_ad_process\n" +
            "where logday = today()\n" +
            "group by app_name, proudct_group, os, advertiser_id\n" +
            "having (os='android' and  active > 30\n" +
            "   and income > 0\n" +
            "   and income > cost * 1.5\n" +
            "   and active > 30 ) or (os='ios' and  active > 30\n" +
            "   and income > 0\n" +
            "   and income > cost * 2\n" +
            "   and active > 30 )\n" +
            "union all\n" +
            "select '快手'             as dsp,\n" +
            "       product_name,\n" +
            "       project_group,\n" +
            "       os,\n" +
            "       toString(advertiser_id),\n" +
            "       sum(rebate_cost) as cost,\n" +
            "       sum(activation)  as active,\n" +
            "       sum(income)      as income\n" +
            "from old_mysql_ads.kuaishou_report_data_unit_process\n" +
            "where logday = today()\n" +
            "group by product_name, project_group, os, advertiser_id\n" +
            "having (os='android' and  active > 30\n" +
            "   and income > 0\n" +
            "   and income > cost * 1.5\n" +
            "   and active > 30 ) or (os='ios' and  active > 30\n" +
            "   and income > 0\n" +
            "   and income > cost * 2\n" +
            "   and active > 30 )\n" +
            "union all\n" +
            "select '广点通'                as dsp,\n" +
            "       app_name,\n" +
            "       project_group,\n" +
            "       os,\n" +
            "       toString(advertiser_id),\n" +
            "       sum(rebate_cost)     as cost,\n" +
            "       sum(activated_count) as active,\n" +
            "       sum(income)          as income\n" +
            "from old_mysql_ads.tencent_report_data_main_process\n" +
            "where logday = today()\n" +
            "group by app_name, project_group, os, advertiser_id\n" +
            "having (os='android' and  active > 30\n" +
            "   and income > 0\n" +
            "   and income > cost * 1.5\n" +
            "   and active > 30 ) or (os='ios' and  active > 30\n" +
            "   and income > 0\n" +
            "   and income > cost * 2\n" +
            "   and active > 30 )\n" +
            "union all\n" +
            "select '快手达人'           as dsp,\n" +
            "       product_name,\n" +
            "       product_group,\n" +
            "       os,\n" +
            "       toString(advertiser_id),\n" +
            "       sum(rebate_cost) as cost,\n" +
            "       sum(dnu)         as active,\n" +
            "       sum(income)      as income\n" +
            "from ads.realtime_kuaishou_talent_report\n" +
            "where logday = today()\n" +
            "group by product_name, product_group, os, advertiser_id\n" +
            "having (os='android' and  active > 30\n" +
            "   and income > 100\n" +
            "   and cost > 100\n" +
            "   and income > cost * 1.5) or (os='ios' and  active > 30\n" +
            "   and income > 100\n" +
            "   and cost > 100\n" +
            "   and income > cost * 2)\n" +
            ";"})
    List<ProductChannelArpuCpa> queryAdvertiserCpa();


    @Select({" select r1.logday             as logday, " +
            "       r1.product            as product, " +
            "       r1.os            as os, " +
            "       r1.dnu                as dnu, " +
            "       r2.locked             as locked_ip, " +
            "       locked_ip / dnu * 100 as locked_rate " +
            " from ( " +
            "         select logday, product, os, uniqExact(device_id) as dnu " +
            "         from dwd.device_dist " +
            "         where logday = yesterday() " +
            "         group by logday, product, os " +
            "         order by logday, dnu desc " +
            "         ) r1 " +
            "         left join ( " +
            "    select logday, product, lower(os) as os, uniqExact(ip) as locked " +
            "    from ( " +
            "          select product, os, ip, min(toDate(create_time)) as logday " +
            "          from " +
            "              mysql('pc-2ze50z2lt1j82269r.rwlb.rds.aliyuncs.com:3306', 'core-safe', " +
            "                    'locked_area_record', 'safeacc', 'safeacc!@#$1234') " +
            "          where toDate(create_time) = yesterday() " +
            "          group by product, os, ip " +
            "             ) " +
            "    group by logday, os, product " +
            "    ) r2 on r1.logday = r2.logday and r1.product = r2.product and r1.os = r2.os " +
            " where dnu > 1000 " +
            "  and locked_rate > 15 " +
            " order by r1.logday, r1.dnu"})
    List<LockedRate> queryYesterdayLockArea();

    @Select("select s1.product       as product, " +
            "       s2.product_name  as product_name, " +
            "       s2.product_group as product_group, " +
            "       s1.channel       as channel, " +
            "       s1.lockedIp      as lockedIp " +
            "from ( " +
            "         select product, channel, uniqExact(ip) as lockedIp " +
            "         from " +
            "             mysql('pc-2ze50z2lt1j82269r.rwlb.rds.aliyuncs.com:3306', 'core-safe', 'locked_area_record', " +
            "                   'safeacc', 'safeacc!@#$1234') " +
            "         where toDate(create_time) = today() " +
            "         group by product, channel " +
            "         having lockedIp > 1000 " +
            "         ) s1 " +
            "         left join dwd.product_map_dist s2 on s1.product = s2.product")
    List<LockedChannel> queryTodayRealtimeLockIp();

    @Select({"select   logday,\n" +
            "                   r1.product as product,\n" +
            "                   max(r2.product_name) as  product_name,\n" +
            "                   os,\n" +
            "                   sum(if(ad_action ='video_load_finish_fail',1,0)) as fail_count,\n" +
            "                   count(distinct if(ad_action ='video_load_finish_fail',r1.device_id,null)) as fail_uv,\n" +
            "                   sum(if(ad_action ='video_load_finish',1,0)) as suc_count,\n" +
            "                   sum(if(ad_action ='video_request_count',1,0)) as all_count,\n" +
            "                   uniqExact(r1.device_id) as dau,\n" +
            "                   uniqExact(lku.device_id) as locked_dau,\n" +
            "                   count(distinct if(lku.userid is not null and lku.userid != '',lku.userid,null)) as lock_fail_userids,\n" +
            "                   fail_count/all_count * 100 as rate\n" +
            "from ods.event_dist r1 left join dwd.product_map_dist r2 on r1.product = r2.product\n" +
            "left join (\n" +
            "             select distinct device_id,product,userid from ods.event_dist  where  logday = today()\n" +
            "             and ((event = 'Startup' and lower(extend1) = 'true')\n" +
            "            or (event = 'AppData' and ad_action='lock_new' and lower(extend1) = 'true') )\n" +
            ") lku on r1.product=lku.product and lku.device_id=r1.device_id and lku.userid=r1.userid\n" +
            "where  logday = today()  and product !='swty'\n" +
            "  and product global in(\n" +
            "                     select product from (\n" +
            "                     select product, uniqExact(device_id) as dau\n" +
            "                     from dwd.au_device_dist\n" +
            "                     where logday = yesterday()\n" +
            "                     group by product\n" +
            "                     having dau > 10000\n" +
            "                        )\n" +
            "  )\n" +
            "  and os = 'android'\n" +
            "  and event = 'AdData'\n" +
            "  and time >= addMinutes(now(),-180)\n" +
            "  and ad_action in ('video_load_finish_fail','video_load_finish','video_request_count')\n" +
            "group by logday, r1.product, os having all_count > 0 and rate > 5 and dau > 1000;"})
    List<FailedRequestCount> queryLast60MinusFailedRequestCount();

    @Select({" select r1.product as product, " +
            "       product_name, " +
            "       r1.os as os, " +
            "       channel, " +
            "       r1.ntf_user as ntf_user, " +
            "       r1.dnu as dnu, " +
            "       r1.ntf_rate as ntf_rate " +
            "       from ( " +
            "               select product, " +
            "                      os, " +
            "                      channel, " +
            "                      uniqExact(if(source = '自然量', user_id, null)) as ntf_user, " +
            "                      uniqExact(user_id)                           as dnu, " +
            "                      ntf_user / dnu * 100                              as ntf_rate " +
            "               from dwd.user_active_dist " +
            "               where toDate(create_time) = today() and os !='wmin' and product_part != 'wmin' " +
            "                 and match(channel, '^tt|^ks|^gdt|^csj') = 1 " +
            "               group by product, os, channel " +
            "               having dnu > 200 " +
            "                  and ntf_rate > 25 " +
            " ) r1 left join dwd.product_map_dist r2 on r1.product = r2.product"})
    List<UserActiveChannel> queryUserActiveChannel();


    @Select({" select r1.product as product ,r3.product_name as product_name,r1.os as os,r1.dnu as mdDnu,r2.dnu as wechatDnu " +
            " from ( " +
            "        select product, os, uniqExact(device_id) as dnu " +
            "        from dwd.device_dist " +
            "        where logday = yesterday() " +
            "        group by product, os " +
            "        ) r1 " +
            "        left join ( " +
            "   select product, os, uniqExact(user_id) as dnu " +
            "   from dwd.user_active_dist " +
            "   where toDate(create_time) = yesterday() " +
            "   group by product, os " +
            "   ) r2 on r1.product = r2.product and r1.os = r2.os " +
            " left join dwd.product_map_dist r3 on r1.product = r3.product " +
            " left join ( " +
            "    select product,os,uniqExact(distinct_id) as dis_dnu from ods.event_dist where logday = yesterday() and userid global in ( " +
            "        select toString(user_id) " +
            "           from dwd.user_active_dist " +
            "           where toDate(create_time) = yesterday() " +
            "        ) " +
            "    group by product,os " +
            "    ) r4 on r1.product = r4.product  and r1.os = r4.os " +
            " where r1.dnu > 100 " +
            " and ( " +
            "   ((r2.dnu - r1.dnu) > 0) or (r1.dnu - r2.dnu) / r1.dnu > 0.97 " +
            "   ) " +
            " and ( " +
            "   ((r2.dnu - r4.dis_dnu) > 0) or (r4.dis_dnu - r2.dnu) / r4.dis_dnu > 0.97 " +
            "   )"})
    List<UserActiveNum> queryUserActiveNum();


    @Select({"select product_name,product, " +
            "       sum(if(os='android',amount,0))/100 as android_pay, " +
            "       sum(if(os='ios',amount,0))/100 as ios_pay, " +
            "       sum(amount)/100 as pay, " +
            "       ios_pay/pay as ios_rate " +
            " from dwd.payment_detail_dist where logday = today() and status = 1 " +
            " group by product_name,product " +
            " having pay >50000 and ios_rate > 0.4"})
    List<PaymentRate> queryPaymentRate();
    
    
    @Select(" select s1.product,s1.rc,s2.product_name,s2.product_group from ( " +
            "  select product, uniqExact(distinct_id) as rc " +
            "  from ods.event_dist " +
            "  where logday = today() " +
            "    and channel = 'ALIYUN_MAN_CHANNEL' " +
            "  group by product " +
            "  having (product not in ('tyxfsh','ywrs') and rc > 25) or (rc>50 and product in ('tyxfsh','ywrs')) " +
            " ) s1 left join dwd.product_map_dist s2 on s1.product = s2.product")
    List<AliyunChannelExBean> queryAliyunChannelEx();

    @Select("select today.product as product,\n" +
            "       today.product_group as product_group,\n" +
            "       today.os as os,\n" +
            "       today.dsp as dsp,\n" +
            "       today.sum_active as today_active,\n" +
            "       today.cpa as today_cpa,\n" +
            "       today.arpu as today_arpu,\n" +
            "        today.sum_cost as today_cost,\n" +
            "       today.roi as today_roi,\n" +
            "        yesterday.sum_active as yesterday_active,\n" +
            "        yesterday.cpa as yesterday_cpa,\n" +
            "        yesterday.arpu as yesterday_arpu,\n" +
            "        yesterday.sum_cost as yesterday_cost,\n" +
            "        yesterday.roi as yesterday_roi\n" +
            "from (\n" +
            "     SELECT\n" +
            "    app_name AS product,\n" +
            "    proudct_group AS product_group,  -- 修正拼写错误\n" +
            "    os AS os,\n" +
            "    '头条' AS dsp,\n" +
            "    sum(income) as sum_income,\n" +
            "    sum(withdraw_amount) as sum_withdraw_amount,\n" +
            "    sum(ifNull(active, 0)) AS sum_active,  -- 处理 NULL 值\n" +
            "    sum(ifNull(cost, 0)) AS sum_cost,      -- 处理 NULL 值\n" +
            "    (sum(ifNull(cost, 0)) / sum(CAST(ifNull(active, 0) AS Float64))) AS cpa,\n" +
            "    (sum(ifNull(income, 0)) / sum(CAST(ifNull(active, 0) AS Float64))) AS arpu,\n" +
            "    (sum(ifNull(income, 0)) - sum(ifNull(withdraw_amount, 0))) / sum(ifNull(cost, 0)) AS roi\n" +
            "FROM (\n" +
            "    select * from ads.toutiao_report_tf_new_ad_process_time where data_create_time = (SELECT data_create_time\n" +
            "        FROM ads.toutiao_report_tf_new_ad_process_time\n" +
            "        GROUP BY data_create_time\n" +
            "        ORDER BY abs(toUnixTimestamp(data_create_time) - toUnixTimestamp(now()))  -- 计算时间差\n" +
            "        LIMIT 1))\n" +
            "GROUP BY app_name, proudct_group, os  -- 确保所有非聚合字段都在 GROUP BY 中\n" +
            "\n" +
            "UNION ALL\n" +
            "\n" +
            "SELECT\n" +
            "    product_name AS product,\n" +
            "    project_group AS product_group,\n" +
            "    os AS os,\n" +
            "    '快手' AS dsp,\n" +
            "    sum(income) as sum_income,\n" +
            "    sum(withdraw_amount) as sum_withdraw_amount,\n" +
            "    sum(ifNull(activation, 0)) AS sum_active,   -- 处理 NULL 值\n" +
            "    sum(ifNull(charge, 0)) AS sum_cost,         -- 处理 NULL 值\n" +
            "    (sum(ifNull(charge, 0)) / sum(CAST(ifNull(activation, 0) AS Float64))) AS cpa,\n" +
            "    (sum(ifNull(income, 0)) / sum(CAST(ifNull(activation, 0) AS Float64))) AS arpu,\n" +
            "    (sum(ifNull(income, 0)) - sum(ifNull(withdraw_amount, 0))) / sum(ifNull(charge, 0)) AS roi\n" +
            "FROM (\n" +
            "    select * from ads.kuaishou_report_data_unit_process_time where data_create_time = (SELECT data_create_time\n" +
            "        FROM ads.kuaishou_report_data_unit_process_time\n" +
            "        GROUP BY data_create_time\n" +
            "        ORDER BY abs(toUnixTimestamp(data_create_time) - toUnixTimestamp(now()))  -- 计算时间差\n" +
            "        LIMIT 1))\n" +
            "GROUP BY product_name, project_group, os  -- 确保所有非聚合字段都在 GROUP BY 中\n" +
            "\n" +
            "UNION ALL\n" +
            "\n" +
            "SELECT\n" +
            "    app_name AS product,\n" +
            "    project_group AS product_group,  -- 修正拼写错误\n" +
            "    os AS os,\n" +
            "    '腾讯' AS dsp,\n" +
            "    sum(income) as sum_income,\n" +
            "    sum(withdraw_amount) as sum_withdraw_amount,\n" +
            "    sum(ifNull(activated_count, 0)) AS sum_active,  -- 处理 NULL 值\n" +
            "    sum(CAST(ifNull(cost, 0) as Float64))/100 AS sum_cost,               -- 处理 NULL 值\n" +
            "    (sum(CAST(ifNull(cost, 0) AS Float64))/100 / sum(CAST(ifNull(activated_count, 0) AS Float64))) AS cpa,\n" +
            "    (sum(ifNull(income, 0)) / sum(CAST(ifNull(activated_count, 0) AS Float64))) AS arpu,\n" +
            "    (sum(ifNull(income, 0)) - sum(ifNull(withdraw_amount, 0))) / (sum(CAST(ifNull(cost, 0) AS Float64))/100) AS roi\n" +
            "    FROM (\n" +
            "        select * from ads.tencent_report_data_main_process_time where data_create_time = (SELECT data_create_time\n" +
            "            FROM ads.tencent_report_data_main_process_time\n" +
            "            GROUP BY data_create_time\n" +
            "            ORDER BY abs(toUnixTimestamp(data_create_time) - toUnixTimestamp(now()))  -- 计算时间差\n" +
            "            LIMIT 1))\n" +
            "GROUP BY app_name, project_group, os  ) today\n" +
            "    inner join (\n" +
            "\n" +
            "\n" +
            "    SELECT\n" +
            "    app_name AS product,\n" +
            "    proudct_group AS product_group,  -- 修正拼写错误\n" +
            "    os AS os,\n" +
            "    '头条' AS dsp,\n" +
            "    sum(income) as sum_income,\n" +
            "    sum(withdraw_amount) as sum_withdraw_amount,\n" +
            "    sum(ifNull(active, 0)) AS sum_active,  -- 处理 NULL 值\n" +
            "    sum(ifNull(cost, 0)) AS sum_cost,      -- 处理 NULL 值\n" +
            "    (sum(ifNull(cost, 0)) / sum(CAST(ifNull(active, 0) AS Float64))) AS cpa,\n" +
            "    (sum(ifNull(income, 0)) / sum(CAST(ifNull(active, 0) AS Float64))) AS arpu,\n" +
            "    (sum(ifNull(income, 0)) - sum(ifNull(withdraw_amount, 0))) / sum(ifNull(cost, 0)) AS roi\n" +
            "FROM (\n" +
            "    select * from ads.toutiao_report_tf_new_ad_process_time where data_create_time = (SELECT data_create_time\n" +
            "        FROM ads.toutiao_report_tf_new_ad_process_time\n" +
            "        GROUP BY data_create_time\n" +
            "        ORDER BY abs(toUnixTimestamp(data_create_time) - toUnixTimestamp(DATE_SUB(now(),INTERVAL 1 DAY)))  -- 计算时间差\n" +
            "        LIMIT 1))\n" +
            "GROUP BY app_name, product_group, os  -- 确保所有非聚合字段都在 GROUP BY 中\n" +
            "having sum_cost > 10000\n" +
            "\n" +
            "UNION ALL\n" +
            "\n" +
            "SELECT\n" +
            "    product_name AS product,\n" +
            "    project_group AS product_group,\n" +
            "    os AS os,\n" +
            "    '快手' AS dsp,\n" +
            "    sum(income) as sum_income,\n" +
            "    sum(withdraw_amount) as sum_withdraw_amount,\n" +
            "    sum(ifNull(activation, 0)) AS sum_active,   -- 处理 NULL 值\n" +
            "    sum(ifNull(charge, 0)) AS sum_cost,         -- 处理 NULL 值\n" +
            "    (sum(ifNull(charge, 0)) / sum(CAST(ifNull(activation, 0) AS Float64))) AS cpa,\n" +
            "    (sum(ifNull(income, 0)) / sum(CAST(ifNull(activation, 0) AS Float64))) AS arpu,\n" +
            "    (sum(ifNull(income, 0)) - sum(ifNull(withdraw_amount, 0))) / sum(ifNull(charge, 0)) AS roi\n" +
            "FROM (\n" +
            "    select * from ads.kuaishou_report_data_unit_process_time where data_create_time = (SELECT data_create_time\n" +
            "        FROM ads.kuaishou_report_data_unit_process_time\n" +
            "        GROUP BY data_create_time\n" +
            "        ORDER BY abs(toUnixTimestamp(data_create_time) - toUnixTimestamp(DATE_SUB(now(),INTERVAL 1 DAY))) -- 计算时间差\n" +
            "        LIMIT 1))\n" +
            "GROUP BY product_name, project_group, os\n" +
            "having sum_cost > 10000              -- 确保所有非聚合字段都在 GROUP BY 中\n" +
            "\n" +
            "UNION ALL\n" +
            "\n" +
            "SELECT\n" +
            "    app_name AS product,\n" +
            "    project_group AS product_group,  -- 修正拼写错误\n" +
            "    os AS os,\n" +
            "    '腾讯' AS dsp,\n" +
            "    sum(income) as sum_income,\n" +
            "    sum(withdraw_amount) as sum_withdraw_amount,\n" +
            "    sum(ifNull(activated_count, 0)) AS sum_active,  -- 处理 NULL 值\n" +
            "    sum(CAST(ifNull(cost, 0) as Float64))/100 AS sum_cost,               -- 处理 NULL 值\n" +
            "    (sum(CAST(ifNull(cost, 0) AS Float64))/100 / sum(CAST(ifNull(activated_count, 0) AS Float64))) AS cpa,\n" +
            "    (sum(ifNull(income, 0)) / sum(CAST(ifNull(activated_count, 0) AS Float64))) AS arpu,\n" +
            "    (sum(ifNull(income, 0)) - sum(ifNull(withdraw_amount, 0))) / (sum(CAST(ifNull(cost, 0) AS Float64))/100) AS roi\n" +
            "FROM (\n" +
            "    select * from ads.tencent_report_data_main_process_time where data_create_time = (SELECT data_create_time\n" +
            "        FROM ads.tencent_report_data_main_process_time\n" +
            "        GROUP BY data_create_time\n" +
            "        ORDER BY abs(toUnixTimestamp(data_create_time) - toUnixTimestamp(DATE_SUB(now(),INTERVAL 1 DAY)))  -- 计算时间差\n" +
            "        LIMIT 1))\n" +
            "GROUP BY app_name, project_group, os\n" +
            "having sum_cost > 10000\n" +
            "\n" +
            ") yesterday on     today.product = yesterday.product\n" +
            "                   and today.dsp = yesterday.dsp\n" +
            "                   and today.os = yesterday.os\n" +
            "                  WHERE today.sum_cost > yesterday.sum_cost * 0.7\n" +
            "  AND (today.roi > yesterday.roi * 1.2 OR today.roi < yesterday.roi * 0.9)")
    List<ProductRoiMinute> queryProductRoiMinute();


    @Select("select yes.product_name as product,yes.product_group as product_group,yes.dsp as dsp" +
            ",ycost as yestoday_cost,tcost as today_cost,(ycost-tcost)/ycost as rate,allcost as  yestoday_all_cost \n" +
            "from (\n" +
            "      select logday, hour, product_name,product_group, dsp, sum(cost) ycost\n" +
            "      from ads.realtime_advertiser_cost_sum\n" +
            "      where logday = yesterday()\n" +
            "      and hour = '${beforeHour}'\n" +
            "      group by logday, hour, product_name, dsp,product_group\n" +
            ")yes\n" +
            "left join (\n" +
            "                  select logday, hour, product_name,product_group, dsp, sum(cost) tcost\n" +
            "                  from ads.realtime_advertiser_cost_sum\n" +
            "                  where logday = today()\n" +
            "                  and hour = '${beforeHour}'\n" +
            "                  group by logday, hour, product_name, dsp,product_group\n" +
            ") today\n" +
            "on yes.product_name=today.product_name and yes.hour=today.hour and yes.dsp=today.dsp\n" +
            "left join(\n" +
            "                select logday, hour, product_name, dsp, sum(cost) allcost\n" +
            "                from ads.realtime_advertiser_cost_sum\n" +
            "                where logday = yesterday()\n" +
            "                and hour = 23\n" +
            "                group by logday, hour, product_name, dsp\n" +
            ") yestotal\n" +
            "on yes.product_name=yestotal.product_name and yes.dsp=yestotal.dsp\n" +
            "where allcost>5000 and rate>0.5;")
    List<ProductCostHour> queryProductCostHour(@Param("beforeHour") Integer beforeHour);


    //穿山甲ECPM波动分析
    @Select("SELECT e.product                                                        as product\n" +
            "     , m.product_name                                                   as product_name\n" +
            "     , e.os                                                             as os\n" +
            "     -- 曝光量\n" +
            "     , SUM(CASE\n" +
            "               WHEN t.ad_id is not null\n" +
            "                   THEN 1\n" +
            "               ELSE 0 END)                                              AS bidding_pv\n" +
            "     , SUM(CASE\n" +
            "               WHEN t.ad_id is null\n" +
            "                   THEN 1\n" +
            "               ELSE 0 END)                                              AS non_bidding_pv\n" +
            "     -- 总收入\n" +
            "     , round(SUM(CASE\n" +
            "                     WHEN t.ad_id is not null\n" +
            "                         THEN toFloat32OrZero(e.extend1)\n" +
            "                     ELSE 0 END) / 1000, 2)                             AS bidding_revenue\n" +
            "     , round(SUM(CASE\n" +
            "                     WHEN t.ad_id is null\n" +
            "                         THEN toFloat32OrZero(e.extend1)\n" +
            "                     ELSE 0 END) / 1000, 2)                             AS non_bidding_revenue\n" +
            "     -- eCPM\n" +
            "     , round(bidding_revenue / NULLIF(bidding_pv, 0) * 1000, 2)         AS bidding_ecpm\n" +
            "     , round(non_bidding_revenue / NULLIF(non_bidding_pv, 0) * 1000, 2) AS non_bidding_ecpm\n" +
            "     -- 最近10分钟\n" +
            "     , round((SUM(CASE\n" +
            "                      WHEN t.ad_id is not null\n" +
            "                          AND e.sendTime >= (toUInt64(now()) * 1000 - 10 * 60 * 1000)\n" +
            "                          THEN toFloat32OrZero(e.extend1)\n" +
            "                      ELSE 0 END)\n" +
            "    / 1000) / NULLIF(SUM(CASE\n" +
            "                             WHEN t.ad_id is not null\n" +
            "                                 AND e.sendTime >= (toUInt64(now()) * 1000 - 10 * 60 * 1000)\n" +
            "                                 THEN 1\n" +
            "                             ELSE 0 END), 0) * 1000, 2)                 AS bidding_ecpm_10min\n" +
            "     , round((SUM(\n" +
            "                      CASE\n" +
            "                          WHEN t.ad_id is null\n" +
            "                              AND e.sendTime >= (toUInt64(now()) * 1000 - 10 * 60 * 1000)\n" +
            "                              THEN toFloat32OrZero(e.extend1)\n" +
            "                          ELSE 0 END)\n" +
            "    / 1000) / NULLIF(SUM(CASE\n" +
            "                             WHEN t.ad_id is null\n" +
            "                                 AND e.sendTime >= (toUInt64(now()) * 1000 - 10 * 60 * 1000)\n" +
            "                                 THEN 1\n" +
            "                             ELSE 0 END), 0) * 1000, 2)                 AS non_bidding_ecpm_10min\n" +
            "FROM ods.event_exposure_dist e\n" +
            "         global\n" +
            "         INNER JOIN\n" +
            "     dwd.product_map_dist m\n" +
            "     ON\n" +
            "         e.product = m.product\n" +
            "         global\n" +
            "         left join\n" +
            "     (\n" +
            "         select ad_id\n" +
            "         from ods_mysql.tb_ap_bidding\n" +
            "         group by ad_id\n" +
            "         )\n" +
            "         t\n" +
            "     on\n" +
            "         toString(e.ad_id) = toString(t.ad_id)\n" +
            "WHERE e.logday = today()\n" +
            "  AND e.ad_action = 'exposure'\n" +
            "  AND e.event = 'AdData'\n" +
            "  AND e.product != ''\n" +
            "  AND userid is NOT NULL\n" +
            "  AND toFloat32OrZero(e.extend1) < 100000\n" +
            "  AND toFloat32OrZero(e.extend1) > 0\n" +
            "  AND toInt64OrZero(userid) global in (select user_id as userid\n" +
            "                                 from dwd.user_active_dist\n" +
            "                                 where toDate(create_time) = today())\n" +
            "  AND e.ad_type GLOBAL IN (SELECT toString(ad_type) AS ad_type\n" +
            "                           FROM dwd.ad_type_basic_dist\n" +
            "                           WHERE source_name = '穿山甲')\n" +
            "  AND (\n" +
            "    toString(e.ad_type) LIKE '%1008%' OR\n" +
            "    toString(e.ad_type) LIKE '%1015%' OR\n" +
            "    toString(e.ad_type) LIKE '%1018%' OR\n" +
            "    toString(e.ad_type) LIKE '%1099%' OR\n" +
            "    toString(e.ad_type) LIKE '%1094%' OR\n" +
            "    toString(e.ad_type) LIKE '%1104%' OR\n" +
            "    toString(e.ad_type) LIKE '%1093%' OR\n" +
            "    toString(e.ad_type) LIKE '%1095%' OR\n" +
            "    toString(e.ad_type) LIKE '%1083%' OR\n" +
            "    toString(e.ad_type) LIKE '%1200%'\n" +
            "    )\n" +
            "GROUP BY e.product\n" +
            "       , m.product_name\n" +
            "       , e.os\n" +
            "Having bidding_ecpm >= 100\n" +
            "   and bidding_ecpm >= non_bidding_ecpm * 2\n" +
            "   and bidding_pv >= 1000\n" +
            "   and non_bidding_pv >= 1000\n" +
            "ORDER BY bidding_ecpm DESC;")
    List<ProductEcpm> queryProductEcpm();
}
