package com.shinet.core.alert.coreservice.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.shinet.core.alert.safe.entity.IosCheckUrl;
import com.shinet.core.alert.safe.service.IosCheckUrlService;
import io.swagger.annotations.ApiModel;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.Date;

@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
/**
 * create table ios_check_record
 * (
 *     id           int auto_increment primary key,
 *     product       varchar(60)  not null,
 *     product_name  varchar(60)  null,
 *     url           varchar(256) null,
 *     app_store_url varchar(256) null,
 *
 *     up_status     int          null comment '上架状态0-已下架 1-已上架',
 *
 *     no            int          null comment '排名',
 *     score         double       null comment '评分',
 *
 *
 *     create_time  datetime     null,
 *     update_time  datetime     null
 * );
 */
@NoArgsConstructor
public class IosCheckRecord extends IosCheckUrl implements Serializable {

    private static final long serialVersionUID = 3L;

    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    private Integer no;

    private Double score;

    private Date createTime;

    private Date updateTime;


    public IosCheckRecord(IosCheckUrl p, IosCheckUrlService.Res r) {
        this.setProduct(p.getProduct());
        this.setProductName(p.getProductName());
        this.setUrl(p.getUrl());
        this.setAppStoreUrl(p.getAppStoreUrl());
        this.setUpStatus(p.getUpStatus());

        Date now = new Date();
        this.setCreateTime(now);
        this.setUpdateTime(now);

        if(r == null){
            return;
        }
        this.no = r.getNo();
        this.score = r.getScore();
    }
}
