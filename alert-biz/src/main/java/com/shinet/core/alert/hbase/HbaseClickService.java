package com.shinet.core.alert.hbase;

import com.alibaba.fastjson.JSONObject;
import com.shinet.core.alert.clickhouse.entity.ToutiaoClick;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.codec.digest.DigestUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.hadoop.hbase.Cell;
import org.apache.hadoop.hbase.TableName;
import org.apache.hadoop.hbase.client.*;
import org.apache.hadoop.hbase.util.Bytes;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

@Component
@Slf4j
public class HbaseClickService {

    static String toutiaoClickLong = "ToutiaoClickLong";
    @Resource
    private Connection toutiaoClickConnection;
    public void saveHbaseClick(List<ToutiaoClick> clickList) {
        saveHbaseClick(clickList,toutiaoClickLong,toutiaoClickConnection);
    }

    public void saveHbaseClick(List<ToutiaoClick> clickList, String tableName, Connection hbaseDConnection) {
        try (Table table = hbaseDConnection.getTable(TableName.valueOf(tableName))) {
            List<Put> putList = new ArrayList<>();
            for(ToutiaoClick click : clickList){
                String product = click.getProduct();
                String deviceId = click.getOcpcDeviceId();
                String oaId = click.getOaid();
                String mac= click.getMac();

                String os = click.getOs();
                String oaid2 = click.getOaid2();
                if (StringUtils.isNotBlank(deviceId) && !DigestUtils.md5Hex("0").equals(deviceId)) {
                    String key1 = RedisKeyConstants.getClickDeviceKey(product, os, deviceId);
                    Put put = new Put(Bytes.toBytes(key1));
                    put.addColumn(Bytes.toBytes("family"), Bytes.toBytes("qualifier"), Bytes.toBytes(JSONObject.toJSONString(click)));
                    putList.add(put);
                }
                if (StringUtils.isNotBlank(oaId)) {

                    String key1 = RedisKeyConstants.getClickOaIdKey(product, os, oaId);
                    Put put = new Put(Bytes.toBytes(key1));
                    put.addColumn(Bytes.toBytes("family"), Bytes.toBytes("qualifier"), Bytes.toBytes(JSONObject.toJSONString(click)));
                    putList.add(put);
                }
                if (StringUtils.isNotBlank(oaid2)) {

                    String key1 = RedisKeyConstants.getClickOaId2Key(product, os, oaid2);
                    Put put = new Put(Bytes.toBytes(key1));
                    put.addColumn(Bytes.toBytes("family"), Bytes.toBytes("qualifier"), Bytes.toBytes(JSONObject.toJSONString(click)));
                    putList.add(put);
                }
                if (StringUtils.isNotBlank(mac)) {
                    String key1 = RedisKeyConstants.getClickMacKey(product, os, mac);
                    Put put = new Put(Bytes.toBytes(key1));
                    put.addColumn(Bytes.toBytes("family"), Bytes.toBytes("qualifier"), Bytes.toBytes(JSONObject.toJSONString(click)));
                    putList.add(put);
                }
                if (StringUtils.isNotBlank(click.getAndroidId())) {
                    String key1 = RedisKeyConstants.getClickAndroidIdKey(product, os, click.getAndroidId());
                    Put put = new Put(Bytes.toBytes(key1));
                    put.addColumn(Bytes.toBytes("family"), Bytes.toBytes("qualifier"), Bytes.toBytes(JSONObject.toJSONString(click)));
                    putList.add(put);
                }
            }
            table.put(putList);
        } catch (Exception e) {
            log.error("hbase add error ", e);
        }
    }

    public void saveKuaishouSatrData2Hbase(String key, List<List<String>> dataList) {
        try (Table table = toutiaoClickConnection.getTable(TableName.valueOf(toutiaoClickLong))) {
            Put put = new Put(Bytes.toBytes(key));
            put.addColumn(Bytes.toBytes("family"), Bytes.toBytes("qualifier"), Bytes.toBytes(JSONObject.toJSONString(dataList)));
            table.put(put);
        } catch (Exception e) {
            log.error("hbase add error ", e);
        }
    }


    public String getKuaishouSatrData(String key) {
        try (Table table = toutiaoClickConnection.getTable(TableName.valueOf(toutiaoClickLong))) {

            Get get = new Get(Bytes.toBytes(key));
            Result res = table.get(get);

            List<Cell> cells = res.listCells();
            if (cells != null && cells.size() > 0) {
                byte[] val = res.getValue(Bytes.toBytes("family"), Bytes.toBytes("qualifier"));
                return Bytes.toString(val);
            }
            return null;
        } catch (Exception e) {
            log.error("hbase add error ", e);
        }
        return null;
    }
}
