package com.shinet.core.alert.safe.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.Date;

/**
 * <p>
 * 锁区IOS特殊策略
 * </p>
 *
 * <AUTHOR>
 * @since 2024-03-18
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value="AndroidLockConf对象", description="锁区IOS特殊策略")
public class AndroidLockConf implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    private Integer appId;

    private String product;

    private String productName;

    @ApiModelProperty(value = "商店名称")
    private String storeName;

    private String lockPkgs;

    private String lockVersions;

    @ApiModelProperty(value = "1为生效 0为删除")
    private Integer isEnable;
    @ApiModelProperty(value = "1 全锁   0只锁非ocpc  默认1全锁 ")
    private Integer allLock;

    private String lockCitys;

    private String remark;

    private Date createTime;

    private Date updateTime;


}
