package com.shinet.core.alert.safe.service;


import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.extension.service.additional.query.impl.LambdaQueryChainWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.shinet.core.alert.coreservice.entity.AlertRecord;
import com.shinet.core.alert.coreservice.enums.*;
import com.shinet.core.alert.coreservice.service.AlertDelayJobService;
import com.shinet.core.alert.coreservice.service.AlertRecordService;
import com.shinet.core.alert.dingtalk.DingTailService;
import com.shinet.core.alert.dsp.service.ProductService;
import com.shinet.core.alert.safe.entity.*;
import com.shinet.core.alert.safe.mapper.AndroidLockRstMapper;
import com.xxl.job.core.log.XxlJobLogger;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import java.util.*;
import java.util.stream.Collectors;


/**
 * <p>
 *  服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-03-15
 */
@Service
@Slf4j
public class AndroidLockRstService extends ServiceImpl<AndroidLockRstMapper, AndroidLockRst> {

    @Autowired
    AlertRecordService alertRecordService;
    @Autowired
    AlertDelayJobService alertDelayJobService;
    @Autowired
    private ProductService productService;
    @Autowired
    private AndroidLockConfService androidLockConfService;

    private static Set<String> phones = new HashSet<>();
    static {
        phones.add("15386932967");
    }

    private static Map<String,AndoridFirstLock> cache = new HashMap<>();

    public void getFcLockNums(String jobName,Integer maxNum){
        List<AndoridFirstLock> allLockNums = baseMapper.getDlockNum();

        XxlJobLogger.log("锁区数据查询完成 "+JSON.toJSONString(allLockNums));

        String altMsg = "";
        List<String> productList = new ArrayList<>();
        for(AndoridFirstLock andoridFirstLock : allLockNums){
            AndoridFirstLock temp = null;
            if(
                andoridFirstLock.getLockIpNum() != null
                && andoridFirstLock.getLockIpNum() > maxNum
            ){
                String key = andoridFirstLock.getProduct() + "-" + andoridFirstLock.getAppVersion() + "-" + andoridFirstLock.getChannel();
                if(cache.containsKey(key)){
                    temp = cache.get(key);
                    if(checkAlert(temp, andoridFirstLock)){
                        andoridFirstLock.setAlertCount(temp.getAlertCount() + 1);
                        cache.put(key, andoridFirstLock);
                        altMsg = altMsg+" "+productService.getProductRemarkByName(andoridFirstLock.getProduct()) + ":" + andoridFirstLock.getProduct()+"-"+andoridFirstLock.getAppVersion()+"-"+andoridFirstLock.getChannel()+":"+andoridFirstLock.getLockIpNum()+" \n\n";
                        productList.add(andoridFirstLock.getProduct());
                    }
                }else{
                    andoridFirstLock.setAlertCount(1);
                    cache.put(key, andoridFirstLock);
                    altMsg = altMsg+" "+productService.getProductRemarkByName(andoridFirstLock.getProduct()) + ":" + andoridFirstLock.getProduct()+"-"+andoridFirstLock.getAppVersion()+"-"+andoridFirstLock.getChannel()+":"+andoridFirstLock.getLockIpNum()+" \n\n";
                    productList.add(andoridFirstLock.getProduct());
                }
            }
        }
        if(StringUtils.isNotBlank(altMsg)){
            altMsg = "全锁首层用户超 \n\n"+altMsg;
            AlertRecord alertRecord = alertRecordService.insertAlertRecord(jobName,jobName, AlertModel.androidlock, altMsg, AlertStatus.INIT, phones, AlertType.DINGDING);
            DingTailService.sendMarkdownMsgDelay(alertRecord, AlertRoute.JISHU.value, AlertJobDelayModel.ANDROID_LOCK_ALERT,productList);
        }
    }

    private boolean checkAlert(AndoridFirstLock model, AndoridFirstLock andoridFirstLock) {
        if(model.getAlertCount() < 3){
            return true;
        }

        if(andoridFirstLock.getLockIpNum() > 300){
            if(andoridFirstLock.getLockIpNum() - model.getLockIpNum() >= 30){
                return true;
            }
        }
        return false;
    }


    /**
     * android单产品锁区预警
     * @param jobName
     * @param maxNum
     */
    public void getProductLockNums(String jobName, Integer maxNum, Set<String> pset){
        List<AndoridProductLock> andoridProductLockList = baseMapper.queryProductIpLockNum();

        XxlJobLogger.log("锁区数据查询完成 "+JSON.toJSONString(andoridProductLockList));

        String altMsg = "";
        List<String> productList = new ArrayList<>();
        for(AndoridProductLock andoridProductLock : andoridProductLockList){
            if(andoridProductLock.getLockIpNum()>maxNum && !pset.contains(andoridProductLock.getProduct())){
                altMsg = altMsg+" "+andoridProductLock.getProduct()+"-"+andoridProductLock.getStoreName()+":"+andoridProductLock.getLockOaNum()+","+andoridProductLock.getLockIpNum()+" \n\n";
                productList.add(andoridProductLock.getProduct());
            }
        }
        if(StringUtils.isNotBlank(altMsg)){
            altMsg = "单个产品锁过多 \n\n"+altMsg;
            AlertRecord alertRecord = alertRecordService.insertAlertRecord(jobName,jobName, AlertModel.androidlock, altMsg, AlertStatus.INIT, DingTailService.dset, AlertType.DINGDING);
            DingTailService.sendMarkdownMsgDelay(alertRecord, AlertRoute.JISHU.value, AlertJobDelayModel.ANDROID_LOCK_ALERT,productList);
        }
    }

    public List<AndroidLockRst> getAllLockNums( String jobName, int maxAllLock) {
        List<AndroidLockRst> allLockNums = baseMapper.getAllLockNum(maxAllLock);
        log.info("全锁ocpc用户限制 " + JSON.toJSONString(allLockNums));
        allLockNums = allLockNums.stream().filter(r -> alertDelayJobService.canSend(AlertJobDelayModel.ANDROID_LOCK_ALERT.getJobId()+"_"+r.getProduct())).collect(Collectors.toList());
        List<String> productList = new ArrayList<>();

        List<String> productListSy = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(allLockNums)) {
            String altMsg = "";

            String altMsgSyqun = "";
            Set<String> phoneListToShangye = DingTailService.lockAlarmData;
            for(AndroidLockRst androidLockRst : allLockNums){

                String product =  androidLockRst.getProduct();

                LambdaQueryChainWrapper<AndroidLockRst> lb = lambdaQuery();
                lb.eq(AndroidLockRst::getProduct,product).eq(AndroidLockRst::getIsOcpc,1).eq(AndroidLockRst::getLockFlag,"true");
                if(StringUtils.isNotBlank(androidLockRst.getChannel())){
                    lb.eq(AndroidLockRst::getChannel,androidLockRst.getChannel());
                }
                if(StringUtils.isNotBlank(androidLockRst.getAppVersion())){
                    lb.eq(AndroidLockRst::getAppVersion,androidLockRst.getAppVersion());
                }
                long countNum = lb.count();

                if(StringUtils.contains(androidLockRst.getChannel(),"vivo") ||
                       StringUtils.contains(androidLockRst.getChannel(),"oppo") ||
                      StringUtils.contains(androidLockRst.getChannel(),"mi") ||
                     StringUtils.contains(androidLockRst.getChannel(),"huawei") ||
                     StringUtils.contains(androidLockRst.getChannel(),"honor")
                ){
                    altMsg = altMsg + "("+ productService.getProductRemarkByName(androidLockRst.getProduct()) + ":" + androidLockRst.getProduct() +"->"+androidLockRst.getChannel()+" "+androidLockRst.getAppVersion()+" "+androidLockRst.getId()+" ) \n\n";
                    productList.add(androidLockRst.getProduct());
                }else{
                    altMsgSyqun = altMsgSyqun + "("+ productService.getProductRemarkByName(androidLockRst.getProduct()) + ":" + androidLockRst.getProduct() +"->"+androidLockRst.getChannel()+" "+androidLockRst.getAppVersion()+" "+androidLockRst.getId()+") \n\n";
                    productListSy.add(androidLockRst.getProduct());
                    String productGroup = productService.getTeamByCnName(productService.getProductRemarkByName(androidLockRst.getProduct()));
                    if ("项目一组".equals(productGroup)) {
                        phoneListToShangye.add("18600655825");
                    } else if ("项目五组".equals(productGroup)) {
                        phoneListToShangye.add("17611265922");
                    } else if ("项目七组".equals(productGroup)) {
                        phoneListToShangye.add("18810288450");
                    } else if ("项目十组".equals(productGroup)) {
                        phoneListToShangye.add("17306396520");
                    }
                }
            }

            if(StringUtils.isNotBlank(altMsg)){
                altMsg = "全锁ocpc用户超 " + maxAllLock + "  \n\n"+altMsg;
                AlertRecord alertRecord = alertRecordService.insertAlertRecord(jobName,jobName, AlertModel.androidlock, altMsg, AlertStatus.INIT, DingTailService.lockAlarmStoreDataForJSX, AlertType.DINGDING);
                DingTailService.sendMarkdownMsgDelay(alertRecord, AlertRoute.JISHU.value, AlertJobDelayModel.ANDROID_LOCK_ALERT,productList);
            }


            if (StringUtils.isNotBlank(altMsgSyqun)) {
                altMsgSyqun = "Alert: 全锁ocpc用户超 " + maxAllLock + "  \n\n" + altMsgSyqun;
                List<AlertRecord> alertRecordHistory = alertRecordService.queryAlertRecord(jobName, altMsgSyqun);
                if (CollectionUtils.isEmpty(alertRecordHistory) || (CollectionUtils.isNotEmpty(alertRecordHistory) && alertRecordHistory.get(0) == null)) {
                    AlertRecord alertRecord = alertRecordService.insertAlertRecord(jobName, jobName, AlertModel.androidlock, altMsgSyqun, AlertStatus.INIT, phoneListToShangye, AlertType.DINGDING);
                    DingTailService.sendMarkdownMsgDelay(alertRecord, AlertRoute.SUOQU.value, AlertJobDelayModel.ANDROID_LOCK_ALERT, productListSy);
                }
            }
        }
        return allLockNums;
    }


    public void closeAllLockNums(String jobName, int maxAllLock) {
        List<AndroidLockRst> allLockNums = baseMapper.getAllLockNum(maxAllLock);

        List<String> productListSy = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(allLockNums)) {
            String altMsgSyqun = "";
            Set<String> phoneListToShangye = DingTailService.lockAlarmData;
            for (AndroidLockRst androidLockRst : allLockNums) {
                if (StringUtils.contains(androidLockRst.getChannel(), "vivo") ||
                        StringUtils.contains(androidLockRst.getChannel(), "oppo") ||
                        StringUtils.contains(androidLockRst.getChannel(), "mi") ||
                        StringUtils.contains(androidLockRst.getChannel(), "huawei") ||
                        StringUtils.contains(androidLockRst.getChannel(), "honor")
                ) {
                    continue;
                } else {
                    altMsgSyqun = altMsgSyqun + "(" + productService.getProductRemarkByName(androidLockRst.getProduct()) + ":" + androidLockRst.getProduct() + "->" + androidLockRst.getChannel() + " " + androidLockRst.getAppVersion() + " " + androidLockRst.getId() + ") \n\n";
                    productListSy.add(androidLockRst.getProduct());
                    String productGroup = productService.getTeamByCnName(productService.getProductRemarkByName(androidLockRst.getProduct()));
                    if ("项目一组".equals(productGroup)) {
                        phoneListToShangye.add("18600655825");
                    } else if ("项目五组".equals(productGroup)) {
                        phoneListToShangye.add("17611265922");
                    } else if ("项目七组".equals(productGroup)) {
                        phoneListToShangye.add("18810288450");
                    } else if ("项目十组".equals(productGroup)) {
                        phoneListToShangye.add("17306396520");
                    }
                    String allLockPkgs = androidLockConfService.getAllLockPkgs(androidLockRst.getProduct(), androidLockRst.getChannel(), androidLockRst.getAppVersion());
                    String appVersion = androidLockRst.getAppVersion();
                    if (StringUtils.isBlank(allLockPkgs)) {
                        allLockPkgs = androidLockConfService.getAllLockPkgs(androidLockRst.getProduct(), androidLockRst.getChannel(), "");
                        if (StringUtils.isBlank(allLockPkgs)) {
                            continue;
                        }else {
                            appVersion = "";
                        }
                    }
                    List<String> allLockPkgsList = new ArrayList<>(Arrays.asList(allLockPkgs.split(",")));
                    allLockPkgsList.remove(androidLockRst.getChannel());
                    String closeLockMsg = "";
                    if (allLockPkgsList.size() > 0) {
                        StringBuilder newAllLockPkgs = new StringBuilder();
                        allLockPkgsList.forEach(pkg -> {
                            newAllLockPkgs.append(pkg + ",");
                        });
                        newAllLockPkgs.deleteCharAt(newAllLockPkgs.length() - 1);
                        if (StringUtils.isNotBlank(newAllLockPkgs.toString())) {
                            androidLockConfService.disableAllLockByChannel(androidLockRst.getProduct(), androidLockRst.getChannel(), newAllLockPkgs.toString(), appVersion);
                            closeLockMsg = " 本次已解全锁 — " + androidLockRst.getChannel() + " — " + appVersion;
                            altMsgSyqun = altMsgSyqun + closeLockMsg;
                        }
                    } else {
                        androidLockConfService.disableAllLock(androidLockRst.getProduct(), androidLockRst.getChannel(), appVersion);
                        closeLockMsg = " 本次已解全锁(单条) " + androidLockRst.getChannel() + " — " + appVersion;
                        altMsgSyqun = altMsgSyqun + closeLockMsg;
                    }
                    log.info("非厂商渠道锁量14自动解全锁:" + closeLockMsg);
                }
            }


            if (StringUtils.isNotBlank(altMsgSyqun)) {
                altMsgSyqun = "Alert: 全锁ocpc用户超 " + maxAllLock + "  \n\n" + altMsgSyqun;
                List<AlertRecord> alertRecordHistory = alertRecordService.queryAlertRecord(jobName, altMsgSyqun);
                if (CollectionUtils.isEmpty(alertRecordHistory) || (CollectionUtils.isNotEmpty(alertRecordHistory) && alertRecordHistory.get(0) == null)) {
                    alertRecordService.insertAlertRecord(jobName, jobName, AlertModel.androidlock, altMsgSyqun, AlertStatus.INIT, phoneListToShangye, AlertType.DINGDING);
                }
            }
        }
    }
}
