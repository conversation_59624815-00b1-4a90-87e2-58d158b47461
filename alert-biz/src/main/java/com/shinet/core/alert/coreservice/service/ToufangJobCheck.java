package com.shinet.core.alert.coreservice.service;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.google.common.util.concurrent.SimpleTimeLimiter;
import com.shinet.core.alert.common.HttpClientService;
import com.shinet.core.alert.coreservice.entity.AlertRecord;
import com.shinet.core.alert.coreservice.enums.AlertModel;
import com.shinet.core.alert.coreservice.enums.AlertStatus;
import com.shinet.core.alert.coreservice.enums.AlertType;
import com.shinet.core.alert.dingtalk.DingTailService;
import com.shinet.core.alert.dingtalk.VedioAlertService;
import com.xxl.job.core.log.XxlJobLogger;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.joda.time.DateTimeConstants;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.concurrent.ArrayBlockingQueue;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicReference;
import java.util.function.Function;

@Slf4j
@Service
public class ToufangJobCheck {

    SimpleTimeLimiter simpleTimeLimiter = SimpleTimeLimiter.create(new ThreadPoolExecutor(
            1,
            1,
            DateTimeConstants.SECONDS_PER_MINUTE * 10,
            TimeUnit.SECONDS,
            new ArrayBlockingQueue(100)
    ));
    int timeOutSeconds = 5;
    @Autowired
    HttpClientService httpClientService;
    @Autowired
    AlertRecordService alertRecordService;

    // health  *************  ************* 8080
    //toufangjob-*************:8080,*************:8080;
    public void checkToufangJob(String jobName, String ipsStr,String chenNameSplitStr, Function<com.alibaba.fastjson.JSONObject, Boolean> resultJudgeFun) {
        if(StringUtils.isBlank(chenNameSplitStr)){
            chenNameSplitStr = "-";
        }
        List<String> checkNameIps = Arrays.asList(ipsStr.split(";"));
        long dtime = System.currentTimeMillis();
        AtomicReference<String> alertMsg = new AtomicReference<>("");
        String finalChenNameSplitStr = chenNameSplitStr;
        Date date = new Date();
        checkNameIps.forEach(nameIps -> {
            String checkName = nameIps.split(finalChenNameSplitStr)[0];
            if(StringUtils.contains(jobName,"toufang") && (date.getHours()<9 || date.getHours()==23)){
                XxlJobLogger.log("toufang job 凌晨不做check");
            }else{
                List<String> checkIps = Arrays.asList(nameIps.split(finalChenNameSplitStr)[1].split(","));
                for (String ipStr : checkIps) {
                    try {
                        String alertMsgD = sendMsg(ipStr,checkName,resultJudgeFun,5);
                        if(StringUtils.isNotBlank(alertMsgD)){
                            alertMsg.set(alertMsg + "" +alertMsgD);
                        }
                    } catch (Exception e) {
                        if (!alertMsg.get().contains(checkName)) {
                            alertMsg.set(alertMsg + "" + checkName + " ip:" + ipStr);
                        } else {
                            alertMsg.set(alertMsg + " ip:" + ipStr);
                        }

                        XxlJobLogger.log(e);
                    }
                }
            }
        });

        if (StringUtils.isNotBlank(alertMsg.get())) {
            XxlJobLogger.log("业务异常 " + alertMsg.get());
            VedioAlertService.sendVocMsg("投放JOB ", alertMsg.get());
            AlertRecord alertRecord = alertRecordService.insertAlertRecord(jobName, "投放JOB get请求", AlertModel.SERVICEREQ, alertMsg.get(), 0d, 0d, 0, 0, AlertStatus.INIT, DingTailService.dset, AlertType.DINGPHONE);
            DingTailService.sendMarkdownMsg(alertRecord);
        }
    }

    private String sendMsg(String ipStr, String checkName, Function<com.alibaba.fastjson.JSONObject, Boolean> resultJudgeFun, int retryNum) {
        AtomicReference<String> alertMsg = new AtomicReference<String>();
        for (int i = 0; i < retryNum; i++) {
            try {
                TimeUnit.SECONDS.sleep(5);
                alertMsg = new AtomicReference<String>();
                String reqAuthUrl = "http://" + ipStr + "/health";
                if(ipStr.contains("health")){
                    reqAuthUrl = ipStr;
                }
                String rspStr = null;
                try {
                    rspStr = httpClientService.sendReq(reqAuthUrl, null);
                } catch (Exception e) {
                    log.error("", e);
                    alertMsg.set(alertMsg + "" + checkName + " ip:" + ipStr+e.getMessage());
                }
                XxlJobLogger.log(reqAuthUrl+" rspStr  " + rspStr);
                if (StringUtils.isNotBlank(rspStr) && rspStr.equalsIgnoreCase("-1")) {
                    if (alertMsg.get() != null && !alertMsg.get().contains(checkName)) {
                        alertMsg.set(alertMsg + "" + checkName + " ip:" + ipStr);
                    } else {
                        alertMsg.set(alertMsg + " ip:" + ipStr);
                    }
                } else {
                    JSONObject jsonObject = JSON.parseObject(rspStr);
                    log.info("rspStr  " + rspStr);
                    if (resultJudgeFun != null && resultJudgeFun.apply(jsonObject)) {
                        if (!alertMsg.get().contains(checkName)) {
                            alertMsg.set(alertMsg + "" + checkName + " ip:" + ipStr);
                        } else {
                            alertMsg.set(alertMsg + " ip:" + ipStr);
                        }
                    }else{
                        return "";
                    }
                }
            } catch (Exception e) {
                log.error(ipStr, e);
                alertMsg.set(alertMsg + "" + checkName + " ip:" + ipStr+e.getMessage());
            }
        }
        return alertMsg.get();
    }
}
