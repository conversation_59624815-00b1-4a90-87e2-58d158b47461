package com.shinet.core.alert.bpcommon.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import java.io.Serializable;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR>
 * @since 2021-12-06
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value="WechatApp对象", description="")
public class WechatApp implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    private Long appId;

    private String wechatAppId;

    private String wechatAppSecret;

    private String remark;

    @ApiModelProperty(value = "一经设定，不可更改\n用以区分不同产品马甲包之间的 用户ID\n当 step=0 时，当前马甲包与step=0 的包 数据共享 否则数据分离")
    private Integer step;

    private Long createTime;

    private Long updateTime;


}
