package com.shinet.core.alert.clickhouse.service;

import cn.hutool.core.thread.RejectPolicy;
import cn.hutool.core.thread.ThreadUtil;
import com.google.common.util.concurrent.RateLimiter;
import com.shinet.core.alert.clickhouse.entity.ToutiaoClick;
import com.shinet.core.alert.clickhouse.mapper.ck12.ToutiaoClickMapper;
import com.shinet.core.alert.hbase.HbaseClickService;
import com.shinet.core.alert.util.DateUtils;
import com.xxl.job.core.log.XxlJobLogger;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.FileUtils;
import org.apache.commons.lang3.StringUtils;
import org.joda.time.DateTimeConstants;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.io.*;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.concurrent.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.concurrent.atomic.AtomicLong;
import java.util.concurrent.atomic.AtomicReferenceArray;

@Component
@Slf4j
public class ToutiaoClickService {
    @Autowired
    ToutiaoClickMapper toutiaoClickMapper;
    @Autowired
    HbaseClickService hbaseClickService;
    RateLimiter raterLimiter = RateLimiter.create(2);

    public void moveClickToHbase(long offset, int limit, String logday) {
        try {
            raterLimiter.acquire();
            List<ToutiaoClick> dlist = toutiaoClickMapper.queryClickByOffset(offset, limit, logday);
            if (dlist.size() > 0) {
                hbaseClickService.saveHbaseClick(dlist);
            }
        } catch (Exception e) {
            log.error("", e);
        }
    }

    ThreadFactory threadFactory = ThreadUtil.newNamedThreadFactory("threadMoveClick", null, false);
    ExecutorService executorService = new ThreadPoolExecutor(
            4
            , 4,
            20,
            TimeUnit.SECONDS,
            new ArrayBlockingQueue<>(20),
            threadFactory,
            RejectPolicy.CALLER_RUNS.getValue()
    );
    int pageNum = 10000;

    public void threadMove(String startDateStr) {
        Date date = DateUtils.parse(startDateStr, DateUtils.PATTERN_YMD);
        Date endDate = DateUtils.parse("2021-11-18", DateUtils.PATTERN_YMD);
        long day = (endDate.getTime() - date.getTime()) / DateTimeConstants.MILLIS_PER_DAY;

        List<CompletableFuture> completableFutureList = new ArrayList<>();
        for (int i = 0; i < day; i++) {
            Date logdate = new Date(date.getTime() + i * DateTimeConstants.MILLIS_PER_DAY);
            String logdateStr = DateUtils.formatDateForYMD(logdate);
            long cnum = toutiaoClickMapper.countLogdayClick(logdateStr);
            log.info("开始处理 " + logdateStr + "数据 共" + cnum + "条");
            long ctime = System.currentTimeMillis();

            AtomicLong anum = new AtomicLong(cnum);

            for (int j = 0; j <= cnum / pageNum; j++) {

                int finalJ = j;
                CompletableFuture completableFuture = CompletableFuture.runAsync(() -> {
                    try {
                        long dtime = System.currentTimeMillis();
                        long offset = finalJ * pageNum;
                        moveClickToHbase(offset, pageNum, logdateStr);

                        anum.addAndGet(-pageNum);
                        String logstr = logdateStr + "数据迁移完成 剩余" + (anum.get()) + " 耗时" + (System.currentTimeMillis() - dtime);
                        XxlJobLogger.log(logstr);
                        log.info(logstr);
                    } catch (Exception e) {
                        log.error("", e);
                    }
                }, executorService);

                completableFutureList.add(completableFuture);

                ctime = System.currentTimeMillis();
            }
        }
        CompletableFuture<Void> combindFuture = CompletableFuture.allOf(completableFutureList.toArray(new CompletableFuture[completableFutureList.size()]));
        try {
            combindFuture.get();
        } catch (Exception e) {
            XxlJobLogger.log("", e);
        }
    }

    public static void main(String[] args){
        String cline = "2021-11-09,0,toutiao,jnxfsh,ios,1714940444410952,42AF88E7-7D80-49E0-A260-D34A5537307E,1636465511000,\\N,http://ad.toutiao.com/track/activate/?callback=CMvIoYWk_YUDEI25ivCk_YUDGI3f0PfXAiCP5JWPswEoi9mX9Lz6hQMwDDjBuAJCIjIwMjExMTA5MjE0MzQzMDEwMTUwMTc2MDM1M0MzODlFMkNIwbgCUACIAQKQAQKYAQA=&os=1&muid=42AF88E7-7D80-49E0-A260-D34A5537307E,1715144958844045,1715049351297251,1715144734827595,,\\N,0,\\N,0,2021-11-09 21:45:42,2021-11-09 21:45:42,\\N,\\N,0,";
        String[] clickStrs = cline.split(",");

        for(int m=0;m<clickStrs.length;m++){
            if(StringUtils.isNotBlank(clickStrs[m]) && StringUtils.equalsIgnoreCase("\\N",clickStrs[m])){
                clickStrs[m] =null;
            }
        }
        log.info(clickStrs.length+"");
        if(clickStrs.length<24){
            log.info(cline);
        }
        String logday = clickStrs[0];
        String id = clickStrs[1];
        String dsp = clickStrs[2];
        String product = clickStrs[3];
        String os = clickStrs[4];
        String accountId = clickStrs[5];
        String ocpcDeviceId = clickStrs[6];
        String ts = clickStrs[7];
        String accountName = clickStrs[8];
        String callBack = clickStrs[9];
        String cid = clickStrs[10];
        String gid = clickStrs[11];
        String pid = clickStrs[12];
        String oaid = clickStrs[13];
        String aidname = clickStrs[14];
        String groupName = clickStrs[15];
        String cidName = clickStrs[16];
        String acctiveCount = clickStrs[17];
        String createTime = clickStrs[18];
        String updateTime = clickStrs[19];
        String mac = clickStrs[20];
        String pkgChannel = clickStrs[21];
        String unionSite = clickStrs[22];
        if(clickStrs.length>=23){
            unionSite = clickStrs[22];
        }
        String androidId = null;
        if(clickStrs.length>=24){
            androidId = clickStrs[23];
        }

        log.info(androidId);

    }
    public void moveByFile(String filePath) {
        try {
            BufferedReader br = new BufferedReader(new FileReader(filePath));
            String contentLine;
            ArrayList<ToutiaoClick> dlist = new ArrayList();
            Integer saveNum = new Integer(0);
            long ctime = System.currentTimeMillis();
            String fileName = new File(filePath).getName();
            while ((contentLine = br.readLine()) != null) {
                try {
                    contentLine = contentLine.replaceAll("\"","");
                    String[] clickStrs = contentLine.split(",");
                    for(int m=0;m<clickStrs.length;m++){
                        if(StringUtils.isNotBlank(clickStrs[m]) && StringUtils.equalsIgnoreCase("\\N",clickStrs[m])){
                            clickStrs[m] =null;
                        }
                    }
                    String logday = clickStrs[0];
                    String id = clickStrs[1];
                    String dsp = clickStrs[2];
                    String product = clickStrs[3];
                    String os = clickStrs[4];
                    String accountId = clickStrs[5];
                    String ocpcDeviceId = clickStrs[6];
                    String ts = clickStrs[7];
                    String accountName = clickStrs[8];
                    String callBack = clickStrs[9];
                    String cid = clickStrs[10];
                    String gid = clickStrs[11];
                    String pid = clickStrs[12];
                    String oaid = clickStrs[13];
                    String aidname = clickStrs[14];
                    String groupName = clickStrs[15];
                    String cidName = clickStrs[16];
                    String acctiveCount = clickStrs[17];
                    String createTime = clickStrs[18];
                    String updateTime = clickStrs[19];
                    String mac = clickStrs[20];
                    String pkgChannel = clickStrs[21];
                    String unionSite = null;
                    if(clickStrs.length>=23){
                        unionSite = clickStrs[22];
                    }
                    String androidId = null;
                    if(clickStrs.length>=24){
                        androidId = clickStrs[23];
                    }
                    ToutiaoClick toutiaoClick = new ToutiaoClick();
                    toutiaoClick.setAccountId(accountId);
                    toutiaoClick.setAccountName(accountName);
                    toutiaoClick.setAndroidId(androidId) ;
                    toutiaoClick.setCallbackUrl(callBack);
                    toutiaoClick.setCid(cid);
                    toutiaoClick.setCidName(cidName);
                    toutiaoClick.setCreateTime(DateUtils.parse(createTime,DateUtils.PATTERN_YHMS));
                    toutiaoClick.setDsp(dsp);
                    toutiaoClick.setGid(gid);
                    toutiaoClick.setGroupName(groupName);
                    toutiaoClick.setMac(mac);
                    toutiaoClick.setOaid(oaid);
                    toutiaoClick.setOcpcDeviceId(ocpcDeviceId);
                    toutiaoClick.setOs(os);
                    toutiaoClick.setPid(pid);
                    toutiaoClick.setPkgChannel(pkgChannel);
                    toutiaoClick.setProduct(product);
                    toutiaoClick.setTs(ts);
                    toutiaoClick.setUpdateTime(DateUtils.parse(updateTime,DateUtils.PATTERN_YHMS));

                    dlist.add(toutiaoClick);
                }catch (Exception e){
                    log.error(contentLine,e);
                }

                if(dlist.size()>=20000){
                    hbaseClickService.saveHbaseClick(dlist);
                    saveNum = saveNum+dlist.size();
                    XxlJobLogger.log("共保存 "+ saveNum +"条数据 耗时"+(System.currentTimeMillis()- ctime)+"@"+fileName);
                    log.info("共保存 "+ saveNum +"条数据 耗时"+(System.currentTimeMillis()- ctime)+"@"+fileName);
                    ctime = System.currentTimeMillis();
                    dlist = new ArrayList<>();
                }
            }

            hbaseClickService.saveHbaseClick(dlist);
            saveNum = saveNum+dlist.size();
            XxlJobLogger.log("数据迁移完成 共保存 "+ saveNum +"条数据 耗时"+(System.currentTimeMillis()- ctime)+ ""+filePath);
            log.info("数据迁移完成 共保存 "+ saveNum +"条数据 耗时"+(System.currentTimeMillis()- ctime)+ ""+filePath);

        } catch (Exception e) {
            log.error("",e);
        }
    }
}
