package com.shinet.core.alert.coreservice;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.google.common.util.concurrent.SimpleTimeLimiter;
import com.shinet.core.alert.common.HttpClientService;
import com.shinet.core.alert.coreservice.dto.CommonHeaderDTO;
import com.shinet.core.alert.coreservice.entity.AlertRecord;
import com.shinet.core.alert.coreservice.enums.AlertModel;
import com.shinet.core.alert.coreservice.enums.AlertStatus;
import com.shinet.core.alert.coreservice.enums.AlertType;
import com.shinet.core.alert.coreservice.service.AlertRecordService;
import com.shinet.core.alert.dingtalk.DingTailService;
import com.xxl.job.core.log.XxlJobLogger;
import lombok.extern.slf4j.Slf4j;
import org.joda.time.DateTimeConstants;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.concurrent.ArrayBlockingQueue;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;

@Component
@Slf4j
public class UserServiceCheck {
    @Autowired
    HttpClientService httpClientService;

    // https://bp-api.shinet.cn/common/user/checkAuth
    private String authUrl = "http://bp-api.shinet.cn/bp/user/checkAuth";

    SimpleTimeLimiter simpleTimeLimiter = SimpleTimeLimiter.create(new ThreadPoolExecutor(
            1,
            1,
            DateTimeConstants.SECONDS_PER_MINUTE * 10,
            TimeUnit.SECONDS,
            new ArrayBlockingQueue(1)
    ));
    @Autowired
    AlertRecordService alertRecordService;
    ///user/checkAuth
    public boolean checkAuth(String jobName,int timeOutSeconds) {
        try {
            simpleTimeLimiter.runWithTimeout(()->{
                CommonHeaderDTO commonHeaderDTO = new CommonHeaderDTO();
                commonHeaderDTO.setDeviceId("06fbd3fe696dac35");
                commonHeaderDTO.setBrand("vivo");
                commonHeaderDTO.setGps("0.0,0.0");
                commonHeaderDTO.setBs("bs");
                commonHeaderDTO.setAppVersion("appVersion");
                commonHeaderDTO.setOs("android");
                commonHeaderDTO.setChannel("channel");
                commonHeaderDTO.setRomVersion("romVersion");
                commonHeaderDTO.setOsVersion("1");
                commonHeaderDTO.setAccessKey("a21fde9f430e500f32792b28c6687173_256549244");
                commonHeaderDTO.setWechatId("123");
                commonHeaderDTO.setPkgId("333");
                commonHeaderDTO.setAppId("482");
                String reqAuthUrl = authUrl;
                String rspStr = httpClientService.doPost(reqAuthUrl,commonHeaderDTO.getMap(),commonHeaderDTO.getMap());
                JSONObject jsonObject = JSON.parseObject(rspStr);
                Integer isAck = jsonObject.getInteger("code");
                log.info("rspStr  "+rspStr);
                if(isAck!=0){
                    AlertRecord alertRecord = alertRecordService.insertAlertRecord(jobName,"用户checkAuth", AlertModel.SERVICEREQ,
                            "测试登录失败"+rspStr, AlertStatus.INIT,DingTailService.dset, AlertType.DINGDING);
                    DingTailService.sendMarkdownMsg(alertRecord);
                }
            },timeOutSeconds,TimeUnit.SECONDS);
        }catch (Exception e){
            XxlJobLogger.log(e.toString());
            AlertRecord alertRecord = alertRecordService.insertAlertRecord(jobName,"用户checkAuth", AlertModel.SERVICEREQ,
                    ""+e.getClass().getName()+" "+e.getMessage(), AlertStatus.INIT,DingTailService.dset, AlertType.DINGDING);
            DingTailService.sendMarkdownMsg(alertRecord);
        }
        return false;
    }
}
