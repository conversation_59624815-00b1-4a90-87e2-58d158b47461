package com.shinet.core.alert.clickhouse.service;

import com.alibaba.fastjson.JSON;
import com.shinet.core.alert.clickhouse.entity.*;
import com.shinet.core.alert.clickhouse.mapper.ck1.ClickHouseDwdMapper;
import com.shinet.core.alert.clickhouse.mapper.ck4.ClickHouse4DwdMapper;
import com.shinet.core.alert.coreservice.entity.AlertRecord;
import com.shinet.core.alert.coreservice.enums.*;
import com.shinet.core.alert.coreservice.service.AlertDelayJobService;
import com.shinet.core.alert.coreservice.service.AlertRecordService;
import com.shinet.core.alert.dingtalk.DingTailService;
import com.shinet.core.alert.dingtalk.VedioAlertService;
import com.xxl.job.core.log.XxlJobLogger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2024/1/30
 */
@Service
public class RealtimeCostHourService {

    @Resource
    private ClickHouseDwdMapper clickHouseDwdMapper;
    @Autowired
    private AlertRecordService alertRecordService;
    @Autowired
    private ClickHouse4DwdMapper clickHouse4DwdMapper;
    @Autowired
    private AlertDelayJobService alertDelayJobService;

    /**
     * 分时数据校验
     */
    public void checkHourData() {
        int gapRate = 20;
        Date now = new Date();
        int minus = now.getMinutes();
        if (minus < 30) {
            gapRate = 30;
        }
        XxlJobLogger.log("当前时间检查, GAP容错设置为 {} ", gapRate);
        List<RealtimeCostHourBean> realtimeCostHourBeanList = clickHouseDwdMapper.queryRealHourCostEx(gapRate);
        realtimeCostHourBeanList = realtimeCostHourBeanList.stream()
                .filter(r -> {
                    double bdGapRate = 40d;
                    if (minus < 30) {
                        bdGapRate = 60d;
                    }
                    if ("baidu".equals(r.getDsp())) {
                        return r.getDnuGap() > bdGapRate;
                    }
                    return true;
                }).collect(Collectors.toList());
        if (realtimeCostHourBeanList.size() > 0) {
            String alert = "### 前一小时  \n\n " + realtimeCostHourBeanList.stream()
                    .map(r -> String.format("平台:%s,今日我方激活:**%s**,三方激活:**%s**,消耗:**%s**,GAP过大",
                            r.getDsp(), r.getDnuOwnTy(), r.getDnuThirdTy(), format(r.getCostTy())))
                    .collect(Collectors.joining(";\n\n"));
            AlertRecord alertRecord = alertRecordService.insertAlertRecord(AlertJobDelayModel.ALL_DSP_COST.getJobId(),
                    AlertJobDelayModel.ALL_DSP_COST.getJobName(), AlertModel.ADVERTISER_COST_HOUR_CHECK, alert
                    , AlertStatus.INIT, DingTailService.dsetData, AlertType.DINGDING);
            DingTailService.sendMarkdownMsg(alertRecord, AlertRoute.SJXT.value);
        }
    }

    //    @PostConstruct
    public void todayNewAuIncomelAlert() {
        try {
            List<TodayNewAuIncomeBean> todayNewAuIncomeBeanList = clickHouse4DwdMapper.queryTodayNewAuIncome();
            XxlJobLogger.log("今日新增数据结果：", JSON.toJSON(todayNewAuIncomeBeanList));

            if (todayNewAuIncomeBeanList.size() > 0) {

                List<TodayNewAuIncomeBean> callBackRateList = todayNewAuIncomeBeanList.stream()
                        .filter(r -> alertDelayJobService.canSend(AlertJobDelayModel.TODAY_NEW_AU_INCOME_ALERT.getJobId() + "_" + r.getOs()))
                        .collect(Collectors.toList());
                if (callBackRateList.size() > 0) {
                    List<String> productList = callBackRateList.stream().map(TodayNewAuIncomeBean::getOs).collect(Collectors.toList());

                    String alert = todayNewAuIncomeBeanList.stream()
                            .map(r -> String.format("**%s** 截止:**%s**时 **%s** -新增设备:  \n\n" +
                                                    " 今日**%s**时新增设备数：**%s** (**%s**) \n\n" +
                                                    " 今日总新增设备数：**%s** (**%s**) \n\n" +
                                                    " 今日总新增设备收入：**%s** (**%s**)  ",
                                            r.getLogday(), r.getHour() + 1, r.getOs(),
                                            r.getHour(), r.getNewAuTodayHour().toString(), r.getCeilP(r.getNewAuRateYesHour()),
                                            r.getNewAuToday().toString(), r.getCeilP(r.getNewAuRateYes()),
                                            r.getNewAuIncomeToday().toString(), r.getCeilP(r.getNewAuIncomeRateYes())
                                    )
                            )
                            .collect(Collectors.joining(";\n\n"));

                    AlertRecord alertRecord = alertRecordService.insertAlertRecord(AlertJobDelayModel.TODAY_NEW_AU_INCOME_ALERT.getJobId(),
                            AlertJobDelayModel.TODAY_NEW_AU_INCOME_ALERT.getJobName(), AlertModel.TODAY_DATA, alert
                            , AlertStatus.INIT, DingTailService.hxzbData, AlertType.DINGDING);
                    //生产
                    DingTailService.sendMarkdownMsgDelay(alertRecord, AlertRoute.GSHXZB.value, AlertJobDelayModel.TODAY_NEW_AU_INCOME_ALERT, productList);

                    for (int i = 0; i < todayNewAuIncomeBeanList.size(); i++) {
                        TodayNewAuIncomeBean todayNewAuIncomeBean = todayNewAuIncomeBeanList.get(i);
                        Double newAuRateYesHour = todayNewAuIncomeBean.getNewAuRateYesHour();
                        if (newAuRateYesHour < -50) {
                            String phone = String.format("**%s** 截止:**%s**时 **%s**平台 -新增设备波动异常:  \n\n" +
                                                    " 今日**%s**时新增设备数下降超：**%s**",
                                            todayNewAuIncomeBean.getLogday(), todayNewAuIncomeBean.getHour() + 1, todayNewAuIncomeBean.getOs(),
                                            todayNewAuIncomeBean.getHour(),  todayNewAuIncomeBean.getFloorP(todayNewAuIncomeBean.getNewAuRateYesHour())
                                    );
                            //电话
                            VedioAlertService.sendVocMsgWithBiRui("公司核心指标紧急预警", phone, DingTailService.hxzbPhoneData);
                        }
                    }


                }
            }
        } catch (Exception e) {
            XxlJobLogger.log("CheckEx:", e);
        }
    }


    //    @PostConstruct
    public void todayOldAuIncomelAlert() {
        try {
            List<TodayOldAuIncomeBean> todayOldAuIncomeBeanList = clickHouse4DwdMapper.queryTodayOldAuIncome();
            XxlJobLogger.log("今日老设备数据结果：", JSON.toJSON(todayOldAuIncomeBeanList));

            if (todayOldAuIncomeBeanList.size() > 0) {

                List<TodayOldAuIncomeBean> callBackRateList = todayOldAuIncomeBeanList.stream()
                        .filter(r -> alertDelayJobService.canSend(AlertJobDelayModel.TODAY_OLD_AU_INCOME_ALERT.getJobId() + "_" + r.getOs()))
                        .collect(Collectors.toList());
                if (callBackRateList.size() > 0) {
                    List<String> productList = callBackRateList.stream().map(TodayOldAuIncomeBean::getOs).collect(Collectors.toList());

                    String alert = todayOldAuIncomeBeanList.stream()
                            .map(r -> String.format("**%s** 截止:**%s**时 **%s** -老设备 :  \n\n" +
                                                    " 今日**%s**时老设备收入：**%s** (**%s**)  \n\n" +
                                                    " 今日总老设备数：**%s** (**%s**)  \n\n" +
                                                    " 今日总老设备收入：**%s** (**%s**)  ",
                                            r.getLogday(), r.getHour() + 1, r.getOs(),
                                            r.getHour(), r.getOldAuIncomeTodayHour().toString(), r.getCeilP(r.getOldAuIncomeRateYesHour()),
                                            r.getOldAuToday().toString(), r.getCeilP(r.getOldAuRateYes()),
                                            r.getOldAuIncomeToday().toString(), r.getCeilP(r.getOldAuIncomeRateYes())
                                    )
                            )
                            .collect(Collectors.joining(";\n\n"));

                    AlertRecord alertRecord = alertRecordService.insertAlertRecord(AlertJobDelayModel.TODAY_OLD_AU_INCOME_ALERT.getJobId(),
                            AlertJobDelayModel.TODAY_OLD_AU_INCOME_ALERT.getJobName(), AlertModel.TODAY_DATA, alert
                            , AlertStatus.INIT, DingTailService.hxzbData, AlertType.DINGDING);
                    //生产
                    DingTailService.sendMarkdownMsgDelay(alertRecord, AlertRoute.GSHXZB.value, AlertJobDelayModel.TODAY_OLD_AU_INCOME_ALERT, productList);

                    for (int i = 0; i < todayOldAuIncomeBeanList.size(); i++) {
                        TodayOldAuIncomeBean todayOldAuIncomeBean = todayOldAuIncomeBeanList.get(i);
                        Double newAuRateYesHour = todayOldAuIncomeBean.getOldAuIncomeRateYesHour();
                        if (newAuRateYesHour < -50) {
                            String phone = String.format("**%s** 截止:**%s**时 **%s**平台 --老设备收入波动异常:  \n\n" +
                                            " 今日**%s**时老设备收入下降超：**%s**",
                                    todayOldAuIncomeBean.getLogday(), todayOldAuIncomeBean.getHour() + 1, todayOldAuIncomeBean.getOs(),
                                    todayOldAuIncomeBean.getHour(),  todayOldAuIncomeBean.getFloorP(todayOldAuIncomeBean.getOldAuIncomeRateYesHour())
                            );
                            //电话
                            VedioAlertService.sendVocMsgWithBiRui("公司核心指标紧急预警", phone, DingTailService.hxzbPhoneData);
                        }
                    }

                }
            }
        } catch (Exception e) {
            XxlJobLogger.log("CheckEx:", e);
        }
    }


    //    @PostConstruct
    public void yesNewAuIncomelAlert() {
        try {
            List<TodayNewAuIncomeBean> todayNewAuIncomeBeanList = clickHouse4DwdMapper.queryYesNewAuIncome();
            XxlJobLogger.log("今日新增数据结果：", JSON.toJSON(todayNewAuIncomeBeanList));

            if (todayNewAuIncomeBeanList.size() > 0) {
                List<TodayNewAuIncomeBean> callBackRateList = todayNewAuIncomeBeanList.stream()
                        .filter(r -> alertDelayJobService.canSend(AlertJobDelayModel.TODAY_NEW_AU_INCOME_ALERT.getJobId() + "_" + r.getOs()))
                        .collect(Collectors.toList());

                if (callBackRateList.size() > 0) {
                    List<String> productList = callBackRateList.stream().map(TodayNewAuIncomeBean::getOs).collect(Collectors.toList());

                    String alert = todayNewAuIncomeBeanList.stream()
                            .map(r -> String.format("**%s** 截止:**%s**时 **%s** -新增设备:  \n\n" +
                                                    " 今日**%s**时新增设备数：**%s** (**%s**) \n\n" +
                                                    " 今日总新增设备数：**%s** (**%s**) \n\n" +
                                                    " 今日总新增设备收入：**%s** (**%s**)  ",
                                            r.getLogday(), r.getHour() + 1, r.getOs(),
                                            r.getHour(), r.getNewAuTodayHour().toString(), r.getCeilP(r.getNewAuRateYesHour()),
                                            r.getNewAuToday().toString(), r.getCeilP(r.getNewAuRateYes()),
                                            r.getNewAuIncomeToday().toString(), r.getCeilP(r.getNewAuIncomeRateYes())
                                    )
                            )
                            .collect(Collectors.joining(";\n\n"));

                    AlertRecord alertRecord = alertRecordService.insertAlertRecord(AlertJobDelayModel.TODAY_NEW_AU_INCOME_ALERT.getJobId(),
                            AlertJobDelayModel.TODAY_NEW_AU_INCOME_ALERT.getJobName(), AlertModel.TODAY_DATA, alert
                            , AlertStatus.INIT, DingTailService.hxzbData, AlertType.DINGDING);
                    //生产
                    DingTailService.sendMarkdownMsgDelay(alertRecord, AlertRoute.GSHXZB.value, AlertJobDelayModel.TODAY_NEW_AU_INCOME_ALERT, productList);

                    for (int i = 0; i < todayNewAuIncomeBeanList.size(); i++) {
                        TodayNewAuIncomeBean todayNewAuIncomeBean = todayNewAuIncomeBeanList.get(i);
                        Double newAuRateYesHour = todayNewAuIncomeBean.getNewAuRateYesHour();
                        if (newAuRateYesHour < -50) {
                            String phone = String.format("**%s** 截止:**%s**时 **%s**平台 -新增设备波动异常:  \n\n" +
                                            " 今日**%s**时新增设备数下降超：**%s**",
                                    todayNewAuIncomeBean.getLogday(), todayNewAuIncomeBean.getHour() + 1, todayNewAuIncomeBean.getOs(),
                                    todayNewAuIncomeBean.getHour(),  todayNewAuIncomeBean.getFloorP(todayNewAuIncomeBean.getNewAuRateYesHour())
                            );
                            //电话
                            VedioAlertService.sendVocMsgWithBiRui("公司核心指标紧急预警", phone, DingTailService.hxzbPhoneData);
                        }
                    }

                }
            }
        } catch (Exception e) {
            XxlJobLogger.log("CheckEx:", e);
        }
    }


    //    @PostConstruct
    public void yesOldAuIncomelAlert() {
        try {
            List<TodayOldAuIncomeBean> todayOldAuIncomeBeanList = clickHouse4DwdMapper.queryYesOldAuIncome();
            XxlJobLogger.log("今日老设备数据结果：", JSON.toJSON(todayOldAuIncomeBeanList));

            if (todayOldAuIncomeBeanList.size() > 0) {

                List<TodayOldAuIncomeBean> callBackRateList = todayOldAuIncomeBeanList.stream()
                        .filter(r -> alertDelayJobService.canSend(AlertJobDelayModel.TODAY_OLD_AU_INCOME_ALERT.getJobId() + "_" + r.getOs()))
                        .collect(Collectors.toList());
                if (callBackRateList.size() > 0) {
                    List<String> productList = callBackRateList.stream().map(TodayOldAuIncomeBean::getOs).collect(Collectors.toList());

                    String alert = todayOldAuIncomeBeanList.stream()
                            .map(r -> String.format("**%s** 截止:**%s**时 **%s** -老设备 :  \n\n" +
                                                    " 今日**%s**时老设备收入：**%s** (**%s**)  \n\n" +
                                                    " 今日总老设备数：**%s** (**%s**)  \n\n" +
                                                    " 今日总老设备收入：**%s** (**%s**)  ",
                                            r.getLogday(), r.getHour() + 1, r.getOs(),
                                            r.getHour(), r.getOldAuIncomeTodayHour().toString(), r.getCeilP(r.getOldAuIncomeRateYesHour()),
                                            r.getOldAuToday().toString(), r.getCeilP(r.getOldAuRateYes()),
                                            r.getOldAuIncomeToday().toString(), r.getCeilP(r.getOldAuIncomeRateYes())
                                    )
                            )
                            .collect(Collectors.joining(";\n\n"));

                    AlertRecord alertRecord = alertRecordService.insertAlertRecord(AlertJobDelayModel.TODAY_OLD_AU_INCOME_ALERT.getJobId(),
                            AlertJobDelayModel.TODAY_OLD_AU_INCOME_ALERT.getJobName(), AlertModel.TODAY_DATA, alert
                            , AlertStatus.INIT, DingTailService.hxzbData, AlertType.DINGDING);
                    //生产
                    DingTailService.sendMarkdownMsgDelay(alertRecord, AlertRoute.GSHXZB.value, AlertJobDelayModel.TODAY_OLD_AU_INCOME_ALERT, productList);

                    for (int i = 0; i < todayOldAuIncomeBeanList.size(); i++) {
                        TodayOldAuIncomeBean todayOldAuIncomeBean = todayOldAuIncomeBeanList.get(i);
                        Double newAuRateYesHour = todayOldAuIncomeBean.getOldAuIncomeRateYesHour();
                        if (newAuRateYesHour < -50) {
                            String phone = String.format("**%s** 截止:**%s**时 **%s**平台 --老设备收入波动异常:  \n\n" +
                                            " 今日**%s**时老设备收入下降超：**%s**",
                                    todayOldAuIncomeBean.getLogday(), todayOldAuIncomeBean.getHour() + 1, todayOldAuIncomeBean.getOs(),
                                    todayOldAuIncomeBean.getHour(),  todayOldAuIncomeBean.getFloorP(todayOldAuIncomeBean.getOldAuIncomeRateYesHour())
                            );
                            //电话
                            VedioAlertService.sendVocMsgWithBiRui("公司核心指标紧急预警", phone, DingTailService.hxzbPhoneData);
                        }
                    }

                }
            }
        } catch (Exception e) {
            XxlJobLogger.log("CheckEx:", e);
        }
    }

//    @PostConstruct
    public void realtimeCheckDataAlert() {
        try {
            List<CheckDataSumPv> odsEventDistPvList = clickHouse4DwdMapper.queryOdsEventDistPv();
            XxlJobLogger.log("过去5分钟数据：", JSON.toJSON(odsEventDistPvList));

            if (odsEventDistPvList.size() > 0) {


                    for (int i = 0; i < odsEventDistPvList.size(); i++) {
                        CheckDataSumPv checkDataSumPv = odsEventDistPvList.get(i);
                        Long pv = checkDataSumPv.getPv();
                        if (pv == 0) {
                            String phone = String.format("紧急紧急紧急，ODS埋点表过去五分钟数据为0了，火速处理  "
                            );
                            //电话
                            VedioAlertService.sendVocMsgWithBiRui("超级无敌宇宙第一紧急预警", phone, DingTailService.dailyData);
                        }
                    }


            }
        } catch (Exception e) {
            XxlJobLogger.log("CheckEx:", e);
        }
    }

//        @PostConstruct
    public void realtimeCheckOcpcRateAlert() {
        try {
            List<OcpcRateBean> ocpcRateList = clickHouse4DwdMapper.queryOcpcRate();
            XxlJobLogger.log("最新：", JSON.toJSON(ocpcRateList));

            if (ocpcRateList.size() > 0) {

                List<OcpcRateBean> callBackRateList = ocpcRateList.stream()
                        .filter(r -> alertDelayJobService.canSend(AlertJobDelayModel.TODAY_OCPC_EXACT_RATE_ALERT.getJobId() + "_" + r.getProduct()))
                        .collect(Collectors.toList());

                if (callBackRateList.size() > 0) {
                    List<String> productList = callBackRateList.stream().map(OcpcRateBean::getProduct).collect(Collectors.toList());

                    String alert = callBackRateList.stream()
                            .map(r -> String.format("%s(**%s**): 当前精准归因数**%s**, 占比**%s**, 同比%s",
                                            r.getProductName(),r.getProduct(),r.getGyPvToday(),r.getFloorP(r.getRateToday()),r.getFloorP(r.getRateDown())
                                    )
                            )
                            .collect(Collectors.joining(";\n\n"));

                    AlertRecord alertRecord = alertRecordService.insertAlertRecord(AlertJobDelayModel.TODAY_OCPC_EXACT_RATE_ALERT.getJobId(),
                            AlertJobDelayModel.TODAY_OCPC_EXACT_RATE_ALERT.getJobName(), AlertModel.OCPC_EXACT_DOWN_ALERT, alert
                            , AlertStatus.INIT, DingTailService.ocpcAlarmData, AlertType.DINGDING);
                    //生产
                    DingTailService.sendMarkdownMsgDelay(alertRecord, AlertRoute.GYHCHXQ.value, AlertJobDelayModel.TODAY_OCPC_EXACT_RATE_ALERT, productList);
//                    DingTailService.sendMarkdownMsg(alertRecord, AlertRoute.SJXT.value);

                }

            }
        } catch (Exception e) {
            XxlJobLogger.log("CheckEx:", e);
        }
    }

    private static String format(Double value) {
        BigDecimal bd = new BigDecimal(value);
        bd = bd.setScale(2, RoundingMode.HALF_UP);
        return bd.toString();
    }
}
