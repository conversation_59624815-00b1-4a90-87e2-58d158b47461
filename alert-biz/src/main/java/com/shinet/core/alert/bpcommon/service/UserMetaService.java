package com.shinet.core.alert.bpcommon.service;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.google.common.base.Functions;
import com.shinet.core.alert.bpcommon.entity.User;
import com.shinet.core.alert.bpcommon.entity.UserMeta;
import com.shinet.core.alert.bpcommon.mapper.UserMetaMapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.shinet.core.alert.dataagent.entity.UserMetaDel;
import com.shinet.core.alert.dataagent.service.UserMetaDelService;
import lombok.extern.slf4j.Slf4j;
import org.joda.time.DateTimeConstants;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
* <p>
    * 用户 meta data 服务实现类
    * </p>
*
* <AUTHOR>
* @since 2021-11-26
*/
@Service
@Slf4j
public class UserMetaService extends ServiceImpl<UserMetaMapper, UserMeta> {
    @Autowired
    UserMetaDelService userMetaDelService;
    public int removeAndSaveUserMeta(List<Long> userIdSet, int pkgId,boolean isDelStart) {
        if(userIdSet==null || userIdSet.size()==0){
            return 0;
        }
        QueryWrapper<UserMeta> objectQueryWrapper = new QueryWrapper<UserMeta>();
        objectQueryWrapper.lambda().in(UserMeta::getUserId, userIdSet);
        objectQueryWrapper.lambda().eq(UserMeta::getPkgId, pkgId);

        List<UserMeta> userMetaList = this.baseMapper.selectList(objectQueryWrapper);
        List<UserMeta> userDelMetaList = userMetaList.stream().filter(userMeta -> userMeta.getCreateTime() < (System.currentTimeMillis() - DateTimeConstants.MILLIS_PER_DAY * 60)).collect(Collectors.toList());
        log.info("总计 "+userDelMetaList.size()+"条userMeta需要回收");
        if (userDelMetaList.size() > 0) {
            List<UserMetaDel> userMetaListSave = userDelMetaList.stream().map(userMeta -> {
                UserMetaDel userMetaDel = new UserMetaDel();
                BeanUtils.copyProperties(userMeta, userMetaDel);
                return userMetaDel;
            }).collect(Collectors.toList());
            if (userMetaListSave.size() > 0) {
                QueryWrapper<UserMetaDel> delSaveWrapper = new QueryWrapper<UserMetaDel>();
                delSaveWrapper.lambda().in(UserMetaDel::getUserId, userIdSet);
                delSaveWrapper.lambda().eq(UserMetaDel::getPkgId, pkgId);
                boolean isSuc = true;
                int dbDelNum = userMetaDelService.count(delSaveWrapper);
                int saveNum = userMetaListSave.size();
                log.info("开始保存userMetaDel 已经存在 "+dbDelNum);
                if(dbDelNum==0){
                    isSuc = userMetaDelService.saveBatch(userMetaListSave);
                }else{
                    List<UserMetaDel> userMetaDels  = userMetaDelService.list(delSaveWrapper);
                    Map<String,UserMetaDel> userDelMap = userMetaDels.stream().collect(Collectors.toMap(userMetaDel -> userMetaDel.getUserId()+"@"+userMetaDel.getPkgId(), Functions.identity(),(t1, t2)->t1));
                    List<UserMetaDel> toSaveList = userMetaListSave.stream().filter(userMeta->userDelMap.get(userMeta.getUserId()+"@"+userMeta.getPkgId())==null).collect(Collectors.toList());
                    if(toSaveList.size()>0){
                        isSuc = userMetaDelService.saveBatch(toSaveList);
                        saveNum = toSaveList.size();
                    }
                }
                log.info("保存userMetaDel 完成 "+saveNum);
                if (isSuc) {
                    List<Long> removeUids = userMetaListSave.stream().map(userMetaDel -> userMetaDel.getUserId()).collect(Collectors.toList());
                    if (removeUids.size() > 0) {
                        QueryWrapper<UserMeta> objectQueryWrapperRmove = new QueryWrapper<UserMeta>();
                        objectQueryWrapperRmove.lambda().in(UserMeta::getUserId, removeUids);
                        objectQueryWrapperRmove.lambda().eq(UserMeta::getPkgId, pkgId);
                        if(isDelStart){
                            int cnum = count(objectQueryWrapperRmove);
                            if(cnum<=10000){
                                remove(objectQueryWrapperRmove);
                                log.info("已经删除 "+cnum+" 条userMeta数据");
                            }
                        }else{
                            int cnum = count(objectQueryWrapperRmove);
                            log.info("删除 "+cnum+" 条");
                        }
                        return removeUids.size();
                    }
                }
            }
        }else{
            log.info("userMeta数据已经迁移完成，直接忽略");
        }
        return 0;
    }
    @Autowired
    WechatAppService wechatAppService;
    public List<Long> getNotContainUsers(List<Long> userIdSet,int pkgId){
        Set<Long> notPkgSet = wechatAppService.getNotSet(pkgId);
        QueryWrapper<UserMeta> queryWrapper = new QueryWrapper<UserMeta>();
        queryWrapper.lambda().in(UserMeta::getUserId, userIdSet);
        queryWrapper.lambda().in(UserMeta::getPkgId, notPkgSet);

        List<UserMeta> notMoveUserMeta = list(queryWrapper);
        if(notMoveUserMeta.size()>0){
            log.info(notMoveUserMeta.size()+" 条数据不应该呗移除 ");
            Map<Long,UserMeta> userMetaMap = notMoveUserMeta.stream().collect(Collectors.toMap(UserMeta::getUserId, Function.identity(),(t1,t2)->t1));
            List<Long> userMoveIdSet = userIdSet.stream().filter(userId->userMetaMap.get(userId)==null).collect(Collectors.toList());
            return userMoveIdSet;
        }else{
            return userIdSet;
        }
    }
}
