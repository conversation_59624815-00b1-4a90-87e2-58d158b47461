package com.shinet.core.alert.safe.entity;

import com.shinet.core.alert.dsp.entity.AdPlanConfig;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR>
 * @since 2023-11-16
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
public class VirusCheck implements Serializable {

    private static final long serialVersionUID = 1L;

    private String product;

    private String productName;

    private String url;

    private Integer upStatus;

    public VirusCheck(AdPlanConfig p){
        productName = p.getAppName();
        url = p.getDownloadUrl();
    }

}
