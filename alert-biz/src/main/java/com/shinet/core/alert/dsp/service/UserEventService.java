package com.shinet.core.alert.dsp.service;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.shinet.core.alert.coreservice.entity.AlertRecord;
import com.shinet.core.alert.coreservice.enums.AlertModel;
import com.shinet.core.alert.coreservice.enums.AlertStatus;
import com.shinet.core.alert.coreservice.enums.AlertType;
import com.shinet.core.alert.coreservice.service.AlertRecordService;
import com.shinet.core.alert.data.entity.DataCheck;
import com.shinet.core.alert.dingtalk.DingMsgService;
import com.shinet.core.alert.dingtalk.DingTailService;
import com.shinet.core.alert.dingtalk.VedioAlertService;
import com.shinet.core.alert.dsp.entity.AppCoreeventBean;
import com.shinet.core.alert.dsp.entity.UserEvent;
import com.shinet.core.alert.dsp.mapper.UserEventMapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.shinet.core.alert.util.DateUtils;
import com.shinet.core.alert.util.DoubleUtil;
import com.xxl.job.core.log.XxlJobLogger;
import lombok.extern.slf4j.Slf4j;
import org.joda.time.DateTimeConstants;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <p>
 * 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-08-09
 */
@Service
@Slf4j
public class UserEventService extends ServiceImpl<UserEventMapper, UserEvent> {

    public long queryActiveCount(Date startDate, Date endDate) {
        QueryWrapper<UserEvent> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().ge(UserEvent::getCreateTime,startDate).le(UserEvent::getCreateTime,endDate).eq(UserEvent::getEventType,0);

        return baseMapper.selectCount(queryWrapper);
    }
    @Autowired
    AlertRecordService alertRecordService;
    @Autowired
    UserEventMapper userEventMapper;
    public void alertCoreEvent(String jobName,double rateL,int eventType){
        //select LEFT(create_time,13),count(1) from ocpc_event where create_time>'2021-11-23' and create_time<'2021-11-24' and event_type=25  and dsp='toutiao'  GROUP BY LEFT(create_time,13);
        long curTime = System.currentTimeMillis();
        String  start1DateStr = DateUtils.formatDate(new Date(),DateUtils.PATTERN_H);
        String  end1DateStr= DateUtils.formatDate(new Date(),DateUtils.PATTERN_YHMS);
        List<AppCoreeventBean>  appCoreeventBeanList = userEventMapper.queryDspCoreevent(start1DateStr,end1DateStr,eventType);

        Map<String,AppCoreeventBean> appCoreeventBeanMap = appCoreeventBeanList.stream().collect(Collectors.toMap(appCoreeventBean -> appCoreeventBean.getDsp()+appCoreeventBean.getCovertTime().getHours(), Function.identity()));

        String  start2DateStr = DateUtils.formatDate(new Date(curTime - DateTimeConstants.MILLIS_PER_DAY),DateUtils.PATTERN_H);
        String  end2DateStr= DateUtils.formatDate(new Date(curTime - DateTimeConstants.MILLIS_PER_DAY),DateUtils.PATTERN_YHMS);
        List<AppCoreeventBean>  yesCoreeventBeanList = userEventMapper.queryDspCoreevent(start2DateStr,end2DateStr,eventType);
        String alertMsg = "";
        boolean isAlert = false;
        for(AppCoreeventBean appCoreeventBean : yesCoreeventBeanList){
            int yesHours = appCoreeventBean.getCovertTime().getHours();
            String yesDsp = appCoreeventBean.getDsp();
            int yesConvertNum = appCoreeventBean.getConvertNum();
            if(yesConvertNum<=100){
                continue;
            }
            AppCoreeventBean todayConvertBean = appCoreeventBeanMap.get(yesDsp+yesHours);
            alertMsg = alertMsg + "\r\n yes:"+yesDsp+"-"+yesHours+"-"+yesConvertNum;
            double rate = DoubleUtil.divideDouble(yesConvertNum-todayConvertBean.getConvertNum(),yesConvertNum)*100;
            if(todayConvertBean==null){
                alertMsg = alertMsg + " today:is null rate:" +rate;
                XxlJobLogger.log(alertMsg);
            }else{
                alertMsg = alertMsg + " today:"+todayConvertBean.getDsp()+"-"+todayConvertBean.getCovertTime().getHours()+"-"+todayConvertBean.getConvertNum()+" rate:" +rate;;
                XxlJobLogger.log(alertMsg);
            }

            if(yesConvertNum>todayConvertBean.getConvertNum() && rate>0 && rate>=(rateL*100)
//                    && !"guangdiantong".equalsIgnoreCase(todayConvertBean.getDsp())//临时剔除 广点通
            ){
                isAlert = true;
            }
        }

        if(isAlert){
            AlertRecord alertRecord = alertRecordService.insertAlertRecord(jobName,"用户checkAuth", AlertModel.OCPC,alertMsg, AlertStatus.INIT,DingTailService.dset, AlertType.DINGDING);
            DingTailService.sendMarkdownMsg(alertRecord);
            VedioAlertService.sendVocMsg("OCPC",alertMsg);
            XxlJobLogger.log("预警："+alertMsg);
        }

    }
}
