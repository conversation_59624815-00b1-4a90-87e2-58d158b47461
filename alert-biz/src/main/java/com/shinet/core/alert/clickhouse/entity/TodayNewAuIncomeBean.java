package com.shinet.core.alert.clickhouse.entity;

import lombok.Data;

/**
 * <AUTHOR>
 * @since 2025/04/29
 */
@Data
public class TodayNewAuIncomeBean {

    private String logday;
    private Integer hour;
    private String os;
    private Integer newAuToday;
    private Double newAuIncomeToday;
    private Integer newAuTodayHour;
    private Integer newAuYes;
    private Double newAuIncomeYes;
    private Integer newAuYesHour;
    private Double newAuRateYes;
    private Double newAuIncomeRateYes;
    private Double newAuRateYesHour;

    public String getCeilP(Double p) {
        return Math.ceil(p) + "%";
    }

    public String getFloorP(Double p) {
        return Math.round(Math.floor(p)) + "%";
    }

}
