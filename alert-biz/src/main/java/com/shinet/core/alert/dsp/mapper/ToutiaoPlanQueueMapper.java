package com.shinet.core.alert.dsp.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.shinet.core.alert.dsp.entity.PlanStatusCount;
import com.shinet.core.alert.dsp.entity.ToutiaoPlanQueue;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * <p>
 *  Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2022-09-22
 */
public interface ToutiaoPlanQueueMapper extends BaseMapper<ToutiaoPlanQueue> {
    @Select("select plan_status,count(1) as cnum from toutiao_plan_queue where create_time>'${startTime}' GROUP BY plan_status")
    List<PlanStatusCount> queryStatusCount(@Param("startTime") String startTime);
}
