package com.shinet.core.alert.bpcommon.service;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.shinet.core.alert.bpcommon.entity.WechatOpenId;
import com.shinet.core.alert.bpcommon.mapper.WechatOpenIdMapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.shinet.core.alert.dataagent.entity.UserDel;
import com.shinet.core.alert.dataagent.entity.WechatOpenIdDel;
import com.shinet.core.alert.dataagent.service.WechatOpenIdDelService;
import lombok.extern.slf4j.Slf4j;
import org.joda.time.DateTimeConstants;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
* <p>
    *  服务实现类
    * </p>
*
* <AUTHOR>
* @since 2021-11-26
*/
@Service
@Slf4j
public class WechatOpenIdService extends ServiceImpl<WechatOpenIdMapper, WechatOpenId> {
    @Autowired
    WechatOpenIdDelService wechatOpenIdDelService;
    public int removeAndSaveOpenId(List<Long> userIdSet, int pkgId,boolean isDelStart) {
        if (userIdSet == null || userIdSet.size() == 0) {
            return 0;
        }
        QueryWrapper<WechatOpenId> userQueryWrapper = new QueryWrapper<WechatOpenId>();
        userQueryWrapper.lambda().in(WechatOpenId::getUserId, userIdSet);
        userQueryWrapper.lambda().eq(WechatOpenId::getWechatId, pkgId);
        List<WechatOpenId> userOpenIdList = list(userQueryWrapper);
        List<WechatOpenId> userOpenIdListRemove = userOpenIdList.stream().filter(wechatOpenId -> wechatOpenId.getCreateTime() < (System.currentTimeMillis() - DateTimeConstants.MILLIS_PER_DAY * 60)).collect(Collectors.toList());
        if (userOpenIdListRemove.size() > 0) {
            List<WechatOpenIdDel> wechatOpenIdDelListSave = userOpenIdListRemove.stream().map(wechatOpenId -> {
                WechatOpenIdDel wechatOpenIdDel = new WechatOpenIdDel();
                BeanUtils.copyProperties(wechatOpenId, wechatOpenIdDel);
                return wechatOpenIdDel;
            }).collect(Collectors.toList());
            if (wechatOpenIdDelListSave.size() > 0) {
                QueryWrapper<WechatOpenIdDel> delSaveWrapper = new QueryWrapper<WechatOpenIdDel>();
                delSaveWrapper.lambda().in(WechatOpenIdDel::getOpenId, userOpenIdListRemove);
                log.info("开始保存wechatOpenIdDelListSave "+wechatOpenIdDelListSave.size());
                boolean isSaveSuc = false;
                int saveNum = wechatOpenIdDelListSave.size();
                if(wechatOpenIdDelService.count(delSaveWrapper)==0){
                    isSaveSuc = wechatOpenIdDelService.saveBatch(wechatOpenIdDelListSave);
                }else{
                    List<WechatOpenIdDel> dbList = wechatOpenIdDelService.list(delSaveWrapper);
                    Map<String,WechatOpenIdDel> dbdelMap = dbList.stream().collect(Collectors.toMap(WechatOpenIdDel::getOpenId, Function.identity(),(t1, t2)->t1));
                    List<WechatOpenIdDel> toSaveList = wechatOpenIdDelListSave.stream().filter(wechatOpenIdDel ->dbdelMap.get(wechatOpenIdDel.getOpenId())==null ).collect(Collectors.toList());
                    saveNum = toSaveList.size();
                    isSaveSuc = wechatOpenIdDelService.saveBatch(toSaveList);
                }
                log.info("保存完成wechatOpenIdDelListSave "+saveNum);
                if (isSaveSuc) {
                    List<String> openIdList = wechatOpenIdDelListSave.stream().map(WechatOpenIdDel::getOpenId).collect(Collectors.toList());
                    if (isDelStart && openIdList.size() < 10000 && openIdList.size()>0) {
                        removeByIds(openIdList);
                        log.info("已经删除 " + openIdList.size() + " 条openId数据");
                    }
                    return openIdList.size();
                }
            }
        } else {
            log.info("openId数据已经迁移完成，直接忽略");
        }
        return 0;
    }
}
