package com.shinet.core.alert.dsp.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.shinet.core.alert.dsp.entity.KuaishouStarOrder;
import com.shinet.core.alert.dsp.entity.KuaishouStarTask;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.math.BigInteger;
import java.util.List;
import java.util.Set;

public interface KuaishouStarOrderMapper extends BaseMapper<KuaishouStarOrder> {

    @Select("select a.advertiser_id as advertiser_id, " +
            "a.task_id as task_id, " +
            "a.order_id as order_id, " +
            "a.star_name as star_name, " +
            "a.user_id as user_id, " +
            "(a.amount - b.amount) / 1000 as sum_cost\n" +
            "from (select *\n" +
            "      from kuaishou_star_order\n" +
            "      where advertiser_id = '4256976886'\n" +
            "        and order_type = 1\n" +
            "        and task_id = '1054447500458064'\n" +
            "        and crawl_date = '2025-03-12') as a\n" +
            "         inner join (select *\n" +
            "                     from kuaishou_star_order\n" +
            "                     where advertiser_id = '4256976886'\n" +
            "                       and order_type = 1\n" +
            "                       and task_id = '1054447500458064'\n" +
            "                       and crawl_date = '2025-03-10') as b on a.order_id = b.order_id\n" +
            "having sum_cost > 0;")
    List<KuaishouStarOrder> queryOrderCost(String advertiser_id, List<String> taskIdList, String start_time, String end_time);


    @Select("<script>" +
            "SELECT a.task_id, (a.sum_amount - b.sum_amount) / 1000 AS sum_cost " +
            "FROM (" +
            "  SELECT task_id, SUM(amount) AS sum_amount " +
            "  FROM kuaishou_star_order " +
            "  WHERE advertiser_id = #{advertiser_id} " +
            "    AND order_type = 1 " +
            "    <if test='taskIdList != null and !taskIdList.isEmpty()'> " +
            "      AND task_id IN " +
            "      <foreach item='item' collection='taskIdList' open='(' separator=',' close=')'>" +
            "        #{item} " +
            "      </foreach> " +
            "    </if> " +
            "    AND crawl_date = #{end_time} " +
            "  GROUP BY task_id" +
            ") AS a " +
            "INNER JOIN (" +
            "  SELECT task_id, SUM(amount) AS sum_amount " +
            "  FROM kuaishou_star_order " +
            "  WHERE advertiser_id = #{advertiser_id} " +
            "    AND order_type = 1 " +
            "    <if test='taskIdList != null and !taskIdList.isEmpty()'> " +
            "      AND task_id IN " +
            "      <foreach item='item' collection='taskIdList' open='(' separator=',' close=')'>" +
            "        #{item} " +
            "      </foreach> " +
            "    </if> " +
            "    AND crawl_date = #{start_time} " +
            "  GROUP BY task_id" +
            ") AS b ON a.task_id = b.task_id" +
            "</script>")
    List<KuaishouStarOrder> queryTaskCost(
            @Param("advertiser_id") String advertiser_id,
            @Param("taskIdList") Set<BigInteger> taskIdList,
            @Param("start_time") String start_time,
            @Param("end_time") String end_time
    );
}
