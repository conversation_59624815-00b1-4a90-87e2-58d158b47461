package com.shinet.core.alert.bpcommon.mapper;

import com.shinet.core.alert.bpcommon.entity.UserMeta;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;

import java.util.List;
import java.util.Set;

/**
 * <p>
 * 用户 meta data Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2021-11-26
 */
public interface UserMetaMapper extends BaseMapper<UserMeta> {


    @Select("select * from user_meta where pkg_id = #{pkgId} and user_id=#{userId}")
    UserMeta findById(
            @Param("pkgId") int pkgId,
            @Param("userId") Long userId
    );


    @Select("<script>select * from user_meta where pkg_id = #{pkgId} and user_id in " +
            "<foreach item='item' index='index' collection='userIdSet' open='(' separator=',' close=')'>"
            + "#{item}"
            + "</foreach></script>")
    List<UserMeta> findByIds(
            @Param("pkgId") int pkgId,
            @Param("userIdSet") Set<Long> userIdSet
    );


    @Update("delete from user_meta where pkg_id = #{pkgId} and user_id=#{userId}")
    int deleteById(
            @Param("pkgId") int pkgId,
            @Param("userId") Long userId
    );


    @Update("<script>delete from user_meta where pkg_id = #{pkgId} and user_id in " +
            " <foreach item='item' index='index' collection='userIdSet' open='(' separator=',' close=')'>"
            + "#{item}"
            + "</foreach></script>")
    int deleteByIds(
            @Param("pkgId") int pkgId,
            @Param("userIdSet") Set<Long> userIdSet
    );
}
