package com.shinet.core.alert.clickhouse.entity;

import lombok.Data;

@Data
public class AdBidRate {
    /**
     * select  product_name,os,sum(if(pos_name like '%bid%',income,0)) as bidincom,sum(if(pos_name like '%bid%',pv,0)) as bidpv,
     *        sum(if(pos_name not like '%bid%',income,0)) as flowincom,sum(if(pos_name not like '%bid%',0,pv)) as flowpv,
     *        sum(income) as allincom,sum(pv) as allpv,(bidincom/allincom)*100 as bidRate
     *        from old_mysql_ads.realtime_ad_income where logday='2024-07-22'  group by  product_name,os having allincom>1000 order by allincom desc;
     */

    private String productName;
    private String os;
    private Double bidincom;
    private Double bidpv;
    private Double flowincom;
    private Double flowpv;
    private Double allincom;
    private Double allpv;
    private Double bidRate;
}
