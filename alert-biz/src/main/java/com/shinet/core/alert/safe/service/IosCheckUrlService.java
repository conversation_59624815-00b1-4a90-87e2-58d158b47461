package com.shinet.core.alert.safe.service;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.shinet.core.alert.common.HttpClientService;
import com.shinet.core.alert.coreservice.entity.AlertRecord;
import com.shinet.core.alert.coreservice.entity.IosCheckRecord;
import com.shinet.core.alert.coreservice.enums.*;
import com.shinet.core.alert.coreservice.service.AlertDelayJobService;
import com.shinet.core.alert.coreservice.service.IosCheckRecordService;
import com.shinet.core.alert.dingtalk.VedioAlertService;
import com.shinet.core.alert.safe.entity.IosCheckUrl;
import com.shinet.core.alert.coreservice.service.AlertRecordService;
import com.shinet.core.alert.safe.mapper.IosCheckUrlMapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.shinet.core.alert.dingtalk.DingTailService;
import com.xxl.job.core.log.XxlJobLogger;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.directory.api.util.Strings;
import org.apache.http.NameValuePair;
import org.apache.http.client.entity.UrlEncodedFormEntity;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpGet;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.message.BasicNameValuePair;
import org.apache.http.params.BasicHttpParams;
import org.apache.http.params.HttpParams;
import org.apache.http.util.EntityUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.stream.Collectors;

/**
* <p>
    *  服务实现类
    * </p>
*
* <AUTHOR>
* @since 2023-11-16
*/
@Slf4j
@Service
public class IosCheckUrlService extends ServiceImpl<IosCheckUrlMapper, IosCheckUrl> {

    @Autowired
    private HttpClientService httpClientService;
    @Autowired
    private AlertRecordService alertRecordService;
    @Autowired
    private IosCheckRecordService iosCheckRecordService;
    @Autowired
    private IOSEmailService iosEmailService;

    public void checkUrlAlive(){
        List<IosCheckUrl> checkUrlList = lambdaQuery().list();

        if (checkUrlList.size() > 0){

            List<IosCheckUrl> sendDingTalkAndPhone = new ArrayList<>();

            for (int i = 0; i < 5 && checkUrlList.size() != 0; i++) {
                List<IosCheckUrl> temp = new ArrayList<>();
                checkUrlAlives(checkUrlList, temp);
                checkUrlList = temp;
                sendDingTalkAndPhone = temp;
                try {
                    Thread.sleep(1000);
                } catch (InterruptedException e) {
                    log.info("checkUrlAlive sleep {}", e);
                }
            }

            if (sendDingTalkAndPhone.size() > 0){
                String az = sendDingTalkAndPhone.stream().map(iosCheckUrl -> {
                    return iosCheckUrl.getProductName() + iosCheckUrl.getUrl();
                }).collect(Collectors.joining(",\n"))+"域名响应异常";
//                VedioAlertService.sendVocMsg("IOS URL ",az,DingTailService.dset);
                AlertRecord alertRecord = alertRecordService.insertAlertRecord("IOS-URL-Check","IOS-URL-Check", AlertModel.iosUrl,
                        az, AlertStatus.INIT,DingTailService.dset, AlertType.DINGPHONE);
                DingTailService.sendMarkdownMsg(alertRecord);
            }
        }
    }

    private void checkUrlAlives(List<IosCheckUrl> checkUrlList, List<IosCheckUrl> sendDingTalkAndPhone) {
        for (IosCheckUrl iosCheckUrl : checkUrlList){
            if (Strings.isNotEmpty(iosCheckUrl.getUrl())){
                try {
                    checkOne(iosCheckUrl, sendDingTalkAndPhone);
                }catch (Exception e){
                    XxlJobLogger.log("Request Er:"+e);
                    log.error("checkUrlAlives checkOne error", e);
                    sendDingTalkAndPhone.add(iosCheckUrl);
                }

            }
        }
    }

    private void checkOne(IosCheckUrl iosCheckUrl, List<IosCheckUrl> sendDingTalkAndPhone) {
        XxlJobLogger.log("start check {}", iosCheckUrl.getUrl());
        HttpStatus httpStatus = httpClientService.getCode(iosCheckUrl.getUrl()+"/ap-gateway/health/info");
        if (httpStatus == null || !httpStatus.is2xxSuccessful()){
            XxlJobLogger.log("Url {} not 2xx,Er code {}", iosCheckUrl.getUrl(), httpStatus);
            sendDingTalkAndPhone.add(iosCheckUrl);
        }
    }

    @Autowired
    private AlertDelayJobService alertDelayJobService;

    public void checkAppStoreUrlAlive(){
        List<IosCheckUrl> checkUrlList = lambdaQuery().list();
        List<IosCheckUrl> waitCheckList = checkUrlList.stream()
                .filter(r -> Strings.isNotEmpty(r.getAppStoreUrl()))
                //下架状态 不检测
                .filter(r -> !Integer.valueOf(0).equals(r.getUpStatus()))
                .collect(Collectors.toList());

        if (waitCheckList.size() > 0){
            List<IosCheckUrl> downAppList = new ArrayList<>();
            List<IosCheckUrl> noAppList = new ArrayList<>();
            List<IosCheckUrl> scoreAppList = new ArrayList<>();
            for (IosCheckUrl iosCheckUrl : waitCheckList){
                try {
                    checkOne(iosCheckUrl, downAppList, noAppList, scoreAppList);
                }catch (Exception e){
                    log.error("iosCheckUrl checkOne error ", e);
                }
            }

            HashSet<String> phones = new HashSet<>();

            if (downAppList.size() > 0){
                for(IosCheckUrl temp : downAppList){
                    if(StringUtils.isNotBlank(temp.getPhone())){
                        phones.add(temp.getPhone());
                    }
                }
                if(phones.isEmpty()){
                    phones.add("15175062197");
                }
                String az = downAppList.stream().map(IosCheckUrl::getProductName).collect(Collectors.joining(","))+"应用商店未查询到";
                AlertRecord alertRecord = alertRecordService.insertAlertRecord("IOS-AppStore-Check","IOS-AppStore-Check", AlertModel.iosAppStore,
                        az, AlertStatus.INIT,phones, AlertType.DINGDING);
                DingTailService.sendMarkdownMsg(alertRecord);
            }

            phones.clear();
            if(CollectionUtils.isNotEmpty(noAppList)){
                noAppList = noAppList.stream().filter(r -> alertDelayJobService.canSend("IOS-AppStore-Check-NO_" + r.getProduct())).collect(Collectors.toList());
                if(CollectionUtils.isNotEmpty(noAppList)){
                    phones.add("18211128646");
                }
                for (int i = 0; i < noAppList.size(); i += 20) {
                    List<IosCheckUrl> temp = noAppList.subList(i, i + 20 < noAppList.size() ? i + 20 : noAppList.size());
                    String az = temp.stream().map(IosCheckUrl::getProductName).collect(Collectors.joining(","))+"应用商店未上榜";
                    AlertRecord alertRecord = alertRecordService.insertAlertRecord("IOS-AppStore-Check-NO","IOS-AppStore-Check-NO", AlertModel.iosAppStoreNO,
                            az, AlertStatus.INIT,phones
                            , AlertType.PHONE);
                    DingTailService.sendMarkdownMsgDelay(alertRecord, AlertRoute.JISHU.value, AlertJobDelayModel.IOS_AppStore_Check_NO, temp.stream().map(IosCheckUrl::getProduct).collect(Collectors.toList()));
                    VedioAlertService.sendVocMsg(AlertJobDelayModel.IOS_AppStore_Check_NO.getJobName(), az, phones);
                }
            }
//            暂不开启评分告警
//            if(CollectionUtils.isNotEmpty(scoreAppList)){
//                for (int i = 0; i < scoreAppList.size(); i += 20) {
//                    List<IosCheckUrl> temp = scoreAppList.subList(i, i + 20 < scoreAppList.size() ? i + 20 : scoreAppList.size());
//                    String az = temp.stream().map(IosCheckUrl::getProductName).collect(Collectors.joining(",")) + "应用商店无评分";
//                    AlertRecord alertRecord = alertRecordService.insertAlertRecord("IOS-AppStore-Check-Score", "IOS-AppStore-Check-Score", AlertModel.iosAppStoreScore,
//                            az, AlertStatus.INIT, DingTailService.dset, AlertType.DINGDING);
//                    DingTailService.sendMarkdownMsgDelay(alertRecord, AlertRoute.SJWT.value, AlertJobDelayModel.IOS_AppStore_Check_SCORE, temp.stream().map(IosCheckUrl::getProduct).collect(Collectors.toList()));
//                }
//            }
        }
    }

    private void checkOne(IosCheckUrl iosCheckUrl, List<IosCheckUrl> downAppList, List<IosCheckUrl> noAppList, List<IosCheckUrl> scoreAppList) {
        Res res = requestAppStore(iosCheckUrl.getAppStoreUrl());

        if (res == null  || res.getFlag() == null || !res.getFlag()){
            downAppList.add(iosCheckUrl);
        }
        else if(iosCheckUrl.getNoStatus() == null || iosCheckUrl.getNoStatus() != 2){
            boolean alreadyAlerted = false;

            if(StringUtils.isNotBlank(iosCheckUrl.getEmail())
                && !StringUtils.startsWith(iosCheckUrl.getEmail(), "nocheckEmail---")
                && StringUtils.isNotBlank(iosCheckUrl.getAuthCode())
            ){
                alreadyAlerted = iosEmailService.resceiveTry(iosCheckUrl.getEmail(), iosCheckUrl.getAuthCode(), iosCheckUrl);
            }else{
                log.warn("iosEmailService.resceive 信息缺失 {}", iosCheckUrl);
            }

            if(!alreadyAlerted){
                checkHtmlStatus(iosCheckUrl, noAppList, scoreAppList, res);
            }
        }
        iosCheckRecordService.save(iosCheckUrl, res);
    }

    private void checkHtmlStatus(IosCheckUrl iosCheckUrl, List<IosCheckUrl> noAppList, List<IosCheckUrl> scoreAppList, Res res) {
        IosCheckRecord topOne = iosCheckRecordService.getTopOne(iosCheckUrl);
        IosCheckRecord healthCount = iosCheckRecordService.countHealth(iosCheckUrl);
        if(healthCount.getNo() > 0
                && topOne.getNo() == null
                && res.getNo() == null){
//                if(!check7maiExistsNo(iosCheckUrl)){
                noAppList.add(iosCheckUrl);
//                }
        }
        if(healthCount.getScore() > 0
//                    && topOne.getScore() != null
                && res.getScore() == null
        ){
            scoreAppList.add(iosCheckUrl);
        }
    }

    private static boolean check7maiExistsNo(IosCheckUrl iosCheckUrl) {
        CloseableHttpResponse response = null;
        CloseableHttpClient httpClient = null;
        String url = "https://api.qimai.cn/app/appinfo?analysis=eA8zECY8Gkp4dWYbKQx3QyVFSAkICVVUQUcKCgdWdkZQVlBOTUoFAgJRUSEaBQ%3D%3D&country=cn&appid=";
        try {
            String[] ids = iosCheckUrl.getAppStoreUrl().split("id");
            httpClient = HttpClients.createDefault();
            HttpGet httpGet = new HttpGet(url + ids[ids.length - 1]);
            response = httpClient.execute(httpGet);
            int statusCode = response.getStatusLine().getStatusCode();
            if (statusCode == 200){
                log.info("check7maiExistsNo 调用成功 {} ", url + ids[ids.length - 1]);
                String tempR = EntityUtils.toString(response.getEntity(), StandardCharsets.UTF_8);
                JSONObject jsonObject = JSON.parseObject(tempR);
                Integer rank = jsonObject.getObject("appInfo", JSONObject.class)
                        .getObject("appRealTimeRanks", JSONObject.class)
                        .getObject("rank", JSONObject.class)
                        .getInteger("ranking");
                return rank != null;
            }
        }catch (Exception e){
            log.warn("Request Er:",e);
        }finally {
            try {
                response.close();
            } catch (IOException e) {
                log.error(" response.close ", e);
            }
            try {
                httpClient.close();
            } catch (IOException e) {
                log.error(" httpClient.close ", e);
            }
        }
        return false;
    }

    @Data
    public static class Res{
        private Boolean flag;
        private Integer no;
        private Double score;
    }
    private static Res requestAppStore(String url){
        Res res = new Res();
        res.setFlag(true);
        CloseableHttpResponse newResponse = null;
        CloseableHttpResponse response = null;
        CloseableHttpClient httpClient = null;
        try {
            httpClient = HttpClients.createDefault();
            HttpGet httpGet = new HttpGet(url);
            response = httpClient.execute(httpGet);
            int statusCode = response.getStatusLine().getStatusCode();

            if (statusCode == 200){
                log.info("url {} 正常在线", url);
                res.setFlag(true);
                handlerNoAndScore(response, res);
            }else if (statusCode == 301) {
                // 获取新的地址
                String newUrl = response.getFirstHeader("Location").getValue();
                // 使用新的地址重新发起请求
                HttpGet newHttpGet = new HttpGet(newUrl);
                newResponse = httpClient.execute(newHttpGet);
                // 处理新请求的响应...
                statusCode = newResponse.getStatusLine().getStatusCode();

                if (statusCode == 200){
                    log.info("url {} 正常在线", url);
                    res.setFlag(true);
                    handlerNoAndScore(newResponse, res);
                }else if (statusCode == 404){
                    // 处理正常的响应...
                    log.info("url {} 404 已被下架", url);
                    Thread.sleep(1 * 1000);
                    newResponse = httpClient.execute(newHttpGet);
                    // 处理新请求的响应...
                    statusCode = newResponse.getStatusLine().getStatusCode();
                    if(statusCode == 404){
                        log.info("url {} 404*2 已被下架",url);
                        res.setFlag(false);
                    }else{
                        log.info("url {} 正常在线", url);
                        handlerNoAndScore(response, res);
                    }
                }
            } else if (statusCode == 404){
                // 处理正常的响应...
                log.info("url {} 404 已被下架",url);
                Thread.sleep(1 * 1000);
                response = httpClient.execute(httpGet);
                statusCode = response.getStatusLine().getStatusCode();
                if(statusCode == 404){
                    log.info("url {} 404*2 已被下架",url);
                    res.setFlag(false);
                }else{
                    log.info("url {} 正常在线", url);
                    handlerNoAndScore(response, res);
                }
            }

        }catch (Exception e){
            log.error("Request Er:",e);
        }finally {
            if(newResponse != null){
                try {
                    newResponse.close();
                } catch (IOException e) {
                    log.error(" newResponse.close ", e);
                }
            }
            try {
                response.close();
            } catch (IOException e) {
                log.error(" response.close ", e);
            }
            try {
                httpClient.close();
            } catch (IOException e) {
                log.error(" httpClient.close ", e);
            }
        }
        return res;
    }

    private static void handlerNoAndScore(CloseableHttpResponse response, Res res){
        try{
            String tempR = EntityUtils.toString(response.getEntity(), StandardCharsets.UTF_8).split("product-header__list app-header__list")[1];
            String[] noR = tempR.split("类第 ");
            if(noR.length > 1){
                String no = noR[1].split(" 名")[0];
                res.setNo(Integer.valueOf(no));
            }
            String[] scoreR = tempR.split("<figure class=\"we-star-rating\" aria-label=\"");
            if(scoreR.length > 1){
                String score = scoreR[1].split("（满分 5 分）\">")[0];
                if(score.contains("1 out")){
                    res.setScore(1.0);
                } else if(score.contains("2 out")){
                    res.setScore(2.0);
                } else if(score.contains("3 out")){
                    res.setScore(3.0);
                } else if(score.contains("4 out")){
                    res.setScore(4.0);
                } else if(score.contains("5 out")){
                    res.setScore(5.0);
                }else{
                    res.setScore(Double.valueOf(score));
                }
            }
        }catch (Exception e){
            log.error("handlerNoAndScore ", e);
        }
    }

    public static void main(String[] args) {
//        Res res = requestAppStore("https://apps.apple.com/cn/app/%E6%B1%BD%E8%BD%A6%E4%B8%8D%E8%83%BD%E5%81%9C-%E5%87%BB%E4%BA%86%E4%B8%AA%E8%BD%A6/id6544783946");
//        System.out.println(res);
//        Res res = requestAppStore("https://apps.apple.com/cn/app/%E7%BF%A1%E7%BF%A0%E5%A4%A7%E7%8E%8B/id6505062130");
//        Res res = requestAppStore("https://apps.apple.com/cn/app/%E9%87%91%E5%B8%81%E5%A4%A7%E5%B8%88-%E7%A6%8F%E7%A6%84%E5%AF%BF/id6503031360");
//        Res res = requestAppStore("https://apps.apple.com/cn/app/id6529534320");
//        IosCheckRecord iosCheckRecord = new IosCheckRecord();
//        iosCheckRecord.setAppStoreUrl("https://apps.apple.com/cn/app/%E6%B1%BD%E8%BD%A6%E4%B8%8D%E8%83%BD%E5%81%9C-%E5%87%BB%E4%BA%86%E4%B8%AA%E8%BD%A6/id6504740412");
//        System.out.println(check7maiExistsNo(iosCheckRecord));
    }
}
