package com.shinet.core.alert.clickhouse.service;

import com.shinet.core.alert.clickhouse.entity.ProductEntity;
import com.shinet.core.alert.clickhouse.mapper.ck1.ClickHouseDwdMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2021/9/25
 */
@Slf4j
@Service
public class AppConfig {

    @Resource
    private ClickHouseDwdMapper clickHouseDwdMapper;

    public static Map<String, ProductEntity> productEnMap = new HashMap<>();
    public static Map<Long, ProductEntity> appIdMap = new HashMap<>();

    @PostConstruct
    @Scheduled(cron = "0 0 * * * ?")
    public void refreshProductMap(){
        log.info("START REFRESH PRODUCT MAP...");
        List<ProductEntity> productEntityList = clickHouseDwdMapper.queryProductMap();
        productEnMap = productEntityList.stream().collect(Collectors.toMap(ProductEntity::getProduct,l->l,(l1,l2)-> l2));
        appIdMap = productEntityList.stream().collect(Collectors.toMap(p -> Long.valueOf(p.getId()),l->l,(l1,l2)->l2));
        log.info("COMPLETE REFRESH PRODUCT MAP...");
    }
}
