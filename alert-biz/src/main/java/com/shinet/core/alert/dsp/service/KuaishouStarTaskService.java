package com.shinet.core.alert.dsp.service;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.shinet.core.alert.dsp.entity.KuaishouStarTask;
import com.shinet.core.alert.dsp.mapper.KuaishouStarTaskMapper;
import org.springframework.stereotype.Service;

import java.math.BigInteger;
import java.util.List;
import java.util.Set;

@Service
public class KuaishouStarTaskService extends ServiceImpl<KuaishouStarTaskMapper, KuaishouStarTask> {

    public List<KuaishouStarTask> queryByAdertiserId(String advertiserId) {
        QueryWrapper<KuaishouStarTask> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("advertiser_id", advertiserId);
        queryWrapper.isNotNull("app_name");
        return list(queryWrapper);
    }

    public List<KuaishouStarTask> queryByAdertiserIdAndCreatetime(String advertiserId, String preCreateTime, String afterCreateTime, Set<String> appNameSet) {
        QueryWrapper<KuaishouStarTask> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("advertiser_id", advertiserId);
        queryWrapper.in("app_name", appNameSet);
        queryWrapper.gt("create_time", preCreateTime);
        queryWrapper.lt("create_time", afterCreateTime);
        return list(queryWrapper);
    }
}
