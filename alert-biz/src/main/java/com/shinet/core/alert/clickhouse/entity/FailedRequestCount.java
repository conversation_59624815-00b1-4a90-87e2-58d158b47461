package com.shinet.core.alert.clickhouse.entity;

import lombok.Data;

/**
 * <AUTHOR>
 * @since 2022/12/26
 */
@Data
public class FailedRequestCount {
    private String logday;
    private String product;
    private String productName;
    private String os;
    private Integer failCount;
    private Integer failUv;
    private Integer sucCount;
    private Integer allCount;
    private Integer dau;
    private Integer lockedDau;
    private Integer lockFailUserids;
    private Double rate;
    public String getCeilP(Double p){
        return Math.ceil(p) +"%";
    }
}
