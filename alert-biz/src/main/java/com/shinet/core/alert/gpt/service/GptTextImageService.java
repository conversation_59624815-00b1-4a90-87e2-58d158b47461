package com.shinet.core.alert.gpt.service;

import com.baomidou.mybatisplus.extension.service.additional.query.impl.LambdaQueryChainWrapper;
import com.shinet.core.alert.coreservice.entity.AlertMall;
import com.shinet.core.alert.coreservice.entity.AlertRecord;
import com.shinet.core.alert.coreservice.enums.AlertModel;
import com.shinet.core.alert.coreservice.enums.AlertStatus;
import com.shinet.core.alert.coreservice.enums.AlertType;
import com.shinet.core.alert.coreservice.service.AlertRecordService;
import com.shinet.core.alert.dingtalk.DingTailService;
import com.shinet.core.alert.dingtalk.VedioAlertService;
import com.shinet.core.alert.gpt.entity.GptTextImage;
import com.shinet.core.alert.gpt.mapper.GptTextImageMapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.shinet.core.alert.util.DateUtils;
import com.xxl.job.core.log.XxlJobLogger;
import org.joda.time.DateTimeConstants;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.Set;
import java.util.stream.Collectors;

/**
* <p>
    *  服务实现类
    * </p>
*
* <AUTHOR>
* @since 2023-04-23
*/
@Service
public class GptTextImageService extends ServiceImpl<GptTextImageMapper, GptTextImage> {
    @Autowired
    AlertRecordService alertRecordService;

    public void alertGptImgsCost(String jobName,int mins,int maxInitNum,int maxFalNum){
        long sucNum = getCnum(mins,0,15,2);
        long longNum = getCnum(mins,15,30,2);
        long toLgNum = getCnum(mins,31,1000,2);

        XxlJobLogger.log("15秒-> "+sucNum+",30秒-> "+longNum+" 31秒->"+toLgNum);

        if((sucNum+longNum+toLgNum)<30){
            if((longNum+toLgNum)>10){
                String alertMsg = "图片生成时间过长 15秒-> "+sucNum+",30秒-> "+longNum+" 31秒->"+toLgNum;
                AlertRecord alertRecord = alertRecordService.insertAlertRecord(jobName,"图片生成时间过长", AlertModel.SDIMG,alertMsg,0d,
                        0d,0,0, AlertStatus.INIT, DingTailService.dset, AlertType.DINGPHONE);
                DingTailService.sendMarkdownMsg(alertRecord);
                XxlJobLogger.log(alertMsg);
            }
        }else{
            float sRate = toLgNum/((sucNum+longNum+toLgNum)*1.0f) *100;
            if(sRate>10){
                String alertMsg = "图片生成时间过长 15秒-> "+sucNum+",30秒-> "+longNum+" 31秒->"+toLgNum +" 超长率为:"+sRate+"% ";
                AlertRecord alertRecord = alertRecordService.insertAlertRecord(jobName,"图片生成时间过长", AlertModel.SDIMG,alertMsg,0d,
                        0d,0,0, AlertStatus.INIT, DingTailService.dset, AlertType.DINGPHONE);
                DingTailService.sendMarkdownMsg(alertRecord);
                XxlJobLogger.log(alertMsg);
            }
        }

        long jiayNum = getJiyaCnum(mins);
        if(jiayNum>maxInitNum){
            String alertMsg = "图片生成积压过多 "+jiayNum+" 15秒-> "+sucNum+",30秒-> "+longNum+" 31秒->"+toLgNum;
            AlertRecord alertRecord = alertRecordService.insertAlertRecord(jobName,"图片生成积压过多", AlertModel.SDIMG,alertMsg,0d,
                    0d,0,0, AlertStatus.INIT, DingTailService.dset, AlertType.DINGPHONE);
            DingTailService.sendMarkdownMsg(alertRecord);
            XxlJobLogger.log(alertMsg);
        }

        long falNum = getFailaCnum(mins);
        if(falNum>maxFalNum){
            String alertMsg = "图片生成失败过多 "+falNum+" 15秒-> "+sucNum+",30秒-> "+longNum+" 31秒->"+toLgNum;
            AlertRecord alertRecord = alertRecordService.insertAlertRecord(jobName,"图片生成失败过多", AlertModel.SDIMG,alertMsg,0d,
                    0d,0,0, AlertStatus.INIT, DingTailService.dset, AlertType.DINGPHONE);
            DingTailService.sendMarkdownMsg(alertRecord);
            XxlJobLogger.log(alertMsg);
        }

        XxlJobLogger.log("积压："+jiayNum+"失败："+falNum+" 15秒-> "+sucNum+",30秒-> "+longNum+" 31秒->"+toLgNum);
    }

    public long getCnum(int mins,int minCostTm,int maxCostTm,int imgStatus){
        Date date = new Date(System.currentTimeMillis() - mins* DateTimeConstants.MILLIS_PER_MINUTE);
        LambdaQueryChainWrapper<GptTextImage> lq = this.lambdaQuery();
        lq.ge(GptTextImage::getCostTime,minCostTm*1000).lt(GptTextImage::getCostTime,maxCostTm*1000).eq(GptTextImage::getImgStatus,imgStatus).gt(GptTextImage::getCreateTime,date);
        long successNumLe15 = lq.count();
        return successNumLe15;
    }


    public long getJiyaCnum(int mins){
        Date date = new Date(System.currentTimeMillis() - mins* DateTimeConstants.MILLIS_PER_MINUTE);
        LambdaQueryChainWrapper<GptTextImage> lq = this.lambdaQuery();
        lq.eq(GptTextImage::getImgStatus,0).gt(GptTextImage::getCreateTime,date);
        long successNumLe15 = lq.count();
        return successNumLe15;
    }

    public long getFailaCnum(int mins){
        Date date = new Date(System.currentTimeMillis() - mins* DateTimeConstants.MILLIS_PER_MINUTE);
        LambdaQueryChainWrapper<GptTextImage> lq = this.lambdaQuery();
        lq.lt(GptTextImage::getImgStatus,0).gt(GptTextImage::getCreateTime,date);
        long successNumLe15 = lq.count();
        return successNumLe15;
    }
}
