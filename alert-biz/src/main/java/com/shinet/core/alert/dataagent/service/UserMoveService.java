package com.shinet.core.alert.dataagent.service;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.additional.query.impl.LambdaQueryChainWrapper;
import com.shinet.core.alert.bpcommon.entity.User;
import com.shinet.core.alert.bpcommon.entity.UserMeta;
import com.shinet.core.alert.bpcommon.entity.WechatOpenId;
import com.shinet.core.alert.bpcommon.mapper.UserMetaMapper;
import com.shinet.core.alert.bpcommon.mapper.WechatOpenIdMapper;
import com.shinet.core.alert.bpcommon.service.UserMetaService;
import com.shinet.core.alert.bpcommon.service.UserService;
import com.shinet.core.alert.bpcommon.service.WechatOpenIdService;
import com.shinet.core.alert.dataagent.entity.UserDel;
import com.shinet.core.alert.dataagent.entity.UserMetaDel;
import com.shinet.core.alert.dataagent.entity.WechatOpenIdDel;
import com.shinet.core.alert.dataagent.mapper.UserDelMapper;
import com.shinet.core.alert.dataagent.mapper.UserMetaDelMapper;
import com.shinet.core.alert.util.DateUtils;
import com.xxl.job.core.log.XxlJobLogger;
import lombok.extern.slf4j.Slf4j;
import org.joda.time.DateTimeConstants;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.sql.Wrapper;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.function.Function;
import java.util.stream.Collectors;

@Component
@Slf4j
public class UserMoveService {
    @Autowired
    UserMetaDelService userMetaDelService;
    @Autowired
    UserMetaService userMetaService;

    @Autowired
    UserService userService;
    @Autowired
    UserDelService userDelService;
    @Autowired
    WechatOpenIdDelService wechatOpenIdDelService;
    @Autowired
    WechatOpenIdService wechatOpenIdService;
    @Autowired
    UserMetaMapper userMetaMapper;
    @Autowired
    WechatOpenIdMapper wechatOpenIdMapper;

    @Autowired
    UserMetaDelMapper userMetaDelMapper;
    @Autowired
    UserDelMapper userDelMapper;

    @Transactional
    public void moveUserMetas(List<Long> userIdSet, int pkgId, AtomicInteger delNum, boolean isDelStart, int allNum) {
        if (userIdSet.size() > 0 && pkgId > 0) {
        } else {
            return;
        }

        int removeMetaNum = userMetaService.removeAndSaveUserMeta(userIdSet, pkgId, isDelStart);
        int removeUserNum = userService.removeAndSaveUser(userIdSet, pkgId, isDelStart);
        int removeOpenIdNum = wechatOpenIdService.removeAndSaveOpenId(userIdSet, pkgId, isDelStart);
        delNum.addAndGet(userIdSet.size());
        String msg = "总" + allNum + " 剩余" + (allNum - delNum.get()) + " 输入" + userIdSet.size() + " 迁移 metaNum:" + removeMetaNum + " removeUserNum:" + removeUserNum + "removeOpenIdNum:" + removeOpenIdNum;
        log.info(msg);
        XxlJobLogger.log(msg);
    }


    @Transactional
    public void moveUserTos(List<Long> userIdSetF) {
        if (userIdSetF.size() > 0) {
        } else {
            return;
        }
        int page = 1000;
        for(int i=0;i<=userIdSetF.size()/page;i++){
            int start = i*page;
            int end = (i+1)*page>userIdSetF.size()?userIdSetF.size():(i+1)*page;
            List<Long> duidList=  userIdSetF.subList(start,end);

            QueryWrapper<UserDel> userDelQueryWrapper = new QueryWrapper<UserDel>();
            userDelQueryWrapper.lambda().in(UserDel::getId, duidList);
            List<UserDel> dlist = userDelService.list(userDelQueryWrapper);

            List<User> userList = dlist.stream().map(userDel -> {
                User user = new User();
                BeanUtils.copyProperties(userDel, user);
                return user;
            }).collect(Collectors.toList());

            QueryWrapper<User> userQueryWrapper = new QueryWrapper<User>();
            userQueryWrapper.lambda().in(User::getId, duidList);
            List<User> dbUserList = userService.list(userQueryWrapper);

            int size = userList.size();
            if (dbUserList.size() == 0) {
                userList.forEach(tosaveUser->{
                    try {
                        userService.save(tosaveUser);
                        log.info("保存成功");
                    }catch (Exception e){
                        log.error("",e);
                    }
                });
            } else {
                Map<Long, User> dmap = dbUserList.stream().collect(Collectors.toMap(User::getId, Function.identity(), (t1, t2) -> t1));
                List<User> toSaveUserList = userList.stream().filter(user -> dmap.get(user.getId()) == null).collect(Collectors.toList());
                if (toSaveUserList.size() > 0) {
                    toSaveUserList.forEach(tosaveUser -> {
                        try {
                            userService.save(tosaveUser);
                            log.info("保存成功");
                        }catch (Exception e){
                            log.error("",e);
                        }
                    });
                }
            }
        }

        String msg = "总迁移 user:" ;
        log.info(msg);
        XxlJobLogger.log(msg);
    }


    @Transactional
    public void moveUserMetasOpenIds(List<Long> userIdSet, int pkgId, AtomicInteger delNum, boolean isDelStart, int allNum) {
        if (userIdSet.size() > 0 && pkgId > 0) {
        } else {
            return;
        }

        int removeMetaNum = userMetaService.removeAndSaveUserMeta(userIdSet, pkgId, isDelStart);
        int removeOpenIdNum = wechatOpenIdService.removeAndSaveOpenId(userIdSet, pkgId, isDelStart);
        delNum.addAndGet(userIdSet.size());
        String msg = "总" + allNum + " 剩余" + (allNum - delNum.get()) + " 输入" + userIdSet.size() + " 迁移 metaNum:" + removeMetaNum + " removeOpenIdNum:" + removeOpenIdNum;
        log.info(msg);
        XxlJobLogger.log(msg);
    }


    @Transactional
    public void moveUserMeta(Long userId, int pkgId, AtomicInteger delNum, boolean isDelStart, int allNum) {
        if (userId > 0 && pkgId > 0) {
        } else {
            return;
        }
        UserMeta userMeta = userMetaMapper.findById(pkgId, userId);
        int dnum = delNum.get();
        if (userMeta == null) {
            if (dnum % 99 == 1) {
                log.info("迁移 第" + dnum + "条 剩余 " + (allNum - dnum) + " 已经迁移");
            }
        }
        if (userMeta != null) {
            long ctime = userMeta.getCreateTime();
            Date userMetaCreateTime = new Date(ctime);
            if (ctime < (System.currentTimeMillis() - DateTimeConstants.MILLIS_PER_DAY * 60)) {
                UserMetaDel userMetaDel = new UserMetaDel();
                BeanUtils.copyProperties(userMeta, userMetaDel);
                int cnum = userMetaDelService.lambdaQuery().eq(UserMetaDel::getPkgId, userMeta.getPkgId()).eq(UserMetaDel::getUserId, userId).count();
                if (cnum == 0) {
                    userMetaDelMapper.insertUserMeta(userMetaDel);
                }
                if (isDelStart) {
                    userMetaMapper.deleteById(pkgId, userId);
                }
                User user = userService.getById(userMeta.getUserId());
                if (user != null) {
                    UserDel userDel = new UserDel();
                    if (userDelService.getById(user.getId()) == null) {
                        BeanUtils.copyProperties(user, userDel);
                        userDelMapper.insertUser(userDel);
                    }
                    if (isDelStart) {
                        userService.removeById(user.getId());
                    }
                }


                WechatOpenId wechatOpenId = wechatOpenIdMapper.findByUserIdAndWechatId(userId, pkgId);
                if (wechatOpenId != null) {
                    WechatOpenIdDel wechatOpenIdDel = new WechatOpenIdDel();
                    BeanUtils.copyProperties(wechatOpenId, wechatOpenIdDel);
                    long delCount = wechatOpenIdDelService.lambdaQuery().eq(WechatOpenIdDel::getUserId, userId).eq(WechatOpenIdDel::getWechatId, pkgId).count();
                    if (delCount == 0) {
                        wechatOpenIdDelService.save(wechatOpenIdDel);
                    }
                    if (isDelStart) {
                        wechatOpenIdMapper.deleteByUserIdAndWechatId(userId, pkgId);
                    }
                    if (dnum % 99 == 1) {
                        String msg = "迁移 第" + dnum + "条 剩余 " + (allNum - dnum) + "@" + userMeta.getUserId() + " " + userMeta.getChannel() + " 创建时间为 " + DateUtils.formatDate(userMetaCreateTime) + " 完成";
                        log.info(msg);
                        XxlJobLogger.log(msg);
                    }
                }
            }
        }
    }

}
