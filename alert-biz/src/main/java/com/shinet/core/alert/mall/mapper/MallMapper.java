package com.shinet.core.alert.mall.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.shinet.core.alert.coreservice.entity.AlertMall;
import com.shinet.core.alert.data.entity.AppMallDataCheck;
import com.shinet.core.alert.data.entity.MallSucCheck;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

public interface MallMapper extends BaseMapper<AppMallDataCheck> {

    @Select(" select app_name,LEFT(create_time,10) as create_time,wechat_message,count(1) as cnum from bp_mall.wechat_message  " +
            " where create_time>'${startDate}' and create_time<'${endDate}' " +
            " and wechat_message not like '%非实名用户账号不可发放%' " +
            " and wechat_message not like '%存在风险%' " +
            " and wechat_message not like '%系统繁忙%' " +
            " and wechat_message not like '%对方账户状态异常%' " +
            " and wechat_message not like '%当前用户账户暂时无法完成交易%' " +
            " and wechat_message not like '%该用户今日付款次数超过限制%' " +
            " and wechat_message not like '%EXCEED_PAYEE_ACCOUNT_LIMIT%' " +
            " GROUP BY LEFT(create_time,10),wechat_message,app_name ORDER BY cnum desc;")
    List<AppMallDataCheck> queryMallError(@Param("startDate") String startDate, @Param("endDate") String endDate);



    @Select("select  count(1) as suc_num,order_status from bp_mall.biz_order_union_${tabend} " +
            "where order_create_time>'${startDate}' and order_create_time<'${endDate}'  GROUP BY order_status")
    List<MallSucCheck> queryMallSuc(@Param("startDate") String startDate, @Param("endDate") String endDate, @Param("tabend") String tabend);



    @Select("select  LEFT(create_time,13) as logday,sum(if(order_status=5,1,0)) as success_num,sum(if(order_status=1,1,0)) as failed_num, " +
            "    ROUND(sum(if(order_status=1,1,0))/sum(if(order_status=5,1,0))  *100,2) as fail_rate " +
            "    from bp_mall.biz_order_union_${tabend} GROUP BY LEFT(create_time,13)")
    List<AlertMall> queryAlertMallSuc(@Param("tabend") String tabend);
}
