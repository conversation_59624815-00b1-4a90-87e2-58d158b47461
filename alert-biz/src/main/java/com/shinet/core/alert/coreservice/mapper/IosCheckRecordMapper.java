package com.shinet.core.alert.coreservice.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.shinet.core.alert.coreservice.entity.AlertRecord;
import com.shinet.core.alert.coreservice.entity.IosCheckRecord;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

public interface IosCheckRecordMapper extends BaseMapper<IosCheckRecord> {
    @Select("select * from ios_check_record where product = #{product} order by id desc limit 1")
    IosCheckRecord getTopOne(@Param("product") String product);

    @Select("select count(no) no,count(score) score from ios_check_record where product = #{product} and (no is not null or score is not null) order by id desc")
    IosCheckRecord countHealth(@Param("product") String product);
}
