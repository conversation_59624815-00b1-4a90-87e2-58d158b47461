package com.shinet.core.alert.clickhouse.mapper.ck1;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.shinet.core.alert.clickhouse.entity.AdBidRate;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

public interface AdBidRateMapper  extends BaseMapper<AdBidRate> {
    @Select("       select  product_name,os,sum(if(pos_name like '%bid%',income,0)) as bidincom,sum(if(pos_name like '%bid%',pv,0)) as bidpv,                       " +
            "       sum(if(pos_name not like '%bid%',income,0)) as flowincom,sum(if(pos_name not like '%bid%',0,pv)) as flowpv,                                     " +
            "       sum(income) as allincom,sum(pv) as allpv,(bidincom/allincom)*100 as bidRate                                                                     " +
            "       from old_mysql_ads.realtime_ad_income_warning where logday='${logday}'  group by  product_name,os having allincom>1000 order by allincom desc;  ")
    List<AdBidRate> queryAdArpu(@Param("logday") String logday);

}
