package com.shinet.core.alert.dsp.mapper;

import com.shinet.core.alert.dsp.entity.DspCostAlertBean;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * <AUTHOR>
 * @createTime 2023/10/31 11:48
 * @description
 */
public interface DspCostAlertMapper {


    /**
     * 【平台穿优百快 账户消耗超过10w 预警账户id
     * 如果聚星星任务 星任务消耗超过5w 预警广告主id 任务id
     * 如果是星图 单个任务消耗超过2w 预警任务id
     * 如果是助推 单个助推订单超过0.5w 预警广告主id 任务id orderid 助推id】
     */
    @Select({" select dsp,appName,advertiserId,cost from (\n" +
            "select 'guangdiantong' as dsp ,app_name as appName ,advertiser_ID as advertiserId,sum(cost/100) cost from tencent_report_data_main where date=CURRENT_DATE GROUP BY app_name,advertiser_ID having cost > 100000 " +
            "union all " +
            "select 'kuaishou',product_name ,advertiser_ID,sum(charge) cost from kuaishou_creative_video_data where stat_date=CURRENT_DATE GROUP BY product_name,advertiser_ID having cost > 100000 " +
            "union all " +
            "select 'toutiao',app_name ,advertiser_ID,sum(cost) cost from toutiao_report_plan_video where data_date=CURRENT_DATE GROUP BY app_name,advertiser_ID having cost > 100000 " +
            "union all " +
            "select 'baidufeed',product_name ,advertiser_id,sum(cost) cost from baidu_report_plan_video where data_date=CURRENT_DATE GROUP BY product_name,advertiser_ID having cost > 100000 " +
            ") a "})
    public List<DspCostAlertBean> getDspDaliyCost();


    /**
     * 今天星任务5w
     * @return
     */
    @Select({" select a.advertiser_id advertiserId,a.task_id taskId,a.cost,b.app_name as appName from (\n" +
            "            select advertiser_id,task_id,sum(cost/1000) cost from kuaishou_star_order_daily where crawl_date = CURRENT_DATE AND COST >0 group by advertiser_id,task_id HAVING cost > 50000\n" +
            "            )  a   left join kuaishou_star_task b on b.task_id = a.task_id\n"})
    public List<DspCostAlertBean> getDaRenTaskDaliyCost();

    /**
     * 单助推10000
     * @return
     */
    @Select({"select b.app_name as appName,a.advertiser_id as advertiserId,a.task_id as taskId, a.order_id as orderId,a.supplement_order_id as supplementOrderId, a.cost  from ( " +
            "            select advertiser_id,task_id,order_id,supplement_order_id,sum(consume_amount/1000) cost from kuaishou_supplement_order_realtime where logday = CURRENT_DATE  group by advertiser_id,task_id,order_id,supplement_order_id having cost > 10000 )  a left join kuaishou_star_task b on a.task_id = b.task_id"})
    public List<DspCostAlertBean> getDaRenZTDaliyCost();


    /**
     * 出价大于50预警
     * @return
     */
    @Select({" select 'toutiao' as dsp, b.product_name as appName, a.advertiser_id as advertiserId,promotion_id as adId,cpa_bid as cpaBid from toutiao_report_promotion a left join auth_toutiao_advertiser b on a.advertiser_id = b.advertiser_id where stat_datetime = CURRENT_DATE and opt_status = 'ENABLE' and status ='PROMOTION_STATUS_ENABLE' and cpa_bid > 50 " +
            "            union all  " +
            "            select'kuaishou',b.product_name,a.advertiser_id,unit_id,cpa_bid/1000 from kuaishou_ad_group a left join (select a.advertiser_id ,b.product_name from auth_kuaishou_app a left join auth_kuaishou_advertiser b on a.id = b.customer_id and a.del_flag = 0) b on a.advertiser_id = b.advertiser_id  " +
            "            where data_date = CURRENT_DATE  and cpa_bid >50000 " +
            "            union all  " +
            "            select 'gungdiantong',b.app_name as appName, a.advertiser_id,adgroup_id,bid_amount/100 from tencent_ad_group a left join tencent_developer b on a.advertiser_id = b.advertiser_id where data_date = CURRENT_DATE and `status` in ('STATUS_ACTIVE','STATUS_PART_ACTIVE','STATUS_READY','STATUS_PART_READY')and bid_amount > 5000 " +
            "union all " +
            "select  'baidufeed',b.product_name as appName, a.advertiser_id,a.ad_group_id,ocpc_bid from baidu_ad_group_data a left join baidu_advertiser b on a.advertiser_id = b.advertiser_id where data_date =CURRENT_DATE AND ocpc_bid > 50"})
    public List<DspCostAlertBean> getAdvertiserCpaBid();
    @Select({" select  b.app_name as appName,a.advertiser_id as advertiserId,a.task_id as taskId, a.order_id as orderId,a.supplement_order_id as supplementOrderId, a.unit_price/1000 as cpaBid  from kuaishou_star_supplement_order a LEFT JOIN kuaishou_star_task b on a.task_id = b. task_id where a.crawl_date =CURRENT_DATE AND unit_price >30000"})
    public List<DspCostAlertBean> getAdvertiserDarenCpaBid();


    /**
     * 星任务出价大于35 预警
     * @return
     */
    @Select({" SELECT '星任务' as dsp,b.app_name as appName,a.advertiser_id advertiserId,a.task_id taskId,a.cost,a.bid as cpaBid FROM ( " +
            "            select advertiser_id,task_id,sum(cost/1000) cost ,sum(conversion_num) num ,sum(cost/1000)/sum(conversion_num) bid " +
            "    from kuaishou_star_order_daily where crawl_date =CURRENT_DATE  and cost >0 " +
            "    group by advertiser_id,task_id " +
            "    HAVING bid >35 " +
            "            )a left join " +
            "    kuaishou_star_task b on a.task_id = b.task_id "})
    public List<DspCostAlertBean> getDaRenStarOrderCpaBid();


    @Select({"select distinct 'toutiao' as dsp, b.product_name as appName, a.advertiser_id as advertiserId,promotion_name as optimization from toutiao_report_promotion  a left join auth_toutiao_advertiser b on a.advertiser_id = b.advertiser_id where stat_datetime =CURRENT_DATE and opt_status = 'ENABLE' and  promotion_name not like '12%' and promotion_name not like '13%' and promotion_name not like '行为%'  and promotion_name not like '图片%'    and (promotion_name  like '%WMIN%' and promotion_name not like'付费%' ) \n" +
            " union all  \n" +
            " select distinct 'kuaishou',b.product_name,a.advertiser_id,ocpx_action_type from kuaishou_ad_group a left join (select a.advertiser_id ,b.product_name from auth_kuaishou_app a left join auth_kuaishou_advertiser b on a.id = b.customer_id and a.del_flag = 0) b on a.advertiser_id = b.advertiser_id \n" +
            " where data_date = CURRENT_DATE  and  ocpx_action_type !=773 and ocpx_action_type !=180 \n" +
            " union all \n" +
            " select distinct 'gungdiantong',b.app_name as appName, a.advertiser_id,optimization_goal from tencent_ad_group a left join tencent_developer b on a.advertiser_id = b.advertiser_id where data_date = CURRENT_DATE and `status` in ('STATUS_ACTIVE','STATUS_READY')and optimization_goal not in ('OPTIMIZATIONGOAL_CORE_ACTION','OPTIMIZATIONGOAL_APP_REGISTER','OPTIMIZATIONGOAL_APP_ACTIVATE') \n" +
            " AND EXISTS (  \n" +
            "    select 1 from tencent_report_data_main_creative where date =CURRENT_DATE AND PLAN_ID  = a.adgroup_id AND COST > 500 \n" +
            ")\n" +
            " union all \n" +
            "select  distinct 'baidufeed',b.product_name as appName, a.advertiser_id,trans_type from baidu_ad_group_data a left join baidu_advertiser b on a.advertiser_id = b.advertiser_id where data_date =CURRENT_DATE AND trans_type not in (4,119) "})
    public List<DspCostAlertBean> getAdvertiserOptimization();
    @Select({"  select  b.app_name as appName,a.advertiser_id as advertiserId,a.task_id as taskId, a.order_id as orderId,a.supplement_order_id as supplementOrderId, unit_type as optimization  from kuaishou_star_supplement_order a LEFT JOIN kuaishou_star_task b on a.task_id = b. task_id where a.crawl_date =CURRENT_DATE AND unit_type !=104"})
    public List<DspCostAlertBean> getAdvertiserDarenOptimization();




    /** 助推单产品消耗统计 */
    @Select("          select b.app_name as appName ,b.team_name as teamName,sum(cost) as cost  from  " +
            "          (select TASK_ID,sum(consume_amount)/1000 as cost  from kuaishou_supplement_order_realtime where logday = CURRENT_DATE GROUP BY TASK_ID) a   " +
            "             left join kuaishou_star_task b on a.task_id = b.task_id   group by b.team_name,b.app_name HAVING  cost >   #{checkCost} ")
    List<DspCostAlertBean> getAppCost(@Param("checkCost")Integer checkCost);

    /** 单助推订单超1w */
    @Select(" select a.advertiser_id as advertiserId,a.task_id as taskId,a.order_id as orderId,a.supplement_order_id  as supplementOrderId, b.app_name as appName , b.team_name as teamName,(a.amount/1000) amount,(a.consume_amount/1000) as cost, DATE_FORMAT(a.promotion_begin_time,'%Y-%m-%d %H:%i:%s') as beginTime from kuaishou_star_supplement_order a " +
            "        left join kuaishou_star_task b on a.task_id = b.task_id " +
            "        where crawl_date = CURRENT_DATE AND amount > 20000000 and closing_time is null")
    List<DspCostAlertBean> getSupplementOrderCost();

}
