package com.shinet.core.alert.dsp.service;

import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.shinet.core.alert.dsp.entity.AuthToutiaoAdvertiser;
import com.shinet.core.alert.dsp.mapper.AuthToutiaoAdvertiserMapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.shinet.core.alert.util.DateUtils;
import com.xxl.job.core.log.XxlJobLogger;
import lombok.Data;
import org.apache.http.client.config.RequestConfig;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpEntityEnclosingRequestBase;
import org.apache.http.entity.ContentType;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClientBuilder;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.io.BufferedReader;
import java.io.InputStreamReader;
import java.net.URI;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;

/**
* <p>
    * 广告主 服务实现类
    * </p>
*
* <AUTHOR>
* @since 2024-03-05
*/
@Service
public class AuthToutiaoAdvertiserService extends ServiceImpl<AuthToutiaoAdvertiserMapper, AuthToutiaoAdvertiser> {
    public static final String GetCostUri = "report/advertiser/get/";
    private static final int timeOut = 30000;
    public static int retryTime = 2;
    @Autowired
    AuthToutiaoCustomerService authToutiaoCustomerService;

    public Float findCostByAdvertiserId(AuthToutiaoAdvertiser authToutiaoAdvertiser, Date cdate) {
        try {
            String startTimeStr = DateUtils.formatDateForYMD(cdate);
            //获取头条cost值数据
            XxlJobLogger.log(authToutiaoAdvertiser.getAdvertiserId()+" 获取时间为 "+startTimeStr);
            List<JSONObject> jsonObjectList = reqestAdvertisCost(authToutiaoAdvertiser, startTimeStr, true);
            if (CollectionUtil.isNotEmpty(jsonObjectList)) {
                for (int i = 0; i < jsonObjectList.size(); i++) {
                    Float cost = jsonObjectList.get(i).getFloatValue("cost");
                    return cost;
                }
            }
        } catch (Exception e) {
            log.error("错误信息|{}", e);
            XxlJobLogger.log("刷新广告主：{}出错：{}",authToutiaoAdvertiser.getAdvertiserId(), e);
            return null;
        }
        return null;
    }

    public List<JSONObject> reqestAdvertisCost(AuthToutiaoAdvertiser advertiser, String startTime, Boolean isByDay) {
        // 请求地址
        String open_api_url_prefix = "https://ad.oceanengine.com/open_api/2/";
        String uri = GetCostUri;
        final Map data = new HashMap() {
            {   //广告主ID
                put("advertiser_id", advertiser.getAdvertiserId());
                //开始时间
                put("start_date", startTime);
                //结束时间
                put("end_date", startTime);
                put("page_size", 100);
                //判断时间段
                if (isByDay) {
                    put("time_granularity", "STAT_TIME_GRANULARITY_DAILY");
                } else {
                    put("time_granularity", "STAT_TIME_GRANULARITY_HOURLY");
                }
            }
        };
        // 构造请求
        HttpEntityEnclosingRequestBase httpEntity = new HttpEntityEnclosingRequestBase() {
            @Override
            public String getMethod() {
                return "GET";
            }
        };
        String accessToken = authToutiaoCustomerService.getById(advertiser.getCustomerId()).getAccessToken();
        httpEntity.setHeader("Access-Token", accessToken);
        CloseableHttpResponse response = null;
        CloseableHttpClient client = null;
        try {
            client = HttpClientBuilder.create().build();
            RequestConfig requestConfig = RequestConfig.custom().setConnectionRequestTimeout(timeOut)
                    .setSocketTimeout(timeOut).setConnectTimeout(timeOut).build();
            httpEntity.setConfig(requestConfig);
            httpEntity.setURI(URI.create(open_api_url_prefix + uri));
            httpEntity.setEntity(new StringEntity(JSONObject.toJSONString(data), ContentType.APPLICATION_JSON));
            response = client.execute(httpEntity);
            if (response != null && response.getStatusLine().getStatusCode() == 200) {
                BufferedReader bufferedReader = new BufferedReader(new InputStreamReader(response.getEntity().getContent()));
                StringBuffer result = new StringBuffer();
                String line = "";
                while ((line = bufferedReader.readLine()) != null) {
                    result.append(line);
                }
                bufferedReader.close();
                //将得到的string 转成json对象
                JSONObject jsonObject = JSONObject.parseObject(result.toString());
                if (jsonObject.getInteger("code") != 0) {
                    if (40100 == jsonObject.getInteger("code")) {
                        XxlJobLogger.log("开始睡眠20s：" + advertiser.getAdvertiserId() + "获取数据为【{}】 "+result.toString());
                        TimeUnit.SECONDS.sleep(20);
                    }
                    return null;
                } else {
                    JSONObject jsonData = jsonObject.getJSONObject("data");
                    JSONObject pageInfoJsonObject = jsonData.getJSONObject("page_info");
                    JSONArray jsonArrayList = jsonData.getJSONArray("list");
                    List<JSONObject> arrayLists = jsonArrayList.toJavaList(JSONObject.class);
                    PageInfo pageInfo = pageInfoJsonObject.toJavaObject(PageInfo.class);
                    if (CollectionUtil.isNotEmpty(arrayLists)) {
                        return arrayLists;
                    }
                }
            } else {
                log.error("request toutiao exception response:{}");
                return null;
            }
        } catch (Exception e) {
            log.error("", e);
            return null;
        }
        return null;
    }

    @Data
    static class PageInfo {
        private Integer total_number;
        private Integer page;
        private Integer page_size;
        private Integer total_page;
    }

    @Data
    static class AdData {
        private PageInfo page_info;
        private List<JSONObject> list;
    }
}
