package com.shinet.core.alert.dataagent.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import java.util.Date;
import com.baomidou.mybatisplus.annotation.TableId;
import java.io.Serializable;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR>
 * @since 2021-11-26
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value="UserMetaWechatDel对象", description="")
public class UserMetaWechatDel implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    @ApiModelProperty(value = "微信用户的唯一标识")
    private String openId;

    @ApiModelProperty(value = "用户昵称")
    private String nickName;

    @ApiModelProperty(value = "1时是男性，值为2时是女性，值为0时是未知")
    private Integer gender;

    @ApiModelProperty(value = "用户个人资料填写的省份")
    private String province;

    @ApiModelProperty(value = "普通用户个人资料填写的城市")
    private String city;

    @ApiModelProperty(value = "国家")
    private String country;

    @ApiModelProperty(value = "用户头像")
    private String headimgurl;

    @ApiModelProperty(value = "使用语言")
    private String language;

    @ApiModelProperty(value = "用户绑定到微信开放平台帐号后，才会出现该字段")
    private String unionId;

    @ApiModelProperty(value = "业务id")
    private Long userId;

    @ApiModelProperty(value = "Android:IMEI IOS:idfa")
    private String deviceId;

    @ApiModelProperty(value = "0:android 1:ios")
    private Integer os;

    @ApiModelProperty(value = "appid")
    private Long appId;

    @ApiModelProperty(value = "1.0.0")
    private String appVersion;

    private Date createTime;

    @ApiModelProperty(value = "修改时间")
    private Date updateTime;


}
