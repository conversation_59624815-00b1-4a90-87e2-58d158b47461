package com.shinet.core.alert.config;

import org.apache.commons.pool2.impl.GenericObjectPoolConfig;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import redis.clients.jedis.HostAndPort;
import redis.clients.jedis.JedisCluster;

import java.util.HashSet;
import java.util.Set;

@Configuration
public class JedisConfig {

    /**
     * bp-ap-redis
     *
     * @return
     */
    @Bean("apJedisCluster")
    public JedisCluster safeRedisJedisCluster() {
        Set<HostAndPort> nodes = new HashSet<HostAndPort>();
        nodes.add(new HostAndPort("redis-bp-ap001.coohua-inc.com", 9720));
        nodes.add(new HostAndPort("redis-bp-ap002.coohua-inc.com", 9720));
        nodes.add(new HostAndPort("redis-bp-ap003.coohua-inc.com", 9720));

        JedisCluster jedisCluster = null;
        if (!nodes.isEmpty()) {
            GenericObjectPoolConfig pool = new GenericObjectPoolConfig();
            pool.setMaxTotal(200);
            pool.setMinIdle(10);
            pool.setMaxIdle(50);
            pool.setMaxWaitMillis(2000);
            jedisCluster = new JedisCluster(nodes, pool);
        }
        return jedisCluster;
    }

}