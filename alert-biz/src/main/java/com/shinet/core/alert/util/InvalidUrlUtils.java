package com.shinet.core.alert.util;

import java.util.HashSet;
import java.util.Set;

public class InvalidUrlUtils {

    private static Set<String> invalidUrl = new HashSet<>();

    static {
        //core-dispense 404内容
        invalidUrl.add("/site/xxx/");
        invalidUrl.add("/template/920ka/css/xxx/");
        invalidUrl.add("/aab9/");
        invalidUrl.add("/static/mobile/xxx/");
        invalidUrl.add("/api/banner/");
        invalidUrl.add("/clientapi/app/getinfo/");
        invalidUrl.add("/config/");
        invalidUrl.add("/mobile/v3/xxx/");
        invalidUrl.add("/public/wap/js/xxx/");
        invalidUrl.add("/vendor/xxx/");
        invalidUrl.add("/xy/");
        invalidUrl.add("/Home/Index/api/");
        invalidUrl.add("/im/App/config/");
        invalidUrl.add("/jiaoyimao/");
        invalidUrl.add("/room/getRoomBangFans/");
        invalidUrl.add("/xxx/config/");
        invalidUrl.add("/api/common/config/");
        invalidUrl.add("/css/nsc/xxx/");
        invalidUrl.add("/static/images/config/common/xxx/");
        invalidUrl.add("/local/xxx/");
        invalidUrl.add("/home/<USER>/");
        invalidUrl.add("/resource/home/<USER>/xxx/");
        invalidUrl.add("/css/xxx/");
        invalidUrl.add("/index/index/info/");
        invalidUrl.add("/sdk/");
        invalidUrl.add("/api/shop/getKF/");
        invalidUrl.add("/data/json/xxx/");
        invalidUrl.add("/testing/vendor/phpunit/phpunit/src/Util/PHP/xxx/");
        invalidUrl.add("/xxx/Wap/Api/getBanner/");
        invalidUrl.add("/api/apps/config/");
        invalidUrl.add("/api/getCustomLink/");
        invalidUrl.add("/client/api/findConfigByKey/");
        invalidUrl.add("/admin/xxx/");
        invalidUrl.add("/im/");
        invalidUrl.add("/static/customer/js/xxx/");
        invalidUrl.add("/owa/auth/xxx/");
        invalidUrl.add("/stock/mzhishu/");
        invalidUrl.add("/phpunit/phpunit/Util/PHP/xxx/");
        invalidUrl.add("/Public/H5/js/xxx/");
        invalidUrl.add("/core/Database/xxx/");
        invalidUrl.add("/vendor/phpunit/phpunit/src/Util/PHP/xxx/");
        invalidUrl.add("/wap/");
        invalidUrl.add("/api/appVersion/");
        invalidUrl.add("/banner/xxx/");
        invalidUrl.add("/api/config/");
        invalidUrl.add("/getLocale/");
        invalidUrl.add("/xxx/getPlatParam/");
        invalidUrl.add("/views/commData/xxx/");
        invalidUrl.add("/mPlayer/");
        invalidUrl.add("/other/getTopQuestion/");
        invalidUrl.add("/api/index/init/");
        invalidUrl.add("/my/xxx/");
        invalidUrl.add("/api/stock/xxx/");
        invalidUrl.add("/api/c/a/");
        invalidUrl.add("/common/template/lottery/lecai/css/xxx/");
        invalidUrl.add("/css/skin/xxx/");
        invalidUrl.add("/friendGroup/list/");
        invalidUrl.add("/home/<USER>/");
        invalidUrl.add("/a/");
        invalidUrl.add("/site/api/v1/site/vipExclusiveDomain/getGuestDomain/");
        invalidUrl.add("/www/vendor/phpunit/phpunit/src/Util/PHP/xxx/");
        invalidUrl.add("/GponForm/xxx/");
        invalidUrl.add("/admin/vendor/phpunit/phpunit/src/Util/PHP/xxx/");
        invalidUrl.add("/api/index/webconfig/");
        invalidUrl.add("/index/index/home/");
        invalidUrl.add("/Public/Qts/Home/js/xxx/");
        invalidUrl.add("/laravel/xxx/");
        invalidUrl.add("/member/js/xxx/");
        invalidUrl.add("/query/");
        invalidUrl.add("/systembc/xxx/");
        invalidUrl.add("/wcm/");
        invalidUrl.add("/nyyh/xxx/");
        invalidUrl.add("/static/images/auth/xxx/");
        invalidUrl.add("/backend/xxx/");
        invalidUrl.add("/vendor/phpunit/src/Util/PHP/xxx/");
        invalidUrl.add("/xxx/ok/xxx/");
        invalidUrl.add("/agent/login/");
        invalidUrl.add("/static/index/js/lk/xxx/");
        invalidUrl.add("/Content/xxx/");
        invalidUrl.add("/fePublicInfo/");
        invalidUrl.add("/pro/qb365/");
        invalidUrl.add("/api/v1/config/");
        invalidUrl.add("/public/h5static/js/xxx/");
        invalidUrl.add("/backup/vendor/phpunit/phpunit/src/Util/PHP/xxx/");
        invalidUrl.add("/core/app/xxx/");
        invalidUrl.add("/h5/static/tabbar/xxx/");
        invalidUrl.add("/setting/global/");
        invalidUrl.add("/Public/css/xxx/");
        invalidUrl.add("/prod/api/common/config/");
        invalidUrl.add("/public/img/xxx/");
        invalidUrl.add("/api/notice/");
        invalidUrl.add("/Public/Mobile/xxx/xxx/");
        invalidUrl.add("/saconfig/secure/xxx/");
        invalidUrl.add("/appxz/xxx/");
        invalidUrl.add("/solr/admin/cores/");
        invalidUrl.add("/v2/xxx/");
        invalidUrl.add("/Public/Home/xxx/xxx/");
        invalidUrl.add("/aab8/");
        invalidUrl.add("/api/networkTest/");
        invalidUrl.add("/cms/xxx/");
        invalidUrl.add("/phpMyAdmin/");
        invalidUrl.add("/API/Web/xxx/");
        invalidUrl.add("/public/vendor/phpunit/phpunit/src/Util/PHP/xxx/");
        invalidUrl.add("/forerest/user/custSrv/findOne/");
        invalidUrl.add("/cms/vendor/phpunit/phpunit/src/Util/PHP/xxx/");
        invalidUrl.add("/api/public/");
        invalidUrl.add("/api/im/v2/app/config/");
        invalidUrl.add("/Public/mobile/css/xxx/");
        invalidUrl.add("/api/Config/getShowConfig/");
        invalidUrl.add("/demo/vendor/phpunit/phpunit/src/Util/PHP/xxx/");
        invalidUrl.add("/tests/vendor/phpunit/phpunit/src/Util/PHP/xxx/");
        invalidUrl.add("/HNAP1/");
        invalidUrl.add("/api/user/ismustmobile/");
        invalidUrl.add("/mobile/");
        invalidUrl.add("/remote/logincheck/");
        invalidUrl.add("/resources/css/xxx/");
        invalidUrl.add("/sitemaps/xxx/");
        invalidUrl.add("/m/allticker/xxx/");
        invalidUrl.add("/static/home/<USER>/xxx/");
        invalidUrl.add("/static/wap/js/xxx/");
        invalidUrl.add("/vendor/phpunit/phpunit/LICENSE/xxx/");
        invalidUrl.add("/web/xxx/");
        invalidUrl.add("/index/newapi/api/");
        invalidUrl.add("/V2/vendor/phpunit/phpunit/src/Util/PHP/xxx/");
        invalidUrl.add("/public/assets/js/lib/xxx/");
        invalidUrl.add("/Public/Home/js/xxx/");
        invalidUrl.add("/images/xxx/");
        invalidUrl.add("/public/xxx/");
        invalidUrl.add("/Home/Get/getJnd28/");
        invalidUrl.add("/lib/vendor/phpunit/phpunit/src/Util/PHP/xxx/");
        invalidUrl.add("/druid/xxx/");
        invalidUrl.add("/static/voice/xxx/");
        invalidUrl.add("/dist/azzara/css/xxx/");
        invalidUrl.add("/common/member/js/xxx/");
        invalidUrl.add("/market/xxx/xxx/");
        invalidUrl.add("/Home/Bind/binding/");
        invalidUrl.add("/index/police/xxx/");
        invalidUrl.add("/phpunit/phpunit/src/Util/PHP/xxx/");
        invalidUrl.add("/platform/");
        invalidUrl.add("/resolve/");
        invalidUrl.add("/files/xxx/");
        invalidUrl.add("/mall/toget/banner/");
        invalidUrl.add("/crm/vendor/phpunit/phpunit/src/Util/PHP/xxx/");
        invalidUrl.add("/skin/main/xxx/");
        invalidUrl.add("/admin/");
        invalidUrl.add("/api/v2/static/xxx/");
        invalidUrl.add("/static/guide/xxx/");
        invalidUrl.add("/xxx/phpinfo/");
        invalidUrl.add("/Public/home/<USER>/css/xxx/");
        invalidUrl.add("/xxx/xxx/cctv/cctv1hd/xxx/xxx/");
        invalidUrl.add("/Public/js/xxx/");
        invalidUrl.add("/phone/images/xxx/");
        invalidUrl.add("/xxx/xxx/xxx/");
        invalidUrl.add("/pages/console/js/xxx/");
        invalidUrl.add("/public/css/xxx/");
        invalidUrl.add("/app/js/xxx/");
        invalidUrl.add("/api/message/webInfo/");
        invalidUrl.add("/apps/vendor/phpunit/phpunit/src/Util/PHP/xxx/");
        invalidUrl.add("/JS/xxx/");
        invalidUrl.add("/api/currency/xxx/");
        invalidUrl.add("/xxx/supervisor/xxx/");
        invalidUrl.add("/geoserver/web/");
        invalidUrl.add("/test/vendor/phpunit/phpunit/src/Util/PHP/xxx/");
        invalidUrl.add("/ws/ec/vendor/phpunit/phpunit/src/Util/PHP/xxx/");
        invalidUrl.add("/assets/xxx/");
        invalidUrl.add("/api/product/getPointStore/");
        invalidUrl.add("/app/static/js/xxx/");
        invalidUrl.add("/dvr/cmd/");
        invalidUrl.add("/static/new/css/xxx/");
        invalidUrl.add("/Public/Wchat/js/xxx/");
        invalidUrl.add("/Res/font/xxx/");
        invalidUrl.add("/index/login/");
        invalidUrl.add("/Home/GetInitSource/");
        invalidUrl.add("/Scripts/xxx/");
        invalidUrl.add("/index/user/register/");
        invalidUrl.add("/lib/phpunit/phpunit/src/Util/PHP/xxx/");
        invalidUrl.add("/phpunit/Util/PHP/xxx/");
        invalidUrl.add("/static/css/xxx/");
        invalidUrl.add("/mobile/film/css/xxx/");
        invalidUrl.add("/proxy/games/");
        invalidUrl.add("/static/admincp/js/xxx/");
        invalidUrl.add("/api/config/getkefu/");
        invalidUrl.add("/app/");
        invalidUrl.add("/wap/api/xxx/");
        invalidUrl.add("/laravel/vendor/phpunit/phpunit/src/Util/PHP/xxx/");
        invalidUrl.add("/front/index/getSiteSetting/");
        invalidUrl.add("/site/info/");
        invalidUrl.add("/Templates/user/js/xxx/");
        invalidUrl.add("/index/aurl/");
        invalidUrl.add("/phpunit/src/Util/PHP/xxx/");
        invalidUrl.add("/workspace/drupal/vendor/phpunit/phpunit/src/Util/PHP/xxx/");
        invalidUrl.add("/api/vue/transaction/config/");
        invalidUrl.add("/app/vendor/phpunit/phpunit/src/Util/PHP/xxx/");
        invalidUrl.add("/env/");
        invalidUrl.add("/getConfig/xxx/");
        invalidUrl.add("/template/mb/lang/xxx/");
        invalidUrl.add("/wap/static/images/xxx/");
        invalidUrl.add("/boaform/admin/formLogin/");
        invalidUrl.add("/static/admin/javascript/xxx/");
        invalidUrl.add("/homes/");
        invalidUrl.add("/fonts/xxx/");
        invalidUrl.add("/yii/vendor/phpunit/phpunit/src/Util/PHP/xxx/");
        invalidUrl.add("/app/xxx/");
        invalidUrl.add("/bet/lotteryinfo/allLotteryInfoList/");
        invalidUrl.add("/api/vendor/phpunit/phpunit/src/Util/PHP/xxx/");
        invalidUrl.add("/dist/xxx/");
        invalidUrl.add("/zend/vendor/phpunit/phpunit/src/Util/PHP/xxx/");
        invalidUrl.add("/xxx/sign/");
        invalidUrl.add("/resources/main/xxx/");
        invalidUrl.add("/js/xxx/");
        invalidUrl.add("/static/home/<USER>/xxx/");
        invalidUrl.add("/static/img/xxx/");
        invalidUrl.add("/infe/rest/fig/advertise/xxx/");
        invalidUrl.add("/api/Event/basic/");
        invalidUrl.add("/lib/phpunit/Util/PHP/xxx/");
        invalidUrl.add("/api/shares/hqStrList/");
        invalidUrl.add("/biz/server/config/");
        invalidUrl.add("/en/xxx/");
        invalidUrl.add("/lib/phpunit/phpunit/Util/PHP/xxx/");
        invalidUrl.add("/m/");
        invalidUrl.add("/api/");
        invalidUrl.add("/api/index/grailindex/");
        invalidUrl.add("/static/js/xxx/");
        invalidUrl.add("/xxx/basic/download/info/");
        invalidUrl.add("/xxx/xxx/");
        invalidUrl.add("/api/Business/");
        invalidUrl.add("/api/app/indexList/");
        invalidUrl.add("/api/uploads/apimap/");
        invalidUrl.add("/mytio/config/base/");
        invalidUrl.add("/panel/vendor/phpunit/phpunit/src/Util/PHP/xxx/");
        invalidUrl.add("/Content/css/xxx/");
        invalidUrl.add("/skin/js/xxx/");
        invalidUrl.add("/static/common/js/xxx/");
        invalidUrl.add("/static/index/css/xxx/");
        invalidUrl.add("/lib/xxx/");
        invalidUrl.add("/bao/img/xxx/");
        invalidUrl.add("/user/xxx/");
        invalidUrl.add("/hudson/");
        invalidUrl.add("/xxx/common/configKey/all/");
        invalidUrl.add("/public/static/home/<USER>/moblie/xxx/");
        invalidUrl.add("/static/xxx/");
        invalidUrl.add("/kline/1m/xxx/");
        invalidUrl.add("/lib/phpunit/src/Util/PHP/xxx/");
        invalidUrl.add("/client/static/icon/xxx/");
        invalidUrl.add("/img/xxx/");
        invalidUrl.add("/lanren/css/xxx/");
        invalidUrl.add("/mobile/xxx/");
        invalidUrl.add("/static/mobile/zj/css/xxx/");
        invalidUrl.add("/js/nsc/xxx/");
        invalidUrl.add("/Public/home/<USER>/xxx/");
        invalidUrl.add("/source/xxx/static/wap/js/xxx/");
        invalidUrl.add("/mobile/index/home/");
        invalidUrl.add("/api/system/systemConfigs/getCustomerServiceLink/");
        invalidUrl.add("/otc/");
        invalidUrl.add("/static/home/<USER>/xxx/");
        invalidUrl.add("/xxx/ysp/xxx/");
        invalidUrl.add("/");
        invalidUrl.add("/evox/about/");
        invalidUrl.add("/t4/");
        invalidUrl.add("/xxx/app/config/");
        invalidUrl.add("/xxx/luci/xxx/locale/");
        invalidUrl.add("/core/xxx/");
        invalidUrl.add("/imei/");
        invalidUrl.add("/h5/");
        invalidUrl.add("/index/index/getchatLog/");
        invalidUrl.add("/static/wap/css/xxx/");
        invalidUrl.add("/ui/authentication/");
        invalidUrl.add("/procoin/config/xxx/");
        invalidUrl.add("/public/static/index/picture/xxx/");
        invalidUrl.add("/vendor/vendor/phpunit/phpunit/src/Util/PHP/xxx/");
        invalidUrl.add("/masterControl/getSystemSetting/");
        invalidUrl.add("/iexchange/webtrader/");
        invalidUrl.add("/wap/forward/");
        invalidUrl.add("/api/v1/member/kefu/");
        invalidUrl.add("/vendor/phpunit/Util/PHP/xxx/");
        invalidUrl.add("/api/xxx/");
        invalidUrl.add("/download/info/");
        invalidUrl.add("/Public/xxx/");
        invalidUrl.add("/static/data/xxx/");
        invalidUrl.add("/assets/res/mods/xxx/");
        invalidUrl.add("/blog/vendor/phpunit/phpunit/src/Util/PHP/xxx/");
        invalidUrl.add("/manager/js/xxx/");
        invalidUrl.add("/aktv/img/nyyh/xxx/");
        invalidUrl.add("/download/popy/");
        invalidUrl.add("/manager/html/");
        invalidUrl.add("/static/h5/img/xxx/");
        invalidUrl.add("/ws/index/getTheLotteryInitList/");
        invalidUrl.add("/ajax/xxx/id/xxx/");
        invalidUrl.add("/kefu/css/xxx/");
        invalidUrl.add("/static/picture/xxx/");
        invalidUrl.add("/api/index/getConfig/");
        invalidUrl.add("/api/v/index/queryOfficePage/");
        invalidUrl.add("/app/static/picture/xxx/");
        invalidUrl.add("/melody/api/v1/pageconfig/list/");
        invalidUrl.add("/SiteLoader/");
        invalidUrl.add("/api/ping/");
        invalidUrl.add("/im/h5/");
        invalidUrl.add("/Template/Mobile/js/xxx/");
        invalidUrl.add("/Public/home/<USER>/js/xxx/");
        invalidUrl.add("/ws/vendor/phpunit/phpunit/src/Util/PHP/xxx/");
        invalidUrl.add("/api/front/index/");
        invalidUrl.add("/api/version/");
        invalidUrl.add("/xy/image/xxx/");
        invalidUrl.add("/download/xxx/");
        invalidUrl.add("/dsxs/");
        invalidUrl.add("/geoserver/xxx/");
        invalidUrl.add("/dist/images/xxx/");
        invalidUrl.add("/phpmyadmin/");
        invalidUrl.add("/WuEL/");
        invalidUrl.add("/index/home/<USER>/");
        invalidUrl.add("/portal/index/xxx/");
        invalidUrl.add("/static/index/js/xxx/");
        invalidUrl.add("/vendor/phpunit/phpunit/Util/PHP/xxx/");
        invalidUrl.add("/index/login/register/");
        invalidUrl.add("/xxx/xxx/phpinfo/");
        invalidUrl.add("/api/site/xxx/");

        // safe-api
        invalidUrl.add("/x1files/xxx/");
        invalidUrl.add("/index/api/getweb/");
        invalidUrl.add("/agent/xxx/");
        invalidUrl.add("/wp1/xxx/xxx/");
        invalidUrl.add("/wp2/xxx/xxx/");
        invalidUrl.add("/cms/xxx/xxx/");
        invalidUrl.add("/news/xxx/xxx/");
        invalidUrl.add("/sf/config/check/");
        invalidUrl.add("/api/Home/videoNew/");
        invalidUrl.add("/public/assets/img/index/xxx/");
        invalidUrl.add("/page/");
        invalidUrl.add("/api/home/<USER>/");
        invalidUrl.add("/xxx/xxx/xxx/xxx/");
        invalidUrl.add("/Ctrls/GetSysCoin/");
        invalidUrl.add("/f/user/index/");
        invalidUrl.add("/app/api/app/xxx/");
        invalidUrl.add("/blog/xxx/xxx/");
        invalidUrl.add("/acubu/");
        invalidUrl.add("/version/");
        invalidUrl.add("/invoker/EJBInvokerServlet/");
        invalidUrl.add("/index/login/index/");
        invalidUrl.add("/api/v1/version/");
        invalidUrl.add("/db/xxx/xxx/");
        invalidUrl.add("/unSecurity/app/config/");
        invalidUrl.add("/ecp/Current/exporttool/xxx/");
        invalidUrl.add("/dl1/xxx/");
        invalidUrl.add("/api/unSecurity/app/listAppVersionInfo/");
        invalidUrl.add("/Web/js/v1/xxx/");
        invalidUrl.add("/ReportServer/");
        invalidUrl.add("/dwcc/configxLxn/inxfx/");
        invalidUrl.add("/website/xxx/xxx/");
        invalidUrl.add("/sql/xxx/xxx/");
        invalidUrl.add("/wp/xxx/xxx/");
        invalidUrl.add("/site/xxx/xxx/");
        invalidUrl.add("/public/api/index/config/");
        invalidUrl.add("/test/xxx/xxx/");
        invalidUrl.add("/qs/");
        invalidUrl.add("/static/mobile/yunbi/css/xxx/");
        invalidUrl.add("/home/<USER>/");
        invalidUrl.add("/sites/xxx/");
        invalidUrl.add("/sito/xxx/xxx/");
        invalidUrl.add("/autodiscover/xxx/");
        invalidUrl.add("/home/<USER>/data/");

        invalidUrl.add("/web/xxx/xxx/");

        invalidUrl.add("/api/client/app/xxx/");
        invalidUrl.add("/api/index/web/");

        invalidUrl.add("/containers/json/");

        invalidUrl.add("/neptune/");
        invalidUrl.add("/admin/modules/framework/xxx/htdocs/admin/xxx/");

        //safe-api 0429
        invalidUrl.add("/oiumk/xxx/");
        invalidUrl.add("/AnMjSjfe87/policy/getVersion/");
        invalidUrl.add("/addons/fastim/");
        invalidUrl.add("/admin/appContent/staticImagePosition/");
        invalidUrl.add("/api/GetConfigByKeys/");
        invalidUrl.add("/api/ServerMember/getChatList/");
        invalidUrl.add("/api/Video/config/");
        invalidUrl.add("/api/api/xxx/");
        invalidUrl.add("/api/common/configs/");
        invalidUrl.add("/api/common/init/");
        invalidUrl.add("/api/common/menus/");
        invalidUrl.add("/api/currencyMatch/all/");
        invalidUrl.add("/api/dict/getServiceConfig/");
        invalidUrl.add("/api/getWebSiteConfig/");
        invalidUrl.add("/api/heartbeat/");
        invalidUrl.add("/api/index/config/");
        invalidUrl.add("/api/index/index/");
        invalidUrl.add("/api/index/productshow/");
        invalidUrl.add("/api/init/");
        invalidUrl.add("/api/system/notice/find/");
        invalidUrl.add("/api/user/getconfig/");
        invalidUrl.add("/api/wanlshop/common/init/");
        invalidUrl.add("/apis/globals/");
        invalidUrl.add("/apix/tongchengyue/tags/");
        invalidUrl.add("/assets/js/xxx/");
        invalidUrl.add("/assets/mstock/newimg/xxx/");
        invalidUrl.add("/categories/");
        invalidUrl.add("/chat/home/");
        invalidUrl.add("/ciod/cnfig/setinf/");
        invalidUrl.add("/dwcc/cnfig/setinf/");
        invalidUrl.add("/facebook/xxx/");
        invalidUrl.add("/garcon/");
        invalidUrl.add("/geapi/webConfig/");
        invalidUrl.add("/getDownloadUrl/");
        invalidUrl.add("/getsdkLink/");
        invalidUrl.add("/gjs/stock/");
        invalidUrl.add("/h5/img/icon/xxx/");
        invalidUrl.add("/html/xxx/");
        invalidUrl.add("/index/index/ajaxindexpro/");
        invalidUrl.add("/index/index/cx/u/");
        invalidUrl.add("/instatll/");
        invalidUrl.add("/kfcvwo50/");
        invalidUrl.add("/mfzbs/config/base/");
        invalidUrl.add("/mobile/lottery/list/");
        invalidUrl.add("/public/js/xxx/");
        invalidUrl.add("/scheduler/");
        invalidUrl.add("/share/xxx/");
        invalidUrl.add("/static/common/css/xxx/");
        invalidUrl.add("/static/images/config/grzx/xxx/");
        invalidUrl.add("/static/images/xxx/");
        invalidUrl.add("/static/py/un/xxx/");
        invalidUrl.add("/statics/images/xxx/");
        invalidUrl.add("/syn/");
        invalidUrl.add("/v2/");
        invalidUrl.add("/vip/");
        invalidUrl.add("/wa/xxx/");
        invalidUrl.add("/wx/static/css/xxx/");
        invalidUrl.add("/xxx/api/index/info/");
        invalidUrl.add("/yongxing/");
        invalidUrl.add("/yoxin/v1/common/upush/connector/");
    }

    public static boolean isInvalid(String app, String url) {
        return invalidUrl.contains(url);
    }
}
