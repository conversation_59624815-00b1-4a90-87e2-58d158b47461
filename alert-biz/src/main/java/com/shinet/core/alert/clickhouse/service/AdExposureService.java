package com.shinet.core.alert.clickhouse.service;

import com.shinet.core.alert.clickhouse.entity.*;
import com.shinet.core.alert.clickhouse.mapper.ck1.ExposureOdsMapper;
import com.shinet.core.alert.coreservice.entity.AlertRecord;
import com.shinet.core.alert.coreservice.enums.*;
import com.shinet.core.alert.coreservice.service.AlertRecordService;
import com.shinet.core.alert.dingtalk.DingTailService;
import com.shinet.core.alert.dingtalk.VedioAlertService;
import com.shinet.core.alert.util.DateUtils;
import com.shinet.core.alert.util.DoubleUtil;
import com.xxl.job.core.log.XxlJobLogger;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

@Slf4j
@Service
public class AdExposureService {
    @Resource
    ExposureOdsMapper exposureOdsMapper;
    @Autowired
    private QueryBpAdminOpLogService queryBpAdminOpLogService;

    public void getAdExposure(String jobName,int mins, List<String> excludeAdIdList){
        Date date = new Date(System.currentTimeMillis()-mins*60*1000L);
        String startTime = (System.currentTimeMillis()-mins*60*1000L)+"";

        List<AdIdExposure>  adIdExposureList = exposureOdsMapper.getAdActionNums(startTime);
//        Set<String>  adIdBiddings = exposureOdsMapper.getAdBiddings();


        Map<String,List<AdIdExposure>> dmap = new HashMap<>();
        for(AdIdExposure adIdExposure : adIdExposureList){
            String adId = adIdExposure.getAdId();
            if(StringUtils.isNotBlank(adId)){
                List<AdIdExposure> adIdExposureList1 = dmap.get(adId);
                if(adIdExposureList1==null){
                    adIdExposureList1 = new ArrayList<>();
                }
                adIdExposureList1.add(adIdExposure);

                dmap.put(adId,adIdExposureList1);
            }
        }

        Set<String> dset = dmap.keySet();

        String dmsg = "";
        for(String adId:dset){
            List<AdIdExposure> adIdExposureList1 = dmap.get(adId);
            if(adIdExposureList1.size()>0){
                Long exposureNum = 0L;
                Long rewardNum = 0L;
                Long closeNum = 0L;
                String product = "";

                for(AdIdExposure adIdExposure : adIdExposureList1){
                    String adAction  = adIdExposure.getAdAction();
                    product = adIdExposure.getProduct();
                    if("exposure".equalsIgnoreCase(adAction)){
                        exposureNum = adIdExposure.getShowNum();
                    }else if("reward".equalsIgnoreCase(adAction)){
                        rewardNum = adIdExposure.getShowNum();
                    }else if("close".equalsIgnoreCase(adAction)){
                        closeNum = adIdExposure.getShowNum();
                    }
                }

                if(rewardNum==null){
                    rewardNum = 0L;
                }
                if(exposureNum==null){
                    exposureNum = 0L;
                }
                if(excludeAdIdList.contains(adId)){
                    XxlJobLogger.log(" 过滤掉 "+adId);
                }
                if(exposureNum>=3000 && Math.abs(rewardNum-exposureNum)>4000 && !excludeAdIdList.contains(adId)){
                    float rewardRate = (rewardNum*1.0f)/(exposureNum*1.0f);
                    float closeRate = rewardNum/exposureNum;

                    if(rewardRate<0.6){

                        List<AdIdExposure>  adIdExposures = exposureOdsMapper.getAdActionNumsByAdId(startTime,adId);
                        Long rwardNuNum = getEventNum(adIdExposures,"reward");
                        if(rwardNuNum>rewardNum){
                            float reward2Rate = (rwardNuNum*1.0f)/(exposureNum*1.0f);
                            if(reward2Rate<0.6){
                                dmsg = dmsg + " product:"+product+" adId:"+adId+" rewardNum-"+rewardNum+" exposureNum-"+exposureNum+ " rewardRate-"+rewardRate+" <0.7 \r\n";
                                log.info("product:"+product+" adId:"+adId+" rewardNum:"+rewardNum+" exposureNum:"+exposureNum+ " rewardRate:"+rewardRate+" <0.7");

                                XxlJobLogger.log("product:"+product+" adId:"+adId+" rewardNum:"+rewardNum+" exposureNum:"+exposureNum+ " rewardRate:"+rewardRate+" <0.7");
                            }
                        }
                    }
                }
            }
        }
        XxlJobLogger.log("广告exposure 预警完成 "+dmsg);
        if(StringUtils.isNotBlank(dmsg)){
            AlertRecord alertRecord = alertRecordService.insertAlertRecord(jobName,
                    jobName, AlertModel.REWARDEXPOSURE, dmsg
                    , AlertStatus.INIT, DingTailService.dset, AlertType.DINGDING);
            DingTailService.sendMarkdownMsg(alertRecord, AlertRoute.JISHU.value);
            VedioAlertService.sendVocMsg(" dauArpu  ", dmsg);
            VedioAlertService.sendVocMsg(" dauArpu  ", dmsg,"15822339517");
        }
    }

    private Long getEventNum(List<AdIdExposure>  adIdExposures,String action){
        Long actionNum = 0l;
        for(AdIdExposure adIdExposure : adIdExposures){
            String adAction  = adIdExposure.getAdAction();
            if(action.equalsIgnoreCase(adAction)){
                actionNum = adIdExposure.getShowNum();
            }
        }
        return actionNum;
    }
    @Autowired
    AlertRecordService alertRecordService;

    public void getThirdRewardExposure(String jobName,int mins){
        Date date = new Date(System.currentTimeMillis()-mins*60*1000L);
        String startTime = (System.currentTimeMillis()-mins*60*1000L)+"";

        List<AdIdExposure>  adIdExposureList = exposureOdsMapper.getAdActionNums(startTime);
        Set<String>  adIdBiddings = exposureOdsMapper.getAdBiddings();
        List<AdIdExposure>  rewardExposureList = exposureOdsMapper.getAdRewardNums(startTime);

        Map<String,AdIdExposure> rewardMap = rewardExposureList.stream().collect(Collectors.toMap(AdIdExposure::getAdId,a->a));

        Map<String,List<AdIdExposure>> dmap = new HashMap<>();
        for(AdIdExposure adIdExposure : adIdExposureList){
            String adId = adIdExposure.getAdId();
            if(StringUtils.isNotBlank(adId)){
                AdIdExposure rewardExposure = rewardMap.get(adId);
                Long rewardNumThird = 0L;
                if(rewardExposure!=null && rewardExposure.getShowNum()!=null){
                    rewardNumThird = rewardExposure.getShowNum();
                }
                adIdExposure.setRewardNum(rewardNumThird);
                List<AdIdExposure> adIdExposureList1 = dmap.get(adId);
                if(adIdExposureList1==null){
                    adIdExposureList1 = new ArrayList<>();
                }
                adIdExposureList1.add(adIdExposure);

                dmap.put(adId,adIdExposureList1);
            }
        }

        Set<String> dset = dmap.keySet();

        String dmsg = "";
        for(String adId:dset){
            List<AdIdExposure> adIdExposureList1 = dmap.get(adId);
            if(adIdExposureList1.size()>0){
                Long exposureNum = 0L;
                Long rewardNum = 0L;
                Long closeNum = 0L;
                String product = "";
                Long thirdRewardNum = 0L;

                for(AdIdExposure adIdExposure : adIdExposureList1){
                    String adAction  = adIdExposure.getAdAction();
                    product = adIdExposure.getProduct();
                    thirdRewardNum = adIdExposure.getRewardNum();
                    if("exposure".equalsIgnoreCase(adAction)){
                        exposureNum = adIdExposure.getShowNum();
                    }else if("reward".equalsIgnoreCase(adAction)){
                        rewardNum = adIdExposure.getShowNum();
                    }else if("closeNum".equalsIgnoreCase(adAction)){
                        closeNum = adIdExposure.getShowNum();
                    }
                }

                if(rewardNum>=1000){
                    float rewardRate = (thirdRewardNum*1.0f)/(rewardNum*1.0f);
                    if(adIdBiddings.contains(adId)){
                        rewardRate = (thirdRewardNum*1.0f)/(closeNum*1.0f);
                    }

                    if(rewardRate<0.6){
                        log.info("product:"+product+" adId:"+adId+" rewardNum:"+rewardNum+" thirdRewardNum:"+thirdRewardNum+ " thirdrewardRate:"+rewardRate+" <0.7");

                        dmsg = dmsg +"product:"+product+" adId:"+adId+" rewardNum:"+rewardNum+" thirdRewardNum:"+thirdRewardNum+ " thirdrewardRate:"+rewardRate+" <0.7 \r\n ";
                    }
                }
            }
        }
        if(StringUtils.isNotBlank(dmsg)){
            AlertRecord alertRecord = alertRecordService.insertAlertRecord(jobName,
                    jobName, AlertModel.REWARDTHIRD, dmsg
                    , AlertStatus.INIT, DingTailService.dset, AlertType.DINGDING);
            DingTailService.sendMarkdownMsg(alertRecord, AlertRoute.JISHU.value);
        }
    }


    public void getAdArpuExposure(String jobName,int mins,int minDau,int alertRate,List<String> bkprojectSet){
//        long ctime = DateUtils.stringToDate("2022-06-07 03:50:00",DateUtils.PATTERN_YHMS).getTime();
        long ctime = System.currentTimeMillis();
        Date yesDay = new Date(ctime-24*60*60*1000L);
        Date yesLogday = DateUtils.getDayBeginDate(yesDay);

        String yesLogdaystr = DateUtils.formatDateForYMD(yesLogday);
        String yesStratDate = DateUtils.formatDate(yesLogday);
        String yesEndDate = DateUtils.getDayEndDate(yesDay);
        List<AdDauArapBean> adDauArapBeans = exposureOdsMapper.queryDauArpu(yesStratDate,yesEndDate,yesLogdaystr);


        String todayLogdaystr = DateUtils.formatDateForYMD(new Date(ctime));
        String startDayTime = DateUtils.formatDate(new Date(ctime-mins*60*1000L));
        String endDayTime = DateUtils.formatDate(new Date(ctime));
        List<AdDauArapBean> todayArapBeans = exposureOdsMapper.queryDauArpu(startDayTime,endDayTime,todayLogdaystr);
        Map<String,AdDauArapBean> todayPmap = todayArapBeans.stream().collect(Collectors.toMap(AdDauArapBean::getProduct, Function.identity(),(a,b)->a));

        String startYesDayTime = DateUtils.formatDate(new Date(ctime-24*60*60*1000L-mins*60*1000L));
        String endYesDayTime = DateUtils.formatDate(new Date(ctime-24*60*60*1000L));

        List<AdDauArapBean> yesArapBeans = exposureOdsMapper.queryDauArpu(startYesDayTime,endYesDayTime,yesLogdaystr);
        Map<String,AdDauArapBean> yesPmap = yesArapBeans.stream().collect(Collectors.toMap(AdDauArapBean::getProduct, Function.identity(),(a,b)->a));


        int dauAlertRate = -40;
        int arpuAlertRate = -alertRate;
        int ecpmAlertRate = -alertRate;
        int pvAlertRate = -alertRate;

        String dauMsg = "";
        String arpuMsg = "";
        String ecpmMsg = "";
        String pvMsg = "";

        Set<String> productSet = new HashSet<>();
        for(AdDauArapBean adDauArapBean : adDauArapBeans){
            String product = adDauArapBean.getProduct();
            if(bkprojectSet.contains(product)){
                XxlJobLogger.log("跳过 "+product);
                continue;
            }
            Integer yesDau = adDauArapBean.getDau();
            if(yesDau>=minDau){
                XxlJobLogger.log("开始检测 "+product+" dau为 "+yesDau);
                //同比20min dau ecpm pv 查阅
                AdDauArapBean todayArapBean = todayPmap.get(product);
                AdDauArapBean yesArapBean = yesPmap.get(product);

                Date date = new Date(ctime);
                if(date.getHours()<=8){
                    if(todayArapBean.getDau()<5000){
                        XxlJobLogger.log("开始检测 "+product+" dau为 "+yesDau+" 因时间为"+date.getHours()+" dau 需大于5000 当前dau为"+adDauArapBean.getDau());
                        continue;
                    }
                }
                Double rate2 = getRate(todayArapBean.getEcpm(),yesArapBean.getEcpm());
                XxlJobLogger.log("ecpm检测项 "+product+" todayEcpm为 "+todayArapBean.getEcpm()+" yesEcpm:"+yesArapBean.getEcpm()+" rate:"+rate2);
                if(todayArapBean.getEcpm()< yesArapBean.getEcpm()){
                    if(rate2<ecpmAlertRate){
                        //xmzcl-(t:1000,y:2000,r:20)
                        ecpmMsg = ecpmMsg+" "+product+"-(T:"+DoubleUtil.getDoubleByTwo(todayArapBean.getEcpm())+",Y:"+DoubleUtil.getDoubleByTwo(yesArapBean.getEcpm())+",R:"+rate2+"),";
                        productSet.add(todayArapBean.getProduct());
                    }
                }

                Double rate3 = getRate(todayArapBean.getPvren(),yesArapBean.getPvren());
                XxlJobLogger.log("Pvren检测项 "+product+" todayPvren为 "+todayArapBean.getPvren()+" yesPvren:"+yesArapBean.getPvren()+" rate:"+rate3);
                if(todayArapBean.getPvren()< yesArapBean.getPvren()){
                    if(rate3<pvAlertRate){
                        //xmzcl-(t:1000,y:2000,r:20)
                        pvMsg = pvMsg+" "+product+"-(T:"+DoubleUtil.getDoubleByTwo(todayArapBean.getPvren())+",Y:"+DoubleUtil.getDoubleByTwo(yesArapBean.getPvren())+",R:"+rate3+"),";
                        productSet.add(todayArapBean.getProduct());
                    }
                }

                Double rate4 = getRate(todayArapBean.getArpu(),yesArapBean.getArpu());
                XxlJobLogger.log("Arpu检测项 "+product+" todayArpu为 "+todayArapBean.getArpu()+" yesArpu:"+yesArapBean.getArpu()+" rate:"+rate4);
                if(todayArapBean.getArpu()< yesArapBean.getArpu()){
                    if(rate4<arpuAlertRate){
                        //xmzcl-(t:1000,y:2000,r:20)
                        arpuMsg = arpuMsg+" "+product+"-(T:"+DoubleUtil.getDoubleByTwo(todayArapBean.getArpu())+",Y:"+DoubleUtil.getDoubleByTwo(yesArapBean.getArpu())+",R:"+rate4+"),";
                        productSet.add(todayArapBean.getProduct());
                    }
                }
                Double rate5 = getRate(todayArapBean.getDau(),yesArapBean.getDau());
                XxlJobLogger.log("dau检测项 "+product+" todaydau为 "+todayArapBean.getDau()+" yesDau:"+yesArapBean.getDau()+" rate:"+rate5);

                Double arpuRate = getRate(todayArapBean.getArpu(),yesArapBean.getArpu());
                if(todayArapBean.getDau()< yesArapBean.getDau()){

                    if(rate5<dauAlertRate && arpuRate<arpuAlertRate){
                        //xmzcl-(t:1000,y:2000,r:20)
                        dauMsg = dauMsg+" "+product+"-(T:"+todayArapBean.getDau()+",Y:"+yesArapBean.getDau()+",R:"+rate5+"),";
                        productSet.add(todayArapBean.getProduct());
                    }
                }

            }
        }
        if(StringUtils.isNotBlank(dauMsg)){
            dauMsg = "dau:"+dauMsg;
        }
        if(StringUtils.isNotBlank(arpuMsg)){
            arpuMsg = " arpu::"+arpuMsg;
        }
        if(StringUtils.isNotBlank(ecpmMsg)){
            ecpmMsg = " ecpm:"+ecpmMsg;
        }
        if(StringUtils.isNotBlank(pvMsg)){
            pvMsg = " pv:"+pvMsg;
        }
        //只监控dau 大于
        if(StringUtils.isNotBlank(dauMsg) || StringUtils.isNotBlank(arpuMsg) || StringUtils.isNotBlank(ecpmMsg) || StringUtils.isNotBlank(pvMsg)){
            String alertMsg = dauMsg+"\r\n"+arpuMsg+"\r\n"+ecpmMsg+"\r\n"+pvMsg;
            AlertRecord alertRecord = alertRecordService.insertAlertRecord(jobName,
                    jobName, AlertModel.ADPVECPM, alertMsg
                    , AlertStatus.INIT, DingTailService.dset, AlertType.DINGDING);
            DingTailService.sendMarkdownMsg(alertRecord, AlertRoute.JISHU.value);
            DingTailService.sendMarkdownMsg(alertRecord, AlertRoute.ERHAOALERT.value);
            if (productSet.size() > 0) {
                Long updatedCount = productSet.stream().filter(pr ->{
                    ProductEntity productEntity = AppConfig.productEnMap.getOrDefault(pr, new ProductEntity() {{
                        setId(0);
                    }});
                    return queryBpAdminOpLogService.updateAdConfig(productEntity.getId());
                }).count();
                //有修改 电话报警
                if (updatedCount > 0) {
                    VedioAlertService.sendVocMsg(" dauArpu  ", alertMsg);
                }
            }
        }
    }

    public static Double getRate(Integer a1,Integer a2){
        if(a1==null){
            a1 = 0;
        }

        if(a2==null){
            a2 = 0;
            return 0d;
        }
        return  DoubleUtil.getDoubleByTwo((((a1*1.0d) -(a2*1.0d)) /(a2*1.0d))*100);
    }


    public static Double getRate(Double a1,Double a2){
        if(a1==null){
            a1 = 0d;
        }

        if(a2==null){
            a2 = 0d;
            return 0d;
        }
        return DoubleUtil.getDoubleByTwo((((a1*1.0d) -(a2*1.0d)) /(a2*1.0d))*100);
    }



}
