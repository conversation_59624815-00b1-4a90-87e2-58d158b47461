package com.shinet.core.alert.dingtalk;

import com.google.common.collect.Lists;
import com.shinet.core.alert.coreservice.entity.AlertRecord;
import com.shinet.core.alert.coreservice.enums.AlertJobDelayModel;
import com.shinet.core.alert.coreservice.enums.AlertStatus;
import com.shinet.core.alert.util.DateUtils;

import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

public class DingMsgService {

    public static List<String> StopHas2HourJobs = Arrays.asList(AlertJobDelayModel.CPA_HOUR_REALTIME.getJobId(),
            AlertJobDelayModel.ALL_DSP_COST.getJobId(),
            AlertJobDelayModel.ALL_AD_CPA_BID.getJobId(),
            AlertJobDelayModel.ALL_DSP_OPTIMIZATION.getJobId(),
            AlertJobDelayModel.SUPPLEMENT_AMOUNT.getJobId()
    );



    public static String getDingMarkdownMsg(AlertRecord alertRecord){
        String alertPhoneStr = Lists.newArrayList(alertRecord.getAlertPhone().split(",")).stream().map(phone->" @"+phone).collect(Collectors.joining("  \n  "));

        String butContent = "[**开始处理**](http://*************/alert/confirm?alertId="+alertRecord.getId()+")     [**处理完成**](http://*************/alert/confirm?alertId="+alertRecord.getId()+") ";

        String sendMsg =
                "## "+alertRecord.getModelName()+
                        "  \n  _ _ _  \n  "+
                        "jobName:"+alertRecord.getJobName()+
                        "  \n   _ _ _   \n  "+
                        alertRecord.getAlertMsg()+
                        "  \n   _ _ _   \n  "+
                        alertPhoneStr+ "  \n   _ _ _   \n  ";

        AlertStatus alertStatus = AlertStatus.getStatus(alertRecord.getAlertStatus());
        if(AlertStatus.ENDOPT.equals(alertStatus)){
            sendMsg = sendMsg + DateUtils.formatDate(alertRecord.getUpdateTime())+ "  处理完成 ";
        }else if(AlertStatus.STARTOPT.equals(alertStatus)){
            sendMsg = sendMsg + "[**处理完成**](http://*************/alert/confirm?alertId="+alertRecord.getId()+")";
        }else if (AlertStatus.DONT_SOLVE.equals(alertStatus)){
            return sendMsg;
        }else{
            sendMsg = sendMsg + butContent;
        }

        return sendMsg;
    }

    public static String getDingMarkdownWithDelayTime(AlertRecord alertRecord, AlertJobDelayModel model, List<String> skipProductList){
        String alertPhoneStr = Lists.newArrayList(alertRecord.getAlertPhone().split(","))
                .stream().map(phone->" @"+phone)
                .collect(Collectors.joining("  \n  "));
        //DelayJobController.delayJob
        String butContent = skipProductList.stream()
         .map(rs -> {
             String base = "暂停["+rs + "] ";
             if (!StopHas2HourJobs.contains(model.getJobId())) {
                 base = base + "[**30min**](dingtalk://dingtalkclient/page/link?url=http%3A%2F%2F*************%2Falert%2FdelayJob%3FjobId%3D"+model.getJobId()+"_"+rs+"%26delayTime%3D1800000&pc_slide=true) " +
                         "[**1h**](dingtalk://dingtalkclient/page/link?url=http%3A%2F%2F*************%2Falert%2FdelayJob%3FjobId%3D"+model.getJobId()+"_"+rs+"%26delayTime%3D3600000&pc_slide=true)  ";
             } else {
                 base =  base + "[**0.5h**](dingtalk://dingtalkclient/page/link?url=http%3A%2F%2F*************%2Falert%2FdelayJob%3FjobId%3D"+model.getJobId()+"_"+rs+"%26delayTime%3D1800000&pc_slide=true) " +
                         "[**1h**](dingtalk://dingtalkclient/page/link?url=http%3A%2F%2F*************%2Falert%2FdelayJob%3FjobId%3D"+model.getJobId()+"_"+rs+"%26delayTime%3D3600000&pc_slide=true)  " +
                         "[**2h**](dingtalk://dingtalkclient/page/link?url=http%3A%2F%2F*************%2Falert%2FdelayJob%3FjobId%3D"+model.getJobId()+"_"+rs+"%26delayTime%3D7200000&pc_slide=true) ";
             }
             base = base +
                "[**6h**](dingtalk://dingtalkclient/page/link?url=http%3A%2F%2F*************%2Falert%2FdelayJob%3FjobId%3D"+model.getJobId()+"_"+rs+"%26delayTime%3D21600000&pc_slide=true)  " +
                "[**12h**](dingtalk://dingtalkclient/page/link?url=http%3A%2F%2F*************%2Falert%2FdelayJob%3FjobId%3D"+model.getJobId()+"_"+rs+"%26delayTime%3D43200000&pc_slide=true) " +
                "[**24h**](dingtalk://dingtalkclient/page/link?url=http%3A%2F%2F*************%2Falert%2FdelayJob%3FjobId%3D"+model.getJobId()+"_"+rs+"%26delayTime%3D86400000&pc_slide=true)";
             return base;
         }).collect(Collectors.joining(" \n\n"));

        String sendMsg =
                "## "+alertRecord.getModelName()+
                        "  \n  _ _ _  \n  "+
                        "jobName:"+alertRecord.getJobName()+
                        "  \n   _ _ _   \n  "+
                        alertRecord.getAlertMsg()+
                        "  \n   _ _ _   \n  "+
                        alertPhoneStr+ "  \n   _ _ _   \n  ";

        sendMsg = sendMsg + butContent;

        return sendMsg;
    }

}
