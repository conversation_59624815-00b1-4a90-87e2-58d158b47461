package com.shinet.core.alert.safe.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.shinet.core.alert.safe.entity.AndroidLockConf;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;

import java.util.Date;
import java.util.List;

/**
 * <p>
 * 锁区IOS特殊策略 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2024-03-15
 */
public interface AndroidLockConfMapper extends BaseMapper<AndroidLockConf> {

    @Update("update android_lock_conf set all_lock = 0, is_enable = 0,update_time = #{date} where product = #{product} and lock_pkgs = #{lockPkgs} and lock_versions = #{lockVersions}")
    int disableAllLock(@Param("product") String product, @Param("lockPkgs") String lockPkgs, @Param("lockVersions") String lockVersions, @Param("date") Date date);


    @Update("update android_lock_conf set lock_pkgs = #{newLockPkgs},update_time = #{date} where product = #{product} and lock_pkgs like concat('%', #{lockPkgs}, '%') and lock_versions = #{lockVersions}")
    int disableAllLockByChannel(@Param("product") String product, @Param("lockPkgs") String lockPkgs, @Param("lockVersions") String lockVersions,@Param("newLockPkgs") String newLockPkgs, @Param("date") Date date);

    @Select("select lock_pkgs from android_lock_conf where product = #{product} and lock_pkgs like concat('%', #{lockPkgs}, '%') and lock_versions = #{lockVersions} and is_enable = 1 and all_lock = 1 limit 1")
    String getAllLockPkgs(@Param("product") String product, @Param("lockPkgs") String lockPkgs, @Param("lockVersions") String lockVersions);
}
