package com.shinet.core.alert.bpap.mapper;

import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.Date;

/**
 * <AUTHOR>
 * @since 2022/8/31
 */
public interface BpAdminMapper {

    @Select({"select COUNT(1) from `bp-ap`.tb_ap_config_orientation where product = #{appId} and update_time >=#{updateTime}"})
    Integer countConfigBaseOrigin(@Param("appId") Integer appId,@Param("updateTime") Date updateTime);

    @Select({"select COUNT(1) from `bp-ap`.tb_ap_config_base where product = #{appId} and update_time >=#{updateTime}"})
    Integer countConfigBase(@Param("appId") Integer appId,@Param("updateTime") Date updateTime);

    @Select({"select COUNT(1) from `bp-ap`.tb_ap_ad_information where product = #{appId} and update_time >=#{updateTime}"})
    Integer countAd(@Param("appId") Integer appId,@Param("updateTime") Date updateTime);

    @Select({"select COUNT(1) from `bp-ap`.tb_bp_ap_operate_log where product = #{appId} and operate_time >=#{updateTime}"})
    Integer countAdSystem(@Param("appId") Integer appId,@Param("updateTime") Date updateTime);
}
