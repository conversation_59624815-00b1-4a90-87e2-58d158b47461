package com.shinet.core.alert.dsp.service;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.shinet.core.alert.dsp.entity.KuaishouStarOrder;
import com.shinet.core.alert.dsp.entity.KuaishouSupplementOrderRealtime;
import com.shinet.core.alert.dsp.mapper.KuaishouStarOrderMapper;
import org.checkerframework.checker.units.qual.A;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigInteger;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.List;
import java.util.Set;

@Service
public class KuaishouStarOrderService extends ServiceImpl<KuaishouStarOrderMapper, KuaishouStarOrder> {

    @Autowired
    KuaishouStarOrderMapper kuaishouStarOrderMapper;



    public List<KuaishouStarOrder> queryVideoTaskCostByAdvertiserId(String advertiserId, Set<BigInteger> taskIdList, int day) {

        // 获取day天前的日期
        String preDay = LocalDate.now().minusDays(day).format(DateTimeFormatter.ofPattern("yyyy-MM-dd"));
        String nowDay = LocalDate.now().minusDays(1).format(DateTimeFormatter.ofPattern("yyyy-MM-dd"));

        return  kuaishouStarOrderMapper.queryTaskCost(advertiserId,taskIdList,preDay,nowDay);
    }
}
