package com.shinet.core.alert.dsp.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import java.util.Date;
import com.baomidou.mybatisplus.annotation.TableId;
import java.io.Serializable;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * 广告主
 * </p>
 *
 * <AUTHOR>
 * @since 2024-03-05
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value="AuthToutiaoAdvertiser对象", description="广告主")
public class AuthToutiaoAdvertiser implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    @ApiModelProperty(value = "关联客户中心表ID ")
    private Long customerId;

    @ApiModelProperty(value = "广告主ID（自动生成）")
    private Long advertiserId;

    @ApiModelProperty(value = "名称")
    private String advertiserName;

    @ApiModelProperty(value = "产品表ID（运营选择）")
    private Long productId;

    @ApiModelProperty(value = "产品名称")
    private String productName;

    @ApiModelProperty(value = "代理商ID")
    private String agentId;

    @ApiModelProperty(value = "代理商名称")
    private String agentName;

    @ApiModelProperty(value = "投放类型")
    private String putType;

    @ApiModelProperty(value = "花费")
    private Double costMoney;

    @ApiModelProperty(value = "行为类型 1 观看视频次数 2 24小时arpu")
    private Integer eventType;

    @ApiModelProperty(value = "行为次数")
    private Integer eventNumber;

    @ApiModelProperty(value = "扩展版行为值 行为类型非1时适用")
    private String eventValue;

    @ApiModelProperty(value = "分组ID")
    private Integer groupId;

    @ApiModelProperty(value = "分组名称")
    private String groupName;

    @ApiModelProperty(value = "部门ID")
    private Integer deptId;

    @ApiModelProperty(value = "部门名称")
    private String deptName;

    @ApiModelProperty(value = "删除标识")
    private Integer delFlag;

    @ApiModelProperty(value = "创建人ID")
    private Integer createId;

    @ApiModelProperty(value = "创建人")
    private String createName;

    @ApiModelProperty(value = "更新人ID")
    private Integer updateId;

    @ApiModelProperty(value = "修改人")
    private String editorName;

    @ApiModelProperty(value = "修改人修改时间")
    private Date editorTime;

    @ApiModelProperty(value = "创建时间")
    private Date createTime;

    @ApiModelProperty(value = "修改人")
    private String updateName;

    @ApiModelProperty(value = "更新时间")
    private Date updateTime;

    @ApiModelProperty(value = "有效标识（目前用在拉广告数据），-1:无效，0：有效")
    private Integer validFlag;

    @ApiModelProperty(value = "是否是测试账户，0不是 1是")
    private Integer testAccount;

    @ApiModelProperty(value = "行为类型集合")
    private String eventTypes;

    @ApiModelProperty(value = "行为数值集合")
    private String eventValues;

    @ApiModelProperty(value = "数据累积期限，单位为分钟")
    private Integer maxAccumulatePeriod;

    @ApiModelProperty(value = "是否回传ltv数值 1:回传 0:不回传")
    private Integer callbackLtv;

    private Integer enable;

    @ApiModelProperty(value = "1普通账户 2达人账户")
    private Integer accountType;

    @ApiModelProperty(value = "是否可以新建计划 0:可以 -1:不可以")
    private Integer enableCreatePlan;

    @ApiModelProperty(value = "达人出价配置")
    private Double bidPrice;

    @ApiModelProperty(value = "0正常 1游戏行为")
    private Integer actionType;


}
