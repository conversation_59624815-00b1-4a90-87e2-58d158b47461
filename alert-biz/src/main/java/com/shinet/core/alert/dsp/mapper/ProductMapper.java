package com.shinet.core.alert.dsp.mapper;


import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.shinet.core.alert.dsp.entity.AdPlanConfig;
import com.shinet.core.alert.dsp.entity.Product;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * <p>
 *  Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2020-10-19
 */
public interface ProductMapper extends BaseMapper<Product> {

    @Select(
              "              select * from `coo-dispense`.ad_plan_config                                                  "
            + "              where id in (select max(d.id)                                                      "
            + "              from `coo-dispense`.ad_plan_config d                                                  "
            + "               join `coo-dispense`.video_plan b on b.id = d.plan_id                                                       "
            + "              where d.os = 'APP_ANDROID'                                                  "
            + "               and d.download_url is not null                                                      "
            + "               and d.download_url != ''                                                  "
            + "              group by d.app_name)                                                   "
            + "            and app_name in (                                                                "
            + "                 select d.app_name                                                  "
            + "                 from `coo-dispense`.ad_plan_config d                                                  "
            + "                  join `coo-dispense`.video_plan b on b.id = d.plan_id                                                       "
            + "                 where d.del_flag = 0                                                  "
            + "                  and d.os = 'APP_ANDROID'                                                      "
            + "                  and b.plan_status in (0, 2)                                                  "
            + "                   and b.del_flag = 0                                                   "
            + "                 group by d.app_name)                                                  "
    )
    List<AdPlanConfig> getGroupDownurl();
    /*@Select(
            "          select * from (                                      "
        +   "          select d.app_name                                    "
        +   "          from `coo-dispense`.ad_plan_config d                "
        +   "           join `coo-dispense`.video_plan b                              "
        +   "               on b.id = d.plan_id                             "
        +   "          where d.del_flag = 0                                 "
        +   "           and d.os = 'APP_ANDROID'                                "
        +   "           and b.plan_status in (0, 2)                             "
        +   "            and b.del_flag = 0                                 "
        +   "          group by d.app_name) a                                   "
        +   "          left join (select d.app_name                             "
        +   "          from `coo-dispense`.ad_plan_config d                             "
        +   "           join `coo-dispense`.video_plan b                        "
        +   "               on b.id = d.plan_id                                     "
        +   "          where d.os = 'APP_ANDROID'                               "
        +   "           and d.download_url is not null                              "
        +   "           and d.download_url != ''                                    "
        +   "          group by d.app_name) b                                   "
        +   "          on a.app_name = b.app_name                               "
        +   "          where b.app_name is null                             "
    )
    List<String> diffGroupDownurl();*/
}
