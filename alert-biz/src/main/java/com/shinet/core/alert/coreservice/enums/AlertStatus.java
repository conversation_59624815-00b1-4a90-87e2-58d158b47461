package com.shinet.core.alert.coreservice.enums;

public enum AlertStatus {
    NOMAL(0,"正常"),
    INIT(1,"初始化"),
    STARTOPT(2,"开始处理"),
    ENDOPT(3,"处理完成"),
    DONT_SOLVE(4,"无需处理"),
    ;
    public Integer value;
    public String name;

    AlertStatus(Integer value, String name) {
        this.value = value;
        this.name = name;
    }

    public static AlertStatus getStatus(Integer value) {
        if (value != null) {
            AlertStatus[] otypes = AlertStatus.values();
            for (AlertStatus memberType : otypes) {
                if (value.equals(memberType.value)) {
                    return memberType;
                }
            }
        }
        return null;
    }
}
