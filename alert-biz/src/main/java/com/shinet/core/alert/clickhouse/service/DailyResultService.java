package com.shinet.core.alert.clickhouse.service;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.shinet.core.alert.adb.entity.DailyResultCheck;
import com.shinet.core.alert.adb.entity.DailyResultPlatformIncomeCheck;
import com.shinet.core.alert.adb.entity.RealtimeWithdrawTimeDistribution;
import com.shinet.core.alert.adb.mapper.DailyResultMapper;
import com.shinet.core.alert.adb.mapper.RealtimeWithdrawTimeDistributionMapper;
import com.shinet.core.alert.clickhouse.entity.*;
import com.shinet.core.alert.clickhouse.mapper.ck1.ClickHouseDwdMapper;
import com.shinet.core.alert.coreservice.entity.AlertRecord;
import com.shinet.core.alert.coreservice.enums.*;
import com.shinet.core.alert.coreservice.service.AlertDelayJobService;
import com.shinet.core.alert.coreservice.service.AlertRecordService;
import com.shinet.core.alert.dingtalk.DingTailService;
import com.shinet.core.alert.dingtalk.VedioAlertService;
import com.shinet.core.alert.util.DateUtils;
import com.xxl.job.core.log.XxlJobLogger;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import static java.math.BigDecimal.ROUND_HALF_UP;

@Slf4j
@Service
public class DailyResultService {

	@Resource
	private ClickHouseDwdMapper clickHouseDwdMapper;
	@Autowired
	AlertRecordService alertRecordService;
	@Resource
	private DailyResultMapper dailyResultMapper;

	/**
	 * description:
	 */
	public void startDailyResultCheck(){
		XxlJobLogger.log("产品日报预警任务开始");
		Integer count =  clickHouseDwdMapper.startDailyResultCheck(DateUtils.getBeforeDay(1),DateUtils.getBeforeDay(2));
		if (count > 0){
			XxlJobLogger.log("钉钉电话预警");
			String msg = "产品日报更新预警，与前一日相比某渠道收入低于百分之七十，不更新tebleau表";
			VedioAlertService.sendVocMsg("DAILY_RESULT_CHECK",msg,DingTailService.dailyData);
		}

		XxlJobLogger.log("产品日报，预警任务结束，count = " + count);

	}

	public void startDailyResultCheckDetail(){
		XxlJobLogger.log("产品日报预警任务开始");
		DailyResultCheck dailyResultCheck = dailyResultMapper.checkDailyResult(DateUtils.getBeforeDay(1), DateUtils.getBeforeDay(2));
		/*
		* 	总活跃 总新增  不能低于 百分之四十
			收入 0.7 各个大渠道的
			七点之后 每15分钟跑一次
		* */
		if (null != dailyResultCheck && (dailyResultCheck.getGapActivate() < 0.4
				|| dailyResultCheck.getGapActive() < 0.4
				|| dailyResultCheck.getGapChuanshanjia() < 0.7
				|| dailyResultCheck.getGapKuaishou() < 0.7
				|| dailyResultCheck.getGapGuangdiantong() < 0.7
				|| dailyResultCheck.getGapBaidu() < 0.7) ){
			XxlJobLogger.log("钉钉电话预警");
			String msg = "Mysql-产品日报，数据与昨日某渠道或总活跃或总新增数据比较，gap值偏下,数据有误";
			VedioAlertService.sendVocMsg("DAILY_RESULT_CHECK_DETAIL",msg,DingTailService.dailyData);
			//钉钉群消息预警
			String alert = "Mysql-产品日报，数据与昨日某渠道或总活跃或总新增数据比较，gap值偏下,数据有误";
			AlertRecord alertRecord = alertRecordService.insertAlertRecord(AlertJobDelayModel.DAILY_RESULT_INCOME_CHECK.getJobId(),
					AlertJobDelayModel.DAILY_RESULT_INCOME_CHECK.getJobName(), AlertModel.dailyIncome,alert
					, AlertStatus.INIT, DingTailService.dailyData, AlertType.DINGDING);
			DingTailService.sendMarkdownMsg(alertRecord, AlertRoute.JJSYJ.value);

		}

		XxlJobLogger.log("产品日报，mysql数据预警，detailGap = " + JSON.toJSON(dailyResultCheck));

	}


	public void realtimeResultHourIncomeCheck(){
		XxlJobLogger.log("实时日报预警任务开始");
		Integer count =  clickHouseDwdMapper.realtimeResultHourIncomeCheck(DateUtils.getBeforeDay(0),DateUtils.getBeforeDay(1));
		if (count > 0){
			XxlJobLogger.log("钉钉电话预警加消息预警");
			String msg = "实时日报预警，与前一日收入相比，某小时收入低于百分之四十";
			//电话预警
			VedioAlertService.sendVocMsg("REALTIME_RESULT_HOUR_CHECK",msg,DingTailService.dailyData);
			//钉钉群消息预警
			String alert = "实时日报预警，与前一日收入相比，某小时收入低于百分之四十";
			AlertRecord alertRecord = alertRecordService.insertAlertRecord(AlertJobDelayModel.REALTIME_RESULT_HOUR_CHECK.getJobId(),
					AlertJobDelayModel.REALTIME_RESULT_HOUR_CHECK.getJobName(), AlertModel.realtimeHour,alert
					, AlertStatus.INIT, DingTailService.dailyData, AlertType.DINGDING);
			DingTailService.sendMarkdownMsg(alertRecord, AlertRoute.JJSYJ.value);
		}

		XxlJobLogger.log("实时日报，预警任务结束，count = " + count);

	}

	public void dailyResultChannelIncomeCheck(){
		XxlJobLogger.log("产品日报渠道收入校验开始");
		DailyResultPlatformIncomeCheck check = clickHouseDwdMapper.dailyResultChannelIncomeCheck(DateUtils.getBeforeDay(1), DateUtils.getBeforeDay(2));
		/*
		 * 	各gap  不能低于 0.7
		 * */
		check.setGapIncome(0.3);
		if (null != check && (check.getGapIncome() < 0.7
				|| check.getGapAli() < 0.7
				|| check.getGapChuanshanjia() < 0.7
				|| check.getGapKuaishou() < 0.7
				|| check.getGapGuangdiantong() < 0.7
				|| check.getGapBaidu() < 0.7) ){
			XxlJobLogger.log("钉钉预警");
			//钉钉群消息预警
			String alert = "**产品日报更新预警，与前一日相比某渠道收入低于百分之七十，不更新tebleau表** \n\n";
			alert +="**"+check.getIncome()+"** : **前日**："+Math.round(check.getCkIncome())+ ", **昨日**："+Math.round(check.getYesIncome())+" , **gap** : "+ check.getGapIncome()+" \n\n";
			alert +="**"+check.getGuangdiantong()+"** : **前日**："+Math.round(check.getCkGuangdiantong())+ ", **昨日**："+Math.round(check.getYesGuangdiantong())+" , **gap** : "+ check.getGapGuangdiantong()+" \n\n";
			alert +="**"+check.getChuanshanjia()+"** : **前日**："+Math.round(check.getCkChuanshanjia())+ ", **昨日**："+Math.round(check.getYesChuanshanjia())+" , **gap** : "+check.getGapChuanshanjia()+" \n\n";
			alert +="**"+check.getKuaishou()+"** : **前日**："+Math.round(check.getCkKuaishou())+ ", **昨日**："+Math.round(check.getYesKuaishou())+" , **gap** : "+ check.getGapKuaishou()+" \n\n";
			alert +="**"+check.getBaidu()+"** : **前日**："+Math.round(check.getCkBaidu())+ ", **昨日**："+Math.round(check.getYesBaidu())+" , **gap** : "+ check.getGapBaidu()+" \n\n";
			alert +="**"+check.getAli()+"** : **前日**："+Math.round(check.getCkAli())+ ", **昨日**："+Math.round(check.getYesAli())+" , **gap** : "+ check.getGapAli()+" \n\n";

			AlertRecord alertRecord = alertRecordService.insertAlertRecord(AlertJobDelayModel.DAILY_RESULT_CHANNEL_INCOME_CHECK.getJobId(),
					AlertJobDelayModel.DAILY_RESULT_CHANNEL_INCOME_CHECK.getJobName(), AlertModel.dailyChannelIncome,alert
					, AlertStatus.INIT, DingTailService.dailyData, AlertType.DINGDING);
			DingTailService.sendMarkdownMsg(alertRecord, AlertRoute.JJSYJ.value);

		}

		XxlJobLogger.log("产品日报更新预警" + JSON.toJSON(check));

	}

}
