package com.shinet.core.alert.bpcommon.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import java.io.Serializable;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR>
 * @since 2021-11-26
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value="WechatOpenId对象", description="")
public class WechatOpenId implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(value = "open_id", type = IdType.INPUT)
    private String openId;

    private Long wechatId;

    private String unionId;

    private Long userId;

    private Long createTime;

    private Long updateTime;


}
