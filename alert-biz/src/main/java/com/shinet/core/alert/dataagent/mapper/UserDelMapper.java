package com.shinet.core.alert.dataagent.mapper;

import com.shinet.core.alert.dataagent.entity.UserDel;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Insert;
import org.apache.ibatis.annotations.Options;

/**
 * <p>
 * 用户基础信息表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2021-11-26
 */
public interface UserDelMapper extends BaseMapper<UserDel> {
    @Insert("insert into user_del " +
            "(id,union_id, photo_url, nick_name, state, os, brand, channel, device_id, app_version, os_version, rom_version, create_time, update_time) " +
            "values (" +
            "#{id},#{unionId},#{photoUrl},#{nickName},0, #{os}, #{brand}, #{channel}, #{deviceId}, #{appVersion}, #{osVersion}, #{romVersion}, #{createTime}, #{updateTime}" +
            ")")
    @Options(useGeneratedKeys = true)
    int insertUser(UserDel userEntity);
}
