package com.shinet.core.alert.clickhouse.enums;

/**
 * <AUTHOR>
 * @since 2023/11/7
 */
public enum Task {
    DAILY_RESULT("daily_result","每日日报"),
    DAILY_RESULT_CHANNEL("daily_result_channel","分渠道产品日报"),
    REALTIME_RESULT("realtime_result","实时毛利"),
    DAILY_RESULT_ACC("daily_result_accumulated","累计产品日报"),
    ;

    private String task;
    private String desc;

    Task(String task, String desc) {
        this.task = task;
        this.desc = desc;
    }

    public String getTask() {
        return task;
    }

    public void setTask(String task) {
        this.task = task;
    }

    public String getDesc() {
        return desc;
    }

    public void setDesc(String desc) {
        this.desc = desc;
    }


    public static Task getByTask(String task){
        for (Task taskRs : Task.values()){
            if (taskRs.task.equals(task)){
                return taskRs;
            }
        }
        return null;
    }
}
