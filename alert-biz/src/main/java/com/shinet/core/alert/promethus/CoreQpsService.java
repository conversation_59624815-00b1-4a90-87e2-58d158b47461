package com.shinet.core.alert.promethus;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.shinet.core.alert.common.HttpClientService;
import com.shinet.core.alert.coreservice.entity.AlertRecord;
import com.shinet.core.alert.coreservice.enums.*;
import com.shinet.core.alert.coreservice.service.AlertRecordService;
import com.shinet.core.alert.dingtalk.DingTailService;
import com.shinet.core.alert.dingtalk.VedioAlertService;
import com.shinet.core.alert.promethus.bean.Data;
import com.shinet.core.alert.promethus.bean.P99Jobj;
import com.shinet.core.alert.promethus.bean.Result;
import com.shinet.core.alert.util.BlackProjUtils;
import com.shinet.core.alert.util.DateUtils;
import com.shinet.core.alert.util.DoubleUtil;
import com.shinet.core.alert.util.InvalidUrlUtils;
import com.xxl.job.core.log.XxlJobLogger;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.directory.api.util.Strings;
import org.joda.time.DateTimeConstants;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import java.io.IOException;
import java.io.UnsupportedEncodingException;
import java.net.URLEncoder;
import java.util.*;
import java.util.stream.Collectors;

@Service
@Slf4j
public class CoreQpsService {
    @Autowired
    HttpClientService httpClientService;
    public static String reqBaseUrl = "http://172.16.38.235:30762/api/v1/query_range?query=";

    public static String reqBaseV2Url = "http://172.16.11.185:9090/api/v1/query_range?query=";
    int minTm = 5;

    @Autowired
    AlertRecordService alertRecordService;
    /**
     * gtSeconds  1;//20s
     * gtNumForAlert = 5;//半小时5min直接抛错
     * p99 半小时耗时查看
     */
    public void checkP99Cost(String reqUrl, String jobName,int gtSeconds,int gtMinForAlert){
        String promethusReqUrl = reqUrl+"max(pepper_summary_http_in_duration_seconds%7Bquantile%3D%220.99%22%2Capp!~%22rta-.%2B%22%7D)%20by%20(app)";
        long endSeconds = System.currentTimeMillis()/ 1000L;
        long startTimeSeconds = (System.currentTimeMillis()- DateTimeConstants.MILLIS_PER_MINUTE*minTm) / 1000L;

        promethusReqUrl = promethusReqUrl+"&start="+startTimeSeconds+"&end="+endSeconds+"&step=14&_="+(System.currentTimeMillis()/ 1000L);
        try {
            String alertMsg = reqMsgAlert(promethusReqUrl,gtSeconds,gtMinForAlert);
            if(StringUtils.isNotBlank(alertMsg)){
                VedioAlertService.sendVocMsg(" p99耗时  ", alertMsg);
                XxlJobLogger.log(alertMsg);
                AlertRecord alertRecord = alertRecordService.insertAlertRecord(jobName,"p99耗时", AlertModel.SERVICEQPS,alertMsg,
                        gtMinForAlert*1.0d,0d,0,0, AlertStatus.INIT,DingTailService.dset, AlertType.DINGPHONE
                        );
                DingTailService.sendMarkdownMsg(alertRecord);
            }
        }catch (Exception e){
            log.error("",e);
        }
    }

    /**
     *
     * @param maxQps 最大qps
     * @param gtNumForAlert 连续发生多少次
     */
    public void checkQps(String reqUrl,String jobName,int maxQps,int gtNumForAlert){
        String promethusReqUrl = reqUrl+"sum%20by(app)%20(rate(pepper_summary_http_in_duration_seconds_count%5B3m%5D))%20%3E0";
        long endSeconds = System.currentTimeMillis()/ 1000L;
        long startTimeSeconds = (System.currentTimeMillis()- DateTimeConstants.MILLIS_PER_MINUTE*minTm) / 1000L;

        promethusReqUrl = promethusReqUrl+"&start="+startTimeSeconds+"&end="+endSeconds+"&step=14&_="+(System.currentTimeMillis()/ 1000L);
//        promethusReqUrl= URLEncoder.encode(promethusReqUrl);
        try {
            String alertMsg = reqMsgAlert(promethusReqUrl,maxQps,gtNumForAlert,true);
            if(StringUtils.isNotBlank(alertMsg)){
                VedioAlertService.sendVocMsg(" qps  ", alertMsg);
                XxlJobLogger.log(alertMsg);

                AlertRecord alertRecord = alertRecordService.insertAlertRecord(jobName,"qps", AlertModel.SERVICEQPS,alertMsg,
                        gtNumForAlert*1.0d,0d,0,0, AlertStatus.INIT,DingTailService.dset, AlertType.DINGPHONE
                );
                DingTailService.sendMarkdownMsg(alertRecord);
            }
        }catch (Exception e){
            log.error("",e);
        }
    }

    /**
     *
     * @param maxCurrent
     * @param gtNumForAlert
     */
    public void checkCurReq(String reqUrl,String jobName,int maxCurrent,int gtNumForAlert){
        //String promethusReqUrl = reqUrl+"sum%20(pepper_gauge_http_in_concurrent%7B%20offset%201m%20)%20by%20(app)";
        String promethusReqUrl = reqUrl+"sum%20(pepper_gauge_http_in_concurrent%7Bapp!~%22rta.%2B%22%7D%20%20offset%201m)%20by(app)";
        long endSeconds = System.currentTimeMillis()/ 1000L;
        long startTimeSeconds = (System.currentTimeMillis()- DateTimeConstants.MILLIS_PER_MINUTE*minTm) / 1000L;

//        long endSeconds = (DateUtils.parse("2021-11-27 20:03:00",DateUtils.PATTERN_YHMS).getTime()/ 1000L);
//        long startTimeSeconds = (DateUtils.parse("2021-11-27 20:03:00",DateUtils.PATTERN_YHMS).getTime()- DateTimeConstants.MILLIS_PER_MINUTE*minTm) / 1000L;

        promethusReqUrl = promethusReqUrl+"&start="+startTimeSeconds+"&end="+endSeconds+"&step=14&_="+(System.currentTimeMillis()/ 1000L);
//        promethusReqUrl= URLEncoder.encode(promethusReqUrl);
        try {
            String alertMsg = reqMsgAlert(promethusReqUrl,maxCurrent,gtNumForAlert);
            if(StringUtils.isNotBlank(alertMsg)){
                VedioAlertService.sendVocMsg(" http_in_concurrent-并发  ", alertMsg);
                XxlJobLogger.log(alertMsg);

                AlertRecord alertRecord = alertRecordService.insertAlertRecord(jobName,"http_in_concurrent-并发", AlertModel.SERVICEQPS,alertMsg,
                        gtNumForAlert*1.0d,0d,0,0, AlertStatus.INIT,DingTailService.dset, AlertType.DINGPHONE
                );
                DingTailService.sendMarkdownMsg(alertRecord);
            }
        }catch (Exception e){
            log.error("",e);
        }
    }


    /**
     *
     * @param maxCurrent
     * @param gtNumForAlert
     */
    public void checkV2CurReq(String jobName,int maxCurrent,int gtNumForAlert){
        //sum by(application) (rate(http_server_requests_seconds_sum [5m]) ) >0&start=1630154094.556&end=1630157694.556&step=14&_=1630156719497
        long endSeconds = System.currentTimeMillis()/ 1000L;
        long startTimeSeconds = (System.currentTimeMillis()- DateTimeConstants.MILLIS_PER_MINUTE*minTm) / 1000L;
        String promethusReqUrl = reqBaseV2Url+"sum%20by(application)%20(rate(http_server_requests_seconds_sum[5m]))%20%3E0";
        promethusReqUrl = promethusReqUrl+"&start="+startTimeSeconds+"&end="+endSeconds+"&step=14&_="+(System.currentTimeMillis()/ 1000L);
        try {
            String alertMsg = reqMsgAlert(promethusReqUrl,maxCurrent,gtNumForAlert);
            if(StringUtils.isNotBlank(alertMsg)){
                VedioAlertService.sendVocMsg(" http_in_concurrent-并发  ", alertMsg);
                XxlJobLogger.log(alertMsg);
                AlertRecord alertRecord = alertRecordService.insertAlertRecord(jobName,"http_in_concurrent-并发", AlertModel.SERVICEQPS,alertMsg,
                        gtNumForAlert*1.0d,0d,0,0, AlertStatus.INIT,DingTailService.dset, AlertType.DINGPHONE
                );
                DingTailService.sendMarkdownMsg(alertRecord);
            }
        }catch (Exception e){
            log.error("",e);
        }
    }

    public void checkV2Qps(String jobName,int maxQps,int gtNumForAlert){
        String promethusReqUrl = reqBaseV2Url+"sum%20by(application)(increase(http_server_requests_seconds_count[5m])/300)%20%3E0";
        long endSeconds = System.currentTimeMillis()/ 1000L;
        long startTimeSeconds = (System.currentTimeMillis()- DateTimeConstants.MILLIS_PER_MINUTE*minTm) / 1000L;

        promethusReqUrl = promethusReqUrl+"&start="+startTimeSeconds+"&end="+endSeconds+"&step=14&_="+(System.currentTimeMillis()/ 1000L);
//        promethusReqUrl= URLEncoder.encode(promethusReqUrl);
        try {
            String alertMsg = reqMsgAlert(promethusReqUrl,maxQps,gtNumForAlert,true);
            if(StringUtils.isNotBlank(alertMsg)){
                VedioAlertService.sendVocMsg(" qps  ", alertMsg);
                XxlJobLogger.log(alertMsg);

                AlertRecord alertRecord = alertRecordService.insertAlertRecord(jobName,"http_in_concurrent-并发", AlertModel.SERVICEQPS,alertMsg,
                        gtNumForAlert*1.0d,0d,0,0, AlertStatus.INIT,DingTailService.dset, AlertType.DINGPHONE
                );
                DingTailService.sendMarkdownMsg(alertRecord);
            }
        }catch (Exception e){
            log.error("",e);
        }
    }


    public void checkCoreServiceDoubleTimeQps(String reqUrl,String jobName,float dfv,boolean isV2){
        long curMils = System.currentTimeMillis();
        String promethusReqUrl = reqUrl+"sum%20by(app)%20(rate(pepper_summary_http_in_duration_seconds_count%5B3m%5D))%20%3E0";
        if(isV2){
            promethusReqUrl = reqUrl+"sum%20by(application)(increase(http_server_requests_seconds_count[5m])/300)%20%3E0";
        }
        long endSeconds = curMils/ 1000L;
        long startTimeSeconds = (curMils- DateTimeConstants.MILLIS_PER_MINUTE*minTm) / 1000L;

        String promethusReqUrlStr = promethusReqUrl+"&start="+startTimeSeconds+"&end="+endSeconds+"&step=14&_="+(System.currentTimeMillis()/ 1000L);

        Map<String,Double> startDoubleMap = reqMsgToMap(promethusReqUrlStr);


        long lastDayEndSeconds = (curMils - DateTimeConstants.MILLIS_PER_DAY)/ 1000L;
        long lastDayStartTimeSeconds = (curMils - DateTimeConstants.MILLIS_PER_DAY - DateTimeConstants.MILLIS_PER_MINUTE*minTm) / 1000L;
        String promethusReqEndUrl = promethusReqUrl+"&start="+lastDayStartTimeSeconds+"&end="+lastDayEndSeconds+"&step=14&_="+(System.currentTimeMillis()/ 1000L);
        Map<String,Double> endDoubleMap = reqMsgToMap(promethusReqEndUrl);

        List<String> alertMsgList = new ArrayList<>();

        startDoubleMap.forEach((app,dvalue)->{
            if(!BlackProjUtils.isInBlack(app)){
                Double lastValue = DoubleUtil.getDoubleByZero(endDoubleMap.get(app));
                dvalue = DoubleUtil.getDoubleByZero(dvalue);
                Double drate = DoubleUtil.divideDouble(dvalue,lastValue);
                XxlJobLogger.log(app+" 今天： "+ dvalue+" 昨天： "+lastValue+","+drate+","+(1d-drate));
                if(lastValue==0){
                    lastValue = 1d;
                }
                if(!"core-caf-dispense".equalsIgnoreCase(app) &&
                        !"core-dispense".equalsIgnoreCase(app) &&
                        !"core-rta-tx".equalsIgnoreCase(app) &&
                        !"user-event-gateway".equalsIgnoreCase(app) &&
                        !"user-event".equalsIgnoreCase(app)&&
                        !"bp-ap-business-gateway".equalsIgnoreCase(app)&&
                   !"data-exposure".equalsIgnoreCase(app) && !"core-exposure".equalsIgnoreCase(app)
                ){
                    if(dvalue<lastValue && (dvalue>10 || lastValue>10)){
                        if((dvalue<100 && lastValue<100)){
                            if(DoubleUtil.subtractionDouble(1d,drate)>less50Rate){
                                alertMsgList.add(app + " qps->{"+dvalue+","+lastValue+","+drate+","+DoubleUtil.subtractionDouble(1d,drate)+"}");
                            }
                        }else if((1d-(dvalue/lastValue))>dfv && !"bp-user-service".equalsIgnoreCase(app)){
                            alertMsgList.add(app + " eqps->{"+dvalue+","+lastValue+","+drate+","+(1d-drate)+"}");
                        }
                    }
                }
            }
        });

        String alertMsg = alertMsgList.stream().collect(Collectors.joining(","));

        log.info(alertMsg);

        if(StringUtils.isNotBlank(alertMsg)){
            VedioAlertService.sendVocMsg(" checkCoreServiceDoubleTimeQps  ", alertMsg);
            log.info("预警 "+alertMsg);
            XxlJobLogger.log("预警 "+alertMsg);

            AlertRecord alertRecord = alertRecordService.insertAlertRecord(jobName,"checkCoreServiceDoubleTimeQps", AlertModel.SERVICEQPS,alertMsg,
                    dfv*1.0d,0d,0,0, AlertStatus.INIT,DingTailService.dset, AlertType.DINGPHONE
            );
            DingTailService.sendMarkdownMsg(alertRecord);
        }
    }

    float less50Rate = 0.7f;
    public void checkDoubleTimeQps(String jobName,float dfv){
        long curMils = System.currentTimeMillis();
        String promethusReqUrl = reqBaseV2Url+"sum%20by(application)(increase(http_server_requests_seconds_count[5m])/300)%20%3E0";
        long endSeconds = curMils/ 1000L;
        long startTimeSeconds = (curMils- DateTimeConstants.MILLIS_PER_MINUTE*minTm) / 1000L;

        String promethusReqUrlStr = promethusReqUrl+"&start="+startTimeSeconds+"&end="+endSeconds+"&step=14&_="+(System.currentTimeMillis()/ 1000L);

        Map<String,Double> startDoubleMap = reqMsgToMap(promethusReqUrlStr);


        long lastDayEndSeconds = (curMils - DateTimeConstants.MILLIS_PER_DAY)/ 1000L;
        long lastDayStartTimeSeconds = (curMils - DateTimeConstants.MILLIS_PER_DAY - DateTimeConstants.MILLIS_PER_MINUTE*minTm) / 1000L;
        String promethusReqEndUrl = promethusReqUrl+"&start="+lastDayStartTimeSeconds+"&end="+lastDayEndSeconds+"&step=14&_="+(System.currentTimeMillis()/ 1000L);
        Map<String,Double> endDoubleMap = reqMsgToMap(promethusReqEndUrl);

        List<String> alertMsgList = new ArrayList<>();

        startDoubleMap.forEach((app,dvalue)->{
            if(!BlackProjUtils.isInBlack(app)){
                Double lastValue = DoubleUtil.getDoubleByTwo(endDoubleMap.get(app));
                dvalue = DoubleUtil.getDoubleByTwo(dvalue);
                Double drate = DoubleUtil.divideDouble(dvalue,lastValue);
                XxlJobLogger.log(app+"->{"+dvalue+","+lastValue+","+drate+"}");
                if(lastValue==0){
                    lastValue = 1d;
                }
                if(!"core-caf-dispense".equalsIgnoreCase(app) &&
                        !"user-event-gateway".equalsIgnoreCase(app)&&
                        !"user-event".equalsIgnoreCase(app)){
                    //"core-caf-dispense" 不穩定
                    if(dvalue<lastValue && (dvalue>10 || lastValue>10)){
                        if((dvalue<100 && lastValue<100)){
                            if(DoubleUtil.subtractionDouble(1d,drate)>less50Rate){
                                alertMsgList.add(app + " qps->{"+dvalue+","+lastValue+","+drate+"}");
                            }
                        }else  if((1d-drate)>dfv){
                            alertMsgList.add(app + " qps->{"+dvalue+","+lastValue+","+drate+"}");
                        }
                    }
                }
            }
        });

        String alertMsg = alertMsgList.stream().collect(Collectors.joining(","));

        log.info(alertMsg);

        if(StringUtils.isNotBlank(alertMsg)){
            AlertRecord alertRecord = alertRecordService.insertAlertRecord(jobName,"doubleDayQps", AlertModel.SERVICEQPS,alertMsg,
                    dfv*1.0d,0d,0,0, AlertStatus.INIT,DingTailService.dset, AlertType.DINGDING
            );
            XxlJobLogger.log(alertMsg);
            DingTailService.sendMarkdownMsg(alertRecord);
        }
    }
    public Map<String,Double> reqMsgToMap(String promethusReqUrl) {

        Map<String,Double> dmap = new HashMap<>();
        try {
            String msgProme = httpClientService.sendReq(promethusReqUrl,new HashMap<>());

            P99Jobj p99Jobj = JSON.parseObject(msgProme, P99Jobj.class);

            Data data = p99Jobj.getData();
            List<Result> drlist = data.getResult();
            for(Result result : drlist){
                try {
                    String app = result.getMetric().getApp();
                    if(StringUtils.isBlank(app)){
                        app = result.getMetric().getApplication();
                    }

                    if(BlackProjUtils.isInBlack(app)){
                        continue;
                    }
                    List<List<Double>> timeCostList = result.getValues();
                    int appGtNum = 0;
                    Double dvalueSum = 0d;
                    for(List<Double> timeCost : timeCostList){
                        long timeSeconds = timeCost.get(0).longValue();
                        Double dvalue = timeCost.get(1);
                        dvalueSum = dvalueSum + dvalue;
                        appGtNum = appGtNum + 1;
                    }
                    if(dvalueSum>0 && appGtNum>0){
                        dmap.put(app,(dvalueSum/appGtNum));// 均值
                    }
                }catch (Exception e){
                    log.error("",e);
                    XxlJobLogger.log(e);
                }
            }

        }catch (Exception e){
            log.error("",e);
        }

        return dmap;
    }

    public String reqMsgAlert(String promethusReqUrl,int gtMax,int gtMin) throws IOException{
        return reqMsgAlert(promethusReqUrl,gtMax,gtMin,false);
    }

    public String reqMsgAlert(String promethusReqUrl,int gtMax,int gtMin,boolean isCheckQps) throws IOException {
        String msgProme = httpClientService.sendReq(promethusReqUrl,new HashMap<>());

        P99Jobj p99Jobj = JSON.parseObject(msgProme, P99Jobj.class);

        Data data = p99Jobj.getData();
        List<Result> drlist = data.getResult();
        String alertMsg = "";
        for(Result result : drlist){
            try {
                String app = result.getMetric().getApp();
                if(StringUtils.isBlank(app)){
                    app = result.getMetric().getApplication();
                }

                if(BlackProjUtils.isInBlack(app)){
                    continue;
                }
                // RTA调整阈值
                if (isCheckQps && app.contains("rta")){
                    gtMax = 400000;
                }

                // RTA去除qps报警
                if (isCheckQps && app.contains("rta")){
                    continue;
                }

                log.info("app="+app);
                List<List<Double>> timeCostList = result.getValues();
                int appGtNum = 0;
                String appMsg = "";
                int cnum = 0;
                long gtSeconds = 0;
                long qianSeconds = 0L;
                for(List<Double> timeCost : timeCostList){
                    long timeSeconds = timeCost.get(0).longValue();
                    Double costTime = DoubleUtil.getDoubleByZero(timeCost.get(1));
                    if(costTime>gtMax){
                        appGtNum = appGtNum + 1;
                        Date date = new Date(timeSeconds*1000);
                        cnum++;
                        if(qianSeconds>0){
                            gtSeconds = gtSeconds + (timeSeconds-qianSeconds) ;
                        }
                        appMsg = appMsg + DateUtils.formatDateForHMSD(date)+"("+costTime.intValue()+") ";
                    }
                    qianSeconds = timeSeconds;
                }



                if(cnum>0 && StringUtils.isNotBlank(appMsg)){
                    appMsg = "app:"+app +" 持续时间："+(gtSeconds)/60+" min " + StringUtils.substring(appMsg,0,50);
                }

                if(gtSeconds>=gtMin* DateTimeConstants.SECONDS_PER_MINUTE){
                    alertMsg = alertMsg +"\n"+ appMsg;
                }
            }catch (Exception e){
                log.error("",e);
                XxlJobLogger.log(e);
            }
        }

        return alertMsg;
    }

    private static final List<String> checkUrlPro = Arrays.asList("core-dispense", "safe-api", "bp-ap-gateway", "rta-byte");

    public void checkHtppN200(){
        String queryTr = "sum by(app) (increase(pepper_summary_http_status_in_duration_seconds_count{ url!=\"/actuator/health/\",url!=\"/xxx/\",url!=\"/actuator/prometheus/\",status!~\"2..\"}[2m]))";
        //不好所有404都排除，先检查url吧
        String queryUrlTr = "sum by(app, url) (increase(pepper_summary_http_status_in_duration_seconds_count{ url!=\"/actuator/health/\",url!=\"/xxx/\",url!=\"/actuator/prometheus/\",status!~\"2..\"}[5m])) > 0";
        long endSeconds = System.currentTimeMillis()/ 1000L;
        long startTimeSeconds = (System.currentTimeMillis()- DateTimeConstants.MILLIS_PER_MINUTE*minTm) / 1000L;
        try {
            String encodedQuery = URLEncoder.encode(queryTr, "UTF-8");
            String preQuery = encodedQuery+"&start="+startTimeSeconds+"&end="+endSeconds+"&step=14";
            String msgProme = httpClientService.sendReq(reqBaseUrl+preQuery,new HashMap<>());
            checkAndSendHttpNo200(msgProme);
            String msgPromeV2 = httpClientService.sendReq(reqBaseV2Url+preQuery,new HashMap<>());
            checkAndSendHttpNo200(msgPromeV2);
            String encodedUrlQuery = URLEncoder.encode(queryUrlTr, "UTF-8");
            String preUrlQuery = encodedUrlQuery+"&start="+startTimeSeconds+"&end="+endSeconds+"&step=14";
            String urlPromeV2 = httpClientService.sendReq(reqBaseV2Url+preUrlQuery,new HashMap<>());
            checkUrlAndSendHttpNo200(urlPromeV2);
        }catch (Exception e){
            log.error("Query Ex:",e);
        }
    }

    private void checkAndSendHttpNo200(String resp){

        if (Strings.isNotEmpty(resp)){
            JSONObject jsonObject = JSONObject.parseObject(resp);
            JSONArray array = jsonObject.getJSONObject("data").getJSONArray("result");

            List<JSONObject> exPushList = new ArrayList<>();
            for (Object jr : array) {

                JSONObject metric = (JSONObject) jr;
                String app = metric.getJSONObject("metric").getString("app");
                JSONArray values = metric.getJSONArray("values");
                double defaultEr5m = 500d;
                if ("data-product".equals(app)){
                    defaultEr5m = 10000d;
                }else if ("rta-byte".equals(app)){
                    defaultEr5m = 30000d;
                }else if (checkUrlPro.contains(app)) {
                    continue;
                }
                double currentEr5m = defaultEr5m;
                List<List<Object>> exList = values.stream()
                        .map(r -> JSON.parseArray(JSON.toJSONString(r),Object.class))
                        .filter(r -> Double.parseDouble(r.get(1).toString()) > currentEr5m)
                        .collect(Collectors.toList());

                if (exList.size() > 0 && !BlackProjUtils.isInBlack(app)){

                    JSONObject exPush = new JSONObject();
                    exPush.put("app",app);
                    exPush.put("not2xxCount",exList.get(0).get(1));
                    exPushList.add(exPush);
                }
            }

            if (exPushList.size() > 0){
                XxlJobLogger.log(JSON.toJSONString(exPushList));
                String alert = exPushList.stream()
                        .map(r -> String.format("应用：**%s**,过去5m,avg Http not2xx个数：**%s**", r.getString("app"),
                                r.getDouble("not2xxCount").intValue()))
                        .collect(Collectors.joining(";\n\n"));
                AlertRecord alertRecord = alertRecordService.insertAlertRecord(AlertJobDelayModel.HTTP_NOT_2xx.getJobId(),
                        AlertJobDelayModel.HTTP_NOT_2xx.getJobName(), AlertModel.SERVICEREQ, alert
                        , AlertStatus.INIT, DingTailService.dset, AlertType.DINGDING);
                DingTailService.sendMarkdownMsg(alertRecord, AlertRoute.JISHU.value);
                VedioAlertService.sendVocMsg(AlertJobDelayModel.HTTP_NOT_2xx.getJobName(), alert);
            }
        }
    }

    private void checkUrlAndSendHttpNo200(String resp) {
        if (Strings.isNotEmpty(resp)){
            JSONObject jsonObject = JSONObject.parseObject(resp);
            JSONArray array = jsonObject.getJSONObject("data").getJSONArray("result");

            Map<String, Double> appValue = new HashMap<>();
            Map<String, String> appUrl = new HashMap<>();
            //合并app的值
            for (Object jr : array) {
                JSONObject metric = (JSONObject) jr;
                String app = metric.getJSONObject("metric").getString("app");
                String url = metric.getJSONObject("metric").getString("url");
                JSONArray values = metric.getJSONArray("values");
                if (!checkUrlPro.contains(app)) {
                    continue;
                }

                if (!InvalidUrlUtils.isInvalid(app, url)){
                    appValue.merge(app, values.getJSONArray(0).getDoubleValue(1), Double::sum);
                    appUrl.merge(app, url, (o1, o2) -> o1 + "," + o2);
                }
            }

            if (!appValue.isEmpty()){
                XxlJobLogger.log(JSON.toJSONString(appValue));
                List<String> values = new ArrayList<>(appValue.size());
                for(Map.Entry<String, Double> map : appValue.entrySet()) {
                    if(map.getValue() > 500d) {
                        values.add(String.format("应用：**%s**,过去5m,avg Http not2xx个数：**%s**", map.getKey(), map.getValue().intValue()));
                        log.info("应用:{}, 过去5m,avg Http not2xx个数:{}, urls:{}", map.getKey(), map.getValue().intValue(), appUrl.get(map.getKey()));
                    }
                }
                if(!values.isEmpty()) {
                    String alert = StringUtils.join(values, ";\n\n");
                    AlertRecord alertRecord = alertRecordService.insertAlertRecord(AlertJobDelayModel.HTTP_NOT_2xx.getJobId(),
                            AlertJobDelayModel.HTTP_NOT_2xx.getJobName(), AlertModel.SERVICEREQ, alert
                            , AlertStatus.INIT, DingTailService.dset, AlertType.DINGDING);
                    DingTailService.sendMarkdownMsg(alertRecord, AlertRoute.JISHU.value);
                    VedioAlertService.sendVocMsg(AlertJobDelayModel.HTTP_NOT_2xx.getJobName(), alert);
                }
            }
        }
    }
}
