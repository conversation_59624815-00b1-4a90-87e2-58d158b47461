package com.shinet.core.alert.safe.service;


import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;

import com.shinet.core.alert.safe.entity.AndroidLockConf;
import com.shinet.core.alert.safe.mapper.AndroidLockConfMapper;
import lombok.extern.slf4j.Slf4j;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import java.util.Date;
import java.util.List;

/**
* <p>
    * 锁区IOS特殊策略 服务实现类
    * </p>
*
* <AUTHOR>
* @since 2024-03-15
*/
@Service
@Slf4j
public class AndroidLockConfService extends ServiceImpl<AndroidLockConfMapper, AndroidLockConf> {

    @Autowired
    private AndroidLockConfMapper androidLockConfMapper;

    int disableAllLock(String product, String lockPkgs, String lockVersions){
        return androidLockConfMapper.disableAllLock(product, lockPkgs, lockVersions, new Date());
    }

    int disableAllLockByChannel(String product, String lockPkgs, String newLockPkgs, String lockVersions){
        return androidLockConfMapper.disableAllLockByChannel(product, lockPkgs, lockVersions, newLockPkgs, new Date());
    }

    String getAllLockPkgs(String product, String lockPkgs, String lockVersions){
        return androidLockConfMapper.getAllLockPkgs(product, lockPkgs, lockVersions);
    }



}
