package com.shinet.core.alert.safe.mapper;

import com.shinet.core.alert.dsp.entity.DspCostAlertBean;
import com.shinet.core.alert.safe.entity.AndoridALock;
import com.shinet.core.alert.safe.entity.AndoridLockLook;
import com.shinet.core.alert.safe.entity.IosCheckUrl;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * <p>
 *  Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2023-11-16
 */
public interface IosCheckUrlMapper extends BaseMapper<IosCheckUrl> {
    @Select({"      select * from (  select product,lock_flag,count(DISTINCT user_id) as lockUnum,count(DISTINCT oaid) lockOaidNum,count(DISTINCT ip) lockIpNum             " +
            "      from android_lock_rst where lock_flag='true' and channel not like 'ksdr%'   GROUP BY product,lock_flag                                                  " +
            "      )a LEFT JOIN (select product,lock_flag,count(DISTINCT user_id) as unlockUnum,count(DISTINCT oaid) unlockOaidNum,count(DISTINCT ip) unlockIpNum          " +
            "      from android_lock_rst where lock_flag='false'  GROUP BY product,lock_flag                                                                               " +
            "      ) b on a.product=b.product order by lockIpNum desc                                                                                                      " +
            "      ; "})
    public List<AndoridLockLook> getLockInfos();


    /**
     * 分商店锁区预警
     * @return
     */
    @Select({"select store_name as product,count(1) as lockUnum,count(DISTINCT oaid) lockOaidNum,count(DISTINCT ip) lockIpNum  from android_lock_rst where   lock_flag='true'  and store_name in ('huawei','honor','mi','vivo','oppo') GROUP BY store_name ; "})
    public List<AndoridLockLook> getLockOaidNums();


    @Select({"select product,channel,app_version,count(DISTINCT ip) as lockIpNum from android_lock_rst where remark2='android首层锁区' GROUP BY product,channel,app_version ;"})
    public AndoridALock getAlLockAlt();


}
