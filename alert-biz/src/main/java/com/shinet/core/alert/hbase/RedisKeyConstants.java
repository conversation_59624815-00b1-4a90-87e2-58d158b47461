package com.shinet.core.alert.hbase;

/**
 * (。≖ˇェˇ≖。)
 * redis key 常数类
 * @author: zkk
 * DateTime: 2020/8/7 17:59
 */
public class RedisKeyConstants {

	/**
	 * click 事件 mac key
	 */
	public static final String REMOVE_EVENT_LOCK ="REMOVE_EVENT_LOCK";
	public static final String REMOVE_CLIKC_LOCK ="REMOVE_CLICK_LOCK";
	/**
	 * click 事件 mac key
	 */
	public static final String CLICK_MAC_PRE ="ck:mac:%s:%s:%s";
	/**
	 * click 事件 androidId key
	 */
	public static final String CLICK_ANDROID_ID_PRE ="ck:android:id:%s:%s:%s";
	/**
	 * click 事件 oaid key
	 */
	public static final String CLICK_OAID_PRE ="ck:od:%s:%s:%s";
	/**
	 * click 事件 oaid2 key
	 */
	public static final String CLICK_OAID2_PRE ="ck:od2:%s:%s:%s";
	/**
	 * click事件 deviceId key（idfa和安卓的设备号）
	 */
	public static final String CLICK_DEVICE_PRE ="ck:did:%s:%s:%s";

	public static final String  REPORT_LOCK_KEY= "report:lock:%s:%s:%d";

	/**
	 * 创建ocpc表
	 */
	public static final String CREATE_OCPC_TABLE_LOCK_KEY = "crt_ocpc_table_lock";
	/**
	 * click 事件 androidId key
	 */
	public static final String CLICK_ANDROID_ID_PRE_PKG ="ck:android:id:%s:%s:%s:%s";
	/**
	 * click 事件 mac key
	 */
	public static final String CLICK_MAC_PRE_PKG ="ck:mac:%s:%s:%s:%s";
	/**
	 * click 事件 oaid key
	 */
	public static final String CLICK_OAID_PRE_PKG ="ck:od:%s:%s:%s:%s";
	/**
	 * click 事件 oaid key
	 */
	public static final String CLICK_OAID2_PRE_PKG ="ck:od2:%s:%s:%s:%s";
	/**
	 * click事件 deviceId key（idfa和安卓的设备号）
	 */
	public static final String CLICK_DEVICE_PRE_PKG ="ck:did:%s:%s:%s:%s";


	/**
	 * click事件 deviceId key（idfa和安卓的设备号）
	 */
	public static final String EVENT_DEVICE_ACTIVE ="event:active:%s:%s:%s:%s";

	public static final String EVENT_COMPENSATE_LOCK = "event2:compensate:lock";
	public static final String VIVO_CLICK_LOCK = "vivo:click:lock";

	public static final String EVENT_ACTIVE_USERID = "event:useractive:%s:%s:%s";

	public  static String getActiveUserKey( String product,String os,String userId){
		return String.format(EVENT_ACTIVE_USERID,product,os,userId);
	}
	public  static String getActiveEventKey( String product,String os,String type,String strId){
		return String.format(EVENT_DEVICE_ACTIVE,product,os,type,strId);
	}

	public static final String KEY_EVENT_FORMATTER = "key:event:%s:%s:%s:%s";

	public static final String KEY_ACTION_EXT_EVENT_LIST_FORMATTER = "key:ext_event_list:%s:%s:%s";

	public static final String SYNC_PKG_TO_PRODUCT_TO_OCPC_KEY = "key:sync:pkgToProduct";

	public static final String SYNC_PRODUCT_TO_OCPC_KEY = "key:sync:product";

	public static final String USER_ACC_ARPU_LOCK_KEY = "key:lock:user_acc_arpu:%s:%s:%s";

	public static final String USER_ACC_ARPU_FORMATTER = "key:user_acc_arpu:%s:%s:%s";

	public static String  getClickAndroidIdKey( String product,String os, String androidId) {
		return String.format(CLICK_ANDROID_ID_PRE,product,os,androidId);
	}

	public static String  getClickMacKey( String product,String os, String mac) {
		return String.format(CLICK_MAC_PRE,product,os,mac);
	}

	public static String getClickOaIdKey(String product, String os,String oaId) {
		return String.format(CLICK_OAID_PRE,product,os,oaId);
	}

	public static String getClickOaId2Key(String product, String os,String oaId2) {
		return String.format(CLICK_OAID2_PRE,product,os,oaId2);
	}

	public static String getClickDeviceKey(String product, String os,String deviceId) {
		return String.format(CLICK_DEVICE_PRE,product,os,deviceId);
	}


	public static String  getClickAndroidIdKeyByPkgChannel( String product,String os, String androidId,String pkgChannel) {
		return String.format(CLICK_ANDROID_ID_PRE_PKG,product,os,androidId,pkgChannel);
	}

	public static String  getClickMacKeyByPkgChannel( String product,String os, String mac,String pkgChannel) {
		return String.format(CLICK_MAC_PRE_PKG,product,os,mac,pkgChannel);
	}

	public static String getClickOaIdKeyByPkgChannel(String product, String os,String oaId,String pkgChannel) {
		return String.format(CLICK_OAID_PRE_PKG,product,os,oaId,pkgChannel);
	}

	public static String getClickOaId2KeyByPkgChannel(String product, String os,String oaId,String pkgChannel) {
		return String.format(CLICK_OAID2_PRE_PKG,product,os,oaId,pkgChannel);
	}

	public static String getClickDeviceKeyByPkgChannel(String product, String os,String deviceId,String pkgChannel) {
		return String.format(CLICK_DEVICE_PRE_PKG,product,os,deviceId,pkgChannel);
	}

	public static String getReportLockKey(String product, String userId, int eventType) {
		return String.format(REPORT_LOCK_KEY, product, userId, eventType);
	}

	public static String getUserAccArpuLockKey(String product, String os, String userId) {
		return String.format(USER_ACC_ARPU_LOCK_KEY, product, os, userId);
	}

}
