package com.shinet.core.alert.safe.mapper;

import com.shinet.core.alert.safe.entity.IosLockStatsVo;
import com.shinet.core.alert.safe.entity.LockIosRst;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * <p>
 *  Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2023-11-09
 */
public interface LockIosRstMapper extends BaseMapper<LockIosRst> {

    /**
     * 查询今天的iOS锁区统计数据
     * 同时统计CAID和IP两个维度的锁定情况
     *
     * @return 按产品分组的锁区统计数据，按锁定比率降序排列
     */
    @Select({
        "SELECT",
        "    product,",
        "    COUNT(DISTINCT CASE WHEN lock_flag = 'true' THEN caid END) AS lockCaidNum,",
        "    COUNT(DISTINCT caid) AS totalCaidNum,",
        "    COUNT(DISTINCT CASE WHEN lock_flag = 'true' THEN ip END) AS lockIpNum,",
        "    COUNT(DISTINCT ip) AS totalIpNum",
        "FROM lock_ios_rst",
        "WHERE create_time >= CURDATE()",
        "    AND product IS NOT NULL",
        "GROUP BY product",
        "HAVING totalCaidNum > 0 and lockCaidNum >= #{minLockNum}",
        "ORDER BY product"
    })
    List<IosLockStatsVo> queryLockStatsToday(@Param("minLockNum") Integer minLockNum);

}
