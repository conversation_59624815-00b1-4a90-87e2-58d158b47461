package com.shinet.core.alert.dsp.service;

import com.google.common.collect.Sets;
import com.shinet.core.alert.coreservice.entity.AlertRecord;
import com.shinet.core.alert.coreservice.enums.AlertModel;
import com.shinet.core.alert.coreservice.enums.AlertStatus;
import com.shinet.core.alert.coreservice.enums.AlertType;
import com.shinet.core.alert.coreservice.service.AlertRecordService;
import com.shinet.core.alert.dingtalk.DingTailService;
import com.shinet.core.alert.dingtalk.VedioAlertService;
import com.shinet.core.alert.dsp.entity.AppActiveAlertBean;
import com.shinet.core.alert.dsp.entity.ToutiaoReportIdeaday;
import com.shinet.core.alert.dsp.mapper.ToutiaoReportIdeadayMapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.shinet.core.alert.util.DateUtils;
import com.shinet.core.alert.util.DoubleUtil;
import com.xxl.job.core.log.XxlJobLogger;
import org.apache.commons.lang3.StringUtils;
import org.joda.time.DateTimeConstants;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

/**
* <p>
    *  服务实现类
    * </p>
*
* <AUTHOR>
* @since 2021-08-09
*/
@Service
public class ToutiaoReportIdeadayService extends ServiceImpl<ToutiaoReportIdeadayMapper, ToutiaoReportIdeaday> {
    @Autowired
    ToutiaoReportIdeadayMapper toutiaoReportIdeadayMapper;
    @Autowired
    AlertRecordService alertRecordService;

    private Set<String> dset = Sets.newHashSet(new String[]{
            "qzlgdxg"
    });
    private int oaLimit = 2000;
    public void  activeAlert(String jobName,double alertRate,double bigAlertRate){
        Date date = new Date();
        if(date.getHours()<2 && date.getMinutes()<20){
            XxlJobLogger.log("00 不做预警");
            return;
        }
        List<AppActiveAlertBean>  appActiveAlertBeans = toutiaoReportIdeadayMapper.queryActiveGap();
        appActiveAlertBeans = appActiveAlertBeans.stream().filter(a->!dset.contains(a.getProduct())).collect(Collectors.toList());
        String alertMsg = jisRateMsg(appActiveAlertBeans,alertRate,bigAlertRate);
        String tmsg = "头条ocpc激活异常 "+alertRate+"\n"+alertMsg;
        XxlJobLogger.log(tmsg);
        if(StringUtils.isNotBlank(alertMsg)){
            XxlJobLogger.log(tmsg);
            AlertRecord alertRecord =alertRecordService.insertAlertRecord(jobName,"头条ocpc激活异常", AlertModel.OCPC,tmsg, AlertStatus.INIT,DingTailService.dset, AlertType.DINGDING);
            DingTailService.sendMarkdownMsg(alertRecord);
        }

        List<AppActiveAlertBean>  appActiveAlertBeanList = toutiaoReportIdeadayMapper.queryKuaishouActiveGap();
        String alertKuaishouMsg = jisRateMsg(appActiveAlertBeanList,alertRate,bigAlertRate);
        String kmsg = "快手ocpc激活异常 "+alertRate+"\n"+alertMsg;
        XxlJobLogger.log(kmsg);
        if(StringUtils.isNotBlank(alertKuaishouMsg)){

            XxlJobLogger.log(kmsg);
            AlertRecord alertRecord =alertRecordService.insertAlertRecord(jobName,"快手ocpc激活异常", AlertModel.OCPC,kmsg, AlertStatus.INIT,DingTailService.dset, AlertType.DINGDING);
            DingTailService.sendMarkdownMsg(alertRecord);
        }
    }
    @Autowired
    UserEventService userEventService;
    public void activeAllActive(String jobName,double flFa){
        XxlJobLogger.log("设置范围值为"+flFa);
        Date todyStartdate = DateUtils.getStartHourDate(new Date(System.currentTimeMillis()-DateTimeConstants.MILLIS_PER_HOUR));
        Date todyenddate = DateUtils.getStartHourDate(new Date());
        Date yesHourDate = DateUtils.getStartHourDate(new Date(System.currentTimeMillis()- DateTimeConstants.MILLIS_PER_DAY-DateTimeConstants.MILLIS_PER_HOUR));
        Date yesEndHourDate = DateUtils.getStartHourDate(new Date(System.currentTimeMillis()- DateTimeConstants.MILLIS_PER_DAY));

        long todayHourActiveCount = userEventService.queryActiveCount(todyStartdate,todyenddate);
        long yesHourActiveCount = userEventService.queryActiveCount(yesHourDate,yesEndHourDate);

        XxlJobLogger.log(todyStartdate.getHours()+"时，激活总量波动率 "+(1d-todayHourActiveCount/yesHourActiveCount*1.0d) +" 设定值："+flFa +"昨天："+yesHourActiveCount+" 今天："+todayHourActiveCount);

        if(yesHourActiveCount<10000){
            //若同期 激活小10000 则波动率调大
            flFa = 0.55;
        }
        if(todayHourActiveCount<yesHourActiveCount){
            double curlFloat = DoubleUtil.getDoubleByTwo(1d-(todayHourActiveCount*1.0d/yesHourActiveCount*1.0d));
            if(curlFloat>flFa){
                String tmsg = todyStartdate.getHours()+"时，激活总量波动率 "+(1d-todayHourActiveCount/yesHourActiveCount*1.0d) +" 设定值："+flFa +"昨天："+yesHourActiveCount+" 今天："+todayHourActiveCount;
                VedioAlertService.sendVocMsg(" ocpc ", tmsg);
                AlertRecord alertRecord =alertRecordService.insertAlertRecord(jobName,"ocpc 10000 10%", AlertModel.OCPC,tmsg, AlertStatus.INIT,DingTailService.dset,AlertType.DINGPHONE);
                DingTailService.sendMarkdownMsg(alertRecord);
            }
        }


        Date todyMinStartdate = DateUtils.getStartHourDate(new Date());
        Date todyMinenddate = DateUtils.getStartHourDate(new Date(System.currentTimeMillis()+DateTimeConstants.MILLIS_PER_HOUR));
        Date yesMinHourDate = DateUtils.getStartHourDate(new Date(System.currentTimeMillis()- DateTimeConstants.MILLIS_PER_DAY));
        Date yesMinEndHourDate =  DateUtils.getStartHourDate(new Date(System.currentTimeMillis()- DateTimeConstants.MILLIS_PER_DAY+DateTimeConstants.MILLIS_PER_HOUR));

        long todayMinHourActiveCount = userEventService.queryActiveCount(todyMinStartdate,todyMinenddate);
        double yesMinHourActiveCount = DoubleUtil.getDoubleByTwo(userEventService.queryActiveCount(yesMinHourDate,yesMinEndHourDate)*(new Date().getMinutes()/60d));
        XxlJobLogger.log(todyMinStartdate.getHours()+"时，激活总量波动率 "+(1d-todayHourActiveCount*1.0d/yesHourActiveCount*1.0d) +" 设定值："+flFa +"昨天："+yesHourActiveCount+" 今天："+todayHourActiveCount);

        if(todayMinHourActiveCount<yesMinHourActiveCount){
            double curlFloat = DoubleUtil.getDoubleByTwo(1d-(todayMinHourActiveCount*1.0d/yesMinHourActiveCount*1.0d));
            if(curlFloat>(flFa+0.2)){
                String tmsg=todyMinStartdate.getHours()+"时，激活总量波动率 "+curlFloat +" 设定值："+(flFa+0.2) +"昨天："+yesMinHourActiveCount+" 今天："+todayMinHourActiveCount;
                AlertRecord alertRecord =alertRecordService.insertAlertRecord(jobName,"激活总量波动率", AlertModel.OCPC,tmsg, AlertStatus.INIT,DingTailService.dset,AlertType.DINGDING);
                DingTailService.sendMarkdownMsg(alertRecord);
            }
        }
    }


    private String jisRateMsg(List<AppActiveAlertBean>  appActiveAlertBeans,double alertRate,double bigAlertRate){
        String alertMsg = "";
        for(AppActiveAlertBean appActiveAlertBean : appActiveAlertBeans){
            Integer activeNum = appActiveAlertBean.getActive()==null ? 0:appActiveAlertBean.getActive();
            Integer ocpcActiveNum  = appActiveAlertBean.getOcpcActive()==null ?0:appActiveAlertBean.getOcpcActive();

            if(ocpcActiveNum>oaLimit){
                double checkRate = DoubleUtil.getDoubleByTwo(Math.abs((ocpcActiveNum-activeNum)*1.0d/ocpcActiveNum*1.0d));
                XxlJobLogger.log(appActiveAlertBean.getProduct()+" ocpc："+ocpcActiveNum+" activeNum："+activeNum+" 率："+checkRate+"\n");
                if(checkRate>alertRate){
                    alertMsg = alertMsg+""+appActiveAlertBean.getProduct()+" ocpc："+ocpcActiveNum+" activeNum："+activeNum+" 率："+checkRate+"\n";
                }
                Date date = new Date();
                if(checkRate>bigAlertRate && ocpcActiveNum>20000 && date.getHours()>=8){
                    String tmsg = appActiveAlertBean.getProduct()+" ocpc："+ocpcActiveNum+" activeNum："+activeNum+" 率："+checkRate;
                    XxlJobLogger.log("产生预警 大于20000"+appActiveAlertBean.getProduct()+" ocpc："+ocpcActiveNum+" activeNum："+activeNum+" 率："+checkRate+"\n");

                    VedioAlertService.sendVocMsg("ocpc拉取数据 ",tmsg);
                    AlertRecord alertRecord =alertRecordService.insertAlertRecord("ocpcactive","ocpc拉取数据", AlertModel.OCPC,tmsg, AlertStatus.INIT,DingTailService.dset,AlertType.DINGPHONE);
                    DingTailService.sendMarkdownMsg(alertRecord);
                }
            }
        }
        return alertMsg;
    }
}
