package com.shinet.core.alert.dingtalk;

import com.alibaba.fastjson.JSON;
import com.shinet.core.alert.common.HttpClientService;
import com.shinet.core.alert.common.HttpOkService;
import com.shinet.core.alert.dingtalk.rsp.DingUserInfo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Component
@Slf4j
public class DingdingAuthService {
    @Autowired
    HttpClientService httpClientService;

    public static String appKey = "dingkcjznmyf1bklyhli";
    public static String appSecret = "jtqbFEG25zFNQakfa7JRqAX7q9Af8LSmD-IAS7Wikjg80JRsSmc1ktxGEld_a4Oj";

    public static String getAuthToken(){
        //GET https://oapi.dingtalk.com/gettoken?appkey=appkey&appsecret=appsecret
        String reqUrl = "https://oapi.dingtalk.com/gettoken?appkey="+appKey+"&appsecret="+appSecret;

        String atoken = HttpOkService.reqGet(reqUrl);
        //{"errcode":0,"access_token":"1e2dc25f142c3809850e583497e3d862","errmsg":"ok","expires_in":7200}
        if(StringUtils.isNotBlank(atoken)){
            return JSON.parseObject(atoken).getString("access_token");
        }
        return atoken;
    }



    public static String getTicket(String atoken){
        //GET https://oapi.dingtalk.com/get_jsapi_ticket?access_token=ACCESS_TOKEN
        String reqUrl = "https://oapi.dingtalk.com/get_jsapi_ticket?access_token="+atoken;
        String tickStr = HttpOkService.reqGet(reqUrl);
        if(StringUtils.isNotBlank(tickStr)){
            return JSON.parseObject(tickStr).getString("ticket");
        }
        return null;
    }

    public static DingUserInfo getUserInfo(String ucode){
        //https://oapi.dingtalk.com/user/getuserinfo
        String reqUrl = "https://oapi.dingtalk.com/user/getuserinfo?access_token="+getAuthToken()+"&code="+ucode;
        String uinfoStr = HttpOkService.reqGet(reqUrl);
        log.info(uinfoStr);
        DingUserInfo dingUserInfo = JSON.parseObject(uinfoStr, DingUserInfo.class);
        return dingUserInfo;
    }

    public static void main(String[] args){
        getUserInfo("d7ac0e3938343860b19751744695983f");
    }
}
