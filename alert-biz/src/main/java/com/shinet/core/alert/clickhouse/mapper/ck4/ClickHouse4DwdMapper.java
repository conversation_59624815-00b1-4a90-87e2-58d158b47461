package com.shinet.core.alert.clickhouse.mapper.ck4;

import com.shinet.core.alert.clickhouse.entity.*;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;


public interface ClickHouse4DwdMapper {

    /**
     * description: 查询实时分渠道投放用户比例
    */
    @Select({"select product, " +
            "       product_name, " +
            "       os, " +
            "       tupleElement(exChannel, 2)           as channel, " +
            "       tupleElement(exChannel, 3)           as ocpc_count, " +
            "       tupleElement(exChannel, 4)           as all_count, " +
            "       round(tupleElement(exChannel, 1), 4) as ocpc_rate, " +
            "       1 - ocpc_rate                        as no_ocpc_rate " +
            " from ( " +
            "         select t1.product as product, t2.product_name as product_name, os, exChannel " +
            "         from ( " +
            "                  select r1.product                                               as product, " +
            "                         r1.os                                                    as os, " +
            "                         deviceMap, " +
            "                         arrayMap(x -> (bitmapAndCardinality(tupleElement(x, 2), r2.device_bit)/tupleElement(x, 3), " +
            "                                        tupleElement(x, 1), bitmapAndCardinality(tupleElement(x, 2), r2.device_bit), " +
            "                                        tupleElement(x, 3)), deviceMap)           as channel_rate, " +
            "                         arrayFilter(x -> tupleElement(x, 1) < #{ocpcRate}, channel_rate) as exChannel " +
            "                  from ( " +
            "                           select product, " +
            "                                  os, " +
            "                                  groupArray(channel)        as channleArray, " +
            "                                  groupArray(device_bit)     as deviceArray, " +
            "                                  groupArray(device_count)   as deviceCountArray, " +
            "                                  arrayMap((x, y, z)-> (x, y, z), channleArray, deviceArray, " +
            "                                           deviceCountArray) as deviceMap " +
            "                           from ( " +
            "                                 select product, " +
            "                                        os, " +
            "                                        channel, " +
            "                                        groupBitmapState(xxHash32(device_id)) as device_bit, " +
            "                                        bitmapCardinality(device_bit)         as device_count " +
            "                                 from dwd.device_dist " +
            "                                 where logday = today() and hour <= toHour(now())-2 " +
            "                                   and (channel like 'csj%' " +
            "                                     or channel like 'ttzz%' " +
            "                                     or channel like 'ks%' " +
            "                                     or channel like 'KS%' " +
            "                                     or channel like 'tx%' " +
            "                                     or channel like 'gdt%') " +
            "                                 group by product, os, channel " +
            "                                 having device_count > #{deviceCount} " +
            "                                    ) " +
            "                           group by product, os " +
            "                           ) r1 " +
            "                           left join ( select product, os, groupBitmapMergeState(device_bitmap) as device_bit " +
            "                                       from dwd.aggregation_tf_device " +
            "                                       group by product, os) r2 on r1.product = r2.product and r1.os = r2.os " +
            "                  ) t1 " +
            "                  left join dwd.product_map_dist t2 on t1.product = t2.product " +
            "         where length(exChannel) >= 1 " +
            "         ) " +
            "         array join exChannel;"})
    List<NuDeviceChannelOcpcRate> queryNuDeviceChannelOcpcRateList(@Param("deviceCount") Integer deviceCount,@Param("ocpcRate") Double ocpcRate);

    /**
     * description: 查询实时分渠道投放用户比例
    */
    @Select({"select product, " +
            "       product_name, " +
            "       hour, " +
            "       os, " +
            "       tupleElement(exChannel, 2)           as channel, " +
            "       tupleElement(exChannel, 3)           as ocpc_count, " +
            "       tupleElement(exChannel, 4)           as all_count, " +
            "       round(tupleElement(exChannel, 1), 4) as ocpc_rate, " +
            "       1 - ocpc_rate                        as no_ocpc_rate " +
            " from ( " +
            "         select t1.product as product, t2.product_name as product_name, os, hour, exChannel " +
            "         from ( " +
            "                  select r1.product                                               as product, " +
            "                         r1.os                                                    as os, " +
            "                         r1.hour                                                  as hour, " +
            "                         deviceMap, " +
            "                         arrayMap(x -> (bitmapAndCardinality(tupleElement(x, 2), r2.device_bit) / tupleElement(x, 3), " +
            "                                        tupleElement(x, 1), bitmapAndCardinality(tupleElement(x, 2), r2.device_bit), " +
            "                                        tupleElement(x, 3)), deviceMap)           as channel_rate, " +
            "                         arrayFilter(x -> tupleElement(x, 1) < #{ocpcRate}, channel_rate) as exChannel " +
            "                  from ( " +
            "                           select product, " +
            "                                  os, " +
            "                                  hour, " +
            "                                  groupArray(channel)        as channleArray, " +
            "                                  groupArray(device_bit)     as deviceArray, " +
            "                                  groupArray(device_count)   as deviceCountArray, " +
            "                                  arrayMap((x, y, z)-> (x, y, z), channleArray, deviceArray, " +
            "                                           deviceCountArray) as deviceMap " +
            "                           from ( " +
            "                                 select product, " +
            "                                        os, " +
            "                                        hour, " +
            "                                        channel, " +
            "                                        groupBitmapState(xxHash32(device_id)) as device_bit, " +
            "                                        bitmapCardinality(device_bit)         as device_count " +
            "                                 from dwd.device_dist " +
            "                                 where logday = today() " +
            "                                   and hour = toHour(now())-2 " +
            "                                   and (channel like 'csj%' " +
            "                                     or channel like 'ttzz%' " +
            "                                     or channel like 'ks%' " +
            "                                     or channel like 'KS%' " +
            "                                     or channel like 'tx%' " +
            "                                     or channel like 'gdt%') " +
            "                                 group by product, os, hour, channel " +
            "                                 having device_count > #{deviceCount} " +
            "                                    ) " +
            "                           group by product, os, hour " +
            "                           ) r1 " +
            "                           left join ( select product, os, groupBitmapMergeState(device_bitmap) as device_bit " +
            "                                       from dwd.aggregation_tf_device " +
            "                                       group by product, os) r2 on r1.product = r2.product and r1.os = r2.os " +
            "                  ) t1 " +
            "                  left join dwd.product_map_dist t2 on t1.product = t2.product " +
            "         where length(exChannel) >= 1 " +
            "         ) " +
            "         array join exChannel;"})
    List<NuDeviceChannelOcpcRate> queryNuDeviceChannelOcpcHourRateList(@Param("deviceCount") Integer deviceCount,@Param("ocpcRate") Double ocpcRate);



    //当日截至目前新增用户及收入 同比环比
    @Select({"select today()                                                                 AS logday,\n" +
            "       toHour(now()) - 1                                                       AS hour,\n" +
            "       t1.os                                                                   AS os,\n" +
            "       max(t1.new_au_today)                                                    AS new_au_today,\n" +
            "       round(max(t1.new_au_income_today), 2)                                   AS new_au_income_today,\n" +
            "       max(t1.new_au_today_hour)                                               AS new_au_today_hour,\n" +
            "       max(t1.new_au_yes)                                                      AS new_au_yes,\n" +
            "       round(max(t1.new_au_income_yes), 2)                                     AS new_au_income_yes,\n" +
            "       max(t1.new_au_yes_hour)                                                 AS new_au_yes_hour,\n" +
            "       round((new_au_today - new_au_yes) * 100 / new_au_yes, 2)                AS new_au_rate_yes,\n" +
            "       round((new_au_income_today - new_au_income_yes) * 100 / new_au_income_yes,\n" +
            "             2)                                                                AS new_au_income_rate_yes,\n" +
            "       round((new_au_today_hour - new_au_yes_hour) * 100 / new_au_yes_hour, 2) AS new_au_rate_yes_hour\n" +
            "from (\n" +
            "         SELECT p.os,\n" +
            "                0                 AS new_au_today,\n" +
            "                sum(p.income_val) AS new_au_income_today,\n" +
            "                0                 as new_au_today_hour,\n" +
            "                0                 as new_au_yes,\n" +
            "                0                 as new_au_income_yes,\n" +
            "                0                 as new_au_yes_hour\n" +
            "         FROM (\n" +
            "                  SELECT os,\n" +
            "                         toFloat64OrZero(extend1) / 1000 AS income_val\n" +
            "                  FROM ods.event_exposure_dist\n" +
            "                  WHERE logday = today()\n" +
            "                    AND ad_action = 'exposure'\n" +
            "                    AND event = 'AdData'\n" +
            "                    AND hour < toHour(now())\n" +
            "                    AND os in ('ios', 'android')\n" +
            "                    AND toFloat64OrZero(extend1) / 1000 < 30000\n" +
            "                    and concat(device_id, product, os) GLOBAL IN\n" +
            "                        (SELECT concat(device_id, product, os)\n" +
            "                         FROM dwd.device_dist\n" +
            "                         WHERE logday = today()\n" +
            "                           and hour < toHour(now()))\n" +
            "                  ) p\n" +
            "         group by p.os\n" +
            "         union all\n" +
            "         SELECT p.os,\n" +
            "                0                 as new_au_today,\n" +
            "                0                 as new_au_income_today,\n" +
            "                0                 as new_au_today_hour,\n" +
            "                0                 AS new_au_yes,\n" +
            "                sum(p.income_val) AS new_au_income_yes,\n" +
            "                0                 as new_au_yes_hour\n" +
            "         FROM (\n" +
            "                  SELECT os,\n" +
            "                         toFloat64OrZero(extend1) / 1000 AS income_val\n" +
            "                  FROM ods.event_exposure_dist\n" +
            "                  WHERE logday = yesterday()\n" +
            "                    AND ad_action = 'exposure'\n" +
            "                    AND event = 'AdData'\n" +
            "                    AND hour < toHour(now())\n" +
            "                    AND os in ('ios', 'android')\n" +
            "                    AND toFloat64OrZero(extend1) / 1000 < 30000\n" +
            "                    and concat(device_id, product, os) GLOBAL IN\n" +
            "                        (SELECT concat(device_id, product, os)\n" +
            "                         FROM dwd.device_dist\n" +
            "                         WHERE logday = yesterday()\n" +
            "                           and hour < toHour(now()))\n" +
            "                  ) p\n" +
            "         group by p.os\n" +
            "         union all\n" +
            "         SELECT os,\n" +
            "                0                                                                         as new_au_today,\n" +
            "                0                                                                         as new_au_income_today,\n" +
            "                0                                                                         as new_au_today_hour,\n" +
            "                uniqExact(concat(product, device_id))                                     as new_au_yes,\n" +
            "                0                                                                         as new_au_income_rate_yes,\n" +
            "                uniqExact(if(hour = toHour(now()) - 1, concat(product, device_id), null)) AS new_au_yes_hour\n" +
            "         FROM dwd.device_dist\n" +
            "         WHERE logday = yesterday()\n" +
            "           and hour < toHour(now())\n" +
            "           AND os in ('ios', 'android')\n" +
            "         group by os\n" +
            "         union all\n" +
            "         SELECT os,\n" +
            "                uniqExact(concat(product, device_id))                                     as new_au_today,\n" +
            "                0                                                                         as new_au_income_today,\n" +
            "                uniqExact(if(hour = toHour(now()) - 1, concat(product, device_id), null)) AS new_au_today_hour,\n" +
            "                0                                                                         as new_au_yes,\n" +
            "                0                                                                         as new_au_income_rate_yes,\n" +
            "                0                                                                         AS new_au_yes_hour\n" +
            "         FROM dwd.device_dist\n" +
            "         WHERE logday = today()\n" +
            "           and hour < toHour(now())\n" +
            "           AND os in ('ios', 'android')\n" +
            "         group by os\n" +
            "         ) t1\n" +
            "group by t1.os\n" +
            "having new_au_today_hour > 0 and (new_au_rate_yes_hour >= 30\n" +
            "    or new_au_rate_yes_hour <= -30);"})
    List<TodayNewAuIncomeBean> queryTodayNewAuIncome();


    //当日截至目前新增用户及收入 同比环比
    @Select({"select today()                                                  AS logday,\n" +
            "       toHour(now()) - 1                                        AS hour,\n" +
            "       t1.os                                                    AS os,\n" +
            "       max(t1.old_au_today)                                     AS old_au_today,\n" +
            "       round(max(t1.old_au_income_today), 2)                    AS old_au_income_today,\n" +
            "       round(max(t1.old_au_income_today_hour), 2)               AS old_au_income_today_hour,\n" +
            "       max(t1.old_au_yes)                                       AS old_au_yes,\n" +
            "       round(max(t1.old_au_income_yes), 2)                      AS old_au_income_yes,\n" +
            "       round(max(t1.old_au_income_yes_hour), 2)                 AS old_au_income_yes_hour,\n" +
            "       round((old_au_today - old_au_yes) * 100 / old_au_yes, 2) AS old_au_rate_yes,\n" +
            "       round((old_au_income_today - old_au_income_yes) * 100 / old_au_income_yes,\n" +
            "             2)                                                 AS old_au_income_rate_yes,\n" +
            "       round((old_au_income_today_hour - old_au_income_yes_hour) * 100 / old_au_income_yes_hour,\n" +
            "             2)                                                 AS old_au_income_rate_yes_hour\n" +
            "from (\n" +
            "         SELECT p.os,\n" +
            "                0                                                    AS old_au_today,\n" +
            "                sum(p.income_val)                                    AS old_au_income_today,\n" +
            "                sum(if(p.hour = toHour(now()) - 1, p.income_val, 0)) as old_au_income_today_hour,\n" +
            "                0                                                    as old_au_yes,\n" +
            "                0                                                    as old_au_income_yes,\n" +
            "                0                                                    as old_au_income_yes_hour\n" +
            "         FROM (\n" +
            "                  SELECT os,\n" +
            "                         hour,\n" +
            "                         toFloat64OrZero(extend1) / 1000 AS income_val\n" +
            "                  FROM ods.event_exposure_dist\n" +
            "                  WHERE logday = today()\n" +
            "                    AND ad_action = 'exposure'\n" +
            "                    AND event = 'AdData'\n" +
            "                    AND hour < toHour(now())\n" +
            "                    AND os in ('ios', 'android')\n" +
            "                    AND toFloat64OrZero(extend1) / 1000 < 30000\n" +
            "                    and concat(device_id, product, os) GLOBAL NOT IN\n" +
            "                        (SELECT concat(device_id, product, os)\n" +
            "                         FROM dwd.device_dist\n" +
            "                         WHERE logday = today()\n" +
            "                           and hour < toHour(now()))\n" +
            "                  ) p\n" +
            "         group by p.os\n" +
            "         union all\n" +
            "         SELECT p.os,\n" +
            "                0                                                    as old_au_today,\n" +
            "                0                                                    as old_au_income_today,\n" +
            "                0                                                    as old_au_income_today_hour,\n" +
            "                0                                                    AS old_au_yes,\n" +
            "                sum(p.income_val)                                    AS old_au_income_yes,\n" +
            "                sum(if(p.hour = toHour(now()) - 1, p.income_val, 0)) as old_au_income_yes_hour\n" +
            "         FROM (\n" +
            "                  SELECT os,\n" +
            "                         hour,\n" +
            "                         toFloat64OrZero(extend1) / 1000 AS income_val\n" +
            "                  FROM ods.event_exposure_dist\n" +
            "                  WHERE logday = yesterday()\n" +
            "                    AND ad_action = 'exposure'\n" +
            "                    AND event = 'AdData'\n" +
            "                    AND hour < toHour(now())\n" +
            "                    AND os in ('ios', 'android')\n" +
            "                    AND toFloat64OrZero(extend1) / 1000 < 30000\n" +
            "                    and concat(device_id, product, os) GLOBAL NOT IN\n" +
            "                        (SELECT concat(device_id, product, os)\n" +
            "                         FROM dwd.device_dist\n" +
            "                         WHERE logday = yesterday()\n" +
            "                           and hour < toHour(now()))\n" +
            "                  ) p\n" +
            "         group by p.os\n" +
            "         union all\n" +
            "         select os,\n" +
            "                0                                     as old_au_today,\n" +
            "                0                                     as old_au_income_today,\n" +
            "                0                                     as old_au_income_today_hour,\n" +
            "                uniqExact(concat(product, device_id)) AS old_au_yes,\n" +
            "                0                                     AS old_au_income_yes,\n" +
            "                0                                     as old_au_income_yes_hour\n" +
            "         from dwd.au_device_dist\n" +
            "         where logday = yesterday()\n" +
            "           and hour < toHour(now())\n" +
            "           and os in ('ios', 'android')\n" +
            "           and concat(device_id, product, os) GLOBAL NOT IN\n" +
            "               (SELECT concat(device_id, product, os)\n" +
            "                FROM dwd.device_dist\n" +
            "                WHERE logday = yesterday()\n" +
            "                  and hour < toHour(now()))\n" +
            "         group by os\n" +
            "         union all\n" +
            "         select os,\n" +
            "                uniqExact(concat(product, device_id)) as old_au_today,\n" +
            "                0                                     as old_au_income_today,\n" +
            "                0                                     as old_au_income_today_hour,\n" +
            "                0                                     AS old_au_yes,\n" +
            "                0                                     AS old_au_income_yes,\n" +
            "                0                                     as old_au_income_yes_hour\n" +
            "         from dwd.au_device_dist\n" +
            "         where logday = today()\n" +
            "           and hour < toHour(now())\n" +
            "           and os in ('ios', 'android')\n" +
            "           and concat(device_id, product, os) GLOBAL NOT IN\n" +
            "               (SELECT concat(device_id, product, os)\n" +
            "                FROM dwd.device_dist\n" +
            "                WHERE logday = today()\n" +
            "                  and hour < toHour(now()))\n" +
            "         group by os\n" +
            "         ) t1\n" +
            "group by t1.os\n" +
            "having old_au_income_today_hour >0 and (old_au_income_rate_yes_hour >= 30\n" +
            "    or old_au_income_rate_yes_hour <= -30);"})
    List<TodayOldAuIncomeBean> queryTodayOldAuIncome();


    //昨日截至24时目前新增用户及收入 同比环比
    @Select({"select today() - 1                                                             AS logday,\n" +
            "       23                                                                      AS hour,\n" +
            "       t1.os                                                                   AS os,\n" +
            "       max(t1.new_au_today)                                                    AS new_au_today,\n" +
            "       round(max(t1.new_au_income_today), 2)                                   AS new_au_income_today,\n" +
            "       max(t1.new_au_today_hour)                                               AS new_au_today_hour,\n" +
            "       max(t1.new_au_yes)                                                      AS new_au_yes,\n" +
            "       round(max(t1.new_au_income_yes), 2)                                     AS new_au_income_yes,\n" +
            "       max(t1.new_au_yes_hour)                                                 AS new_au_yes_hour,\n" +
            "       round((new_au_today - new_au_yes) * 100 / new_au_yes, 2)                AS new_au_rate_yes,\n" +
            "       round((new_au_income_today - new_au_income_yes) * 100 / new_au_income_yes,\n" +
            "             2)                                                                AS new_au_income_rate_yes,\n" +
            "       round((new_au_today_hour - new_au_yes_hour) * 100 / new_au_yes_hour, 2) AS new_au_rate_yes_hour\n" +
            "from (\n" +
            "         SELECT p.os,\n" +
            "                0                 AS new_au_today,\n" +
            "                sum(p.income_val) AS new_au_income_today,\n" +
            "                0                 as new_au_today_hour,\n" +
            "                0                 as new_au_yes,\n" +
            "                0                 as new_au_income_yes,\n" +
            "                0                 as new_au_yes_hour\n" +
            "         FROM (\n" +
            "                  SELECT os,\n" +
            "                         toFloat64OrZero(extend1) / 1000 AS income_val\n" +
            "                  FROM ods.event_exposure_dist\n" +
            "                  WHERE logday = today() - 1\n" +
            "                    AND ad_action = 'exposure'\n" +
            "                    AND event = 'AdData'\n" +
            "                    AND hour <= 24\n" +
            "                    AND os in ('ios', 'android')\n" +
            "                    AND toFloat64OrZero(extend1) / 1000 < 30000\n" +
            "                    and concat(device_id, product, os) GLOBAL IN\n" +
            "                        (SELECT concat(device_id, product, os)\n" +
            "                         FROM dwd.device_dist\n" +
            "                         WHERE logday = today() - 1\n" +
            "                           AND os in ('ios', 'android')\n" +
            "                           and hour <= 24)\n" +
            "                  ) p\n" +
            "         group by p.os\n" +
            "         union all\n" +
            "         SELECT p.os,\n" +
            "                0                 as new_au_today,\n" +
            "                0                 as new_au_income_today,\n" +
            "                0                 as new_au_today_hour,\n" +
            "                0                 AS new_au_yes,\n" +
            "                sum(p.income_val) AS new_au_income_yes,\n" +
            "                0                 as new_au_yes_hour\n" +
            "         FROM (\n" +
            "                  SELECT os,\n" +
            "                         toFloat64OrZero(extend1) / 1000 AS income_val\n" +
            "                  FROM ods.event_exposure_dist\n" +
            "                  WHERE logday = yesterday() - 1\n" +
            "                    AND ad_action = 'exposure'\n" +
            "                    AND event = 'AdData'\n" +
            "                    AND hour <= 24\n" +
            "                    AND os in ('ios', 'android')\n" +
            "                    AND toFloat64OrZero(extend1) / 1000 < 30000\n" +
            "                    and concat(device_id, product, os) GLOBAL IN\n" +
            "                        (SELECT concat(device_id, product, os)\n" +
            "                         FROM dwd.device_dist\n" +
            "                         WHERE logday = yesterday() - 1\n" +
            "                           AND os in ('ios', 'android')\n" +
            "                           and hour <= 24)\n" +
            "                  ) p\n" +
            "         group by p.os\n" +
            "         union all\n" +
            "         SELECT os,\n" +
            "                0                                                          as new_au_today,\n" +
            "                0                                                          as new_au_income_today,\n" +
            "                0                                                          as new_au_today_hour,\n" +
            "                uniqExact(concat(product, device_id))                      as new_au_yes,\n" +
            "                0                                                          as new_au_income_yes,\n" +
            "                uniqExact(if(hour = 23, concat(product, device_id), null)) AS new_au_yes_hour\n" +
            "         FROM dwd.device_dist\n" +
            "         WHERE logday = yesterday() - 1\n" +
            "           AND os in ('ios', 'android')\n" +
            "         group by os\n" +
            "         union all\n" +
            "         SELECT os,\n" +
            "                uniqExact(concat(product, device_id))                      as new_au_today,\n" +
            "                0                                                          as new_au_income_today,\n" +
            "                uniqExact(if(hour = 23, concat(product, device_id), null)) AS new_au_today_hour,\n" +
            "                0                                                          as new_au_yes,\n" +
            "                0                                                          as new_au_income_yes,\n" +
            "                0                                                          AS new_au_yes_hour\n" +
            "         FROM dwd.device_dist\n" +
            "         WHERE logday = today() - 1\n" +
            "           AND os in ('ios', 'android')\n" +
            "         group by os\n" +
            "         ) t1\n" +
            "group by t1.os\n" +
            "having new_au_today_hour >0 and (new_au_rate_yes_hour >= 30\n" +
            "    or new_au_rate_yes_hour <= -30);"})
    List<TodayNewAuIncomeBean> queryYesNewAuIncome();


    //昨日截至24时新增用户及收入 同比环比
    @Select({"select today() - 1                                              AS logday,\n" +
            "       23                                                       AS hour,\n" +
            "       t1.os                                                    AS os,\n" +
            "       max(t1.old_au_today)                                     AS old_au_today,\n" +
            "       round(max(t1.old_au_income_today), 2)                    AS old_au_income_today,\n" +
            "       round(max(t1.old_au_income_today_hour), 2)               AS old_au_income_today_hour,\n" +
            "       max(t1.old_au_yes)                                       AS old_au_yes,\n" +
            "       round(max(t1.old_au_income_yes), 2)                      AS old_au_income_yes,\n" +
            "       round(max(t1.old_au_income_yes_hour), 2)                 AS old_au_income_yes_hour,\n" +
            "       round((old_au_today - old_au_yes) * 100 / old_au_yes, 2) AS old_au_rate_yes,\n" +
            "       round((old_au_income_today - old_au_income_yes) * 100 / old_au_income_yes,\n" +
            "             2)                                                 AS old_au_income_rate_yes,\n" +
            "       round((old_au_income_today_hour - old_au_income_yes_hour) * 100 / old_au_income_yes_hour,\n" +
            "             2)                                                 AS old_au_income_rate_yes_hour\n" +
            "from (\n" +
            "         SELECT p.os,\n" +
            "                0                                     AS old_au_today,\n" +
            "                sum(p.income_val)                     AS old_au_income_today,\n" +
            "                sum(if(p.hour = 23, p.income_val, 0)) as old_au_income_today_hour,\n" +
            "                0                                     as old_au_yes,\n" +
            "                0                                     as old_au_income_yes,\n" +
            "                0                                     as old_au_income_yes_hour\n" +
            "         FROM (\n" +
            "                  SELECT os,\n" +
            "                         hour,\n" +
            "                         toFloat64OrZero(extend1) / 1000 AS income_val\n" +
            "                  FROM ods.event_exposure_dist\n" +
            "                  WHERE logday = today() - 1\n" +
            "                    AND ad_action = 'exposure'\n" +
            "                    AND event = 'AdData'\n" +
            "                    AND hour <= 24\n" +
            "                    AND toFloat64OrZero(extend1) / 1000 < 30000\n" +
            "                    and concat(device_id, product, os) GLOBAL NOT IN\n" +
            "                        (SELECT concat(device_id, product, os)\n" +
            "                         FROM dwd.device_dist\n" +
            "                         WHERE logday = today() - 1\n" +
            "                           and hour <= 24)\n" +
            "                  ) p\n" +
            "         group by p.os\n" +
            "         union all\n" +
            "         SELECT p.os,\n" +
            "                0                                     as old_au_today,\n" +
            "                0                                     as old_au_income_today,\n" +
            "                0                                     as old_au_income_today_hour,\n" +
            "                0                                     AS old_au_yes,\n" +
            "                sum(p.income_val)                     AS old_au_income_yes,\n" +
            "                sum(if(p.hour = 23, p.income_val, 0)) as old_au_income_yes_hour\n" +
            "         FROM (\n" +
            "                  SELECT os,\n" +
            "                         hour,\n" +
            "                         toFloat64OrZero(extend1) / 1000 AS income_val\n" +
            "                  FROM ods.event_exposure_dist\n" +
            "                  WHERE logday = yesterday() - 1\n" +
            "                    AND ad_action = 'exposure'\n" +
            "                    AND event = 'AdData'\n" +
            "                    AND hour <= 24\n" +
            "                    AND toFloat64OrZero(extend1) / 1000 < 30000\n" +
            "                    and concat(device_id, product, os) GLOBAL NOT IN\n" +
            "                        (SELECT concat(device_id, product, os)\n" +
            "                         FROM dwd.device_dist\n" +
            "                         WHERE logday = yesterday() - 1\n" +
            "                           and hour <= 24)\n" +
            "                  ) p\n" +
            "         group by p.os\n" +
            "         union all\n" +
            "         select os,\n" +
            "                0                                     as old_au_today,\n" +
            "                0                                     as old_au_income_today,\n" +
            "                0                                     as old_au_income_today_hour,\n" +
            "                uniqExact(concat(product, device_id)) AS old_au_yes,\n" +
            "                0                                     AS old_au_income_yes,\n" +
            "                0                                     as old_au_income_yes_hour\n" +
            "         from dwd.au_device_dist\n" +
            "         where logday = yesterday() - 1\n" +
            "           and hour <= 24\n" +
            "           and os in ('ios', 'android')\n" +
            "           and concat(device_id, product, os) GLOBAL NOT IN\n" +
            "               (SELECT concat(device_id, product, os)\n" +
            "                FROM dwd.device_dist\n" +
            "                WHERE logday = yesterday() - 1\n" +
            "                  and hour <= 24)\n" +
            "         group by os\n" +
            "         union all\n" +
            "         select os,\n" +
            "                uniqExact(concat(product, device_id)) as old_au_today,\n" +
            "                0                                     as old_au_income_today,\n" +
            "                0                                     as old_au_income_today_hour,\n" +
            "                0                                     AS old_au_yes,\n" +
            "                0                                     AS old_au_income_yes,\n" +
            "                0                                     as old_au_income_yes_hour\n" +
            "         from dwd.au_device_dist\n" +
            "         where logday = today() - 1\n" +
            "           and hour <= 24\n" +
            "           and os in ('ios', 'android')\n" +
            "           and concat(device_id, product, os) GLOBAL NOT IN\n" +
            "               (SELECT concat(device_id, product, os)\n" +
            "                FROM dwd.device_dist\n" +
            "                WHERE logday = today() - 1\n" +
            "                  and hour <= 24)\n" +
            "         group by os\n" +
            "         ) t1\n" +
            "group by t1.os\n" +
            "having old_au_income_today_hour > 0 and (old_au_income_rate_yes_hour >= 30\n" +
            "    or old_au_income_rate_yes_hour <= -30);"})
    List<TodayOldAuIncomeBean> queryYesOldAuIncome();


    /**
     * description: 查询上小时拉黑用户设备数
     */
    @Select({"select max(user_hui_today)   as user_hui_today,\n" +
            "       max(user_hui_hour)    as user_hui_hour,\n" +
            "       max(device_num_today) as device_num_today,\n" +
            "       max(device_num_hour)  as device_num_hour,\n" +
            "       max(user_num_today)   as user_num_today,\n" +
            "       max(user_num_hour)    as user_num_hour,\n" +
            "       max(remarks_hour)     as remarks_hour\n" +
            "from (\n" +
            "         --用户数\n" +
            "         select 0                                                                       as user_hui_today,\n" +
            "                0                                                                       as user_hui_hour,\n" +
            "                0                                                                       as device_num_today,\n" +
            "                0                                                                       as device_num_hour,\n" +
            "                uniqExact(target_id)                                                    as user_num_today,\n" +
            "                uniqExact(if(toHour(create_time) = toHour(now()) - 1, target_id, null)) as user_num_hour,\n" +
            "                ''                                                                      as remarks_hour\n" +
            "         from ods_mysql.already_gray_user\n" +
            "         where toDate(create_time) = today()\n" +
            "           and remark != '外部调用-用户注销'\n" +
            "           and target_type = 2\n" +
            "         union all\n" +
            "         --设备数\n" +
            "         select 0                                                                           as user_hui_today,\n" +
            "                0                                                                           as user_hui_hour,\n" +
            "                uniqExact(b.device_id)                                                      as device_num_today,\n" +
            "                uniqExact(if(toHour(a.create_time) = toHour(now()) - 1, b.device_id, null)) as device_num_hour,\n" +
            "                0                                                                           as user_num_today,\n" +
            "                0                                                                           as user_num_hour,\n" +
            "                ''                                                                          as remarks_hour\n" +
            "         from ods_mysql.already_gray_user a\n" +
            "                  inner join\n" +
            "              (select distinct device_id from dwd.au_device_dist where logday = today()) b\n" +
            "              on a.target_id = b.device_id\n" +
            "         where toDate(a.create_time) = today()\n" +
            "           and a.remark != '外部调用-用户注销'\n" +
            "           and a.target_type = 1\n" +
            "         union all\n" +
            "         --上小时拉黑超20的remark集合\n" +
            "         select 0                                     as user_hui_today,\n" +
            "                0                                     as user_hui_hour,\n" +
            "                0                                     as device_num_today,\n" +
            "                0                                     as device_num_hour,\n" +
            "                0                                     as user_num_today,\n" +
            "                0                                     as user_num_hour,\n" +
            "                toString(groupArrayDistinct(remark1)) as remarks_hour\n" +
            "         from (\n" +
            "                  select ifNull(a.remark, '空空空空') as remark1, count(distinct b.device_id) as dim_val\n" +
            "                  from ods_mysql.already_gray_user a\n" +
            "                           inner join\n" +
            "                       (select distinct device_id from dwd.au_device_dist where logday = today()) b\n" +
            "                       on a.target_id = b.device_id\n" +
            "                  where toDate(a.create_time) = today()\n" +
            "                    and a.remark != '外部调用-用户注销'\n" +
            "                    and a.target_type = 1\n" +
            "                    and toHour(create_time) = toHour(now()) - 1\n" +
            "                  group by ifNull(a.remark, '空空空空')\n" +
            "                  union all\n" +
            "                  select ifNull(remark, '空空空空') as remark1, count(distinct target_id) as dim_val\n" +
            "                  from ods_mysql.already_gray_user\n" +
            "                  where toDate(create_time) = today()\n" +
            "                    and remark != '外部调用-用户注销'\n" +
            "                    and target_type = 2\n" +
            "                    and toHour(create_time) = toHour(now()) - 1\n" +
            "                  group by ifNull(remark, '空空空空')\n" +
            "                  )\n" +
            "         where dim_val >= 20\n" +
            "         union all\n" +
            "         --拉灰\n" +
            "         SELECT uniqExact(user_id)  as user_hui_today,\n" +
            "                uniqExact(if(toHour(toDateTime(create_time / 1000)) = toHour(now()) - 1, user_id,\n" +
            "                             null)) as user_hui_hour,\n" +
            "                0                   as device_num_today,\n" +
            "                0                   as device_num_hour,\n" +
            "                0                   as user_num_today,\n" +
            "                0                   as user_num_hour,\n" +
            "                ''                  as remarks_hour\n" +
            "         FROM mysql('rr-2ze9i9uz5jmeg098m.mysql.rds.aliyuncs.com:3306', 'bp_user', 'user_gray', 'big_data',\n" +
            "                    'wriwe4itfjei')\n" +
            "         where toDate(toDateTime(create_time / 1000)) = today()\n" +
            "         );\n"})
    List<GrayUserDeviceSum> queryGrayUserHour();

    /**
     * description: 查询上小时拉黑用户设备数
     */
    @Select({"select max(user_hui_today)   as user_hui_today,\n" +
            "       max(user_hui_hour)    as user_hui_hour,\n" +
            "       max(device_num_today) as device_num_today,\n" +
            "       max(device_num_hour)  as device_num_hour,\n" +
            "       max(user_num_today)   as user_num_today,\n" +
            "       max(user_num_hour)    as user_num_hour,\n" +
            "       max(remarks_hour)     as remarks_hour\n" +
            "from (\n" +
            "         --用户数\n" +
            "         select 0                                                        as user_hui_today,\n" +
            "                0                                                        as user_hui_hour,\n" +
            "                0                                                        as device_num_today,\n" +
            "                0                                                        as device_num_hour,\n" +
            "                uniqExact(target_id)                                     as user_num_today,\n" +
            "                uniqExact(if(toHour(create_time) = 23, target_id, null)) as user_num_hour,\n" +
            "                ''                                                       as remarks_hour\n" +
            "         from ods_mysql.already_gray_user\n" +
            "         where toDate(create_time) = yesterday()\n" +
            "           and remark != '外部调用-用户注销'\n" +
            "           and target_type = 2\n" +
            "         union all\n" +
            "         --设备数\n" +
            "         select 0                                                            as user_hui_today,\n" +
            "                0                                                            as user_hui_hour,\n" +
            "                uniqExact(b.device_id)                                       as device_num_today,\n" +
            "                uniqExact(if(toHour(a.create_time) = 23, b.device_id, null)) as device_num_hour,\n" +
            "                0                                                            as user_num_today,\n" +
            "                0                                                            as user_num_hour,\n" +
            "                ''                                                           as remarks_hour\n" +
            "         from ods_mysql.already_gray_user a\n" +
            "                  inner join\n" +
            "              (select distinct device_id from dwd.au_device_dist where logday = yesterday()) b\n" +
            "              on a.target_id = b.device_id\n" +
            "         where toDate(a.create_time) = yesterday()\n" +
            "           and a.remark != '外部调用-用户注销'\n" +
            "           and a.target_type = 1\n" +
            "         union all\n" +
            "         --上小时拉黑超20的remark集合\n" +
            "         select 0                                     as user_hui_today,\n" +
            "                0                                     as user_hui_hour,\n" +
            "                0                                     as device_num_today,\n" +
            "                0                                     as device_num_hour,\n" +
            "                0                                     as user_num_today,\n" +
            "                0                                     as user_num_hour,\n" +
            "                toString(groupArrayDistinct(remark1)) as remarks_hour\n" +
            "         from (\n" +
            "                  select ifNull(a.remark, '空空空空') as remark1, count(distinct b.device_id) as dim_val\n" +
            "                  from ods_mysql.already_gray_user a\n" +
            "                           inner join\n" +
            "                       (select distinct device_id from dwd.au_device_dist where logday = yesterday()) b\n" +
            "                       on a.target_id = b.device_id\n" +
            "                  where toDate(a.create_time) = yesterday()\n" +
            "                    and a.remark != '外部调用-用户注销'\n" +
            "                    and a.target_type = 1\n" +
            "                    and toHour(create_time) = 23\n" +
            "                  group by ifNull(a.remark, '空空空空')\n" +
            "                  union all\n" +
            "                  select ifNull(remark, '空空空空') as remark1, count(distinct target_id) as dim_val\n" +
            "                  from ods_mysql.already_gray_user\n" +
            "                  where toDate(create_time) = yesterday()\n" +
            "                    and remark != '外部调用-用户注销'\n" +
            "                    and target_type = 2\n" +
            "                    and toHour(create_time) = 23\n" +
            "                  group by ifNull(remark, '空空空空')\n" +
            "                  )\n" +
            "         where dim_val >= 20\n" +
            "         union all\n" +
            "         --拉灰\n" +
            "         SELECT uniqExact(user_id)  as user_hui_today,\n" +
            "                uniqExact(if(toHour(toDateTime(create_time / 1000)) = 23, user_id,\n" +
            "                             null)) as user_hui_hour,\n" +
            "                0                   as device_num_today,\n" +
            "                0                   as device_num_hour,\n" +
            "                0                   as user_num_today,\n" +
            "                0                   as user_num_hour,\n" +
            "                ''                  as remarks_hour\n" +
            "         FROM mysql('rr-2ze9i9uz5jmeg098m.mysql.rds.aliyuncs.com:3306', 'bp_user', 'user_gray', 'big_data',\n" +
            "                    'wriwe4itfjei')\n" +
            "         where toDate(toDateTime(create_time / 1000)) = yesterday()\n" +
            "         );"})
    List<GrayUserDeviceSum> queryGrayUserHourYes();



    @Select({"select a.os,\n" +
            "       round(sum(if(a.sendTime >= (toUInt64(now()) * 1000 - 70 * 60 * 1000) and\n" +
            "                    a.sendTime < (toUInt64(now()) * 1000 - 10 * 60 * 1000) and b.device_id != '',\n" +
            "                    toFloat32OrZero(a.extend1) / 1000, 0)) /\n" +
            "             sum(if(a.sendTime >= (toUInt64(now()) * 1000 - 70 * 60 * 1000) and\n" +
            "                    a.sendTime < (toUInt64(now()) * 1000 - 10 * 60 * 1000) and b.device_id != '', 1, 0)) * 1000,\n" +
            "             2)                                                                               as new_ecpm_one_h,\n" +
            "       round(sum(if(a.sendTime >= (toUInt64(now()) * 1000 - 10 * 60 * 1000) and b.device_id != '',\n" +
            "                    toFloat32OrZero(a.extend1) / 1000, 0)) /\n" +
            "             sum(if(a.sendTime >= (toUInt64(now()) * 1000 - 10 * 60 * 1000) and b.device_id != '', 1, 0)) * 1000,\n" +
            "             2)                                                                               as new_ecpm_ten_min,\n" +
            "       round(sum(if(a.sendTime >= (toUInt64(now()) * 1000 - 70 * 60 * 1000) and\n" +
            "                    a.sendTime < (toUInt64(now()) * 1000 - 10 * 60 * 1000) and b.device_id = '',\n" +
            "                    toFloat32OrZero(a.extend1) / 1000, 0)) /\n" +
            "             sum(if(a.sendTime >= (toUInt64(now()) * 1000 - 70 * 60 * 1000) and\n" +
            "                    a.sendTime < (toUInt64(now()) * 1000 - 10 * 60 * 1000) and b.device_id = '', 1, 0)) * 1000,\n" +
            "             2)                                                                               as old_ecpm_one_h,\n" +
            "       round(sum(if(a.sendTime >= (toUInt64(now()) * 1000 - 10 * 60 * 1000) and b.device_id = '',\n" +
            "                    toFloat32OrZero(a.extend1) / 1000, 0)) /\n" +
            "             sum(if(a.sendTime >= (toUInt64(now()) * 1000 - 10 * 60 * 1000) and b.device_id = '', 1, 0)) * 1000,\n" +
            "             2)                                                                               as old_ecpm_ten_min\n" +
            "from ods.event_exposure_dist a\n" +
            "         global\n" +
            "         left join\n" +
            "     (select os, product, device_id\n" +
            "      from dwd.device_dist\n" +
            "      where logday = today()\n" +
            "        and os in ('android', 'ios')\n" +
            "      group by os, product, device_id) b\n" +
            "     on a.os = b.os\n" +
            "         and a.product = b.product\n" +
            "         and a.device_id = b.device_id\n" +
            "where a.logday = today()\n" +
            "  and a.event = 'AdData'\n" +
            "  and a.ad_action = 'exposure'\n" +
            "  and a.os in ('android', 'ios')\n" +
            "  and a.ad_type global in (select toString(ad_type)\n" +
            "                           from dwd.ad_type_basic_dist\n" +
            "                           where source_name = '穿山甲')\n" +
            "  and a.sendTime >= (toUInt64(now()) * 1000 - 70 * 60 * 1000)\n" +
            "  and toFloat32OrZero(a.extend1) < 100000\n" +
            "  and toFloat32OrZero(a.extend1) > 0\n" +
            "group by a.os\n" +
            "having new_ecpm_ten_min >= new_ecpm_one_h * 1.2;\n"})
    List<TodayEcpm> queryTodayEcpm();



    @Select({"select group_name            as group,\n" +
            "       os,\n" +
            "       product_name          as products,\n" +
            "       round(arpu_step24, 4) as arpu_step24,\n" +
            "       round(arpu_step12, 4) as arpu_step12,\n" +
            "       round(arpu_step6, 4)  as arpu_step6,\n" +
            "       round(arpu_step3, 4)  as arpu_step3,\n" +
            "       round(arpu_step1, 4)  as arpu_step1,\n" +
            "       dau_24a,\n" +
            "       dau_24b,\n" +
            "       dau_12a,\n" +
            "       dau_12b,\n" +
            "       dau_6a,\n" +
            "       dau_6b,\n" +
            "       dau_3a,\n" +
            "       dau_3b,\n" +
            "       dau_1a,\n" +
            "       dau_1b\n" +
            "from old_mysql_bpap.tb_ap_diminish_monitor\n" +
            "where toDate(create_time) = today()\n" +
            "  AND toHour(create_time) = toHour(now())\n" +
            "  and (\n" +
            "    (arpu_step6 < 0.05 and arpu_step3 < 0.05) or\n" +
            "    (arpu_step1 < 0 and arpu_step3 < 0 and dau_3b >= 200) or\n" +
            "    (arpu_step1 < 0 and dau_3b >= 200)\n" +
            "    );"})
    List<ProductArpuDown> queryProductArpuDown();


    @Select({"select count() as pv\n" +
            "from ods.event_dist\n" +
            "where logday = today()\n" +
            "  and time > addMinutes(now(), -5);"})
    List<CheckDataSumPv> queryOdsEventDistPv();


    @Select({"select a.product                       as product,\n" +
            "       c.product_name                  as product_name,\n" +
            "       a.pv_today                      as pv_today,\n" +
            "       a.gy_pv_today                   as gy_pv_today,\n" +
            "       a.rate_today                    as rate_today,\n" +
            "       b.pv_yes                        as pv_yes,\n" +
            "       b.gy_pv_yes                     as gy_pv_yes,\n" +
            "       b.rate_yes                      as rate_yes,\n" +
            "       round(rate_today - rate_yes, 2) as rate_down\n" +
            "from (\n" +
            "         SELECT product,\n" +
            "                count()                                as pv_today,\n" +
            "                count(if(gy_type in\n" +
            "                         ('idfa', 'caidMaping', 'caid', 'idfaMaping', 'androidId', 'adDeive',\n" +
            "                          'adOaid'), 1,\n" +
            "                         null))                        as gy_pv_today,\n" +
            "                round(gy_pv_today * 100 / pv_today, 2) as rate_today\n" +
            "         FROM mysql('pc-2zew4i2gf9v145p0t.rwlb.rds.aliyuncs.com:3306', 'coo-ocpc', 'ocpc_event', 'cn_read',\n" +
            "                    'gW3s8@L5N!xcbgD4')\n" +
            "         where create_time >= toDateTime(today())\n" +
            "           and create_time < toDateTime(today() + 1)\n" +
            "           and event_type = 0\n" +
            "         group by product\n" +
            "         ) a\n" +
            "         inner join\n" +
            "     (\n" +
            "         SELECT product,\n" +
            "                count()                            as pv_yes,\n" +
            "                count(if(gy_type in\n" +
            "                         ('idfa', 'caidMaping', 'caid', 'idfaMaping', 'androidId', 'adDeive',\n" +
            "                          'adOaid'), 1,\n" +
            "                         null))                    as gy_pv_yes,\n" +
            "                round(gy_pv_yes * 100 / pv_yes, 2) as rate_yes\n" +
            "         FROM mysql('pc-2zew4i2gf9v145p0t.rwlb.rds.aliyuncs.com:3306', 'coo-ocpc', 'ocpc_event', 'cn_read',\n" +
            "                    'gW3s8@L5N!xcbgD4')\n" +
            "         where create_time >= toDateTime(yesterday())\n" +
            "           and create_time < toDateTime(today())\n" +
            "           and event_type = 0\n" +
            "         group by product\n" +
            "         ) b on a.product = b.os.product\n" +
            "         global\n" +
            "         left join\n" +
            "     dwd.app_mapping_dist c\n" +
            "     on a.product = c.product\n" +
            "having (pv_yes > 1000\n" +
            "    and pv_today > 500\n" +
            "    and rate_down < -10)\n" +
            "    or (pv_today > 500 and rate_today < 70);"})
    List<OcpcRateBean> queryOcpcRate();



}
