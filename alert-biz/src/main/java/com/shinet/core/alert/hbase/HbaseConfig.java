package com.shinet.core.alert.hbase;

import org.apache.hadoop.conf.Configuration;
import org.apache.hadoop.hbase.HBaseConfiguration;
import org.apache.hadoop.hbase.client.Connection;
import org.apache.hadoop.hbase.client.ConnectionFactory;
import org.springframework.context.annotation.Bean;

import java.io.IOException;
@org.springframework.context.annotation.Configuration
public class HbaseConfig {
    @Bean(name = "toutiaoClickConnection")
    public Connection initToutiaoClickHbase() throws IOException {
        // 新建一个Configuration
        Configuration conf = HBaseConfiguration.create();
// 集群的连接地址(VPC内网地址)在控制台页面的数据库连接界面获得
        conf.set("hbase.zookeeper.quorum", "ld-2zeo06b341w4czco7-proxy-lindorm.lindorm.rds.aliyuncs.com:30020");
// xml_template.comment.hbaseue.username_password.default
        conf.set("hbase.client.username", "root");
        conf.set("hbase.client.password", "root");

        Connection connection = ConnectionFactory.createConnection(conf);
        return connection;
    }
}
