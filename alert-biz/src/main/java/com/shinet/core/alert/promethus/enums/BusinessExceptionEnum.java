package com.shinet.core.alert.promethus.enums;

import java.io.Serializable;

public enum BusinessExceptionEnum implements Serializable {

    PARAM_ERROR("PARAM_ERROR", "参数异常"),
    NONE_STRATEGY("NONE_STRATEGY", "未找到策略"),
    NONE_AD("NONE_AD", "无广告返回"),
    PROXY_REQ("PROXY_REQ", "代理请求"),
    SIGN_ERROR("SIGN_ERROR", "签名异常"),
    CACHE_ERROR("CACHE_ERROR", "缓存短策略"),
    EXPOSURE_LIMIT("EXPOSURE_LIMIT", "曝光达到限制次数");

    private String value;
    private String desc;

    public String getValue() {
        return value;
    }

    BusinessExceptionEnum(String value, String desc) {
        this.value = value;
        this.desc = desc;
    }

    public static String descFromValue(String value){
        for(BusinessExceptionEnum businessExceptionEnum : BusinessExceptionEnum.values()){
            if(businessExceptionEnum.value.equals(value)){
                return businessExceptionEnum.desc;
            }
        }
        return "";
    }
}
