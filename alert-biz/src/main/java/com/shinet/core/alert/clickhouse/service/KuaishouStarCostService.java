package com.shinet.core.alert.clickhouse.service;

import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.metadata.Sheet;
import com.alibaba.excel.metadata.Table;
import com.alibaba.excel.support.ExcelTypeEnum;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.ImmutableList;
import com.shinet.core.alert.common.SystemInfo;
import com.shinet.core.alert.dingtalk.DingTailService;
import com.shinet.core.alert.dsp.entity.KuaishouStarOrder;
import com.shinet.core.alert.dsp.entity.KuaishouStarTask;
import com.shinet.core.alert.dsp.entity.KuaishouSupplementOrderRealtime;
import com.shinet.core.alert.dsp.service.KuaishouStarOrderService;
import com.shinet.core.alert.dsp.service.KuaishouStarTaskService;
import com.shinet.core.alert.dsp.service.KuaishouSupplementOrderRealtimeService;
import com.shinet.core.alert.hbase.HbaseClickService;
import com.shinet.core.alert.util.UploadFileToOSSUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import redis.clients.jedis.Jedis;
import redis.clients.jedis.JedisCluster;
import redis.clients.jedis.JedisPool;

import java.io.File;
import java.io.FileOutputStream;
import java.math.BigInteger;
import java.time.DayOfWeek;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.format.DateTimeFormatter;
import java.time.temporal.TemporalAdjusters;
import java.util.*;
import java.util.stream.Collectors;

import static org.jacoco.agent.rt.internal_43f5073.core.runtime.AgentOptions.OutputMode.file;

@Slf4j
@Service
public class KuaishouStarCostService {

    @Autowired
    KuaishouStarOrderService kuaishouStarOrderService;

    @Autowired
    KuaishouSupplementOrderRealtimeService kuaishouSupplementOrderRealtimeService;

    @Autowired
    KuaishouStarTaskService kuaishouStarTaskService;

    @Autowired
    UploadFileToOSSUtil uploadFileToOSSUtil;

    @Autowired
    HbaseClickService hbaseClickService;


    private static final List<List<String>> TITLES = ImmutableList.of(
            ImmutableList.of("组别"),
            ImmutableList.of("产品"),
            ImmutableList.of("达人名称"),
            ImmutableList.of("达人id"),
            ImmutableList.of("总消耗")
    );


    private static final List<List<String>> TITLES2 = ImmutableList.of(
            ImmutableList.of("组别"),
            ImmutableList.of("产品"),
            ImmutableList.of("达人id"),
            ImmutableList.of("达人名称"),
            ImmutableList.of("是否创建新任务"),
            ImmutableList.of("总消耗")
    );

    public void starCostAlert() {
        // 查询advertiserId 下的所有taskId
        List<KuaishouStarTask> starTaskList = kuaishouStarTaskService.queryByAdertiserId("4256976886");

        // key 组名称  value appName

        Map<String, List<String>> taskNameMap = starTaskList.stream().filter(kuaishouStarTask -> StringUtils.isNotBlank(kuaishouStarTask.getTeamName()))
                .collect(Collectors.groupingBy(
                        KuaishouStarTask::getTeamName,
                        Collectors.mapping(
                                KuaishouStarTask::getAppName,
                                Collectors.toList()
                        )
                ));
        // key appName  value taskIdList
        Map<String, Set<BigInteger>> appTaskIdMap = starTaskList.stream().filter(kuaishouStarTask -> StringUtils.isNotBlank(kuaishouStarTask.getTeamName()))
                .collect(Collectors.groupingBy(
                        KuaishouStarTask::getAppName,
                        Collectors.mapping(
                                KuaishouStarTask::getTaskId,
                                Collectors.toSet()
                        )
                ));

        List<List<String>> dataList = new ArrayList<>();

        for (Map.Entry<String, List<String>> entry : taskNameMap.entrySet()) {
            String teamName = entry.getKey();
            List<String> appNameList = entry.getValue();

            Map<String, Double> appCostMap = new HashMap<>();
            for (String appName : appNameList) {
                Set<BigInteger> taskIdList = appTaskIdMap.get(appName);
                // 查询3天前的助推消耗
                List<KuaishouSupplementOrderRealtime> supplementOrderCosts = kuaishouSupplementOrderRealtimeService.queryTaskSupplementCostByAdvertiserId("4256976886", taskIdList, 3);
                // 查询3天前视频的消耗
                List<KuaishouStarOrder> videoOrderCosts = kuaishouStarOrderService.queryVideoTaskCostByAdvertiserId("4256976886", taskIdList, 3);
                // 聚合appName的消耗
                if (!supplementOrderCosts.isEmpty()) {
                    for (KuaishouSupplementOrderRealtime supplementOrderCost : supplementOrderCosts) {
                        if (appCostMap.containsKey(appName)) {
                            appCostMap.computeIfPresent(appName, (k, cost) -> cost + (supplementOrderCost.getSumCost()));
                        } else {
                            appCostMap.put(appName, supplementOrderCost.getSumCost());
                        }
                    }
                }

                if (!videoOrderCosts.isEmpty()) {
                    for (KuaishouStarOrder videoOrderCost : videoOrderCosts) {
                        if (appCostMap.containsKey(appName)) {
                            appCostMap.computeIfPresent(appName, (k, cost) -> cost + videoOrderCost.getSumCost());
                        } else {
                            appCostMap.put(appName, videoOrderCost.getSumCost());
                        }
                    }
                }
            }


            //  取出消耗前三的任务
            List<String> topThreeAppNames = getTopThreeKeys(appCostMap);

            // 每个任务下top5消耗的达人
            for (String appName : topThreeAppNames) {
                Set<BigInteger> taskIdList = appTaskIdMap.get(appName);
                List<KuaishouSupplementOrderRealtime> kuaishouSupplementOrderRealtimes = kuaishouSupplementOrderRealtimeService.queryUserSupplementCostByAdvertiserId("4256976886", taskIdList, 3);
                // 组别/taskName   产品/taskIdNameMap.get(String.valueof(taskId))     达人名称/KuaishouSupplementOrderRealtime.getStarName    达人id/KuaishouSupplementOrderRealtime.getStarUserId  总消耗/sumCost
                buildDataList(dataList, teamName, appName, kuaishouSupplementOrderRealtimes);

            }

        }

        if (dataList.isEmpty()) {
            return;
        }

        sendDingTalk(dataList, "快手星任务-视频消耗", TITLES, "Alert-快手星任务-视频消耗", "快手星任务-视频消耗数据下载");
        //DingTailService.sendMsg("fee212bedd699c3b2b837e16106a687c71dd9ff2502738003483e72e9a0a9861","快手星任务-视频消耗",downloadUrl);

        // 保存当前数据
        new Thread(() -> {
            hbaseClickService.saveKuaishouSatrData2Hbase("kuaishou:star:data" + LocalDateTime.now().getDayOfWeek(), dataList);
        }).start();

    }

    private void sendDingTalk(List<List<String>> dataList, String fileName, List<List<String>> titles, String key, String msg) {
        File file = null;
        try {
            file = exportExcel("", fileName, "", dataList, titles);
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
        String downloadUrl = uploadFileToOSSUtil.uploadFileToOss(file, "kuaishouStarTaskOffLineData/");

        Set<String> phoneSet = new HashSet<>();
        phoneSet.add("15680878462");
        DingTailService.sendMsgAndPhone("6fe24f8a09b3df3c53615d9e37b7cd0ecb9f689b2533841bff73933156951252", key, msg, downloadUrl, phoneSet);
    }

    private void buildDataList(List<List<String>> dataList, String teamName, String appName, List<KuaishouSupplementOrderRealtime> kuaishouSupplementOrderRealtimes) {
        for (KuaishouSupplementOrderRealtime kuaishouSupplementOrderRealtime : kuaishouSupplementOrderRealtimes) {
            List<String> data = new ArrayList<>();
            data.add(teamName); // 组名
            data.add(appName); // 产品
            data.add(kuaishouSupplementOrderRealtime.getStarName()); // 达人名称
            data.add(String.valueOf(kuaishouSupplementOrderRealtime.getStarUserId())); // 达人id
            data.add(String.valueOf(kuaishouSupplementOrderRealtime.getSumCost()));
            dataList.add(data);
        }
    }

    private List<String> getTopThreeKeys(Map<String, Double> appCostMap) {
        if (appCostMap.isEmpty()) {
            return Collections.emptyList();
        }

        // 将 Map 中的 entry 转换为一个列表
        List<Map.Entry<String, Double>> entryList = new ArrayList<>(appCostMap.entrySet());

        // 对列表按照 value 进行降序排序
        entryList.sort((entry1, entry2) -> Double.compare(entry2.getValue(), entry1.getValue()));

        // 取出前三个 entry 的 key
        List<String> topThreeKeys = new ArrayList<>();
        for (int i = 0; i < Math.min(3, entryList.size()); i++) {
            topThreeKeys.add(entryList.get(i).getKey());
        }
        return topThreeKeys;
    }


    private File exportExcel(String day, String fileName, String sheetBaseName, List<List<String>> dataList, List<List<String>> titles) throws Exception {
        String savePath = "/data/tempCanDelete/";
        if (SystemInfo.isWin()) {
            savePath = "E:/data/temp/";
        }
        //文件保存位置
        File saveDir = new File(savePath);
        if (!saveDir.exists()) {
            saveDir.mkdir();
        }
        File file = new File(saveDir + File.separator + day + fileName + ".xlsx");
        FileOutputStream fos = new FileOutputStream(file);
        try {
            ExcelWriter writer = new ExcelWriter(fos, ExcelTypeEnum.XLSX);
            // 设置标题
            Table table = new Table(1);
            table.setHead(titles);
            // 创建SHEET
            Sheet sheet = new Sheet(1, 0);
            sheet.setSheetName(sheetBaseName);
            writer.write0(dataList, sheet, table);
            writer.finish();
            fos.flush();
        } finally {
            fos.close();
        }
        return file;
    }


    public void starTaskTf() {
        // 周二周三查询周一存储的数据  周一周五查询周四存储的数据
        DayOfWeek dayOfWeek = LocalDateTime.now().getDayOfWeek();
        if (dayOfWeek == DayOfWeek.TUESDAY || dayOfWeek == DayOfWeek.WEDNESDAY) {
            dayOfWeek = DayOfWeek.MONDAY;
        } else if (dayOfWeek == DayOfWeek.MONDAY || dayOfWeek == DayOfWeek.FRIDAY) {
            dayOfWeek = DayOfWeek.THURSDAY;
        } else {
            return;
        }

        List<List<String>> exportExcelList = new ArrayList<>();

        String kuaishouSatrData = hbaseClickService.getKuaishouSatrData("kuaishou:star:data" + dayOfWeek);

        if (StringUtils.isBlank(kuaishouSatrData)) {
            return;
        }

        List<List> dataList = JSONObject.parseArray(kuaishouSatrData, List.class);

        if (dataList.isEmpty()) {
            return;
        }

        // 转化为map key appName  value 达人名称+达人id
        Map<String, List<String>> appUserMap = new HashMap<>();
        for (List list : dataList) {
            String appName = (String) list.get(1);
            String userName = (String) list.get(2);
            String userId = (String) list.get(3);
            if (appUserMap.containsKey(appName)) {
                appUserMap.get(appName).add(userId + ":" + userName);
            } else {
                List<String> userList = new ArrayList<>();
                userList.add(userId + ":" + userName);
                appUserMap.put(appName, userList);
            }
        }

        // 选择时间。周五、周一：周四预警时间到现在    周二周三：周一预警时间到现在

        String preCreateTime = null;
        String afterCreateTime = null;
        DayOfWeek nowDayOfWeek = LocalDateTime.now().getDayOfWeek();
        if (nowDayOfWeek == DayOfWeek.TUESDAY || nowDayOfWeek == DayOfWeek.WEDNESDAY) {
            preCreateTime = LocalDateTime.now().with(TemporalAdjusters.previous(DayOfWeek.MONDAY)).with(LocalTime.of(9, 0)).format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
            afterCreateTime = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
        } else if (nowDayOfWeek == DayOfWeek.MONDAY || nowDayOfWeek == DayOfWeek.FRIDAY) {
            preCreateTime = LocalDateTime.now().with(TemporalAdjusters.previousOrSame(DayOfWeek.THURSDAY)).with(LocalTime.of(9, 0)).format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
            afterCreateTime = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
        } else {
            return;
        }

        Set<String> appNameSet = appUserMap.keySet();
        // 查询最新创建的appName，
        List<KuaishouStarTask> starTaskList = kuaishouStarTaskService.queryByAdertiserIdAndCreatetime("4256976886", preCreateTime, afterCreateTime, appNameSet);

        if (starTaskList == null || starTaskList.isEmpty()) {
            for (String appName : appNameSet) {
                buildDataList2(exportExcelList, "", appName, appUserMap.get(appName), false, null);
            }
            sendDingTalk(exportExcelList, "达人新任务投放效果", TITLES2, "Alert-快手达人新任务订单创建", "快手达人新任务订单创建数据下载");
            return;
        }

        Set<String> subAppNameSet = starTaskList.stream().map(KuaishouStarTask::getAppName).collect(Collectors.toSet());


        // key 组名称  value appName
        Map<String, List<String>> taskNameMap = starTaskList.stream().filter(kuaishouStarTask -> StringUtils.isNotBlank(kuaishouStarTask.getTeamName()))
                .collect(Collectors.groupingBy(
                        KuaishouStarTask::getTeamName,
                        Collectors.mapping(
                                KuaishouStarTask::getAppName,
                                Collectors.toList()
                        )
                ));


        // key appName  value 组名称
        Map<String, String> appTeamMap = starTaskList.stream()
                .filter(kuaishouStarTask -> StringUtils.isNotBlank(kuaishouStarTask.getTeamName()))
                .collect(Collectors.toMap(
                        KuaishouStarTask::getAppName,       // Key Mapper
                        KuaishouStarTask::getTeamName,      // Value Mapper
                        (existingValue, newValue) -> existingValue // 合并函数：保留旧值
                ));


        // key appName  value taskIdList
        Map<String, Set<BigInteger>> appTaskIdMap = starTaskList.stream().filter(kuaishouStarTask -> StringUtils.isNotBlank(kuaishouStarTask.getTeamName()))
                .collect(Collectors.groupingBy(
                        KuaishouStarTask::getAppName,
                        Collectors.mapping(
                                KuaishouStarTask::getTaskId,
                                Collectors.toSet()
                        )
                ));


        if (appNameSet.size() > subAppNameSet.size()) {
            // 排除 appNameSet 中存在于 subAppNameSet 中的元素
            List<String> result = appNameSet.stream()
                    .filter(item -> !subAppNameSet.contains(item))
                    .collect(Collectors.toList());

            // 更新taskNameMap
            for (Map.Entry<String, List<String>> entry : taskNameMap.entrySet()) {
                List<String> appNames = entry.getValue().stream().filter(item -> !result.contains(item)).collect(Collectors.toList());
                taskNameMap.put(entry.getKey(), appNames);
            }

            for (String appName : result) {
                // 构建exportExcelList
                buildDataList2(exportExcelList, appTeamMap.get(appName), appName, appUserMap.get(appName), false, null);
            }

        }

        for (Map.Entry<String, List<String>> entry : taskNameMap.entrySet()) {
            String teamName = entry.getKey();
            List<String> appNameList = entry.getValue();


            for (String appName : appNameList) {
                Set<BigInteger> taskIdList = appTaskIdMap.get(appName);

                // 查询任务下所有达人的总消耗
                List<KuaishouSupplementOrderRealtime> kuaishouSupplementOrderRealtimes = kuaishouSupplementOrderRealtimeService.queryUserSupplementCostByAdvertiserId2("4256976886", taskIdList, preCreateTime);


                // 聚合每个达人的消耗  key userId+userName  value cost
                Map<String, Double> userCostMap = new HashMap<>();

                for (KuaishouSupplementOrderRealtime supplementOrderCost : kuaishouSupplementOrderRealtimes) {
                    String key = supplementOrderCost.getStarUserId() + ":" + supplementOrderCost.getStarName();
                    if (!userCostMap.containsKey(key)) {
                        userCostMap.put(key, supplementOrderCost.getSumCost());
                    } else {
                        userCostMap.put(key, supplementOrderCost.getSumCost() + userCostMap.get(key));
                    }
                }


                List<String> userList = appUserMap.get(appName);
                for (String userIdName : userList) {

                    Double sumCost = userCostMap.get(userIdName);

                    buildDataList2(exportExcelList, teamName, appName, Collections.singletonList(userIdName), true, sumCost);
                }


            }

        }

        sendDingTalk(exportExcelList, "达人新任务投放效果", TITLES2, "Alert-快手达人新任务订单创建", "快手达人新任务订单创建数据下载");
    }

    private void buildDataList2(List<List<String>> exportExcelList, String teamName, String appName, List<String> userIdNameList, boolean createNewTask, Double sumCost) {
        for (String user : userIdNameList) {
            List<String> data = new ArrayList<>();
            String userId = user.split(":")[0];
            String userName = user.split(":")[1];
            data.add(teamName);
            data.add(appName);
            data.add(userId);
            data.add(userName);
            data.add(createNewTask ? "创建新任务" : "未创建新任务");
            data.add(String.valueOf(sumCost));
            exportExcelList.add(data);
        }
    }


}
