package com.shinet.core.alert.clickhouse.entity;

import lombok.Data;

/**
 * <AUTHOR>
 * @since 2022/4/21
 */
@Data
public class OcpcRateBean {
    private String product;
    private String productName;
    private Integer pvToday;
    private Integer gyPvToday;
    private Double rateToday;
    private Integer pvYes;
    private Integer gyPvYes;
    private Double rateYes;
    private Double rateDown;

    public String getFloorP(Double p) {
        return Math.round(Math.floor(p)) + "%";
    }
}
