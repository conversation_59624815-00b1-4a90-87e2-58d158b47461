package com.shinet.core.alert.clickhouse.service;

import com.shinet.core.alert.clickhouse.entity.AdExposureChData;
import com.shinet.core.alert.clickhouse.entity.AdExposureChannelManfi;
import com.shinet.core.alert.clickhouse.mapper.ck1.ExposureOdsMapper;
import com.shinet.core.alert.coreservice.entity.AlertRecord;
import com.shinet.core.alert.coreservice.enums.AlertModel;
import com.shinet.core.alert.coreservice.enums.AlertRoute;
import com.shinet.core.alert.coreservice.enums.AlertStatus;
import com.shinet.core.alert.coreservice.enums.AlertType;
import com.shinet.core.alert.coreservice.service.AlertRecordService;
import com.shinet.core.alert.dingtalk.DingTailService;
import com.shinet.core.alert.util.DoubleUtil;
import com.xxl.job.core.log.XxlJobLogger;
import io.swagger.models.auth.In;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.joda.time.DateTimeConstants;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

@Slf4j
@Service
public class AdChannelExposureService {
    @Resource
    ExposureOdsMapper exposureOdsMapper;
    @Resource
    AlertRecordService alertRecordService;

    public void altExposureChannel(String jobName,Integer maxChannel,Integer maxManfi){
        List<AdExposureChannelManfi>  manfiList = exposureOdsMapper.getAllExposureChannel(maxChannel,maxManfi);

        String altChMsg = "";
        String altManMsg = "";
        for(AdExposureChannelManfi adExposureChannelManfi : manfiList){

            if(adExposureChannelManfi.getChannelNum()!=null && (adExposureChannelManfi.getChannelNum()>maxChannel)){
                altChMsg = altChMsg+" "+adExposureChannelManfi.getProduct()+"->"+adExposureChannelManfi.getChannelNum();
            }

            if(adExposureChannelManfi.getManNum()!=null && (adExposureChannelManfi.getManNum()>maxManfi)){
                altManMsg = altManMsg+" "+adExposureChannelManfi.getProduct()+"->"+adExposureChannelManfi.getManNum();
            }
        }



        if(StringUtils.isNotBlank(altChMsg)){
            AlertRecord alertRecord = alertRecordService.insertAlertRecord(jobName,
                    jobName, AlertModel.androidChannelNum, altChMsg
                    , AlertStatus.INIT, DingTailService.dset, AlertType.DINGDING);
            DingTailService.sendMarkdownMsg(alertRecord, AlertRoute.JISHU.value);

            XxlJobLogger.log("渠道数 "+altChMsg);
        }

        if(StringUtils.isNotBlank(altManMsg)){
            AlertRecord alertRecord = alertRecordService.insertAlertRecord(jobName,
                    jobName, AlertModel.androidManNum, altManMsg
                    , AlertStatus.INIT, DingTailService.dset, AlertType.DINGDING);
            DingTailService.sendMarkdownMsg(alertRecord, AlertRoute.JISHU.value);

            XxlJobLogger.log("机型数 "+altManMsg);
        }
    }

    public void altExposurePvChannel(String jobName, Double lv, Integer minuv){
        Date todate = new Date();
        long startTime = todate.getTime()- DateTimeConstants.MILLIS_PER_MINUTE*30;
        long endTime = todate.getTime();


        Date yesDate = new Date(System.currentTimeMillis()-DateTimeConstants.MILLIS_PER_DAY);
        long yesStartTime = yesDate.getTime()- DateTimeConstants.MILLIS_PER_MINUTE*30;
        long yesEndTime = yesDate.getTime();
        List<AdExposureChData> manfiList = exposureOdsMapper.getAllChannelData(startTime,endTime,yesStartTime,yesEndTime,minuv);

        String altChMsg = "";
        String altManMsg = "";
        for(AdExposureChData adExposureChData : manfiList){

            if(adExposureChData.getPvLv()!=null && (adExposureChData.getPvLv()<=lv)){
                altChMsg = altChMsg+" "+adExposureChData.getProduct()+"-"+adExposureChData.getChannel()+"->"+ DoubleUtil.getDoubleByTwo(adExposureChData.getTdPv())+"/"+DoubleUtil.getDoubleByTwo(adExposureChData.getYesPv()) +"="+DoubleUtil.getDoubleByTwo(adExposureChData.getPvLv());
            }

            if(adExposureChData.getSrlv()!=null && (adExposureChData.getSrlv()<=lv)){
                altManMsg = altManMsg+" "+adExposureChData.getProduct()+"-"+adExposureChData.getChannel()+"->"+DoubleUtil.getDoubleByTwo(adExposureChData.getTdSr())+"/"+DoubleUtil.getDoubleByTwo(adExposureChData.getYesSr())+"="+ DoubleUtil.getDoubleByTwo(adExposureChData.getSrlv());
            }
        }



        if(StringUtils.isNotBlank(altChMsg)){
            AlertRecord alertRecord = alertRecordService.insertAlertRecord(jobName,
                    jobName, AlertModel.channelPvLv, altChMsg
                    , AlertStatus.INIT, DingTailService.dset, AlertType.DINGDING);
            DingTailService.sendMarkdownMsg(alertRecord, AlertRoute.JISHU.value);

            XxlJobLogger.log("渠道pv波动 "+altChMsg);
        }

        if(StringUtils.isNotBlank(altManMsg)){
            AlertRecord alertRecord = alertRecordService.insertAlertRecord(jobName,
                    jobName, AlertModel.channelSrLv, altManMsg
                    , AlertStatus.INIT, DingTailService.dset, AlertType.DINGDING);
            DingTailService.sendMarkdownMsg(alertRecord, AlertRoute.JISHU.value);

            XxlJobLogger.log("渠道收入波动 "+altManMsg);
        }
    }
}
