package com.shinet.core.alert.dataagent.service;

import cn.hutool.core.thread.RejectPolicy;
import cn.hutool.core.thread.ThreadUtil;
import com.xxl.job.core.log.XxlJobLogger;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.FileUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.io.File;
import java.io.IOException;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.*;
import java.util.concurrent.atomic.AtomicInteger;

@Component
@Slf4j
public class CommonUserService {
    @Autowired
    UserMoveService userMoveService;
    ThreadFactory threadFactory = ThreadUtil.newNamedThreadFactory("userMove", null, false);
    ExecutorService executorService = new ThreadPoolExecutor(
            5
            , 5,
            20,
            TimeUnit.SECONDS,
            new ArrayBlockingQueue<>(20),
            threadFactory,
            RejectPolicy.CALLER_RUNS.getValue()
    );
    public void moveProductUser(String filePath,boolean isDelStart,boolean isMoveUser) {
        try {
            File fileTh = new File(filePath);
            if(isCompleteFile(filePath)){
                return;
            }
            AtomicInteger saveNum = new AtomicInteger(0);
            long ctime = System.currentTimeMillis();
            Map<Integer,List<Long>>  dmap = getUidList(fileTh);
            List<CompletableFuture> completableFutureList = new ArrayList<>();
            dmap.forEach((pkgId,uIdList)->{
                int page = 2000;
                for(int i=0;i<=uIdList.size()/page;i++){
                    try {
                        int start = i*page;
                        int end = (i+1)*page>uIdList.size()?uIdList.size():(i+1)*page;
                        List<Long> duidList=  uIdList.subList(start,end);
                        CompletableFuture completableFuture = CompletableFuture.runAsync(() -> {
                            try {
                                if(isMoveUser){
                                    userMoveService.moveUserMetas(duidList,pkgId,saveNum,isDelStart,uIdList.size());
                                }else{
                                    userMoveService.moveUserMetasOpenIds(duidList,pkgId,saveNum,isDelStart,uIdList.size());
                                }
                            } catch (Exception e) {
                                log.error("", e);
                            }
                        }, executorService);
                        completableFutureList.add(completableFuture);

                    }catch (Exception e){
                        log.error("",e);
                    }
                }
            });
            CompletableFuture<Void> combindFuture = CompletableFuture.allOf(completableFutureList.toArray(new CompletableFuture[completableFutureList.size()]));
            try {
                combindFuture.get();
            } catch (Exception e) {
                XxlJobLogger.log("", e);
            }
            XxlJobLogger.log("数据迁移完成 共保存 "+ saveNum +"条数据 耗时"+(System.currentTimeMillis()- ctime)+ ""+filePath);
            log.info("数据迁移完成 共保存 "+ saveNum +"条数据 耗时"+(System.currentTimeMillis()- ctime)+ ""+filePath);

        } catch (Exception e) {
            log.error("",e);
        }
    }


    public void moveToProductUser(String filePath,boolean isDelStart,boolean isMoveUser) {
        try {
            File fileTh = new File(filePath);
            if(isCompleteFile(filePath)){
                return;
            }
            AtomicInteger saveNum = new AtomicInteger(0);
            long ctime = System.currentTimeMillis();
            Map<Integer,List<Long>>  dmap = getUidList(fileTh);
            List<CompletableFuture> completableFutureList = new ArrayList<>();
            dmap.forEach((pkgId,uIdList)->{
                int page = 2000;
                for(int i=0;i<=uIdList.size()/page;i++){
                    try {
                        int start = i*page;
                        int end = (i+1)*page>uIdList.size()?uIdList.size():(i+1)*page;
                        List<Long> duidList=  uIdList.subList(start,end);

                        CompletableFuture completableFuture = CompletableFuture.runAsync(() -> {
                            try {
                                userMoveService.moveUserTos(duidList);
                            } catch (Exception e) {
                                log.error("", e);
                            }
                        }, executorService);
                        completableFutureList.add(completableFuture);

                    }catch (Exception e){
                        log.error("",e);
                    }
                }
            });
            CompletableFuture<Void> combindFuture = CompletableFuture.allOf(completableFutureList.toArray(new CompletableFuture[completableFutureList.size()]));
            try {
                combindFuture.get();
            } catch (Exception e) {
                XxlJobLogger.log("", e);
            }
            XxlJobLogger.log("数据迁移完成 共保存 "+ saveNum +"条数据 耗时"+(System.currentTimeMillis()- ctime)+ ""+filePath);
            log.info("数据迁移完成 共保存 "+ saveNum +"条数据 耗时"+(System.currentTimeMillis()- ctime)+ ""+filePath);

        } catch (Exception e) {
            log.error("",e);
        }
    }

    public File getAlDelFile(String filePath){
        File fileTh = new File(filePath);
        File file = new File(fileTh.getParentFile().getAbsoluteFile()+"/allDel");
        if(!file.exists()){
            file.mkdirs();
        }
        return file;
    }
    private boolean isCompleteFile(String filePath){
        File fileTh = new File(filePath);
        File file = getAlDelFile(filePath);

        File[] dfile = file.listFiles();
        for(File completeFile : dfile){
            String rFileName = fileTh.getName().split("udel")[0];
            String fileName2 = completeFile.getName().split("udel")[0];
            if(StringUtils.equalsIgnoreCase(rFileName,fileName2)){
                log.info(filePath+" 已经迁移完成，直接跳过");
                return true;
            }
        }
        return false;
    }

    private Map<Integer,List<Long>> getUidList(File file) throws IOException {
        Map<Integer,List<Long>> dmap = new HashMap<>();
        List<String> lineList = FileUtils.readLines(file);
        int allNum = lineList.size();
        for(String contentLine : lineList){
            contentLine = contentLine.replaceAll("\"","");
            String[] userPkgs = contentLine.split(",");

            List<Long> ulist = dmap.get(Integer.parseInt(userPkgs[1]));
            if(ulist==null){
                ulist = new ArrayList<>();
            }
            ulist.add(Long.parseLong(userPkgs[0]));
            dmap.put(Integer.parseInt(userPkgs[1]),ulist);
        }
        return dmap;
    }
}
