package com.shinet.core.alert.safe.mapper;

import com.shinet.core.alert.dsp.entity.DspCostAlertBean;
import com.shinet.core.alert.safe.entity.IosIpRst;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.shinet.core.alert.safe.entity.IosLockRateVo;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * <p>
 *  Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2024-05-22
 */
public interface IosIpRstMapper extends BaseMapper<IosIpRst> {
    @Select({" select b.product,b.cnum as unlockNum,a.cnum as lockNum,a.cnum/(a.cnum+b.cnum)*100 as lockRate from ( " +
            "select product,count(1) as cnum from lock_ios_rst where  create_time>'${ctimeStr}' and  lock_flag='true' GROUP BY product " +
            ") a LEFT JOIN ( " +
            "select product,count(1) as cnum from lock_ios_rst where  create_time>'${ctimeStr}' and  lock_flag='false' GROUP BY product " +
            ") b on a.product = b.product order by b.cnum desc "})
    public List<IosLockRateVo> queryLockRate(@Param("ctimeStr") String ctimeStr);


    @Select({" select b.product,b.cnum as unlockNum,a.cnum as lockNum,a.cnum/(a.cnum+b.cnum)*100 as lockRate from ( " +
            " select product,count(1) as cnum from ios_ip_rst where  create_time>'${ctimeStr}' and  lock_flag='1' GROUP BY product  " +
            " ) a LEFT JOIN ( " +
            " select product,count(1) as cnum from ios_ip_rst where  create_time>'${ctimeStr}' and  lock_flag='0' GROUP BY product " +
            " ) b on a.product = b.product order by b.cnum desc "})
    public List<IosLockRateVo> queryLockIpRate(@Param("ctimeStr") String ctimeStr);




}
