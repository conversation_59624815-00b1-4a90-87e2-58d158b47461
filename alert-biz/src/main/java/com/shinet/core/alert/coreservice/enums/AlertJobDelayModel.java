package com.shinet.core.alert.coreservice.enums;

/**
 * <AUTHOR>
 * @since 2022/4/21
 */
public enum  AlertJobDelayModel {
    WITHDRAW_CK("withdraw-ck-check-alert","提现累积异常核查"),
    WITHDRAW_ALERT("withdraw-ck-alert","提现波动核查"),
    WITHDRAW_AMOUNT("withdraw-bigAmount-alert","大额提现核查"),
    WITHDRAW_CHANNEL("withdraw-channel-alert","提现渠道核查"),
    WITHDRAW_CHANNEL_NO_POINT("withdraw-channel-nop-alert","无埋点提现渠道核查"),
    WITHDRAW_CHANNEL_BIG_CHECK("withdraw-channel-big-alert","渠道新增用户提现异常核查"),
    WITHDRAW_FLOAT_POINT("withdraw-float-alert","提现波动核查"),
    WITHDRAW_NUM_ABNORMAL_ALERT("withdraw-num-abnormal-alert","提现波动核查"),
    AD_CK_PV("ad-ck-pv-check-alert","PV前一小时波动核查"),
    AD_CK_PV_ALL_DEV("ad-ck-pv-check-alert","全部设备PV前一小时波动核查"),
    AD_KS_CALL_BACK("ad-ck-ks-call-alert","快手Reward回传核查"),
    IOS_AppStore_Check_NO("IOS-AppStore-Check-NO","IOS-AppStore-Check-NO"),
    IOS_AppStore_Check_SCORE("IOS-AppStore-Check-Score","IOS-AppStore-Check-Score"),
    AD_ARPU_CAP("ad-ck-arpu-cpa-alert","实时ARPU-CPA核查"),
    FAILED_REQUEST("ad-failed-request-alert","实时视频失败请求率"),
    AD_CK_ARPU("ad-ck-arpu-check-alert","ARPU大于5核查"),
    AD_CK_ECPM("ad-ck-ecpm-check-alert","ECPM前一小时波动核查"),
    AD_PV_GAP("ad-third-own-check-alert","我方三方数据核查"),
    AD_PV_GAP_REALTIME("ad-third-own-check-alert-realtime","我方三方数据核查实时"),
    CPA_CK("cpa-ck-check-alert","CPA累计核查"),
    FILTER_CK("filter-ck-check-alert","自然量异常高"),
    USER_ACTIVE("filter-user-active-alert","用户自然量异常高"),
    USER_ACTIVE_NUM("filter-user-active-num-alert","用户激活数量异常"),
    CPA_HOUR_REALTIME("cpa-realtime-hour-alert","CPA波动核查"),
    CPA_BID_REALTIME("cpa-bid-num-alert","CPA-BID快手达人"),
    CPA_BID_ZT("cpa-bid-zt-alert","CPA-快手助推"),
    DSP_COST("dsp-cost-check-alert","资金流水消耗核查"),
    STORE_ACTIVE("store-active-check-alert","商店新增核查"),
    KSDR_COST("ksdrzt-dr-check-alert","快手达人助推核查"),
    ALREADY_GRAY_USER("already-gray-num-alert","已拉黑人数核查"),
    ALL_DSP_COST("check-allDsp-cost-alert","账号消耗监控"),
    ALL_AD_CPA_BID("check-cpaBid-alert","账号消耗监控"),
    ALL_DSP_OPTIMIZATION("check-optimization-alert","优化目标监控"),
    CLICK_EXPOSURE("check-click-third-alert","三方点击回调监控"),
    SUPPLEMENT_AMOUNT("check-amount-alert","助推预算监控"),
    CHECK_AZKABAN("check-azkaban-alert","Azkaban任务监控"),
    CHECK_AZKABAN_HOUR("check-azkaban-alert-hour","Azkaban任务监控-超一小时"),
    CHECK_INCOME("check-daily-income-alert","日报收入监控"),
    USER_ACTIVE_ALI("filter-user-active-alert-ali","用户自然量异常高-Aliyun"),
    KSDR_COST_FEE("check-ksdrzt-realtime-fee","快手达人服务费监控"),
    TTDR_COST("check-ttdr-cost","巨量星图我方预估消耗监控"),
    HTTP_NOT_2xx("http-status-check","HTTP返回状态监控"),
    LOCKED_IP_RATE("locked-ip-rate-check","锁区渠道IP监控"),
    ANDROID_LOCK_ALERT("android-lock-alert","android锁区监控"),
    WITHDRAW_IP_ALERT("withdraw-ip-alert","提现ip监控"),
    WITHDRAW_IP_ALERT_PRODUCT("withdraw-ip-alert-sum","自然量ip聚集提现异常"),
    DEVICE_NEW_ALERT("device-new-alert","新用户占比监控"),
    WITHDRAW_NATURAL_ALERT("withdraw-natural-alert","自然量提现占比监控"),
    REALTIME_RESULT_HOUR_CHECK("realtime-result-hour-check","实时日报每小时收入监控"),
    DAILY_RESULT_INCOME_CHECK("daily-result-income-check","产品日报收入监控"),
    DAILY_RESULT_CHANNEL_INCOME_CHECK("daily-result-channel-income-check","产品日报各渠道收入监控"),
    NATUREAL_WITHDRAW_CHANNEL_LIMIT_CHECK("withdraw-channel-limit-alert","产品配置自然量渠道拦截订单异常"),
    PROFIT_REALTIME_HOUR_ALERT("profit-realtime-hour-alert","当日预估毛利异常"),
    SDK_RETURN_PROPORTION_HIGH("sdk-return-proportion-high","sdk回传占比过高"),
    CHANNEL_NATURAL_PROPORTION("channel-natural-proportion","分渠道自然量占比异常"),
    PRODUCT_NO_EXPOSURE_PROPORTION("product-no-exposure-proportion","产品分平台无曝光占比异常"),
    CHANNEL_HOUR_PV("channel-hour-pv","渠道pv数据异常"),
    ROI_FLUCTUATE_EXCEPTION("roi-fluctuate-exception","ROI波动异常"),
    WOOKMARK_SCREEN_PV("wookmark-screen-pv","瀑布流激励视频PV异常"),
    REALTIME_COST_HOUR("realtime_cost_hour","各渠道小时消耗异常"),
    TOUTIAO_COMPANY_COST_DAILY("toutiao-company-cost-daily","各主体每日限流预警"),
    TODAY_NEW_AU_INCOME_ALERT("today-new-au-income-alert","今日新增设备收入监控"),
    TODAY_OLD_AU_INCOME_ALERT("today-old-au-income-alert","今日老设备收入监控"),
    TODAY_GRAY_USER_IP_SUM_ALERT("today-gray-user-ip-sum-alert","今日IP拉黑用户数小时监控"),
    TODAY_PRODUCT_ECPM_HIGN_ALERT("today-product-ecpm-high-alert","穿山甲bidding ECPM过高"),
    TODAY_PRODUCT_ARPU_DOWN_ALERT("today-product-arpu-down-alert","广告位实验衰减预警"),
    TODAY_OS_ECPM_ALERT("today-os-ecpm-alert","实时ECPM变化预警"),
    TODAY_OCPC_EXACT_RATE_ALERT("today-ocpc-exact-rate-alert","归因精准率预警"),
    REALTIME_OS_PRODUCT_ECPM_ALERT("realtime-os-product-ecpm-alert","实时产品ECPM预警"),
    REALTIME_OS_PRODUCT_ARPU_ADJUST_ALERT("realtime-os-product-arpu-adjust-alert","穿山甲渠道ECPM异常升高预警（已自动调价）"),
    ;
    private String jobId;
    private String jobName;

    AlertJobDelayModel(String jobId, String jobName) {
        this.jobId = jobId;
        this.jobName = jobName;
    }

    public static AlertJobDelayModel getByJobId(String jobId){
        if (jobId == null){
            return null;
        }
        for (AlertJobDelayModel model : values()){
            if (model.getJobId().startsWith(jobId) || jobId.startsWith(model.getJobId())){
                return model;
            }
        }
        return null;
    }

    public String getJobId() {
        return jobId;
    }

    public void setJobId(String jobId) {
        this.jobId = jobId;
    }

    public String getJobName() {
        return jobName;
    }

    public void setJobName(String jobName) {
        this.jobName = jobName;
    }
}
