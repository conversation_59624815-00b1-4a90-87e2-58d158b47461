package com.shinet.core.alert.promethus;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.shinet.core.alert.common.HttpClientService;
import com.shinet.core.alert.util.DoubleUtil;
import com.xxl.job.core.log.XxlJobLogger;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.HashMap;

@Slf4j
@Component
public class MemCpuService {
    @Autowired
    HttpClientService httpClientService;

    public static String reqBseQueryV2URL= "http://172.16.11.185:9090/api/v1/query?query=";
    /**
     *
     * @param nodeGroupName  ck-node
     * @param limitRate limitRate
     */
    public String cpuAlert(String nodeGroupName,Double limitRate){
        //(1%20-%20avg(irate(node_cpu_seconds_total%7Bjob%3D~%22bpap-node%22%2Cmode%3D%22idle%22%7D%5B5m%5D))%20by%20(instance))%20*%20100
        long curMils = System.currentTimeMillis()/1000L;
        String promethusReqUrl = reqBseQueryV2URL+"(1%20-%20avg(irate(node_cpu_seconds_total%7Bjob%3D~%22"+nodeGroupName+"%22%2Cmode%3D%22idle%22%7D%5B5m%5D))%20by%20(instance))%20*%20100&time="+curMils;
        String alertMsg = getRateMsg(nodeGroupName,promethusReqUrl,limitRate,"cpu");
        log.info(alertMsg);
        return alertMsg;
    }


    public String memAlert(String nodeGroupName,Double limitRate){
        //count(node_cpu_seconds_total{job=~"ck-node",mode='system'}) by (instance)
        //http://172.16.11.185:9090/api/v1/query?query=(1 - (node_memory_MemAvailable_bytes{job=~"ck-node"} / (node_memory_MemTotal_bytes{job=~"ck-node"})))* 100&time=1656919407.311&_=1656918790610
        long curMils = System.currentTimeMillis()/1000L;
        String promethusReqUrl = reqBseQueryV2URL+"(1%20-%20(node_memory_MemAvailable_bytes%7Bjob%3D~%22"+nodeGroupName+"%22%7D%20%2F%20(node_memory_MemTotal_bytes%7Bjob%3D~%22"+nodeGroupName+"%22%7D)))*%20100&time="+curMils;

        String alertMsg = getRateMsg(nodeGroupName,promethusReqUrl,limitRate,"mem");

        log.info(alertMsg);
        return alertMsg;
    }



    //max((node_filesystem_size_bytes%7Bjob%3D~%22ck-node%22%2Cfstype%3D~%22ext.%3F%7Cxfs%22%7D-node_filesystem_free_bytes%7Bjob%3D~%22ck-node%22%2Cfstype%3D~%22ext.%3F%7Cxfs%22%7D)%20*100%2F(node_filesystem_avail_bytes%20%7Bjob%3D~%22ck-node%22%2Cfstype%3D~%22ext.%3F%7Cxfs%22%7D%2B(node_filesystem_size_bytes%7Bjob%3D~%22ck-node%22%2Cfstype%3D~%22ext.%3F%7Cxfs%22%7D-node_filesystem_free_bytes%7Bjob%3D~%22ck-node%22%2Cfstype%3D~%22ext.%3F%7Cxfs%22%7D)))by(instance)&time=1656919407.311&_=1656918790614
    public String diskAlert(String nodeGroupName,Double limitRate){
        //count(node_cpu_seconds_total{job=~"ck-node",mode='system'}) by (instance)
        //http://172.16.11.185:9090/api/v1/query?query=(1 - (node_memory_MemAvailable_bytes{job=~"ck-node"} / (node_memory_MemTotal_bytes{job=~"ck-node"})))* 100&time=1656919407.311&_=1656918790610
        long curMils = System.currentTimeMillis()/1000L;
        String promethusReqUrl = reqBseQueryV2URL+"max((node_filesystem_size_bytes%7Bjob%3D~%22"+nodeGroupName+"%22%2Cfstype%3D~%22ext.%3F%7Cxfs%22%7D-node_filesystem_free_bytes%7Bjob%3D~%22"+nodeGroupName+"%22%2Cfstype%3D~%22ext.%3F%7Cxfs%22%7D)%20*100%2F(node_filesystem_avail_bytes%20%7Bjob%3D~%22"+nodeGroupName+"%22%2Cfstype%3D~%22ext.%3F%7Cxfs%22%7D%2B(node_filesystem_size_bytes%7Bjob%3D~%22"+nodeGroupName+"%22%2Cfstype%3D~%22ext.%3F%7Cxfs%22%7D-node_filesystem_free_bytes%7Bjob%3D~%22"+nodeGroupName+"%22%2Cfstype%3D~%22ext.%3F%7Cxfs%22%7D)))by(instance)&time="+curMils;

        String alertMsg = getRateMsg(nodeGroupName,promethusReqUrl,limitRate,"disk");

        log.info(alertMsg);
        return alertMsg;
    }


    public String getRateMsg(String nodeGroupName,String promethusReqUrl,Double limitRate,String tag){
        String amsg = "";
        try {
            String msgProme = httpClientService.sendReq(promethusReqUrl,new HashMap<>());
            JSONObject memObj = JSON.parseObject(msgProme);

            JSONArray rstAry = memObj.getJSONObject("data").getJSONArray("result");

            for(int i=0;i<rstAry.size();i++){
                JSONObject iobj = rstAry.getJSONObject(i);

                //metric
                String instanceIp = iobj.getJSONObject("metric").getString("instance");
                Double useRate = iobj.getJSONArray("value").getDouble(1);

                if(useRate>limitRate){
                    amsg =  amsg+instanceIp+"-"+ DoubleUtil.getDoubleByTwo(useRate) +",";
                    XxlJobLogger.log("超过预警值："+nodeGroupName+" tag:"+tag+" ip:"+instanceIp+"-"+DoubleUtil.getDoubleByTwo(useRate)+">"+limitRate);
                }else{
                    XxlJobLogger.log(nodeGroupName+" tag:"+tag+" ip:"+instanceIp+"-"+DoubleUtil.getDoubleByTwo(useRate)+">"+limitRate);
                }
            }

            if(StringUtils.isNotBlank(amsg)){
                amsg = tag+":"+amsg;
            }
        }catch (Exception e){
            XxlJobLogger.log(e);
        }
        return amsg;
    }
}
