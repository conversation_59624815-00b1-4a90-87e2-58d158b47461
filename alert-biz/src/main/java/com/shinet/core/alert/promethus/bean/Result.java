/**
  * Copyright 2021 bejson.com 
  */
package com.shinet.core.alert.promethus.bean;
import java.util.List;

/**
 * Auto-generated: 2021-08-04 18:2:3
 *
 * <AUTHOR> (<EMAIL>)
 * @website http://www.bejson.com/java2pojo/
 */
public class Result {

    private Metric metric;
    private List<List<Double>> values;
    public void setMetric(Metric metric) {
         this.metric = metric;
     }
     public Metric getMetric() {
         return metric;
     }

    public void setValues(List<List<Double>> values) {
         this.values = values;
     }
     public List<List<Double>> getValues() {
         return values;
     }

}