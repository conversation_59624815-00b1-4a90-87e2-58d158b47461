package com.shinet.core.alert.safe.service;

import com.shinet.core.alert.coreservice.entity.AlertRecord;
import com.shinet.core.alert.coreservice.entity.IosCheckRecord;
import com.shinet.core.alert.coreservice.enums.AlertModel;
import com.shinet.core.alert.coreservice.enums.AlertStatus;
import com.shinet.core.alert.coreservice.enums.AlertType;
import com.shinet.core.alert.coreservice.service.AlertRecordService;
import com.shinet.core.alert.dingtalk.DingTailService;
import com.shinet.core.alert.dingtalk.VedioAlertService;
import com.shinet.core.alert.safe.entity.IosCheckUrl;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import javax.mail.*;
import javax.mail.internet.InternetAddress;
import javax.mail.internet.MimeMessage;
import javax.mail.internet.MimeMultipart;
import javax.mail.internet.MimeUtility;
import java.io.*;
import java.text.SimpleDateFormat;
import java.util.*;

@Slf4j
@Service
public class IOSEmailService {

    @Autowired
    private AlertRecordService alertRecordService;

    @Autowired
    private IosCheckUrlService iosCheckUrlService;

    private Set secondPhone = new HashSet();

//    @PostConstruct
//    public void testMySelf(){
//        IosCheckUrl iosCheckUrl = new IosCheckRecord();
//        iosCheckUrl.setPhone("13820011993");
//        iosCheckUrl.setProductName("测试");
//        int i = 1;
//        while(true){
//            try {
//                resceive("<EMAIL>", "YEqXdv4isfzhQHgr", iosCheckUrl);
//                log.info("##############" + i++);
//                Thread.sleep(100);
//            }catch (Exception e){
//                log.error(i + " testMySelf", e);
//                throw new RuntimeException(e);
//            }
//        }
//    }
//    public void test1(){
//        IosCheckUrl iosCheckUrl = new IosCheckRecord();
//        iosCheckUrl.setPhone("13820011993");
//        iosCheckUrl.setProductName("测试");
//        try{
//            resceive("<EMAIL>", "DTVBOVOYUVRLIJBY", iosCheckUrl);
//        }catch (Exception e){
//            e.printStackTrace();
//        }
//    }
//
//    public void test2(){
//        IosCheckUrl iosCheckUrl = new IosCheckRecord();
//        iosCheckUrl.setPhone("13820011993");
//        iosCheckUrl.setProductName("测试");
//        try{
//            resceive("<EMAIL>", "NMKNVIZAIWAGLLZU", iosCheckUrl);
//        }catch (Exception e){
//            e.printStackTrace();
//        }
//    }
//
//    public void test3(){
//        IosCheckUrl iosCheckUrl = new IosCheckRecord();
//        iosCheckUrl.setPhone("13820011993");
//        iosCheckUrl.setProductName("测试");
//        try{
//            resceive("<EMAIL>", "KTBNAEZRNDNNAUKK", iosCheckUrl);
//        }catch (Exception e){
//            e.printStackTrace();
//        }
//    }

    public boolean resceiveTry(String emailAdress, String password , IosCheckUrl iosCheckUrl) {
        int i = 0;
        while(true){
            try {
                resceive(emailAdress, password, iosCheckUrl);
                break;
            }catch (Exception e){
                log.warn("IOSEmailService resceiveTry resceive 失败 {}", Arrays.toString(e.getStackTrace()));
                if(i++ > 3){
                    log.error("IOSEmailService resceiveTry resceive 重试超过次数限制", e);
                    sendErrorDing(iosCheckUrl);
                    return true;
                }
                try {
                    Thread.sleep(1000 * 30 * i);
                } catch (InterruptedException ex) {
                    log.error("ex" , ex);
                }
            }
        }
        return false;
    }

    /**
     * 获取邮箱信息
     *
     * @param emailAdress 需要解析的邮箱地址
     * @param password    邮箱的授权密码
     * @throws Exception
     */
    public void resceive(String emailAdress, String password , IosCheckUrl iosCheckUrl) throws MessagingException, IOException {

        String port = "110";   // 端口号
        String servicePath = "pop.163.com";   // 服务器地址


        // 准备连接服务器的会话信息
        Properties props = new Properties();
        props.setProperty("mail.store.protocol", "pop3");       // 使用pop3协议
        props.setProperty("mail.pop3.port", port);           // 端口
        props.setProperty("mail.pop3.host", servicePath);       // pop3服务器

        // 创建Session实例对象
        Session session = Session.getInstance(props);
        Store store = session.getStore("pop3");
        store.connect(emailAdress, password); //163邮箱程序登录属于第三方登录所以这里的密码是163给的授权密码而并非普通的登录密码


        // 获得收件箱
        Folder folder = store.getFolder("INBOX");
        /* Folder.READ_ONLY：只读权限
         * Folder.READ_WRITE：可读可写（可以修改邮件的状态）
         */
        folder.open(Folder.READ_WRITE); //打开收件箱

        // 得到收件箱中的所有邮件
        Message[] messages = folder.getMessages();//获取收件箱中的所有邮件

        //解析邮件
        parseMessage(iosCheckUrl, messages);

        //释放资源
        folder.close(true);
        store.close();
    }

    /**
     * 解析邮件
     *
     * @param messages 要解析的邮件列表
     */
    public void parseMessage(IosCheckUrl iosCheckUrl, Message... messages) throws MessagingException, IOException {
        if (messages == null || messages.length < 1)
            throw new MessagingException("未找到要解析的邮件!");

        // 解析所有邮件
        for (int i = 0, count = messages.length; i < count; i++) {
            MimeMessage msg = (MimeMessage) messages[i];
            log.info("Notification of Apple Developer Program msg {}", getSubject(msg));
            if(!getSubject(msg).contains("Notification of Apple Developer Program")){
//                暂时手动删除多余邮件
//                deleteMessage(msg);
                continue;
            }
            log.info("Notification of Apple Developer Program msg {}", getSubject(msg));
            StringBuffer content = new StringBuffer(30);
            //解析邮件正文
            getMailTextContent(msg, content);
            if(StringUtils.containsIgnoreCase(content, "Section 11.2")){
                sendDing(iosCheckUrl);
            }
        }
    }

    public void sendDing(IosCheckUrl iosCheckUrl){
        log.info("IOSEmailService sendDing {}", iosCheckUrl);
        Set<String> phones = new HashSet<>();
        if(StringUtils.isNotBlank(iosCheckUrl.getPhone())){
            phones.add(iosCheckUrl.getPhone());
        }else{
            phones.add("18600252573");
        }

        String az = iosCheckUrl.getProductName() + "收到IOS下榜邮件";

        AlertRecord alertRecord = alertRecordService.insertAlertRecord(
            "Received-Email-IOS-Off-List",
            "收到IOS下榜邮件",
            AlertModel.Received_Email_IOS_Off_List,
            az,
            AlertStatus.INIT,
            phones,
            AlertType.DINGDING
        );
        DingTailService.sendMarkdownMsg(alertRecord);
        VedioAlertService.sendVocMsg(AlertModel.Received_Email_IOS_Off_List.toString(), az, phones);
    }

    public void sendErrorDing(IosCheckUrl iosCheckUrl){
        log.info("IOSEmailService sendErrorDing {}", iosCheckUrl);
        Set<String> phones = new HashSet<>();
        if(StringUtils.isNotBlank(iosCheckUrl.getPhone())){
            phones.add(iosCheckUrl.getPhone());
        }else{
            phones.add("18600252573");
        }
        phones.add("15175062197");

        String az = iosCheckUrl.getProductName() + "邮箱信息维护错误";

        AlertRecord alertRecord = alertRecordService.insertAlertRecord(
            "Email_INFO_ERROR_IOS_Off_List",
            "邮箱信息维护错误",
            AlertModel.Email_INFO_ERROR_IOS_Off_List,
            az,
            AlertStatus.INIT,
            phones,
            AlertType.DINGDING
        );
        DingTailService.sendMarkdownMsg(alertRecord);
        VedioAlertService.sendVocMsg(AlertModel.Email_INFO_ERROR_IOS_Off_List.toString(), az, phones);

        if(secondPhone.contains(iosCheckUrl)){
            IosCheckUrl temp = iosCheckUrlService.getById(iosCheckUrl.getProduct());
            temp.setEmail("nocheckEmail--- " + temp.getEmail());
            iosCheckUrlService.updateById(temp);
            secondPhone.remove(iosCheckUrl);
        }else{
            secondPhone.add(iosCheckUrl);
        }
    }

    /**
     * 删除邮件
     *
     * @param messages 要删除邮件列表
     */
    public static void deleteMessage(Message... messages) throws MessagingException, IOException {
        if (messages == null || messages.length < 1)
            throw new MessagingException("未找到要解析的邮件!");

        // 解析所有邮件
        for (int i = 0, count = messages.length; i < count; i++) {

            /**
             *   邮件删除
             */
            Message message = messages[i];
            String subject = message.getSubject();
            // set the DELETE flag to true
            message.setFlag(Flags.Flag.DELETED, true);
            System.out.println("Marked DELETE for message: " + subject);


        }
    }

    /**
     * 获得邮件主题
     *
     * @param msg 邮件内容
     * @return 解码后的邮件主题
     */
    public static String getSubject(MimeMessage msg) throws UnsupportedEncodingException, MessagingException {
        return MimeUtility.decodeText(msg.getSubject());
    }

    /**
     * 获得邮件发件人
     *
     * @param msg 邮件内容
     * @return 姓名 <Email地址>
     * @throws MessagingException
     * @throws UnsupportedEncodingException
     */
    public static String getFrom(MimeMessage msg) throws MessagingException, UnsupportedEncodingException {
        String from = "";
        Address[] froms = msg.getFrom();
        if (froms.length < 1)
            throw new MessagingException("没有发件人!");

        InternetAddress address = (InternetAddress) froms[0];
        String person = address.getPersonal();
        if (person != null) {
            person = MimeUtility.decodeText(person) + " ";
        } else {
            person = "";
        }
        from = person + "<" + address.getAddress() + ">";

        return from;
    }

    /**
     * 根据收件人类型，获取邮件收件人、抄送和密送地址。如果收件人类型为空，则获得所有的收件人
     * <p>Message.RecipientType.TO  收件人</p>
     * <p>Message.RecipientType.CC  抄送</p>
     * <p>Message.RecipientType.BCC 密送</p>
     *
     * @param msg  邮件内容
     * @param type 收件人类型
     * @return 收件人1 <邮件地址1>, 收件人2 <邮件地址2>, ...
     * @throws MessagingException
     */
    public static String getReceiveAddress(MimeMessage msg, Message.RecipientType type) throws MessagingException {
        StringBuffer receiveAddress = new StringBuffer();
        Address[] addresss = null;
        if (type == null) {
            addresss = msg.getAllRecipients();
        } else {
            addresss = msg.getRecipients(type);
        }

        if (addresss == null || addresss.length < 1)
            throw new MessagingException("没有收件人!");
        for (Address address : addresss) {
            InternetAddress internetAddress = (InternetAddress) address;
            receiveAddress.append(internetAddress.toUnicodeString()).append(",");
        }

        receiveAddress.deleteCharAt(receiveAddress.length() - 1); //删除最后一个逗号

        return receiveAddress.toString();
    }

    /**
     * 获得邮件发送时间
     *
     * @param msg 邮件内容
     * @return yyyy年mm月dd日 星期X HH:mm
     * @throws MessagingException
     */
    public static String getSentDate(MimeMessage msg, String pattern) throws MessagingException {
        Date receivedDate = msg.getSentDate();
        if (receivedDate == null)
            return "";

        if (pattern == null || "".equals(pattern))
            pattern = "yyyy年MM月dd日 E HH:mm ";

        return new SimpleDateFormat(pattern).format(receivedDate);
    }

    /**
     * 判断邮件中是否包含附件
     *
     * @param part 邮件内容
     * @return 邮件中存在附件返回true，不存在返回false
     * @throws MessagingException
     * @throws IOException
     */
    public static boolean isContainAttachment(Part part) throws MessagingException, IOException {
        boolean flag = false;
        if (part.isMimeType("multipart/*")) {
            MimeMultipart multipart = (MimeMultipart) part.getContent();
            int partCount = multipart.getCount();
            for (int i = 0; i < partCount; i++) {
                BodyPart bodyPart = multipart.getBodyPart(i);
                String disp = bodyPart.getDisposition();
                if (disp != null && (disp.equalsIgnoreCase(Part.ATTACHMENT) || disp.equalsIgnoreCase(Part.INLINE))) {
                    flag = true;
                } else if (bodyPart.isMimeType("multipart/*")) {
                    flag = isContainAttachment(bodyPart);
                } else {
                    String contentType = bodyPart.getContentType();
                    if (contentType.indexOf("application") != -1) {
                        flag = true;
                    }

                    if (contentType.indexOf("name") != -1) {
                        flag = true;
                    }
                }

                if (flag) break;
            }
        } else if (part.isMimeType("message/rfc822")) {
            flag = isContainAttachment((Part) part.getContent());
        }
        return flag;
    }

    /**
     * 判断邮件是否已读
     *
     * @param msg 邮件内容
     * @return 如果邮件已读返回true, 否则返回false
     * @throws MessagingException
     */
    public static boolean isSeen(MimeMessage msg) throws MessagingException {
        return msg.getFlags().contains(Flags.Flag.SEEN);
    }

    /**
     * 判断邮件是否需要阅读回执
     *
     * @param msg 邮件内容
     * @return 需要回执返回true, 否则返回false
     * @throws MessagingException
     */
    public static boolean isReplySign(MimeMessage msg) throws MessagingException {
        boolean replySign = false;
        String[] headers = msg.getHeader("Disposition-Notification-To");
        if (headers != null)
            replySign = true;
        return replySign;
    }

    /**
     * 获得邮件的优先级
     *
     * @param msg 邮件内容
     * @return 1(High):紧急  3:普通(Normal)  5:低(Low)
     * @throws MessagingException
     */
    public static String getPriority(MimeMessage msg) throws MessagingException {
        String priority = "普通";
        String[] headers = msg.getHeader("X-Priority");
        if (headers != null) {
            String headerPriority = headers[0];
            if (headerPriority.indexOf("1") != -1 || headerPriority.indexOf("High") != -1)
                priority = "紧急";
            else if (headerPriority.indexOf("5") != -1 || headerPriority.indexOf("Low") != -1)
                priority = "低";
            else
                priority = "普通";
        }
        return priority;
    }

    /**
     * 获得邮件文本内容
     *
     * @param part    邮件体
     * @param content 存储邮件文本内容的字符串
     * @throws MessagingException
     * @throws IOException
     */
    public static void getMailTextContent(Part part, StringBuffer content) throws MessagingException, IOException {
        //如果是文本类型的附件，通过getContent方法可以取到文本内容，但这不是我们需要的结果，所以在这里要做判断
        boolean isContainTextAttach = part.getContentType().indexOf("name") > 0;
        if (part.isMimeType("text/*") && !isContainTextAttach) {
            content.append(part.getContent().toString());
        } else if (part.isMimeType("message/rfc822")) {
            getMailTextContent((Part) part.getContent(), content);
        } else if (part.isMimeType("multipart/*")) {
            Multipart multipart = (Multipart) part.getContent();
            int partCount = multipart.getCount();
            for (int i = 0; i < partCount; i++) {
                BodyPart bodyPart = multipart.getBodyPart(i);
                getMailTextContent(bodyPart, content);
            }
        }
    }

    /**
     * 保存附件
     *
     * @param part    邮件中多个组合体中的其中一个组合体
     * @param destDir 附件保存目录
     * @throws UnsupportedEncodingException
     * @throws MessagingException
     * @throws FileNotFoundException
     * @throws IOException
     */
    public static void saveAttachment(Part part, String destDir) throws MessagingException, IOException {
        if (part.isMimeType("multipart/*")) {
            Multipart multipart = (Multipart) part.getContent();    //复杂体邮件
            //复杂体邮件包含多个邮件体
            int partCount = multipart.getCount();
            for (int i = 0; i < partCount; i++) {
                //获得复杂体邮件中其中一个邮件体
                BodyPart bodyPart = multipart.getBodyPart(i);
                //某一个邮件体也有可能是由多个邮件体组成的复杂体
                String disp = bodyPart.getDisposition();
                if (disp != null && (disp.equalsIgnoreCase(Part.ATTACHMENT) || disp.equalsIgnoreCase(Part.INLINE))) {
                    InputStream is = bodyPart.getInputStream();
                    saveFile(is, destDir, decodeText(bodyPart.getFileName()));
                } else if (bodyPart.isMimeType("multipart/*")) {
                    saveAttachment(bodyPart, destDir);
                } else {
                    String contentType = bodyPart.getContentType();
                    if (contentType.indexOf("name") != -1 || contentType.indexOf("application") != -1) {
                        saveFile(bodyPart.getInputStream(), destDir, decodeText(bodyPart.getFileName()));
                    }
                }
            }
        } else if (part.isMimeType("message/rfc822")) {
            saveAttachment((Part) part.getContent(), destDir);
        }
    }

    /**
     * 读取输入流中的数据保存至指定目录
     *
     * @param is       输入流
     * @param fileName 文件名
     * @param destDir  文件存储目录
     * @throws FileNotFoundException
     * @throws IOException
     */
    private static void saveFile(InputStream is, String destDir, String fileName) throws FileNotFoundException, IOException {
        BufferedInputStream bis = new BufferedInputStream(is);
        BufferedOutputStream bos = new BufferedOutputStream(
                new FileOutputStream(new File(destDir + fileName)));
        int len = -1;
        while ((len = bis.read()) != -1) {
            bos.write(len);
            bos.flush();
        }
        bos.close();
        bis.close();
    }

    /**
     * 文本解码
     *
     * @param encodeText 解码MimeUtility.encodeText(String text)方法编码后的文本
     * @return 解码后的文本
     * @throws UnsupportedEncodingException
     */
    public static String decodeText(String encodeText) throws UnsupportedEncodingException {
        if (encodeText == null || "".equals(encodeText)) {
            return "";
        } else {
            return MimeUtility.decodeText(encodeText);
        }
    }

}
