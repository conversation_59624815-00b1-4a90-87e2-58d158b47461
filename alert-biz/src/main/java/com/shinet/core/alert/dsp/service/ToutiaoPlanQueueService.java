package com.shinet.core.alert.dsp.service;

import com.shinet.core.alert.coreservice.entity.AlertRecord;
import com.shinet.core.alert.coreservice.enums.AlertModel;
import com.shinet.core.alert.coreservice.enums.AlertStatus;
import com.shinet.core.alert.coreservice.enums.AlertType;
import com.shinet.core.alert.coreservice.service.AlertRecordService;
import com.shinet.core.alert.dingtalk.DingTailService;
import com.shinet.core.alert.dingtalk.VedioAlertService;
import com.shinet.core.alert.dsp.entity.PlanStatusCount;
import com.shinet.core.alert.dsp.entity.ToutiaoPlanQueue;
import com.shinet.core.alert.dsp.mapper.ToutiaoPlanQueueMapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.shinet.core.alert.util.DateUtils;
import com.shinet.core.alert.util.DoubleUtil;
import com.xxl.job.core.log.XxlJobLogger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;

/**
* <p>
    *  服务实现类
    * </p>
*
* <AUTHOR>
* @since 2022-09-22
*/
@Service
public class ToutiaoPlanQueueService extends ServiceImpl<ToutiaoPlanQueueMapper, ToutiaoPlanQueue> {
    @Autowired
    AlertRecordService alertRecordService;
    public void alertPlanCreate(int lowCreateNum,int lv){
        String startDate = DateUtils.formatDateForYMD(new Date());
        List<PlanStatusCount>  planStatusCounts = this.baseMapper.queryStatusCount(startDate);

        int sucNum = 0;
        int errNum = 0;
        int excpNum = 0;
        int toRunNum = 0;
        for(PlanStatusCount planStatusCount : planStatusCounts){
            if(planStatusCount.getPlanStatus()==1){
                sucNum = sucNum +planStatusCount.getCnum();
            }else if(planStatusCount.getPlanStatus()==-5){
                errNum = errNum +planStatusCount.getCnum();
            }else if(planStatusCount.getPlanStatus()==0){
                toRunNum = toRunNum +planStatusCount.getCnum();
            }else{
                excpNum = excpNum +planStatusCount.getCnum();
            }
        }

        double dlv = ((toRunNum+excpNum+errNum)*1.0d)/(sucNum*1.0d);

        String alertMsg = "开始预警 新建计划 积压率："+ DoubleUtil.getDoubleByTwo(dlv*100d) +"% 阈值为："+lv+" " +
                "已成功："+sucNum+" errNum:"+errNum+" toRunNum:"+toRunNum+" excpNum:"+excpNum;
        XxlJobLogger.log(alertMsg);
        if(dlv*100>lv && (toRunNum+excpNum+errNum+sucNum)>lowCreateNum){

            AlertRecord alertRecord = alertRecordService.insertAlertRecord("alert-videoUpload","新建计划积压", AlertModel.PLANCREEXP,
                    "新建计划积压异常 （planqueue）"+alertMsg, AlertStatus.INIT, DingTailService.dset, AlertType.DINGDING);
            DingTailService.sendMarkdownMsg(alertRecord);
//            VedioAlertService.sendVocMsg("新建计划积压 ",alertMsg);
        }
    }
}
