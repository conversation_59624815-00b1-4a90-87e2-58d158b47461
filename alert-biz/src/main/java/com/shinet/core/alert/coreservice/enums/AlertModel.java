package com.shinet.core.alert.coreservice.enums;

public enum AlertModel {
    SERVICEQPS(1,"服务qps-并发"),
    SERVICEREQ(2,"服务request"),
    MALL(3,"提现"),
    ADARPU(4,"ADARPU"),
    DAUNU(5,"DAUNU"),
    OCPC(6,"<PERSON><PERSON><PERSON>"),
    AZKABAN(7,"AZ<PERSON><PERSON><PERSON>"),
    BUSINESS_EXPETION(8,"BUSINESS-EXPETION"),
    OCPCDNU(9,"[Alert] DNU-OCPC-RATE"),
    CLICKHOUSE(10,"CLICKHOUSE"),
    MALL_CK(11,"[Alert] 提现占比异常"),
    ARPU_CK(12,"[Alert] ARPU-波动异常"),
    SLS(13,"[Alert] SLS-错误异常"),
    REWARDEXPOSURE(14,"[Alert] reward/exposure"),
    REWARDTHIRD(15,"[Alert] reward/third"),
    ADPVECPM(16,"[Alert] ad/pvecpm"),
    NORMAL(17,"[Alert] 自然量异常高"),
    CPUMEM(18,"[Alert] CPUMEMDISK"),
    ADPVGAP(19,"[Alert] [离线]PV GAP"),
    ADPVGAP_REALTIME(20,"[Alert] [实时] PV GAP"),
    VIDEOUPLOAD(21,"[Alert] VIDEOUPLOAD"),
    PLANCREEXP(22,"[Alert] PLANCREEXP"),
    VIDEOTIPSEXP(23,"[Alert] VIDEOTIPSEXP"),
    CALLBACK(24,"[Alert] Realtime Reward CallBack"),
    REALTIMECHANNEL(24,"[Alert] Realtime Channel Arpu"),
    REALTIME_REQUEST_FAILED(24,"[Alert] Realtime Request Failed"),
    LOCKED_RATE(25,"[Alert] 锁区占比"),
    WITHDRAW_RATE(25,"[Alert] 提现波动异常"),
    WITHDRAW_BASIC(26,"[Alert] 提现异常"),
    PAYMENT_RATE(27,"[Alert] 支付占比异常"),
    CPA_RATE(28,"[Alert] CPA波动异常"),
    CPA_BID(29,"[Alert] 流量助推CPA波动预警"),
    SDIMG(30,"sd图片"),
    GRAY_EX(31,"[Alert]反作弊拉黑异常"),
    DEVICE_CHECK(32,"[Alert]OAID获取异常"),
    COST_CHECK(33,"[Alert] 消耗核查"),
    STORE_ACTIVE_CHECK(34,"[Alert] 商店新增核查"),
    KSDR_CHECK(35,"[Alert] 快手达人助推核查"),
    ADVERTISER_COST_CHECK(36,"[Alert] 账号消耗监控"),
    AD_CPA_BID_CHECK(37,"[Alert] 计划出价监控"),
    ADVERTISER_OPTIMIZATION(38,"[Alert] 优化目标监控"),
    CLICK_ALERT(39,"[Alert] 三方Click回调监控"),
    SUPPLEMENT_AMOUNT_CHECK(40,"[Alert] 助推预算监控"),
    IOS_LOCK(41,"[Alert] IOS锁区"),
    AZKABAN_TIMEOUT(42,"[Alert] Azkaban任务检测"),
    DAILY_INCOME(43,"[Alert] DailyIncome任务检测"),
    iosapple(44,"[Alert] iosapple"),
    iosUrl(45,"[Alert] IOS域名请求不通"),
    CPA_BID_CHECK(46,"[Alert] TT-CAP_BID监控"),
    iosAppStore(47,"[Alert] IOS AppStore上架监测"),
    KSDR_CHECK_FEE(48,"[Alert] 快手达人服务费核查"),
    TABLEAU(49,"[Alert] TABLEAU API服务检查"),
    TTDR_COST(50,"[Alert] 巨量星图我方预估消耗监控"),
    ADVERTISER_COST_HOUR_CHECK(51,"[Alert] 分时消耗数据监控"),
    RTA(52,"rta cost"),
    ADECPMCPOS(53,"ADECPM 测试 广告位配置"),
    SRKS(54,"收入-投放+提现"),
    POSNUM(55,"POS拉取数"),
    androidlock(56,"android锁区"),
    WITHDRAW_IP(57,"[Alert] IP提现异常"),
    WITHDRAW_IP_PRODUCT(571,"[Alert] 自然量ip聚集提现异常"),
    DEVICE_NEW(58,"[Alert] 新用户占比异常"),
    WITHDRAW_NATURAL(59,"[Alert] 自然量提现异常"),
    androidChannelNum(57,"产品渠道数"),
    androidManNum(58,"机型数"),
    channelPvLv(59,"渠道pv波动"),
    channelSrLv(60,"渠道收入波动"),
    ioslock(61,"[Alert] ioslock"),
    realtimeHour(61,"[Alert] 实时收入校验"),
    dailyIncome(62,"[Alert] 日报收入校验"),
    dailyChannelIncome(63,"[Alert] 日报各渠道收入校验"),
    NATURAL_WITHDRAW_CHANNEL_LIMIT(64,"[Alert] 产品配置自然量渠道拦截订单异常"),

    SYSTEM_ERROR_MESSAGE(65,"[Alert] 服务器日志异常"),
    bidRateLimit(67,"bidding占比异常"),
    PROFIT_REALTIME_HOUR_ALERT(66,"[Alert] 当日预估毛利异常"),
    iosAppStoreNO(68,"[Alert] IOS AppStore 排名"),
    iosAppStoreScore(69,"[Alert] IOS AppStore 评分"),
    tencentMobileSecurity(70,"[Alert] 腾讯管家 检测 未通过"),
    SDK_RETURN_PROPORTION_HIGH(71,"[Alert] sdk回传占比过高"),
    CHANNEL_NATURAL_PROPORTION(72,"[Alert] 分渠道自然量占比异常"),
    PRODUCT_NO_EXPOSURE_PROPORTION(73,"[Alert] 产品分平台无曝光占比异常"),
    AppDiffCheck(74,"[Alert] 应用缺失检测"),
    CHANNEL_HOUR_PV(75,"[Alert] 渠道pv数据异常"),
    Received_Email_IOS_Off_List(76,"[Alert] 收到IOS下榜邮件"),
    Email_INFO_ERROR_IOS_Off_List(77,"[Alert] IOS邮件异常"),
    ROI_FLUCTUATE_ALERT(78,"[Alert] ROI波动异常"),
    WOOKMARK_SCREEN_PV(79,"[Alert] 瀑布流激励视频PV异常"),
    REALTIME_COST_HOUR(80,"[Alert] 各渠道小时消耗异常"),
    TOUTIAO_COMPANY_COST_DAILY(81,"[Alert] 各主体每日限流预警"),
    NORMAL_USER_ACTIVE_NUM(82,"[Alert] 用户激活数量异常"),
    USER_LOGIN_NUM(83,"[Alert] 用户登录情况"),
    TODAY_DATA(84,"[Alert] 今日数据指标"),
    GRAY_EX_SUM(85,"[Alert]反作弊拉黑数量小时统计"),
    BIDDING_ECPM_HIGH(86,"[Alert]穿山甲bidding ECPM过高"),
    PRODUCT_ARPU_DOWN(87,"[Alert]替换广告位实验衰减预警"),
    OCPC_EXACT_DOWN_ALERT(88,"[Alert]归因精准率预警"),
    androidlockNewUser(89,"android锁区-当日新增用户"),
    TODAY_CSJ_ECPM_ALERT(90,"[- [Alert]]今日穿山甲渠道ECPM预警"),
    TODAY_CSJ_ECPM_ADJUSTED_ALERT(91,"[- [Alert]]今日穿山甲渠道ECPM预警(已自动调整回传要求)"),
    ;
    public Integer value;
    public String name;

    AlertModel(Integer value, String name) {
        this.value = value;
        this.name = name;
    }

    public static AlertModel getStatus(Integer value) {
        if (value != null) {
            AlertModel[] otypes = AlertModel.values();
            for (AlertModel memberType : otypes) {
                if (value.equals(memberType.value)) {
                    return memberType;
                }
            }
        }
        return null;
    }
}
