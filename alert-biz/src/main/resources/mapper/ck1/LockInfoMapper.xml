<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.shinet.core.alert.clickhouse.mapper.ck1.IosLockMapper">
    <select id="getAndroidLockInfo" resultType="com.shinet.core.alert.safe.entity.AndoridLockLook">
        select product,
               sum(if(lock_flag = 'true', 1, 0))                                           as lockOaidNum,
               count(oaid)                                                                 as oaidNum,
               sum(if(lock_flag = 'true' and ip != '' and ip is not null, 1, 0))           as lockIpNum,
               count(ip)                                                                   as ipNum,
               sum(if(lock_flag = 'true' and user_id != '' and user_id is not null, 1, 0)) as lockUnum,
               count(user_id)                                                              as userNum
        from (
                 select product,
                        oaid,
                        channel,
                        argMax(ip, update_time)           ip,
                        argMax(user_id, update_time)      user_id,
                        argMax(lock_flag, update_time) as lock_flag
                 from mysql('pc-2ze50z2lt1j82269r.rwlb.rds.aliyuncs.com:3306', 'core-safe', 'android_lock_rst',
                            'safeacc', 'safeacc!@#$1234')
                 where concat(product, oaid) global in (select concat(product, device_id)
                                                from dwd.device_dist
                                                where logday = today()
                                                  and os = 'android')
            or oaid = ''
            or oaid is null
                 group by product, oaid, channel
             ) res
        group by product
        having lockOaidNum > #{minLockNum}
    </select>
</mapper>
