package com.coohua.event.risk.main.service;

import com.alibaba.fastjson.JSON;
import com.coohua.event.risk.main.entity.EventEntity;
import com.coohua.event.risk.main.service.rules.*;
import com.coohua.event.risk.main.service.rules.impl.RiskFilter;
import com.coohua.event.risk.main.utils.EventUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Service;
import top.zrbcool.pepper.boot.jediscluster.JedisClusterClient;
import top.zrbcool.pepper.boot.jediscluster.JedisClusterClientRefer;

import javax.annotation.Resource;
import java.util.List;
import java.util.UUID;
import java.util.concurrent.CompletableFuture;

/**
 * <AUTHOR>
 * @since 2021/11/25
 */
@Slf4j
@Service
public class RealtimeFilterService {

    @Resource(name = "partitionJobTaskPool")
    private ThreadPoolTaskExecutor executor;
    @Autowired
    private HistoryIpGrayFilter historyIpGrayFilter;
    @Autowired
    private SameIpMoreThanFilter sameIpMoreThanFilter;
    @Autowired
    private PropertiesTargetGrayFilter propertiesTargetGrayFilter;
    @Autowired
    private MiModelDontShowCsjAdFilter miModelDontShowCsjAdFilter;
    @Autowired
    private CsjAdRealtimeCheckFilter csjAdRealtimeCheckFilter;
    @Autowired
    private RootUserCheckFilter rootUserCheckFilter;
    @Autowired
    private RealtimeNtfUserCheckFilter realtimeNtfUserCheckFilter;
    @Autowired
    private BaiduAdCheckFilter baiduAdCheckFilter;
    @Autowired
    private AdExposureFilter adExposureFilter;
    @Autowired
    private UserDeviceRedisFilter userDeviceRedisFilter;
    @Autowired
    private UserNoDeviceCheckFilter userNoDeviceCheckFilter;
    @Autowired
    private RewardCacheCount rewardCacheCount;

    @JedisClusterClientRefer(namespace = "user-event")
    private JedisClusterClient userEventJedisClusterClient;


    public void doProcess(List<String> eventStr){
        CompletableFuture.runAsync(()->{
            //1.DEVICE/USER为空 3.非MVP
            try {
                List<EventEntity> eventEntityList = EventUtils.filter(eventStr);
                if (eventEntityList.size() == 0){
                    return;
                }
                doFilterProcess(eventEntityList);
            }catch (Exception e){
                log.error("Solve Batch Risk Error:",e);
            }
        }, executor);

    }

    private void doFilterProcess(List<EventEntity> eventEntityList){
        String trace = UUID.randomUUID().toString().replaceAll("-","");
        log.info("{} ===> Risk Solve Batch Size:{}, Now Start",trace,eventEntityList.size());
        RiskFilter.Builder builder = new RiskFilter.Builder();
        builder.addRiskFilter(rewardCacheCount)
                .addRiskFilter(historyIpGrayFilter)
                .addRiskFilter(propertiesTargetGrayFilter)
                .addRiskFilter(miModelDontShowCsjAdFilter)
                .addRiskFilter(csjAdRealtimeCheckFilter)
//                .addRiskFilter(rootUserCheckFilter)
                .addRiskFilter(realtimeNtfUserCheckFilter)
                .addRiskFilter(baiduAdCheckFilter)
                .addRiskFilter(sameIpMoreThanFilter)
                .addRiskFilter(adExposureFilter)
                .addRiskFilter(userDeviceRedisFilter)
                .addRiskFilter(userNoDeviceCheckFilter)
                .build()
                .doProcess(eventEntityList,trace);
        log.info("{} ===> Risk Solve Complete..",trace);
    }

    public void doFilterPropertiesProcess(List<EventEntity> eventEntities){
        String trace = UUID.randomUUID().toString().replaceAll("-","");
        RiskFilter.Builder builder = new RiskFilter.Builder();
        log.info("==> {} TF {}", JSON.toJSONString(eventEntities),trace);
        builder.addRiskFilter(propertiesTargetGrayFilter)
                .addRiskFilter(sameIpMoreThanFilter)
                .addRiskFilter(userDeviceRedisFilter)
                .build()
                .doProcess(eventEntities,trace);

        //记录ios 用户信息
        /*eventEntities.forEach(event ->{
            EventEntity.Properties properties = event.getProperties();
            if (Objects.isNull(properties) || StringUtils.isBlank(properties.getProduct()) || StringUtils.isBlank(properties.getCaid())) {
                return;
            }
            if (Objects.equals(properties.get$os(), "ios")) {
                String iosCaidCommonKey = RedisKeyConstants.iosCaidCommonKey(properties.getProduct(), properties.getCaid());
                log.info("addIosMember {} {}",iosCaidCommonKey,properties.getUserId());
                userEventJedisClusterClient.sadd(iosCaidCommonKey, properties.getUserId());
                userEventJedisClusterClient.expire(iosCaidCommonKey, RedisKeyConstants.EXPIRE_THREE_DAYS);
            }
        });*/

    }
}
