package com.coohua.event.risk.main.service.rules;

import com.coohua.event.risk.main.config.RiskReason;
import com.coohua.event.risk.main.entity.EventEntity;
import com.coohua.event.risk.main.service.rules.impl.RiskFilter;
import com.coohua.event.risk.main.utils.EventUtils;
import com.coohua.user.event.biz.click.entity.AdEcpmConfig;
import com.coohua.user.event.biz.click.entity.ProductEntity;
import com.coohua.user.event.biz.click.service.ClickHouseService;
import com.coohua.user.event.biz.enums.GrayType;
import com.coohua.user.event.biz.service.TFUserService;
import com.coohua.user.event.biz.service.UserGrayService;
import com.coohua.user.event.biz.service.bean.OcpcEvent;
import com.coohua.user.event.biz.util.AppConfig;
import com.coohua.user.event.biz.util.DateUtil;
import com.coohua.user.event.biz.util.Lists;
import com.pepper.metrics.core.ThreadFactory;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;
import top.zrbcool.pepper.boot.jediscluster.JedisClusterClient;
import top.zrbcool.pepper.boot.jediscluster.JedisClusterClientRefer;

import java.util.*;
import java.util.concurrent.ArrayBlockingQueue;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collector;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2022/12/13
 */
@Slf4j
@Service
public class RealtimeNtfUserCheckFilter  extends RiskFilter {

    @JedisClusterClientRefer(namespace = "user-event")
    private JedisClusterClient userEventJedisClusterClient;
    @Autowired
    private ClickHouseService clickHouseService;

    @Autowired
    private UserGrayService userGrayService;
    @Autowired
    private TFUserService tfUserService;


    private List<Long> biddingIdList;
    private Map<Long,Integer> adEcpmConfigMap;




    @Scheduled(cron = "0 0 * * * ?")
    public void refreshAdBiddingConfig(){
        try {
            this.biddingIdList = clickHouseService.queryBiddingAdId();
            List<AdEcpmConfig> adEcpmConfigList = clickHouseService.queryAdEcpmConf();
            adEcpmConfigMap = adEcpmConfigList.stream()
                    .collect(Collectors.toMap(AdEcpmConfig::getAdId,AdEcpmConfig::getEcpm,(r1,r2)->r1));
        }catch (Exception e){
            log.error("Refresh AdConfig Ex:",e);
        }
    }


    @Override
    public void doProcess(List<EventEntity> eventList, String trace) {
        this.threadPoolExecutor.execute(() -> {
            String logday = DateUtil.dateToString(new Date(), DateUtil.ISO_DATE_FORMAT);
            // 保留视频PV 曝光
            Map<String, List<UserView>> userMoreThan10kTimes = eventList.parallelStream()
                    .filter(r -> "exposure".equals(r.getProperties().getAd_action()))
                    .filter(r -> r.getProperties().getAd_type().startsWith("1015")
                            || r.getProperties().getAd_type().startsWith("1018")
                            || r.getProperties().getAd_type().startsWith("1008"))
                    .filter(r -> !Objects.equals("项目十组", AppConfig.appIdMap.get(Long.valueOf(r.getAppId())).getProductGroup()))
                    .map(r -> {
                        UserView userView = new UserView();
                        userView.setProduct(r.getProperties().getProduct());
                        userView.setUserId(r.getProperties().getUserId());
                        Long adID = Long.valueOf(r.getProperties().getAd_id());
                        if (biddingIdList.contains(adID)) {
                            userView.setEcpm(Double.valueOf(r.getProperties().getExtend1()));
                        } else {
                            Integer ecpm = adEcpmConfigMap.getOrDefault(adID, 10);
                            userView.setEcpm(Double.valueOf(ecpm));
                        }
                        userView.setAdId(adID);
                        userView.setEventEntity(r);
                        return userView;
                    }).filter(uv -> uv.getEcpm() >= 1000d)
                    .collect(Collectors.groupingBy(r -> this.buildUserEcpmSumKey(logday, r.getUserId(), r.getProduct())));
            if (userMoreThan10kTimes.size() > 0) {
                // 累计并计算
                List<EventEntity> needGrayUserList = new ArrayList<>();
                userMoreThan10kTimes.forEach((redisKey, pvEntity) -> {
                    int pv = pvEntity.size();
                    UserView userView = pvEntity.get(0);
                    Long result = userEventJedisClusterClient.incrBy(redisKey, pv);
                    userEventJedisClusterClient.expire(redisKey, 60 * 60 * 24);
                    // 当日总计10次
                    if (result > 20L) {
                        needGrayUserList.add(userView.getEventEntity());
                    }
                });

                if (Lists.noEmpty(needGrayUserList)) {
                    Map<String, Set<String>> userMap = EventUtils.getUserIdMap(needGrayUserList);
                    Map<String, List<EventEntity>> productUserEventMap = EventUtils.getProductUserEventMap(needGrayUserList);
                    // 查看是否投放
                    userMap.forEach((product, sets) -> {
                        if (sets != null && sets.size() > 0) {
                            ArrayList<String> userIdList = new ArrayList<>(sets);
                            List<String> needGrayList = new ArrayList<>();
                            //        是否使用设备归因
                            // 配置的产品列表为空或者包含当前产品，使用设备归因，否则走原来逻辑
                            if (tfUserService.isUseDeviceGui(product)) {
                                try {
                                    List<OcpcEvent> ocpcEventList = tfUserService.queryUserDeviceGuiBatch(EventUtils.convertEventEntityToUserScoreEntity(productUserEventMap.get(product)));
                                    needGrayList = userIdList.stream()
                                            .filter(userId -> !ocpcEventList.stream().anyMatch(event -> event.getUserId().equals(userId)))
                                            .collect(Collectors.toList());
                                    if (tfUserService.isDeviceGuiLogEnabled) {
                                        log.info("RealtimeNtfUserCheckFilter设备归因 {} {} {} {}", product, userIdList, needGrayList, ocpcEventList);
                                    }
                                } catch (Exception e) {
                                    log.error("RealtimeNtfUserCheckFilter使用设备归因异常 {} {} {}",product, productUserEventMap, userIdList, e);
                                }
                            } else {
                                needGrayList = tfUserService.queryNtfUser(product, new ArrayList<>(sets), trace);
                            }
                            if (needGrayList.size() > 0) {
                                log.info("{} ===> Risk Confirmed, Reason {} Need Gray,Checkout U:{}", trace, this.riskReason.getDesc(), needGrayList.size());
                                userGrayService.grayUserAsync(GrayType.USER, needGrayList, this.riskReason.getDesc(), product, null, trace);
                            }
                        }
                    });
                }
            }
        });
        if (this.nextRiskFilter != null) {
            this.nextRiskFilter.doProcess(eventList, trace);
        }
    }

    private String buildUserEcpmSumKey(String logday,String userId,String product){
        return String.format("{user:1k}:%s:%s:%s",logday,product,userId);
    }

    @Data
    private static class UserView{
        private String product;
        private String userId;
        private EventEntity eventEntity;
        private Long adId;
        private Double ecpm;
    }

    @Override
    public void initReason() {
        this.riskReason = RiskReason.R13;
    }

    @Override
    public void initThreadPool() {
        this.threadPoolExecutor =  new ThreadPoolExecutor(4,8,100, TimeUnit.SECONDS,
                new ArrayBlockingQueue<>(20),new ThreadFactory("RealtimeNtfUserCheckFilter-"),new ThreadPoolExecutor.AbortPolicy());
    }

    @Override
    public void afterPropertiesSet() throws Exception {
        this.initReason();
        this.initThreadPool();
        this.refreshAdBiddingConfig();
    }
}
