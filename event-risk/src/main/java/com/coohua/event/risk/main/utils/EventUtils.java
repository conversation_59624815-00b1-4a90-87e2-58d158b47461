package com.coohua.event.risk.main.utils;

import com.alibaba.fastjson.JSON;
import com.coohua.event.risk.main.entity.EventEntity;
import com.coohua.user.event.biz.click.entity.UserScoreEntity;
import com.coohua.user.event.biz.util.Strings;
import org.apache.commons.lang3.StringUtils;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2021/11/25
 */
public class EventUtils {

    private final static List<String> needProcessEventList = new ArrayList<String>(){{
//        add("WebClick");
//        add("Startup");
//        add("AppUse");
//        add("AppStatus");
//        add("AppPageView");
        add("AdData");
    }};

    private final static List<String> needProcessAdActionList = new ArrayList<String>(){{
        add("reward");
        add("click");
        add("exposure");
        add("video_cache_finish");
    }};

    public static List<EventEntity> filter(List<String> eventStr){
        return eventStr.stream()
                .map(EventUtils::apply)
                .filter(event -> "mvp".equals(event.getProject()))
                .filter(event -> needProcessEventList.contains(event.getEvent()))
                .filter(event -> needProcessAdActionList.contains(event.getProperties().getAd_action()) || StringUtils.isBlank(event.getProperties().getAd_action()))
                .collect(Collectors.toList());
    }

    private static EventEntity apply(String str) {
        return JSON.parseObject(str, EventEntity.class);
    }

    public static Map<String,Set<String>> getUserIdMap(List<EventEntity> eventEntityList){
        Map<String,List<EventEntity>> entityMap = eventEntityList.stream().collect(Collectors.groupingBy(r -> r.getProperties().getProduct()));
        Map<String,Set<String>> resMap = new HashMap<>();
        entityMap.forEach((product,list) -> resMap.put(product,getUserIdList(list)));
        return resMap;
    }

    public static Set<String> getUserIdList(List<EventEntity> eventEntityList){
        return eventEntityList.stream()
                .filter(r -> Strings.noEmptyExNull(r.getProperties().getUserId()))
                .map(r-> r.getProperties().getUserId())
                .collect(Collectors.toSet());
    }

    public static Set<String> getDeviceIdList(List<EventEntity> eventEntityList){
        return eventEntityList.stream()
                .filter(r -> Strings.noEmptyExNull(r.getProperties().get$device_id()) || Strings.noEmptyExNull(r.getProperties().getImei()))
                .map(r-> {
                    String deviceId = r.getProperties().get$device_id();
                    if (Strings.isEmpty(deviceId)){
                        deviceId = r.getProperties().getImei();
                    }
                    return deviceId;
                }).collect(Collectors.toSet());
    }

    public static Map<String, List<EventEntity>> getProductUserEventMap(List<EventEntity> eventEntityList) {
        return eventEntityList.stream().collect(Collectors.groupingBy(r -> r.getProperties().getProduct()));
    }

    public static List<UserScoreEntity> convertEventEntityToUserScoreEntity(List<EventEntity> eventEntities) {
        List<UserScoreEntity> userScoreEntityList = new ArrayList<>();
        eventEntities.forEach(eventEntity -> {
            UserScoreEntity userScoreEntity = new UserScoreEntity();
            userScoreEntity.setProduct(eventEntity.getProperties().getProduct());
            userScoreEntity.setOs(eventEntity.getProperties().get$os());
            userScoreEntity.setUserId(eventEntity.getProperties().getUserId());
            userScoreEntity.setDeviceId(eventEntity.getProperties().get$device_id());
            userScoreEntity.setIp(eventEntity.getProperties().getIp());
            userScoreEntityList.add(userScoreEntity);
        });
        return userScoreEntityList;
    }
}
