package com.coohua.event.risk.main.service.rules;

import com.coohua.event.risk.main.config.RiskReason;
import com.coohua.event.risk.main.entity.EventEntity;
import com.coohua.event.risk.main.service.rules.impl.RiskFilter;
import com.coohua.user.event.biz.enums.GrayType;
import com.coohua.user.event.biz.service.UserGrayService;
import com.coohua.user.event.biz.util.Lists;
import com.ctrip.framework.apollo.spring.annotation.ApolloJsonValue;
import com.pepper.metrics.core.ThreadFactory;
import lombok.extern.slf4j.Slf4j;
import org.slf4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;
import java.util.concurrent.ArrayBlockingQueue;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2022/11/3
 */
@Slf4j
@Service
public class RootUserCheckFilter extends RiskFilter {

    @Autowired
    private UserGrayService userGrayService;

    @ApolloJsonValue("${white.device.root.list:[\"44f6cacdde2cb8d5\",\"25d687b296a0503b\"]}")
    private List<String> witheListDevice;

    @Override
    public void doProcess(List<EventEntity> eventList, String trace) {
        this.threadPoolExecutor.execute(() -> {
            List<EventEntity> eventEntities = eventList.stream()
                    .filter(r -> !witheListDevice.contains(r.getProperties().get$device_id()))
                    .filter(r -> "exposure".equals(r.getProperties().getAd_action()) && "true".equals(r.getProperties().getPage_name()))
                    .collect(Collectors.toList());

            if (Lists.noEmpty(eventEntities)){
                grayPublic(trace, eventEntities, log, this.riskReason.getDesc(), userGrayService);
            }
        });
        this.nextRiskFilter.doProcess(eventList,trace);
    }



    @Override
    public void initReason() {
        this.riskReason = RiskReason.R12;
    }

    @Override
    public void initThreadPool() {
        this.threadPoolExecutor =  new ThreadPoolExecutor(4,8,100, TimeUnit.SECONDS,
                new ArrayBlockingQueue<>(20),new ThreadFactory("RootUserFilter-"),new ThreadPoolExecutor.AbortPolicy());
    }

    @Override
    public void afterPropertiesSet() throws Exception {
        this.initReason();
        this.initThreadPool();
    }
}
