package com.coohua.event.risk.main.entity;

import com.coohua.user.event.biz.click.entity.ProductEntity;
import com.coohua.user.event.biz.util.AppConfig;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

/**
 * <AUTHOR>
 * @since 2023/3/8
 */
@Slf4j
@Data
public class UserLoginBean {
    private CommonHeaderDTO commonHeader;
    private UserEntity userEntity;


    public EventEntity toEvent(){
        EventEntity eventEntity = new EventEntity();
        EventEntity.Properties properties = new EventEntity.Properties();
        properties.set$app_version(this.commonHeader.getAppVersion());
        properties.set$device_id(this.commonHeader.getDeviceId());
        properties.setImei(this.getCommonHeader().getImei());
        properties.set$manufacturer(this.getCommonHeader().getBrand());
        properties.set$model(this.getCommonHeader().getModel());
        properties.set$os(this.getCommonHeader().getOs().toLowerCase());
        properties.setAd_action("login");
        properties.setChannel(this.getCommonHeader().getChannel());
        properties.setIp(this.getCommonHeader().getIp());
        properties.setCaid(this.getCommonHeader().getCaid());
        try {
            if (this.getUserEntity()!=null) {
                properties.setUserId(String.valueOf(this.getUserEntity().getId()));
            }else {
                if (StringUtils.isNotBlank(this.getCommonHeader().getAccessKey())) {
                    properties.setUserId(this.getCommonHeader().getAccessKey().split("_")[1]);
                }
            }
        }catch (Exception e){
            log.warn("Parse Warning:",e);
        }
        properties.setOaid(this.getCommonHeader().getOaid());
        properties.setProduct(this.getCommonHeader().getProduct());
        if (StringUtils.isEmpty(properties.getProduct())){
            if (StringUtils.isNotEmpty(this.getCommonHeader().getAppId())){
                ProductEntity productEntity = AppConfig.appIdMap.get(Long.valueOf(this.commonHeader.getAppId()));
                if (productEntity != null){
                    properties.setProduct(productEntity.getProduct());
                }
            }
        }
        eventEntity.setEvent("login");
        eventEntity.setProject("mvp");
        eventEntity.setAppId(this.getCommonHeader().getAppId());
        eventEntity.setProperties(properties);


        return eventEntity;
    }
}
