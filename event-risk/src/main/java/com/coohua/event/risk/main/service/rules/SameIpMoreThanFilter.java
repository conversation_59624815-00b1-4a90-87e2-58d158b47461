package com.coohua.event.risk.main.service.rules;

import com.alibaba.fastjson.JSON;
import com.coohua.event.risk.main.config.RiskReason;
import com.coohua.event.risk.main.entity.EventEntity;
import com.coohua.event.risk.main.service.rules.impl.RiskFilter;
import com.coohua.user.event.biz.click.entity.ProductEntity;
import com.coohua.user.event.biz.service.UserGrayService;
import com.coohua.user.event.biz.util.AppConfig;
import com.coohua.user.event.biz.util.DateUtil;
import com.coohua.user.event.biz.util.Lists;
import com.coohua.user.event.biz.util.Strings;
import com.ctrip.framework.apollo.spring.annotation.ApolloJsonValue;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.http.nio.pool.NIOConnFactory;
import org.apache.ibatis.annotations.Param;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import redis.clients.jedis.Jedis;
import redis.clients.jedis.Pipeline;
import redis.clients.jedis.Response;
import top.zrbcool.pepper.boot.core.ThreadFactory;
import top.zrbcool.pepper.boot.jediscluster.JedisClusterClient;
import top.zrbcool.pepper.boot.jediscluster.JedisClusterClientRefer;

import java.util.*;
import java.util.concurrent.ArrayBlockingQueue;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2021/11/26
 */
@Slf4j
@Service
public class SameIpMoreThanFilter extends RiskFilter {

    @JedisClusterClientRefer(namespace = "user-event")
    private JedisClusterClient userEventJedisClusterClient;
    private final static String PRE_FIX = "{user:ip}:";

    @ApolloJsonValue("${join.ip.skip.app:[\"llazc\"]}")
    private List<String> skipCheckProduct;
    @Autowired
    private UserGrayService userGrayService;

    @Override
    public void doProcess(List<EventEntity> eventList, String trace) {
        this.threadPoolExecutor.execute(() ->{
            if (Lists.noEmpty(eventList)) {
                Date now = new Date();
                String logday = DateUtil.dateToString(now,DateUtil.ISO_DATE_FORMAT);
                Map<String,List<EventEntity>> resMap = eventList.stream()
                        .filter(r -> "exposure".equals(r.getProperties().getAd_action()) ||
                                "login".equals(r.getProperties().getAd_action()))
                        .filter(r -> !"0".equals(r.getProperties().getUserId()))
                        .filter(r -> !"android".equalsIgnoreCase(r.getProperties().get$os()))
                        .filter(r -> !skipCheckProduct.contains(r.getProperties().getProduct()))
                        .peek(r -> r.setProject(buildRipKey(r,logday)))
                        .collect(Collectors.groupingBy(EventEntity::getProject));
                if (resMap.size() == 0) {
                    return;
                }
                Map<String,RedisSaveIpBean> exUserMap =  batchSaveAndCheckRedis(resMap);
                // 记录预警信息
                Map<String,RedisSaveChannelBean> trigMap = batchSaveAndCheckChannel(exUserMap);
                if (trigMap.size() > 0){
                    log.info("Current ip repeat alert ==> {} {} ",trace,JSON.toJSONString(trigMap.keySet()));
                }

                // 检查异常登录产品埋点
                List<EventEntity> erEvents = eventList.stream()
                        .filter(r->"login".equals(r.getProperties().getAd_action()))
                        .filter(r-> {
                            ProductEntity productEntity = AppConfig.productEnMap.get(r.getProperties().getProduct());
                            if (productEntity == null){
                                return false;
                            }
                            return Strings.noEmpty(r.getAppId()) && !productEntity.getId().toString().equals(r.getAppId());
                        }).collect(Collectors.toList());
                if (erEvents.size() > 0){
                    Map<String,List<EventEntity>> logInfoMap = erEvents.stream()
                            .collect(Collectors.groupingBy(r-> r.getProperties().getProduct()));
                    logInfoMap.forEach((product,list)->{
                        log.info("登录上报异常埋点 产品 {} 用户 {}",product,JSON.toJSONString(list.stream()
                                .map(r->r.getProperties().getUserId())
                                .collect(Collectors.toList()))
                        );
                    });

                }
            }
        });
        if (this.nextRiskFilter != null) {
            this.nextRiskFilter.doProcess(eventList, trace);
        }
    }

    @Data
    private static class RedisSaveIpBean{
        private String product;
        private String channel;
        private String model;
        private String redisKey;
        private List<String> currentUser;

        public RedisSaveIpBean build(List<EventEntity> eventEntitys){
            EventEntity eventEntity = eventEntitys.get(0);
            RedisSaveIpBean redisSaveIpBean = new RedisSaveIpBean();
            redisSaveIpBean.setRedisKey(eventEntity.getProject());
            redisSaveIpBean.setProduct(eventEntity.getProperties().getProduct());
            redisSaveIpBean.setModel(eventEntity.getProperties().get$model());
            redisSaveIpBean.setChannel(eventEntity.getProperties().getChannel());
            redisSaveIpBean.setCurrentUser(eventEntitys.stream().map(r->r.getProperties()
                    .getUserId()).collect(Collectors.toList()));
            return redisSaveIpBean;
        }
    }

    @Data
    private static class RedisSaveChannelBean{
        private String key;
        private String product;
        private String channel;
        private List<String> userIdList;
        private List<String> ipStartList;
    }

    private Map<String,RedisSaveChannelBean> batchSaveAndCheckChannel(Map<String,RedisSaveIpBean> targetMap){
        if (targetMap.size() == 0){
            return new HashMap<>();
        }
        String logday = DateUtil.dateToString(new Date(),DateUtil.ISO_DATE_FORMAT);
        Map<String,RedisSaveChannelBean> trigMap = new HashMap<>();
        try (Jedis jedis = userEventJedisClusterClient.getResource(PRE_FIX)){
            // 获取渠道、产品
            List<RedisSaveChannelBean> redisSaveChannelBeans = new ArrayList<>();
            for (Map.Entry<String, RedisSaveIpBean> entry : targetMap.entrySet()) {
                RedisSaveIpBean redisSaveIpBean = entry.getValue();
                String newKey = buildChannelKey(redisSaveIpBean,logday);
                RedisSaveChannelBean current = new RedisSaveChannelBean();
                current.setChannel(redisSaveIpBean.getChannel());
                current.setProduct(redisSaveIpBean.getProduct());
                current.setKey(newKey);
                String ip = getIpFromRipKey(redisSaveIpBean.getRedisKey());
                current.setIpStartList(Collections.singletonList(ip));
                current.setUserIdList(current.getUserIdList());
                redisSaveChannelBeans.add(current);
            }
            // 聚合保存
            Map<String,List<RedisSaveChannelBean>> res = redisSaveChannelBeans.stream()
                    .collect(Collectors.groupingBy(RedisSaveChannelBean::getKey));

            res.forEach((key,rlist) -> {
                RedisSaveChannelBean source = rlist.get(0);
                List<String> targetUList = new ArrayList<>();
                List<String> targetIPList = new ArrayList<>();
                for (RedisSaveChannelBean redisSaveChannelBean : rlist){
                    if (redisSaveChannelBean.getUserIdList() != null) {
                        targetUList.addAll(redisSaveChannelBean.getUserIdList());
                    }
                    if (redisSaveChannelBean.getIpStartList() != null) {
                        targetIPList.addAll(redisSaveChannelBean.getIpStartList());
                    }
                }

                List<String> distinctU = new ArrayList<>(new HashSet<>(targetUList));
                List<String> distinctIp = new ArrayList<>(new HashSet<>(targetIPList));
                if (distinctU.size() > 30){
                    RedisSaveChannelBean trig = new RedisSaveChannelBean();
                    trig.setKey(key);
                    trig.setProduct(source.getProduct());
                    trig.setChannel(source.getChannel());
                    trig.setIpStartList(distinctIp);
                    trig.setUserIdList(distinctU);
                    trigMap.put(key,trig);
                }
            });

            if (trigMap.size() > 0){
                Pipeline pipe = jedis.pipelined();
                trigMap.forEach((key,bean) ->{
                    pipe.set(key,JSON.toJSONString(bean));
                    pipe.expire(key,60 * 60 * 24 * 3);
                });

                String alertKey = PRE_FIX+"alert:product:info";
                pipe.set(alertKey,JSON.toJSONString(trigMap.keySet()));
                pipe.expire(alertKey,60 * 60 * 24 * 3);
                pipe.sync();
                pipe.close();
            }
        }catch (Exception e){
            log.error("Save Channel Bean Ex:",e);
        }
        return trigMap;
    }

    private Map<String,RedisSaveIpBean> batchSaveAndCheckRedis(Map<String,List<EventEntity>> resMap){
        Map<String,RedisSaveIpBean> exUserBeanMap = new HashMap<>();
        try (Jedis jedis = userEventJedisClusterClient.getResource(PRE_FIX)){
            Pipeline pipe = jedis.pipelined();
            Response<List<String>> response = pipe.mget(new ArrayList<>(resMap.keySet()).toArray(new String[resMap.keySet().size()]));
            // 先查再递增
            pipe.sync();
            List<String> resList = response.get();
            if (resList == null){
                resList = new ArrayList<>();
            }
            List<RedisSaveIpBean> redisSaveIpBeans = resList.stream()
                    .filter(Strings::noEmpty)
                    .map(r -> JSON.parseObject(r,RedisSaveIpBean.class))
                    .collect(Collectors.toList());
            Map<String,RedisSaveIpBean> redisResMap = redisSaveIpBeans.stream()
                    .collect(Collectors.toMap(RedisSaveIpBean::getRedisKey, r->r,(t1, t2)->t1));

            resMap.forEach((rdKey,eList) -> {
                RedisSaveIpBean tempBean = new RedisSaveIpBean().build(eList);
                RedisSaveIpBean queryBean = redisResMap.getOrDefault(rdKey,null);
                RedisSaveIpBean resultBean = mergeIpBean(tempBean,queryBean);

                pipe.set(rdKey,JSON.toJSONString(resultBean));
                pipe.expire(rdKey,60 * 60 * 24 * 3);
                if (resultBean.getCurrentUser().size() > 10){
                    exUserBeanMap.put(rdKey,resultBean);
                }
            });
            pipe.sync();
            pipe.close();
            return exUserBeanMap;
        }catch (Exception e){
            log.error("RedisEr:",e);
            return new HashMap<>();
        }
    }

    private RedisSaveIpBean mergeIpBean(RedisSaveIpBean currentBean,RedisSaveIpBean queryBean){
        if (queryBean == null){
            return currentBean;
        }else {
            List<String> userIdList = currentBean.getCurrentUser();
            userIdList.addAll(queryBean.getCurrentUser());
            Set<String> userSet = new HashSet<>(userIdList);
            currentBean.setCurrentUser(new ArrayList<>(userSet));
            if (Strings.noEmpty(queryBean.getChannel())) {
                currentBean.setChannel(queryBean.getChannel());
            }
            return currentBean;
        }
    }

    private String getIpFromRipKey(String key){
        String[] kAry = key.split(":");
        return kAry[kAry.length-2];
    }

    private String buildChannelKey(RedisSaveIpBean redisSaveIpBean,String logday){
        return String.format(PRE_FIX + "channel:%s:%s:%s",
                logday,
                redisSaveIpBean.getProduct(),
                redisSaveIpBean.getChannel()
        );
    }


    private String buildRipKey(EventEntity eventEntity,String logday){
        String ip = eventEntity.getProperties().getIp();
        if (Strings.isEmpty(ip)){
            return null;
        }else {
            String[] ipPre = ip.split("[.]");
            StringBuilder pre = new StringBuilder();
            for (int i = 0;i<ipPre.length -1;i++){
                if (i < ipPre.length -2) {
                    pre.append(ipPre[i]).append(".");
                }else {
                    pre.append(ipPre[i]);
                }
            }
            return String.format(PRE_FIX + "%s:%s:%s:%s:%s",
                    logday,
                    eventEntity.getProperties().getProduct(),
                    eventEntity.getProperties().getChannel(),
                    eventEntity.getProperties().get$model(),
                    pre.toString()
            );
        }
    }

    @Override
    public void initReason() {
        this.riskReason = RiskReason.R15;
    }

    @Override
    public void initThreadPool() {
        this.threadPoolExecutor =  new ThreadPoolExecutor(8,16,100, TimeUnit.SECONDS,
                new ArrayBlockingQueue<>(40),new ThreadFactory("SameIpMoreThanFilter-"),new ThreadPoolExecutor.AbortPolicy());
    }

    @Override
    public void afterPropertiesSet() throws Exception {
        this.initReason();
        this.initThreadPool();
    }
}
