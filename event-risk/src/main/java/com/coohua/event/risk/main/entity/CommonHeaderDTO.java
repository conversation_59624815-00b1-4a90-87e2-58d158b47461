package com.coohua.event.risk.main.entity;

import lombok.Data;

import java.io.Serializable;

@Data
public class CommonHeaderDTO implements Serializable {
    private String deviceId;
    private String brand;
    private String gps;
    private String bs;
    private String appVersion;
    private String os;
    private String channel;
    private String romVersion;
    private String osVersion;
    private String accessKey;
    private String wechatId;
    private String pkgId;
    private String appId;

    private String imei;
    private String oaid;
    private String androidId;
    private String mac;
    private String idfa;
    private String product;
    private String model;
    private String ip;
    private String ua;
    private String caid;
}
