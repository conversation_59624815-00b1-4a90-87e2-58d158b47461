package com.coohua.event.risk.main.service.rules;

import com.coohua.event.risk.main.config.RiskReason;
import com.coohua.event.risk.main.entity.EventEntity;
import com.coohua.event.risk.main.service.rules.impl.RiskFilter;
import com.coohua.user.event.biz.click.entity.ProductEntity;
import com.coohua.user.event.biz.service.AdCloseService;
import com.coohua.user.event.biz.service.UserGrayService;
import com.coohua.user.event.biz.util.AppConfig;
import com.coohua.user.event.biz.util.Strings;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import top.zrbcool.pepper.boot.core.ThreadFactory;

import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.ArrayBlockingQueue;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2023/4/13
 */
@Slf4j
@Service
public class BaiduAdCheckFilter extends RiskFilter {

    @Autowired
    private UserGrayService userGrayService;
    @Autowired
    private AdCloseService adCloseService;

    @Override
    public void doProcess(List<EventEntity> eventList, String trace) {
        this.threadPoolExecutor.execute(() ->{
            List<EventEntity> eventEntities = eventList.stream()
                    .filter(eventEntity -> "exposure".equals(eventEntity.getEvent()) &&
                            eventEntity.getProperties().getAd_type().startsWith("1099"))
                    .filter(eventEntity -> {
                        String epStr = eventEntity.getProperties().getExtend1();
                        double ecpm = 0d;
                        if (StringUtils.isNotEmpty(epStr)){
                            ecpm = Double.parseDouble(epStr);
                        }
                        return ecpm > 3000;
                    }).collect(Collectors.toList());
            if (eventEntities.size() > 0){
                grayPublic(trace, eventEntities, log, this.riskReason.getDesc(), userGrayService);
            }

            // 张倩新增规则 "2304FPN6DC" 机型不看百度广告
            List<EventEntity> eventEntityList = eventList.stream()
                    .filter(r -> "2304FPN6DC".equals(r.getProperties().get$model()))
                    .collect(Collectors.toList());
            if (eventEntityList.size() > 0){
                Map<String,List<EventEntity>> noBdGd = eventEntityList.stream()
                        .collect(Collectors.groupingBy(r -> r.getProperties().getProduct()));

                noBdGd.forEach((prc,list) ->{
                    ProductEntity productEntity = AppConfig.productEnMap.get(prc);
                    if (productEntity == null){
                        return;
                    }
                    Set<String> userSet = list.stream()
                            .filter(r -> Strings.noEmpty(r.getProperties().getUserId()))
                            .filter(r -> !"null".equals(r.getProperties().getUserId()))
                            .filter(r -> !"0".equals(r.getProperties().getUserId()))
                            .map(r -> r.getProperties().getUserId())
                            .collect(Collectors.toSet());

                    for (String user : userSet){
                        adCloseService.setSkipPlatform(productEntity.getId(),user, Collections.singletonList(4),60 * 60 * 24 * 7);
                    }
                });
            }
        });
        if (this.nextRiskFilter != null) {
            this.nextRiskFilter.doProcess(eventList, trace);
        }
    }

    @Override
    public void initReason() {
        this.riskReason = RiskReason.R14;
    }

    @Override
    public void initThreadPool() {
        this.threadPoolExecutor =  new ThreadPoolExecutor(4,8,100, TimeUnit.SECONDS,
                new ArrayBlockingQueue<>(20),new ThreadFactory("BaiduAdCheckFilter-"),new ThreadPoolExecutor.AbortPolicy());
    }

    @Override
    public void afterPropertiesSet() throws Exception {
        this.initReason();
        this.initThreadPool();
    }
}
