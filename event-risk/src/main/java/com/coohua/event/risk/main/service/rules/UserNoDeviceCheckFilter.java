package com.coohua.event.risk.main.service.rules;

import com.coohua.event.risk.main.config.RiskReason;
import com.coohua.event.risk.main.entity.EventEntity;
import com.coohua.event.risk.main.service.rules.impl.RiskFilter;
import com.coohua.user.event.biz.service.UserGrayService;
import com.coohua.user.event.biz.util.Lists;
import com.coohua.user.event.biz.util.RedisKeyConstants;
import com.pepper.metrics.core.ThreadFactory;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import top.zrbcool.pepper.boot.jediscluster.JedisClusterClient;
import top.zrbcool.pepper.boot.jediscluster.JedisClusterClientRefer;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.concurrent.ArrayBlockingQueue;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2022/11/3
 */
@Slf4j
@Service
public class UserNoDeviceCheckFilter extends RiskFilter {

    @Autowired
    private UserGrayService userGrayService;
    @JedisClusterClientRefer(namespace = "user-event")
    private JedisClusterClient userEventJedisClusterClient;

    @Value("${realtime.rule.risk.switch:false}")
    private boolean ruleRiskSwitch ;

    @Override
    public void doProcess(List<EventEntity> eventList, String trace) {

        if (!ruleRiskSwitch) {
            return;
        }
        this.threadPoolExecutor.execute(() -> {
            // 1.过滤曝光事件
            List<EventEntity> eventEntities = eventList.parallelStream()
                    .filter(r -> "exposure".equals(r.getProperties().getAd_action()))
                    .collect(Collectors.toList());
            //2. 查询 distinct_id device_id android 都空的用户累计出现3次直接拉黑
            List<EventEntity>  deviceFilterEntitys = eventEntities.parallelStream()
                    .filter(r -> StringUtils.isBlank(r.getDistinct_id()) && StringUtils.isBlank(r.getProperties().get$device_id()) && StringUtils.isBlank(r.getProperties().getAndroidId())).collect(Collectors.toList());
            List<EventEntity> grayList = new ArrayList<>();
            if (Lists.noEmpty(deviceFilterEntitys)){
                deviceFilterEntitys.stream().forEach(event ->{
                    String exposureNoDeviceKey = RedisKeyConstants.getExposureNoDeviceKey(event.getProperties().getProduct(), event.getProperties().getUserId());
                    Long noDeviceCount = userEventJedisClusterClient.incrBy(exposureNoDeviceKey, 1);
                    userEventJedisClusterClient.expire(exposureNoDeviceKey, RedisKeyConstants.EXPIRE_ONE_DAYS);
                    log.info("异常event {} {} ",event.getProperties().getProduct(), event.getProperties().getUserId());
                    if (noDeviceCount >= 3) {
                        grayList.add(event);
                    }
                });
            }
            if (Lists.noEmpty(grayList)) {
                grayPublic(trace, grayList, log, this.riskReason.getDesc(), userGrayService);
            }
            // 3. 查询 ecpm 大于1200的用户，累计数量
            List<EventEntity>  highEcpmEntitys = eventEntities.parallelStream()
                    .filter(r -> !Objects.equals(r.getProperties().get$os(),"ios") && StringUtils.isNotBlank(r.getProperties().getExtend1()) && Double.valueOf(r.getProperties().getExtend1()) >= 1200).collect(Collectors.toList());
            if (Lists.noEmpty(highEcpmEntitys)) {
                highEcpmEntitys.stream().forEach(event ->{
                    String exposureHighEcpmKey = RedisKeyConstants.getExposureHighEcpmKey(event.getProperties().getProduct(), event.getProperties().getUserId());
                    Long noDeviceCount = userEventJedisClusterClient.incrBy(exposureHighEcpmKey, 1);
                    userEventJedisClusterClient.expire(exposureHighEcpmKey, RedisKeyConstants.EXPIRE_ONE_DAYS);
                    log.info("曝光价值大于1200 {} {} ",event.getProperties().getProduct(), event.getProperties().getUserId());
                    if (noDeviceCount >= 5) {
                        log.info("多次曝光高价值用户 {} {} ",event.getProperties().getProduct(), event.getProperties().getUserId());
                    }
                });

                // 4. 查询 ecpm 大于1500的用户，累计数量
                highEcpmEntitys = eventEntities.parallelStream()
                        .filter(r -> StringUtils.isNotBlank(r.getProperties().getExtend1()) && Double.valueOf(r.getProperties().getExtend1()) >= 1500)
                        .collect(Collectors.toList());
                if (Lists.noEmpty(highEcpmEntitys)) {
                    highEcpmEntitys.stream().forEach(event ->{
                        String exposureHighEcpmKey = RedisKeyConstants.getExposureHigh1500EcpmKey(event.getProperties().getProduct(), event.getProperties().getUserId());
                        Long noDeviceCount = userEventJedisClusterClient.incrBy(exposureHighEcpmKey, 1);
                        userEventJedisClusterClient.expire(exposureHighEcpmKey, RedisKeyConstants.EXPIRE_ONE_DAYS);
                        log.info("曝光价值大于1500 {} {} ",event.getProperties().getProduct(), event.getProperties().getUserId());
                        if (noDeviceCount >= 5) {
                            log.info("多次曝光高价值1500用户 {} {} ",event.getProperties().getProduct(), event.getProperties().getUserId());
                        }
                    });
                }

                highEcpmEntitys = highEcpmEntitys.parallelStream()
                        .filter(r -> Double.valueOf(r.getProperties().getExtend1()) >= 2000)
                        .collect(Collectors.toList());
                if (Lists.noEmpty(highEcpmEntitys)) {
                    highEcpmEntitys.stream().forEach(event ->{
                        String exposureHighEcpmKey = RedisKeyConstants.getExposureHigh2000EcpmKey(event.getProperties().getProduct(), event.getProperties().getUserId());
                        Long noDeviceCount = userEventJedisClusterClient.incrBy(exposureHighEcpmKey, 1);
                        userEventJedisClusterClient.expire(exposureHighEcpmKey, RedisKeyConstants.EXPIRE_ONE_DAYS);
                        log.info("曝光价值大于2000 {} {} ",event.getProperties().getProduct(), event.getProperties().getUserId());
                        if (noDeviceCount >= 5) {
                            log.info("多次曝光高价值2000用户 {} {} ",event.getProperties().getProduct(), event.getProperties().getUserId());
                        }
                        if (Double.valueOf(event.getProperties().getExtend1()) >= 3000) {
                            String highEcpmKey = RedisKeyConstants.getExposureHigh3000EcpmKey(event.getProperties().getProduct(), event.getProperties().getUserId());
                            Long count = userEventJedisClusterClient.incrBy(highEcpmKey, 1);
                            userEventJedisClusterClient.expire(highEcpmKey, RedisKeyConstants.EXPIRE_ONE_DAYS);
                        }
                    });
                }
            }

        });
        if (this.nextRiskFilter != null) {
            this.nextRiskFilter.doProcess(eventList, trace);
        }
    }



    @Override
    public void initReason() {
        this.riskReason = RiskReason.R16;
    }

    @Override
    public void initThreadPool() {
        this.threadPoolExecutor =  new ThreadPoolExecutor(4,8,100, TimeUnit.SECONDS,
                new ArrayBlockingQueue<>(20),new ThreadFactory("UserNoDeviceFilter-"),new ThreadPoolExecutor.AbortPolicy());
    }

    @Override
    public void afterPropertiesSet() throws Exception {
        this.initReason();
        this.initThreadPool();
    }
}
