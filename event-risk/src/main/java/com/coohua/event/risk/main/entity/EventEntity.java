package com.coohua.event.risk.main.entity;

import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @since 2021/11/25
 */
@Data
@NoArgsConstructor
public class EventEntity {
    // 基础事件
    private String event;
    private String appId;
    private String project;
    private String distinct_id;
    private Properties properties;

    private String reason;
    private Long serverTime;

    public static class Properties{
        private String $app_version;
        private String $carrier;
        private String $device_id;
        private String $model;
        private String $manufacturer;
        private String $os;
        private String ad_action;
        private String ad_type;
        private String ad_id;
        private String channel;
        private String imei;
        private String oaid;
        private String ip;
        private String product;
        private String userId;
        private String sdkVersion;
        private String page_name;
        private String element_uri;
        private String timestampClient;
        private String lib;
        private String androidId;

        private String extend1;
        private String extend3;
        private String extend5;
        private String extend8;

        private String caid;

        public String getElement_uri() {
            return element_uri;
        }

        public void setElement_uri(String element_uri) {
            this.element_uri = element_uri;
        }

        public String get$app_version() {
            return $app_version;
        }

        public void set$app_version(String $app_version) {
            this.$app_version = $app_version;
        }

        public String get$carrier() {
            return $carrier;
        }

        public void set$carrier(String $carrier) {
            this.$carrier = $carrier;
        }

        public String get$device_id() {
            return $device_id;
        }

        public void set$device_id(String $device_id) {
            this.$device_id = $device_id;
        }

        public String get$model() {
            return $model;
        }

        public void set$model(String $model) {
            this.$model = $model;
        }

        public String get$manufacturer() {
            return $manufacturer;
        }

        public void set$manufacturer(String $manufacturer) {
            this.$manufacturer = $manufacturer;
        }

        public String get$os() {
            return $os;
        }

        public void set$os(String $os) {
            this.$os = $os;
        }

        public String getAd_action() {
            return ad_action;
        }

        public void setAd_action(String ad_action) {
            this.ad_action = ad_action;
        }

        public String getChannel() {
            return channel;
        }

        public void setChannel(String channel) {
            this.channel = channel;
        }

        public String getImei() {
            return imei;
        }

        public void setImei(String imei) {
            this.imei = imei;
        }

        public String getOaid() {
            return oaid;
        }

        public void setOaid(String oaid) {
            this.oaid = oaid;
        }

        public String getIp() {
            return ip;
        }

        public void setIp(String ip) {
            this.ip = ip;
        }

        public String getProduct() {
            return product;
        }

        public void setProduct(String product) {
            this.product = product;
        }

        public String getUserId() {
            return userId;
        }

        public void setUserId(String userId) {
            this.userId = userId;
        }

        public String getSdkVersion() {
            return sdkVersion;
        }

        public void setSdkVersion(String sdkVersion) {
            this.sdkVersion = sdkVersion;
        }

        public String getExtend1() {
            return extend1;
        }

        public void setExtend1(String extend1) {
            this.extend1 = extend1;
        }

        public String getExtend3() {
            return extend3;
        }

        public void setExtend3(String extend3) {
            this.extend3 = extend3;
        }

        public String getAd_type() {
            return ad_type;
        }

        public void setAd_type(String ad_type) {
            this.ad_type = ad_type;
        }

        public String getPage_name() {
            return page_name;
        }

        public void setPage_name(String page_name) {
            this.page_name = page_name;
        }

        public String getAd_id() {
            return ad_id;
        }

        public void setAd_id(String ad_id) {
            this.ad_id = ad_id;
        }

        public String getTimestampClient() {
            return timestampClient;
        }

        public void setTimestampClient(String timestampClient) {
            this.timestampClient = timestampClient;
        }

        public String getExtend5() {
            return extend5;
        }

        public void setExtend5(String extend5) {
            this.extend5 = extend5;
        }

        public String getLib() {
            return lib;
        }

        public void setLib(String lib) {
            this.lib = lib;
        }

        public String getAndroidId() {
            return androidId;
        }

        public void setAndroidId(String androidId) {
            this.androidId = androidId;
        }

        public String getExtend8() {
            return extend8;
        }

        public void setExtend8(String extend8) {
            this.extend8 = extend8;
        }

        public String getCaid() {
            return caid;
        }

        public void setCaid(String caid) {
            this.caid = caid;
        }
    }
}
