package com.coohua.event.risk.main;

import com.coohua.caf.core.apollo.EnableAutoChangeApolloConfig;
import com.coohua.event.risk.main.log.Log4j2ContextInitializerOwn;
import com.ctrip.framework.apollo.spring.annotation.EnableApolloConfig;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.cache.annotation.EnableCaching;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.scheduling.annotation.EnableScheduling;
import top.zrbcool.pepper.boot.jediscluster.EnableJedisClusterClient;
import top.zrbcool.pepper.boot.motan.EnableMotan;

/**
 * <AUTHOR>
 * @since 2021/11/20
 */
@EnableApolloConfig(value = {
        "application",
        "caf.log.level",
        "caf.base.registry",
        "ad.base.health",
        "bp.user.rpc.referer",
        "bp.account.rpc.referer",
        "user-event-sub-db",
        "ad.user.event.rpc.service",
        "ad.user-event.redis",
        "ap.redis.cluster",
})
@EnableAutoChangeApolloConfig
@SpringBootApplication
@EnableScheduling
@EnableCaching
@EnableJedisClusterClient(namespace = "user-event")
@EnableJedisClusterClient(namespace = "ap-cluster")
@EnableMotan(namespace = "bp-user")
@EnableMotan(namespace = "bp-account")
@ComponentScan(basePackages = {"com.coohua.event.risk","com.coohua.user.event.biz"})
public class EventRiskApplication {
    public static void main(String[] args) {
        SpringApplication springApplication = new SpringApplication(EventRiskApplication.class);
        springApplication.addInitializers(new Log4j2ContextInitializerOwn());
        springApplication.run(args);
    }
}
