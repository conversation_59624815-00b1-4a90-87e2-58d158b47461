package com.coohua.event.risk.main.service.rules;

import com.coohua.event.risk.main.entity.EventEntity;
import com.coohua.event.risk.main.service.rules.impl.RiskFilter;
import com.coohua.user.event.biz.util.RedisKeyConstants;
import com.pepper.metrics.core.ThreadFactory;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;
import redis.clients.jedis.Jedis;
import redis.clients.jedis.Pipeline;
import top.zrbcool.pepper.boot.jediscluster.JedisClusterClient;
import top.zrbcool.pepper.boot.jediscluster.JedisClusterClientRefer;

import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.ArrayBlockingQueue;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2021/11/25
 */
@Slf4j
@Service
public class RewardCacheCount extends RiskFilter {
    @Value("${help.handler.chache.buffer:100}")
    private int helpHandlerCacheBufferSize = 100;
    @JedisClusterClientRefer(namespace = "user-event")
    private JedisClusterClient userEventJedisClusterClient;

    private static final String BASE = "{risk:reward}";
    private static final String COLON = ":";

    private static final Object mainLock = new Object();

    private static List<EventEntity> buffers = new ArrayList<>(1000);

    private static String cur;

    // 兜底每秒更新一次
    @Scheduled(cron = "*/1 * * * * ?")
//    @PostConstruct
    public void refreshRewardCacheCountRedis(){
        log.info("<=== refresh Reward Cache Count Redis...");
        if(CollectionUtils.isEmpty(buffers)){
            return;
        }

        List<EventEntity> params = null;
        synchronized (mainLock){
            params = buffers.parallelStream().collect(Collectors.toList());
            buffers = new ArrayList<>(1000);
        }

        if(CollectionUtils.isEmpty(params)){
            return;
        }
        cur = String.valueOf(System.currentTimeMillis());
        saveRedis(params);
    }

    public void saveRedis(List<EventEntity> params) {
        log.info("RewardCacheCount saveRedis params.size : {} params top : {} ", params.size(), params != null ? params.get(0) : null);
        try (Jedis jedis = userEventJedisClusterClient.getResource(BASE)){
            Pipeline pipeline = jedis.pipelined();
            params.forEach(entity -> {
                String key = buildKey(entity.getProperties().getUserId(), entity.getProperties().getProduct());
                pipeline.hset(key, "ct",cur);
                pipeline.hincrBy(key, "co", 1);
                pipeline.expire(key, 3 * 60 * 60 * 24);
            });
            pipeline.sync();
        }catch (Exception e){
            log.error("Batch SaveEx : {} , params : {}", e, params);
        }
    }

    private static String buildKey(String userId, String product) {
        return BASE + COLON + userId + COLON + product;
    }

    @Override
    public void doProcess(List<EventEntity> eventList, String trace) {
        this.threadPoolExecutor.execute(() -> {
            List<EventEntity> beans = eventList.parallelStream()
                    .filter(eventEntity -> StringUtils.equalsIgnoreCase(eventEntity.getProperties().getAd_action(), "reward"))
                    .collect(Collectors.toList());
            if (beans.size() > 0){
                this.threadPoolExecutor.submit(new Runnable() {
                    @Override
                    public void run() {
                        saveReward(beans, trace);
                    }
                });
            }
        });
        if(this.nextRiskFilter != null && CollectionUtils.isNotEmpty(eventList)){
            this.nextRiskFilter.doProcess(eventList, trace);
        }
    }

    private void saveReward(List<EventEntity> eventList, String trace){
        synchronized (mainLock){
            eventList.parallelStream().forEach(item -> buffers.add(item));
        }
        log.info("save saveReward eventList.size : {} eventList top : {} trace : {} ", eventList.size(), eventList != null ? eventList.get(0) : null, trace);
        if(buffers.size() > helpHandlerCacheBufferSize){
            refreshRewardCacheCountRedis();
        }
    }

    @Override
    public void initReason() {

    }

    @Override
    public void initThreadPool() {
        this.threadPoolExecutor =  new ThreadPoolExecutor(4,8,100, TimeUnit.SECONDS,
                new ArrayBlockingQueue<>(20),new ThreadFactory("RewardCacheCount-"),new ThreadPoolExecutor.AbortPolicy());
    }

    @Override
    public void afterPropertiesSet() throws Exception {
        this.initReason();
        this.initThreadPool();
    }
}
