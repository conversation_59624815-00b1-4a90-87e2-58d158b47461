package com.coohua.event.risk.main.utils;

import org.apache.commons.lang3.StringUtils;

/**
 * <pre>
 * Comments:
 *  版本号工具
 *      Func:
 *          1.提供比较版本大小的功能
 *              使用方式：
 *              Version this = new Version("1.2.3");
 *              Version other = new Version("2.3.4");
 *              int ret = this.compareTo(other);
 *
 * <pre/>
 * <hr/>
 * Created by <PERSON><PERSON><PERSON> on 2017/3/10.
 */
public class Version implements Comparable<Version> {

    private String version;

    public Version(String version) {
        this.version = version;
    }

    public String getVersion() {
        return version;
    }

    /**
     * <pre>
     *  比较版本大小
     *      比较规则：从左向右比较，当遇到一个this > other时，说明当前版本大，等于则继续比较子版本，小于说明当前版本小
     *               当长度不等时，先比较前几位，当前版本大则返回1；
     *                           前几位相等，如果当前版本长度短，返回-1，当前版本长度长，返回1。
     * </pre>
     * @param other 需要比较的版本号，必须以点号分隔(.)
     * @return 当前版本大时，返回1 <br/>
     *         当前版本小时，返回-1 <br/>
     *         相等则返回0
     */
    @Override
    public int compareTo(Version other) {
        if (StringUtils.isEmpty(other.getVersion())) {
            return 1;
        }
        String[] thisVersion = this.version.split("\\.");
        String[] otherVersion = other.getVersion().split("\\.");

        int length = Math.min(thisVersion.length, otherVersion.length);

        int index = 0;
        for (; index < length; index++) {
            int thisVer = Integer.parseInt(thisVersion[index]);
            int otherVer = Integer.parseInt(otherVersion[index]);
            if (thisVer > otherVer) {
                return 1;
            } else if (thisVer < otherVer) {
                return -1;
            }
        }

        // 如果上面没有return，说明较短version与较长version截断后的结果相同。需要继续判断较长version后面的值，如果全是0，则两个version相等，返回0
        // 否则，较长的那个version肯定包含>0的子版本号，则判定较长version大，返回1
        String[] longer = thisVersion.length > otherVersion.length ? thisVersion : otherVersion;
        boolean isThisVersionLonger = thisVersion.length > otherVersion.length;
        boolean containsGtZero = false;
        for (; index < longer.length; index++) {
            if (Integer.parseInt(longer[index]) > 0) {
                containsGtZero = true;
                break;
            }
        }

        if (containsGtZero) {
            return isThisVersionLonger ? 1 : -1;
        }

        return 0;
    }

    /**
     * 比较两个版本号的大小，返回version1.compareTo(version2)的结果
     * @param version1
     * @param version2
     * @return
     */
    public static int compare(String version1, String version2) {
        return new Version(version1).compareTo(new Version(version2));
    }

    public static void main(String[] args) {
        Version version1 = new Version("1.0.0.1");
        Version version2 = new Version("1.0.0.0");

        System.out.println(version1.compareTo(version2));

        int ret = Version.compare("1.4.5.3", "2.4.1");
        System.out.println(ret);

    }
}
