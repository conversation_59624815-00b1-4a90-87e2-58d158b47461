package com.coohua.event.risk.main.service.rules;

import com.alibaba.fastjson.JSON;
import com.coohua.event.risk.main.entity.EventEntity;
import com.coohua.event.risk.main.service.rules.impl.RiskFilter;
import com.coohua.user.event.biz.click.service.ClickHouseService;
import com.coohua.user.event.biz.enums.GrayType;
import com.coohua.user.event.biz.service.TFUserService;
import com.coohua.user.event.biz.service.UserGrayService;
import com.coohua.event.risk.main.config.RiskReason;
import com.coohua.event.risk.main.utils.EventUtils;
import com.coohua.user.event.biz.util.DateUtil;
import com.coohua.user.event.biz.util.Lists;
import com.coohua.user.event.biz.util.Strings;
import com.pepper.metrics.core.ThreadFactory;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;
import top.zrbcool.pepper.boot.jediscluster.JedisClusterClient;
import top.zrbcool.pepper.boot.jediscluster.JedisClusterClientRefer;

import java.util.*;
import java.util.concurrent.ArrayBlockingQueue;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2021/11/25
 */
@Slf4j
@Service
public class HistoryIpGrayFilter extends RiskFilter {

    @Autowired
    private UserGrayService userGrayService;
    @Autowired
    private ClickHouseService clickHouseService;
    @Autowired
    private TFUserService tfUserService;
    @JedisClusterClientRefer(namespace = "user-event")
    private JedisClusterClient userEventJedisClusterClient;

    private List<String> exIpListRedis = new ArrayList<>();

    // 每五分钟更新一次
    @Scheduled(cron = "0 0/5 * * * ?")
    public void refreshExIpListRedis(){
        log.info("<=== Risk Start Refresh Redis GrayIp...");
        Date now = new Date();
        // 简单实现
        String key = "{user:ip}:ip:gray:list"+ DateUtil.dateToString(now,DateUtil.ISO_DATE_FORMAT);
        String key1 = "{user:ip}:ip:gray:list"+ DateUtil.dateToString(DateUtil.dateIncreaseByDay(now,-1),DateUtil.ISO_DATE_FORMAT);
        String key2 = "{user:ip}:ip:gray:list"+ DateUtil.dateToString(DateUtil.dateIncreaseByDay(now,-2),DateUtil.ISO_DATE_FORMAT);
        String key3 = "{user:ip}:ip:gray:list"+ DateUtil.dateToString(DateUtil.dateIncreaseByDay(now,-3),DateUtil.ISO_DATE_FORMAT);
        List<String> results = userEventJedisClusterClient.mget(key,key1,key2,key3);
        List<String> ipGrayList = new ArrayList<>();
        ipGrayList.add("*************");
        if (Lists.noEmpty(results)){
            for (String rs : results){
                if (Strings.noEmpty(rs)){
                    List<String> ipList = JSON.parseArray(rs,String.class);
                    if (Lists.noEmpty(ipList)){
                        ipGrayList.addAll(ipList);
                    }
                }
            }
        }
        // 获取列表
        this.exIpListRedis = ipGrayList;
        log.info("<=== Risk End Refresh Redis GrayIp, Current Size:{}...",this.exIpListRedis.size());
    }

    @Override
    public void doProcess(List<EventEntity> eventList, String trace) {
        this.threadPoolExecutor.execute(() -> {
            List<EventEntity> needGrayEventList = eventList.parallelStream()
                    .filter(eventEntity -> !"ios".equalsIgnoreCase(eventEntity.getProperties().get$os()))
                    .filter(eventEntity -> exIpListRedis.contains(eventEntity.getProperties().getIp()))
                    .collect(Collectors.toList());
            // 2023-05-18 更新ip Redis动态调控
            if (needGrayEventList.size() > 0){
                grayPublic(trace, needGrayEventList, log, this.riskReason.getDesc(), userGrayService);
            }
        });
        this.nextRiskFilter.doProcess(eventList,trace);
    }

    @Override
    public void initReason() {
        this.riskReason = RiskReason.R8;
    }

    @Override
    public void initThreadPool() {
        this.threadPoolExecutor =  new ThreadPoolExecutor(4,8,100, TimeUnit.SECONDS,
                new ArrayBlockingQueue<>(20),new ThreadFactory("historyIpFilter-"),new ThreadPoolExecutor.AbortPolicy());
    }

    @Override
    public void afterPropertiesSet() throws Exception {
        this.initReason();
        this.initThreadPool();
        this.refreshExIpListRedis();
    }
}
