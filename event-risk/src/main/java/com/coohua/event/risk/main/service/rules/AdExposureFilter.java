package com.coohua.event.risk.main.service.rules;

import cn.hutool.core.collection.CollUtil;
import com.alibaba.fastjson.JSON;
import com.coohua.event.risk.main.config.RiskReason;
import com.coohua.event.risk.main.entity.EventEntity;
import com.coohua.event.risk.main.service.rules.impl.RiskFilter;
import com.coohua.event.risk.main.utils.EventUtils;
import com.coohua.user.event.biz.click.entity.ProductEntity;
import com.coohua.user.event.biz.service.AdCloseService;
import com.coohua.user.event.biz.service.TFUserService;
import com.coohua.user.event.biz.service.UserGrayService;
import com.coohua.user.event.biz.service.bean.OcpcEvent;
import com.coohua.user.event.biz.util.*;
import com.pepper.metrics.core.ThreadFactory;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import top.zrbcool.pepper.boot.jediscluster.JedisClusterClient;
import top.zrbcool.pepper.boot.jediscluster.JedisClusterClientRefer;

import java.util.*;
import java.util.concurrent.ArrayBlockingQueue;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

@Slf4j
@Service
public class AdExposureFilter extends RiskFilter {

    @Autowired
    private UserGrayService userGrayService;
    @Autowired
    private TFUserService tfUserService;
    @Autowired
    private AdCloseService adCloseService;
    @JedisClusterClientRefer(namespace = "ap-cluster")
    private JedisClusterClient apJedisClusterClient;
    @JedisClusterClientRefer(namespace = "user-event")
    private JedisClusterClient userEventJedisClusterClient;
    private final static String PRE_FIX = "{bp:own:exposure}:";

    @Value("${enable.realtime.check.multi.reqId:false}")
    private boolean enableRealtimeCheckMultiReqId;

    @Override
    public void doProcess(List<EventEntity> eventList, String trace) {
        this.threadPoolExecutor.execute(() -> {
//            List<EventEntity> eventEntities = eventList.stream()
//                    .filter(eventEntity -> "exposure".equals(eventEntity.getProperties().getAd_action()))
//                    .filter(eventEntity -> {
//                        String adType = eventEntity.getProperties().getAd_type();
//                        return adType.startsWith("1015") ||
//                                adType.startsWith("1008") ||
//                                adType.startsWith("1018") ||
//                                adType.startsWith("109") ||
//                                adType.startsWith("110") ||
//                                // 插屏也加入频控计算 进行控制
//                                adType.startsWith("1087") ||
//                                adType.startsWith("1073") ||
//                                adType.startsWith("1084") ||
//                                adType.startsWith("1061") ||
//                                adType.startsWith("1101") ||
//                                adType.startsWith("1081") ||
//                                adType.startsWith("1062") ||
//                                adType.startsWith("1083")
//                                ;
//                    })
//                    .filter(eventEntity -> !"0".equals(eventEntity.getProperties().getUserId()))
//                    .collect(Collectors.toList());
//            if (Lists.noEmpty(eventEntities)){
//                List<EventEntity> needGrayList = new ArrayList<>();
//                List<EventEntity> needDirectGrayList = new ArrayList<>();
//                eventEntities.forEach(eventEntity -> {
//                    String userId = eventEntity.getProperties().getUserId();
//                    String product = eventEntity.getProperties().getProduct();
//                    String time = eventEntity.getProperties().getTimestampClient();
//                    String productGroup = AppConfig.appIdMap.get(Long.valueOf(eventEntity.getAppId())).getProductGroup();
//                    if (Strings.noEmpty(time) && time.contains(".")){
//                        log.info("时间上报异常 {} {} {}",product,userId,JSON.toJSONString(eventEntity));
//                    }
//                    // 曝光pv频次检测
//                    long count = checkUserExposureCount(product,userId,time, eventEntity.getServerTime());
//                    if ("项目五组".equals(productGroup) && count > 10
//                            && StringUtils.isNotBlank(eventEntity.getProperties().getExtend1())
//                            && Double.parseDouble(eventEntity.getProperties().getExtend1()) > 1500) {
//                        needGrayList.add(eventEntity);
//                    } else if (count > 10 && count < 25) {
//                        needGrayList.add(eventEntity);
//                    } else if (count >= 25 && !"项目十组".equals(productGroup)){
//                        needDirectGrayList.add(eventEntity);
//                    }// extend5重复度检测
//                    if (Strings.noEmpty(eventEntity.getProperties().getExtend5())
//                            && "android".equals(eventEntity.getProperties().get$os())
//                            && !"MiniGame".equals(eventEntity.getProperties().getLib())){
//                        if (!checkExtend5(product,userId,eventEntity.getProperties().getExtend5())){
//                            needDirectGrayList.add(eventEntity);
//                        }
//                    }
//                });
//
//                if (needGrayList.size() == 0 && needDirectGrayList.size() == 0){
//                    return;
//                }
//                log.info("检测到曝光集中异常用户 {} {} {}",trace,needGrayList.size(),needDirectGrayList.size());
//                if (needGrayList.size() > 0) {
//                    // 筛选其中的非投放用户
//                    Map<String, Set<String>> userMap = EventUtils.getUserIdMap(needGrayList);
//                    Map<String, List<EventEntity>> productUserEventMap = EventUtils.getProductUserEventMap(needGrayList);
//                    // 查看是否投放
//                    userMap.forEach((product, sets) -> {
//                        ProductEntity productEntity = AppConfig.productEnMap.get(product);
//                        if (productEntity == null) {
//                            return;
//                        }
//                        if (sets != null && sets.size() > 0) {
//                            List<String> needGrayCurrentList = new ArrayList<>();
//                            if (productEntity.getProductGroup().equals("项目五组")) {
//                                needGrayCurrentList.addAll(sets);
//                            } else {
//                                List<String> userIdList = new ArrayList<>(sets);
//                                //        是否使用设备归因
//                                // 配置的产品列表为空或者包含当前产品，使用设备归因，否则走原来逻辑
//                                if (tfUserService.isUseDeviceGui(product)) {
//                                    try {
//                                        List<OcpcEvent> ocpcEventList = tfUserService.queryUserDeviceGuiBatch(EventUtils.convertEventEntityToUserScoreEntity(productUserEventMap.get(product)));
//                                        needGrayCurrentList = userIdList.stream()
//                                                .filter(userId -> !ocpcEventList.stream().anyMatch(event -> event.getUserId().equals(userId)))
//                                                .collect(Collectors.toList());
//                                        if (tfUserService.isDeviceGuiLogEnabled) {
//                                            log.info("AdExposureFilter设备归因 {} {} {} {}", product, userIdList, needGrayCurrentList, ocpcEventList);
//                                        }
//                                    } catch (Exception e) {
//                                        log.error("AdExposureFilter使用设备归因异常{} {}  {}", product, userIdList, productUserEventMap, e);
//                                    }
//                                } else {
//                                    needGrayCurrentList = tfUserService.queryNtfUser(product, new ArrayList<>(sets), trace);
//                                }
//                            }
//
//                            if (needGrayCurrentList.size() > 0) {
//                                // 直接拉黑
//                                grayPublic(trace, needGrayCurrentList, product, "android", log, "非投放用户曝光集中异常", userGrayService);
//                            }
//                        }
//                    });
////                }
//                // extend5 25 直接干
//                if (needDirectGrayList.size() > 0){
//                    grayPublic(trace, needDirectGrayList, log, "用户曝光集中异常-分钟频控", userGrayService);
//                }
//            }

            try {
                checkMultiReqId(eventList, trace);
            } catch (Exception e) {
                log.error("realtime check multi reqId error! {} data {}", trace, eventList, e);
            }
        });
        if (this.nextRiskFilter != null) {
            this.nextRiskFilter.doProcess(eventList, trace);
        }
    }

    private void checkMultiReqId(List<EventEntity> eventList, String trace) {

        if (!enableRealtimeCheckMultiReqId) {
            return;
        }

        List<EventEntity> eventEntities = eventList.stream()
                .filter(eventEntity -> "exposure".equals(eventEntity.getProperties().getAd_action()))
                .filter(eventEntity -> StringUtils.isNotBlank(eventEntity.getProperties().getElement_uri()))
                .filter(eventEntity -> !"0".equals(eventEntity.getProperties().getUserId()))
                .collect(Collectors.toList());

        if (CollUtil.isEmpty(eventEntities)) return;

        List<EventEntity> needBlackList = new ArrayList<>();
        eventEntities.stream()
                .collect(Collectors.toMap(
                        e -> e.getProperties().getProduct() +
                                e.getProperties().get$os() +
                                e.getProperties().getElement_uri() +
                                e.getProperties().getUserId(),
                        e -> e,
                        (existing, replacement) -> existing // userId维度 去重重复曝光
                ))
                .values()
                // 产品 os reqId维度分组统计重复reqid
                .stream()
                .collect(Collectors.groupingBy(this::buildMultiReqIdGroupKey))
                .forEach((key, v) -> {
                    String redisKey = RedisKeyConstants.buildMultiReqIdKey(key);
                    userEventJedisClusterClient.sadd(redisKey, v.stream().map(e -> e.getProperties().getUserId()).toArray(String[]::new));
                    userEventJedisClusterClient.expire(redisKey, 60 * 60 * 24);
                    if (userEventJedisClusterClient.scard(redisKey) > 1) {
                        needBlackList.addAll(v);
                    }
                });


        if (CollUtil.isNotEmpty(needBlackList)) {
            grayPublic(trace, needBlackList, log, "单产品不同用户req_id重复-异常拉黑-实时", userGrayService);
        }
    }

    private String buildMultiReqIdGroupKey(EventEntity e) {
        return e.getProperties().getProduct() + ":" +
                e.getProperties().get$os() + ":" +
                e.getProperties().getElement_uri();
    }

    private boolean checkExtend5(String product,String userId,String extend5){
        try {
            String jrKey = buildUserExtend5CheckKey(extend5);
            long count = userEventJedisClusterClient.incr(jrKey);
            userEventJedisClusterClient.expire(jrKey,60 * 60);
            if (count > 50){
                log.info("Extend5异常到上限 {} {} {}",product,userId,count);
                return false;
            }
            return true;
        }catch (Exception e){
            log.error("Do Check Extend5 Ex:",e);
            return true;
        }
    }

    // 若消息堆积异常 采用埋点时间做key
    private long checkUserExposureCount(String product,String userId,String time, Long serverTime){
        try {
            Date now = new Date();
            Date timeClient = now;
            if (Strings.noEmpty(time)) {
                if (time.contains(".")){
                    time = time.split("[.]")[0];
                }
                // 校验客户端上报时间是否篡改，篡改使用系统时间
                long timestampClient = Long.parseLong(time);
                timeClient = new Date(timestampClient);

                // 如果客户端时间比系统时间小x0分钟 或者超过当前时间
                if (timestampClient < now.getTime() - 1000 * 60 * 60 || timestampClient > now.getTime()) {
                    log.info("客户端篡改时间 {} {} timestampClient : {}  now : {} serverTime : {}", product, userId, timestampClient , now.getTime(), serverTime);
                    if(serverTime != null){
                        timeClient = new Date(serverTime);
                    }else{
                        timeClient = now;
                    }
                }
            }
            int keepTime = 60 * 2;
            String timeKey = DateUtil.dateToString(timeClient,DateUtil.COMPACT_TIME_FORMAT_MIN);
            String key = buildUserCountKey(product,userId,timeKey);
            long count = userEventJedisClusterClient.incr(key);
            userEventJedisClusterClient.expire(key,keepTime);
            if (count > 10){
                log.info("App {} User {} 当前曝光1min 达到 {}",product,userId,count);
            }

            return count;
        }catch (Exception e){
            log.error("Solve TimeBone Er:",e);
            return 0;
        }
    }


    private static String buildUserExtend5CheckKey(String extend5){
        return PRE_FIX +"exposure:extend5:" + extend5;
    }

    private static String buildUserCountKey(String product,String userId,String timeKey){
        return PRE_FIX +"set:build:" + product + ":" + userId + ":" + timeKey;
    }


    @Override
    public void initReason() {
        this.riskReason = RiskReason.R11;
    }

    @Override
    public void initThreadPool() {
        this.threadPoolExecutor =  new ThreadPoolExecutor(4,8,100, TimeUnit.SECONDS,
                new ArrayBlockingQueue<>(20),new ThreadFactory("AdExposureFilter-"),new ThreadPoolExecutor.AbortPolicy());
    }

    @Override
    public void afterPropertiesSet() throws Exception {
        this.initReason();
        this.initThreadPool();
    }
}
