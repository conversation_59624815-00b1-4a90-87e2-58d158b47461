package com.coohua.event.risk.main.service.rules;

import com.alibaba.fastjson.JSON;
import com.coohua.event.risk.main.config.RiskReason;
import com.coohua.event.risk.main.entity.EventEntity;
import com.coohua.event.risk.main.service.rules.impl.RiskFilter;
import com.coohua.event.risk.main.utils.Version;
import com.coohua.user.event.biz.click.entity.ProductEntity;
import com.coohua.user.event.biz.dc.entity.RealtimeRule;
import com.coohua.user.event.biz.dc.service.RealtimeRuleService;
import com.coohua.user.event.biz.enums.ActionType;
import com.coohua.user.event.biz.enums.ActiveChannel;
import com.coohua.user.event.biz.service.*;
import com.coohua.user.event.biz.service.bean.OcpcEvent;
import com.coohua.user.event.biz.service.bean.UserActive;
import com.coohua.user.event.biz.util.AppConfig;
import com.coohua.user.event.biz.util.DateUtil;
import com.coohua.user.event.biz.util.Lists;
import com.coohua.user.event.biz.util.Strings;
import com.ctrip.framework.apollo.spring.annotation.ApolloJsonValue;
import com.pepper.metrics.core.ThreadFactory;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.concurrent.ArrayBlockingQueue;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2022/1/22
 */
@Slf4j
@Service
public class PropertiesTargetGrayFilter extends RiskFilter {

    @Value("${realtime.rule.high.check:false}")
    private boolean ruleHighCheck ;

    @Autowired
    private TFUserService tfUserService;
    @Autowired
    private UserGrayService userGrayService;

    @Autowired
    private RealtimeRuleService realtimeRuleService;
    @Autowired
    private InnerPullService innerPullService;

    @Autowired
    private AdCloseService adCloseService;

    @Override
    public void doProcess(List<EventEntity> eventList, String trace) {
        this.threadPoolExecutor.execute(() -> doMain(eventList,trace));
        if (this.nextRiskFilter != null) {
            this.nextRiskFilter.doProcess(eventList, trace);
        }
    }

    @Override
    public void initReason() {
        this.riskReason = RiskReason.RX;
    }

    @Override
    public void initThreadPool() {
        this.threadPoolExecutor =  new ThreadPoolExecutor(8,16,100, TimeUnit.SECONDS,
                new ArrayBlockingQueue<>(40),new ThreadFactory("PropertiesTargetGrayFilter-"),new ThreadPoolExecutor.AbortPolicy());
    }

    private void  doMain(List<EventEntity> eventEntity,String trace){
        List<EventEntity> oaidSameWithAndroid = eventEntity.parallelStream()
                .filter(r -> (Strings.noEmpty(r.getProperties().getOaid()) && r
                        .getProperties().getOaid().equals(r.getProperties().getAndroidId()))
                )
                .collect(Collectors.toList());

        if (oaidSameWithAndroid.size() > 0){
            grayPublic(trace, oaidSameWithAndroid, log, "OA_ID异常", userGrayService);
        }
            /*try {
                Map<String, Set<String>> stopChannelMap = WithdrawCheckExService.stopChannelMap;

                if (MapUtils.isNotEmpty(stopChannelMap)) {
                    for (Map.Entry<String, Set<String>> stringSetEntry : stopChannelMap.entrySet()) {
                        String key = stringSetEntry.getKey();
                        Set<String> stopDate = stringSetEntry.getValue();
                        List<EventEntity> tempList = eventEntity.parallelStream()
                                .filter(f -> Objects.equals(key,f.getProperties().getProduct() + ":" + f.getProperties().getChannel()))
                                .collect(Collectors.toList());
                        if (tempList.size() > 0) {
                            List<String> userIdList = tempList.stream().map(r -> r.getProperties().getUserId()).collect(Collectors.toList());
                            String product = tempList.get(0).getProperties().getProduct();
                            List<UserActive> userActiveList = tfUserService.queryUserActiveInfo(product, userIdList);
                            if (ruleHighCheck) {
                                //从严，mac归因和ipua归因算作自然量
                                userActiveList.stream().forEach(active -> {
                                    if (StringUtils.isNotBlank(active.getGyType()) && StringUtils.equalsAny(active.getGyType(), "mac", "ipua", "ip", "ipmd")) {
                                        active.setSource(ActiveChannel.ZR.getChannel());
                                        log.info("active从严source {} ", JSON.toJSONString(active));
                                    }
                                });
                            }
                            Set<String> stopUserIds = userActiveList.parallelStream().filter(f -> Objects.equals(f.getSource(), ActiveChannel.ZR.getChannel()) && stopDate.contains(DateUtil.dateToString(f.getCreateTime()))).map(UserActive::getUserId).collect(Collectors.toSet());
                            if (CollectionUtils.isNotEmpty(stopUserIds)) {
                                List<EventEntity> stopEntity = tempList.parallelStream().filter(f -> stopUserIds.contains(f.getProperties().getUserId())).collect(Collectors.toList());
                                log.info("分时新增自然量提现异常拉黑 {} {}", key, stopUserIds);
                                grayPublic(trace, stopEntity, log, "分时新增自然量提现异常拉黑", userGrayService);
                            }
                        }
                    }
                }

            } catch (Exception e) {
                log.info("打印异常 " , e);
            }*/

        List<EventEntity> extend8CheckList = eventEntity.parallelStream()
                .filter(r -> "exposure".equalsIgnoreCase(r.getProperties().getAd_action()))
                // 2051新版本SDK
                .filter(r -> Version.compare("2.0.5.1",r.getProperties().get$app_version()) <= 0)
                .filter(r -> Strings.noEmpty(r.getProperties().getExtend8()) && !"true".equalsIgnoreCase(r.getProperties().getExtend8()))
                .collect(Collectors.toList());

        if (extend8CheckList.size() > 0){
            Set<String> userList = extend8CheckList.stream().map(r -> r.getProperties().getUserId()).collect(Collectors.toSet());
            log.info("Check Extend8List Er Size {} , erUser: {}",userList.size(),JSON.toJSONString(userList));
        }

        for (RealtimeRule realtimeRule : realtimeRuleService.queryBlackRealTimeRules()){
            List<EventEntity> eventEntities = new ArrayList<>();

            List<EventEntity> tempList = eventEntity.parallelStream()
                    .filter(rule -> !Objects.equals(rule.getProperties().get$os(),"ios"))
                    .filter(rule -> realtimeRule.getProduct().equals(rule.getProperties().getProduct()))
                    .filter(rule -> realtimeRule.checkChannel(rule.getProperties().getChannel()))
                    .filter(rule -> realtimeRule.checkBrand(rule.getProperties().get$manufacturer()))
                    .filter(rule -> realtimeRule.checkModel(rule.getProperties().get$model()))
                    .filter(rule -> realtimeRule.checkIp(rule.getProperties().getIp()))
                    .collect(Collectors.toList());

            if (tempList.size() > 0) {
                tempList = tempList.stream().peek(e -> e.setReason(realtimeRule.getRuleName())).collect(Collectors.toList());
                List<String> userIdList = tempList.stream().map(r -> r.getProperties().getUserId()).collect(Collectors.toList());

                List<UserActive> userActiveList = new ArrayList<>();
                List<OcpcEvent> ocpcEventList = new ArrayList<>();

                List<String> ocpcUserIdList = new ArrayList<>();
                List<String> notOcpcUserIdList = new ArrayList<>();

                // 需要查询
                if (realtimeRule.getOcpcType() == 1 || realtimeRule.getOcpcType() == 2
                        || realtimeRule.getNewUserType() > 0 || realtimeRule.getSourceList().size() > 0){
                    userActiveList = tfUserService.queryUserActiveInfo(realtimeRule.getProduct(), userIdList);

                    try {
                        // 设备归因查询是否为ocpc用户
                        if (tfUserService.isUseDeviceGui(realtimeRule.getProduct())) {
                            ocpcEventList = tfUserService.queryUserDeviceGuiBatch(realtimeRule.getProduct(), userIdList, "android", realtimeRule.getAppId());
                            ocpcUserIdList = ocpcEventList.stream().map(OcpcEvent::getUserId).collect(Collectors.toList());
                            List<String> finalOcpcUserIdList = ocpcUserIdList;
                            notOcpcUserIdList = userIdList.stream().filter(f -> !finalOcpcUserIdList.contains(f)).collect(Collectors.toList());
                            if (tfUserService.isDeviceGuiLogEnabled) {
                                log.info("PropertiesTargetGrayFilter设备归因 {} {} {} {}", realtimeRule.getProduct(), userIdList, notOcpcUserIdList, ocpcEventList);
                            }
                        }
                    } catch (Exception e) {
                        log.error("PropertiesTargetGrayFilter使用设备归因异常" , e);
                    }
                }

                if (ruleHighCheck) {
                    //从严，mac归因和ipua归因算作自然量
                    userActiveList.stream().forEach(active -> {
                        if (StringUtils.isNotBlank(active.getGyType()) && StringUtils.equalsAny(active.getGyType(), "mac", "ipua", "ip", "ipmd")) {
                            active.setSource(ActiveChannel.ZR.getChannel());
                            log.info("active从严source {} ",JSON.toJSONString(active));
                        }
                    });
                }
                // OCPC_TYPE检测
                List<EventEntity> filterStep1List = checkOcpcType(realtimeRule,tempList,userActiveList,trace,userIdList, ocpcUserIdList, notOcpcUserIdList);
                // NEW_USER_TYPE&&SOURCE检测
                List<EventEntity> filterStep2List = checkSourceAndNewUserType(realtimeRule,filterStep1List,userActiveList,trace);
                if (filterStep2List.size() > 0){
                    eventEntities.addAll(filterStep2List);
                }
            }
            if (eventEntities.size() > 0){
                solveExUser(realtimeRule,eventEntities,trace);
            }
        }
    }

    private List<EventEntity> checkSourceAndNewUserType(RealtimeRule realtimeRule,List<EventEntity> tempList,
                                                        List<UserActive> userActiveList,String trace){
        List<EventEntity> eventEntities = new ArrayList<>();

        List<UserActive> userActives = userActiveList.stream()
                .filter(r -> realtimeRule.checkSources(r.getSource()))
                .collect(Collectors.toList());

        Date now = new Date();
        String todayBegin = DateUtil.dateToString(now) + " 00:00:00";
        Date today = DateUtil.stringToDate(todayBegin,DateUtil.COMMON_TIME_FORMAT);
        if (realtimeRule.getNewUserType() == 0){
            eventEntities.addAll(tempList);
        }else if (realtimeRule.getNewUserType() == 1){
            // 新用户
            List<String>  newUserList = userActives.stream()
                    .filter(r -> r.getCreateTime().after(today))
                    .map(UserActive::getUserId).collect(Collectors.toList());
            List<String>  alUserList = userActives.stream()
                    .map(UserActive::getUserId).collect(Collectors.toList());
            for (EventEntity eventEntity1 : tempList){
                // 没查到也是新用户
                if (newUserList.contains(eventEntity1.getProperties().getUserId()) ||
                        !alUserList.contains(eventEntity1.getProperties().getUserId())){
                    eventEntities.add(eventEntity1);
                }
            }
        }else if (realtimeRule.getNewUserType() == 2){
            List<String>  oldUserList = userActives.stream()
                    .filter(r -> r.getCreateTime().before(today))
                    .map(UserActive::getUserId).collect(Collectors.toList());
            for (EventEntity eventEntity1 : tempList){
                if (oldUserList.contains(eventEntity1.getProperties().getUserId())){
                    eventEntities.add(eventEntity1);
                }
            }
        }
        return eventEntities;
    }

    private List<EventEntity> checkOcpcType(RealtimeRule realtimeRule,List<EventEntity> tempList,
                               List<UserActive> userActiveList,String trace,List<String> userIdList, List<String> ocpcUserIdList, List<String> notOcpcUserIdList){
        List<EventEntity> eventEntities = new ArrayList<>();
        ProductEntity productEntity = AppConfig.productEnMap.get(realtimeRule.getProduct());

        boolean isUserLogin = tempList.stream().anyMatch(r -> "login".equals(r.getProperties().getAd_action()));

        if (realtimeRule.getOcpcType() == 0) {
            eventEntities.addAll(tempList);
        } else if (realtimeRule.getOcpcType() == 1) {
            List<String>  tfUserList = userActiveList.stream().filter(r ->{
                ActiveChannel activeChannel = ActiveChannel.getByDesc(r.getSource());
                if (Lists.noEmpty(realtimeRule.getSourceList())){
                    return realtimeRule.getSourceList().contains(activeChannel.getChannel());
                }
                return ActiveChannel.isOCPCChanel(activeChannel);
            }).map(UserActive::getUserId).collect(Collectors.toList());
            if (tfUserService.isUseDeviceGui(productEntity.getProduct())) {
                tfUserList = ocpcUserIdList;
            }
            for (EventEntity eventEntity1 : tempList){
                if (tfUserList.contains(eventEntity1.getProperties().getUserId())){
                    eventEntities.add(eventEntity1);
                }
            }
        } else if (realtimeRule.getOcpcType() == 2) {
            List<String>  ntfUserList = userActiveList.stream().filter(r ->{
                ActiveChannel activeChannel = ActiveChannel.getByDesc(r.getSource());
                return !ActiveChannel.isOCPCChanel(activeChannel);
            }).map(UserActive::getUserId).collect(Collectors.toList());
            if (tfUserService.isUseDeviceGui(productEntity.getProduct())) {
                ntfUserList = notOcpcUserIdList;
            }
            for (EventEntity eventEntity1 : tempList){
                if (ntfUserList.contains(eventEntity1.getProperties().getUserId())){
                    eventEntities.add(eventEntity1);
                }
            }
            // 设备归因的话 直接返回，不走下边旧逻辑
            if (tfUserService.isUseDeviceGui(productEntity.getProduct())) {
               return eventEntities;
            }
            if (isUserLogin && eventEntities.size() < tempList.size()){
                log.info("{} not found in userActive, QueryTouTiaoClick",trace);
                for (EventEntity eventEntity1 : tempList){
                    String product = eventEntity1.getProperties().getProduct();
                    String deviceId = eventEntity1.getProperties().get$device_id();
                    String oaid = eventEntity1.getProperties().getOaid();
                    String imei = eventEntity1.getProperties().getImei();
                    if (!tfUserService.isOcpc(deviceId,imei,oaid,product)){
                        eventEntities.add(eventEntity1);
                    }
                }
            }
        } else if (realtimeRule.getOcpcType() == 3){

            if (productEntity != null) {
                List<String> nInnerPullUser = innerPullService.queryNInnerPull(productEntity.getId(), userIdList, trace);
                for (EventEntity eventEntity1 : tempList) {
                    if (nInnerPullUser.contains(eventEntity1.getProperties().getUserId())) {
                        eventEntities.add(eventEntity1);
                    }
                }
            }
        }
        return eventEntities;
    }

    private void solveExUser(RealtimeRule realtimeRule,List<EventEntity> eventEntities,String trace){
        if (ActionType.GRAY_BLACK.getType().equals(realtimeRule.getActionType()) ||
                ActionType.GRAY_BLACK_PART.getType().equals(realtimeRule.getActionType())){
            boolean isGlobalGray = true;
            if (ActionType.GRAY_BLACK_PART.getType().equals(realtimeRule.getActionType())){
                isGlobalGray = false;
            }
            grayPublic(trace, eventEntities, log, this.riskReason.getDesc(), userGrayService,isGlobalGray);
        }else if (ActionType.LIMIT_ECPM.getType().equals(realtimeRule.getActionType())){
            // 输出控制
            // 跳过平台广告
            eventEntities.forEach(eventEntityTk ->{
                if (Lists.noEmpty(realtimeRule.getActionDto().getPlatformLimitSkip())){
                    ProductEntity productEntity = AppConfig.productEnMap.get(eventEntityTk.getProperties().getProduct());
                    if (productEntity == null){
                        return;
                    }
                    adCloseService.setSkipPlatform(productEntity.getId(),eventEntityTk.getProperties().getUserId(),
                            realtimeRule.getActionDto().getPlatformLimitSkip(),
                            60 * 60 * 24 * 15);
                }
            });
        }else if (ActionType.GRAY.getType().equals(realtimeRule.getActionType())){
            eventEntities.forEach(eventEntity -> {
                ProductEntity productEntity = AppConfig.productEnMap.get(eventEntity.getProperties().getProduct());
                if (productEntity == null){
                    return;
                }
                userGrayService.sendUserGrayWithdrawReason(productEntity.getId(),eventEntity.getProperties().getUserId(),this.riskReason.getDesc());
            });
        }
    }

    @Override
    public void afterPropertiesSet() throws Exception {
        this.initReason();
        this.initThreadPool();
    }

}
