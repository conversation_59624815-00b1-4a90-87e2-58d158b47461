package com.coohua.event.risk.main.service.rules;

import com.alibaba.fastjson.JSON;
import com.coohua.event.risk.main.config.RiskReason;
import com.coohua.event.risk.main.entity.EventEntity;
import com.coohua.event.risk.main.service.rules.impl.RiskFilter;
import com.coohua.user.event.biz.click.entity.ProductEntity;
import com.coohua.user.event.biz.click.service.ClickHouseService;
import com.coohua.user.event.biz.service.AdCloseService;
import com.coohua.user.event.biz.service.TFUserService;
import com.coohua.user.event.biz.service.bean.UserActive;
import com.coohua.user.event.biz.toufang.service.TouFangBaseQueryService;
import com.coohua.user.event.biz.util.AppConfig;
import com.ctrip.framework.apollo.spring.annotation.ApolloJsonValue;
import com.pepper.metrics.core.ThreadFactory;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;
import top.zrbcool.pepper.boot.jediscluster.JedisClusterClient;
import top.zrbcool.pepper.boot.jediscluster.JedisClusterClientRefer;

import javax.annotation.PostConstruct;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ArrayBlockingQueue;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2022/2/12
 */
@Slf4j
@Service
public class MiModelDontShowCsjAdFilter extends RiskFilter {

    @ApolloJsonValue("${not.show.csj.ad.model:[\"MI 8\",\"MI 8 Lite\",\"MI 8 SE\",\"MI 8 UD\"]}")
    private List<String> targetModel;

    @ApolloJsonValue("${not.show.csj.ad.app:[\"wdfd\",\"yydxny\",\"jnxfsh\",\"fengshounongchang\",\"xfjc\",\"qmyh\"]}")
    private List<String> targetProduct;

    @JedisClusterClientRefer(namespace = "ap-cluster")
    private JedisClusterClient apJedisClusterClient;

    @ApolloJsonValue("${white.list.user:[\"495_139034\"]}")
    private List<String> whiteList;

    private static List<String> jfModel = Arrays.asList("PEHM00","V1901A");
    private static List<String> jfManufacturer = Arrays.asList("xiaomi","meizu","huawei");

    @Autowired
    private TFUserService tfUserService;
    @Autowired
    private AdCloseService adCloseService;
    @Autowired
    private ClickHouseService clickHouseService;

    private List<String> riskIpList;

//    @Scheduled(cron = "0 0 * * * ?")
    public void refreshRiskIpList(){
        try {
            riskIpList = clickHouseService.getRiskIpList();
        }catch (Exception e){
            log.info("Refresh RiskIpList Ex:",e);
        }
    }

    @Override
    public void doProcess(List<EventEntity> eventList, String trace) {
        this.threadPoolExecutor.execute(() ->{
            Map<String,EventEntity> resultMap = eventList
                    .parallelStream()
                    .filter(eventEntity -> {
                        if (targetProduct.contains(eventEntity.getProperties().getProduct())){
                            return targetModel.contains(eventEntity.getProperties().get$model());
                        }
                        if ("ygjy2".equals(eventEntity.getProperties().getProduct()) && "android".equals(eventEntity.getProperties().get$os())){
                            return jfModel.contains(eventEntity.getProperties().get$model());
                        }
                        if ("zytysh".equals(eventEntity.getProperties().getProduct()) && "android".equals(eventEntity.getProperties().get$os())){
                            if ("neilaxin".equals(eventEntity.getProperties().getChannel())) {
                                return jfManufacturer.contains(eventEntity.getProperties().get$manufacturer());
                            }
                        }
//                        if ("zzzy".equals(eventEntity.getProperties().getProduct()) && "android".equals(eventEntity.getProperties().get$os())){
//                            return riskIpList.contains(eventEntity.getProperties().getIp());
//                        }
                        return false;
                    })
                    .collect(Collectors.toMap(e -> e.getProperties().getProduct() + e.getProperties().getUserId(), r->r,(r1,r2)->r1));

            for (EventEntity eventEntity : resultMap.values()){
                ProductEntity productEntity = AppConfig.productEnMap.get(eventEntity.getProperties().getProduct());
                if (productEntity == null){
                    continue;
                }
                if (whiteList.contains(String.format("%d_%s",productEntity.getId(),eventEntity.getProperties().getUserId()))){
                    log.info("White List,Jump");
                    return;
                }

                if ("ygjy2".equals(productEntity.getProduct())){
                    //        是否使用设备归因
                    if (tfUserService.isUseDeviceGui(productEntity.getProduct())) {
                        try {
                            UserActive userActive = tfUserService.queryUserDeviceGui(productEntity.getProduct(), eventEntity.getProperties().getUserId(), Long.valueOf(productEntity.getId()));
                            if (userActive == null || userActive.isOcpc()) {
                                return;
                            }
                        } catch (Exception e) {
                            log.error("MiModelDontShowCsjAdFilter使用设备归因异常 {} {} {}",productEntity.getProduct(), eventEntity.getProperties().getUserId(), Long.valueOf(productEntity.getId()), e);
                        }
                    } else {
                        List<String> rs = tfUserService.queryNtfUser(productEntity.getProduct(), Collections.singletonList(eventEntity.getProperties().getUserId()),trace);
                        if (rs.size() == 0){
                            return;
                        }
                    }
                }
                // 不看穿山甲
                adCloseService.setNoCsjAd(productEntity.getId(),eventEntity.getProperties().getUserId(),60 * 60 * 24 * 30);
                log.info("{} ===> USER {} {} CANT SEE CSJ AD ANYMORE",trace,productEntity.getId(),eventEntity.getProperties().getUserId());
            }
        });
        this.nextRiskFilter.doProcess(eventList,trace);
    }

    @Override
    public void initReason() {
        this.riskReason = RiskReason.RP;
    }

    @Override
    public void initThreadPool() {
        this.threadPoolExecutor =  new ThreadPoolExecutor(4,8,100, TimeUnit.SECONDS,
                new ArrayBlockingQueue<>(20),new ThreadFactory("HistoryIpGrayFilter-"),new ThreadPoolExecutor.AbortPolicy());
    }

    @Override
    public void afterPropertiesSet() throws Exception {
        this.initReason();
        this.initThreadPool();
        this.refreshRiskIpList();
    }
}
