package com.coohua.event.risk.main.config;

/**
 * <AUTHOR>
 * @since 2021/11/25
 */
public enum RiskReason {
    RP(-999,"其他处理,不拉黑"),
    RX(-1,"自定义规则拉黑:%s"),
    R0(0,"手动拉黑"),
    R1(1,"同IP设备20以上-投放用户小于5-非投放用户拉黑"),
    R2(2,"同IP设备10以上-投放用户小于2-非投放用户拉黑"),
    R3(3,"同IP|厂商|机型设备5以上-投放用户0-用户拉黑"),
    R4(4,"火山SDK检测900-非投放用户拉黑"),
    R5(5,"内拉新个数10-同手机型号超50%-用户拉黑"),
    R6(6,"AndrondIP地址前三段机型8以上-非投放用户拉黑"),
    R7(7,"投放渠道来源-单个IP投放用户占比小于50%-非投放用户拉黑"),
    R8(8,"历史异常IP-非投放用户拉黑"),
    R9(9,"已封禁用户不是ocpc师傅-已封禁用户徒弟-用户拉黑"),
    R10(10,"分渠道投放用户占比过低-非投放用户拉黑"),
    R11(11,"穿山甲鉴定为虚假流量-用户拉灰"),
    R12(12,"ROOT用户手机曝光-用户拉黑"),
    R13(13,"自然量用户曝光高分层广告过多-用户拉黑"),
    R14(14,"百度广告ECPM过高-用户拉黑"),
    R15(15,"同渠道厂商机型IP前三段一样超过10-用户拉黑"),
    R16(16,"设备id空白异常-用户拉黑"),
    ;
    private Integer type;
    private String desc;

    RiskReason(Integer type, String desc) {
        this.type = type;
        this.desc = desc;
    }

    public Integer getType() {
        return type;
    }

    public void setType(Integer type) {
        this.type = type;
    }

    public String getDesc() {
        return desc;
    }

    public void setDesc(String desc) {
        this.desc = desc;
    }

    public static RiskReason getByDesc(String desc){
        for (RiskReason riskReason : RiskReason.values()){
            if (riskReason.getDesc().equals(desc)){
                return riskReason;
            }
        }
        return R0;
    }
}
