package com.coohua.event.risk.main.service.rules.impl;

import com.alibaba.fastjson.JSON;
import com.coohua.event.risk.main.config.RiskReason;
import com.coohua.event.risk.main.entity.EventEntity;
import com.coohua.user.event.biz.enums.GrayType;
import com.coohua.user.event.biz.service.UserGrayService;
import org.slf4j.Logger;
import org.springframework.beans.factory.InitializingBean;

import java.util.List;
import java.util.Map;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2021/11/26
 */
public abstract class RiskFilter implements RealtimeFilter, InitializingBean {

    protected RiskReason riskReason;

    protected ThreadPoolExecutor threadPoolExecutor;

    protected RiskFilter nextRiskFilter;

    protected void setNext(RiskFilter riskFilter){
        this.nextRiskFilter = riskFilter;
    }

    public static class Builder{
        private RiskFilter head;
        private RiskFilter tail;

        public Builder addRiskFilter(RiskFilter riskFilter){
            if (this.head == null){
                this.head = this.tail = riskFilter;
                return this;
            }
            this.tail.setNext(riskFilter);
            this.tail = riskFilter;
            return this;
        }

        public RiskFilter build(){
            return this.head;
        }
    }

    public void grayPublic(String trace, List<String> userIdList,String product,String os, Logger log, String desc, UserGrayService userGrayService) {
        log.info("{} ===> Risk Confirmed, Reason {} Need Gray,Checkout U:{}, usList:{}", trace, desc, userIdList.size(), JSON.toJSONString(userIdList));
        Map<String,String> needGrayMap = userIdList.stream().collect(Collectors.toMap(r->r,r->desc,(r1,r2)->r1));
        userGrayService.grayUserAsync(GrayType.USER,needGrayMap,product,os,trace,true);
    }

    public void grayPublic(String trace, List<EventEntity> eventEntities, Logger log, String desc, UserGrayService userGrayService) {
        grayPublic(trace, eventEntities, log, desc, userGrayService,true);
    }

    public void grayPublic(String trace, List<EventEntity> eventEntities, Logger log, String desc, UserGrayService userGrayService,Boolean isGlobalGray) {
        log.info("{} ===> Risk Confirmed, Reason {} Need Gray,Checkout U:{}", trace, desc, eventEntities.size());
        Map<String,List<EventEntity>> grayMap = eventEntities.stream().collect(Collectors.groupingBy(r ->r.getProperties().getProduct()));
        grayMap.forEach((product,list) -> {
            Map<String,String> needGrayMap = list.stream()
                    .collect(Collectors.toMap(e->e.getProperties().getUserId(),
                            e-> String.format(desc,e.getReason()),
                            (r1,r2)->r2)
                    );
            // 拉满
            Map<String,String> deviceMap = list.stream()
                    .collect(Collectors.toMap(e->e.getProperties().get$device_id(),
                            e-> String.format(desc,e.getReason()),
                            (r1,r2)->r2));
            deviceMap.putAll(list.stream()
                    .collect(Collectors.toMap(e->e.getProperties().getImei(),
                            e-> String.format(desc,e.getReason()),
                            (r1,r2)->r2)));
            deviceMap.putAll(list.stream()
                    .collect(Collectors.toMap(e->e.getProperties().getOaid(),
                            e-> String.format(desc,e.getReason()),
                            (r1,r2)->r2)));
            userGrayService.grayUserAsync(GrayType.USER,needGrayMap,product,"android",trace,isGlobalGray);
            userGrayService.grayUserAsync(GrayType.DEVICE,deviceMap,product,"android",trace,isGlobalGray);
        });
    }
}
