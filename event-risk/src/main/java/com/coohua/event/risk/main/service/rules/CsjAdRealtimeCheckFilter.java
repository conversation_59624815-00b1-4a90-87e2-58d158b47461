package com.coohua.event.risk.main.service.rules;

import com.coohua.event.risk.main.config.RiskReason;
import com.coohua.event.risk.main.entity.EventEntity;
import com.coohua.event.risk.main.service.rules.impl.RiskFilter;
import com.coohua.user.event.biz.click.entity.ProductEntity;
import com.coohua.user.event.biz.service.AdCloseService;
import com.coohua.user.event.biz.service.UserGrayService;
import com.coohua.user.event.biz.util.AppConfig;
import com.coohua.user.event.biz.util.DingTalkPushUtils;
import com.coohua.user.event.biz.util.Lists;
import com.coohua.user.event.biz.util.Strings;
import com.pepper.metrics.core.ThreadFactory;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import redis.clients.jedis.Jedis;
import redis.clients.jedis.Pipeline;
import redis.clients.jedis.Response;
import top.zrbcool.pepper.boot.jediscluster.JedisClusterClient;
import top.zrbcool.pepper.boot.jediscluster.JedisClusterClientRefer;

import java.io.IOException;
import java.util.List;
import java.util.concurrent.ArrayBlockingQueue;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

@Slf4j
@Service
public class CsjAdRealtimeCheckFilter extends RiskFilter {

    @Autowired
    private UserGrayService userGrayService;
    @Autowired
    private AdCloseService adCloseService;
    @JedisClusterClientRefer(namespace = "ap-cluster")
    private JedisClusterClient apJedisClusterClient;
    @JedisClusterClientRefer(namespace = "user-event")
    private JedisClusterClient userEventJedisClusterClient;
    private final static String PRE_FIX = "{user:count}:";

    @Override
    public void doProcess(List<EventEntity> eventList, String trace) {
        this.threadPoolExecutor.execute(() -> {
            List<EventEntity> eventEntities = eventList.stream()
                    .filter(eventEntity -> "reward".equals(eventEntity.getEvent()) &&
                            eventEntity.getProperties().getAd_type().startsWith("1015"))
                    .filter(eventEntity -> "10111".equals(eventEntity.getProperties().getExtend3()))
                    .collect(Collectors.toList());
            if (Lists.noEmpty(eventEntities)){
                log.info("{} 用户存在虚假流量 尝试禁止穿山甲广告",trace);
                eventEntities.forEach(eventEntity -> {
                    try {
                        ProductEntity productEntity = AppConfig.productEnMap.get(eventEntity.getProperties().getProduct());
                        if (productEntity == null){
                            return;
                        }
                        Integer appId = productEntity.getId();
                        String userId = eventEntity.getProperties().getUserId();
                        // 拉灰
//                    userGrayService.sendUserGrayWithdraw(appId,userId);
//                    log.info("{} ==> 已设置 {} {} 提现拉灰..",trace,appId,userId);
                        // 3次以上且20min队列只有1000以内
                        if (checkErCount(appId,userId) && checkUserCount(appId,userId)) {
                            // 设置跳过穿山甲广告
                            adCloseService.setNoCsjAd(appId,userId,60*20);
                            String set1Ecpm = buildCallBackSet1Ecpm(appId, userId);
                            // 20min csj回传1ecpm
                            apJedisClusterClient.set(set1Ecpm, "[1]");
                            apJedisClusterClient.expire(set1Ecpm, 60 * 20);
                            log.info("{} ==> 已设置 {} {} 跳过穿山甲广告 触发穿山甲检测..", trace, appId, userId);
                        }
                    }catch (Exception e){
                        log.error("Solve Csj Er:",e);
                    }
                });
            }
        });
        this.nextRiskFilter.doProcess(eventList,trace);
    }

    ;
    private Boolean checkErCount(Integer appId,String userId){
        String key = String.format(PRE_FIX+"user:er:count:%s:%s",appId,userId);
        String result = userEventJedisClusterClient.get(key);
        if (Strings.isEmpty(result)){
            userEventJedisClusterClient.incr(key);
            userEventJedisClusterClient.expire(key, 60* 60* 24);
            return false;
        }else {
            int count = Integer.parseInt(result);
            if (count <= 2){
                userEventJedisClusterClient.incr(key);
                userEventJedisClusterClient.expire(key, 60* 60* 24);
                return false;
            }else {
                userEventJedisClusterClient.set(key,"0");
                userEventJedisClusterClient.expire(key, 60* 60* 24);
                return true;
            }
        }
    }

    // Redis 滑动窗口统计被处理的用户个数
    private boolean checkUserCount(Integer appId,String userId){
        int keepTime = 60 * 20;
        String key = buildUserCountZSetKey();

        Long count = getNoCsjByThisRules(key, keepTime,String.format("%s:%s",appId,userId));
        if (count <= 1000 && count > 0){
            return true;
        }else {
            if (count > 1300L) {
                String pushKey = dingTalkNoStringKey();
                String result = userEventJedisClusterClient.get(pushKey);
                if (Strings.isEmpty(result)){
                    DingTalkPushUtils.sendMsg("> 穿山甲错误码:10111 设置不看穿山甲已达到" + count + "人");
                    userEventJedisClusterClient.set(pushKey,"true");
                    // 静默周期 15min
                    userEventJedisClusterClient.expire(pushKey, 60 * 15);
                }
            }
        }
        return false;
    }

    public Long getNoCsjByThisRules(String key, int period,String val){
        long ts = System.currentTimeMillis();
        try (Jedis jedis = userEventJedisClusterClient.getResource(PRE_FIX)){
            Pipeline pipe = jedis.pipelined();
            pipe.multi();
            pipe.zadd(key, ts, val);
            pipe.zremrangeByScore(key, 0, ts - (period * 1000L));
            Response<Long> count = pipe.zcard(key);
            pipe.expire(key, period);
            pipe.exec();
            pipe.close();
            return count.get();
        }catch (Exception e){
            log.error("RedisEr:",e);
            return -1L;
        }
    }

    private static String dingTalkNoStringKey(){
        return PRE_FIX + "ding_talk";
    }

    private static String buildUserCountZSetKey(){
        return PRE_FIX +"set:build";
    }


    private static  String buildCallBackSet1Ecpm(Integer app,String userId){
        return String.format("ad:call:back:ecpm:%s:%s",app, userId);
    }

    @Override
    public void initReason() {
        this.riskReason = RiskReason.R11;
    }

    @Override
    public void initThreadPool() {
        this.threadPoolExecutor =  new ThreadPoolExecutor(4,8,100, TimeUnit.SECONDS,
                new ArrayBlockingQueue<>(20),new ThreadFactory("CsjRealtimeCheckFilter-"),new ThreadPoolExecutor.AbortPolicy());
    }

    @Override
    public void afterPropertiesSet() throws Exception {
        this.initReason();
        this.initThreadPool();
    }
}
