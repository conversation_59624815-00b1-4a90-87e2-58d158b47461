package com.coohua.event.risk.main.service.rules;

import com.alibaba.fastjson.JSON;
import com.coohua.event.risk.main.config.RiskReason;
import com.coohua.event.risk.main.entity.EventEntity;
import com.coohua.event.risk.main.service.rules.impl.RiskFilter;
import com.coohua.event.risk.main.utils.EventUtils;
import com.coohua.user.event.biz.click.entity.ProductEntity;
import com.coohua.user.event.biz.service.UserDeviceCheckService;
import com.coohua.user.event.biz.service.UserGrayService;
import com.coohua.user.event.biz.service.bean.UserDeviceBean;
import com.coohua.user.event.biz.service.bean.UserIdBean;
import com.coohua.user.event.biz.util.AppConfig;
import com.coohua.user.event.biz.util.Strings;
import com.pepper.metrics.core.ThreadFactory;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import redis.clients.jedis.Jedis;
import redis.clients.jedis.Pipeline;
import redis.clients.jedis.Response;
import top.zrbcool.pepper.boot.jediscluster.JedisClusterClient;
import top.zrbcool.pepper.boot.jediscluster.JedisClusterClientRefer;

import java.util.*;
import java.util.concurrent.ArrayBlockingQueue;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2023/8/28
 */
@Slf4j
@Service
public class UserDeviceRedisFilter extends RiskFilter {

    @Autowired
    private UserDeviceCheckService userDeviceCheckService;
    @Autowired
    private UserGrayService userGrayService;

    @Override
    public void doProcess(List<EventEntity> eventList, String trace) {
        this.threadPoolExecutor.execute(() ->{
            List<EventEntity> filteredList = eventList.parallelStream()
                    .filter(r -> "exposure".equals(r.getProperties().getAd_action()) ||
                            "login".equals(r.getProperties().getAd_action())
                            || StringUtils.isBlank(r.getProperties().getAd_action()) || "video_cache_finish".equals(r.getProperties().getAd_action()))
                    .filter(r -> !"0".equals(r.getProperties().getUserId()))
                    .collect(Collectors.toList());

            if (filteredList.size() == 0){
                return;
            }
            // 用户-设备关系
            Map<String,UserIdBean> groupUserIdMap = new HashMap<>();
            filteredList.stream().collect(Collectors.groupingBy(r ->
                    r.getProperties().getProduct() + "_" + r.getProperties().getUserId())
            ).forEach((key,list) ->{
                EventEntity eventEntity = list.get(0);
                UserIdBean userIdBean  = new UserIdBean();
                userIdBean.setProduct(eventEntity.getProperties().getProduct());
                userIdBean.setUserId(eventEntity.getProperties().getUserId());
                userIdBean.setDeviceId(list.stream().map(r->r.getProperties().get$device_id())
                        .filter(Strings::noEmpty).collect(Collectors.toSet()));
                userIdBean.setOaid(list.stream().map(r->r.getProperties().getOaid())
                        .filter(Strings::noEmpty).collect(Collectors.toSet()));
                userIdBean.setImei(list.stream().map(r->r.getProperties().getImei())
                        .filter(Strings::noEmpty).collect(Collectors.toSet()));
                userIdBean.setDistinctId(list.stream().map(EventEntity::getDistinct_id)
                        .filter(Strings::noEmpty).collect(Collectors.toSet()));
                userIdBean.setCaid(list.stream().map(r->r.getProperties().getCaid())
                        .filter(Strings::noEmpty).collect(Collectors.toSet()));

                String buildKey = userDeviceCheckService.buildUserIdDeviceKey(userIdBean.getProduct(), userIdBean.getUserId());
                groupUserIdMap.put(buildKey,userIdBean);
            });

            userDeviceCheckService.getAndMergeSave(groupUserIdMap);

            // 设备-用户关系
            Map<String,UserDeviceBean> groupUserDeviceMap = new HashMap<>();
            filteredList.stream()
                    .filter(r -> Strings.noEmpty(r.getProperties().get$device_id()))
                    .filter(r -> !"null".equals(r.getProperties().get$device_id()) && !r.getProperties().get$device_id().startsWith("0000"))
                    .collect(Collectors.groupingBy(r ->
                    r.getProperties().getProduct() + "_" + r.getProperties().get$device_id())
            ).forEach((key,list) ->{
                EventEntity eventEntity = list.get(0);
                UserDeviceBean userDeviceBean  = new UserDeviceBean();
                userDeviceBean.setProduct(eventEntity.getProperties().getProduct());
                userDeviceBean.setDeviceId(eventEntity.getProperties().get$device_id());
                userDeviceBean.setUserId(list.stream().map(r->r.getProperties().getUserId())
                        .filter(Strings::noEmpty)
                        .filter(r -> !"0".equals(r) && !"null".equals(r))
                        .collect(Collectors.toSet()));

                String buildKey = userDeviceCheckService.buildUserIdKey(userDeviceBean.getProduct(), userDeviceBean.getDeviceId());
                groupUserDeviceMap.put(buildKey,userDeviceBean);
            });

            userDeviceCheckService.getAndMergeSaveDevice(groupUserDeviceMap);

            // 检测历史已经拉黑的设备再次处理
            List<String> deviceIdList = filteredList.stream()
                    .filter(r -> !"null".equals(r.getProperties().get$device_id()) && !r.getProperties().get$device_id().startsWith("0000"))
                    .map(r -> r.getProperties().get$device_id())
                    .collect(Collectors.toList());

//            List<String> alreadyGray =  userDeviceCheckService.queryGrayedDevice(deviceIdList);
//            if (alreadyGray.size() > 0){
//                List<EventEntity> needGrayList = new ArrayList<>();
//                filteredList.forEach(eventEntity -> {
//                    if (alreadyGray.contains(eventEntity.getProperties().get$device_id())){
//                        needGrayList.add(eventEntity);
//                    }
//                });
//                Map<String, Set<String>> userMap = EventUtils.getUserIdMap(needGrayList);
//                userMap.forEach((product, sets) -> {
//                    ProductEntity productEntity = AppConfig.productEnMap.get(product);
//                    if (productEntity == null){
//                        return;
//                    }
//                    if (sets != null && sets.size() > 0) {
//                        // 直接拉黑
//                        grayPublic(trace,new ArrayList<>(sets),product,"android",log,"历史设备拉黑-关联userID拉黑",userGrayService);
//                    }
//                });
//            }
        });

        if (this.nextRiskFilter != null){
            this.nextRiskFilter.doProcess(eventList,trace);
        }
    }





    @Override
    public void initReason() {
        this.riskReason = RiskReason.RP;
    }

    @Override
    public void initThreadPool() {
        this.threadPoolExecutor =  new ThreadPoolExecutor(4,8,100, TimeUnit.SECONDS,
                new ArrayBlockingQueue<>(20),new ThreadFactory("UserDeviceRedisFilter-"),new ThreadPoolExecutor.AbortPolicy());
    }

    @Override
    public void afterPropertiesSet() throws Exception {
        this.initReason();
        this.initThreadPool();
    }
}
