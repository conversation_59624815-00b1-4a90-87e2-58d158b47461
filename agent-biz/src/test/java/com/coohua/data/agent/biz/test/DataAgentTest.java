package com.coohua.data.agent.biz.test;

import com.alibaba.fastjson.JSON;
import com.coohua.data.agent.biz.dto.req.CoreEventReq;
import org.junit.Test;

public class DataAgentTest {

    @Test
    public void test() {
        String str = "{\"distinct_id\":\"17282ac2c7117-04adb387ebe06f-50295c19-280800-17282ac2c72113\",\"lib\":{\"$lib\":\"js\",\"$lib_method\":\"code\",\"$lib_version\":\"1.7.14\"},\"properties\":{\"$screen_height\":780,\"$screen_width\":360,\"$lib\":\"js\",\"$lib_version\":\"1.7.14\",\"$latest_referrer\":\"\",\"$latest_traffic_source_type\":\"直接流量\",\"$latest_search_keyword\":\"未取到值_直接打开\",\"$latest_referrer_host\":\"\",\"element_page\":\"转盘\",\"element_name\":\"开始\",\"page_url\":\"https://happygardengame.coohua.com/index.html?deviceId=866629041727891&brand=vivo&appVersion=1.0.1&os=android&channel=ttznhlgyzjsc0201&rom=default&osVersion=9&accessKey=f31745505fd8c30ed8dbe727507d5832_36383698&userId=36383698&env=production&pkg=com.yt.orchard&immersion=1&appId=15&pkgId=41&isPassAndMarket=1&isPass=1&time=1594263084111\",\"userId\":\"36383698\",\"$app_version\":\"1.0.1\",\"$os\":\"android\",\"$device_id\":\"866629041727891\",\"device_id\":\"866629041727891\",\"dp_id\":\"866629041727891\",\"channel\":\"ttznhlgyzjsc0201\",\"product\":\"HappyOrchard\",\"product_part\":\"mvp\",\"$is_first_day\":false},\"type\":\"track\",\"event\":\"WebClick\",\"_nocache\":\"037575549165863\"}";

        CoreEventReq coreEventReq = JSON.parseObject(str, CoreEventReq.class);

        System.out.println(coreEventReq);
    }
}
