package com.coohua.data.agent.biz.db.service;

import com.coohua.data.agent.biz.db.entity.EventTest;
import com.coohua.data.agent.biz.db.entity.Tables;
import com.coohua.data.agent.biz.db.mapper.EventTestMapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.coohua.data.agent.biz.db.mapper.InfomationSchemaMapper;
import com.coohua.data.agent.biz.dto.CoreEventDomain;
import com.coohua.data.agent.biz.dto.req.CoreEventReq;
import com.coohua.data.agent.biz.util.DateUtils;
import lombok.extern.slf4j.Slf4j;
import org.joda.time.DateTimeConstants;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.concurrent.CopyOnWriteArrayList;

/**
 * <p>
 * 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-05-14
 */
@Service
@Slf4j
public class EventTestService extends ServiceImpl<EventTestMapper, EventTest> {
    @Resource(name = "agentDbSavePool")
    ThreadPoolTaskExecutor poolTaskExecutor;
    @Autowired
    EventTestMapper eventTestMapper;

    @Autowired
    InfomationSchemaMapper infomationSchemaMapper;

    private List<CoreEventDomain> dlsit =  new CopyOnWriteArrayList<>();
    public void saveByThread(List<CoreEventDomain> dRequestList) {
//        try {
//            poolTaskExecutor.execute(() -> {
//                try {
//                    dlsit.addAll(dRequestList);
//                    saveEventTest(dlsit);
//                    dlsit.clear();
//                    dlsit = new CopyOnWriteArrayList<>();
//
//                    Date date = new Date();
//                    if(date.getHours()==9 && date.getMinutes()==15){
//                        renameTable();
//                    }
//                }catch (Exception e){
//                    log.error("",e);
//                }
//            });
//        } catch (Exception e) {
//            log.error("", e);
//        }
    }

    public void renameTable() {
        String dateStr = DateUtils.formatDateYMD(new Date());
        List<Tables>  tbs = infomationSchemaMapper.queryTablesByEnd(dateStr);
        if(tbs.size()==0){
            try {
                eventTestMapper.renameTable(dateStr);
                eventTestMapper.createOrderUnionTable();
                log.info("agentck rename " + dateStr + " 成功");
            } catch (Exception e) {
                log.warn("",e);
            }
        }
        try {
            String delDateStr = DateUtils.formatDateYMD(new Date(System.currentTimeMillis() - 2 * DateTimeConstants.MILLIS_PER_DAY));
            List<Tables>  delTabs = infomationSchemaMapper.queryTablesByEnd(delDateStr);
            if(delTabs.size()>0){
                eventTestMapper.truncateOrderUnionTable(delDateStr);
                eventTestMapper.dropOrderUnionTable(delDateStr);
                log.info("agentck 删除表 " + delDateStr + " 成功");
            }
        } catch (Exception e) {
            log.warn("",e);
        }
    }

    public void saveEventTest(List<CoreEventDomain> dRequestList) {
        List<EventTest> dlist = new ArrayList();
        for (CoreEventDomain coreEventDomain : dRequestList) {
            EventTest eventTest = new EventTest();
            eventTest.setLogday(coreEventDomain.getLogday());
            eventTest.setProduct(coreEventDomain.product);

            eventTest.setEvent(coreEventDomain.getEvent());
            eventTest.setHour(coreEventDomain.getHour());
            eventTest.setTime(coreEventDomain.getTime());
            eventTest.setDeviceId(coreEventDomain.getDevice_id());
            eventTest.setUserid(coreEventDomain.getUserid());
            eventTest.setDistinctId(coreEventDomain.getDistinct_id());
            eventTest.setNocache(coreEventDomain.getNocache());
            eventTest.setLib(coreEventDomain.getLib());
            eventTest.setLibMethod(coreEventDomain.getLib_method());
            eventTest.setLibVersion(coreEventDomain.getLib_version());
            eventTest.setAppVersion(coreEventDomain.getApp_version());
            eventTest.setIsFirstDay(coreEventDomain.getIs_first_day());
            eventTest.setLatestReferrer(coreEventDomain.getLatest_referrer());
            eventTest.setLatestReferrerHost(coreEventDomain.getLatest_referrer_host());
            eventTest.setLatestSearchKeyword(coreEventDomain.getLatest_search_keyword());
            eventTest.setLatestTrafficSourceType(coreEventDomain.getLatest_traffic_source_type());
            eventTest.setOs(coreEventDomain.getOs());
            eventTest.setScreenHeight(coreEventDomain.getScreen_height());
            eventTest.setScreenWidth(coreEventDomain.getScreen_width());
            eventTest.setChannel(coreEventDomain.getChannel());
            eventTest.setDpId(coreEventDomain.getDp_id());
            eventTest.setElementName(coreEventDomain.getElement_name());
            eventTest.setElementPage(coreEventDomain.getElement_page());
            eventTest.setElementUri(coreEventDomain.getElement_uri());
            eventTest.setOaid(coreEventDomain.getOaid());
            eventTest.setPageUrl(coreEventDomain.getPage_url());
            eventTest.setProductPart(coreEventDomain.getProduct_part());
            eventTest.setSendTime(coreEventDomain.getSendTime());
            eventTest.setType(coreEventDomain.getType());
            eventTest.setBrand(coreEventDomain.getBrand());
            eventTest.setEventtime(coreEventDomain.getEventtime());
            eventTest.setGps(coreEventDomain.getGps());
            eventTest.setOsVersion(coreEventDomain.getOs_version());
            eventTest.setJstest(coreEventDomain.getJstest());
            eventTest.setAdAction(coreEventDomain.getAd_action());
            eventTest.setAdType(coreEventDomain.getAd_type());
            eventTest.setPageName(coreEventDomain.getPage_name());
            eventTest.setAdId(coreEventDomain.getAd_id());
            eventTest.setManufacturer(coreEventDomain.getManufacturer());
            eventTest.setCarrier(coreEventDomain.getCarrier());
            eventTest.setModel(coreEventDomain.getModel());
            if (coreEventDomain.getMinus() != null) {
                eventTest.setMinus(coreEventDomain.getMinus().intValue());
            } else {
                eventTest.setMinus(0);
            }
            eventTest.setImei(coreEventDomain.getImei());
            eventTest.setAdPage(coreEventDomain.getAd_page());
            eventTest.setTimestampClient(coreEventDomain.getTimestampClient());
            eventTest.setPosId(coreEventDomain.getPos_id());
            eventTest.setExtend1(coreEventDomain.getExtend1());
            eventTest.setExtend2(coreEventDomain.getExtend2());
            eventTest.setExtend3(coreEventDomain.getExtend3());
            dlist.add(eventTest);
        }

        try {
            boolean isSave = saveBatch(dlist);
            if (!isSave) {
                log.error(" dataagent保存数据失败 " + dlist.size());
            } else {
                log.info(" dataagent保存et数据成功 " + dlist.size());

            }
        } catch (Exception e) {
            log.error("", e);
        }
    }
}
