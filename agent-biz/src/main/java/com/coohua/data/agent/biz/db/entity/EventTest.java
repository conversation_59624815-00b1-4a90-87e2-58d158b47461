package com.coohua.data.agent.biz.db.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import java.util.Date;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableField;
import java.io.Serializable;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR>
 * @since 2023-03-14
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value="EventTest对象", description="")
public class EventTest implements Serializable {

    private static final long serialVersionUID = 1L;

    private Date logday;

    private String product;

    private String event;

    private Integer hour;

    private Date time;

    private String deviceId;

    private String userid;

    private String distinctId;

    private String nocache;

    private String lib;

    private String libMethod;

    private String libVersion;

    private String appVersion;

    private Integer isFirstDay;

    private String latestReferrer;

    private String latestReferrerHost;

    private String latestSearchKeyword;

    private String latestTrafficSourceType;

    private String os;

    private Integer screenHeight;

    private Integer screenWidth;

    private String channel;

    private String dpId;

    private String elementName;

    private String elementPage;

    private String elementUri;

    private String oaid;

    private String pageUrl;

    private String productPart;

    @TableField("sendTime")
    private Long sendTime;

    private String type;

    private String brand;

    private Long eventtime;

    private String gps;

    private String osVersion;

    private String jstest;

    private Integer minus;

    private String adType;

    private String adAction;

    private String pageName;

    private String adId;

    private String manufacturer;

    private String carrier;

    private String model;

    private Integer idDel;

    private String imei;

    private String adPage;

    @TableField("timestampClient")
    private Long timestampClient;

    private String posId;

    private String extend1;

    private String extend2;

    private String extend3;


}
