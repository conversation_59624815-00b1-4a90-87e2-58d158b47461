package com.coohua.data.agent.biz.db;

import com.coohua.data.agent.biz.db.entity.DataCheckAtomicLong;
import com.google.common.cache.Cache;
import com.google.common.cache.CacheBuilder;
import org.springframework.context.annotation.Configuration;

import java.util.concurrent.TimeUnit;

@Configuration
public class CacheConfig {
    public static Cache<String,DataCheckAtomicLong> checkDataCache = CacheBuilder.newBuilder()
            .expireAfterWrite(3, TimeUnit.HOURS)
            .maximumSize(50000)
            .build();
}
