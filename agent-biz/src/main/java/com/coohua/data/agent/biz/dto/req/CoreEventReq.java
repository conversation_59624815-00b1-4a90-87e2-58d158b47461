/**
  * Copyright 2020 bejson.com 
  */
package com.coohua.data.agent.biz.dto.req;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.serializer.SerializerFeature;
import lombok.Data;

import java.util.Date;

@Data
public class CoreEventReq {
    private String project;
    private String distinct_id;
    private EventLib lib;
    private EventProperties properties;
    private String type;
    private String event;
    private String _nocache;

    private Date sendTime;
    private String jstest;
    private Long serverTime;
    public static void main(String[] args) {
        String str = "{\"distinct_id\":\"1724ed36c49d9-0db6e311bb565d-1529551e-331776-1724ed36c4a3d\",\"lib\":{\"$lib\":\"js\",\"$lib_method\":\"code\",\"$lib_version\":\"1.7.14\"},\"properties\":{\"$screen_height\":768,\"$screen_width\":432,\"$lib\":\"js\",\"$lib_version\":\"1.7.14\",\"$latest_referrer\":\"\",\"$latest_traffic_source_type\":\"直接流量\",\"$latest_search_keyword\":\"未取到值_直接打开\",\"$latest_referrer_host\":\"\",\"element_page\":\"首页\",\"element_name\":\"水壶水滴足够\",\"page_url\":\"https://duofarmgame.coohua.com/index.html?oaid=&deviceId=866960029251756&brand=HUAWEI&appVersion=1.0.8&os=android&channel=ALIYUN_MAN_CHANNEL&rom=default&osVersion=5.1.1&accessKey=fd50688a1f7ee4cc71a61179086a39b2_34379564&userId=34379564&env=production&pkg=com.yaotian.ddnc&immersion=1&appId=14&pkgId=40&isPass=1&time=1594348112901\",\"userId\":\"34379564\",\"$app_version\":\"1.0.8\",\"$os\":\"android\",\"$device_id\":\"866960029251756\",\"device_id\":\"866960029251756\",\"oaid\":\"\",\"channel\":\"ALIYUN_MAN_CHANNEL\",\"element_uri\":15,\"product\":\"duoduonongchang\",\"$is_first_day\":false},\"type\":\"track\",\"event\":\"WebClick\",\"_nocache\":\"025215884357449\"}";
        System.out.println(str);
        CoreEventReq core = JSON.parseObject(str, CoreEventReq.class);
        System.out.println(core.toString());

        System.out.println(JSON.toJSONString(core, SerializerFeature.PrettyFormat));
    }

}