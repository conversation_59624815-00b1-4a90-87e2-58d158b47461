package com.coohua.data.agent.biz.dto;

import com.coohua.data.agent.biz.dto.req.CoreEventReq;
import com.coohua.data.agent.biz.dto.req.EventProperties;
import com.coohua.data.agent.biz.util.UrlUtil;
import lombok.Data;
import lombok.ToString;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.apache.logging.log4j.util.Strings;
import org.joda.time.DateTime;

import java.math.BigInteger;
import java.sql.Date;
import java.sql.Timestamp;
import java.util.HashMap;
import java.util.Map;
import java.util.Optional;
import java.util.regex.Pattern;

@Data
@ToString
@Slf4j
public class CoreEventDomain {
    public Date logday;
    public String product;
    private String event;
    private int hour;
    private Timestamp time;
    public String device_id;
    public String userid;
    private String distinct_id;
    private String nocache;
    public String lib;
    public String lib_method;
    public String lib_version;
    public String app_version;
    public int is_first_day;
    public String latest_referrer;
    public String latest_referrer_host;
    public String latest_search_keyword;
    public String latest_traffic_source_type;
    public String os;
    public int screen_height;
    public int screen_width;
    public String channel;
    public String dp_id;
    public String imei;
    public String element_name;
    public String element_page;
    public String element_uri;
    public String oaid;
    public String page_url;
    public String product_part;
    private long sendTime;
    private String type;
    private String brand;
    private long eventtime;
    private String gps;
    private String os_version;
    private String jstest;
    private BigInteger minus;
    private String ad_action;
    private String ad_type;
    private String page_name;
    private String ad_id;
    private String manufacturer;
	private String carrier;
	private String model;
	private int id_del;
	private String ad_page;
	private long timestampClient;
	private String pos_id;
	private Integer strategyId;
	private String sdkVersion;
	private String ip;
	private String extend1;
	private String extend2;
	private String extend3;
	private String extend4;
	private String extend5;
	private String extend6;
	private String extend7;
	private String extend8;
	private String caid;
	private String android_id;
	private String mac;
    private int device_type;
	public static	Pattern pattern = Pattern.compile("^-?\\d+(\\.\\d+)?$");//这个也行

	public CoreEventDomain fromCoreEventReq(CoreEventReq coreEventReq
            ,Map<String, Map<Integer,Integer>> deviceOldCheck
            ,Map<String, Map<Integer,Integer>> userOldCheck
            ,boolean isTest) {
        //兼容生产者
        if(null ==deviceOldCheck ){
            deviceOldCheck = new HashMap<>();
        }
        if(null ==userOldCheck ){
            userOldCheck = new HashMap<>();
        }
        EventProperties properties = coreEventReq.getProperties();
	    String minus = properties.getMinus();
        try {
            if (StringUtils.isNotBlank(minus)) {
                if(!minus.contains("-") && !minus.contains(".") && minus.matches("\\d*")) {
                    this.minus = new BigInteger(properties.getMinus());//2020-08
                    if(this.minus.longValue()>100000000){
                        this.minus = new BigInteger("0");
                    }
                }//For more information
            }
        }catch (Exception e){
            log.warn(properties.getMinus()+"  Long error ",e);
        }
        this.ad_id = StringUtils.trimToEmpty(properties.getAd_id());
        this.product = StringUtils.trimToEmpty(coreEventReq.getProperties().getProduct());
        this.event = StringUtils.trimToEmpty(coreEventReq.getEvent());
        this.nocache = StringUtils.trimToEmpty(coreEventReq.get_nocache());
        this.lib = StringUtils.trimToEmpty(coreEventReq.getLib().$lib);
        this.lib_method = StringUtils.trimToEmpty(coreEventReq.getLib().$lib_method);
        this.lib_version = StringUtils.trimToEmpty(coreEventReq.getLib().$lib_version);
        //必有参数
        this.product_part = StringUtils.trimToEmpty(coreEventReq.getProject());
        this.manufacturer=StringUtils.trimToEmpty(properties.$manufacturer);
		this.carrier=StringUtils.trimToEmpty(properties.$carrier);
		this.model=StringUtils.trimToEmpty(properties.$model);
        this.channel = StringUtils.trimToEmpty(properties.channel);
        //以下为 MessageFormatParse#parseOdsMap方法逻辑 迁移
        //处理distinct_id
        this.distinct_id = Optional.ofNullable(StringUtils.trimToEmpty(coreEventReq.getDistinct_id())).orElse(Strings.EMPTY);
        //处理userId
        String userId = properties.userId;
        //如果userid为空，对于userid这个字段赋值为”null“
        if (userId == null && (distinct_id.contains("-") || distinct_id.matches(".*[a-zA-z].*"))) {
            this.userid = "null";
        }
//        else if (userId == null) {
//            this.userid = distinct_id;
//        }
        else {
            this.userid = userId;
        }
//        if (null == userId) {
//            this.userid = distinct_id;
//        }
        //以下为 deviceId逻辑

        String os = properties.$os;
        String os_version = Optional.ofNullable(properties.$os_version).orElseGet(() -> "0");
        String osVersion = os_version.split("\\.")[0];
        int parseOsVersion = parseInt(osVersion, 0);
        String oaid = properties.getOaid();
        //服务端传imei时，将imei带上了$符号
        String imei = Optional.ofNullable(properties.imei).orElseGet(() -> properties.$imei);
        String device_id = properties.$device_id;
        String android_id = StringUtils.trimToEmpty(properties.android_id);

        if (device_id == null || "".equals(device_id)) {
            device_id = "null";
        }
        this.device_type = 0;
        if ("Android".equalsIgnoreCase(os)) {
            if (StringUtils.isEmpty(imei)) {
                imei = "null";
            }
            this.imei = imei;
            if (parseOsVersion >= 10) {
                this.device_type = 13;
                this.device_id = oaid;
                this.dp_id = oaid;
            } else {
                this.device_type = 1;
                this.device_id = imei;
                this.dp_id = imei;
            }

            try{
                //if (isTestProduct(this.product) || isTest){
                    //新老用户判断
                    /*
                    * 1、device_id 是有效数据时（不为空也不为null字符）判断是否为老设备
                    *      或device_id 为空或是null
                    * 2、
                    *
                    *
                    * */
                    this.device_type = 100;
                    if( isNewDevice(this.device_id,os,this.product,deviceOldCheck)
                            && isNewUser(this.userid,os,this.product,userOldCheck)){

                        if(isNullDevice(this.device_id)){
                            if (!StringUtils.isEmpty(oaid) && !"null".equalsIgnoreCase(oaid) ){
                                this.device_id = oaid;
                                this.device_type = 23;
                            }else if(!StringUtils.isEmpty(imei) && !"null".equalsIgnoreCase(imei)){
                                this.device_id = imei;
                                this.device_type = 24;
                            }else if(!StringUtils.isEmpty(android_id) && !"null".equalsIgnoreCase(android_id)){
                                this.device_id = android_id;
                                this.device_type = 25;
                            }else{
                                this.device_type = 26;
                            }
                        }else{
                            if(this.device_id.equals(oaid)){
                                this.device_type = 12;
                            } else if (this.device_id.equals(imei)){
                                this.device_type = 11;
                                this.device_id = oaid;
                                this.dp_id = oaid;
                            }
                        }

                    }
                //}

            }catch (Exception e){
                log.error("fromCoreEventReq-校验新设备有误:"+e.getMessage());
            }

        } else if ("IOS".equalsIgnoreCase(os) && !StringUtils.equalsIgnoreCase("MiniGame",properties.$lib)) {
            this.device_id = device_id;
            this.dp_id = device_id;
            // 使用 distinct_id 来填充
            if ("0".equals(this.device_id)){
                this.device_id = this.distinct_id;
            }
            // 苹果没有渠道
            this.channel = "AppStore";
        }else if("wmin".equalsIgnoreCase(os) || "byte".equalsIgnoreCase(os)){
            this.device_id = device_id;
        } else {
            this.device_id = "null";
            this.dp_id = properties.userId;
        }
        this.os_version = properties.$os_version;
        this.os = StringUtils.trimToEmpty(properties.$os);
        this.app_version = StringUtils.trimToEmpty(properties.$app_version);
        this.is_first_day = properties.$is_first_day ? 0 : 1;
        this.latest_referrer = StringUtils.trimToEmpty(properties.$latest_referrer);
        this.latest_referrer_host = StringUtils.trimToEmpty(properties.$latest_referrer_host);
        this.latest_search_keyword = StringUtils.trimToEmpty(properties.$latest_search_keyword);
        this.latest_traffic_source_type = StringUtils.trimToEmpty(properties.$latest_traffic_source_type);
        this.screen_height = properties.$screen_height;
        this.screen_width = properties.$screen_width;
        this.element_name = StringUtils.trimToEmpty(properties.element_name);
        this.element_page = StringUtils.trimToEmpty(properties.element_page);
        this.element_uri = StringUtils.trimToEmpty(properties.element_uri);
        this.oaid = StringUtils.trimToEmpty(properties.oaid);
        this.sendTime = coreEventReq.getSendTime().getTime();
        this.type = StringUtils.trimToEmpty(coreEventReq.getType());

        this.page_url = StringUtils.trimToEmpty(properties.page_url);
        this.jstest = StringUtils.trimToEmpty(coreEventReq.getJstest());

        this.ad_action = StringUtils.trimToEmpty(properties.ad_action);
        this.ad_type = StringUtils.trimToEmpty(properties.ad_type);
        this.pos_id = StringUtils.trimToEmpty(properties.pos_id);
        try {
            if (NumberUtils.isCreatable(properties.strategyId)){
                this.strategyId = Integer.valueOf(properties.strategyId);
            }else {
                this.strategyId = 0;
            }
        }catch (Exception e){
            log.error("Format StrategyId ex:",e);
        }
        this.sdkVersion = Optional.ofNullable(properties.sdkVersion).orElse("");
        this.ip = StringUtils.trimToEmpty(properties.ip);
        this.extend1 = StringUtils.trimToEmpty(properties.extend1);
        this.extend2 = StringUtils.trimToEmpty(properties.extend2);
        this.extend3 = StringUtils.trimToEmpty(properties.extend3);
        this.extend4 = StringUtils.trimToEmpty(properties.extend4);
        this.extend5 = StringUtils.trimToEmpty(properties.extend5);
        this.extend6 = StringUtils.trimToEmpty(properties.extend6);
        this.extend7 = StringUtils.trimToEmpty(properties.extend7);
        this.extend8 = StringUtils.trimToEmpty(properties.extend8);
        this.caid = StringUtils.trimToEmpty(properties.caid);
        this.android_id = StringUtils.trimToEmpty(properties.android_id);
        this.mac = StringUtils.trimToEmpty(properties.mac);
        this.ad_page = StringUtils.trimToEmpty(properties.ad_page);
        this.page_name = StringUtils.trimToEmpty(properties.page_name);
        Map<String, String> argsToMap = UrlUtil.urlArgsToMap(properties.page_url);
        if(StringUtils.isBlank(this.device_id) || "null".equalsIgnoreCase(this.device_id)){
            this.device_id = argsToMap.get("deviceId");
        }

        long unixTime = sendTime;
        String time = argsToMap.get("time");
        long unixEventTime = sendTime;
        if (StringUtils.isNotEmpty(time)) {
            try {
                unixEventTime = Long.parseLong(time);
            } catch (Exception e) {
                unixEventTime = coreEventReq.getSendTime().getTime();
            }
        }

        DateTime dateTime = new DateTime(unixTime);
        this.hour = dateTime.getHourOfDay();
        this.eventtime = unixEventTime / 1000;
        this.time = new Timestamp(unixTime);
        this.logday = new Date(unixTime);

        this.brand = argsToMap.getOrDefault("brand", "");
        this.gps = argsToMap.getOrDefault("gps", "");

        if (StringUtils.isNotEmpty(properties.timestampClient)) {
            try {
                this.timestampClient = Long.parseLong(properties.timestampClient);
            } catch (Exception e) {
            }
        }
        return this;
    }

    public static int parseInt(String str, int defaultValue) {
        if (null == str) {
            return defaultValue;
        }
        try {
            return Integer.parseInt(str);
        } catch (Exception x) {
            return defaultValue;
        }
    }

    public boolean isTestProduct(String product){
        boolean isTestProduct = false;
        if("wyfc".equalsIgnoreCase(product) || "dfdg".equalsIgnoreCase(product)
                || "qzhcly".equalsIgnoreCase(product)) {
            isTestProduct =  true;
        }
        return isTestProduct;
    }

    public boolean isNewDevice(String device_id, String os, String product
            , Map<String, Map<Integer,Integer>> deviceOldCheck){
       boolean isNewDevice = false;
       if(StringUtils.isBlank(device_id) || "null".equals(device_id)
               || !deviceOldCheck.containsKey(product+"_"+os)
               || (deviceOldCheck.containsKey(product+"_"+os)  && !deviceOldCheck.get(product+"_"+os).containsKey(device_id.hashCode()))
       ){
           isNewDevice =  true;
       }
        return isNewDevice;
    }

    public boolean isNewUser(String userid, String os, String product
            , Map<String, Map<Integer,Integer>> userOldCheck){
        boolean isNewUser = false;
        if(StringUtils.isEmpty(userid)
          || "null".equals(userid)
          || "0".equals(userid)
          || !userOldCheck.containsKey(product+"_"+os)
          || (userOldCheck.containsKey(product+"_"+os) &&!userOldCheck.get(product+"_"+os).containsKey(userid.hashCode()))
        ){
            isNewUser =  true;
        }

        return isNewUser;
    }

    public  boolean isNullDevice(String deviceId){
        boolean isNullDevice = false;
        if(StringUtils.isEmpty(deviceId) || "null".equalsIgnoreCase(deviceId)){
            isNullDevice =  true;
        }

        return isNullDevice;
    }

    public static void main(String[] args){
	    String deviceId = "wyfc";
        //System.out.println(isTestProductCheck(deviceId));
    }
}
