package com.coohua.data.agent.biz.db.service;

import com.coohua.data.agent.biz.db.CacheConfig;
import com.coohua.data.agent.biz.db.entity.DataCheck;
import com.coohua.data.agent.biz.db.entity.DataCheckAtomicLong;
import com.coohua.data.agent.biz.db.mapper.DataCheckMapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.coohua.data.agent.biz.dto.CoreEventDomain;
import com.coohua.data.agent.biz.dto.req.CoreEventReq;
import com.ctrip.framework.apollo.spring.annotation.ApolloJsonValue;
import com.google.common.cache.CacheBuilder;
import com.google.common.cache.LoadingCache;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Bean;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.print.attribute.HashAttributeSet;
import java.util.Date;
import java.util.HashSet;
import java.util.List;
import java.util.Set;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicLong;

/**
 * <p>
 * 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-05-14
 */
@Service
@Slf4j
public class DataCheckService extends ServiceImpl<DataCheckMapper, DataCheck> {
    public String getEventKey(CoreEventDomain coreEventDomain) {
        String logstr = coreEventDomain.getLogday() + coreEventDomain.getProduct() + coreEventDomain.getEvent() + coreEventDomain.getHour();
        return logstr;
    }

    public String getEventKey(DataCheckAtomicLong dataCheckAtomicLong) {
        String logstr = dataCheckAtomicLong.getLogday() + dataCheckAtomicLong.getProduct() + dataCheckAtomicLong.getEvent() + dataCheckAtomicLong.getHour();
        return logstr;
    }


    //(event in ('AdData','AppNewsView','AppPageView','AppShare','Startup')
    //        or (event='AppClick' and element_page not in ('通知栏', 'AppPush')))
    private static Set<String> eventSet = new HashSet<>();

    static {
        eventSet.add("AdData");
        eventSet.add("AppNewsView");
        eventSet.add("AppPageView");
        eventSet.add("AppShare");
        eventSet.add("Startup");
        eventSet.add("AppClick");
    }
    @Autowired
    DataCheckMapper dataCheckMapper;

    @Transactional
    public void saveOrUpdateCheck(DataCheckAtomicLong dataCheckAtomicLong) {
        String no = getEventKey(dataCheckAtomicLong);
        DataCheck dataCheck = this.getById(no);
        if(dataCheck==null){

            dataCheck  = new DataCheck();
            dataCheck.setExposureNum(dataCheckAtomicLong.getExposureNum().get());
            dataCheck.setEventNum(dataCheckAtomicLong.getEventNum().get());
            dataCheck.setEvent(dataCheckAtomicLong.getEvent());
            dataCheck.setUpdateTime(dataCheckAtomicLong.getUpdateTime());
            dataCheck.setAdAction(dataCheckAtomicLong.getAdAction());
            dataCheck.setCheckNo(no);
            dataCheck.setCreateTime(dataCheckAtomicLong.getCreateTime());
            dataCheck.setHour(dataCheckAtomicLong.getHour());
            dataCheck.setLogday(dataCheckAtomicLong.getLogday());
            dataCheck.setProduct(dataCheckAtomicLong.getProduct());
            save(dataCheck);

        }else{
            long eventNum = dataCheckAtomicLong.getEventNum().get();
            long exposureNum = dataCheckAtomicLong.getExposureNum().get();
            if(eventNum>0 || exposureNum>0){
                if(StringUtils.isNotBlank(no)){
                    dataCheckMapper.lockCheck(no);
                    int cnum = dataCheckMapper.updateDataCheck(no,dataCheckAtomicLong.getEventNum().getAndSet(0),dataCheckAtomicLong.getExposureNum().getAndSet(0));
                    if(cnum==1){
//                    if(eventNum>0){
//                        dataCheckAtomicLong.getEventNum().addAndGet(-eventNum);
//                    }
//                    if(exposureNum>0){
//                        dataCheckAtomicLong.getExposureNum().addAndGet(-exposureNum);
//                    }
                    }
                }
            }
        }
    }
    @ApolloJsonValue("${enable.exposure.set:[\"qijicanting\",\"tianmihuayuan\",\"wodecanting\"]}")
    private Set<String> dset;

    public void updateCounter(CoreEventReq coreEventReq) {
        try {
            CoreEventDomain coreEventDomain = new CoreEventDomain();
            if(coreEventReq.getSendTime()==null){
                coreEventReq.setSendTime(new Date());
            }
            coreEventDomain = coreEventDomain.fromCoreEventReq(coreEventReq,null,null,false);
            if((dset.contains(coreEventDomain.getProduct())) &&
                    StringUtils.isNotBlank(coreEventDomain.getAd_action()) &&
                    "exposure".equalsIgnoreCase(coreEventDomain.getAd_action())
            ){
                log.info(coreEventDomain.getProduct()+" exposure "+coreEventDomain.getImei()+" "+coreEventDomain.getProduct()+coreEventDomain.getAd_id()+" "+coreEventDomain.getApp_version());
            }
            getByCache(coreEventDomain);
        } catch (Exception e) {
            log.error("", e);
        }
    }

    public DataCheckAtomicLong getByCache(CoreEventDomain coreEventDomain) throws ExecutionException {
        boolean isCheck = false;
        if (eventSet.contains(coreEventDomain.getEvent())) {
            if ("AppClick".equals(coreEventDomain.getEvent())) {
                if (!"通知栏".equals(coreEventDomain.getElement_page()) && !"AppPush".equals(coreEventDomain.getElement_page())) {
                    isCheck = true;
                }
            } else {
                isCheck = true;
            }
        }

        if (!isCheck) {
            return null;
        }
        String ekey = getEventKey(coreEventDomain);
        DataCheckAtomicLong dataCheckAtomicLong = CacheConfig.checkDataCache.getIfPresent(ekey);

        if (dataCheckAtomicLong == null) {
            Date date = new Date();
            dataCheckAtomicLong = new DataCheckAtomicLong();
            dataCheckAtomicLong.setAdAction(coreEventDomain.getAd_action());
            dataCheckAtomicLong.setCreateTime(new Date(coreEventDomain.getSendTime()));
            dataCheckAtomicLong.setEvent(coreEventDomain.getEvent());
            dataCheckAtomicLong.setEventNum(new AtomicLong(0));
            dataCheckAtomicLong.setExposureNum(new AtomicLong(0));
            dataCheckAtomicLong.setHour(coreEventDomain.getHour());
            dataCheckAtomicLong.setLogday(coreEventDomain.getLogday());
            dataCheckAtomicLong.setProduct(coreEventDomain.getProduct());
            dataCheckAtomicLong.setUpdateTime(date);
            if(CacheConfig.checkDataCache.getIfPresent(ekey)==null){
                CacheConfig.checkDataCache.put(ekey, dataCheckAtomicLong);
            }
        }

        if ("AdData".equalsIgnoreCase(coreEventDomain.getEvent()) && "exposure".equalsIgnoreCase(coreEventDomain.getAd_action())) {
            dataCheckAtomicLong.getExposureNum().getAndIncrement();
        }

        long eventNum = dataCheckAtomicLong.getEventNum().getAndIncrement();
        return dataCheckAtomicLong;
    }

}
