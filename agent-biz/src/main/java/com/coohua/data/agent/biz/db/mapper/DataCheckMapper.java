package com.coohua.data.agent.biz.db.mapper;

import com.coohua.data.agent.biz.db.entity.DataCheck;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Update;

/**
 * <p>
 *  Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2021-05-14
 */
public interface DataCheckMapper extends BaseMapper<DataCheck> {
    @Update("update data_check set exposure_num=exposure_num+${exposureNum},event_num=event_num+${eventNum} where check_no='${checkNo}'")
    int updateDataCheck(
            @Param("checkNo") String checkNo,
            @Param("eventNum") long eventNum,
            @Param("exposureNum") long exposureNum
    );

    @Update("select * from  data_check where check_no='${checkNo}' for update ")
    void lockCheck(
            @Param("checkNo") String checkNo
    );
}
