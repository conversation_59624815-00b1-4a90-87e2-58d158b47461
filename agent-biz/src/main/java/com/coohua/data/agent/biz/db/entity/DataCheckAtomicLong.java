package com.coohua.data.agent.biz.db.entity;

import lombok.Data;

import java.util.Date;
import java.util.concurrent.atomic.AtomicLong;
@Data
public class DataCheckAtomicLong {
    private static final long serialVersionUID = 1L;

    private Date logday;

    private String product;

    private String event;

    private String adAction;

    private Integer hour;

    private AtomicLong exposureNum;

    private AtomicLong eventNum;

    private Date createTime;

    private Date updateTime;

    private Date saveIime;
}
