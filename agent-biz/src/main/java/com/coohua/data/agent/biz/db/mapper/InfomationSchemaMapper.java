package com.coohua.data.agent.biz.db.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.coohua.data.agent.biz.db.entity.Tables;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

public interface InfomationSchemaMapper  extends BaseMapper {
    @Select("select * from `information_schema`.tables where table_name ='event_test_${tableEnd}'")
    List<Tables> queryTablesByEnd(@Param("tableEnd") String tableEnd);

    @Select("select * from `information_schema`.tables where table_name like 'event_test_%'")
    List<Tables> queryAllClickTables();
}
