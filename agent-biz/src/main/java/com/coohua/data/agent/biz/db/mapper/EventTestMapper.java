package com.coohua.data.agent.biz.db.mapper;

import com.coohua.data.agent.biz.db.entity.EventTest;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Update;

/**
 * <p>
 *  Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2021-05-14
 */
public interface EventTestMapper extends BaseMapper<EventTest> {
    @Update("create table if not exists  `event_test` like event_test_tmp")
    int createOrderUnionTable();
    @Update("rename table  event_test to event_test_${tableEnd}")
    void renameTable(@Param("tableEnd") String tableEnd);

    @Update("truncate table  event_test_${tableEnd}")
    void truncateOrderUnionTable(@Param("tableEnd") String tableEnd);
    @Update("drop table  event_test_${tableEnd}")
    void dropOrderUnionTable(@Param("tableEnd") String tableEnd);
}
