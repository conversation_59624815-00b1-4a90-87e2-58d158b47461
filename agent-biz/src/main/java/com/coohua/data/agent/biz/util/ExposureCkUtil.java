package com.coohua.data.agent.biz.util;

public class ExposureCkUtil {
    public static String getAbc(Long ctime){
        return "abcadfsfewr@"+ctime;
    }
    public static String getM(Long ctime,String ks,Double ecpm,String hid){
        String m5 = MD5Utils.getMd5Sum(ctime+ks+ecpm+hid);
        return m5;
    }

    public static void main(String[] args){
        String a1 = getAbc(1694571313288l);
        String eq = getM(1694571313288l,a1,58.4d,"157750669459134216945713110112");

        System.out.println(eq);
    }
}