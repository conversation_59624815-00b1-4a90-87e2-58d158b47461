package com.coohua.data.agent.biz.util;

import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON> on 2016/10/24.
 */
public class MD5Utils {

    private static final char[] DIGITS_LOWER =
            {'0', '1', '2', '3', '4', '5', '6', '7', '8', '9', 'a', 'b', 'c', 'd', 'e', 'f'};

    public static String getMd5Sum(String input) {
        try {
            MessageDigest md = MessageDigest.getInstance("MD5");
            md.reset();
            byte[] buf = input.getBytes();
            md.update(buf);
            byte[] digest = md.digest();
            String result = new String(encodeHex(digest));
            md.reset();
            return result;
        } catch (NoSuchAlgorithmException e) {
            return "";
        }
    }

    protected static char[] encodeHex(final byte[] data) {
        final int l = data.length;
        final char[] out = new char[l << 1];
        // two characters form the hex value.
        for (int i = 0, j = 0; i < l; i++) {
            out[j++] = MD5Utils.DIGITS_LOWER[(0xF0 & data[i]) >>> 4];
            out[j++] = MD5Utils.DIGITS_LOWER[0x0F & data[i]];
        }
        return out;
    }
}
