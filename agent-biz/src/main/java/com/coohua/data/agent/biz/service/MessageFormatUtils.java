package com.coohua.data.agent.biz.service;

import org.apache.commons.codec.binary.Base64InputStream;
import org.apache.commons.io.IOUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.ByteArrayInputStream;
import java.util.zip.GZIPInputStream;
import java.util.zip.ZipException;

/**
 * 解析原始信息，原始信息在nginx+lua入到kafka时，做了相关的压缩和base64，需要根据规则解析出来
 * 目前规则信息如下：
 * 1. nginx收到的由客户端或服务端上报过来的数据，都做了base64编码，但是不一定做压缩编码。 所以数据为 [gzip|not gzip] + base64
 * 2. 收到nginx数据后，lua 做了将上报数据拼接上头信息，然后整体做base64。规则为： base64(headersize+header+上报数据)
 * <p>
 * 所以数据最后的格式为 ： base64(headersize+header+base64([gizp|not gzip]))，解码的时候从外层到里层逐步解析就可以了
 * （panchao desc）
 */
public class MessageFormatUtils {

    private static final Logger logger = LoggerFactory.getLogger("stan");

    public static void main(String[] args){
        String formatMessage = formatMessage("eyJkaXN0aW5jdF9pZCI6IjE3MmNlZDZkNDdlMTUxLTBkZjVmMWI5ZTFmZDk2LTYyMmU2ZTYyLTI4MDgwMC0xNzJjZWQ2ZDQ3ZjE4ZiIsImxpYiI6eyIkbGliIjoianMiLCIkbGliX21ldGhvZCI6ImNvZGUiLCIkbGliX3ZlcnNpb24iOiIxLjcuMTQifSwicHJvcGVydGllcyI6eyIkc2NyZWVuX2hlaWdodCI6NzgwLCIkc2NyZWVuX3dpZHRoIjozNjAsIiRsaWIiOiJqcyIsIiRsaWJfdmVyc2lvbiI6IjEuNy4xNCIsIiRsYXRlc3RfcmVmZXJyZXIiOiIiLCIkbGF0ZXN0X3RyYWZmaWNfc291cmNlX3R5cGUiOiLnm7TmjqXmtYHph48iLCIkbGF0ZXN0X3NlYXJjaF9rZXl3b3JkIjoi5pyq5Y+W5Yiw5YC8X+ebtOaOpeaJk+W8gCIsIiRsYXRlc3RfcmVmZXJyZXJfaG9zdCI6IiIsImVsZW1lbnRfcGFnZSI6IummlumhtSIsImVsZW1lbnRfbmFtZSI6Iua1h+awtOe6ouWMhSIsInBhZ2VfdXJsIjoiaHR0cHM6Ly9oYXBweWdhcmRlbmdhbWUuY29vaHVhLmNvbS9pbmRleC5odG1sP2RldmljZUlkPTg2NTMxNjA0Mjg4NTAyNCZicmFuZD1IVUFXRUkmYXBwVmVyc2lvbj0xLjAuMyZvcz1hbmRyb2lkJmNoYW5uZWw9a3NobGd5empzYzAxMDImcm9tPWRlZmF1bHQmb3NWZXJzaW9uPTkmYWNjZXNzS2V5PTQ4YjFiNDhiMGVhOTc0MDE1N2IwY2NiYzk4YmM1MWMwXzIwMDU0ODcxJnVzZXJJZD0yMDA1NDg3MSZlbnY9cHJvZHVjdGlvbiZwa2c9Y29tLnl0Lm9yY2hhcmQmb2FpZD05YjI3ZmZlMy1lZmY3LTBiMzMtY2JkNS1lZmZmNzNiZjQwMWImaW1tZXJzaW9uPTEmYXBwSWQ9MTUmcGtnSWQ9NDEmZ3BzPTM3LjAzOTIyMSwxMjEuNjg0ODUxJmlzUGFzc0FuZE1hcmtldD0xJmlzUGFzcz0xJnRpbWU9MTU5NDEyOTYwNDc1NCIsInVzZXJJZCI6IjIwMDU0ODcxIiwiJGFwcF92ZXJzaW9uIjoiMS4wLjMiLCIkb3MiOiJhbmRyb2lkIiwiJGRldmljZV9pZCI6Ijg2NTMxNjA0Mjg4NTAyNCIsImRldmljZV9pZCI6Ijg2NTMxNjA0Mjg4NTAyNCIsImRwX2lkIjoiODY1MzE2MDQyODg1MDI0Iiwib2FpZCI6IjliMjdmZmUzLWVmZjctMGIzMy1jYmQ1LWVmZmY3M2JmNDAxYiIsImNoYW5uZWwiOiJrc2hsZ3l6anNjMDEwMiIsImVsZW1lbnRfdXJpIjoxOCwicHJvZHVjdCI6IkhhcHB5T3JjaGFyZCIsInByb2R1Y3RfcGFydCI6Im12cCIsIiRpc19maXJzdF9kYXkiOmZhbHNlfSwidHlwZSI6InRyYWNrIiwiZXZlbnQiOiJXZWJDbGljayIsIl9ub2NhY2hlIjoiMDM2NDUxMTYwNzM0MDI2In0= HTTP/1.1");
        System.out.println(formatMessage);
    }

    /**
     * 将message字符串封装成message对象
     *
     * @param msgOrigin
     * @return
     */
    public static String formatMessage(String msgOrigin) {

        GZIPInputStream unzipInput = null;
        Base64InputStream originBase64Input = null;
        try {
            ByteArrayInputStream originByteInput = new ByteArrayInputStream(msgOrigin.getBytes());
            originBase64Input = new Base64InputStream(originByteInput);
            //对原始message进行解码

            byte[] originByte = IOUtils.toByteArray(originBase64Input);
            //原始messge
            String originStr = new String(originByte);

            //已解压 ~~~~~
            return originStr;
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
        } finally {
            IOUtils.closeQuietly(originBase64Input);
        }
        return null;
    }

}
