package com.coohua.data.agent.biz.util;

import java.text.SimpleDateFormat;
import java.util.Date;

public class DateUtils {
    public static final String PATTERN_YMD = "yyyy-MM-dd";
    public static final String YYMMDD = "yyyyMMdd";
    public static String formatDateForYMD(Date date) {
        if (date == null) {
            throw new IllegalArgumentException("date is null");
        }

        return new SimpleDateFormat(PATTERN_YMD).format(date);
    }
    public static String formatDateYMD(Date date) {
        if (date == null) {
            throw new IllegalArgumentException("date is null");
        }

        return new SimpleDateFormat(YYMMDD).format(date);
    }
    public static Date parseDate(String dateValue) {
        return parseDate(dateValue);
    }

}
