package com.coohua.data.agent.biz.util;

import org.apache.commons.lang3.ArrayUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.HashMap;
import java.util.Map;

public class UrlUtil {

    public static String filterUrlArgs(String url) {
        return StringUtils.substringBefore(url, "?");
    }

    public static Map<String, String> urlArgsToMap(String url) {
        if (StringUtils.isEmpty(url)) return new HashMap<>();
        String[] split = StringUtils.substringAfter(url, "?").split("&");
        Map<String, String> params = new HashMap<>();
        if (ArrayUtils.isNotEmpty(split)) {
            for (String one : split) {
                String[] spl = StringUtils.split(one, '=');
                if (spl != null && spl.length == 2) {
                    params.put(spl[0], spl[1]);
                }
            }
        }
        return params;
    }

    public static void main(String[] args) {
        String s ="//https://duofuguoyuangame.coohua.com/index.html?mac=D6:9A:A0:14:C0:AE&oaid=539806c223160e93&deviceId=26df04924c6d72db&brand=Xiaomi&appVersion=1.0.1&os=android&channel=ALIYUN_MAN_CHANNEL&rom=default&osVersion=10&accessKey=167ed77859a0655afbea8bbd46660746_53650463&userId=53650463&env=production&pkg=com.hainanyd.duofuguoyuan&immersion=1&appId=53&pkgId=79&isPass=1&time=1598856470800";
        Map<String, String> stringStringMap = urlArgsToMap(s);
//        stringStringMap.forEach((k,v)-> System.out.println(k+"="+v));
        System.out.println("stringStringMap.get(\"deviceId\") = " + stringStringMap.get("deviceId"));
    }
}
