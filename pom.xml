<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <artifactId>caf-boot-parent</artifactId>
        <groupId>com.coohua.caf</groupId>
        <version>2.1.14</version>
    </parent>
    <packaging>pom</packaging>
    <groupId>com.coohua.data</groupId>
    <artifactId>data-agent</artifactId>
    <version>${revision}</version>
    <name>data-agent</name>
    <description>数据代理</description>

    <properties>
        <java.version>1.8</java.version>
        <revision>0.0.2-RELEASE</revision>
    </properties>

    <dependencyManagement>
        <dependencies>
            <!-- 指定guava版本为20.0 -->
            <dependency>
                <groupId>io.prometheus</groupId>
                <artifactId>simpleclient_spring_boot</artifactId>
                <version>0.11.0</version>
            </dependency>
            <dependency>
                <groupId>io.prometheus</groupId>
                <artifactId>simpleclient_hotspot</artifactId>
                <version>0.11.0</version>
            </dependency>
            <dependency>
                <groupId>io.prometheus</groupId>
                <artifactId>simpleclient_servlet</artifactId>
                <version>0.11.0</version>
            </dependency>
            <dependency>
                <groupId>io.prometheus</groupId>
                <artifactId>simpleclient_pushgateway</artifactId>
                <version>0.11.0</version>
            </dependency>
            <dependency>
                <groupId>io.prometheus</groupId>
                <artifactId>simpleclient</artifactId>
                <version>0.11.0</version>
            </dependency>
            <dependency>
                <groupId>io.prometheus</groupId>
                <artifactId>simpleclient_common</artifactId>
                <version>0.11.0</version>
            </dependency>
            <!-- https://mvnrepository.com/artifact/io.prometheus/simpleclient_hotspot -->
            <dependency>
                <groupId>io.prometheus</groupId>
                <artifactId>simpleclient_hotspot</artifactId>
                <version>0.11.0</version>
            </dependency>
            <!-- https://mvnrepository.com/artifact/io.prometheus/simpleclient_httpserver -->
            <dependency>
                <groupId>io.prometheus</groupId>
                <artifactId>simpleclient_httpserver</artifactId>
                <version>0.11.0</version>
            </dependency>
            <dependency>
                <groupId>mysql</groupId>
                <artifactId>mysql-connector-java</artifactId>
                <version>8.0.11</version>
            </dependency>
        </dependencies>
    </dependencyManagement>

    <dependencies>
<!--        <dependency>-->
<!--            <groupId>com.alibaba</groupId>-->
<!--            <artifactId>fastjson</artifactId>-->
<!--            <version>1.2.60</version>-->
<!--        </dependency>-->

<!--        <dependency>-->
<!--            <groupId>org.springframework.boot</groupId>-->
<!--            <artifactId>spring-boot-starter</artifactId>-->
<!--        </dependency>-->

<!--        <dependency>-->
<!--            <groupId>org.springframework.boot</groupId>-->
<!--            <artifactId>spring-boot-starter-web</artifactId>-->
<!--        </dependency>-->

<!--        <dependency>-->
<!--            <groupId>com.alibaba</groupId>-->
<!--            <artifactId>fastjson</artifactId>-->
<!--            <version>1.2.60</version>-->
<!--        </dependency>-->

<!--        <dependency>-->
<!--            <groupId>org.projectlombok</groupId>-->
<!--            <artifactId>lombok</artifactId>-->
<!--            <scope>provided</scope>-->
<!--        </dependency>-->
<!--        <dependency>-->
<!--            <groupId>com.arronlong</groupId>-->
<!--            <artifactId>httpclientutil</artifactId>-->
<!--            <version>1.0.4</version>-->
<!--        </dependency>-->
<!--        <dependency>-->
<!--            <groupId>com.spring4all</groupId>-->
<!--            <artifactId>swagger-spring-boot-starter</artifactId>-->
<!--            <version>1.9.0.RELEASE</version>-->
<!--        </dependency>-->
<!--        <dependency>-->
<!--            <groupId>com.github.xiaoymin</groupId>-->
<!--            <artifactId>swagger-bootstrap-ui</artifactId>-->
<!--            <version>1.9.0</version>-->
<!--        </dependency>-->



<!--        <dependency>-->
<!--            <groupId>com.google.code.gson</groupId>-->
<!--            <artifactId>gson</artifactId>-->
<!--            <version>2.8.2</version>-->
<!--        </dependency>-->
        <dependency>
            <groupId>com.spring4all</groupId>
            <artifactId>swagger-spring-boot-starter</artifactId>
            <version>1.9.0.RELEASE</version>
        </dependency>
        <dependency>
            <groupId>com.github.xiaoymin</groupId>
            <artifactId>swagger-bootstrap-ui</artifactId>
            <version>1.9.0</version>
        </dependency>
    </dependencies>

    <profiles>
        <profile>
            <id>deploy-all</id>
            <modules>
                <module>agent-biz</module>
                <module>agent-api</module>
                <module>agent-connector-ck</module>
                <module>agent-ad-api</module>
            </modules>
        </profile>
        <profile>
            <id>deploy-event-api</id>
            <modules>
                <module>agent-api</module>
            </modules>
        </profile>
        <profile>
            <id>deploy-event-biz</id>
            <modules>
                <module>agent-biz</module>
            </modules>
        </profile>
    </profiles>


    <repositories>
        <repository>
            <id>coohua_releases</id>
            <url>http://maven.coohua.com:8002/nexus/content/repositories/releases</url>
            <releases>
                <enabled>true</enabled>
            </releases>
            <snapshots>
                <enabled>false</enabled>
            </snapshots>
        </repository>
        <repository>
            <id>coohua_snapshots</id>
            <url>http://maven.coohua.com:8002/nexus/content/repositories/snapshots</url>
            <releases>
                <enabled>true</enabled>
            </releases>
            <snapshots>
                <enabled>true</enabled>
            </snapshots>
        </repository>
        <repository>
            <id>coohua_3rdparty</id>
            <url>http://maven.coohua.com:8002/nexus/content/repositories/thirdparty/</url>
            <releases>
                <enabled>true</enabled>
            </releases>
            <snapshots>
                <enabled>false</enabled>
            </snapshots>
        </repository>
    </repositories>

    <build>
        <pluginManagement>
            <plugins>
                <plugin>
                    <groupId>org.codehaus.mojo</groupId>
                    <artifactId>flatten-maven-plugin</artifactId>
                    <version>${flatten-maven-plugin.version}</version>
                </plugin>
            </plugins>
        </pluginManagement>
        <plugins>
            <plugin>
                <artifactId>maven-source-plugin</artifactId>
                <configuration>
                    <attach>true</attach>
                </configuration>
                <executions>
                    <execution>
                        <phase>compile</phase><!--指定绑定的声明周期阶段-->
                        <goals>
                            <goal>jar</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
            <plugin>
                <groupId>org.codehaus.mojo</groupId>
                <artifactId>flatten-maven-plugin</artifactId>
                <version>1.0.1</version>
                <configuration>
                    <updatePomFile>true</updatePomFile>
                </configuration>
                <executions>
                    <execution>
                        <id>flatten</id>
                        <phase>process-resources</phase>
                        <goals>
                            <goal>flatten</goal>
                        </goals>
                    </execution>
                    <execution>
                        <id>flatten.clean</id>
                        <phase>clean</phase>
                        <goals>
                            <goal>clean</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
        </plugins>
    </build>

    <distributionManagement>
        <repository>
            <id>coohua_releases</id>
            <name>releases</name>
            <url>http://maven.coohua.com:8002/nexus/content/repositories/releases</url>
        </repository>
        <snapshotRepository>
            <id>coohua_snapshots</id>
            <name>snapshots</name>
            <url>http://maven.coohua.com:8002/nexus/content/repositories/snapshots</url>
        </snapshotRepository>
    </distributionManagement>

</project>
