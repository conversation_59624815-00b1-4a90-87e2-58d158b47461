<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <artifactId>caf-boot-parent</artifactId>
        <groupId>com.coohua.caf</groupId>
        <version>2.1.16</version>
    </parent>

    <packaging>pom</packaging>
    <groupId>com.coohua.core</groupId>
    <artifactId>user-event</artifactId>
    <version>${revision}</version>
    <name>user-event</name>

    <properties>
        <java.version>1.8</java.version>
        <revision>0.0.2-SNAPSHOT</revision>
    </properties>




    <profiles>
        <profile>
            <id>deploy-all</id>
            <modules>
                <module>event-biz</module>
                <module>event-api</module>
                <module>user-ad-api</module>
                <module>event-gateway</module>
                <module>event-risk</module>
            </modules>

        </profile>
        <profile>
            <id>deploy-event-api</id>
            <modules>
                <module>event-api</module>
                <module>user-ad-api</module>
            </modules>
        </profile>
        <profile>
            <id>deploy-event-biz</id>
            <modules>
                <module>event-biz</module>
            </modules>
        </profile>

        <profile>
            <id>deploy-job</id>
            <modules>
                <module>event-biz</module>
                <module>event-job</module>
            </modules>
        </profile>

        <profile>
            <id>deploy-admin</id>
            <modules>
                <module>event-admin</module>
                <module>event-biz</module>
            </modules>
        </profile>

        <profile>
            <id>deploy-gateway</id>
            <modules>
                <module>event-gateway</module>
            </modules>
        </profile>

        <profile>
            <id>deploy-risk</id>
            <modules>
                <module>event-biz</module>
                <module>event-risk</module>
            </modules>
        </profile>

    </profiles>


    <repositories>
        <repository>
            <id>coohua_releases</id>
            <url>http://maven.coohua.com:8002/nexus/content/repositories/releases</url>
            <releases>
                <enabled>true</enabled>
            </releases>
            <snapshots>
                <enabled>false</enabled>
            </snapshots>
        </repository>
        <repository>
            <id>coohua_snapshots</id>
            <url>http://maven.coohua.com:8002/nexus/content/repositories/snapshots</url>
            <releases>
                <enabled>true</enabled>
            </releases>
            <snapshots>
                <enabled>true</enabled>
            </snapshots>
        </repository>
        <repository>
            <id>coohua_3rdparty</id>
            <url>http://maven.coohua.com:8002/nexus/content/repositories/thirdparty/</url>
            <releases>
                <enabled>true</enabled>
            </releases>
            <snapshots>
                <enabled>false</enabled>
            </snapshots>
        </repository>
    </repositories>

    <build>
        <pluginManagement>
            <plugins>
                <plugin>
                    <groupId>org.codehaus.mojo</groupId>
                    <artifactId>flatten-maven-plugin</artifactId>
                    <version>${flatten-maven-plugin.version}</version>
                </plugin>
            </plugins>
        </pluginManagement>
        <plugins>
            <plugin>
                <artifactId>maven-source-plugin</artifactId>
                <configuration>
                    <attach>true</attach>
                </configuration>
                <version>3.2.1</version>
                <executions>
                    <execution>
                        <phase>compile</phase><!--指定绑定的声明周期阶段-->
                        <goals>
                            <goal>jar</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
            <plugin>
                <artifactId>spring-boot-maven-plugin</artifactId>
                <groupId>org.springframework.boot</groupId>
                <version>2.7.5</version>
            </plugin>
            <plugin>
                <groupId>org.codehaus.mojo</groupId>
                <artifactId>flatten-maven-plugin</artifactId>
                <version>1.0.1</version>
                <configuration>
                    <updatePomFile>true</updatePomFile>
                </configuration>
                <executions>
                    <execution>
                        <id>flatten</id>
                        <phase>process-resources</phase>
                        <goals>
                            <goal>flatten</goal>
                        </goals>
                    </execution>
                    <execution>
                        <id>flatten.clean</id>
                        <phase>clean</phase>
                        <goals>
                            <goal>clean</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
        </plugins>
    </build>

    <distributionManagement>
        <repository>
            <id>coohua_releases</id>
            <name>releases</name>
            <url>http://maven.coohua.com:8002/nexus/content/repositories/releases</url>
        </repository>
        <snapshotRepository>
            <id>coohua_snapshots</id>
            <name>snapshots</name>
            <url>http://maven.coohua.com:8002/nexus/content/repositories/snapshots</url>
        </snapshotRepository>
    </distributionManagement>

</project>
