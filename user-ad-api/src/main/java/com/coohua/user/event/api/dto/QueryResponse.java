package com.coohua.user.event.api.dto;


import java.io.Serializable;

/**
 * <AUTHOR>
 * @since 2021/6/29
 */
public class QueryResponse implements Serializable {
    private String queryKey;
    private Long userId;
    private Integer ecpmSum;
    private Integer videoExposureCount;

    public String getQueryKey() {
        return queryKey;
    }

    public void setQueryKey(String queryKey) {
        this.queryKey = queryKey;
    }

    public Long getUserId() {
        return userId;
    }

    public void setUserId(Long userId) {
        this.userId = userId;
    }

    public Integer getEcpmSum() {
        return ecpmSum;
    }

    public void setEcpmSum(Integer ecpmSum) {
        this.ecpmSum = ecpmSum;
    }

    public Integer getVideoExposureCount() {
        return videoExposureCount;
    }

    public void setVideoExposureCount(Integer videoExposureCount) {
        this.videoExposureCount = videoExposureCount;
    }
}
