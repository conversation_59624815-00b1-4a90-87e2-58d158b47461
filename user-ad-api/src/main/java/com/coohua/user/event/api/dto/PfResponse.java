package com.coohua.user.event.api.dto;

import java.io.Serializable;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @since 2024/2/2
 */
public class PfResponse implements Serializable {
    private Long adId;
    private String product;
    private String productName;
    private Long advertiserId;
    Map<Long,UserPv> needSendUserMap;

    public static class UserPv implements Serializable{
        private Integer pv;
        private Double arpu;

        public Integer getPv() {
            return pv;
        }

        public void setPv(Integer pv) {
            this.pv = pv;
        }

        public Double getArpu() {
            return arpu;
        }

        public void setArpu(Double arpu) {
            this.arpu = arpu;
        }
    }

    public Long getAdId() {
        return adId;
    }

    public void setAdId(Long adId) {
        this.adId = adId;
    }

    public String getProduct() {
        return product;
    }

    public void setProduct(String product) {
        this.product = product;
    }

    public String getProductName() {
        return productName;
    }

    public void setProductName(String productName) {
        this.productName = productName;
    }

    public Long getAdvertiserId() {
        return advertiserId;
    }

    public void setAdvertiserId(Long advertiserId) {
        this.advertiserId = advertiserId;
    }

    public Map<Long, UserPv> getNeedSendUserMap() {
        return needSendUserMap;
    }

    public void setNeedSendUserMap(Map<Long, UserPv> needSendUserMap) {
        this.needSendUserMap = needSendUserMap;
    }
}
