package com.coohua.user.event.api.dto;

import java.io.Serializable;

public class CheckResult  implements Serializable {

    private Boolean pass;
    private Boolean directRefund;
    private String reason;

    public Boolean getPass() {
        return pass;
    }

    public void setPass(Boolean pass) {
        this.pass = pass;
    }

    public Boolean getDirectRefund() {
        return directRefund;
    }

    public void setDirectRefund(Boolean directRefund) {
        this.directRefund = directRefund;
    }

    public String getReason() {
        return reason;
    }

    public void setReason(String reason) {
        this.reason = reason;
    }
}
