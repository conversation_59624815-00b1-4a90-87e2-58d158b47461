-- 规则引擎相关表结构

-- 规则分组表
CREATE TABLE `rule_group` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `group_code` varchar(100) NOT NULL COMMENT '分组编码，唯一标识',
  `group_name` varchar(200) NOT NULL COMMENT '分组名称',
  `description` text COMMENT '分组描述',
  `app_id` bigint(20) NOT NULL COMMENT '应用ID',
  `status` tinyint(4) NOT NULL DEFAULT '1' COMMENT '分组状态：0-禁用，1-启用',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `creator` varchar(100) DEFAULT NULL COMMENT '创建人',
  `updater` varchar(100) DEFAULT NULL COMMENT '更新人',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_group_code` (`group_code`),
  KEY `idx_app_id` (`app_id`),
  KEY `idx_status` (`status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='规则分组表';

-- 规则定义表
CREATE TABLE `rule_definition` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `rule_code` varchar(100) NOT NULL COMMENT '规则编码，唯一标识',
  `rule_name` varchar(200) NOT NULL COMMENT '规则名称',
  `description` text COMMENT '规则描述',
  `priority` int(11) NOT NULL DEFAULT '100' COMMENT '规则优先级，数字越小优先级越高',
  `rule_group_id` bigint(20) NOT NULL COMMENT '规则所属分组ID',
  `status` tinyint(4) NOT NULL DEFAULT '1' COMMENT '规则状态：0-禁用，1-启用',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `creator` varchar(100) DEFAULT NULL COMMENT '创建人',
  `updater` varchar(100) DEFAULT NULL COMMENT '更新人',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_rule_code` (`rule_code`),
  KEY `idx_rule_group_id` (`rule_group_id`),
  KEY `idx_status` (`status`),
  KEY `idx_priority` (`priority`),
  CONSTRAINT `fk_rule_group` FOREIGN KEY (`rule_group_id`) REFERENCES `rule_group` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='规则定义表';

-- 规则条件表
CREATE TABLE `rule_condition` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `rule_id` bigint(20) NOT NULL COMMENT '规则ID',
  `field_name` varchar(100) NOT NULL COMMENT '条件字段名',
  `operator` varchar(50) NOT NULL COMMENT '条件操作符：equals, not_equals, greater_than, less_than, contains等',
  `field_value` text NOT NULL COMMENT '条件字段值',
  `condition_order` int(11) NOT NULL DEFAULT '1' COMMENT '条件顺序',
  `logical_operator` varchar(10) DEFAULT 'AND' COMMENT '逻辑连接符：AND, OR',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_rule_id` (`rule_id`),
  KEY `idx_condition_order` (`condition_order`),
  CONSTRAINT `fk_rule_condition` FOREIGN KEY (`rule_id`) REFERENCES `rule_definition` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='规则条件表';

-- 规则动作表
CREATE TABLE `rule_action` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `rule_id` bigint(20) NOT NULL COMMENT '规则ID',
  `action_type` varchar(50) NOT NULL COMMENT '动作类型：reject, pass, log, callback等',
  `action_params` text COMMENT '动作参数，JSON格式',
  `action_order` int(11) NOT NULL DEFAULT '1' COMMENT '动作顺序',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_rule_id` (`rule_id`),
  KEY `idx_action_order` (`action_order`),
  CONSTRAINT `fk_rule_action` FOREIGN KEY (`rule_id`) REFERENCES `rule_definition` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='规则动作表';

-- 规则执行日志表
CREATE TABLE `rule_execution_log` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `app_id` bigint(20) NOT NULL COMMENT '应用ID',
  `user_id` bigint(20) NOT NULL COMMENT '用户ID',
  `order_no` varchar(100) DEFAULT NULL COMMENT '订单号',
  `amount` int(11) DEFAULT NULL COMMENT '金额',
  `hit_rule_id` bigint(20) DEFAULT NULL COMMENT '命中的规则ID',
  `hit_rule_name` varchar(200) DEFAULT NULL COMMENT '命中的规则名称',
  `pass` tinyint(4) NOT NULL COMMENT '是否通过：0-不通过，1-通过',
  `direct_refund` tinyint(4) DEFAULT '0' COMMENT '是否直接退款：0-否，1-是',
  `reason` varchar(500) DEFAULT NULL COMMENT '拒绝原因',
  `execution_time` bigint(20) DEFAULT NULL COMMENT '执行时间（毫秒）',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`id`),
  KEY `idx_app_id_user_id` (`app_id`, `user_id`),
  KEY `idx_order_no` (`order_no`),
  KEY `idx_hit_rule_id` (`hit_rule_id`),
  KEY `idx_create_time` (`create_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='规则执行日志表';

-- Redis参数配置表
CREATE TABLE `rule_parameter_redis` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `parameter_name` varchar(100) NOT NULL COMMENT '参数名称',
  `parameter_display_name` varchar(200) DEFAULT NULL COMMENT '参数显示名称',
  `redis_key_template` varchar(500) NOT NULL COMMENT 'Redis键模板，支持变量替换，如user:exposure:video:time:{userId}',
  `redis_command` varchar(50) NOT NULL COMMENT 'Redis命令：get, hget, exists, sismember等',
  `redis_field` varchar(200) DEFAULT NULL COMMENT 'Redis字段，用于hash类型',
  `data_type` varchar(50) DEFAULT 'string' COMMENT '数据类型：string, integer, long, double, boolean',
  `default_value` varchar(500) DEFAULT NULL COMMENT '默认值',
  `description` text COMMENT '参数描述',
  `priority` int(11) NOT NULL DEFAULT '100' COMMENT '参数优先级',
  `status` tinyint(4) NOT NULL DEFAULT '1' COMMENT '参数状态：0-禁用，1-启用',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `creator` varchar(100) DEFAULT NULL COMMENT '创建人',
  `updater` varchar(100) DEFAULT NULL COMMENT '更新人',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_parameter_name` (`parameter_name`),
  KEY `idx_status` (`status`),
  KEY `idx_priority` (`priority`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='Redis参数配置表';

-- 插入示例数据
INSERT INTO `rule_group` (`group_code`, `group_name`, `description`, `app_id`, `status`, `creator`) VALUES
('withdraw_check', '提现检查规则组', '用于提现审核的规则组', 1001, 1, 'system'),
('user_behavior_check', '用户行为检查规则组', '用于用户行为检查的规则组', 1001, 1, 'system');

INSERT INTO `rule_definition` (`rule_code`, `rule_name`, `description`, `priority`, `rule_group_id`, `status`, `creator`) VALUES
('withdraw_amount_limit', '提现金额限制', '检查用户提现金额是否超过限制', 10, 1, 1, 'system'),
('user_exposure_check', '用户曝光检查', '检查用户曝光次数是否异常', 20, 2, 1, 'system');

INSERT INTO `rule_condition` (`rule_id`, `field_name`, `operator`, `field_value`, `condition_order`, `logical_operator`) VALUES
(1, 'totalAmount', 'greater_than', '1000', 1, 'AND'),
(2, 'exposureCount', 'greater_than', '500', 1, 'AND');

INSERT INTO `rule_action` (`rule_id`, `action_type`, `action_params`, `action_order`) VALUES
(1, 'reject', 'directRefund=true,reason=提现金额超过限制', 1),
(2, 'reject', 'directRefund=true,reason=用户曝光次数异常', 1);

INSERT INTO `rule_parameter_redis` (`parameter_name`, `parameter_display_name`, `redis_key_template`, `redis_command`, `data_type`, `description`, `status`, `creator`) VALUES
('exposureCount', '用户曝光次数', 'user:exposure:{userId}:{appId}', 'get', 'integer', '获取用户在指定应用的曝光次数', 1, 'system'),
('userWithdrawTotal', '用户累计提现金额', 'user:withdraw:total:{userId}', 'get', 'integer', '获取用户累计提现金额', 1, 'system');
