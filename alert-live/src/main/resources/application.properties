server.port=8299

spring.mvc.view.prefix=/static/pages/
spring.mvc.view.suffix=.html

xxl.job.admin.addresses = http://*************:8080/xxl-job-admin
xxl.job.executor.appname = alert-service-live
xxl.job.executor.logpath = /data/logs/xxl-job/alert-service-live
xxl.job.executor.logretentiondays = 3
xxl.job.executor.port = -1

spring.datasource.driverClass = com.mysql.jdbc.Driver
spring.datasource.url = *************************************************************************? useUnicode=true&characterEncoding=utf8&useSSL=false&serverTimezone=CTT
spring.datasource.username = dispenseus
spring.datasource.password = yI2gbNL1PneDLscj
spring.datasource.type = com.alibaba.druid.pool.DruidDataSource
spring.datasource.maxActive = 1000
spring.datasource.initialSize = 1
spring.datasource.minIdle = 8
spring.datasource.maxWait = 2000
spring.datasource.timeBetween = 6000000
spring.datasource.minEvictableIdle = 300000
spring.datasource.validationQuery = SELECT 'x'
spring.datasource.testWhileIdle = true
spring.datasource.keepAlive = true
spring.datasource.testOnBorrow = true
spring.datasource.testOnReturn = true
spring.datasource.poolPreparedStatements = true
spring.datasource.maxPoolPreparedStatementPerConnectionSize = 20
spring.datasource.removeAbandoned = false
spring.datasource.removeAbandonedTime = 1800000
mybatis-plus.typeAliasesPackage = com.shinet.core.alert.data.mapper
mybatis-plus.configuration.log-impl = org.apache.ibatis.logging.stdout.StdOutImpl


spring.adb.datasource.url = ****************************************************************? useUnicode=true&characterEncoding=utf8&useSSL=false&serverTimezone=CTT
spring.adb.datasource.username = alertus
spring.adb.datasource.password = alertus!@#$1234

spring.rta.datasource.url = *********************************************************************? useUnicode=true&characterEncoding=utf8&useSSL=false&serverTimezone=CTT
spring.rta.datasource.username = coohua
spring.rta.datasource.password = b3HPYoZ!7#D

spring.dsp.datasource.url = *************************************************************************? useUnicode=true&characterEncoding=utf8&useSSL=false&serverTimezone=CTT
spring.dsp.datasource.username = dsp_account
spring.dsp.datasource.password = yI2gbNL1PneDLscj

spring.bpap.datasource.url = ******************************************************************? useUnicode=true&characterEncoding=utf8&useSSL=false&serverTimezone=CTT
spring.bpap.datasource.username = coohua
spring.bpap.datasource.password = yAsgT6d99B

spring.dataagent.datasource.url = *************************************************************************? useUnicode=true&characterEncoding=utf8&useSSL=false&serverTimezone=CTT&rewriteBatchedStatements=true&allowMultiQueries=true
spring.dataagent.datasource.username = dispenseus
spring.dataagent.datasource.password = yI2gbNL1PneDLscj

spring.alert.datasource.url = ***********************************************************************? useUnicode=true&characterEncoding=utf8&useSSL=false&serverTimezone=CTT
spring.alert.datasource.username = alertacc
spring.alert.datasource.password = yI2gbNL1PneDLscj

spring.common.datasource.url = *********************************************************************? useUnicode=true&characterEncoding=utf8&useSSL=false&serverTimezone=CTT
spring.common.datasource.username = dataclean
spring.common.datasource.password = Ah82LUlD8XIbpYWM


spring.safe.datasource.url = **********************************************************************? useUnicode=true&characterEncoding=utf8&useSSL=false
spring.safe.datasource.username = safeacc
spring.safe.datasource.password = safeacc!@#$1234

spring.mall.datasource.url = *********************************************************************? useUnicode=true&characterEncoding=utf8&useSSL=false&serverTimezone=CTT
spring.mall.datasource.username = cn_read
spring.mall.datasource.password = jTe2c4o8zV

spring.datasource.gpt.url = ******************************************************************************? useUnicode=true&characterEncoding=utf8&useSSL=false&serverTimezone=CTT

spring.click.datasource.username = default
spring.click.datasource.password = Phw7a7A4
spring.click.datasource.url = ******************************************************************
spring.click.datasource.maxActive = 200
spring.click.datasource.initialSize = 10
spring.click.datasource.minIdle = 1
spring.click.datasource.maxWait = 2000
spring.click.datasource.testWhileIdle = false
spring.click.datasource.timeBetweenEvictionRunsMillis = 600000
spring.click.datasource.minEvictableIdleTimeMillis = 300000
spring.click.datasource.keepAlive = true
spring.click.datasource.poolPreparedStatements = true
spring.click.datasource.maxPoolPreparedStatementPerConnectionSize = 20
spring.click.datasource.removeAbandoned = true
spring.click.datasource.removeAbandonedTime = 1800
spring.click.datasource.driverClass = ru.yandex.clickhouse.ClickHouseDriver
spring.click4.datasource.url = ******************************************************************
spring.click12.datasource.url = ******************************************************************



