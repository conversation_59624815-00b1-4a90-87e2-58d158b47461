package com.shinet.core.alert.data;

import com.shinet.core.alert.coreservice.service.ToufangJobCheck;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import com.xxl.job.core.log.XxlJobLogger;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.util.concurrent.TimeUnit;

@Component
@Slf4j
public class AlertLiveChecker {
    @Autowired
    ToufangJobCheck toufangJobCheck;
    public void checkLive(){
        new Thread(){
            @Override
            public void run() {
                while (true){
                    try {
                        String checkIps = "alerttimer-*************:8080,*************:8080;";
                        toufangJobCheck.checkToufangJob("alert-timerapi",checkIps,null,(jsonObject -> jsonObject.getInteger("status")!=200));
                        TimeUnit.SECONDS.sleep(60);
                    }catch (Exception e){
                        log.error("",e);
                    }
                }
            }
        }.start();
    }


    @XxlJob("alert-service-live")
    public ReturnT<?> serviceLive(String param){
        String checkIps = "alerttimer-*************:8080,*************:8080;";
        toufangJobCheck.checkToufangJob("alert-timerapi",checkIps,null,(jsonObject -> jsonObject.getInteger("status")!=200));
        return ReturnT.SUCCESS;
    }

}
