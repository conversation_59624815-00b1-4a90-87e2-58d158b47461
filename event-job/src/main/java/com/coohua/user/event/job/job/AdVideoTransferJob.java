package com.coohua.user.event.job.job;

import com.coohua.user.event.job.service.AdVideoTransferService;
import com.coohua.user.event.biz.util.DingTalkPushUtils;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import com.xxl.job.core.log.XxlJobLogger;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @since 2020/11/5
 */
@Slf4j
@Component
public class AdVideoTransferJob {

    @Autowired
    private AdVideoTransferService adVideoTransferService;

    @XxlJob(value = "AdVideoTransferJob", init = "init", destroy = "destroy")
    public ReturnT<String> adVideoTransferJob(String param) throws Exception {
        try {
            adVideoTransferService.transfer();
            return ReturnT.SUCCESS;
        }catch (Exception e){
            XxlJobLogger.log("Exception:",e);
            DingTalkPushUtils.jobErr("AdVideoTransferJob");
            return ReturnT.FAIL;
        }
    }

    public void init(){
        log.info("AdVideoTransferJob INIT...");
    }

    public void destroy(){
        log.info("AdVideoTransferJob DESTROY...");
    }
}
