package com.coohua.user.event.job.job;

import com.coohua.user.event.biz.dc.service.HistoryIncomeService;
import com.coohua.user.event.biz.util.DingTalkPushUtils;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import com.xxl.job.core.log.XxlJobLogger;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @since 2020/9/7
 */
@Slf4j
@Component
public class HistoryResultReportJob {
    @Autowired
    private HistoryIncomeService historyIncomeService;

    @XxlJob(value = "HistoryResultReportJob", init = "init", destroy = "destroy")
    public ReturnT<String> historyResultReportJob(String param) throws Exception {
        try {
            historyIncomeService.synchHistoryIncome(param);
            return ReturnT.SUCCESS;
        }catch (Exception e){
            XxlJobLogger.log("Exception:",e);
            DingTalkPushUtils.jobErr("HistoryResultReportJob");
            return ReturnT.FAIL;
        }

    }

    public void init(){
        log.info("HistoryResultReportJob INIT...");
    }

    public void destroy(){
        log.info("HistoryResultReportJob DESTROY...");
    }
}
