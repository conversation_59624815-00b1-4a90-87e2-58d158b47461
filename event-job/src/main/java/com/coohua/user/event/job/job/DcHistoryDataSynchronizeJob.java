package com.coohua.user.event.job.job;

import com.coohua.user.event.biz.dc.service.DcHistoryDataService;
import com.coohua.user.event.biz.toufang.service.TouFangBaseQueryService;
import com.coohua.user.event.biz.util.DingTalkPushUtils;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import com.xxl.job.core.log.XxlJobLogger;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @since 2020/9/4
 */
@Slf4j
@Component
public class DcHistoryDataSynchronizeJob {

    @Autowired
    private DcHistoryDataService dcHistoryDataService;

    @XxlJob(value = "DcHistoryDataSynchronizeJob", init = "init", destroy = "destroy")
    public ReturnT<String> dcHistoryDataSynchronizeJob(String param) throws Exception {
        try {
            dcHistoryDataService.synchDate();
            return ReturnT.SUCCESS;
        }catch (Exception e){
            XxlJobLogger.log("Exception:",e);
            DingTalkPushUtils.jobErr("DcHistoryDataSynchronizeJob");
            return ReturnT.FAIL;
        }

    }

    public void init(){
        log.info("DcHistoryDataSynchronizeJob INIT...");
    }

    public void destroy(){
        log.info("DcHistoryDataSynchronizeJob DESTROY...");
    }

}
