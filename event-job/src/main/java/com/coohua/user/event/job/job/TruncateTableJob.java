package com.coohua.user.event.job.job;

import com.coohua.user.event.biz.dc.service.CleanResultService;
import com.coohua.user.event.biz.dc.service.TouFangOfflineService;
import com.coohua.user.event.biz.util.DingTalkPushUtils;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import com.xxl.job.core.log.XxlJobLogger;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @since 2020/9/9
 *
 * 已将所有清空迁移至脚本...
 */
@Slf4j
@Component
public class TruncateTableJob {

    @Autowired
    private CleanResultService cleanResultService;

    @XxlJob(value = "TruncateTableJob", init = "init", destroy = "destroy")
    public ReturnT<String> truncateTableJob(String param) throws Exception {
        try {
            cleanResultService.cleanTable();
            return ReturnT.SUCCESS;
        }catch (Exception e){
            XxlJobLogger.log("Exception:",e);
            DingTalkPushUtils.jobErr("TruncateTableJob");
            return ReturnT.FAIL;
        }

    }

    @XxlJob(value = "TruncateTableJob_Business", init = "init", destroy = "destroy")
    public ReturnT<String> truncateTableJob_Business(String param) throws Exception {
        try {
            cleanResultService.cleanTableBusiness();
            return ReturnT.SUCCESS;
        }catch (Exception e){
            XxlJobLogger.log("Exception:",e);
            DingTalkPushUtils.jobErr("TruncateTableJob_Business");
            return ReturnT.FAIL;
        }

    }

    @XxlJob(value = "TruncateTableJob_VideoTemp", init = "init", destroy = "destroy")
    public ReturnT<String> truncateTableJob_VideoTemp(String param) throws Exception {
        try {
            cleanResultService.cleanVideoTemp();
            return ReturnT.SUCCESS;
        }catch (Exception e){
            XxlJobLogger.log("Exception:",e);
            DingTalkPushUtils.jobErr("TruncateTableJob_VideoTemp");
            return ReturnT.FAIL;
        }

    }

    @XxlJob(value = "DeleteTableJob_DeviceTemp", init = "init", destroy = "destroy")
    public ReturnT<String> deleteTableJob_DeviceTemp(String param) throws Exception {
        try {
            cleanResultService.deleteTemp(param);
            return ReturnT.SUCCESS;
        }catch (Exception e){
            XxlJobLogger.log("Exception:",e);
            DingTalkPushUtils.jobErr("DeleteTableJob_DeviceTemp");
            return ReturnT.FAIL;
        }

    }


    public void init(){
        log.info("TruncateTableJob INIT...");
    }

    public void destroy(){
        log.info("TruncateTableJob DESTROY...");
    }
}
