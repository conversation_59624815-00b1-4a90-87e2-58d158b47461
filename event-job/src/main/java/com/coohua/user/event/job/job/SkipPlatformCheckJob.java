package com.coohua.user.event.job.job;

import com.coohua.user.event.biz.service.AdCloseService;
import com.coohua.user.event.biz.util.DingTalkPushUtils;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import com.xxl.job.core.log.XxlJobLogger;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @since 2023/3/30
 */
@Slf4j
@Component
public class SkipPlatformCheckJob {

    @Autowired
    private AdCloseService adCloseService;

    @XxlJob(value = "SkipPlatformCheckJob", init = "init", destroy = "destroy")
    public ReturnT<String> skipPlatformCheck(String param) throws Exception {
        try {
            adCloseService.checkDisablePlatformCount();
            return ReturnT.SUCCESS;
        }catch (Exception e){
            XxlJobLogger.log("Exception:",e);
            DingTalkPushUtils.jobErr("UserGrayJob");
            return ReturnT.FAIL;
        }

    }

    public void init(){
        log.info("SkipPlatformCheckJob INIT...");
    }

    public void destroy(){
        log.info("SkipPlatformCheckJob DESTROY...");
    }
}
