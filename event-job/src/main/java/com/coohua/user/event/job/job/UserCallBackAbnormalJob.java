package com.coohua.user.event.job.job;

import com.coohua.user.event.biz.service.UserCallBackAbnormalService;
import com.coohua.user.event.biz.util.DingTalkPushUtils;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import com.xxl.job.core.log.XxlJobLogger;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Slf4j
@Component
public class UserCallBackAbnormalJob {

    @Autowired
    private UserCallBackAbnormalService userCallBackAbnormalService;


    @XxlJob(value = "userCallBackAbnormalJob", init = "init", destroy = "destroy")
    public ReturnT<String> userCallBackAbnormalJob(String param) throws Exception {
        try {
            userCallBackAbnormalService.userCallBackAbnormal();
            return ReturnT.SUCCESS;
        } catch (Exception e) {
            log.error("userCallBackAbnormalJob err", e);
            XxlJobLogger.log("Exception:", e);
            DingTalkPushUtils.jobErr("userCallBackAbnormalJob");
            return ReturnT.FAIL;
        }
    }

    public void init(){
        log.info("userCallBackAbnormalJob INIT...");
    }

    public void destroy(){
        log.info("userCallBackAbnormalJob DESTROY...");
    }
}
