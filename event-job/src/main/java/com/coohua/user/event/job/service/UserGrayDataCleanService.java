package com.coohua.user.event.job.service;

import com.coohua.user.event.biz.dc.entity.UserGrayIpEntity;
import com.coohua.user.event.biz.dc.mapper.UserGrayIpMapper;
import com.coohua.user.event.biz.enums.GrayType;
import com.coohua.user.event.biz.util.DateUtil;
import com.coohua.user.event.biz.util.HBaseUtils;
import com.coohua.user.event.biz.util.Lists;
import com.google.common.collect.ImmutableMap;
import lombok.extern.slf4j.Slf4j;
import org.apache.hadoop.hbase.client.Connection;
import org.springframework.stereotype.Service;
import top.zrbcool.pepper.boot.jediscluster.JedisClusterClient;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.function.UnaryOperator;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * <AUTHOR>
 * @since 2022/3/3
 */
@Slf4j
@Service
public class UserGrayDataCleanService {

    @Resource(name = "hbaseConnection")
    private Connection hBaseConnection;
    @Resource
    private UserGrayIpMapper userGrayIpMapper;

    @Resource(name = "bp-userJedisClusterClient")
    private JedisClusterClient bpUserJedisClusterClient;

    // 过期90未更新的数据
    public void delMoreThan90(){
        log.info("开始处理过期的拉黑名单....");
        Date ninetyDaysAgo = DateUtil.dateIncreaseByDay(new Date(),-180);
        List<UserGrayIpEntity> grayIpEntityList = userGrayIpMapper.queryBatchLessThanCreateTime(ninetyDaysAgo);
        if (Lists.noEmpty(grayIpEntityList)) {
            Map<Integer, List<UserGrayIpEntity>> grayMap = grayIpEntityList.stream()
                    .collect(Collectors.groupingBy(UserGrayIpEntity::getTargetType));

            grayMap.forEach(this::delGrayInfo);
        }
        log.info("完成处理过期的拉黑名单....");
    }

    private static final Map<Integer, UnaryOperator<String>> KEY_MAPPER = ImmutableMap.of(
            2, userId -> "user:" + userId,
            1, deviceId -> "device:" + deviceId,
            3, unionId -> "union:" + unionId);

    private void delGrayInfo(Integer targetType,List<UserGrayIpEntity> grayIpEntities){
        GrayType grayType = GrayType.get(targetType);
        if (grayType == null){
            return;
        }
        UnaryOperator<String> keyMapper = KEY_MAPPER.get(grayType.getType());
        // STEP 1 从HBase 删除
        List<String> targetList = grayIpEntities.stream().map(UserGrayIpEntity::getTargetId).collect(Collectors.toList());
        Stream<String> rowKeyStream = targetList.stream().map(keyMapper);
        HBaseUtils.batchDeleteFromHbase(hBaseConnection, "GlobalBlackUser", rowKeyStream, Function.identity());

        // STEP 2 从DB删除
        List<Integer> idList = grayIpEntities.stream().map(UserGrayIpEntity::getId).collect(Collectors.toList());
        userGrayIpMapper.delByIds(idList);
        // 插入删除记录
        grayIpEntities.forEach(userGrayIpEntity -> {
            userGrayIpEntity.setUpdateTime(new Date());
            userGrayIpMapper.insertIntoUserGrayIpDelRecord(userGrayIpEntity);
        });

        // STEP 3 从Redis删除
        if (GrayType.USER.equals(grayType)){
            // 删除用户拉黑
            targetList.forEach(target -> bpUserJedisClusterClient.del(String.format("gray:user:global:%s",target)));
        }
    }

}
