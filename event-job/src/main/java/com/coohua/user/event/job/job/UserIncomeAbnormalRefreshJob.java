package com.coohua.user.event.job.job;

import com.coohua.user.event.biz.service.UserIncomeAbnormalService;
import com.coohua.user.event.biz.util.DingTalkPushUtils;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import com.xxl.job.core.log.XxlJobLogger;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;

/**
 * 用户收入占比异常 数据刷新job
 */
@Slf4j
@Component
public class UserIncomeAbnormalRefreshJob {

    @Resource
    private UserIncomeAbnormalService userIncomeAbnormalService;

    @XxlJob(value = "UserIncomeIosRefreshJob", init = "init", destroy = "destroy")
    public ReturnT<String> UserIncomeIosRefreshJob(String param) throws Exception {
        try {
            userIncomeAbnormalService.refreshUserIncomeAbnormalIos();
            return ReturnT.SUCCESS;
        } catch (Exception e) {
            log.error("UserIncomeIosRefreshJob err", e);
            XxlJobLogger.log("Exception:", e);
            DingTalkPushUtils.jobErr("UserIncomeIosRefreshJob");
            return ReturnT.FAIL;
        }
    }

    @XxlJob(value = "Group10GdtExUserJob", init = "init", destroy = "destroy")
    public ReturnT<String> Group10GdtExUserJob(String param) throws Exception {
        try {
            userIncomeAbnormalService.Group10GdtExUserJob(param);
            return ReturnT.SUCCESS;
        } catch (Exception e) {
            log.error("Group10GdtExUserJob err", e);
            XxlJobLogger.log("Exception:", e);
            DingTalkPushUtils.jobErr("Group10GdtExUserJob");
            return ReturnT.FAIL;
        }
    }

    @XxlJob(value = "GdtGapExUserJob", init = "init", destroy = "destroy")
    public ReturnT<String> GdtGapExUserJob(String param) throws Exception {
        try {
            userIncomeAbnormalService.GdtGapExUserJob(param);
            return ReturnT.SUCCESS;
        } catch (Exception e) {
            log.error("GdtGapExUserJob err", e);
            XxlJobLogger.log("Exception:", e);
            DingTalkPushUtils.jobErr("GdtGapExUserJob");
            return ReturnT.FAIL;
        }
    }

    @XxlJob(value = "GroupSgmGdtExUserJob", init = "init", destroy = "destroy")
    public ReturnT<String> GroupSgmGdtExUserJob(String param) throws Exception {
        try {
            userIncomeAbnormalService.GroupSgmGdtExUserJob(param);
            return ReturnT.SUCCESS;
        } catch (Exception e) {
            log.error("GroupSgmGdtExUserJob err {}", e.getMessage(), e);
            XxlJobLogger.log("Exception:", e);
            DingTalkPushUtils.jobErr("GroupSgmGdtExUserJob");
            return ReturnT.FAIL;
        }
    }

    @XxlJob(value = "userAndroidBaiduIncomeAbnormalJob", init = "init", destroy = "destroy")
    public ReturnT<String> userAndroidBaiduIncomeAbnormalJob(String param) throws Exception {
        try {
            userIncomeAbnormalService.userAndroidBaiduIncomeAbnormalJob();
            return ReturnT.SUCCESS;
        } catch (Exception e) {
            log.error("userAndroidBaiduIncomeAbnormalJob err", e);
            XxlJobLogger.log("Exception:", e);
            DingTalkPushUtils.jobErr("userAndroidBaiduIncomeAbnormalJob");
            return ReturnT.FAIL;
        }
    }

    @XxlJob(value = "userIncomeAbnormalJob", init = "init", destroy = "destroy")
    public ReturnT<String> userIncomeAbnormalJob(String param) throws Exception {
        try {
            userIncomeAbnormalService.userIncomeAbnormalJob();
            return ReturnT.SUCCESS;
        } catch (Exception e) {
            log.error("userIncomeAbnormalJob err", e);
            XxlJobLogger.log("Exception:", e);
            DingTalkPushUtils.jobErr("userIncomeAbnormalJob");
            return ReturnT.FAIL;
        }
    }



    @XxlJob(value = "checkNonBaiduAndroidIncomeAbnormalJob", init = "init", destroy = "destroy")
    public ReturnT<String> checkNonBaiduAndroidIncomeAbnormalJob(String param) throws Exception {
        try {
            userIncomeAbnormalService.checkNonBaiduAndroidIncomeAbnormalJob();
            return ReturnT.SUCCESS;
        } catch (Exception e) {
            log.error("checkNonBaiduAndroidIncomeAbnormalJob err", e);
            XxlJobLogger.log("Exception:", e);
            DingTalkPushUtils.jobErr("checkNonBaiduAndroidIncomeAbnormalJob");
            return ReturnT.FAIL;
        }
    }

    @XxlJob(value = "userCallBackInterceptRefreshJob", init = "init", destroy = "destroy")
    public ReturnT<String> userCallBackInterceptRefreshJob(String param) throws Exception {
        try {
            userIncomeAbnormalService.userCallBackInterceptRefreshJob();
            return ReturnT.SUCCESS;
        } catch (Exception e) {
            log.error("userCallBackInterceptRefreshJob err", e);
            XxlJobLogger.log("Exception:", e);
            DingTalkPushUtils.jobErr("userCallBackInterceptRefreshJob");
            return ReturnT.FAIL;
        }
    }

    /**
     * 检查用户回调拦截数据
     * @param param
     * @return
     * @throws Exception
     */
    @XxlJob(value = "checkUserCallBackInterceptJob", init = "init", destroy = "destroy")
    public ReturnT<String> checkUserCallBackInterceptJob(String param) throws Exception {
        try {
            boolean b = userIncomeAbnormalService.checkUserCallBackInterceptJob();
            if (b) {
                XxlJobLogger.log("查询到今日拦截用户，已发送钉钉");
            } else {
                XxlJobLogger.log("本次任务未查询到拦截用户");
            }
            return ReturnT.SUCCESS;
        } catch (Exception e) {
            log.error("checkUserCallBackInterceptJob err", e);
            XxlJobLogger.log("Exception:", e);
            DingTalkPushUtils.jobErr("userCallBackInterceptRefreshJob");
            return ReturnT.FAIL;
        }
    }

    public void init() {
        log.info("UserIncomeAbnormalRefreshJob INIT...");
    }

    public void destroy() {
        log.info("UserIncomeAbnormalRefreshJob DESTROY...");
    }
}
