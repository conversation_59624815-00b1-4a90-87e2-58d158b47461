package com.coohua.user.event.job.job;

import com.aliyun.openservices.shade.com.google.common.base.Charsets;
import com.coohua.caf.core.util.MD5Util;
import com.coohua.user.event.biz.service.RlAdDataPushService;
import com.google.common.hash.Hashing;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import com.xxl.job.core.log.XxlJobLogger;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.*;

/**
 * <AUTHOR>
 * @since 2023/5/31
 */
@Slf4j
@Component
public class PushAdDataToThirdJob {

    @Autowired
    private RlAdDataPushService pushService;



    @XxlJob(value = "PushAdDataToThird", init = "init", destroy = "destroy")
    public ReturnT<String> pushAdDataToThird(String param) throws Exception {
        try {
            pushService.pushDataToThird(param);
            return ReturnT.SUCCESS;
        }catch (Exception e){
            XxlJobLogger.log("Exception:",e);
            return ReturnT.FAIL;
        }
    }

    @XxlJob(value = "PushAdDataToHs", init = "init", destroy = "destroy")
    public ReturnT<String> pushAdDataToHs(String param) throws Exception {
        try {
            pushService.pushDataToHs(param);
            return ReturnT.SUCCESS;
        }catch (Exception e){
            XxlJobLogger.log("Exception:",e);
            return ReturnT.FAIL;
        }
    }

    public void init(){
        log.info("PushAdDataToThirdJob INIT...");
    }

    public void destroy(){
        log.info("PushAdDataToThirdJob DESTROY...");
    }


}
