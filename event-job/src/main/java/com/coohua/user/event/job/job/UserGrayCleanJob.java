package com.coohua.user.event.job.job;

import com.coohua.user.event.biz.user.service.UserCancelSycnService;
import com.coohua.user.event.biz.util.DingTalkPushUtils;
import com.coohua.user.event.job.service.UserGrayDataCleanService;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import com.xxl.job.core.log.XxlJobLogger;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @since 2022/3/3
 */
@Slf4j
@Component
public class UserGrayCleanJob {

    @Autowired
    private UserGrayDataCleanService userGrayDataCleanService;

    @XxlJob(value = "UserGrayCleanJob", init = "init", destroy = "destroy")
    public ReturnT<String> userGrayJob(String param) throws Exception {
        try {
            userGrayDataCleanService.delMoreThan90();
            return ReturnT.SUCCESS;
        }catch (Exception e){
            XxlJobLogger.log("Exception:",e);
            DingTalkPushUtils.jobErr("UserGrayCleanJob");
            return ReturnT.FAIL;
        }

    }

    public void init(){
        log.info("UserGrayCleanJob INIT...");
    }

    public void destroy(){
        log.info("UserGrayCleanJob DESTROY...");
    }
}
