package com.coohua.user.event.job.service;

import com.alibaba.fastjson.JSON;
import com.coohua.caf.core.util.MD5Util;
import com.coohua.user.event.biz.ecp.entity.AdVideoSource;
import com.coohua.user.event.biz.ecp.service.AdVideoSourceService;
import com.coohua.user.event.biz.toufang.entity.VideoBaseEntity;
import com.coohua.user.event.biz.toufang.mapper.VideoSourceMapper;
import com.coohua.user.event.biz.util.DateUtil;
import com.coohua.user.event.job.util.AliUploadUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.http.HttpResponse;
import org.apache.http.client.HttpClient;
import org.apache.http.client.methods.HttpGet;
import org.apache.http.impl.client.DefaultHttpClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.io.IOException;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.TimeoutException;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2020/11/5
 */
@Slf4j
@Service
public class AdVideoTransferService {

    private static final Integer JUMP_PAGE = 100;

    @Resource
    private VideoSourceMapper videoSourceMapper;
    @Autowired
    private AdVideoSourceService adVideoSourceService;

    public void transfer(){
        Date now = new Date();
        log.info("开始更新广告视频源.....");
        Integer currentAdVideoId = adVideoSourceService.queryVideoIdMax();
        Integer sourceVideoId = videoSourceMapper.selectVideoMaxId();
        sourceVideoId = sourceVideoId == null ? 0 : sourceVideoId;

        if (sourceVideoId > currentAdVideoId){
            if (sourceVideoId - currentAdVideoId > JUMP_PAGE){
                while (currentAdVideoId < sourceVideoId){
                    Integer endIndex = currentAdVideoId + JUMP_PAGE;
                    if (endIndex > sourceVideoId){
                        endIndex = sourceVideoId;
                    }
                   solveVideo(currentAdVideoId,endIndex);
                    currentAdVideoId += JUMP_PAGE;
                }
            }else {
                solveVideo(currentAdVideoId,sourceVideoId);
            }

        }else {
            log.info("[{}] 暂时无新视频数据更新....跳过本次更新", DateUtil.dateToString(now,DateUtil.COMMON_TIME_FORMAT));
        }


        log.info("结束更新广告视频源.....");
    }


    private void solveVideo(Integer currentAdVideoId,Integer sourceVideoId){
        log.info("正在计算{} >>> {} 的数据",currentAdVideoId,sourceVideoId);
        List<VideoBaseEntity> videoBaseEntityList = videoSourceMapper.queryVideoBaseList(currentAdVideoId,sourceVideoId);
        log.info("查询出{}条数据",videoBaseEntityList.size());
        if (videoBaseEntityList.size() > 0){
            int count = this.insertVideoInfo(videoBaseEntityList);
            log.info("本次计算成功插入{} 条视频数据",count);
        }
    }

    private int insertVideoInfo(List<VideoBaseEntity> videoBaseEntityList){
        Date now = new Date();

        Map<Integer,List<VideoBaseEntity>> videoMap = videoBaseEntityList.stream()
                .collect(Collectors.groupingBy(VideoBaseEntity::getVideoId));


        List<AdVideoSource> insertBatch = videoMap.keySet().parallelStream()
                .map(id -> {
                    AdVideoSource adVideoSource = new AdVideoSource();

                    List<VideoBaseEntity> videoBaseEntities = videoMap.get(id);

                    VideoBaseEntity videoBaseEntity = videoBaseEntities.get(0);

                    adVideoSource.setVideoId(videoBaseEntity.getVideoId());
                    adVideoSource.setVideoName(videoBaseEntity.getVideoName());
                    adVideoSource.setAppName(videoBaseEntity.getAppName());
                    adVideoSource.setEditTime(videoBaseEntity.getEditTime());
                    adVideoSource.setEditor(videoBaseEntity.getEditor());
                    adVideoSource.setDirector(videoBaseEntity.getDirector());
                    adVideoSource.setActor(videoBaseEntity.getActor());
                    adVideoSource.setVideoProperty(videoBaseEntity.getVideoProperty());
                    adVideoSource.setVideographer(videoBaseEntity.getVideographer());
                    adVideoSource.setETag(videoBaseEntity.getETag());
                    adVideoSource.setVideoUrl(videoBaseEntity.getVideoUrl());
                    adVideoSource.setOnlineTime(videoBaseEntity.getOnlineTime());
                    adVideoSource.setDeliveryTime(videoBaseEntity.getDeliveryTime());

                    List<String> ideaList = videoBaseEntities.parallelStream().map(VideoBaseEntity::getTitle)
                            .collect(Collectors.toList());
                    adVideoSource.setIdeaInfo(JSON.toJSONString(ideaList));

                    adVideoSource.setCreateTime(now);
                    adVideoSource.setUpdateTime(now);
                    return adVideoSource;
                }).collect(Collectors.toList());

        insertBatch = convertVideoUrl(insertBatch);
        return adVideoSourceService.insertVideoInfo(insertBatch);
    }

    private List<AdVideoSource> convertVideoUrl(List<AdVideoSource> videoBaseEntityList){
        List<AdVideoSource> adVideoSources = new ArrayList<>();
        for (AdVideoSource videoBaseEntity : videoBaseEntityList) {
            try {
                String[] videoInfo = videoBaseEntity.getVideoUrl().replace("https://", "").split("/");
                String newEndUrl = "video_zip/"
                        + UUID.randomUUID().toString().replace("-", "")
                        + MD5Util.md5(videoInfo[videoInfo.length - 1]) + "_zip.mp4";
                log.info("NewNamePath:{}", newEndUrl);
                AliUploadUtil.upload(videoBaseEntity.getVideoUrl(), newEndUrl);
                log.info("transfer url [{}] to newUrl [{}]", videoBaseEntity.getVideoUrl(), newEndUrl);
                videoBaseEntity.setVideoUrl(AliUploadUtil.perUrl + newEndUrl);
                adVideoSources.add(videoBaseEntity);
            } catch (Exception e) {
                log.info("Video {} Ex:", videoBaseEntity.getVideoId(), e);
            }
        }
        return  adVideoSources;
    }

    private static int getHttpCode(String url) {
        HttpClient client = new DefaultHttpClient();
        HttpGet get = new HttpGet(url);
        try {

            HttpResponse response = client.execute(get);
            int code = response.getStatusLine().getStatusCode();
            log.info("{} >>> code :{}",url,code);
            return code;
        } catch (IOException e) {
            e.printStackTrace();
        }
        return 404;
    }

    public void checkVideoStatus(){
        List<AdVideoSource> adVideoSources = adVideoSourceService.list();
        CompletableFuture<Integer>[] completableFutures = new CompletableFuture[adVideoSources.size()];

        for (int i =0 ;i< adVideoSources.size() ; i++){
            AdVideoSource adVideoSource = adVideoSources.get(i);
            completableFutures[i] = CompletableFuture.supplyAsync(() ->{
                int code = getHttpCode(adVideoSource.getVideoUrl().replace("/video/","/video_zip/").replace(".mp4", "_zip.mp4"));
                if (code == 404){
                    return adVideoSource.getVideoId();
                }
                return 0;
            });
        }

        CompletableFuture.allOf(completableFutures);

        List<Integer> idList = new ArrayList<>();
        for (CompletableFuture<Integer> completableFuture : completableFutures){
            try {
                Integer code = completableFuture.get(1,TimeUnit.SECONDS);
                if (code != 0) {
                    idList.add(code);
                }
            } catch (InterruptedException | ExecutionException | TimeoutException e) {
                e.printStackTrace();
            }
        }

        log.info(">>>>>>>>>>> target log :{}",JSON.toJSONString(idList));
    }
}
