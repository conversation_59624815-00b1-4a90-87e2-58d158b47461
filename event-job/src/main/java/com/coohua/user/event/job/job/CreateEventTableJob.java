package com.coohua.user.event.job.job;

import com.coohua.user.event.biz.core.service.CreateTableService;
import com.coohua.user.event.biz.toufang.service.TouFangBaseQueryService;
import com.coohua.user.event.biz.util.DingTalkPushUtils;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import com.xxl.job.core.log.XxlJobLogger;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @since 2020/9/5
 */
@Slf4j
@Component
public class CreateEventTableJob {
    @Autowired
    private CreateTableService createTableService;

    @XxlJob(value = "CreateEventTableJob", init = "init", destroy = "destroy")
    public ReturnT<String> createEventTableJob(String param) throws Exception {
        try {
            createTableService.createTable();
            return ReturnT.SUCCESS;
        }catch (Exception e){
            XxlJobLogger.log("Exception:",e);
            DingTalkPushUtils.jobErr("CreateEventTableJob");
            return ReturnT.FAIL;
        }

    }

    public void init(){
        log.info("CreateEventTableJob INIT...");
    }

    public void destroy(){
        log.info("CreateEventTableJob DESTROY...");
    }
}
