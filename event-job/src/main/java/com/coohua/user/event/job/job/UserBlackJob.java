package com.coohua.user.event.job.job;

import com.coohua.user.event.biz.service.UserBlackService;
import com.coohua.user.event.biz.service.UserGrayService;
import com.coohua.user.event.biz.util.DingTalkPushUtils;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import com.xxl.job.core.log.XxlJobLogger;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;

@Slf4j
@Component
public class UserBlackJob {

    @Autowired
    private UserBlackService userBlackService;

    /**
     * reqId异常拉黑
     * @param param
     * @return
     * @throws Exception
     */
    @XxlJob(value = "userBlackReqIdAbnormal", init = "init", destroy = "destroy")
    public ReturnT<String> userBlackReqIdAbnormal(String param) throws Exception {
        try {
            userBlackService.userBlackReqIdAbnormal();
            return ReturnT.SUCCESS;
        } catch (Exception e) {
            log.error("userBlackReqIdAbnormal err", e);
            XxlJobLogger.log("Exception:", e);
            DingTalkPushUtils.jobErr("userBlackReqIdAbnormal");
            return ReturnT.FAIL;
        }
    }

    @XxlJob(value = "userMultiReqIdAbnormal", init = "init", destroy = "destroy")
    public ReturnT<String> userMultiReqIdAbnormal(String param) throws Exception {
        try {
            userBlackService.userMultiReqIdAbnormal();
            return ReturnT.SUCCESS;
        } catch (Exception e) {
            log.error("userMultiReqIdAbnormal err", e);
            XxlJobLogger.log("Exception:", e);
            DingTalkPushUtils.jobErr("userMultiReqIdAbnormal");
            return ReturnT.FAIL;
        }
    }

    public void init(){
        log.info("UserGrayJob INIT...");
    }

    public void destroy(){
        log.info("UserGrayJob DESTROY...");
    }

}
