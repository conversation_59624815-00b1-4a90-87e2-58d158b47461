package com.coohua.user.event.job.job;

import com.coohua.user.event.biz.core.service.WithdrawNotSendService;
import com.coohua.user.event.biz.service.ManufacturerModelCheckService;
import com.coohua.user.event.biz.service.WithdrawCheckExService;
import com.coohua.user.event.biz.service.WithdrawDelayUserService;
import com.coohua.user.event.biz.service.WithdrawExChannelService;
import com.coohua.user.event.biz.util.DingTalkPushUtils;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import com.xxl.job.core.log.XxlJobLogger;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @since 2023/9/20
 */
@Slf4j
@Component
public class ExChannelRefreshJob {

    @Autowired
    private WithdrawExChannelService withdrawExChannelService;
    @Autowired
    private WithdrawCheckExService withdrawCheckExService;
    @Autowired
    private WithdrawDelayUserService withdrawDelayUserService;
    @Autowired
    private WithdrawNotSendService withdrawNotSendService;
    @Autowired
    private ManufacturerModelCheckService manufacturerModelCheckService;


    @XxlJob(value = "ManufacturerModelRefreshJob", init = "init", destroy = "destroy")
    public ReturnT<String> ManufacturerModelRefreshJob(String param) throws Exception {
        try {
            manufacturerModelCheckService.refreshManufacturerModelListToRedis();
            return ReturnT.SUCCESS;
        }catch (Exception e){
            XxlJobLogger.log("Exception:",e);
            DingTalkPushUtils.jobErr("ManufacturerModelRefreshJob");
            return ReturnT.FAIL;
        }
    }

    @XxlJob(value = "ExChannelRefreshJob", init = "init", destroy = "destroy")
    public ReturnT<String> exChannelRefreshJob(String param) throws Exception {
        try {
            withdrawExChannelService.refreshToRedisExChannel();
            withdrawCheckExService.refreshToRedisExChannelAndModel();
            withdrawCheckExService.refreshToRedisExChannelAndOa();
            //withdrawCheckExService.refreshToRedisHourStopExChannel();
            return ReturnT.SUCCESS;
        }catch (Exception e){
            XxlJobLogger.log("Exception:",e);
            DingTalkPushUtils.jobErr("ExChannelRefreshJob");
            return ReturnT.FAIL;
        }
    }

    @XxlJob(value = "ExWithdrawChannelRefreshJob", init = "init", destroy = "destroy")
    public ReturnT<String> ExWithdrawChannelRefreshJob(String param) throws Exception {
        try {
            withdrawCheckExService.refreshToRedisExChannel();
            withdrawCheckExService.refreshToRedisHourExChannel();
            //withdrawCheckExService.refreshToRedisHourNatureExChannel();
            return ReturnT.SUCCESS;
        }catch (Exception e){
            XxlJobLogger.log("Exception:",e);
            DingTalkPushUtils.jobErr("ExChannelRefreshJob");
            return ReturnT.FAIL;
        }
    }


    @XxlJob(value = "WithdrawDelayUserRefreshJob", init = "init", destroy = "destroy")
    public ReturnT<String> WithdrawDelayUserRefreshJob(String param) throws Exception {
        try {
            withdrawDelayUserService.refreshToRedisWithdrawDelayUser();
            return ReturnT.SUCCESS;
        }catch (Exception e){
            XxlJobLogger.log("Exception:",e);
            DingTalkPushUtils.jobErr("WithdrawDelayUserRefreshJob");
            return ReturnT.FAIL;
        }
    }


    @XxlJob(value = "DeleteLastRejectOrderJob", init = "init", destroy = "destroy")
    public ReturnT<String> deleteLastRejectOrder(String param) throws Exception {
        try {
            withdrawNotSendService.cleanBefore15DaysOrder();
            return ReturnT.SUCCESS;
        }catch (Exception e){
            XxlJobLogger.log("Exception:",e);
            DingTalkPushUtils.jobErr("ExChannelRefreshJob");
            return ReturnT.FAIL;
        }
    }

    public void init(){
        log.info("ExChannelRefreshJob INIT...");
    }

    public void destroy(){
        log.info("ExChannelRefreshJob DESTROY...");
    }
}
