package com.coohua.user.event.job.job;

import com.coohua.user.event.biz.core.service.CreateTableService;
import com.coohua.user.event.biz.service.CoreEventDataService;
import com.coohua.user.event.biz.util.DingTalkPushUtils;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import com.xxl.job.core.log.XxlJobLogger;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @since 2020/9/21
 */
@Slf4j
@Component
public class CoreEventResultJob {

    @Autowired
    private CoreEventDataService coreEventDataService;

    @XxlJob(value = "CoreEventResultJob", init = "init", destroy = "destroy")
    public ReturnT<String> coreEventResultJob(String param) throws Exception {
        try {
            coreEventDataService.doCoreEventProcess(param);
            return ReturnT.SUCCESS;
        }catch (Exception e){
            XxlJobLogger.log("Exception:",e);
            DingTalkPushUtils.jobErr("CoreEventResultJob");
            return ReturnT.FAIL;
        }

    }

    public void init(){
        log.info("CoreEventResultJob INIT...");
    }

    public void destroy(){
        log.info("CoreEventResultJob DESTROY...");
    }
}
