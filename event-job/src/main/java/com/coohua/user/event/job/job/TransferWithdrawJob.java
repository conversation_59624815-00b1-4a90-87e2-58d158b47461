package com.coohua.user.event.job.job;

import com.coohua.user.event.biz.dc.service.HistoryIncomeService;
import com.coohua.user.event.biz.service.CkWithdrawOrderService;
import com.coohua.user.event.biz.service.TransferWithdrawOrderService;
import com.coohua.user.event.biz.util.DateUtil;
import com.coohua.user.event.biz.util.DingTalkPushUtils;
import com.coohua.user.event.biz.util.Strings;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import com.xxl.job.core.log.XxlJobLogger;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Date;

/**
 * <AUTHOR>
 * @since 2020/9/14
 */
@Slf4j
@Component
public class TransferWithdrawJob {

    @Autowired
    private TransferWithdrawOrderService transferWithdrawOrderService;
    @Autowired
    private CkWithdrawOrderService ckWithdrawOrderService;

    @XxlJob(value = "TransferWithdrawJob", init = "init", destroy = "destroy")
    public ReturnT<String> transferWithdrawJob(String param) throws Exception {
        try {
            ckWithdrawOrderService.transferDataToCk(param);
            return ReturnT.SUCCESS;
        }catch (Exception e){
            XxlJobLogger.log("Exception:",e);
            DingTalkPushUtils.jobErr("TransferWithdrawJob");
            return ReturnT.FAIL;
        }

    }

    @XxlJob(value = "TransferWithdrawDailyJob", init = "initDaily", destroy = "destroyDaily")
    public ReturnT<String> transferWithdrawDailyJob(String param) throws Exception {
        try {
            if (Strings.noEmpty(param)){
                String[] dates = param.split(",");
                Date end = DateUtil.stringToDate(dates[1]);
                Date begin = DateUtil.stringToDate(dates[0]);
                if (DateUtil.daysBetween(begin,end) > 3){
                    while (end.after(begin)){
                        Date temp = DateUtil.dateIncreaseByDay(begin,1);
                        XxlJobLogger.log(DateUtil.dateToString(begin)+","+DateUtil.dateToString(temp));
                        ckWithdrawOrderService.transferDataToCkDaily(DateUtil.dateToString(begin)+","+DateUtil.dateToString(temp));
                        begin = temp;
                    }
                }else {
                    ckWithdrawOrderService.transferDataToCkDaily(param);
                }
            }else {
                ckWithdrawOrderService.transferDataToCkDaily(param);
            }
            return ReturnT.SUCCESS;
        }catch (Exception e){
            XxlJobLogger.log("Exception:",e);
            DingTalkPushUtils.jobErr("TransferWithdrawDailyJob");
            return ReturnT.FAIL;
        }

    }

    @XxlJob(value = "TransferHistoryDataToCkDaily", init = "initDaily", destroy = "destroyDaily")
    public ReturnT<String> transferHistoryDataToCkDaily(String param) throws Exception {
        try {
            if (Strings.noEmpty(param)){
                ckWithdrawOrderService.transferHistoryDataToCkDaily(param);
            }
            return ReturnT.SUCCESS;
        }catch (Exception e){
            XxlJobLogger.log("Exception:",e);
            DingTalkPushUtils.jobErr("TransferHistoryDataToCkDaily");
            return ReturnT.FAIL;
        }

    }

    public void init(){
        log.info("TransferWithdrawJobCk INIT...");
    }

    public void initDaily(){
        log.info("TransferWithdrawDailyJobCk INIT...");
    }

    public void destroy(){
        log.info("TransferWithdrawJobCk DESTROY...");
    }

    public void destroyDaily(){
        log.info("TransferWithdrawDailyJobCk DESTROY...");
    }
}
