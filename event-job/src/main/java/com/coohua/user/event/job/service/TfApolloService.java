package com.coohua.user.event.job.service;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.coohua.user.event.biz.click.entity.ActiveToConvertAdv;
import com.coohua.user.event.biz.click.service.CkBatchDailyRepository;
import com.ctrip.framework.apollo.core.ConfigConsts;
import com.ctrip.framework.apollo.openapi.client.ApolloOpenApiClient;
import com.ctrip.framework.apollo.openapi.dto.NamespaceReleaseDTO;
import com.ctrip.framework.apollo.openapi.dto.OpenItemDTO;
import com.xxl.job.core.log.XxlJobLogger;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2022/6/21
 * a
 */
@Slf4j
@Service
public class TfApolloService {

    @Resource(name = "apolloClient")
    private ApolloOpenApiClient client;
    private static final String APP_ID = "core-dispense";
    private static final String AP_GATEWAY_APP_ID = "ap.gateway";
    private static final String ENV = "PRO";

    private static final List<String> AD_KEYS_TO_CLEAR = Arrays.asList(
            "bidding.sdk.skip.map",
            "ad.sdk.bidding.request.user.map",
            "ad.sdk.nocache.stg.user.map"
    );

    @Autowired
    private CkBatchDailyRepository ckBatchDailyRepository;

    public void refreshAdvConfig(){
        OpenItemDTO tencent = apolloGet("tencent.event.actAccount");
        if (tencent != null){
            List<String> rs  = JSONArray.parseArray(tencent.getValue(),String.class);
            List<ActiveToConvertAdv> tencentAdv = rs.stream()
                    .map(r-> new ActiveToConvertAdv(r,"guangdiantong"))
                    .collect(Collectors.toList());
            ckBatchDailyRepository.insertAdvToCk(tencentAdv);
        }
        OpenItemDTO toutiao = apolloGet("ocpc.toutiao.toActive.account");
        if (toutiao != null){
            List<String> rsTT  = JSONArray.parseArray(toutiao.getValue(),String.class);
            List<ActiveToConvertAdv> tencentAdv = rsTT.stream()
                    .map(r-> new ActiveToConvertAdv(r,"toutiao"))
                    .collect(Collectors.toList());
            ckBatchDailyRepository.insertAdvToCk(tencentAdv);
        }
    }

    public OpenItemDTO apolloGet(String key){
        return client.getItem(APP_ID,ENV,ConfigConsts.CLUSTER_NAME_DEFAULT,ConfigConsts.NAMESPACE_APPLICATION,key);
    }

    public OpenItemDTO apolloGet(String appId, String env, String key) {
        return client.getItem(appId, env, ConfigConsts.CLUSTER_NAME_DEFAULT, ConfigConsts.NAMESPACE_APPLICATION, key);
    }

    /**
     * 清除用户跳过广告的配置
     */
    public void clearAdUserSkipPlatform() {
        for (String key : AD_KEYS_TO_CLEAR) {
            apolloClear(AP_GATEWAY_APP_ID, ENV, key);
        }

        apolloPub(AP_GATEWAY_APP_ID, ENV);
    }

    public void apolloPub(String appId, String env){
        NamespaceReleaseDTO namespaceReleaseDTO = new NamespaceReleaseDTO();
        namespaceReleaseDTO.setReleasedBy("apollo");
        namespaceReleaseDTO.setReleaseTitle("REMOTE-"+System.currentTimeMillis());
        namespaceReleaseDTO.setReleaseComment("OPEN-API");
        client.publishNamespace(appId, env,
                ConfigConsts.CLUSTER_NAME_DEFAULT,
                ConfigConsts.NAMESPACE_APPLICATION,
                namespaceReleaseDTO);
    }

    private void apolloClear(String appId, String env, String key){
        XxlJobLogger.log("clear key:{}", key);
        OpenItemDTO openItemDTO = apolloGet(appId, env, key);
        openItemDTO.setValue(JSON.toJSONString(new HashMap<>()));
        apolloPut(appId, env, openItemDTO);
    }

    private void apolloPut(String appId, String env, OpenItemDTO itemDTO) {
        itemDTO.setDataChangeLastModifiedTime(new Date());
        client.createOrUpdateItem(appId, env,
                ConfigConsts.CLUSTER_NAME_DEFAULT,
                ConfigConsts.NAMESPACE_APPLICATION, itemDTO);
    }

}
