package com.coohua.user.event.job.job;

import com.coohua.user.event.biz.click.service.CkTaxAmountService;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import com.xxl.job.core.log.XxlJobLogger;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Slf4j
@Component
public class TaxAmountJob {

    @Autowired
    private CkTaxAmountService ckTaxAmountService;

    @XxlJob(value = "taxAmount", init = "init", destroy = "destroy")
    public ReturnT<String> taxAmount(String param) throws Exception {
        try {
            ckTaxAmountService.taxAmount();
            return ReturnT.SUCCESS;
        }catch (Exception e){
            XxlJobLogger.log("Exception:",e);
            log.error("taxAmount error", e);
            return ReturnT.FAIL;
        }



    }

    public void init(){
        log.info("SkipPlatformCheckJob INIT...");
    }

    public void destroy(){
        log.info("SkipPlatformCheckJob DESTROY...");
    }
}
