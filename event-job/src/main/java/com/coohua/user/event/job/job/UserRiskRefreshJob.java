package com.coohua.user.event.job.job;

import com.coohua.user.event.biz.service.UserRiskService;
import com.coohua.user.event.biz.util.DingTalkPushUtils;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import com.xxl.job.core.log.XxlJobLogger;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @since 2021/9/3
 */
@Slf4j
@Component
public class UserRiskRefreshJob {
    @Autowired
    private UserRiskService userRiskService;

    @XxlJob(value = "UserRiskRefresh", init = "init", destroy = "destroy")
    public ReturnT<String> userRiskRefresh(String param) throws Exception {
        try {
            userRiskService.refreshAllUserRiskIntoRedis();
            return ReturnT.SUCCESS;
        }catch (Exception e){
            XxlJobLogger.log("Exception:",e);
            DingTalkPushUtils.jobErr("UserRiskRefreshJob");
            return ReturnT.FAIL;
        }

    }

    @XxlJob(value = "UserExConfigRefresh", init = "init", destroy = "destroy")
    public ReturnT<String> userExConfigRefresh(String param) throws Exception {
        try {
            userRiskService.refreshAllUserLimitIntoRedis();
            return ReturnT.SUCCESS;
        }catch (Exception e){
            XxlJobLogger.log("Exception:",e);
            DingTalkPushUtils.jobErr("UserExConfigRefreshJob");
            return ReturnT.FAIL;
        }

    }

    @XxlJob(value = "UserExIpRefresh", init = "init", destroy = "destroy")
    public ReturnT<String> userExIpRefresh(String param) throws Exception {
        try {
            userRiskService.refreshAllExIpUser(param);
            return ReturnT.SUCCESS;
        }catch (Exception e){
            XxlJobLogger.log("Exception:",e);
            DingTalkPushUtils.jobErr("UserExConfigRefreshJob");
            return ReturnT.FAIL;
        }

    }

    public void init(){
        log.info("UserRiskRefreshJob INIT...");
    }

    public void destroy(){
        log.info("UserRiskRefreshJob DESTROY...");
    }
}
