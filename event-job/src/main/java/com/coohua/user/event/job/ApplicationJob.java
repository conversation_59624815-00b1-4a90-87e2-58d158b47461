package com.coohua.user.event.job;

import com.coohua.caf.core.apollo.EnableAutoChangeApolloConfig;
import com.ctrip.framework.apollo.spring.annotation.EnableApolloConfig;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.EnableAutoConfiguration;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.autoconfigure.jdbc.DataSourceAutoConfiguration;
import org.springframework.boot.autoconfigure.jdbc.DataSourceTransactionManagerAutoConfiguration;
import org.springframework.cache.annotation.EnableCaching;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.scheduling.annotation.EnableScheduling;
import top.zrbcool.pepper.boot.jediscluster.EnableJedisClusterClient;
import top.zrbcool.pepper.boot.motan.EnableMotan;


@EnableApolloConfig(value = {
        "application",
        "caf.log.level",
        "caf.base.registry",
        "ad.base.health",
        "bp.user.rpc.referer",
        "ap.redis.cluster",
        "user-event-sub-db",
        "ad.user-event.redis",
        "bp.redis.user"
})
@EnableAutoChangeApolloConfig
@EnableScheduling
@EnableCaching
@ComponentScan(basePackages = {"com.coohua.user.event.job","com.coohua.user.event.biz"})
@EnableMotan(namespace = "bp-user")
@EnableMotan(namespace = "bp-account")
@EnableJedisClusterClient(namespace = "ap-cluster")
@EnableJedisClusterClient(namespace = "user-event")
@EnableJedisClusterClient(namespace = "bp-user")
@SpringBootApplication(exclude = { DataSourceAutoConfiguration.class, DataSourceTransactionManagerAutoConfiguration.class })
public class ApplicationJob {

    public static void main(String[] args) {
        SpringApplication.run(ApplicationJob.class, args);
    }

}
