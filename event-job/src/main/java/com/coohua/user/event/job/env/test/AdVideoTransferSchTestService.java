package com.coohua.user.event.job.env.test;

import com.coohua.user.event.job.service.AdVideoTransferService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @since 2020/11/5
 */
@Slf4j
@Service
@ConditionalOnProperty(name = "env",havingValue = "fat")
public class AdVideoTransferSchTestService {


    @Autowired
    private AdVideoTransferService adVideoTransferService;

    @Scheduled(cron = "0 30 0 * * ?")
    public void transfer(){
        log.info("CURRENT ENV TEST.... DO VIDEO TRANSFER");
        adVideoTransferService.transfer();
    }
}
