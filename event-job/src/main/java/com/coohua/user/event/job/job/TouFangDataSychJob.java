package com.coohua.user.event.job.job;

import com.coohua.user.event.biz.dc.service.HistoryIncomeService;
import com.coohua.user.event.biz.dc.service.TouFangOfflineService;
import com.coohua.user.event.biz.util.DingTalkPushUtils;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import com.xxl.job.core.log.XxlJobLogger;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @since 2020/9/8
 */
@Slf4j
@Component
public class TouFangDataSychJob {

    @Autowired
    private TouFangOfflineService touFangOfflineService;

    @XxlJob(value = "sychToufangData", init = "init", destroy = "destroy")
    public ReturnT<String> sychToufangData(String param) throws Exception {
        try {
            return ReturnT.SUCCESS;
        }catch (Exception e){
            XxlJobLogger.log("Exception:",e);
            DingTalkPushUtils.jobErr("sychToufangData");
            return ReturnT.FAIL;
        }

    }

    @XxlJob(value = "sychToufangDataOffline", init = "init", destroy = "destroy")
    public ReturnT<String> sychToufangDataOffline(String param) throws Exception {
        try {
            touFangOfflineService.sychToufangDataOffline(param);
            return ReturnT.SUCCESS;
        }catch (Exception e){
            XxlJobLogger.log("Exception:",e);
            DingTalkPushUtils.jobErr("sychToufangDataOffline");
            return ReturnT.FAIL;
        }

    }

    public void init(){
        log.info("TouFangDataSychJob INIT...");
    }

    public void destroy(){
        log.info("TouFangDataSychJob DESTROY...");
    }


}
