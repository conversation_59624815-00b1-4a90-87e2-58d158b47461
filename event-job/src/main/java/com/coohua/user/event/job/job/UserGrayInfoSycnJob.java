package com.coohua.user.event.job.job;

import com.coohua.user.event.biz.click.entity.UserGrayModelEntity;
import com.coohua.user.event.biz.user.service.UserCancelSycnService;
import com.coohua.user.event.biz.util.DingTalkPushUtils;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import com.xxl.job.core.log.XxlJobLogger;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @since 2022/2/24
 */
@Slf4j
@Component
public class UserGrayInfoSycnJob {
    @Autowired
    private UserCancelSycnService userCancelSycnService;

    @XxlJob(value = "UserGrayInfoSycnJob", init = "init", destroy = "destroy")
    public ReturnT<String> userGrayJob(String param) throws Exception {
        try {
            userCancelSycnService.trans();
            return ReturnT.SUCCESS;
        }catch (Exception e){
            XxlJobLogger.log("Exception:",e);
            DingTalkPushUtils.jobErr("UserGrayJob");
            return ReturnT.FAIL;
        }

    }

    public void init(){
        log.info("UserGrayInfoSycnJob INIT...");
    }

    public void destroy(){
        log.info("UserGrayInfoSycnJob DESTROY...");
    }
}
