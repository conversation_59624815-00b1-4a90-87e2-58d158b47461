package com.coohua.user.event.job.service;

import com.alibaba.fastjson.JSON;
import com.coohua.user.event.biz.click.service.ClickHouseService;
import com.coohua.user.event.biz.util.Lists;
import com.coohua.user.event.biz.util.RedisUtil;
import com.coohua.user.event.biz.util.Strings;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import top.zrbcool.pepper.boot.jediscluster.JedisClusterClient;
import top.zrbcool.pepper.boot.jediscluster.JedisClusterClientRefer;

import javax.annotation.PostConstruct;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2021/10/9
 */
@Slf4j
@Service
public class GrayIpService {

    @JedisClusterClientRefer(namespace = "bp-user")
    private JedisClusterClient jedisClusterClient;

    @Autowired
    private ClickHouseService clickHouseService;

    public void setGrayedIp(){
        log.info(">>>> [开始]更新异常已拉黑IP....");
        String grayIpStr = clickHouseService.queryGrayIpList();
        if (Strings.noEmpty(grayIpStr)){
            List<String> grayedIpList = JSON.parseArray(grayIpStr,String.class);
            if (Lists.noEmpty(grayedIpList)){
                grayedIpList.forEach(grayIp ->{
                    String key = RedisUtil.buildGrayedIpKey(grayIp);
                    jedisClusterClient.set(key,"true");
                    // 七日过期
                    jedisClusterClient.expire(key,60 * 60 * 24 *7);
                });
            }
        }
        log.info(">>>> [完成]更新异常已拉黑IP....");
    }
}
