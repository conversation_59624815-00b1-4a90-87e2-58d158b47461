package com.coohua.user.event.job.job;

import com.coohua.user.event.biz.service.WithDrawAlertService;
import com.coohua.user.event.biz.util.DingTalkPushUtils;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import com.xxl.job.core.log.XxlJobLogger;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

@Slf4j
@Component
public class WithDrawAlertJob {
    @Resource
    private WithDrawAlertService withDrawAlertService;

    @XxlJob(value = "WithDrawRule88Job", init = "init", destroy = "destroy")
    public ReturnT<String> WithDrawRule88Job(String param) throws Exception {
        try {
            Integer minInterceptNum = 100;
            if (StringUtils.isNotBlank(param)) {
                minInterceptNum = Integer.valueOf(param);
            }
            withDrawAlertService.withDrawRule88Alert(minInterceptNum);
            return ReturnT.SUCCESS;
        } catch (Exception e) {
            log.error("WithDrawRule88Job err", e);
            XxlJobLogger.log("Exception:", e);
            DingTalkPushUtils.jobErr("WithDrawRule88Job");
            return ReturnT.FAIL;
        }
    }


    public void init() {
        log.info("UserIncomeAbnormalRefreshJob INIT...");
    }

    public void destroy() {
        log.info("UserIncomeAbnormalRefreshJob DESTROY...");
    }

}
