package com.coohua.user.event.job.util;

import com.aliyun.oss.*;
import lombok.extern.slf4j.Slf4j;
//import org.bytedeco.javacpp.avcodec;
//import org.bytedeco.javacpp.avutil;
//import org.bytedeco.javacv.FFmpegFrameGrabber;
//import org.bytedeco.javacv.FFmpegFrameRecorder;
//import org.bytedeco.javacv.Frame;

import java.io.*;
import java.net.URL;

@Slf4j
public class AliUploadUtil {

    // 内网域名
    public final static String perInnerUrl = "http://ad-video-coohua.oss-cn-beijing-internal.aliyuncs.com/";

    // 外网
    public final static String perUrl = "https://ad-video.coohua.com/";

    // OSS-CLIENT
    private final static String endpoint = "http://oss-cn-beijing-internal.aliyuncs.com";
    private final static String accessKeyId = "LTAI4GEK5WrenZvpZ669SE9y";
    private final static String accessKeySecret = "******************************";
    private final static String bucketName = "ad-video-coohua";


    private static final int FRAME_RATE = 75;

    public static void upload(String url,String name) {
        OSS ossClient = new OSSClientBuilder().build(endpoint, accessKeyId, accessKeySecret);
        long start = System.currentTimeMillis();
        String perFix = "/app/coohua/";
        File dir = new File(perFix + "video_zip");
        if (!dir.exists()) {// 判断目录是否存在
            log.info("目录不存在....创建");
            dir.mkdir();
        }
        String fileBase = perFix + name;
        log.info("上传暂存路径 {}",fileBase);
        File file = new File(fileBase);
        try {
            InputStream inputStream = new URL(url).openStream();

            OutputStream os = new FileOutputStream(file);
            int len = 0;
            byte[] buffer = new byte[8192];
            while ((len = inputStream.read(buffer)) != -1) {
                os.write(buffer, 0, len);
            }
//            String fileName = convert(file);
            String fileName = "";
            inputStream = new FileInputStream(fileName);

            ossClient.putObject(bucketName,name, inputStream);
        } catch (Throwable oe) {
            log.error("", oe);
            throw new RuntimeException("", oe);
        } finally {
            ossClient.shutdown();
            file.delete();
            try {
                String editFileBase = fileBase.replace(".mp4","_edited.mp4" );
                log.info("编辑暂存路径 {}",editFileBase);
                File editFile = new File(editFileBase);
                editFile.delete();
            }catch (Exception e){
                log.error("删除临时压缩视频异常：",e);
            }

        }
        log.info("上传耗时 {}ms",System.currentTimeMillis() - start);
    }

//    public static String convert(File file) {
//
//        FFmpegFrameGrabber frameGrabber = new FFmpegFrameGrabber(file.getAbsolutePath());
//        String fileName = null;
//
//        Frame captured_frame = null;
//
//        FFmpegFrameRecorder recorder = null;
//
//        try {
//            frameGrabber.start();
//            fileName = file.getAbsolutePath().replace(".mp4", "_edited.mp4");
//            log.info("wight:{},height:{}",frameGrabber.getImageWidth(), frameGrabber.getImageHeight());
//
//            int height = frameGrabber.getImageHeight();
//            int widht = frameGrabber.getImageWidth();
//
//            recorder = new FFmpegFrameRecorder(fileName, widht, height, frameGrabber.getAudioChannels());
//            recorder.setFrameRate(FRAME_RATE);
//
//            recorder.setSampleRate(frameGrabber.getSampleRate());
//            // 生成速率降低换取高画质
//            recorder.setVideoOption("preset", "slower");
//            // 调整视频画质
//            recorder.setVideoOption("crf", "30");
//            recorder.setVideoOption("threads", "4");
//            recorder.setPixelFormat(avutil.AV_PIX_FMT_YUV420P);
//            recorder.setVideoCodec(avcodec.AV_CODEC_ID_H264);
//            recorder.setAudioCodec(avcodec.AV_CODEC_ID_AAC);
//            recorder.setFormat("mp4");
//
//            recorder.start();
//
//            while (true) {
//                try {
//                    captured_frame = frameGrabber.grabFrame();
//                    if (captured_frame == null) {
//                        log.info("!!! end cvQueryFrame");
//                        break;
//                    }
//                    recorder.setTimestamp(frameGrabber.getTimestamp());
//                    recorder.record(captured_frame);
//                } catch (Exception e) {
//                }
//            }
//            recorder.stop();
//            recorder.release();
//            frameGrabber.stop();
//        } catch (Exception e) {
//            e.printStackTrace();
//        }
//        return fileName;
//    }


}
