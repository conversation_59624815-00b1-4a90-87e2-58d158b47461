package com.coohua.user.event.job.config;

import com.ctrip.framework.apollo.openapi.client.ApolloOpenApiClient;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * <AUTHOR>
 * @since 2022/6/21
 */
@Slf4j
@Configuration
public class ApolloOpenApiConfig {

    @Bean(name = "apolloClient")
    public ApolloOpenApiClient initBean(){
        String portalUrl = "http://*************:8499"; // portal url
        String token = "0681906e88e65b622931616ef17233dc1f3644dd"; // 申请的token
        ApolloOpenApiClient client = ApolloOpenApiClient.newBuilder()
                .withPortalUrl(portalUrl)
                .withToken(token)
                .build();
        log.info("Apollo Bean Init...");
        return client;
    }
}
