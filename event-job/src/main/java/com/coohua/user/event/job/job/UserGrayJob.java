package com.coohua.user.event.job.job;

import com.coohua.user.event.biz.service.UserEcpmCheckService;
import com.coohua.user.event.biz.service.UserGrayService;
import com.coohua.user.event.biz.util.DingTalkPushUtils;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import com.xxl.job.core.log.XxlJobLogger;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @since 2021/8/5
 */
@Slf4j
@Component
public class UserGrayJob {

    @Autowired
    private UserGrayService userGrayService;
    @Autowired
    private UserEcpmCheckService userEcpmCheckService;

    @XxlJob(value = "UserGrayJob", init = "init", destroy = "destroy")
    public ReturnT<String> userGrayJob(String param) throws Exception {
        try {
            userGrayService.runGray(param);
            return ReturnT.SUCCESS;
        }catch (Exception e){
            XxlJobLogger.log("Exception:",e);
            DingTalkPushUtils.jobErr("UserGrayJob");
            return ReturnT.FAIL;
        }

    }

    @XxlJob(value = "UserGrayScoreJob", init = "init", destroy = "destroy")
    public ReturnT<String> userGrayJobFx(String param) throws Exception {
        try {
            userGrayService.doUserGray();
            return ReturnT.SUCCESS;
        }catch (Exception e){
            XxlJobLogger.log("Exception:",e);
            DingTalkPushUtils.jobErr("UserGrayScoreJob");
            return ReturnT.FAIL;
        }

    }

    @XxlJob(value = "UserGrayScoreJobSub", init = "init", destroy = "destroy")
    public ReturnT<String> UserGrayScoreJobSub(String param) throws Exception {
        try {
            userGrayService.userGraySub();
            return ReturnT.SUCCESS;
        }catch (Exception e){
            log.error("UserGrayScoreJobSub err", e);
            XxlJobLogger.log("Exception:",e);
            DingTalkPushUtils.jobErr("UserGrayScoreJobSub");
            return ReturnT.FAIL;
        }
    }


    @XxlJob(value = "UserGrayScoreJobSub3", init = "init", destroy = "destroy")
    public ReturnT<String> UserGrayScoreJobSub3(String param) throws Exception {
        try {
            userGrayService.userGraySub3();
            return ReturnT.SUCCESS;
        }catch (Exception e){
            log.error("UserGrayScoreJobSub3 err", e);
            XxlJobLogger.log("Exception:",e);
            DingTalkPushUtils.jobErr("UserGrayScoreJobSub3");
            return ReturnT.FAIL;
        }
    }

    @XxlJob(value = "UserGrayScoreJobSub4", init = "init", destroy = "destroy")
    public ReturnT<String> UserGrayScoreJobSub4(String param) throws Exception {
        try {
            userGrayService.userGraySub4();
            return ReturnT.SUCCESS;
        }catch (Exception e){
            log.error("UserGrayScoreJobSub4 err", e);
            XxlJobLogger.log("Exception:",e);
            DingTalkPushUtils.jobErr("UserGrayScoreJobSub4");
            return ReturnT.FAIL;
        }
    }

    @XxlJob(value = "UserGrayIosNatural", init = "init", destroy = "destroy")
    public ReturnT<String> UserGrayIosNatural(String param) throws Exception {
        try {
            userGrayService.UserGrayIosNatural();
            return ReturnT.SUCCESS;
        } catch (Exception e) {
            log.error("UserGrayIosNatural err", e);
            XxlJobLogger.log("Exception:", e);
            DingTalkPushUtils.jobErr("UserGrayIosNatural");
            return ReturnT.FAIL;
        }
    }


    @XxlJob(value = "UserGrayWithdrawCheckJob", init = "init", destroy = "destroy")
    public ReturnT<String> UserGrayWithdrawCheckJob(String param) throws Exception {
        try {
            userGrayService.grayHsExUser();
            return ReturnT.SUCCESS;
        }catch (Exception e){
            XxlJobLogger.log("Exception:",e);
            DingTalkPushUtils.jobErr("UserGrayWithdrawCheckJob");
            return ReturnT.FAIL;
        }

    }

    @XxlJob(value = "UserGrayRateCheck", init = "init", destroy = "destroy")
    public ReturnT<String> userGrayRateCheck(String param) throws Exception {
        try {
            userGrayService.checkGrayRate();
            return ReturnT.SUCCESS;
        }catch (Exception e){
            XxlJobLogger.log("Exception:",e);
            DingTalkPushUtils.jobErr("UserGrayRateCheck");
            return ReturnT.FAIL;
        }
    }

    @XxlJob(value = "userGrayECPMCheck", init = "init", destroy = "destroy")
    public ReturnT<String> userGrayECPMCheck(String param) throws Exception {
        try {
            userGrayService.runEcpmCheck();
            return ReturnT.SUCCESS;
        }catch (Exception e){
            XxlJobLogger.log("Exception:",e);
            DingTalkPushUtils.jobErr("userGrayECPMCheck");
            return ReturnT.FAIL;
        }
    }


    @XxlJob(value = "userGrayChannelEcpmCheck", init = "init", destroy = "destroy")
    public ReturnT<String> userGrayChannelEcpmCheck(String param) throws Exception {
        try {
            log.info(">>> Start userGrayChannelEcpmCheck");
            userEcpmCheckService.checkEcpm();
            log.info(">>> End userGrayChannelEcpmCheck");
            return ReturnT.SUCCESS;
        }catch (Exception e){
            XxlJobLogger.log("Exception:",e);
            DingTalkPushUtils.jobErr("userGrayChannelEcpmCheck");
            return ReturnT.FAIL;
        }
    }

    @XxlJob(value = "delSkipUser", init = "init", destroy = "destroy")
    public ReturnT<String> delSkipUser(String param) throws Exception {
        try {
            log.info(">>> Start delSkipUser");
            long l = userEcpmCheckService.delSkipUser();
            XxlJobLogger.log("delSkipUser:", l);
            log.info(">>> End delSkipUser");
            return ReturnT.SUCCESS;
        }catch (Exception e){
            log.error("delSkipUser err", e);
            return ReturnT.FAIL;
        }
    }



    @XxlJob(value = "userGrayChannelEcpmCheckSub", init = "init", destroy = "destroy")
    public ReturnT<String> userGrayChannelEcpmCheckSub(String param) throws Exception {
        try {
            log.info(">>> Start userGrayChannelEcpmCheckSub");
            userEcpmCheckService.checkEcpmDr();
            log.info(">>> End userGrayChannelEcpmCheckSub");
            return ReturnT.SUCCESS;
        }catch (Exception e){
            log.error("UserGrayScoreJobSub3 err", e);
            XxlJobLogger.log("Exception:",e);
            DingTalkPushUtils.jobErr("userGrayChannelEcpmCheckSub");
            return ReturnT.FAIL;
        }
    }

    @XxlJob(value = "userCheckGdtGap", init = "init", destroy = "destroy")
    public ReturnT<String> userCheckGdtGap(String param) throws Exception {
        try {
            log.info(">>> Start userCheckGdtGap");
            userEcpmCheckService.checkGdtCallBack();
            log.info(">>> End userCheckGdtGap");
            return ReturnT.SUCCESS;
        }catch (Exception e){
            XxlJobLogger.log("Exception:",e);
            DingTalkPushUtils.jobErr("userCheckGdtGap");
            return ReturnT.FAIL;
        }
    }


    public void init(){
        log.info("UserGrayJob INIT...");
    }

    public void destroy(){
        log.info("UserGrayJob DESTROY...");
    }
}
