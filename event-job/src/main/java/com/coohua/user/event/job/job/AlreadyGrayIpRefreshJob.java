package com.coohua.user.event.job.job;

import com.coohua.user.event.biz.util.DingTalkPushUtils;
import com.coohua.user.event.job.service.GrayIpService;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import com.xxl.job.core.log.XxlJobLogger;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @since 2021/10/9
 */
@Slf4j
@Component
public class AlreadyGrayIpRefreshJob {

    @Autowired
    private GrayIpService grayIpService;

    @XxlJob(value = "AlreadyGrayIpRefreshJob", init = "init", destroy = "destroy")
    public ReturnT<String> alreadyGrayIpRefreshJob(String param) throws Exception {
        try {
            grayIpService.setGrayedIp();
            return ReturnT.SUCCESS;
        }catch (Exception e){
            XxlJobLogger.log("Exception:",e);
            DingTalkPushUtils.jobErr("AlreadyGrayIpRefreshJob");
            return ReturnT.FAIL;
        }
    }

    public void init(){
        log.info("AlreadyGrayIpRefreshJob INIT...");
    }

    public void destroy(){
        log.info("AlreadyGrayIpRefreshJob DESTROY...");
    }
}
