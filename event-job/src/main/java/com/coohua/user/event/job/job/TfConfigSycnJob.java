package com.coohua.user.event.job.job;

import com.coohua.user.event.biz.dc.service.TouFangOfflineService;
import com.coohua.user.event.biz.util.DingTalkPushUtils;
import com.coohua.user.event.job.service.TfApolloService;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import com.xxl.job.core.log.XxlJobLogger;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;

/**
 * <AUTHOR>
 * @since 2024/3/19
 */
@Slf4j
@Component
public class TfConfigSycnJob {

    @Autowired
    private TfApolloService tfApolloService;

    @XxlJob(value = "sychActiveToConvertConfig", init = "init", destroy = "destroy")
    public ReturnT<String> sychToufangData(String param) throws Exception {
        try {
            tfApolloService.refreshAdvConfig();
            return ReturnT.SUCCESS;
        }catch (Exception e) {
            XxlJobLogger.log("Exception:", e);
            DingTalkPushUtils.jobErr("sychActiveToConvertConfig");
            return ReturnT.FAIL;
        }
    }

    @XxlJob(value = "clearAdUserSkipPlatform", init = "init", destroy = "destroy")
    public ReturnT<String> clearAdUserSkipPlatform(String param) throws Exception {
        try {
            tfApolloService.clearAdUserSkipPlatform();
            return ReturnT.SUCCESS;
        }catch (Exception e) {
            XxlJobLogger.log("Exception:", e);
            DingTalkPushUtils.jobErr("sychActiveToConvertConfig");
            return ReturnT.FAIL;
        }
    }

    public void init(){
        log.info("TfConfigSycnJob INIT...");
    }

    public void destroy(){
        log.info("TfConfigSycnJob DESTROY...");
    }
}
