package com.coohua.user.event.job.job;

import com.coohua.user.event.biz.toufang.service.TouFangBaseQueryService;
import com.coohua.user.event.biz.user.service.BaseQueryService;
import com.coohua.user.event.biz.util.DingTalkPushUtils;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import com.xxl.job.core.log.XxlJobLogger;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @since 2020/9/3
 */
@Slf4j
@Component
public class BpTouFangReportJob {

    @Autowired
    private TouFangBaseQueryService touFangBaseQueryService;

    @XxlJob(value = "BpTouFangReportJob", init = "init", destroy = "destroy")
    public ReturnT<String> bpAmountReportJob(String param) throws Exception {
        try {
            touFangBaseQueryService.syToResult(param);
            return ReturnT.SUCCESS;
        }catch (Exception e){
            XxlJobLogger.log("Exception:",e);
            DingTalkPushUtils.jobErr("BpTouFangReportJob");
            return ReturnT.FAIL;
        }

    }

    public void init(){
        log.info("BpTouFangReportJob INIT...");
    }

    public void destroy(){
        log.info("BpTouFangReportJob DESTROY...");
    }
}
