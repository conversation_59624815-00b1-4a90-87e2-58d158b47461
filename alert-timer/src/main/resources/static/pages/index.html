<!doctype html>
<html>
<head>
    <meta charset="utf-8">
    <title>监控预警平台</title>
    <meta name="viewport" content="width=device-width,initial-scale=1,user-scalable=0">
    <link rel="stylesheet" href="css/weui.css"/>
    <link rel="stylesheet" href="css/weuix.css"/>
    <link rel="icon" href="favicon.png">
    <link rel="icon" href="favicon.ico">
    <script src="js/zepto.min.js"></script>
    <script src="js/eruda.js"></script>
    <script src="https://g.alicdn.com/dingding/dingtalk-jsapi/2.10.3/dingtalk.open.js"></script>
    <meta name="wpk-bid" content="dta_1_**********">
    <script>
        !(function(c,i,e,b){var h=i.createElement("script");
            var f=i.getElementsByTagName("script")[0];
            h.type="text/javascript";
            h.crossorigin=true;
            h.onload=function(){c[b]||(c[b]=new c.wpkReporter({bid:"dta_1_**********"}));
                c[b].installAll()};
            f.parentNode.insertBefore(h,f);
            h.src=e})(window,document,"https://g.alicdn.com/woodpeckerx/jssdk??wpkReporter.js","__wpk");
    </script>
    <script>

        $(function () {
            var nowTime = new Date().getTime()/1000;
            var before30Min = nowTime-30*60;

            //https://sls.console.aliyun.com/lognext/project/service-log4j2/logsearch/pro?encode=base64&endTime=**********&queryString=bGV2ZWw6IEVSUk9SIGFuZCAoX19zb3VyY2VfXzpicC11c2VyLXNlcnZpY2Ugb3IgX19zb3VyY2VfXzogYnAtbWFsbC1zZXJ2aWNlIG9yIF9fc291cmNlX186IGJwLWFjY291bnQtc2VydmljZSBvciBfX3NvdXJjZV9fOiBjb3JlLWRpc3BlbnNlIG9yIF9fc291cmNlX186ZGF0YS1hZ2VudC1zZXJ2aWNlIG9yIF9fc291cmNlX186YXAuKiBvciBfX3NvdXJjZV9fOnVzZXItZXZlbnQgKSB8IFNFTEVDVCBfX3NvdXJjZV9fIGFzIHNlcnZpY2UsIGNvdW50KCopIGFzIGNvdW50IGdyb3VwIGJ5IF9fc291cmNlX18gb3JkZXIgYnkgY291bnQgREVTQw%3D%3D&queryTimeType=99&startTime=**********
            $("#slsA").attr("href","https://sls.console.aliyun.com/lognext/project/service-log4j2/logsearch/pro?encode=base64&endTime="+nowTime+"&queryString=bGV2ZWw6IEVSUk9SIGFuZCAoX19zb3VyY2VfXzpicC11c2VyLXNlcnZpY2Ugb3IgX19zb3VyY2VfXzogYnAtbWFsbC1zZXJ2aWNlIG9yIF9fc291cmNlX186IGJwLWFjY291bnQtc2VydmljZSBvciBfX3NvdXJjZV9fOiBjb3JlLWRpc3BlbnNlIG9yIF9fc291cmNlX186ZGF0YS1hZ2VudC1zZXJ2aWNlIG9yIF9fc291cmNlX186YXAuKiBvciBfX3NvdXJjZV9fOnVzZXItZXZlbnQgKSB8IFNFTEVDVCBfX3NvdXJjZV9fIGFzIHNlcnZpY2UsIGNvdW50KCopIGFzIGNvdW50IGdyb3VwIGJ5IF9fc291cmNlX18gb3JkZXIgYnkgY291bnQgREVTQw%3D%3D&queryTimeType=99&startTime="+before30Min+"&accounttraceid=e91e9d383d2c47eab3e07e5086ff8e16rinu");

            var corpCgId = "ding15d4680ae95d85a6ffe93478753d9884";
            var dingData = {};
            $.ajax({
                url: "/alert/dingding/auth",
                type: "GET",
                success: function (rs) {
                    if (rs.status == 200) {
                        console.log(rs.data)
                        dingData = rs.data;

                        dd.config({
                            agentId: '**********', // 必填，微应用ID
                            corpId: corpCgId,//必填，企业ID
                            timeStamp: dingData.timeStamp, // 必填，生成签名的时间戳
                            nonceStr: dingData.nonceStr, // 必填，自定义固定字符串。
                            signature: dingData.signature, // 必填，签名
                            type: 0,   //选填。0表示微应用的jsapi,1表示服务窗的jsapi；不填默认为0。该参数从dingtalk.js的0.8.3版本开始支持
                            jsApiList: [
                                'runtime.info',
                                'biz.contact.choose',
                                'device.notification.confirm',
                                'device.notification.alert',
                                'device.notification.prompt',
                                'biz.ding.post',
                                'biz.util.openLink',
                            ] // 必填，需要使用的jsapi列表，注意：不要带dd。
                        });
                    }
                }
            });


            // dd.device.base.getPhoneInfo({
            //     onSuccess: function (data) {
            //         alert("data.model=" + JSON.stringify(data))
            //     },
            //     onFail: function (err) {
            //         alert(JSON.stringify(err))
            //     }
            // });
            //
            // dd.device.base.getUUID({
            //     onSuccess: function (data) {
            //         alert("data.uuid=" + JSON.stringify(data))
            //     },
            //     onFail: function (err) {
            //         alert(JSON.stringify(err))
            //     }
            // });


            dd.error(function (err) {
                // alert('dd error: ' + JSON.stringify(err));
            })//该方法必须带上，用来捕获鉴权出现的异常信息，否则不方便排查出现的问题

            dd.ready(function() {
                dd.runtime.permission.requestAuthCode({
                    corpId: corpCgId, // 企业id
                    onSuccess: function (info) {
                        code = info.code // 通过该免登授权码可以获取用户身份
                        var reqUrl = "/alert/login?code="+code;
                        $.ajax({
                            url: reqUrl,
                            type: "GET",
                            success: function (rs) {
                                // alert( "js="+JSON.stringify(rs));
                                if (rs.status == 200) {
                                    // alert('dd ff: ' + JSON.stringify(rs.data));
                                }
                            }
                        });
                    }});
            });


            $('.collapse .js-category').click(function () {
                $parent = $(this).parent('li');
                if ($parent.hasClass('js-show')) {
                    $parent.removeClass('js-show');
                    $(this).children('i').removeClass('icon-35').addClass('icon-74');
                } else {
                    $parent.siblings().removeClass('js-show');
                    $parent.addClass('js-show');
                    $(this).children('i').removeClass('icon-74').addClass('icon-35');
                    $parent.siblings().find('i').removeClass('icon-35').addClass('icon-74');
                }
            });
        });


    </script>
</head>

<body ontouchstart>
<div class="contianer page-bg">
    <div class="page-hd">
        <h1 class="page-hd-title">
            监控预警平台
        </h1>
    </div>
    <div class="page-bd">
        <ul class="collapse">
            <li>
                <div class="weui-flex js-category">
                    <div class="weui-flex__item">superset</div>
                    <i class="icon icon-74"></i>
                </div>
                <div class="page-category js-categoryInner">
                    <div class="weui-cells page-category-content">
                        <a class="weui-cell weui-cell_access" href="http://superset.coohua-inc.com:8088/superset/dashboard/1/">
                            <div class="weui-cell__bd">
                                <p>admin k413</p>
                            </div>
                            <div class="weui-cell__ft"></div>
                        </a>
                        <a class="weui-cell weui-cell_access"  id="slsA" href="https://sls.console.aliyun.com/lognext/project/service-log4j2/logsearch/pro?encode=base64&endTime=**********&queryString=bGV2ZWw6IEVSUk9SIGFuZCAoX19zb3VyY2VfXzpicC11c2VyLXNlcnZpY2Ugb3IgX19zb3VyY2VfXzogYnAtbWFsbC1zZXJ2aWNlIG9yIF9fc291cmNlX186IGJwLWFjY291bnQtc2VydmljZSkgfCBTRUxFQ1QgX19zb3VyY2VfXyBhcyBzZXJ2aWNlLCBjb3VudCgqKSBhcyBjb3VudCBncm91cCBieSBfX3NvdXJjZV9fIG9yZGVyIGJ5IGNvdW50IERFU0M%3D&queryTimeType=99&startTime=**********&accounttraceid=e91e9d383d2c47eab3e07e5086ff8e16rinu">
                            <div class="weui-cell__bd">
                                <p><EMAIL> k413</p>
                            </div>
                            <div class="weui-cell__ft"></div>
                        </a>
                    </div>
                </div>
            </li>


            <li>
                <div class="weui-flex js-category">
                    <div class="weui-flex__item">old grafana</div>
                    <i class="icon icon-74"></i>
                </div>
                <div class="page-category js-categoryInner">
                    <div class="weui-cells page-category-content">

                        <a class="weui-cell weui-cell_access" href="http://monitor-app.coohua-inc.com/d/ZwhGzzFZz/pepper-dashboard?orgId=1&from=now-1h&to=now">
                            <div class="weui-cell__bd">
                                <p>lkl 123456</p>
                            </div>
                            <div class="weui-cell__ft"></div>
                        </a>
                        <a class="weui-cell weui-cell_access"
                           href="http://monitor-app.coohua-inc.com/d/CSGxiK9Zz/ye-wu-jian-kong-ye-wu-zhong-tai?orgId=1&var-jobname=coohua%2Fcaf2x-exporter&var-app=core-caf-dispense&var-name=dispense&var-level1_name=%E4%BA%A7%E5%93%81&var-level1_value=All&var-level2_name=%E8%B4%A6%E6%88%B7&var-level2_value=All&var-level3_name=%E7%B1%BB%E5%9E%8B&var-level3_value=All&from=now-12h&to=now&refresh=5s">
                            <div class="weui-cell__bd">
                                <p>ocpc回传</p>
                            </div>
                            <div class="weui-cell__ft"></div>
                        </a>
                    </div>
                </div>
            </li>

            <li>
                <div class="weui-flex js-category">
                    <div class="weui-flex__item">new grafana</div>
                    <i class="icon icon-74"></i>
                </div>
                <div class="page-category js-categoryInner">

                    <div class="weui-cells page-category-content">

                        <a class="weui-cell weui-cell_access" href="http://172.16.11.185:3000/d/hb7fSE0Zz/ck-nodeexporter?orgId=1&refresh=15s">
                            <div class="weui-cell__bd">
                                <p>admin admin</p>
                            </div>
                            <div class="weui-cell__ft"></div>
                        </a>
                    </div>

                </div>
            </li>

            <li>
                <div class="weui-flex js-category">
                    <div class="weui-flex__item">azkaban</div>
                    <i class="icon icon-74"></i>
                </div>
                <div class="page-category js-categoryInner">

                    <div class="weui-cells page-category-content">

                        <a class="weui-cell weui-cell_access" href="http://172.16.11.50:9091/index?doaction=search&searchterm=daily_result">
                            <div class="weui-cell__bd">
                                <p>azkaban azkaban</p>
                            </div>
                            <div class="weui-cell__ft"></div>
                        </a>
                    </div>

                </div>
            </li>

            <li>
                <div class="weui-flex js-category">
                    <div class="weui-flex__item">发布平台</div>
                    <i class="icon icon-74"></i>
                </div>
                <div class="page-category js-categoryInner">
                    <div class="weui-cells page-category-content">
                        <a class="weui-cell weui-cell_access" href="http://172.16.11.185:8111/project/CoreAlert?mode=builds">
                            <div class="weui-cell__bd">
                                <p>admin 123456</p>
                            </div>
                            <div class="weui-cell__ft">teamcity</div>
                        </a>
                    </div>
                    <div class="weui-cells page-category-content">
                        <a class="weui-cell weui-cell_access" href="http://wayne.coohua.top/public/portal/namespace/2/app/258/deployment/591">
                            <div class="weui-cell__bd">
                                <p>lukangle</p>  k41
                            </div>
                            <div class="weui-cell__ft">wayne</div>
                        </a>
                    </div>
                </div>
            </li>
            <li>
                <div class="weui-flex js-category">
                    <div class="weui-flex__item">xxljob</div>
                    <i class="icon icon-74"></i>
                </div>
                <div class="page-category js-categoryInner">
                    <div class="weui-cells page-category-content">
                        <a class="weui-cell weui-cell_access" href="http://172.16.11.105:8080/xxl-job-admin/">
                            <div class="weui-cell__bd">
                                <p>admin 123456</p>
                            </div>
                            <div class="weui-cell__ft">xxlJob</div>
                        </a>
                    </div>
                </div>
            </li>

            <li>
                <div class="weui-flex js-category">
                    <div class="weui-flex__item">tableau</div>
                    <i class="icon icon-74"></i>
                </div>
                <div class="page-category js-categoryInner">

                    <div class="weui-cells page-category-content">

                        <a class="weui-cell weui-cell_access" href="http://tableau.coohua.top/#/views/-arpu/arpu?:iid=3">
                            <div class="weui-cell__bd">
                                <p>arpu-dau(dev k41)</p>
                            </div>
                            <div class="weui-cell__ft"></div>
                        </a>

                        <a class="weui-cell weui-cell_access" href="http://tableau.coohua.top/#/views/--MYSQL_16294299741940/sheet0?:iid=4">
                            <div class="weui-cell__bd">
                                <p>新增</p>
                            </div>
                            <div class="weui-cell__ft"></div>
                        </a>


                        <a class="weui-cell weui-cell_access" href="http://tableau.coohua.top/#/projects/9">
                            <div class="weui-cell__bd">
                                <p>dashboard</p>
                            </div>
                            <div class="weui-cell__ft"></div>
                        </a>
                    </div>

                </div>
            </li>
            <li>
                <div class="weui-flex js-category">
                    <div class="weui-flex__item">asa</div>
                    <i class="icon icon-74"></i>
                </div>
                <div class="page-category js-categoryInner">

                    <div class="weui-cells page-category-content">

                        <a class="weui-cell weui-cell_access" href="http://asa.coohua-inc.com/">
                            <div class="weui-cell__bd">
                                <p>admin (ch123456)</p>
                            </div>
                            <div class="weui-cell__ft"></div>
                        </a>

                    </div>

                </div>
            </li>
        </ul>
    </div>
    <div class="weui-footer">
        <p class="weui-footer__text">Copyright &copy;shinet</p>
    </div>

</div>
</body>
</html>
