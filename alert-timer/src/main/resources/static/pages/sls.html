<!doctype html>
<html>
<head>
    <meta charset="utf-8">
    <title>监控预警平台</title>
    <meta name="viewport" content="width=device-width,initial-scale=1,user-scalable=0">
    <link rel="stylesheet" href="css/weui.css"/>
    <link rel="stylesheet" href="css/weuix.css"/>
    <link rel="icon" href="favicon.png">
    <link rel="icon" href="favicon.ico">
    <script src="js/zepto.min.js"></script>
    <script src="js/eruda.js"></script>
    <script src="https://g.alicdn.com/dingding/dingtalk-jsapi/2.10.3/dingtalk.open.js"></script>
    <meta name="wpk-bid" content="dta_1_1295471302">
    <script>
        !(function(c,i,e,b){var h=i.createElement("script");
            var f=i.getElementsByTagName("script")[0];
            h.type="text/javascript";
            h.crossorigin=true;
            h.onload=function(){c[b]||(c[b]=new c.wpkReporter({bid:"dta_1_1295471302"}));
                c[b].installAll()};
            f.parentNode.insertBefore(h,f);
            h.src=e})(window,document,"https://g.alicdn.com/woodpeckerx/jssdk??wpkReporter.js","__wpk");
    </script>
    <script>

        $(function () {
            var dingData = {};
            $.ajax({
                url: "/alert/sls/list",
                type: "GET",
                success: function (rs) {
                    if (rs.status == 200) {
                        console.log(rs.data)
                        dingData = rs.data;
                        dingData = rs.data.detailList;
                        dingData = rs.data.countInfoList;
                    }
                }
            });



            $('.collapse .js-category').click(function () {
                $parent = $(this).parent('li');
                if ($parent.hasClass('js-show')) {
                    $parent.removeClass('js-show');
                    $(this).children('i').removeClass('icon-35').addClass('icon-74');
                } else {
                    $parent.siblings().removeClass('js-show');
                    $parent.addClass('js-show');
                    $(this).children('i').removeClass('icon-74').addClass('icon-35');
                    $parent.siblings().find('i').removeClass('icon-35').addClass('icon-74');
                }
            });
        });


    </script>
</head>

<body ontouchstart>
<div class="contianer page-bg">
    <div class="page-hd">
        <h1 class="page-hd-title">
            SLS信息展示平台
        </h1>
    </div>
    <div class="page-bd">
        <ul class="collapse">
            <li>
                <div class="weui-flex js-category">
                    <div class="weui-flex__item">superset</div>
                    <i class="icon icon-74"></i>
                </div>
                <div class="page-category js-categoryInner">
                    <div class="weui-cells page-category-content">
                        <a class="weui-cell weui-cell_access" href="http://superset.coohua-inc.com:8088/superset/dashboard/1/">
                            <div class="weui-cell__bd">
                                <p>admin k413</p>
                            </div>
                            <div class="weui-cell__ft"></div>
                        </a>
                        <a class="weui-cell weui-cell_access" href="https://sls.console.aliyun.com/lognext/project/service-log4j2/logsearch/pro?encode=base64&endTime=**********&queryString=bGV2ZWw6IEVSUk9SIGFuZCAoX19zb3VyY2VfXzpicC11c2VyLXNlcnZpY2Ugb3IgX19zb3VyY2VfXzogYnAtbWFsbC1zZXJ2aWNlIG9yIF9fc291cmNlX186IGJwLWFjY291bnQtc2VydmljZSkgfCBTRUxFQ1QgX19zb3VyY2VfXyBhcyBzZXJ2aWNlLCBjb3VudCgqKSBhcyBjb3VudCBncm91cCBieSBfX3NvdXJjZV9fIG9yZGVyIGJ5IGNvdW50IERFU0M%3D&queryTimeType=99&startTime=**********&accounttraceid=e91e9d383d2c47eab3e07e5086ff8e16rinu">
                            <div class="weui-cell__bd">
                                <p><EMAIL> k413</p>
                            </div>
                            <div class="weui-cell__ft"></div>
                        </a>
                    </div>
                </div>
            </li>
        </ul>
    </div>
    <div class="weui-footer">
        <p class="weui-footer__text">Copyright &copy;shinet</p>
    </div>

</div>
</body>
</html>
