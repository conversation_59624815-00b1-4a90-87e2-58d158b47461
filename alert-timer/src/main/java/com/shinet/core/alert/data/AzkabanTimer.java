package com.shinet.core.alert.data;

import com.shinet.core.alert.adb.service.AzkabanService;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;

/**
 * <AUTHOR>
 * @since 2021/10/10
 */
@Slf4j
@Component
public class AzkabanTimer {

    @Autowired
    private AzkabanService azkabanService;

    @XxlJob("alert-azkaban")
    public ReturnT<?> azkabn(String param){
        azkabanService.checkJobStatus();
        azkabanService.checkTimeoutTask();
        return ReturnT.SUCCESS;
    }
/*
    @PostConstruct
    public void test() {
        azkabanTimoutOverHour("daily_result");
    }*/

    @XxlJob("alert-azkaban-timeout")
    public ReturnT<?> azkabanTimout(String param){
        azkabanService.checkAvgCostTime();
        return ReturnT.SUCCESS;
    }

    @XxlJob("alert-azkaban-timeout-hour")
    public ReturnT<?> azkabanTimoutOverHour(String param){
        azkabanService.checkCostOverHour();
        return ReturnT.SUCCESS;
    }

    @XxlJob("alert-tableau-service")
    public ReturnT<?> tableauService(String params){
        azkabanService.checkTableauService();
        return ReturnT.SUCCESS;
    }
}
