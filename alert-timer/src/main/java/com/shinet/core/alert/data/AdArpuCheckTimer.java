package com.shinet.core.alert.data;

import com.google.common.collect.Lists;
import com.shinet.core.alert.clickhouse.service.AdChannelExposureService;
import com.shinet.core.alert.clickhouse.service.AdExposureService;
import com.shinet.core.alert.clickhouse.service.ArpuCheckService;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import com.xxl.job.core.log.XxlJobLogger;
import io.swagger.models.auth.In;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.*;

/**
 * <AUTHOR>
 * @since 2022/4/21
 */
@Component
public class AdArpuCheckTimer {
    @Autowired
    private ArpuCheckService arpuCheckService;

    @XxlJob("alert-arpu-ck-check")
    public ReturnT<?> alertArpuCheck(String param){
        XxlJobLogger.log("arpuCheckService.checkArpu():start");
        arpuCheckService.checkArpu();
        XxlJobLogger.log("arpuCheckService.checkArpu():end");
        XxlJobLogger.log("arpuCheckService.checkECPM():start");
        arpuCheckService.checkECPM();
        XxlJobLogger.log("arpuCheckService.checkECPM():end");
        XxlJobLogger.log("arpuCheckService.checkPV():start");
        arpuCheckService.checkPV();
        XxlJobLogger.log("arpuCheckService.checkPV():end");
//        arpuCheckService.checkPVAllDev();
        // 广告平台波动预警
        XxlJobLogger.log("arpuCheckService.checkChannelECPM():start");
        arpuCheckService.checkChannelECPM();
        XxlJobLogger.log("arpuCheckService.checkChannelECPM():end");
        return ReturnT.SUCCESS;
    }

    @XxlJob("alert-filter-ck-check")
    public ReturnT<?> alertFilterCheck(String param){
        arpuCheckService.checkFilter();
        return ReturnT.SUCCESS;
    }

    @XxlJob("alert-exposureCheck-arpu")
    public ReturnT<?> alertExposureArpu(String param){
        int minds = 20;
        int minDau = 100000;
        int alertRate = 35;
        List<String> bkprojectSet = new ArrayList<>();
        if(StringUtils.isNotBlank(param)){
            String[] dst = param.split(",");
            minds = Integer.parseInt(dst[0]);
            minDau = Integer.parseInt(dst[1]);
            alertRate = Integer.parseInt(dst[2]);
            bkprojectSet = Lists.newArrayList(dst[3].split("@"));
        }

        Date date = new Date();
        if(date.getHours()<=8){
            minds = 60;
            alertRate = 45;
        }

        int dayOfWeek = Calendar.getInstance().get(Calendar.DAY_OF_WEEK);
        if((dayOfWeek-1)==1){
            alertRate = alertRate +5;
            XxlJobLogger.log("周一 alertRate+5"+alertRate);
        }

        adExposureService.getAdArpuExposure("alert-exposureCheck-arpu",minds,minDau,alertRate,bkprojectSet);
        return ReturnT.SUCCESS;
    }

    @Autowired
    AdExposureService adExposureService;
    @XxlJob("alert-exposureCheck")
    public ReturnT<?> alertExposureReward(String param){
        Integer mins = 30;
        List<String> excludeAdIdList  = new ArrayList<>();

        if(StringUtils.isNotBlank(param) && param.contains(",")){
            mins = Integer.parseInt(param.split(",")[0]);
            String[] eids = param.split(",")[1].split("-");
            excludeAdIdList = Arrays.asList(eids);
        }
        adExposureService.getAdExposure("alert-exposureCheck",mins,excludeAdIdList);
        return ReturnT.SUCCESS;
    }

    public  static void main(String[] args){
        int dayOfWeek = Calendar.getInstance().get(Calendar.DAY_OF_WEEK);

        System.out.println(dayOfWeek);
    }
    @XxlJob("alert-rewardThird")
    public ReturnT<?> alertRewardThird(String param){
        Integer mins = 30;
        if(StringUtils.isNotBlank(param)){
            mins = Integer.parseInt(param);
        }
        adExposureService.getThirdRewardExposure("alert-rewardThird",mins);
        return ReturnT.SUCCESS;
    }

    @Autowired
    AdChannelExposureService adChannelExposureService;
    @XxlJob("alertChannelManfi")
    public ReturnT<?> alertChannelManfi(String param){
        Integer maxChanel = 200;
        Integer maxMani = 200;
        if(StringUtils.isNotBlank(param)){
            maxChanel = Integer.parseInt(param.split(",")[0]);
            maxMani = Integer.parseInt(param.split(",")[1]);
        }
        adChannelExposureService.altExposureChannel("alertChannelManfi",maxChanel,maxMani);
        return ReturnT.SUCCESS;
    }


    @XxlJob("alertChannelPv")
    public ReturnT<?> alertChannelPv(String param){
        Double altLv = 0.5d;
        Integer minUv = 1000;
        if(StringUtils.isNotBlank(param)){
            altLv = Double.parseDouble(param.split(",")[0]);
            minUv = Integer.parseInt(param.split(",")[1]);
        }
        adChannelExposureService.altExposurePvChannel("alertChannelPv",altLv,minUv);
        return ReturnT.SUCCESS;
    }
}
