package com.shinet.core.alert.data;

import com.shinet.core.alert.coreservice.UserServiceCheck;
import com.shinet.core.alert.data.service.DataCheckService;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Component
public class MReqTimer {
    @Autowired
    UserServiceCheck userServiceCheck;
    /**
     * 曝光预警
     */
    @XxlJob("checkUserService")
    public ReturnT<?> checkUserService(String param){
        int bfloat = 2;
        if(StringUtils.isNotBlank(param)){
            bfloat = Integer.parseInt(param);
        }
        userServiceCheck.checkAuth("checkUserService",bfloat);
        return ReturnT.SUCCESS;
    }
}
