package com.shinet.core.alert.controller;

import com.shinet.core.alert.data.entity.DataSlsError;
import com.shinet.core.alert.dingtalk.SLSService;
import com.shinet.core.alert.vo.ReturnResult;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Slf4j
@RestController
public class SlsController {
    @GetMapping("alert/sls/list")
    @ResponseBody
    public ReturnResult dingdingAuth(String projectName){
        ReturnResult returnResult = new ReturnResult();
        try {

            List<String>  slsList = SLSService.queryLogs(30,projectName);
            List<DataSlsError>  dataSlsErrors = SLSService.queryLogs(30);
            Map<String,Object> dmap = new HashMap<>();
            dmap.put("detailList",slsList);
            dmap.put("countInfoList",dataSlsErrors);
            returnResult.setData(dmap);
        }catch (Exception e){
            log.error("",e);
        }
        return returnResult;
    }

}
