package com.shinet.core.alert.data;

import com.shinet.core.alert.promethus.BusinessExceptionService;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;



@Slf4j
@Component
public class GrafanaTimer {

    @Autowired
    private BusinessExceptionService businessExceptionService;

    @XxlJob("alert-app-exception")
    public ReturnT<?> appException(String param){
        businessExceptionService.checkAppCrash(10);
        return ReturnT.SUCCESS;
    }

    @XxlJob("alert-requset-exception")
    public ReturnT<?> requsetException(String param){
        businessExceptionService.checkRequestException(100);
        return ReturnT.SUCCESS;
    }
}
