package com.shinet.core.alert.data;

import com.shinet.core.alert.coreservice.service.BpApServiceCheck;
import com.shinet.core.alert.coreservice.service.ToufangJobCheck;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import com.xxl.job.core.log.XxlJobLogger;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Date;

@Component
public class ServiceLiveTimer {
    @Autowired
    ToufangJobCheck toufangJobCheck;
    @XxlJob("alert-joblive")
    public ReturnT<?> joblive(String param){
        String checkIps = "toufangjob-*************:8080,*************:8080;";
        if(StringUtils.isNotBlank(param)){
            checkIps = param;
        }

        Date date = new Date();
        if(date.getMinutes()<10){
            XxlJobLogger.log("job任务 10min以内不做预警");
            return ReturnT.SUCCESS;
        }
        toufangJobCheck.checkToufangJob("alert-joblive-toufang",checkIps,null,(jsonObject -> jsonObject.getInteger("code")!=200));
        return ReturnT.SUCCESS;
    }

    @XxlJob("alert-servicelive")
    public ReturnT<?> servicelive(String param){
        String checkIps = "safeapi-*************:8080,*************:8080,*************:8080,*************:8080,*************:8080,*************:8080;";
        if(StringUtils.isNotBlank(param)){
            checkIps = param;
        }
        toufangJobCheck.checkToufangJob("alert-servicelive-safe",checkIps,null,(jsonObject -> jsonObject.getInteger("status")!=200));
        return ReturnT.SUCCESS;
    }


    @XxlJob("alert-actorServicelive")
    public ReturnT<?> actorServicelive(String param){
        String checkIps = "bp-data-retrieve@http://bp-api.shinet-inc.com/bp-data-retrieve/actuator/health;";
        if(StringUtils.isNotBlank(param)){
            checkIps = param;
        }
        toufangJobCheck.checkToufangJob("alert-servicelive-ocpc",checkIps,"@",(jsonObject -> !jsonObject.getString("status").equals("UP")));
        return ReturnT.SUCCESS;
    }

    @Autowired
    BpApServiceCheck bpApServiceCheck;
    @XxlJob("alert-bpaplive")
    public ReturnT<?> bpaplive(String param){
        bpApServiceCheck.checkBpAp("alert-bpaplive");
        return ReturnT.SUCCESS;
    }

    @XxlJob("alert-userServicelive")
    public ReturnT<?> userServicelive(String param){
        String checkIps = "bp-user-service@http://************:8080/bp/actuator/health," +
                "http://************:8080/bp/actuator/health," +
                "http://************:8080/bp/actuator/health," +
                "http://************:8080/bp/actuator/health," +
                "http://*************:8080/bp/actuator/health," +
                "http://*************:8080/bp/actuator/health," +
                "http://*************:8080/bp/actuator/health," +
                "http://*************:8080/bp/actuator/health";
        if(StringUtils.isNotBlank(param)){
            checkIps = param;
        }
        toufangJobCheck.checkToufangJob("alert-servicelive-user",checkIps,"@",(jsonObject -> !jsonObject.getString("status").equals("UP")));
        return ReturnT.SUCCESS;
    }

    @XxlJob("alert-mallServicelive")
    public ReturnT<?> mallServicelive(String param){
        String checkIps = "bp-mall-service@http://*************:8080/actuator/health," +
                "http://*************:8080/actuator/health," +
                "http://*************:8080/actuator/health," +
                "http://***********:8080/actuator/health," +
                "http://***********:8080/actuator/health," +
                "http://***********:8080/actuator/health";
        if(StringUtils.isNotBlank(param)){
            checkIps = param;
        }
        toufangJobCheck.checkToufangJob("alert-servicelive-mall",checkIps,"@",(jsonObject -> !jsonObject.getString("status").equals("UP")));
        return ReturnT.SUCCESS;
    }
}
