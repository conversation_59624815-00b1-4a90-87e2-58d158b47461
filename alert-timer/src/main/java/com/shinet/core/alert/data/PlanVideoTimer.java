package com.shinet.core.alert.data;

import com.shinet.core.alert.dsp.service.ToutiaoPlanQueueService;
import com.shinet.core.alert.dsp.service.VideoMaterialItemService;
import com.shinet.core.alert.dsp.service.VideoPlanConfigMaterialService;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Date;

@Component
public class PlanVideoTimer {
    @Autowired
    VideoPlanConfigMaterialService videoPlanConfigMaterialService;
    @XxlJob("alert-videoUpload")
    public ReturnT<?> videoUpload(String param) {
        Integer bfloat = 3000;
        if(StringUtils.isNotBlank(param)){
            bfloat = Integer.parseInt(param);
        }
        Date date = new Date();
        int hour = date.getHours();
        if(hour>=9){
            videoPlanConfigMaterialService.alertVideoPlan(bfloat);
        }
        return ReturnT.SUCCESS;
    }
    @Autowired
    ToutiaoPlanQueueService toutiaoPlanQueueService;
    @XxlJob("alert-plancreate")
    public ReturnT<?> plancreate(String param) {
        Integer bfloat = 50;
        Integer lowCNum = 10000;
        if(StringUtils.isNotBlank(param)){
            bfloat = Integer.parseInt(param.split(",")[0]);
            lowCNum = Integer.parseInt(param.split(",")[1]);
        }
        Date date = new Date();
        int hour = date.getHours();
        if(hour>=10){
            toutiaoPlanQueueService.alertPlanCreate(bfloat,lowCNum);
        }
        return ReturnT.SUCCESS;
    }
    @Autowired
    VideoMaterialItemService videoMaterialItemService;

    @XxlJob("alert-videoTips")
    public ReturnT<?> videoTips(String param) {
        Integer bfloat = 50;
        Integer lowCNum = 200;
        Integer lowHourNum = 30;
        if(StringUtils.isNotBlank(param)){
            bfloat = Integer.parseInt(param.split(",")[0]);
            lowCNum = Integer.parseInt(param.split(",")[1]);
            lowHourNum = Integer.parseInt(param.split(",")[2]);
        }
        Date date = new Date();
        int hour = date.getHours();
        if(hour>=9){
            videoMaterialItemService.alertMaterialItem(lowCNum,bfloat,lowHourNum);
        }
        return ReturnT.SUCCESS;
    }
}
