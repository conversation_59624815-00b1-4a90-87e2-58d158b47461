package com.shinet.core.alert.data;

import com.shinet.core.alert.adb.service.RealtimeWithdrawTimeDistributionService;
import com.shinet.core.alert.clickhouse.service.DailyResultService;
import com.shinet.core.alert.clickhouse.service.ThirdAdIncomeService;
import com.shinet.core.alert.clickhouse.service.WithdrawService;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;

@Component
public class DailyResultTimer {

    @Autowired
    RealtimeWithdrawTimeDistributionService realtimeWithdrawTimeDistributionService;
    @Autowired
    DailyResultService dailyResultService;
    @Autowired
    private ThirdAdIncomeService thirdAdIncomeService;


    /**
     * 校验产品日报收入数据，比较昨日数据与前日数据，收入数据低于百分之七十，暂时不更新 预警
     * 暂时不使用 直接shell脚本内发起拨打钉钉电话操作
     */
    @XxlJob("daily-result-check")
    public ReturnT<?> startDailyResultCheck(String param) {
        dailyResultService.startDailyResultCheck();
        return ReturnT.SUCCESS;
    }



    @XxlJob("daily-result-check-detail")
    public ReturnT<?> startDailyResultCheckDetail(String param) {
        dailyResultService.startDailyResultCheckDetail();
        return ReturnT.SUCCESS;
    }

   /* @PostConstruct
    public void test() {
        realtimeResultHourIncomeCheck("daily-result-check-detail");
    }*/

    @XxlJob("realtime-result-income-check")
    public ReturnT<?> realtimeResultHourIncomeCheck(String param) {
        dailyResultService.realtimeResultHourIncomeCheck();
        return ReturnT.SUCCESS;
    }

/*   @PostConstruct
    public void test() {
         dailyResultService.dailyResultChannelIncomeCheck();
    }*/

    @XxlJob("checkIncome-local")
    public ReturnT<?> checkIncome(String param) {
        thirdAdIncomeService.checkDayIncome("daily_result");
        return ReturnT.SUCCESS;
    }


}
