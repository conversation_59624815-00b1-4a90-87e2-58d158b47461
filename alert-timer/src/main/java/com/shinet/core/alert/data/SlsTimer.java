package com.shinet.core.alert.data;

import com.shinet.core.alert.data.service.DataSlsErrorService;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Component
public class SlsTimer {
    @Autowired
    DataSlsErrorService dataSlsErrorService;

    @XxlJob("alert-slsError")
    public ReturnT<?> slsError(String param){
        int minNum = 60;
        if(StringUtils.isNotBlank(param)){
            minNum = Integer.parseInt(param);
        }
        dataSlsErrorService.insertSlsError(minNum);
        return ReturnT.SUCCESS;
    }
}
