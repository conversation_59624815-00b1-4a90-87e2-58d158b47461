package com.shinet.core.alert.data;

import com.shinet.core.alert.data.service.DataCheckService;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

@Component
public class ExposureAlertTimer {
    @Autowired
    DataCheckService dataCheckService;
    /**
     * 曝光预警
     */
    @XxlJob("alert-exposure")
    public ReturnT<?> startScheduleExposureAlert(String param){
        double bfloat = 0.4f;
        if(StringUtils.isNotBlank(param)){
            bfloat = Double.parseDouble(param);
        }
        dataCheckService.expourseAlert("alert-exposure",bfloat);
        return ReturnT.SUCCESS;
    }
}
