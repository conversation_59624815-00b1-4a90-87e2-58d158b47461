package com.shinet.core.alert.data;

import com.shinet.core.alert.clickhouse.service.RealtimeRiskService;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @since 2022/12/9
 */
@Slf4j
@Component
public class RealtimeRiskCheckTimer {

    @Autowired
    private RealtimeRiskService realtimeRiskService;

    @XxlJob("alert-ks-callback")
    public ReturnT<?> ksCallBack(String param){
        realtimeRiskService.checkKsCallBackCount();
        return ReturnT.SUCCESS;
    }


    @XxlJob("alert-advertiser-income-cpa")
    public ReturnT<?> accountCpaAndCost(String param){
        realtimeRiskService.checkAdvertiserArpuAndCpa();
        return ReturnT.SUCCESS;
    }

    @XxlJob("check-lock-rate")
    public ReturnT<?> checkLockRate(String param){
        realtimeRiskService.checkLockedRateRecord();
        return ReturnT.SUCCESS;
    }

    @XxlJob("check-lock-ip-channel")
    public ReturnT<?> checkLockRateChannel(String param){
        realtimeRiskService.queryTodayRealtimeLockIp();
        return ReturnT.SUCCESS;
    }

    @XxlJob("ad-failed-request-alert")
    public ReturnT<?> checkRequestFailCount(String param){
        realtimeRiskService.checkVideoFailedCount();
        return ReturnT.SUCCESS;
    }

    @XxlJob("user-active-normal-alert")
    public ReturnT<?> chekNewUserChannelSource(String param){
        realtimeRiskService.chekNewUserChannelSource();
        return ReturnT.SUCCESS;
    }


    @XxlJob("filter-user-active-num-alert")
    public ReturnT<?> checkNewActiveNum(String param){
        realtimeRiskService.checkUserActiveNum();
        return ReturnT.SUCCESS;
    }

    @XxlJob("filter-aliyun-channel-num-alert")
    public ReturnT<?> checkAliyunChannelEx(String param){
        realtimeRiskService.checkAliyunChannelEx();
        return ReturnT.SUCCESS;
    }


    @XxlJob("check-g9-payment-rate")
    public ReturnT<?> checkGroup9Payment(String param){
        realtimeRiskService.checkGroup9Payment();
        return ReturnT.SUCCESS;
    }

    @XxlJob("cpa-realtime-hour-alert")
    public ReturnT<?> checkCpaLastHour(String param){
        realtimeRiskService.checkCpaLast();
        return ReturnT.SUCCESS;
    }

    /**
     * 同时段roi波动报警，降低10%或上升20%
     * @param param
     * @return
     */
    @XxlJob("roi-realtime-hour-alert")
    public ReturnT<?> checkRoiRealtimeHour(String param){
        log.info("roi-realtime-hour-alert start");
        realtimeRiskService.checkRoiLast();
        return ReturnT.SUCCESS;
    }

    @XxlJob("realtime-hour-cost")
    public ReturnT<?> checkRealtimeCostHour(String param){
        log.info("realtime-hour-cost start");
        realtimeRiskService.checkCostHourLast();
        return ReturnT.SUCCESS;
    }
    /**
     * 上小时反作弊拉黑用户数量预警
     */
    @XxlJob("today-gray-user-ip-sum-alert")
    public ReturnT<?> grayUserDeviceSumHour(String param){
        log.info("today-gray-user-ip-sum-alert");
        realtimeRiskService.grayUserDeviceSumHourAlert();
        return ReturnT.SUCCESS;
    }

    /**
     * 凌晨计算上小时反作弊拉黑用户数量预警
     */
    @XxlJob("today-last-gray-user-ip-sum-alert")
    public ReturnT<?> grayUserDeviceSumHourYes(String param){
        log.info("today-last-gray-user-ip-sum-alert");
        realtimeRiskService.grayUserDeviceSumHourYesAlert();
        return ReturnT.SUCCESS;
    }

    @XxlJob("today-product-ecpm-high-alert")
    public ReturnT<?> productEcpmSum(String param){
        log.info("today-product-ecpm-high-alert");
        realtimeRiskService.productEcpmAlert();
        return ReturnT.SUCCESS;
    }


    /**
     * 实时ECPM预警
     */
    @XxlJob("today-os-ecpm-alert")
    public ReturnT<?> todayEcpmAlert(String param){
        log.info("today-os-ecpm-alert");
        realtimeRiskService.todayEcpmAlert();
        return ReturnT.SUCCESS;
    }




    /**
     * 实时产品ECPM预警
     */
    @XxlJob("realtime-os-product-ecpm-alert")
    public ReturnT<?> realtimeOsProductEcpmCostAlert(String param){
        log.info("realtime-os-product-ecpm-alert");
        realtimeRiskService.realtimeOsProductEcpmCostAlert();
        return ReturnT.SUCCESS;
    }

    /**
     * 恢复最初的arpu
     */
    @XxlJob("realtime-os-product-arpu-adjust-alert")
    public ReturnT<?> realtimeOsProductArpuAdjustAlert(String param){
        log.info("realtime-os-product-arpu-adjust-alert");
        realtimeRiskService.recoverOriginArpu();
        return ReturnT.SUCCESS;
    }

    /**
     * 产品arpu下降预警
     */
    @XxlJob("today-product-arpu-down-alert")
    public ReturnT<?> productArpuDownAlert(String param){
        log.info("today-product-arpu-down-alert");
        realtimeRiskService.productArpuDownAlert();
        return ReturnT.SUCCESS;
    }

}
