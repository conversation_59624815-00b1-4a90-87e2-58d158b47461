package com.shinet.core.alert.data;

import com.alibaba.fastjson.JSONObject;
import com.shinet.core.alert.coreservice.entity.AlertRecord;
import com.shinet.core.alert.coreservice.entity.UserLoginCheckRecord;
import com.shinet.core.alert.coreservice.enums.AlertModel;
import com.shinet.core.alert.coreservice.enums.AlertRoute;
import com.shinet.core.alert.coreservice.enums.AlertStatus;
import com.shinet.core.alert.coreservice.enums.AlertType;
import com.shinet.core.alert.coreservice.service.AlertRecordService;
import com.shinet.core.alert.coreservice.service.UserLoginCheckRecordService;
import com.shinet.core.alert.data.service.DataSlsErrorService;
import com.shinet.core.alert.dingtalk.DingTailService;
import com.shinet.core.alert.util.DateUtils;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import com.xxl.job.core.log.XxlJobLogger;
import org.apache.commons.lang3.StringUtils;
import org.checkerframework.checker.units.qual.A;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import redis.clients.jedis.JedisCluster;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.util.*;


@Component
public class UserLoginAlertTimer {

    private static final String LOGIN_TOTAL_COUNT = "user:login:total:%s";
    private static final String LOGIN_SUCCESS_COUNT = "user:login:success:%s";

    @Resource(name = "bpJedisCluster")
    private JedisCluster bpJedisCluster;
    @Autowired
    private AlertRecordService alertRecordService;
    @Autowired
    private UserLoginCheckRecordService userLoginCheckRecordService;

    @XxlJob("alert-user-login")
    public ReturnT<?> userLogin(String param){
        JSONObject json = JSONObject.parseObject(param);
        String date = DateUtils.formatDateForYMD(DateUtils.addTime(new Date(), 300000L));
        XxlJobLogger.log("query date:{}", date);

        Map<String, String> totalMap = bpJedisCluster.hgetAll(String.format(LOGIN_TOTAL_COUNT, date));
        Map<String, String> successMap = bpJedisCluster.hgetAll(String.format(LOGIN_SUCCESS_COUNT, date));
        Set<String> allKey = new HashSet<>();
        allKey.addAll(totalMap.keySet());
        allKey.addAll(successMap.keySet());
        StringBuilder alertMsg = new StringBuilder();
        List<UserLoginCheckRecord> list = new ArrayList<>();
        for (String key : allKey) {
            UserLoginCheckRecord record = new UserLoginCheckRecord();
            record.setFieldKey(key);
            record.setSuccessNum(Long.parseLong(successMap.getOrDefault(key, "0")));
            record.setTotalNum(Long.parseLong(totalMap.getOrDefault(key, "0")));
            list.add(record);

            XxlJobLogger.log(String.format("%s -> 成功数量:%d, 总数量:%d\r\n", key, record.getSuccessNum(), record.getTotalNum()));
            if(json.containsKey(key)) {
                if(0 == record.getTotalNum()) {
                    alertMsg.append(String.format("%s -> 总数量:%d\r\n", key, record.getTotalNum()));
                    continue;
                }
                double failRate = (record.getTotalNum() - record.getSuccessNum()) * 100d / record.getTotalNum(), limitRate = json.getDoubleValue(key);
                if(failRate >= limitRate) {
                    alertMsg.append(String.format("%s -> 成功数量:%d, 总数量:%d, 失败率: %.2f%\r\n", key, record.getSuccessNum(), record.getTotalNum(), failRate));
                }
            }
        }
        XxlJobLogger.log(alertMsg.toString());
        userLoginCheckRecordService.saveBatch(list);
        if(alertMsg.length() > 0) {
            AlertRecord alertRecord = alertRecordService.insertAlertRecord("alert-user-login", "用户登录异常情况", AlertModel.USER_LOGIN_NUM,
                    alertMsg.toString(), 0d, 0d, 0, 0,
                    AlertStatus.INIT, DingTailService.user, AlertType.DINGPHONE);
            DingTailService.sendMarkdownMsg(alertRecord, AlertRoute.FZB.value);
        }
        return ReturnT.SUCCESS;
    }
}
