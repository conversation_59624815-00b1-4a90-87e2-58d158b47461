package com.shinet.core.alert.data;

import com.shinet.core.alert.gpt.service.GptTextImageService;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Component
@Slf4j
public class GptImgCheckTimer {
    @Autowired
    private GptTextImageService gptTextImageService;

    @XxlJob("alert-sdimg")
    public ReturnT<?> sdimg(String param){
        int mins = 20;
        int maxInitNum = 15;
        int maxFalNum = 10;
        if(StringUtils.isNotBlank(param)){
            String[] dsp = param.split(",");
            mins = Integer.parseInt(dsp[0]);
            maxInitNum = Integer.parseInt(dsp[1]);
            maxFalNum = Integer.parseInt(dsp[2]);
        }
        gptTextImageService.alertGptImgsCost("alert-sdimg",mins,maxInitNum,maxFalNum);
        return ReturnT.SUCCESS;
    }

}
