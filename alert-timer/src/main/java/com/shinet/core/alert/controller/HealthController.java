package com.shinet.core.alert.controller;

import com.shinet.core.alert.vo.ReturnResult;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.servlet.http.HttpServletRequest;

@Controller
@Slf4j
public class HealthController {
    @RequestMapping("/health")
    @ResponseBody
    public ReturnResult health(HttpServletRequest request) {
        ReturnResult returnResult = new ReturnResult();
        return returnResult;
    }
}
