package com.shinet.core.alert;

import com.shinet.core.alert.adb.service.AdArpuRealSumService;
import com.shinet.core.alert.adb.service.DauSumService;
import com.shinet.core.alert.clickhouse.service.AdExposureService;
import com.shinet.core.alert.coreservice.UserServiceCheck;
import com.shinet.core.alert.coreservice.service.AlertMallService;
import com.shinet.core.alert.coreservice.service.ToufangJobCheck;
import com.shinet.core.alert.data.*;
import com.shinet.core.alert.data.service.DataCheckService;
import com.shinet.core.alert.data.service.MallCheckService;
import com.shinet.core.alert.dataagent.service.CommonUserService;
import com.shinet.core.alert.dsp.service.ToutiaoReportIdeadayService;
import com.shinet.core.alert.dsp.service.UserEventService;
import com.shinet.core.alert.hbase.HbaseScanerService;
import com.shinet.core.alert.promethus.CoreQpsService;
import com.shinet.core.alert.promethus.MotanQpsService;
import com.shinet.core.alert.safe.service.AndroidLockService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.context.ConfigurableApplicationContext;
import org.springframework.scheduling.annotation.EnableScheduling;

@SpringBootApplication
@EnableScheduling
@Slf4j
public class AlertTimerApplication {

    public static void main(String[] args) {
        try {
            ConfigurableApplicationContext configurableApplicationContext = SpringApplication.run(AlertTimerApplication.class, args);
            //30000,20000,20,100
//            configurableApplicationContext.getBean(AdArpuCheckTimer.class).alertChannelManfi("30,100");
//            configurableApplicationContext.getBean(AdArpuCheckTimer.class).alertChannelPv("0.9,1000");
//            configurableApplicationContext.getBean(AdArpuCheckTimer.class).alertExposureReward("15");
//            mallCheckService.insertMallAlertChart(20);
        } catch (Exception e) {
            e.printStackTrace();
            log.error("", e);
        }
    }

}
