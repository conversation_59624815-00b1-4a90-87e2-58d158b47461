package com.shinet.core.alert.aop;

import com.alibaba.fastjson.JSON;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.springframework.web.context.request.RequestAttributes;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.servlet.http.HttpServletRequest;
import java.util.Set;

@Component
@Aspect
@Slf4j
public class RequestLog {
    @Value("${ocpc.reqlog.enable:true}")
    private boolean reqlogFlag ;

    @Around(value = "@annotation(org.springframework.web.bind.annotation.PostMapping)", argNames = "jp")
    public Object logAopWritePost(ProceedingJoinPoint jp)throws Throwable {
        return logAopWrite(jp);
    }

    @Around(value = "@annotation(org.springframework.web.bind.annotation.GetMapping)", argNames = "jp")
    public Object logAopWriteGet(ProceedingJoinPoint jp)throws Throwable {
        return logAopWrite(jp);
    }

    public String getRemoteAddress(HttpServletRequest request) {
        try {
            String ip = request.getHeader("x-forwarded-for");
            if (ip == null || ip.length() == 0 || ip.equalsIgnoreCase("unknown")) {
                ip = request.getHeader("Proxy-Client-IP");
            }
            if (ip == null || ip.length() == 0 || ip.equalsIgnoreCase("unknown")) {
                ip = request.getHeader("WL-Proxy-Client-IP");
            }
            if (ip == null || ip.length() == 0 || ip.equalsIgnoreCase("unknown")) {
                ip = request.getRemoteAddr();
            }
            return ip;
        }catch (Exception e){
            log.error("",e);

        }
        return "获取失败";
    }

    @Around(value = "@annotation(org.springframework.web.bind.annotation.RequestMapping)", argNames = "jp")
    public Object logAopWrite(ProceedingJoinPoint jp) throws Throwable {
        long curtime = System.currentTimeMillis();
        HttpServletRequest request = null;

        if(reqlogFlag){
            Object[] args = jp.getArgs();
            try{
                for (Object obj : args) {
                    if (obj instanceof HttpServletRequest) {
                        request = (HttpServletRequest) obj;
                    }
                }
                RequestAttributes requestAttributes = RequestContextHolder.getRequestAttributes();
                if(requestAttributes == null){
                    return jp.proceed();
                }
                if(request==null) {
                    request = ((ServletRequestAttributes) requestAttributes).getRequest();
                }
            }catch(Exception e){
                log.error("",e);
                throw e;
            }
            StringBuilder sbstr = new StringBuilder();
            try{
                try {
                    Set<String> keyset = request.getParameterMap().keySet();
                    for (String key : keyset) {
                        String[] values = request.getParameterValues(key);
                        for (String value : values) {
                            sbstr.append(key).append("=").append(value).append("&");
                        }
                    }
                    String ipAddress = getRemoteAddress(request);
                    log.info(ipAddress+"  "+request.getRequestURL().toString() + "?" + sbstr.toString() + "\t");
                }catch (Exception e){
                    log.error("",e);
                }
                Object object = jp.proceed();// 执行该方法
                try {
                    String jspstr = JSON.toJSONString(object);
                    if(jspstr.length()>2048){
                        jspstr = jspstr.substring(0,2048);
                    }

                    String rspStrs = request.getRequestURL().toString() + "?" + sbstr.toString() + "\t" + jspstr + "\tcost [" + (System.currentTimeMillis() - curtime) + "]";
                    log.info(rspStrs);
                }catch (Exception e){
                    log.error("",e);
                }
                return object;
            }catch(Exception e){
                log.error("",e);
                throw e;
            }
        }else{
            Object object = jp.proceed();// 执行该方法
            return object;
        }
    }
}
