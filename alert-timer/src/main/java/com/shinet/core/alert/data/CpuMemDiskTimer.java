package com.shinet.core.alert.data;

import com.shinet.core.alert.coreservice.entity.AlertRecord;
import com.shinet.core.alert.coreservice.enums.AlertModel;
import com.shinet.core.alert.coreservice.enums.AlertStatus;
import com.shinet.core.alert.coreservice.enums.AlertType;
import com.shinet.core.alert.coreservice.service.AlertRecordService;
import com.shinet.core.alert.dingtalk.DingTailService;
import com.shinet.core.alert.dingtalk.VedioAlertService;
import com.shinet.core.alert.promethus.MemCpuService;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import com.xxl.job.core.log.XxlJobLogger;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;

@Slf4j
@Component
public class CpuMemDiskTimer {
    @Autowired
    MemCpuService memCpuService;
    @Autowired
    AlertRecordService alertRecordService;

    @XxlJob("alert-mem-cpu")
    public ReturnT<?> startToCpuMem(String param) {
        double cpuLimit = 80d;
        double memLimit = 80d;
        double diskLimit = 80d;

        if (StringUtils.isNotBlank(param)) {
            String[] rateStrs = param.split(",");
            cpuLimit = Double.parseDouble(rateStrs[0]);
            memLimit = Double.parseDouble(rateStrs[1]);
            diskLimit = Double.parseDouble(rateStrs[2]);
        }

        List<String> nodeGroupList = new ArrayList<>();
        nodeGroupList.add("account-node");
        nodeGroupList.add("bpap-node");
        nodeGroupList.add("config-node");
        nodeGroupList.add("data-consumer");
        nodeGroupList.add("data-produce");
        nodeGroupList.add("dispense-node");
        nodeGroupList.add("mall-node");
        nodeGroupList.add("prometheus-node");
        nodeGroupList.add("user-node");
        nodeGroupList.add("ck-zk");
        nodeGroupList.add("safe-node");
        nodeGroupList.add("motanzk-node");

        List<String> noCpuGroupList = new ArrayList<>();
        noCpuGroupList.add("redis-dispense");
        noCpuGroupList.add("redis-user");


        String alertMsg =  "";
        List<String> alerMsgList = new ArrayList<>();
        for (String nodeGname : nodeGroupList) {
            try {
                double cpuCurrent = cpuLimit;
                double memLimitCurrent = memLimit;
                if ("prometheus-node".equals(nodeGname)){
                    XxlJobLogger.log("prometheus-node change limit to 90");
                    cpuCurrent = 90d;
                    memLimitCurrent = 90d;
                }
                String nodeMemMsg = memCpuService.memAlert(nodeGname, memLimitCurrent);
                if(StringUtils.isNotBlank(nodeMemMsg)){
                    alerMsgList.add(nodeGname+" "+nodeMemMsg);
                }

                String nodeCpuMsg = memCpuService.cpuAlert(nodeGname, cpuCurrent);
                if(StringUtils.isNotBlank(nodeCpuMsg)){
                    alerMsgList.add(nodeGname+" "+nodeCpuMsg);
                }
                String nodeDiskMsg = memCpuService.diskAlert(nodeGname, diskLimit);
                if(StringUtils.isNotBlank(nodeDiskMsg)){
                    alerMsgList.add(nodeGname+" "+nodeDiskMsg);
                }
            } catch (Exception e) {
                XxlJobLogger.log(e);
            }
        }


        for (String nodeGname : noCpuGroupList) {
            try {
                String nodeMemMsg = memCpuService.memAlert(nodeGname, memLimit);
                if(StringUtils.isNotBlank(nodeMemMsg)){
                    alerMsgList.add(nodeGname+" "+nodeMemMsg);
                }
                String nodeDiskMsg = memCpuService.diskAlert(nodeGname, diskLimit);
                if(StringUtils.isNotBlank(nodeDiskMsg)){
                    alerMsgList.add(nodeGname+" "+nodeDiskMsg);
                }
            } catch (Exception e) {
                XxlJobLogger.log(e);
            }
        }


        List<String> diskGroupList = new ArrayList<>();
        diskGroupList.add("ck-node");
        for (String nodeGname : diskGroupList) {
            try {
                String nodeDiskMsg = memCpuService.diskAlert(nodeGname, diskLimit);
                if(StringUtils.isNotBlank(nodeDiskMsg)){
                    alerMsgList.add(nodeGname+" "+nodeDiskMsg);
                }
            } catch (Exception e) {
                XxlJobLogger.log(e);
            }
        }
        for(String msg : alerMsgList){
            alertMsg =alertMsg +"\r\n"+msg;
        }

        if(StringUtils.isNotBlank(alertMsg)){
            AlertRecord alertRecord = alertRecordService.insertAlertRecord("alert-mem-cpu","cpumemdisk使用", AlertModel.CPUMEM,
                    "机器资源使用异常"+alertMsg, AlertStatus.INIT, DingTailService.dset, AlertType.DINGDING);
            DingTailService.sendMarkdownMsg(alertRecord);


            VedioAlertService.sendVocMsg("cpumem ",alertMsg);
        }
        return ReturnT.SUCCESS;
    }
}
