package com.shinet.core.alert.data;

import com.shinet.core.alert.adb.service.RealtimeWithdrawTimeDistributionService;
import com.shinet.core.alert.bpcommon.entity.WechatApp;
import com.shinet.core.alert.clickhouse.service.WithdrawService;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.util.List;

@Component
public class WithdrawAlertTimer {

    @Autowired
    RealtimeWithdrawTimeDistributionService realtimeWithdrawTimeDistributionService;
    @Autowired
    WithdrawService withdrawService;
    /**
     * 提现状态延时预警
     */
    @XxlJob("alert-withdraw")
    public ReturnT<?> startScheduleWithdrawAlert(String param){
        realtimeWithdrawTimeDistributionService.alertShijianWithdraw("alert-withdraw");
        return ReturnT.SUCCESS;
    }


    /**
     * 商户提现余额预警
     */
    @XxlJob("alert-merchant-withdraw")
    public ReturnT<?> startScheduleMerchantWithdrawAlert(String param){
        withdrawService.alertMerchantWithdraw();
        return ReturnT.SUCCESS;
    }

    @XxlJob("alert-withdraw-check-amount")
    public ReturnT<?> withdrawDauCheck(String param){
        withdrawService.checkWithdraw();
        return ReturnT.SUCCESS;
    }

    @XxlJob("alert-withdraw-check-float")
    public ReturnT<?> withdrawCheck(String param){
        withdrawService.checkWithdrawCrossYesterday();
        return ReturnT.SUCCESS;
    }

    @XxlJob("alert-withdraw-check-bigAmount")
    public ReturnT<?> withdrawCheckBigAmount(String param){
        withdrawService.checkWithdrawBigAmount();
        return ReturnT.SUCCESS;
    }


    @XxlJob("withdraw-channel-alert")
    public ReturnT<?> checkWithdrawChannelPart(String param){
        withdrawService.checkWithdrawChannelPart();
        return ReturnT.SUCCESS;
    }


    @XxlJob("withdraw-channel-no-point-alert")
    public ReturnT<?> checkWithdrawNoPointPart(String param){
        withdrawService.checkExPointChannelUserWithdraw();
        return ReturnT.SUCCESS;
    }

    @XxlJob("withdraw-channel-avg-big-alert")
    public ReturnT<?> checkWithdrawBig(String param){
        withdrawService.checkUserAvgBigWithdraw();
        return ReturnT.SUCCESS;
    }

    @XxlJob("withdraw-num-abnormal-alert")
    public ReturnT<?> checkWithDrawNumYes(String param){
        withdrawService.checkWithDrawNumYes();
        return ReturnT.SUCCESS;
    }

    @XxlJob("withdraw-phone-float-alert")
    public ReturnT<?> phoneWithDrawPl(String param){
        withdrawService.phoneWithDrawPl();
        return ReturnT.SUCCESS;
    }


    @XxlJob("withdraw-no-send-alert")
    public ReturnT<?> checkWithdrawNoSendAlert(String param){
        withdrawService.checkWithdrawNoSendAlert();
        return ReturnT.SUCCESS;
    }

//        @PostConstruct
//    public void test() {
//            profitRealtimeHourAlert("daily_result");
//    }

    @XxlJob("profit-realtime-hour-alert")
    public ReturnT<?> profitRealtimeHourAlert(String param){

        //仅仅提醒当天
        withdrawService.profitRealtimeHourAlert();

        return ReturnT.SUCCESS;
    }

    @XxlJob("withdraw-ip-sum-alert")
    public ReturnT<?> checkIPWithdraw(String param){

        //仅仅提醒当天
        withdrawService.checkWithdrawIPAlert();
        return ReturnT.SUCCESS;
    }


    //IP-新用户-提现占比预警
    @XxlJob("withdraw-ip-natural-alert")
    public ReturnT<?> checkWithdraw(String param){

        //仅仅提醒当天
        //withdrawService.checkWithdrawIPAlert();
        withdrawService.checkDeviceNewAlert();
        withdrawService.checkWithdrawNaturalAlert();

        return ReturnT.SUCCESS;
    }

    @XxlJob("withdraw-channel-limit-alert")
    public ReturnT<?> naturalChannelLimit(String param){
        //默认10个预警
        int alertCount = StringUtils.isNotBlank(param) ? Integer.parseInt(param) : 10;
        withdrawService.checkWithdrawNaturalChannelLimitAlert(alertCount);
        return ReturnT.SUCCESS;
    }
}
