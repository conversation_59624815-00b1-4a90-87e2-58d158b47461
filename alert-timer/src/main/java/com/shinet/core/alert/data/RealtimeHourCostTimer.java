package com.shinet.core.alert.data;

import com.shinet.core.alert.clickhouse.service.RealtimeCostHourService;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @since 2024/1/30
 */
@Component
public class RealtimeHourCostTimer {

    @Autowired
    private RealtimeCostHourService realtimeCostHourService;

    @XxlJob("alert-realtime-hour-cost")
    public ReturnT<?> ksCallBack(String param) {
        realtimeCostHourService.checkHourData();
        return ReturnT.SUCCESS;
    }

    @XxlJob("today-au-income-alert")
    public ReturnT<?> checkTodayNewAuIncome(String param) {
        //新老设备一起执行 除了凌晨每小时执行一次
        realtimeCostHourService.todayNewAuIncomelAlert();
        realtimeCostHourService.todayOldAuIncomelAlert();
        return ReturnT.SUCCESS;
    }

    @XxlJob("yes-au-income-alert")
    public ReturnT<?> checkYesNewAuIncome(String param) {
        //新老设备一起执行,凌晨的时候执行一次
        realtimeCostHourService.yesNewAuIncomelAlert();
        realtimeCostHourService.yesOldAuIncomelAlert();
        return ReturnT.SUCCESS;
    }


    @XxlJob("realtime-check-data-alert")
    public ReturnT<?> realtimeCheckDataAlert(String param) {
        //新老设备一起执行,凌晨的时候执行一次
        realtimeCostHourService.realtimeCheckDataAlert();
        return ReturnT.SUCCESS;
    }

    @XxlJob("realtime-check-ocpc-rate-alert")
    public ReturnT<?> realtimeCheckOcpcRateAlert(String param) {
        realtimeCostHourService.realtimeCheckOcpcRateAlert();
        return ReturnT.SUCCESS;
    }

}
