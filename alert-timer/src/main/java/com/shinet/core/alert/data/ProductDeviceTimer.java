package com.shinet.core.alert.data;

import com.shinet.core.alert.adb.service.ProductDeviceCheckService;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @since 2023/9/12
 */
@Component
public class ProductDeviceTimer {

    @Autowired
    private ProductDeviceCheckService productDeviceCheckService;

    @XxlJob("alert-product-device")
    public ReturnT<?> startMallError(String param){
        productDeviceCheckService.checkAndSendExProduct();
        productDeviceCheckService.checkEmptyExposureProduct();
        return ReturnT.SUCCESS;
    }
}
