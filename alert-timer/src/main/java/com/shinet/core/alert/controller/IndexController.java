package com.shinet.core.alert.controller;

import com.shinet.core.alert.dingtalk.DingdingAuthService;
import com.shinet.core.alert.dingtalk.DingdingSignService;
import com.shinet.core.alert.dingtalk.rsp.DingAuthRsp;
import com.shinet.core.alert.dingtalk.rsp.DingUserInfo;
import com.shinet.core.alert.vo.ReturnResult;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

@Slf4j
@RestController
public class IndexController {
    @RequestMapping("alert/index")
    public String toIndex(){
        return "index";
    }


    @GetMapping("alert/dingding/auth")
    @ResponseBody
    public ReturnResult dingdingAuth(){
        ReturnResult returnResult = new ReturnResult();
        try {
            DingAuthRsp dingAuthRsp = DingdingSignService.signDit("http://127.16.41.141:8080/pages/index.html");
            returnResult.setData(dingAuthRsp);
        }catch (Exception e){
            log.error("",e);
        }
        return returnResult;
    }


    @RequestMapping(value = "alert/login", method = RequestMethod.GET)
    public ReturnResult login (@RequestParam("code") String requestAuthCode) {

        // 获取access_token，注意正式代码要有异常流处理
        ReturnResult returnResult = new ReturnResult();
        DingUserInfo dingUserInfo = DingdingAuthService.getUserInfo(requestAuthCode);
        returnResult.setData(dingUserInfo);
        return returnResult;
    }
}
