package com.shinet.core.alert.data;

import com.shinet.core.alert.clickhouse.service.AdBidRateService;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Component
public class BidArpuAlertTimer {
    @Autowired
    AdBidRateService adBidRateService;
    /**
     * 曝光预警
     */
    @XxlJob("alert-bidincome")
    public ReturnT<?> bidincome(String param){
        double iosLimit = 20d;
        double androidLimit = 15d;
        if(StringUtils.isNotBlank(param)){
            iosLimit = Double.parseDouble(param.split(",")[0]);
            androidLimit = Double.parseDouble(param.split(",")[1]);
        }
        adBidRateService.alertBidRate("alert-bidincome",iosLimit,androidLimit);
        return ReturnT.SUCCESS;
    }



}
