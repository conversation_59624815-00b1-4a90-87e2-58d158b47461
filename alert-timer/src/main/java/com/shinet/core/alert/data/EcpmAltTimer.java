package com.shinet.core.alert.data;

import com.shinet.core.alert.adb.service.AdArpuRealSumService;
import com.shinet.core.alert.clickhouse.entity.IosLockBean;
import com.shinet.core.alert.clickhouse.mapper.ck1.IosLockMapper;
import com.shinet.core.alert.coreservice.entity.AlertRecord;
import com.shinet.core.alert.coreservice.enums.AlertModel;
import com.shinet.core.alert.coreservice.enums.AlertStatus;
import com.shinet.core.alert.coreservice.enums.AlertType;
import com.shinet.core.alert.coreservice.service.AlertRecordService;
import com.shinet.core.alert.dingtalk.DingTailService;
import com.shinet.core.alert.dingtalk.VedioAlertService;
import com.shinet.core.alert.safe.service.LockIosRstService;
import com.shinet.core.alert.util.DoubleUtil;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.joda.time.DateTimeConstants;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Date;
import java.util.List;

@Component
@Slf4j
public class EcpmAltTimer {
    @Autowired
    IosLockMapper iosLockMapper;
    @Autowired
    AdArpuRealSumService adArpuRealSumService;
    @XxlJob("ecpm-pos-alert")
    public ReturnT<?> ecpmPosAlert(String param){
        if(StringUtils.isBlank(param)){
            param = "30000,20000,20,200";
        }
        Integer newProjectSr = Integer.parseInt(param.split(",")[0]);

        Integer srShenHeLow = Integer.parseInt(param.split(",")[1]);
        Integer lowEcpm = Integer.parseInt(param.split(",")[2]);
        Integer maxEcpm = Integer.parseInt(param.split(",")[3]);
        adArpuRealSumService.adArpuSumPos("ecpm-pos-alert",newProjectSr,srShenHeLow,lowEcpm,maxEcpm);
        return ReturnT.SUCCESS;
    }


}
