package com.shinet.core.alert.data;

import com.shinet.core.alert.clickhouse.entity.CostGapBean;
import com.shinet.core.alert.clickhouse.entity.TTCpaBidBean;
import com.shinet.core.alert.clickhouse.mapper.ck1.ClickHouseDwdMapper;
import com.shinet.core.alert.coreservice.entity.AlertRecord;
import com.shinet.core.alert.coreservice.enums.*;
import com.shinet.core.alert.coreservice.service.AlertDelayJobService;
import com.shinet.core.alert.coreservice.service.AlertRecordService;
import com.shinet.core.alert.dingtalk.DingTailService;
import com.shinet.core.alert.dingtalk.VedioAlertService;
import com.shinet.core.alert.dsp.entity.*;
import com.shinet.core.alert.util.DateUtils;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2023/10/30
 */
@Component
public class ChannelCostTimer {

    @Resource
    private ClickHouseDwdMapper clickHouseDwdMapper;
    @Autowired
    private AlertDelayJobService alertDelayJobService;
    @Autowired
    private AlertRecordService alertRecordService;

    @XxlJob("check-channel-cost-alert")
    public ReturnT<?> checkChannelCostTask(String param){
        Date now = new Date();
        Date yes = DateUtils.addTime(now,-1000*60*60*24);
        String logday = DateUtils.formatDateForYMD(yes);
        if(StringUtils.isNotBlank(param)){
            logday = param;
        }
        checkChannelCost(logday);
        checkActiveStore();
        return ReturnT.SUCCESS;
    }

    private void checkActiveStore(){
        List<AppActiveBean> appActiveBeans = clickHouseDwdMapper.queryActiveStore();

        if (appActiveBeans.size() > 0){
            String alert = appActiveBeans.stream()
                    .map(r -> String.format("日期:%s,产品:%s(%s-%s),平台:%s,非投放设备:**%s**",
                            r.getLogday(), r.getProductName(),r.getProductGroup(),r.getOs(),r.getChannelName(),r.getNu()))
                    .collect(Collectors.joining(";\n\n _ _ _ \n "));
            AlertRecord alertRecord = alertRecordService.insertAlertRecord(AlertJobDelayModel.STORE_ACTIVE.getJobId(),
                    AlertJobDelayModel.STORE_ACTIVE.getJobName(), AlertModel.STORE_ACTIVE_CHECK,alert
                    , AlertStatus.INIT, DingTailService.dsetData, AlertType.DINGDING);
            DingTailService.sendMarkdownMsg(alertRecord, AlertRoute.ZENGZHANG.value);
        }
    }

    private static String format(Double value){
        BigDecimal bd = new BigDecimal(value);
        bd = bd.setScale(2, RoundingMode.HALF_UP);
        return bd.toString();
    }

    private void checkChannelCost(String logday){
        List<ChannelCostBean> channelCostBeans  = clickHouseDwdMapper.queryChannelCostGap(logday);
        List<ChannelCostBean> callBackRateList = channelCostBeans.stream()
                .filter(r -> alertDelayJobService.canSend(AlertJobDelayModel.DSP_COST.getJobId()+"_"+r.getDsp()))
                .collect(Collectors.toList());
        if (callBackRateList.size() > 0){
            long pageSize = 5;
            for (int page = 0; page <= callBackRateList.size()/pageSize; page++){
                buildAndSend(callBackRateList.stream().skip(page *pageSize).limit(pageSize).collect(Collectors.toList()));
            }
//            VedioAlertService.sendVocMsg("DailyResult ","日报消耗存在异常，请注意查看",DingTailService.dsetData);
        }
    }

    private void buildAndSend(List<ChannelCostBean> callBackRateList){
        if (callBackRateList == null || callBackRateList.size() == 0){
            return;
        }
        List<String> productList = callBackRateList.stream().map(ChannelCostBean::getDsp).collect(Collectors.toList());
        String alert = callBackRateList.stream()
                .map(r -> String.format("日期:%s,平台:%s,资金流水消耗 **%s**,日报消耗 **%s**,gap:**%s**",
                        r.getDate(), r.getDsp(),format(r.getCostFund()),format(r.getCostResult()),format(r.getGapRate()))
                )
                .collect(Collectors.joining(";\n\n _ _ _ \n "));
        AlertRecord alertRecord = alertRecordService.insertAlertRecord(AlertJobDelayModel.DSP_COST.getJobId(),
                AlertJobDelayModel.DSP_COST.getJobName(), AlertModel.COST_CHECK,alert
                , AlertStatus.INIT, DingTailService.dsetData, AlertType.DINGDING);
        DingTailService.sendMarkdownMsgDelay(alertRecord, AlertRoute.ZT_XQ.value,AlertJobDelayModel.DSP_COST,productList);
    }

    @XxlJob("check-click-third-alert")
    public ReturnT<?> checkClickPost(String param){
        checkClick();
        return ReturnT.SUCCESS;
    }



    @XxlJob("sdk-return-proportion-high")
    public ReturnT<?> checkSdkReturnProPortion(String param){
        checkSdkReturnProPortion();
        return ReturnT.SUCCESS;
    }

    @XxlJob("channel-hour-pv")
    public ReturnT<?> channelHourPv(String param){
        checkHourPv();
        return ReturnT.SUCCESS;
    }

    @XxlJob("channel-natural-proportion")
    public ReturnT<?> channelNaturalProPortion(String param){
        checkChannelNaturalProPortion();
        return ReturnT.SUCCESS;
    }

    @XxlJob("product-no-exposure-proportion")
    public ReturnT<?> productNoExposureProPortion(String param){
        productNoExposureProPortion();
        return ReturnT.SUCCESS;
    }

    @XxlJob("wookmark-screen-pv")
    public ReturnT<?> wookmarkScreenPv(String param){
        wookmarkScreenPv();
        return ReturnT.SUCCESS;
    }

    @XxlJob("check-fund-result-alert")
    public ReturnT<?> checkCostGap1(String param){
        checkCost();
        return ReturnT.SUCCESS;
    }

    @XxlJob("check-ttcpabid-alert")
    public ReturnT<?> checkTTCpaBidTask(String param){
        checkTTCpaBid();
        return ReturnT.SUCCESS;
    }

    private void checkClick(){
        List<ClickCheckBean> clickCheckBeans  = clickHouseDwdMapper.queryClick();
        List<ClickCheckBean> callBackRateList = clickCheckBeans.stream()
                .filter(r -> alertDelayJobService.canSend(AlertJobDelayModel.CLICK_EXPOSURE.getJobId()+"_"+r.getProduct()))
                .collect(Collectors.toList());
        if (callBackRateList.size() > 0){
            long pageSize = 6;
            for (int page = 0; page <= callBackRateList.size()/pageSize; page++){
                buildAndSendClick(callBackRateList.stream().skip(page *pageSize).limit(pageSize).collect(Collectors.toList()));
            }
        }
    }

    private void checkSdkReturnProPortion(){
        List<SdkReturnBean> sdkCheckBeans  = clickHouseDwdMapper.querySdkReturn();
        List<SdkReturnBean> callBackRateList = sdkCheckBeans.stream()
                .filter(r -> alertDelayJobService.canSend(AlertJobDelayModel.SDK_RETURN_PROPORTION_HIGH.getJobId()+"_"+r.getProduct()))
                .collect(Collectors.toList());
        if (callBackRateList.size() > 0){
            long pageSize = 6;
            for (int page = 0; page <= callBackRateList.size()/pageSize; page++){
                buildAndSendSdkReturn(callBackRateList.stream().skip(page *pageSize).limit(pageSize).collect(Collectors.toList()));
            }
        }
    }

    private void checkChannelNaturalProPortion(){
        List<ChannelNaturalProportionBean> naturalBeans  = clickHouseDwdMapper.queryChannelNaturalProPortion();
        List<ChannelNaturalProportionBean> callBackRateList = naturalBeans.stream()
                .filter(r -> alertDelayJobService.canSend(AlertJobDelayModel.CHANNEL_NATURAL_PROPORTION.getJobId()+"_"+r.getProduct()))
                .collect(Collectors.toList());
        if (callBackRateList.size() > 0){
            long pageSize = 6;
            for (int page = 0; page <= callBackRateList.size()/pageSize; page++){
                buildAndSendChannelNaturalProportion(callBackRateList.stream().skip(page *pageSize).limit(pageSize).collect(Collectors.toList()));
            }
        }
    }

    private void checkHourPv(){
        List<ChannelHourPv> hourPvBeans  = clickHouseDwdMapper.queryChannelHourPv();
        List<ChannelHourPv> callBackRateList = hourPvBeans.stream()
                .filter(r -> alertDelayJobService.canSend(AlertJobDelayModel.CHANNEL_HOUR_PV.getJobId()+"_"+r.getProduct()+"_"+r.getChannel()))
                .collect(Collectors.toList());
        if (callBackRateList.size() > 0){
            long pageSize = 6;
            for (int page = 0; page <= callBackRateList.size()/pageSize; page++){
                buildAndSendChannelHourPv(callBackRateList.stream().skip(page *pageSize).limit(pageSize).collect(Collectors.toList()));
            }
        }
    }

    private void productNoExposureProPortion(){
        List<ProductNoExposurePropBean> noExposureBeans  = clickHouseDwdMapper.queryProductNoExposureProPortion();
        List<ProductNoExposurePropBean> callBackRateList = noExposureBeans.stream()
                .filter(r -> alertDelayJobService.canSend(AlertJobDelayModel.PRODUCT_NO_EXPOSURE_PROPORTION.getJobId()+"_"+r.getProduct()))
                .collect(Collectors.toList());
        if (callBackRateList.size() > 0){
            long pageSize = 6;
            for (int page = 0; page <= callBackRateList.size()/pageSize; page++){
                buildAndSendProductNoExposureProportion(callBackRateList.stream().skip(page *pageSize).limit(pageSize).collect(Collectors.toList()));
            }
        }
    }

    private void wookmarkScreenPv(){
        List<WookmarkPvBean> wookmarkPvBeans  = clickHouseDwdMapper.wookmarkScreenPv();
        List<WookmarkPvBean> callBackRateList = wookmarkPvBeans.stream()
                .filter(r -> alertDelayJobService.canSend(AlertJobDelayModel.WOOKMARK_SCREEN_PV.getJobId()+"_"+r.getProduct()))
                .collect(Collectors.toList());
        if (callBackRateList.size() > 0){
            long pageSize = 6;
            for (int page = 0; page <= callBackRateList.size()/pageSize; page++){
                buildWookmarkScreenPvProportion(callBackRateList.stream().skip(page *pageSize).limit(pageSize).collect(Collectors.toList()));
            }
        }
    }


    private void checkTTCpaBid(){
        List<TTCpaBidBean> results = clickHouseDwdMapper.queryTTCpaBidTask();
        if (results.size() > 0){
            String alert = results.stream()
                    .map(r -> String.format("日期:%s,有消耗计划:%s,未匹配到出价计划:**%s**,占比:**%s**",
                            r.getDataDate(), r.getCostAd(),r.getNoCpaAd(),format(r.getRate())))
                    .collect(Collectors.joining(";\n\n"));
            AlertRecord alertRecord = alertRecordService.insertAlertRecord(AlertJobDelayModel.ALL_AD_CPA_BID.getJobId(),
                    AlertJobDelayModel.ALL_AD_CPA_BID.getJobName(), AlertModel.CPA_BID_CHECK,alert
                    , AlertStatus.INIT, DingTailService.dsetData, AlertType.DINGDING);
            DingTailService.sendMarkdownMsg(alertRecord, AlertRoute.SJXT.value);
        }
    }


    private void checkCost(){
        List<CostGapBean> results = clickHouseDwdMapper.queryCostGap1();
        if (results.size() > 0){
            String alert = results.stream()
                    .map(r -> String.format("日期:%s,平台:%s,fund:**%s**,result:**%s**,gap:**%s**",
                            r.getDate(), r.getDsp(),format(r.getCostFund()),format(r.getCostResult()),format(r.getGap())))
                    .collect(Collectors.joining(";\n\n _ _ _ \n "));
            AlertRecord alertRecord = alertRecordService.insertAlertRecord(AlertJobDelayModel.ALL_AD_CPA_BID.getJobId(),
                    AlertJobDelayModel.ALL_AD_CPA_BID.getJobName(), AlertModel.ADVERTISER_COST_CHECK,alert
                    , AlertStatus.INIT, DingTailService.dsetData, AlertType.DINGDING);
            DingTailService.sendMarkdownMsg(alertRecord, AlertRoute.SJXT.value);
        }
    }

    private void buildAndSendClick(List<ClickCheckBean> callBackRateList){
        if (callBackRateList == null || callBackRateList.size() == 0){
            return;
        }
        List<String> productList = callBackRateList.stream().map(ClickCheckBean::getProduct).collect(Collectors.toList());
        String alert = callBackRateList.stream()
                .map(r -> String.format("过去十分钟,产品:%s(%s),今日click **%s**,昨日click **%s**,波动率:**%s**%%",
                        r.getProductName(), r.getProduct(),r.getCountToday(),r.getCountYesterday(),format(r.getRateCount()))
                )
                .collect(Collectors.joining(";\n\n _ _ _ \n "));
        AlertRecord alertRecord = alertRecordService.insertAlertRecord(AlertJobDelayModel.CLICK_EXPOSURE.getJobId(),
                AlertJobDelayModel.CLICK_EXPOSURE.getJobName(), AlertModel.CLICK_ALERT,alert
                , AlertStatus.INIT, DingTailService.dsetData, AlertType.DINGDING);
        DingTailService.sendMarkdownMsgDelay(alertRecord, AlertRoute.ZT_XQ.value,AlertJobDelayModel.CLICK_EXPOSURE,productList);
    }

    private void buildAndSendSdkReturn(List<SdkReturnBean> callBackRateList){
        if (callBackRateList == null || callBackRateList.size() == 0){
            return;
        }
        List<String> productList = callBackRateList.stream().map(SdkReturnBean::getProduct).collect(Collectors.toList());
        String alert = callBackRateList.stream()
                .map(r -> String.format("产品:%s(%s),今日pv **%s**,sdk的pv **%s**,比例:**%s**%%",
                        r.getProductName(), r.getProduct(),r.getPv1(),r.getPv2(),format(r.getGap()))
                )
                .collect(Collectors.joining(";\n\n _ _ _ \n "));
        AlertRecord alertRecord = alertRecordService.insertAlertRecord(AlertJobDelayModel.SDK_RETURN_PROPORTION_HIGH.getJobId(),
                AlertJobDelayModel.SDK_RETURN_PROPORTION_HIGH.getJobName(), AlertModel.SDK_RETURN_PROPORTION_HIGH,alert
                , AlertStatus.INIT, DingTailService.dsetData, AlertType.DINGDING);
        DingTailService.sendMarkdownMsgDelay(alertRecord, AlertRoute.ZT_XQ.value,AlertJobDelayModel.SDK_RETURN_PROPORTION_HIGH,productList);
        //DingTailService.sendMarkdownMsgDelay(alertRecord, AlertRoute.SJWT.value,AlertJobDelayModel.SDK_RETURN_PROPORTION_HIGH,productList);
    }

    private void buildAndSendChannelNaturalProportion(List<ChannelNaturalProportionBean> callBackRateList){
        if (callBackRateList == null || callBackRateList.size() == 0){
            return;
        }
        List<String> productList = callBackRateList.stream().map(ChannelNaturalProportionBean::getProduct).collect(Collectors.toList());
        String alert = callBackRateList.stream()
                .map(r -> String.format("%s 产品:**%s**(%s),渠道:**%s**,总新增: **%s**,自然量新增: **%s**,占比:**%s**%%",
                        r.getProductGroup(),r.getProductName(),r.getProduct(),r.getChannel(), r.getNu(),r.getNnu(),format(r.getProportion()*100))
                )
                .collect(Collectors.joining(";\n\n _ _ _ \n "));
        AlertRecord alertRecord = alertRecordService.insertAlertRecord(AlertJobDelayModel.CHANNEL_NATURAL_PROPORTION.getJobId(),
                AlertJobDelayModel.CHANNEL_NATURAL_PROPORTION.getJobName(), AlertModel.CHANNEL_NATURAL_PROPORTION,alert
                , AlertStatus.INIT, DingTailService.bsData, AlertType.DINGDING);
        DingTailService.sendMarkdownMsgDelay(alertRecord, AlertRoute.BUSINESS.value,AlertJobDelayModel.CHANNEL_NATURAL_PROPORTION,productList);
        //DingTailService.sendMarkdownMsgDelay(alertRecord, AlertRoute.SJWT.value,AlertJobDelayModel.CHANNEL_NATURAL_PROPORTION,productList);
    }

    private void buildAndSendChannelHourPv(List<ChannelHourPv> channelHourPvList){
        if (channelHourPvList == null || channelHourPvList.size() == 0){
            return;
        }
        List<String> productList = channelHourPvList.stream().map(r -> r.getProduct()+"("+r.getChannel()+")").collect(Collectors.toList());
        String alert = channelHourPvList.stream()
                .map(r -> String.format("%s 产品:**%s**(%s),渠道:**%s**,渠道新增：**%s**,渠道平均pv: **%s**,总新增：**%s**,总平均pv: **%s**",
                        r.getProductGroup(),r.getProductName(),r.getProduct(),r.getChannel(),r.getChannelDnu(), format(r.getChannelAvgPv()),r.getTotalDnu(),format(r.getAvgPv()))
                )
                .collect(Collectors.joining(";\n\n _ _ _ \n "));
        AlertRecord alertRecord = alertRecordService.insertAlertRecord(AlertJobDelayModel.CHANNEL_HOUR_PV.getJobId(),
                AlertJobDelayModel.CHANNEL_HOUR_PV.getJobName(), AlertModel.CHANNEL_HOUR_PV,alert
                , AlertStatus.INIT, DingTailService.dailyData, AlertType.DINGDING);
        DingTailService.sendMarkdownMsgDelay(alertRecord, AlertRoute.JISHU.value,AlertJobDelayModel.CHANNEL_HOUR_PV,productList);
        //DingTailService.sendMarkdownMsgDelay(alertRecord, AlertRoute.SJWT.value,AlertJobDelayModel.CHANNEL_HOUR_PV,productList);
    }

    private void buildAndSendProductNoExposureProportion(List<ProductNoExposurePropBean> callBackRateList){
        if (callBackRateList == null || callBackRateList.size() == 0){
            return;
        }
        List<String> productList = callBackRateList.stream().map(ProductNoExposurePropBean::getProduct).collect(Collectors.toList());
        String alert = callBackRateList.stream()
                .map(r -> String.format("%s 产品:%s(%s),os:**%s**, 总新增: **%s**,无激励视频曝光新增: **%s**,占比:**%s**%%,无曝光新增: **%s**,占比:**%s**%%,新增总收入: **%s**",
                        r.getProductGroup(), r.getProductName(),r.getProduct(),r.getOs(),r.getNu(),r.getZinu(),format(r.getProportion()*100),r.getZinu2(),format(r.getExposureProportion()*100),r.getIncome())
                )
                .collect(Collectors.joining(";\n\n _ _ _ \n "));
        AlertRecord alertRecord = alertRecordService.insertAlertRecord(AlertJobDelayModel.PRODUCT_NO_EXPOSURE_PROPORTION.getJobId(),
                AlertJobDelayModel.PRODUCT_NO_EXPOSURE_PROPORTION.getJobName(), AlertModel.PRODUCT_NO_EXPOSURE_PROPORTION,alert
                , AlertStatus.INIT, DingTailService.bsData, AlertType.DINGDING);
        DingTailService.sendMarkdownMsgDelay(alertRecord, AlertRoute.BUSINESS.value,AlertJobDelayModel.PRODUCT_NO_EXPOSURE_PROPORTION,productList);
        //DingTailService.sendMarkdownMsgDelay(alertRecord, AlertRoute.SJWT.value,AlertJobDelayModel.PRODUCT_NO_EXPOSURE_PROPORTION,productList);
    }
    private void buildWookmarkScreenPvProportion(List<WookmarkPvBean> callBackRateList){
        if (callBackRateList == null || callBackRateList.size() == 0){
            return;
        }
        List<String> productList = callBackRateList.stream().map(WookmarkPvBean::getProduct).collect(Collectors.toList());
        String alert = callBackRateList.stream()
                .map(r -> String.format("%s 产品:%s(%s),os:**%s**,渠道:**%s**,sdk版本:**%s**" +
                                ",今日dau: **%s**,今日穿优快百激励视频pv: **%s**,其中瀑布流pv: **%s**,今日瀑布流pv占比:**%s**%%。",
                        r.getProductGroup(), r.getProductName(),r.getProductCode(),r.getOs(),r.getChannel(),r.getSdkVersion()
                        ,r.getDau(),r.getPv(),r.getTodayWookmarkPv(),format(r.getTodayWookmarkPvProp()))
                )
                .collect(Collectors.joining(";\n\n _ _ _ \n "));
        AlertRecord alertRecord = alertRecordService.insertAlertRecord(AlertJobDelayModel.WOOKMARK_SCREEN_PV.getJobId(),
                AlertJobDelayModel.WOOKMARK_SCREEN_PV.getJobName(), AlertModel.WOOKMARK_SCREEN_PV,alert
                , AlertStatus.INIT, DingTailService.bsData, AlertType.DINGDING);
        DingTailService.sendMarkdownMsgDelay(alertRecord, AlertRoute.BUSINESS.value,AlertJobDelayModel.WOOKMARK_SCREEN_PV,productList);
        //DingTailService.sendMarkdownMsgDelay(alertRecord, AlertRoute.SJWT.value,AlertJobDelayModel.WOOKMARK_SCREEN_PV,productList);
    }


}
