package com.shinet.core.alert.controller;

import com.shinet.core.alert.clickhouse.service.ArpuCheckService;
import com.shinet.core.alert.data.AdArpuCheckTimer;
import com.shinet.core.alert.data.entity.DataSlsError;
import com.shinet.core.alert.dingtalk.SLSService;
import com.shinet.core.alert.vo.ReturnResult;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.PostConstruct;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Slf4j
@RestController
public class Test {
    @Autowired
    private ArpuCheckService arpuCheckService;
//    @PostConstruct
    public void dingdingAuth(){
        try {
//            arpuCheckService.checkPV();
//            arpuCheckService.checkPVAllDev();
        }catch (Exception e){
            log.error("",e);
        }
    }
}
