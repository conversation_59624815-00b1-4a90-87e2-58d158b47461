package com.shinet.core.alert.data;

import com.shinet.core.alert.clickhouse.entity.KuaishouZtCostBaen;
import com.shinet.core.alert.clickhouse.entity.OrderFeeBean;
import com.shinet.core.alert.clickhouse.entity.ToutiaoCompanyCostBean;
import com.shinet.core.alert.clickhouse.mapper.ck1.ClickHouseDwdMapper;
import com.shinet.core.alert.clickhouse.service.KuaishouStarCostService;
import com.shinet.core.alert.coreservice.entity.AlertRecord;
import com.shinet.core.alert.coreservice.enums.*;
import com.shinet.core.alert.coreservice.service.AlertDelayJobService;
import com.shinet.core.alert.coreservice.service.AlertRecordService;
import com.shinet.core.alert.dingtalk.DingTailService;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2023/10/30
 */
@Component
public class ToutiaoCostTimer {

    @Resource
    private ClickHouseDwdMapper clickHouseDwdMapper;

    @Autowired
    private AlertDelayJobService alertDelayJobService;
    @Autowired
    AlertRecordService alertRecordService;


    /*@PostConstruct
    public void test() {
            checkToutiaoCompanyCost("daily_result");
    }*/

    @XxlJob("toutiao-company-cost-daily")
    public ReturnT<?> checkToutiaoCompanyCost(String param){
        checkTTCost();
        return ReturnT.SUCCESS;
    }

    private void checkTTCost(){
        List<ToutiaoCompanyCostBean> ToutiaoCompanyCostBean = clickHouseDwdMapper.checkTTCost();
        if (ToutiaoCompanyCostBean.size() > 0){
            String alert = ToutiaoCompanyCostBean.stream()
                    .map(r -> String.format("主体:%s,近三日总消耗:**%s**,主体剩余赠款总金额数:**%s**,有消耗账户数:**%s**\n\n",
                            r.getCompanyName(), format(r.getAllCost()), format(r.getTotalValidGrants()),r.getCostAdCounts()))
                    .collect(Collectors.joining(";\n\n _ _ _ \n "));
            AlertRecord alertRecord = alertRecordService.insertAlertRecord(AlertJobDelayModel.TOUTIAO_COMPANY_COST_DAILY.getJobId(),
                    AlertJobDelayModel.TOUTIAO_COMPANY_COST_DAILY.getJobName(), AlertModel.TOUTIAO_COMPANY_COST_DAILY,alert
                    , AlertStatus.INIT, DingTailService.ttCompanyData, AlertType.DINGDING);
            DingTailService.sendMarkdownMsg(alertRecord, AlertRoute.ZZZLH.value);
            //DingTailService.sendMarkdownMsg(alertRecord, AlertRoute.SJWT.value);
        }
    }

    private static String format(Double value){
        BigDecimal bd = new BigDecimal(value);
        bd = bd.setScale(2, RoundingMode.HALF_UP);
        return bd.toString();
    }

}
