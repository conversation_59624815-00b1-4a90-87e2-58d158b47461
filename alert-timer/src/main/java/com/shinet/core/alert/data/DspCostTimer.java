package com.shinet.core.alert.data;

import com.shinet.core.alert.clickhouse.entity.ToutiaoDrCostBean;
import com.shinet.core.alert.clickhouse.mapper.ck1.ClickHouseDwdMapper;
import com.shinet.core.alert.coreservice.entity.AlertRecord;
import com.shinet.core.alert.coreservice.enums.*;
import com.shinet.core.alert.coreservice.service.AlertDelayJobService;
import com.shinet.core.alert.coreservice.service.AlertRecordService;
import com.shinet.core.alert.dingtalk.DingTailService;
import com.shinet.core.alert.dsp.entity.DspCostAlertBean;
import com.shinet.core.alert.dsp.mapper.DspCostAlertMapper;
import com.shinet.core.alert.dsp.service.ProductService;
import com.shinet.core.alert.util.DateUtils;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.joda.time.LocalDate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2023/10/30
 */
@Component
public class DspCostTimer {

    @Resource
    private DspCostAlertMapper dspCostAlertMapper;
    @Resource
    private ClickHouseDwdMapper clickHouseDwdMapper;

    @Autowired
    private AlertDelayJobService alertDelayJobService;
    @Autowired
    AlertRecordService alertRecordService;
    @Autowired
    ProductService productService;


    @XxlJob("check-allDsp-cost-alert")
    public ReturnT<?> checkAllDspCostTask(String param){

        checkAllDspCost();
        return ReturnT.SUCCESS;
    }

    private void checkAllDspCost(){
        String today = DateUtils.formatDateForYMD(new Date());

        List<String> productList = new ArrayList<>();
        Set<String> teamSet = new HashSet<>();
        List<DspCostAlertBean> dspDaliyCost = dspCostAlertMapper.getDspDaliyCost();

        List<String> costAlertList = new ArrayList<>();
        if (dspDaliyCost.size() > 0) {
            productList.addAll(dspDaliyCost.stream().map(DspCostAlertBean::getAppName).collect(Collectors.toList()));
            teamSet.addAll(dspDaliyCost.stream().peek(this::addTeamName).map(DspCostAlertBean::getTeamName).filter(Objects::nonNull).collect(Collectors.toSet()));
            String alert = dspDaliyCost.stream()
                    .map(r -> String.format("日期:%s,\r\n平台:%s,\r\n产品:**%s**,\r\n账户:**%s**,\r\n消耗:**%s**",
                            today, r.getDsp(),r.getAppName(),r.getAdvertiserId(),r.getCost()))
                    .collect(Collectors.joining(";\n\n _ _ _ \n "));
            costAlertList.add(alert);
        }

        //今日星任务消耗
        List<DspCostAlertBean> daRenTskCostList = dspCostAlertMapper.getDaRenTaskDaliyCost();
        if (daRenTskCostList.size() > 0){
            productList.addAll(daRenTskCostList.stream().map(DspCostAlertBean::getAppName).collect(Collectors.toList()));
            teamSet.addAll(daRenTskCostList.stream().peek(this::addTeamName).map(DspCostAlertBean::getTeamName).filter(Objects::nonNull).collect(Collectors.toSet()));
            String alert = daRenTskCostList.stream()
                    .map(r -> String.format("日期:%s,\r\n平台:%s,\r\n产品:**%s**,\r\n账户:**%s**,\r\n任务id:**%s**,\r\n消耗:**%s**",
                            today, "快手-星任务",r.getAppName(),r.getAdvertiserId(),r.getTaskId(),r.getCost()))
                    .collect(Collectors.joining(";\n\n _ _ _ \n "));
            costAlertList.add(alert);
        }

        //今日单助推订单消耗
        List<DspCostAlertBean> daRenZTDaliyCost =  dspCostAlertMapper.getDaRenZTDaliyCost();

        if (daRenZTDaliyCost.size() > 0){
            productList.addAll(daRenTskCostList.stream().map(DspCostAlertBean::getAppName).collect(Collectors.toList()));
            teamSet.addAll(daRenTskCostList.stream().peek(this::addTeamName).map(DspCostAlertBean::getTeamName).filter(Objects::nonNull).collect(Collectors.toSet()));
            String alert = daRenZTDaliyCost.stream()
                    .map(r -> String.format("日期:%s,\r\n平台:%s,\r\n产品:**%s**,\r\n账户:**%s**,\r\n任务id:**%s**,\r\n订单id:**%s**,\r\n助推id:**%s**,\r\n消耗:**%s**",
                            today, "kuaishouDaRen",r.getAppName(),r.getAdvertiserId(),r.getTaskId(),r.getOrderId(),r.getSupplementOrderId(),r.getCost()))
                    .collect(Collectors.joining(";\n\n _ _ _ \n "));
            costAlertList.add(alert);
        }

        //流量助推单产品消耗（昨天+今天）
        String yesterday = LocalDate.now().minusDays(1).toString();
        List<Integer> alarmLevels = Arrays.asList(50000,200000);
        for (Integer alarmLevel : alarmLevels) {
            List<DspCostAlertBean> todayHighApps = dspCostAlertMapper.getAppCost(alarmLevel);
            if (CollectionUtils.isNotEmpty(todayHighApps)) {

                if (todayHighApps.size() > 0) {
                    productList.addAll(todayHighApps.stream().map(DspCostAlertBean::getAppName).collect(Collectors.toList()));
                    teamSet.addAll(todayHighApps.stream().peek(this::addTeamName).map(DspCostAlertBean::getTeamName).filter(Objects::nonNull).collect(Collectors.toSet()));
                    String alert = todayHighApps.stream()
                            .map(r -> String.format("日期:**%s**,\r\n平台:%s,\r\n产品:**%s**,\r\n总消耗:**%s**",
                                    today, "流量助推单产品",r.getAppName(),r.getCost()))
                            .collect(Collectors.joining(";\n\n _ _ _ \n "));
                    costAlertList.add(alert);
                }
            }
        }

        String alert = StringUtils.join(costAlertList, ";\n\n _ _ _ \n ");
        Set<String> pushData = teamSet.stream().flatMap(team -> DingTailService.zengzhangPushMap.get(team).stream()).collect(Collectors.toSet());
        if (StringUtils.isNotBlank(alert)) {
            AlertRecord alertRecord = alertRecordService.insertAlertRecord(AlertJobDelayModel.ALL_DSP_COST.getJobId(),
                    AlertJobDelayModel.ALL_DSP_COST.getJobName(), AlertModel.ADVERTISER_COST_CHECK,alert
                    , AlertStatus.INIT, pushData, AlertType.DINGDING);
            DingTailService.sendMarkdownMsgDelay(alertRecord, AlertRoute.ZENGZHANG.value,AlertJobDelayModel.ALL_DSP_COST,productList);
        }
    }

    @XxlJob("check-cpaBid-alert")
    public ReturnT<?> checkAllDspCpaBidTask(String param){

        checkAllDspCpaBid();
        return ReturnT.SUCCESS;
    }


    private void checkAllDspCpaBid(){
        String today = DateUtils.formatDateForYMD(new Date());
        List<String> productList = new ArrayList<>();
        String skipAdvertiserIds = "1788855994756170,1788855994054858,1788855993285657,1788855992514644,1788855991750667,1788853292270810,1788855864079435,1788855863198729,1788855862496299,1788855861811274,1788855860885578,1788852892400971,1768760336276487,1768760335080455,1768760284737544,1761764124201038,1760312099459085,60823539";
        Set<String> teamSet = new HashSet<>();
        List<DspCostAlertBean> dspDaliyList = dspCostAlertMapper.getAdvertiserCpaBid();
        dspDaliyList = dspDaliyList.stream().filter(f -> !Objects.equals(f.getAdId(),"12134427154") && !skipAdvertiserIds.contains(f.getAdvertiserId())).collect(Collectors.toList());
        List<String> costAlertList = new ArrayList<>();
        dspDaliyList = dspDaliyList.stream()
                .filter(r -> alertDelayJobService.canSend(AlertJobDelayModel.ALL_AD_CPA_BID.getJobId()+"_"+r.getAppName()))
                .collect(Collectors.toList());
        if (dspDaliyList.size() > 0) {
            productList.addAll(dspDaliyList.stream().map(DspCostAlertBean::getAppName).collect(Collectors.toList()));
            teamSet.addAll(dspDaliyList.stream().peek(this::addTeamName).map(DspCostAlertBean::getTeamName).filter(Objects::nonNull).collect(Collectors.toSet()));
            String alert = dspDaliyList.stream()
                    .map(r -> String.format("日期:%s,\r\n平台:%s,\r\n产品:**%s**,\r\n账户:**%s**,\r\n计划:**%s**,\r\n出价:**%s**",
                            today, r.getDsp(),r.getAppName(),r.getAdvertiserId(),r.getAdId(),r.getCpaBid()))
                    .collect(Collectors.joining(";\n\n _ _ _ \n "));
            costAlertList.add(alert);
        }

        //助推出价
        List<DspCostAlertBean> daRenZTDaliyCost =  dspCostAlertMapper.getAdvertiserDarenCpaBid();

        daRenZTDaliyCost = daRenZTDaliyCost.stream()
                .filter(r -> alertDelayJobService.canSend(AlertJobDelayModel.ALL_AD_CPA_BID.getJobId()+"_"+r.getAppName()))
                .collect(Collectors.toList());
        if (daRenZTDaliyCost.size() > 0){
            productList.addAll(daRenZTDaliyCost.stream().map(DspCostAlertBean::getAppName).collect(Collectors.toList()));
            teamSet.addAll(daRenZTDaliyCost.stream().peek(this::addTeamName).map(DspCostAlertBean::getTeamName).filter(Objects::nonNull).collect(Collectors.toSet()));
            String alert = daRenZTDaliyCost.stream()
                    .map(r -> String.format("日期:%s,\r\n平台:%s,\r\n产品:**%s**,\r\n账户:**%s**,\r\n任务id:**%s**,\r\n订单id:**%s**,\r\n助推id:**%s**,\r\n出价:**%s**",
                            today, "kuaishouDaRen",r.getAppName(),r.getAdvertiserId(),r.getTaskId(),r.getOrderId(),r.getSupplementOrderId(),r.getCpaBid()))
                    .collect(Collectors.joining(";\n\n _ _ _ \n "));
            costAlertList.add(alert);
        }
        List<DspCostAlertBean> daRenOrderCost =  dspCostAlertMapper.getDaRenStarOrderCpaBid();

        daRenOrderCost = daRenOrderCost.stream()
                .filter(r -> alertDelayJobService.canSend(AlertJobDelayModel.ALL_AD_CPA_BID.getJobId()+"_"+r.getAppName()))
                .collect(Collectors.toList());
        if (daRenOrderCost.size() > 0){
            productList.addAll(daRenOrderCost.stream().map(DspCostAlertBean::getAppName).collect(Collectors.toList()));
            teamSet.addAll(daRenOrderCost.stream().peek(this::addTeamName).map(DspCostAlertBean::getTeamName).filter(Objects::nonNull).collect(Collectors.toSet()));
            String alert = daRenOrderCost.stream()
                    .map(r -> String.format("日期:%s,\r\n平台:%s,\r\n产品:**%s**,\r\n账户:**%s**,\r\n任务id:**%s**,\r\n订单id:**%s**,\r\n出价:**%s**",
                            today,r.getDsp(),r.getAppName(),r.getAdvertiserId(),r.getTaskId(),r.getOrderId(),r.getCpaBid()))
                    .collect(Collectors.joining(";\n\n _ _ _ \n "));
            costAlertList.add(alert);
        }
        String alert = StringUtils.join(costAlertList, ";\n\n _ _ _ \n ");
        Set<String> pushData = teamSet.stream().flatMap(team -> DingTailService.zengzhangPushMap.get(team).stream()).collect(Collectors.toSet());
        if (StringUtils.isNotBlank(alert)) {
            AlertRecord alertRecord = alertRecordService.insertAlertRecord(AlertJobDelayModel.ALL_AD_CPA_BID.getJobId(),
                    AlertJobDelayModel.ALL_AD_CPA_BID.getJobName(), AlertModel.AD_CPA_BID_CHECK,alert
                    , AlertStatus.INIT, pushData, AlertType.DINGDING);
            DingTailService.sendMarkdownMsgDelay(alertRecord, AlertRoute.ZENGZHANG.value,AlertJobDelayModel.ALL_AD_CPA_BID,productList);
        }
    }


    @XxlJob("check-optimization-alert")
    public ReturnT<?> checkAllDspOptimizationTask(String param){

        checkAllDspOptimization();
        return ReturnT.SUCCESS;
    }


    private void checkAllDspOptimization(){
        String today = DateUtils.formatDateForYMD(new Date());
        List<String> productList = new ArrayList<>();

        Set<String> teamSet = new HashSet<>();
        List<DspCostAlertBean> dspDaliyList = dspCostAlertMapper.getAdvertiserOptimization();
        List<String> costAlertList = new ArrayList<>();
        if (dspDaliyList.size() > 0) {
            productList.addAll(dspDaliyList.stream().map(DspCostAlertBean::getAppName).collect(Collectors.toList()));
            teamSet.addAll(dspDaliyList.stream().peek(this::addTeamName).map(DspCostAlertBean::getTeamName).filter(Objects::nonNull).collect(Collectors.toSet()));
            String alert = dspDaliyList.stream()
                    .map(r -> String.format("日期:%s,\r\n平台:%s,\r\n产品:**%s**,\r\n账户:**%s**,\r\n优化目标:**%s**",
                            today, r.getDsp(),r.getAppName(),r.getAdvertiserId(),r.getOptimization()))
                    .collect(Collectors.joining(";\n\n _ _ _ \n "));
            costAlertList.add(alert);
        }

        //助推出价
        List<DspCostAlertBean> daRenZTDaliyCost =  dspCostAlertMapper.getAdvertiserDarenOptimization();

        if (daRenZTDaliyCost.size() > 0){
            productList.addAll(daRenZTDaliyCost.stream().map(DspCostAlertBean::getAppName).collect(Collectors.toList()));
            teamSet.addAll(daRenZTDaliyCost.stream().peek(this::addTeamName).map(DspCostAlertBean::getTeamName).filter(Objects::nonNull).collect(Collectors.toSet()));
            String alert = daRenZTDaliyCost.stream()
                    .map(r -> String.format("日期:%s,\r\n平台:%s,\r\n产品:**%s**,\r\n账户:**%s**,\r\n任务id:**%s**,\r\n订单id:**%s**,\r\n助推id:**%s**,\r\n优化目标:**%s**",
                            today, "kuaishouDaRen",r.getAppName(),r.getAdvertiserId(),r.getTaskId(),r.getOrderId(),r.getSupplementOrderId(),r.getOptimization()))
                    .collect(Collectors.joining(";\n\n _ _ _ \n "));
            costAlertList.add(alert);
        }
        String alert = StringUtils.join(costAlertList, ";\n\n _ _ _ \n ");
        Set<String> pushData = teamSet.stream().flatMap(team -> DingTailService.zengzhangPushMap.get(team).stream()).collect(Collectors.toSet());
        if (StringUtils.isNotBlank(alert)) {
            AlertRecord alertRecord = alertRecordService.insertAlertRecord(AlertJobDelayModel.ALL_DSP_OPTIMIZATION.getJobId(),
                    AlertJobDelayModel.ALL_DSP_OPTIMIZATION.getJobName(), AlertModel.ADVERTISER_OPTIMIZATION,alert
                    , AlertStatus.INIT, pushData, AlertType.DINGDING);
            DingTailService.sendMarkdownMsgDelay(alertRecord, AlertRoute.ZENGZHANG.value,AlertJobDelayModel.ALL_DSP_OPTIMIZATION,productList);
        }
    }

    @XxlJob("check-amount-alert")
    public ReturnT<?> checkSupplementAmount(String param){

        checkKuaishouSupplementAmount();
        return ReturnT.SUCCESS;
    }


    private void checkKuaishouSupplementAmount(){
        String today = DateUtils.formatDateForYMD(new Date());
        List<String> productList = new ArrayList<>();

        Set<String> teamSet = new HashSet<>();
        List<DspCostAlertBean> dspDaliyList = dspCostAlertMapper.getSupplementOrderCost();
        List<String> costAlertList = new ArrayList<>();
        if (dspDaliyList.size() > 0) {
            productList.addAll(dspDaliyList.stream().map(k -> k.getAppName() + "-" +k.getSupplementOrderId()).collect(Collectors.toList()));
            teamSet.addAll(dspDaliyList.stream().peek(this::addTeamName).map(DspCostAlertBean::getTeamName).filter(Objects::nonNull).collect(Collectors.toSet()));
            String alert = dspDaliyList.stream()
                    .map(r -> String.format("产品:**%s**,\r\n账户:**%s**,\r\n任务id:**%s**,\r\n订单id:**%s**,\r\n助推id:**%s**,\r\n开始时间:**%s**,\r\n预算:**%s**,\r\n已消耗:**%s**",
                           r.getAppName(),r.getAdvertiserId(),r.getTaskId(),r.getOrderId(),r.getSupplementOrderId(),r.getBeginTime(),r.getAmount(),r.getCost()))
                    .collect(Collectors.joining(";\n\n _ _ _ \n "));
            costAlertList.add(alert);
        }
        String alert = StringUtils.join(costAlertList, ";\n\n _ _ _ \n ");
        Set<String> pushData = teamSet.stream().flatMap(team -> DingTailService.zengzhangPushMap.get(team).stream()).collect(Collectors.toSet());
        if (StringUtils.isNotBlank(alert)) {
            AlertRecord alertRecord = alertRecordService.insertAlertRecord(AlertJobDelayModel.SUPPLEMENT_AMOUNT.getJobId(),
                    AlertJobDelayModel.SUPPLEMENT_AMOUNT.getJobName(), AlertModel.SUPPLEMENT_AMOUNT_CHECK,alert
                    , AlertStatus.INIT, pushData, AlertType.DINGDING);
            DingTailService.sendMarkdownMsgDelay(alertRecord, AlertRoute.ZENGZHANG.value,AlertJobDelayModel.SUPPLEMENT_AMOUNT,productList);
        }
    }

    public DspCostAlertBean addTeamName(DspCostAlertBean dspCostAlertBean) {
        String appName = dspCostAlertBean.getAppName();
        if (StringUtils.isBlank(appName)) {
            return dspCostAlertBean;
        }
        dspCostAlertBean.setTeamName(productService.getTransTeamByCnName(appName));
        return dspCostAlertBean;
    }


    @XxlJob("check-toutiao-dr-cost")
    public ReturnT<?> checkTtDrCost(String param){
        this.checkToutiaoDRCost();
        return ReturnT.SUCCESS;
    }

    private void checkToutiaoDRCost(){

        List<ToutiaoDrCostBean> toutiaoDrCostBeans = clickHouseDwdMapper.queryToutiaoGroupIdCost();
        if (toutiaoDrCostBeans != null && toutiaoDrCostBeans.size() > 0){
            List<ToutiaoDrCostBean> toutiaoDrCostBeanList = toutiaoDrCostBeans.stream()
                    .filter(r -> alertDelayJobService.canSend(AlertJobDelayModel.TTDR_COST.getJobId()+"_"+r.getProduct()))
                    .collect(Collectors.toList());
            if (toutiaoDrCostBeanList.size() > 0){
                List<String> productList = toutiaoDrCostBeanList.stream().map(ToutiaoDrCostBean::getProduct).collect(Collectors.toList());
                Set<String> pushData = toutiaoDrCostBeanList.stream()
                        .flatMap(r -> DingTailService.zengzhangPushMap.get(r.getProductGroup()).stream())
                        .collect(Collectors.toSet());

                Map<String,List<ToutiaoDrCostBean>> trMap = toutiaoDrCostBeanList.stream().collect(Collectors.groupingBy(ToutiaoDrCostBean::getProduct));
                StringBuilder alert = new StringBuilder(String.format("#### 日期:%s \n\n 平台:巨量星图 \n\n", DateUtils.formatDateForYMD(new Date())));

                for (Map.Entry<String, List<ToutiaoDrCostBean>> entry : trMap.entrySet()) {
                    List<ToutiaoDrCostBean> list = entry.getValue();
                    ToutiaoDrCostBean s = list.get(0);
                    alert.append(String.format("产品:**%s**(%s-%s) \n\n", s.getProductName(), s.getProductGroup(), s.getProduct()));
                    alert.append(list.stream()
                            .map(r -> String.format("任务id:**%s** \n\n" +
                                    "我方预估消耗(我方预估转化数*我方投放配置出价):%s元\n\n" +
                                    "我方投放配置出价:%s元 \n\n" +
                                    "我方预估转化数:%s ", r.getGroupId(), r.getCost(), r.getCpaBid(), r.getConvert()))
                            .collect(Collectors.joining(";\n\n _ _ _\n")));
                    alert.append("\n\n _ _ _\n");
                }

                AlertRecord alertRecord = alertRecordService.insertAlertRecord(AlertJobDelayModel.TTDR_COST.getJobId(),
                        AlertJobDelayModel.TTDR_COST.getJobName(), AlertModel.TTDR_COST, alert.toString()
                        , AlertStatus.INIT, pushData, AlertType.DINGDING);
                DingTailService.sendMarkdownMsgDelay(alertRecord, AlertRoute.ZENGZHANG.value,AlertJobDelayModel.TTDR_COST,productList);
            }
        }
    }


}
