package com.shinet.core.alert.data;

import cn.hutool.core.collection.CollectionUtil;
import com.shinet.core.alert.coreservice.entity.AlertRecord;
import com.shinet.core.alert.coreservice.enums.AlertModel;
import com.shinet.core.alert.coreservice.enums.AlertStatus;
import com.shinet.core.alert.coreservice.enums.AlertType;
import com.shinet.core.alert.coreservice.service.AlertRecordService;
import com.shinet.core.alert.dingtalk.DingTailService;
import com.shinet.core.alert.promethus.CoreQpsService;
import com.shinet.core.alert.promethus.MotanQpsService;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import com.xxl.job.core.log.XxlJobLogger;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.HashSet;
import java.util.List;
import java.util.Set;

@Component
public class QpsP99Timer {
    @Autowired
    CoreQpsService coreQpsService;
    @Autowired
    private DingTailService dingTailService;
    @Autowired
    AlertRecordService alertRecordService;

    @XxlJob("alert-p99Qps")
    public ReturnT<?> startScheduleExposureAlert(String param) {
//        double bfloat = 0.3f;
//        if(StringUtils.isNotBlank(param)){
//            bfloat = Double.parseDouble(param);
//        }

        try {
            XxlJobLogger.log("开始跑 checkP99Cost");
            coreQpsService.checkP99Cost(CoreQpsService.reqBaseV2Url, "alert-p99Qps", 10, 3);//5min里面的3min开始预警
            XxlJobLogger.log("结束 checkP99Cost");
        } catch (Exception e) {
            XxlJobLogger.log(e);
        }

        try {
            XxlJobLogger.log("开始跑 checkQps");
            coreQpsService.checkQps(CoreQpsService.reqBaseV2Url, "alert-p99Qps", 40000, 3);
            XxlJobLogger.log("结束 checkQps");
        } catch (Exception e) {
            XxlJobLogger.log(e);
        }
        try {
            XxlJobLogger.log("开始跑 checkCurReq");
            coreQpsService.checkCurReq(CoreQpsService.reqBaseV2Url, "alert-p99Qps", 210, 3);
            XxlJobLogger.log("结束 checkCurReq");
        } catch (Exception e) {
            XxlJobLogger.log(e);
        }

        try {
            XxlJobLogger.log("开始跑 wayne checkP99Cost");
            coreQpsService.checkP99Cost(CoreQpsService.reqBaseUrl, "alert-p99Qps", 10, 3);//5min里面的3min开始预警
            XxlJobLogger.log("结束 wayne checkP99Cost");
        } catch (Exception e) {
            XxlJobLogger.log(e);
        }


        try {
            XxlJobLogger.log("开始跑 wayne checkQps");
            coreQpsService.checkQps(CoreQpsService.reqBaseUrl, "alert-p99Qps", 20000, 3);
            XxlJobLogger.log("结束 wayne checkQps");
        } catch (Exception e) {
            XxlJobLogger.log(e);
        }


        try {
            XxlJobLogger.log("开始跑 wayne checkCurReq");
            coreQpsService.checkCurReq(CoreQpsService.reqBaseUrl, "alert-p99Qps", 210, 3);
            XxlJobLogger.log("结束 wayne checkCurReq");
        } catch (Exception e) {
            XxlJobLogger.log(e);
        }


        //新并发
        try {
            XxlJobLogger.log("开始跑 checkV2CurReq");
            coreQpsService.checkV2CurReq("alert-p99Qps", 200, 3);
            XxlJobLogger.log("结束 checkV2CurReq");
        } catch (Exception e) {
            XxlJobLogger.log(e);
        }


        //新并发
        try {
            XxlJobLogger.log("开始跑 checkV2Qps");
            coreQpsService.checkV2Qps("alert-p99Qps", 80000, 3);
            XxlJobLogger.log("结束 checkV2Qps");
        } catch (Exception e) {
            XxlJobLogger.log(e);
        }
        return ReturnT.SUCCESS;
    }

    @XxlJob("alert-double-p99Qps")
    public ReturnT<?> startDoubleP99Qpslert(String param) {

        //新并发
        try {
            Float bfloat = 0.2f;
            if (StringUtils.isNotBlank(param)) {
                bfloat = Float.parseFloat(param);
            }
            XxlJobLogger.log("开始跑 double checkV2Qps");
            coreQpsService.checkDoubleTimeQps("alert-double-p99Qps", bfloat);
            coreQpsService.checkCoreServiceDoubleTimeQps(CoreQpsService.reqBaseUrl, "alert-double-p99Qps", bfloat, false);
            XxlJobLogger.log("结束  double checkV2Qps");
            coreQpsService.checkCoreServiceDoubleTimeQps(CoreQpsService.reqBaseV2Url, "alert-double-p99Qps", bfloat, false);

            coreQpsService.checkCoreServiceDoubleTimeQps(CoreQpsService.reqBaseV2Url, "alert-double-p99Qps", bfloat, true);
            XxlJobLogger.log("结束  double checkV2ServiceDoubleTimeQps");

        } catch (Exception e) {
            XxlJobLogger.log(e);
        }
        return ReturnT.SUCCESS;
    }

    @Autowired
    MotanQpsService motanQpsService;
    private static int WARNING_COUNT = 3;
    private static int COUNT = 0;

    @XxlJob("alert-motan-p99Qps")
    public ReturnT<?> startMotanP99Qpslert(String param) {

        if (COUNT >= WARNING_COUNT){
            COUNT = 0;
            AlertRecord alertRecord = alertRecordService.insertAlertRecord("alert-motan-p99Qps", "服务器异常", AlertModel.SYSTEM_ERROR_MESSAGE, "prometheus出现报错日志，请注意查看",
                    AlertStatus.INIT, DingTailService.dset2, AlertType.DINGDING);
            DingTailService.sendMarkdownMsg(alertRecord);
            return ReturnT.SUCCESS;
        }
        boolean sendFlag = false;
        Float bfloat = 0.2f;
        if (StringUtils.isNotBlank(param)) {
            bfloat = Float.parseFloat(param);
        }

        //新并发
        try {
            XxlJobLogger.log("开始跑 double checkV2Qps");
            motanQpsService.checkMaxQps("alert-motan-p99Qps", 40000, 5, true);
            motanQpsService.checkMaxQps("alert-motan-p99Qps", 40000, 5, false);
            XxlJobLogger.log("结束  double checkV2Qps");
        } catch (Exception e) {
            XxlJobLogger.log(e);
            sendFlag = true;
        }
        try {
            XxlJobLogger.log("开始跑 checkMotanCurReq");
            motanQpsService.checkMotanCurReq("alert-motan-p99Qps", 100, 5, true);
            motanQpsService.checkMotanCurReq("alert-motan-p99Qps", 100, 5, false);
            XxlJobLogger.log("结束  checkMotanCurReq");
        } catch (Exception e) {
            XxlJobLogger.log(e);
            sendFlag = true;
        }

        //新并发
        try {
            XxlJobLogger.log("开始跑 checkDoubleTimeQps");
            motanQpsService.checkDoubleTimeQps("alert-motan-p99Qps", bfloat, true);
            motanQpsService.checkDoubleTimeQps("alert-motan-p99Qps", bfloat, false);
            XxlJobLogger.log("结束  checkDoubleTimeQps");
        } catch (Exception e) {
            XxlJobLogger.log(e);
            sendFlag = true;
        }

        if (sendFlag) {
            COUNT+=1;
            startMotanP99Qpslert(param);
        }
        COUNT = 0;
        return ReturnT.SUCCESS;
    }


    @XxlJob("alert-http-no200")
    public ReturnT<?> httpNo200(String param) {
        coreQpsService.checkHtppN200();
        return ReturnT.SUCCESS;
    }


}
