package com.shinet.core.alert.data;

import com.shinet.core.alert.dsp.service.OcpcMisActiveService;
import com.shinet.core.alert.dsp.service.ToutiaoReportIdeadayService;
import com.shinet.core.alert.dsp.service.UserEventService;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Date;

@Component
public class OcpcAlertTimer {
    @Autowired
    ToutiaoReportIdeadayService toutiaoReportIdeadayService;
    @Autowired
    OcpcMisActiveService ocpcMisActiveService;
    @XxlJob("alert-ocpc")
    public ReturnT<?> startScheduleExposureAlert(String param) {
        double bfloat = 0.4f;
        double bigRate = 0.1f;
        if(StringUtils.isNotBlank(param)){
            String[] rateStrs = param.split(",");
            bfloat = Double.parseDouble(rateStrs[0]);
            bigRate = Double.parseDouble(rateStrs[1]);

        }
        Date date = new Date();
//        if(date.getHours()>11){
//            bfloat = 0.2f;
//        }
        toutiaoReportIdeadayService.activeAlert("alert-ocpc",bfloat,bigRate);
        return ReturnT.SUCCESS;
    }

    @XxlJob("alert-active")
    public ReturnT<?> startScheduleActiveAlert(String param) {
        double bfloat = 0.2f;
        if(StringUtils.isNotBlank(param)){
            bfloat = Double.parseDouble(param);
        }
        toutiaoReportIdeadayService.activeAllActive("alert-active",bfloat);
        return ReturnT.SUCCESS;
    }

    @XxlJob("alert-ocpc-project")
    public ReturnT<?> alertOcpcProject(String param) {
        Integer bfloat = 15;
        if(StringUtils.isNotBlank(param)){
            bfloat = Integer.parseInt(param);
        }
        int hour = new Date().getHours();
        if(hour>=9 && hour<=22){
            ocpcMisActiveService.getAlertRate("alert-ocpc-project","android",bfloat,10);
            ocpcMisActiveService.getAlertRate("alert-ocpc-project","ios",bfloat+15,10);
        }
        return ReturnT.SUCCESS;
    }



    @Autowired
    UserEventService userEventService;
    @XxlJob("alert-coreevent")
    public ReturnT<?> alertCoreEvent(String param) {
        double bfloat = 0.5f;
        if(StringUtils.isNotBlank(param)){
            bfloat = Double.parseDouble(param);
        }
        userEventService.alertCoreEvent("alert-coreevent",bfloat,25);
        return ReturnT.SUCCESS;
    }


    @XxlJob("alert-dspDauAdd")
    public ReturnT<?> dspDauAdd(String param) {
        double bfloat = 0.5f;
        if(StringUtils.isNotBlank(param)){
            bfloat = Double.parseDouble(param);
        }
        userEventService.alertCoreEvent("alert-dspDauAdd",bfloat,0);
        return ReturnT.SUCCESS;
    }
}
