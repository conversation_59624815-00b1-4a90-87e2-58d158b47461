package com.shinet.core.alert.data;

import cn.hutool.core.thread.RejectPolicy;
import cn.hutool.core.thread.ThreadUtil;
import com.shinet.core.alert.clickhouse.service.ToutiaoClickService;
import com.shinet.core.alert.dataagent.service.CommonUserService;
import com.shinet.core.alert.dataagent.service.UserMoveService;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import com.xxl.job.core.log.XxlJobLogger;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.io.File;
import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.*;

@Component
@Slf4j
public class DataCopyTimer {

    ThreadFactory threadFactory = ThreadUtil.newNamedThreadFactory("threadMoveClick", null, false);
    ExecutorService executorService = new ThreadPoolExecutor(
            3
            , 3,
            20,
            TimeUnit.SECONDS,
            new ArrayBlockingQueue<>(20),
            threadFactory,
            RejectPolicy.CALLER_RUNS.getValue()
    );
    @Autowired
    ToutiaoClickService toutiaoClickService;
    @Autowired
    CommonUserService commonUserService;
    @XxlJob("move-user")
    public ReturnT<?> moveUser(String param){
        String filePath = "/data/coohua/data/";
        boolean isDel = false;
        if(StringUtils.isNotBlank(param)){
            isDel = Boolean.parseBoolean(param);
        }
        File file = new File(filePath);
        File[] files = file.listFiles();
        for(File filecsv : files){
            if(filecsv.getAbsolutePath().indexOf("complete")>0){
                try {
                    XxlJobLogger.log("提交"+filecsv.getAbsolutePath());
                    commonUserService.moveProductUser(filecsv.getAbsolutePath(),isDel,true);
                    File pfile = commonUserService.getAlDelFile(filecsv.getAbsolutePath());
                    String rpath = pfile.getAbsolutePath()+File.separator+filecsv.getName()+"del";
                    boolean isSuc = filecsv.renameTo(new File(rpath));
                    log.info("迁移文件成功 "+isSuc+filePath+"-> "+ rpath);
                }catch (Exception e){
                    log.error("",e);
                }
            }else{
                XxlJobLogger.log("未完成文件 "+filecsv.getAbsolutePath());
            }
        }
        return ReturnT.SUCCESS;
    }


    @XxlJob("moveto-user")
    public ReturnT<?> movetoUser(String param){
        String filePath = "/data/coohua/data/moveto";
        boolean isDel = false;
        if(StringUtils.isNotBlank(param)){
            isDel = Boolean.parseBoolean(param);
        }
        File file = new File(filePath);
        File[] files = file.listFiles();
        for(File filecsv : files){
            if(filecsv.getAbsolutePath().indexOf("complete")>0){
                try {
                    XxlJobLogger.log("提交"+filecsv.getAbsolutePath());
                    commonUserService.moveToProductUser(filecsv.getAbsolutePath(),isDel,true);
                    File pfile = commonUserService.getAlDelFile(filecsv.getAbsolutePath());
                    String rpath = pfile.getAbsolutePath()+File.separator+filecsv.getName()+"del";
                    boolean isSuc = filecsv.renameTo(new File(rpath));
                    log.info("迁移文件成功 "+isSuc+filePath+"-> "+ rpath);
                }catch (Exception e){
                    log.error("",e);
                }
            }else{
                XxlJobLogger.log("未完成文件 "+filecsv.getAbsolutePath());
            }
        }
        return ReturnT.SUCCESS;
    }



    @XxlJob("move-usermeta")
    public ReturnT<?> moveUsermeta(String param){
        String filePath = "/data/coohua/datausermeta/";
        boolean isDel = true;
        if(StringUtils.isNotBlank(param)){
            isDel = Boolean.parseBoolean(param);
        }
        File file = new File(filePath);
        File[] files = file.listFiles();
        for(File filecsv : files){
            if(filecsv.getAbsolutePath().indexOf("complete")>0){
                try {
                    XxlJobLogger.log("提交"+filecsv.getAbsolutePath());
                    commonUserService.moveProductUser(filecsv.getAbsolutePath(),isDel,false);
                    File pfile = commonUserService.getAlDelFile(filecsv.getAbsolutePath());
                    String rpath = pfile.getAbsolutePath()+File.separator+filecsv.getName()+"del";
                    boolean isSuc = filecsv.renameTo(new File(rpath));
                    log.info("迁移文件成功 "+isSuc+filePath+"-> "+ rpath);
                }catch (Exception e){
                    log.error("",e);
                }
            }else{
                XxlJobLogger.log("未完成文件 "+filecsv.getAbsolutePath());
            }
        }
        return ReturnT.SUCCESS;
    }

    @XxlJob("copy-toutiaoclick")
    public ReturnT<?> toutiaoclick(String param){
        String filePath = "/data/coohua/data/";
        if(StringUtils.isNotBlank(param)){
            filePath = param;
        }
        List<CompletableFuture> completableFutureList = new ArrayList<>();
        File file = new File(filePath);
        File[] files = file.listFiles();
        for(File filecsv : files){
            if(filecsv.getAbsolutePath().indexOf("complete")>0){
                XxlJobLogger.log("提交"+filecsv.getAbsolutePath());
                CompletableFuture completableFuture = CompletableFuture.runAsync(() -> {
                    try {
                        long dtime = System.currentTimeMillis();
                        toutiaoClickService.moveByFile(filecsv.getAbsolutePath());
                        log.info("logstr "+filecsv.getAbsolutePath()+"迁移完成 耗时"+(System.currentTimeMillis()-dtime));
                        XxlJobLogger.log("logstr "+filecsv.getAbsolutePath()+"迁移完成 耗时"+(System.currentTimeMillis()-dtime));
                        filecsv.delete();
                    } catch (Exception e) {
                        log.error("", e);
                    }
                }, executorService);
                completableFutureList.add(completableFuture);
            }else{
                XxlJobLogger.log("未完成文件 "+filecsv.getAbsolutePath());
            }
        }
        CompletableFuture<Void> combindFuture = CompletableFuture.allOf(completableFutureList.toArray(new CompletableFuture[completableFutureList.size()]));
        try {
            combindFuture.get();
        } catch (Exception e) {
            XxlJobLogger.log("", e);
        }
        return ReturnT.SUCCESS;
    }


    @XxlJob("copy-fileClick")
    public ReturnT<?> fileClick(String param){
        String startDate = "2021-11-01";
        if(StringUtils.isNotBlank(param)){
            startDate = param;
        }
        toutiaoClickService.threadMove(startDate);
        return ReturnT.SUCCESS;
    }
}
