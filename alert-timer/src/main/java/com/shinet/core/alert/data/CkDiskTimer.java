package com.shinet.core.alert.data;

import com.shinet.core.alert.adb.service.CkDiskCheckService;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @since 2022/4/6
 */
@Slf4j
@Component
public class CkDiskTimer {
    @Autowired
    private CkDiskCheckService ckDiskCheckService;

    @XxlJob("alert-clickhouse-disk")
    public ReturnT<?> clickHouse(String param){
        ckDiskCheckService.checkDiskRate();
        return ReturnT.SUCCESS;
    }
}
