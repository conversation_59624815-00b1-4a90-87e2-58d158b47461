package com.shinet.core.alert.data;

import com.shinet.core.alert.safe.service.IosCheckUrlService;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @since 2023/11/16
 */
@Slf4j
@Component
public class IosUrlCheckTimer {

    @Autowired
    private IosCheckUrlService iosCheckUrlService;

    @XxlJob("alert-ios-url-check")
    public ReturnT<?> checkUrlAlive(String param){
        iosCheckUrlService.checkAppStoreUrlAlive();
        iosCheckUrlService.checkUrlAlive();
        return ReturnT.SUCCESS;
    }
}
