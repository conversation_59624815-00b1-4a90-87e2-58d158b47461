package com.shinet.core.alert.data;

import com.shinet.core.alert.safe.service.VirusCheckService;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @since 2023/11/16
 */
@Slf4j
@Component
public class VirusCheckTimer {

    @Autowired
    private VirusCheckService service;

    @XxlJob("check-no-virus")
    public ReturnT<?> checkNoVirus(String param){
        service.checkNoVirus();
        return ReturnT.SUCCESS;
    }

    @XxlJob("check-no-virus-app-diff")
    public ReturnT<?> checkNoVirusAppDiff(String param){
        service.checkNoVirusAppDiff();
        return ReturnT.SUCCESS;
    }
}
