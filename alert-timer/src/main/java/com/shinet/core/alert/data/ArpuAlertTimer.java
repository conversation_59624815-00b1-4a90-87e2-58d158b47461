package com.shinet.core.alert.data;

import com.shinet.core.alert.adb.service.AdArpuRealSumService;
import com.shinet.core.alert.adb.service.DauSumService;
import com.shinet.core.alert.adb.service.RealtimeNuChannelTfAlarmService;
import com.shinet.core.alert.clickhouse.service.AdIncomePvGapCheckService;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import com.xxl.job.core.log.XxlJobLogger;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;

@Component
public class ArpuAlertTimer {
    @Autowired
    AdArpuRealSumService adArpuRealSumService;
    /**
     * 曝光预警
     */
    @XxlJob("alert-arpu")
    public ReturnT<?> startScheduleArpuAlert(String param){
        double bfloat = 0.2f;
        if(StringUtils.isNotBlank(param)){
            bfloat = Double.parseDouble(param);
        }
        adArpuRealSumService.alertArpuNv("alert-arpu",bfloat);
        return ReturnT.SUCCESS;
    }

    @Autowired
    private AdIncomePvGapCheckService adIncomePvGapCheckService;

    @XxlJob("alert-pv-gap-check")
    public ReturnT<?> pvGapCheck(String param){
        adIncomePvGapCheckService.checkProductAndPosId();
        return ReturnT.SUCCESS;
    }

    @XxlJob("alert-pv-gap-check-realtime")
    public ReturnT<?> pvGapCheckRealtime(String param){
        adIncomePvGapCheckService.checkProductGap();
        return ReturnT.SUCCESS;
    }

    @Autowired
    DauSumService dauSumService;
    @XxlJob("alert-dauadd")
    public ReturnT<?> dauadd(String param){
        float addRate = 0.4f;
        float dauRate = 0.2f;
        if(StringUtils.isNotBlank(param)){
            String[] dsf = param.split(",");
            addRate = Float.parseFloat(dsf[0]);
            dauRate = Float.parseFloat(dsf[1]);
        }
        XxlJobLogger.log("addRate="+addRate+" dauRate="+dauRate);
        dauSumService.alertDauNu("alert-dauadd",addRate,dauRate);
        return ReturnT.SUCCESS;
    }

    @Autowired
    RealtimeNuChannelTfAlarmService realtimeNuChannelTfAlarmService;
    @XxlJob("alert-dnu-ocpc")
    public ReturnT<?> dnuOcpc(String param){
        Double ocpcDnuRate = 0.2d;
        Integer deviceCount = 1000;
        if(StringUtils.isNotBlank(param)){
            String[] params = param.split(",");
            ocpcDnuRate = Double.parseDouble(params[0]);
            deviceCount = Integer.parseInt(params[1]);
        }
        XxlJobLogger.log("ocpcDnuRate="+ocpcDnuRate+" deviceCount="+deviceCount);
        realtimeNuChannelTfAlarmService.alertNuChannelTfAlarm(ocpcDnuRate,deviceCount);
        return ReturnT.SUCCESS;
    }

    @XxlJob("alert-dnu-ocpc-hour")
    public ReturnT<?> dnuOcpcHour(String param){
        Double ocpcDnuRate = 0.4d;
        Integer deviceCount = 100;
        if(StringUtils.isNotBlank(param)){
            String[] params = param.split(",");
            ocpcDnuRate = Double.parseDouble(params[0]);
            deviceCount = Integer.parseInt(params[1]);
        }
        XxlJobLogger.log("ocpcDnuRate="+ocpcDnuRate+" deviceCount="+deviceCount);
        realtimeNuChannelTfAlarmService.alertHourNuChannelTfAlarm(ocpcDnuRate,deviceCount);
        return ReturnT.SUCCESS;
    }


}
