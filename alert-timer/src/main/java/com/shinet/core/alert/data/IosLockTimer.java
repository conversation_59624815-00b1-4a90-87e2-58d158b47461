package com.shinet.core.alert.data;

import com.google.common.collect.Lists;
import com.shinet.core.alert.clickhouse.entity.IosLockBean;
import com.shinet.core.alert.clickhouse.mapper.ck1.IosLockMapper;
import com.shinet.core.alert.coreservice.entity.AlertRecord;
import com.shinet.core.alert.coreservice.enums.AlertModel;
import com.shinet.core.alert.coreservice.enums.AlertStatus;
import com.shinet.core.alert.coreservice.enums.AlertType;
import com.shinet.core.alert.coreservice.service.AlertRecordService;
import com.shinet.core.alert.dingtalk.DingTailService;
import com.shinet.core.alert.dingtalk.VedioAlertService;
import com.shinet.core.alert.safe.service.AndroidLockRstService;
import com.shinet.core.alert.safe.service.AndroidLockService;
import com.shinet.core.alert.safe.service.LockIosRstService;
import com.shinet.core.alert.util.DoubleUtil;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import com.xxl.job.core.log.XxlJobLogger;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.joda.time.DateTimeConstants;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import javax.swing.text.DateFormatter;
import java.text.DateFormat;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;

@Component
@Slf4j
public class IosLockTimer {
    @Autowired
    IosLockMapper iosLockMapper;
    @Autowired
    AlertRecordService alertRecordService;
    @XxlJob("ios-lock-alert")
    public ReturnT<?> checkChannelCostTask(String param){
        List<IosLockBean>  iosLockBeans = iosLockMapper.getIosLockRst();
        String altMsg = "";
        boolean isSendVoc = false;
        for(IosLockBean iosLockBean : iosLockBeans){
            if(iosLockBean.getBaifenbi()>5 && !StringUtils.equalsIgnoreCase(iosLockBean.getProduct(),"qzywhzios")){
                altMsg = altMsg+" "+iosLockBean.getProduct()+"->"+iosLockBean.getLockNum()+":"+iosLockBean.getAllNum()+"="+ DoubleUtil.getDoubleByTwo(iosLockBean.getBaifenbi())+"% ";
            }

            isSendVoc = isSendVoc || (iosLockBean.getLockNum()>800);
        }

        if(StringUtils.isNotBlank(altMsg)){
            if(isSendVoc){
                VedioAlertService.sendVocMsg(" ios锁区 ", altMsg);
            }
            AlertRecord alertRecord =alertRecordService.insertAlertRecord("ios-lock-alert","ios锁区",
                    AlertModel.IOS_LOCK,altMsg,0d,0d,0,0,
                    AlertStatus.INIT, DingTailService.dset, AlertType.DINGPHONE);
            DingTailService.sendMarkdownMsg(alertRecord);
        }
        return ReturnT.SUCCESS;
    }

    @Autowired
    LockIosRstService lockIosRstService;
    @XxlJob("ios-app-alert")
    public ReturnT<?> iosAppAlert(String param){
        Integer mints = 20;
        if(StringUtils.isNotBlank(param)){
            mints = Integer.parseInt(param);
        }
        Date startTime = new Date(System.currentTimeMillis()- DateTimeConstants.MILLIS_PER_MINUTE*mints);
        lockIosRstService.checkPGIos(startTime,"ios-app-alert");
        return ReturnT.SUCCESS;
    }

    @XxlJob("ios-duocenglock-alert")
    public ReturnT<?> iosLockAlert(String param){
        Integer alertMaxNum = 150;
        if(StringUtils.isNotBlank(param)){
            alertMaxNum = Integer.parseInt(param);
        }
        XxlJobLogger.log("开始iosLockAlert ip "+alertMaxNum);

        lockIosRstService.alertIosLock("ios-duocenglock-alert",alertMaxNum);
        return ReturnT.SUCCESS;
    }

    @XxlJob("ios-lock2-alert")
    public ReturnT<?> iosLockAlert2(String param){
        Integer minLockNum = 500;
        Double lockRate = 5.0d;
        if(StringUtils.isNotBlank(param)){
            minLockNum = Integer.parseInt(param.split(",")[0]);
            lockRate = Double.parseDouble(param.split(",")[1]);
        }
        lockIosRstService.alertIosLock2("ios-lock2-alert", minLockNum, lockRate);
        return ReturnT.SUCCESS;
    }


    @Autowired
    AndroidLockService androidLockService;
    @XxlJob("android-lock-alert")
    public ReturnT<?> androidAppAlert(String param){
        Integer mints = 20;
        Integer maxLockNum = 1000;
        Integer minLockNum = 100;
        Double lockRateBj = 0.1d;
        if(StringUtils.isNotBlank(param)){
            maxLockNum = Integer.parseInt(param.split(",")[0]);
            minLockNum = Integer.parseInt(param.split(",")[1]);
            lockRateBj = Double.parseDouble(param.split(",")[2]);
        }
        androidLockService.altAndroidLock("android-lock-alert",maxLockNum,minLockNum,lockRateBj);
//        androidLockService.altAndroidLockForNewUser("android-lock-alert",maxLockNum,minLockNum,lockRateBj);
        Date startTime = new Date(System.currentTimeMillis()- DateTimeConstants.MILLIS_PER_MINUTE*mints);
        lockIosRstService.checkPGIos(startTime,"ios-app-alert");
        return ReturnT.SUCCESS;
    }


    @XxlJob("android-lockstore-alert")
    public ReturnT<?> lockstoreAlt(String param){
        Integer maxLockNum = 1000;
        if(StringUtils.isNotBlank(param)){
            maxLockNum = Integer.parseInt(param);
        }
        XxlJobLogger.log("android-lockstore-alert 最大锁 "+maxLockNum);
        androidLockService.alertStoreAb("android-lockstore-alert",maxLockNum);
        return ReturnT.SUCCESS;
    }


    @XxlJob("android-caozuolock-alert")
    public ReturnT<?> caozuolock(String param){
        Integer maxLockNum = 20;
        if(StringUtils.isNotBlank(param)){
            maxLockNum = Integer.parseInt(param);
        }
        XxlJobLogger.log("android-caozuolock-alert 最大锁 "+maxLockNum);
        androidLockService.onlineStoreAlt("android-caozuolock-alert",maxLockNum);
        return ReturnT.SUCCESS;
    }

    @Autowired
    AndroidLockRstService androidLockRstService;
    @XxlJob("android-all-lock-alert")
    public ReturnT<?> androidAllLockAlert(String param){
        Integer maxAllLock = 20;
        if(StringUtils.isNotBlank(param)) {
            maxAllLock = Integer.parseInt(param);
        }
        androidLockRstService.closeAllLockNums("android-all-lock-alert", 14);
        Integer hour = Integer.valueOf(LocalDateTime.now().format(DateTimeFormatter.ofPattern("HH")));
        if (hour > 7 && hour < 23){
            androidLockRstService.getAllLockNums("android-all-lock-alert", maxAllLock);
        }

        return ReturnT.SUCCESS;
    }



    @XxlJob("android-fc-lock-alert")
    public ReturnT<?> fcAdLock(String param){
        Integer maxAllLock = 30;
        if(StringUtils.isNotBlank(param)) {
            maxAllLock = Integer.parseInt(param);
        }
        androidLockRstService.getFcLockNums("android-fc-lock-alert", maxAllLock);

        return ReturnT.SUCCESS;
    }

    /**
     * 没人关注，临时关闭
     * @param param
     * @return
     */
    @XxlJob("android-product-lock-alert")
    public ReturnT<?> androidProductAdLock(String param){
        Integer maxAllLock = 420;
        Set<String> pset = new HashSet<String>();
        if(StringUtils.isNotBlank(param)) {
            maxAllLock = Integer.parseInt(param.split(",")[0]);

            pset.addAll(Lists.newArrayList(param.split(",")[1].split("-")));
        }
        androidLockRstService.getProductLockNums("android-product-lock-alert", maxAllLock,pset);

        return ReturnT.SUCCESS;
    }

}
