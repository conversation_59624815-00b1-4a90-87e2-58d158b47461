package com.shinet.core.alert.controller;

import com.shinet.core.alert.adb.entity.OptMonthResult;
import com.shinet.core.alert.adb.mapper.DailyResultMapper;
import com.shinet.core.alert.dingtalk.DingdingSignService;
import com.shinet.core.alert.dingtalk.rsp.DingAuthRsp;
import com.shinet.core.alert.vo.ReturnResult;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

@Slf4j
@RestController
public class AdsDailyController {
    @Autowired
    DailyResultMapper dailyResultMapper;
    @GetMapping("alert/ads/dailyResult")
    @ResponseBody
    public ReturnResult dailyResult(String logMonth){
        ReturnResult returnResult = new ReturnResult();
        try {
            List<OptMonthResult>  optMonthResults = dailyResultMapper.queryRsultMap(logMonth);
            returnResult.setData(optMonthResults);
        }catch (Exception e){
            log.error("",e);
        }
        return returnResult;
    }
}
