package com.shinet.core.alert.data;

import com.shinet.core.alert.coreservice.service.AlertMallService;
import com.shinet.core.alert.data.service.MallCheckService;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Date;

@Component
public class MallAlertTimer {
    @Autowired
    MallCheckService mallCheckService;

    @XxlJob("alert-mallerror")
    public ReturnT<?> startMallError(String param){
        int errorMaxNum = 200;
        if(StringUtils.isNotBlank(param)){
            errorMaxNum = Integer.parseInt(param);
        }
        mallCheckService.checkMallErrorAlert("alert-mallerror",errorMaxNum);
        return ReturnT.SUCCESS;
    }

    @XxlJob("alert-mallsuc")
    public ReturnT<?> startMallsuc(String param){
        float errorMaxNum = 0.4f;
        if(StringUtils.isNotBlank(param)){
            errorMaxNum = Float.parseFloat(param);
        }
        mallCheckService.checkMallSucAlert("alert-mallsuc",errorMaxNum);
        return ReturnT.SUCCESS;
    }



    @XxlJob("alert-mallDoubleDaySuc")
    public ReturnT<?> mallDoubleDaySuc(String param){
        float errorMaxNum = 0.4f;
        if(StringUtils.isNotBlank(param)){
            errorMaxNum = Float.parseFloat(param);
        }
        Date date = new Date();
        if(date.getHours()==0 && date.getMinutes()<=10){
        }else{
            mallCheckService.checkMallDoubleDaySucAlert("alert-mallDoubleDaySuc",errorMaxNum);
        }
        return ReturnT.SUCCESS;
    }

    @Autowired
    AlertMallService alertMallService;
    @XxlJob("mall-dashborad")
    public ReturnT<?> dashborad(String param){
        float errorFloat = 20;
        if(StringUtils.isNotBlank(param)){
            errorFloat = Float.parseFloat(param);
        }
        alertMallService.insertMallAlertChart("mall-dashborad",errorFloat);
        return ReturnT.SUCCESS;
    }
}
