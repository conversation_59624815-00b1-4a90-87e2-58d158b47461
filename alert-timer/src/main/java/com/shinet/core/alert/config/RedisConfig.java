package com.shinet.core.alert.config;


import lombok.extern.slf4j.Slf4j;
import org.apache.commons.pool2.impl.GenericObjectPoolConfig;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import redis.clients.jedis.HostAndPort;
import redis.clients.jedis.JedisCluster;

import java.util.*;

@Slf4j
@Configuration
public class RedisConfig {

    @Value("${app.jedis-cluster.bp-user.address}")
    private String address;
    @Value("${app.jedis-cluster.bp-user.pool.max-total}")
    private int maxTotal;
    @Value("${app.jedis-cluster.bp-user.pool.max-idle}")
    private int minIdle;
    @Value("${app.jedis-cluster.bp-user.pool.min-idle}")
    private int maxIdle;
    @Value("${app.jedis-cluster.bp-user.pool.max-wait-millis}")
    private int maxWaitMillis;

    @Bean("bpJedisCluster")
    public JedisCluster bpJedisCluster() {
        Set<HostAndPort> nodes = new HashSet<HostAndPort>();
        for(String ipPort : address.split(",")) {
            nodes.add(new HostAndPort(ipPort.split(":")[0], Integer.parseInt(ipPort.split(":")[1])));
        }


        JedisCluster jedisCluster = null;
        if (!nodes.isEmpty()){
            GenericObjectPoolConfig pool = new GenericObjectPoolConfig();
            pool.setMaxTotal(maxTotal);
            pool.setMinIdle(minIdle);
            pool.setMaxIdle(maxIdle);
            pool.setMaxWaitMillis(maxWaitMillis);
            jedisCluster = new JedisCluster(nodes, pool);
        }

        return jedisCluster;
    }
}
