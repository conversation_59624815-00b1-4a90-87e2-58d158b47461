package com.shinet.core.alert.data;

import java.util.HashMap;
import java.util.Map;

public class DelSqlGen {
    public static  void main(String[] args){
        //clickhouse-client -h 172.16.40.187 --password Phw7a7A4 --database="ods_mysql" --query="select user_id,union_Id,create_time,update_time from user_meta where pkg_id =3 and user_id not in ( select user_id from user_meta where pkg_id!=3 ) FORMAT CSV" > 3udel.csv  &
        //mv /data/coohua/data/3udel.csv /data/coohua/data/3udel.csvcomplete

        Map<Integer,String> pkgMap = getDelMap();
        pkgMap.forEach((pkgId,pname) -> {
            String sqlExport = "clickhouse-client -h 172.16.11.213 --password Phw7a7A4 --database=\"ods_mysql\" " +
                    "--query=\"select userArray,pkg_id from ( select bitmapToArray(bitmapAndnot(r1.bit_map, r2.bit_map)) as userArray, "+pkgId+" as pkg_id from (select 'r' as fl, groupBitmapState(user_id) as bit_map  from ods_mysql.user_meta where pkg_id ="+pkgId+") r1 join (select 'r' as fl, groupBitmapState(user_id) as bit_map from ods_mysql.user_meta  where pkg_id != "+pkgId+") r2 on r1.fl = r2.fl ) array join userArray " +
                    "FORMAT CSV\" > "+pname+"udel.csv  ";
            System.out.println(sqlExport);
            String mvSh = "mv /data/coohua/data/"+pname+"udel.csv /data/coohua/data/"+pname+"udel.csvcomplete";
            System.out.println(mvSh);
        });


//        select user_id,pkg_id from user_meta where pkg_id=10;


//        pkgMap.forEach((pkgId,pname) -> {
//            String sqlExport = "clickhouse-client -h 172.16.11.213 --password Phw7a7A4 --database=\"ods_mysql\" " +
//                    "--query=\"select user_id,pkg_id from user_meta where pkg_id="+pkgId+" " +
//                    "FORMAT CSV\" > "+pname+"udel.csv  ";
//            System.out.println(sqlExport);
//            String mvSh = "mv /data/coohua/datausermeta/"+pname+"udel.csv /data/coohua/datausermeta/"+pname+"udel.csvcomplete";
//            System.out.println(mvSh);
//        });
    }


    private static Map<Integer,String> getDelMap(){
        Map<Integer,String> pkgMap = new HashMap<>();
//        pkgMap.put(1,"988小游戏");
//        pkgMap.put(2,"988小游戏-浏览器包名");
//        pkgMap.put(3,"步步多");
//        pkgMap.put(4,"步多");
//        pkgMap.put(5,"步步多福");
//        pkgMap.put(6,"步步多康");
//        pkgMap.put(7,"步步多运");
//        pkgMap.put(8,"步步多钱");
//        pkgMap.put(9,"步步多走");
//        pkgMap.put(10,"健步多");
//        pkgMap.put(11,"康步多");
//        pkgMap.put(12,"走路宝");
//        pkgMap.put(13,"步步多健");
//        pkgMap.put(14,"步步赚");
//        pkgMap.put(15,"全民养猪场");
//        pkgMap.put(16,"刷视频");
//        pkgMap.put(17,"合并小龙龙");
//        pkgMap.put(18,"合并小龙【停用】");
//        pkgMap.put(19,"阳光森林【停用】");
//        pkgMap.put(20,"猪多多");
//        pkgMap.put(21,"刷爆");
//        pkgMap.put(22,"刷爆短视频");
//        pkgMap.put(23,"分红视频");
//        pkgMap.put(24,"淘视频【停用】");
//        pkgMap.put(25,"淘金号");
//        pkgMap.put(26,"恐龙庄园【停用】");
//        pkgMap.put(27,"趣热点");
//        pkgMap.put(28,"养猪微信小游戏");
//        pkgMap.put(29,"恐龙世界");
//        pkgMap.put(30,"如意天气");
//        pkgMap.put(31,"阳光森林");
//        pkgMap.put(32,"王牌消消乐");
//        pkgMap.put(33,"旅行日记");
//        pkgMap.put(34,"猪猪世界");
//        pkgMap.put(35,"我爱消消消");
//        pkgMap.put(36,"爱上消消乐");
//        pkgMap.put(37,"爱上消消");
//        pkgMap.put(38,"简单天气预报");
//        pkgMap.put(39,"简单天气通");
//        pkgMap.put(40,"多多农场");
//        pkgMap.put(41,"欢乐果园");
//        pkgMap.put(42,"我爱猜成语");
//        pkgMap.put(43,"消泡泡传奇");
//        pkgMap.put(44,"欢乐消星星");
//        pkgMap.put(45,"吉日黄历");

        pkgMap.put(46,"蚂蚁果园");
        pkgMap.put(47,"萌宠消消消");
        pkgMap.put(48,"魔方短视频");
        pkgMap.put(50,"飞鸟乐园");
        pkgMap.put(51,"开心谜语");
        pkgMap.put(52,"在家看世界");
        pkgMap.put(53,"爱上鲜花");
        pkgMap.put(54,"全民摆摊");
        pkgMap.put(55,"爱上消消");
        pkgMap.put(56,"爱上消水果");
        pkgMap.put(57,"开心消星星");
        pkgMap.put(58,"天南果园");
        pkgMap.put(59,"欢海大闸蟹");
        pkgMap.put(60,"我的果园");
        pkgMap.put(61,"水果泡泡龙");
        pkgMap.put(62,"开心泡泡龙");
        pkgMap.put(63,"开心养鸡场");
        pkgMap.put(64,"欢乐农场");
        pkgMap.put(65,"欢乐养鸡场");
        pkgMap.put(66,"水果开心消");
        pkgMap.put(67,"我爱切切切");
        pkgMap.put(68,"多乐果园小程序");
        pkgMap.put(69,"我的花园");
        pkgMap.put(70,"水果爱消消");
        pkgMap.put(71,"爱上加特林");
        pkgMap.put(72,"幸福果园");
        pkgMap.put(73,"我的果园小游戏");
        pkgMap.put(74,"一起来走路");
        pkgMap.put(75,"多乐果园");
        pkgMap.put(76,"我爱猜歌");
        pkgMap.put(77,"小屋合成记");
        pkgMap.put(78,"互助果园");
        pkgMap.put(79,"多福果园");
        pkgMap.put(80,"爱上猜歌");
        pkgMap.put(81,"天天果园");
        pkgMap.put(83,"海底世界");
        pkgMap.put(84,"新鲜果园");
        pkgMap.put(85,"健康走");
        pkgMap.put(86,"元气猜歌");
        pkgMap.put(87,"糖果消消消");
        pkgMap.put(88,"多多果园");
        pkgMap.put(89,"元气果园");
        pkgMap.put(90,"精彩2048");
        pkgMap.put(91,"水果天空");
        pkgMap.put(92,"夫妻的秘密");
        pkgMap.put(93,"欢乐猜歌");
        pkgMap.put(94,"我的农场");
        pkgMap.put(95,"全民打怪兽");
        pkgMap.put(96,"保卫家园");
        pkgMap.put(97,"猜歌达人");
        pkgMap.put(98,"我爱答题");
        pkgMap.put(99,"我是农场主");
        pkgMap.put(100,"我爱点点消");
        pkgMap.put(101,"欢快走");
        pkgMap.put(102,"幸福鱼塘");
        pkgMap.put(103,"幸福人生");
        pkgMap.put(104,"元气走");
        pkgMap.put(105,"幸运果园");
        pkgMap.put(106,"我的存钱罐");
        pkgMap.put(107,"幸福小镇");
        pkgMap.put(108,"简单天气通");
        pkgMap.put(109,"家园保卫战");
        pkgMap.put(110,"幸福天气");
        pkgMap.put(111,"走路赚多多");
        pkgMap.put(112,"梦幻果园");
        pkgMap.put(113,"好运天气");
        pkgMap.put(114,"1号农场");
        pkgMap.put(115,"幸福农场");
        pkgMap.put(116,"快来数钱吧");
        pkgMap.put(117,"花花大战僵尸");
        pkgMap.put(118,"我的餐厅");
        pkgMap.put(119,"东东果园");
        pkgMap.put(200,"我的农田");
        pkgMap.put(201,"幸福旅行");
        pkgMap.put(202,"我的小镇");
        pkgMap.put(203,"上网宝");
        pkgMap.put(204,"我的清理大师");
        pkgMap.put(206,"阳光小镇");
        pkgMap.put(207,"幸福小城");
        pkgMap.put(208,"我的田园");
        pkgMap.put(210,"鲜花小镇");
        pkgMap.put(211,"我的聚宝盆");
        pkgMap.put(212,"好运农场");
        pkgMap.put(213,"幸福养殖场");
        pkgMap.put(214,"充电宝");
        pkgMap.put(215,"浪漫花园");
        pkgMap.put(216,"幸运农场");
        return pkgMap;
    }
}
