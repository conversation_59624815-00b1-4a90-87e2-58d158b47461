package com.shinet.core.alert.data;

import com.shinet.core.alert.adb.service.UserGrayCountService;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import com.xxl.job.core.log.XxlJobLogger;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.io.File;

/**
 * <AUTHOR>
 * @since 2023/9/1
 */
@Component
public class UserGrayCountTimer {

    @Autowired
    private UserGrayCountService userGrayCountService;

    @XxlJob("check-user-gray-count")
    public ReturnT<?> movetoUser(String param){
        userGrayCountService.checkGrayCount();
        return ReturnT.SUCCESS;
    }
}
