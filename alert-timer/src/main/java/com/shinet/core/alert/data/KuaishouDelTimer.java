package com.shinet.core.alert.data;

import com.shinet.core.alert.clickhouse.entity.ProductCpaHour;
import com.shinet.core.alert.clickhouse.mapper.ck1.ClickHouseDwdMapper;
import com.shinet.core.alert.coreservice.entity.AlertRecord;
import com.shinet.core.alert.coreservice.enums.*;
import com.shinet.core.alert.coreservice.service.AlertDelayJobService;
import com.shinet.core.alert.coreservice.service.AlertRecordService;
import com.shinet.core.alert.dingtalk.DingTailService;
import com.shinet.core.alert.dingtalk.VedioAlertService;
import com.shinet.core.alert.dsp.entity.KuaishouDrCostBean;
import com.shinet.core.alert.dsp.entity.KuaishouErBean;
import com.shinet.core.alert.dsp.mapper.KuaishouDelMapper;
import com.shinet.core.alert.util.DateUtils;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import com.xxl.job.core.log.XxlJobLogger;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.stream.Collectors;

@Component
public class KuaishouDelTimer {
    @Resource
    KuaishouDelMapper kuaishouDelMapper;

    @Resource
    private ClickHouseDwdMapper clickHouseDwdMapper;

    @Autowired
    private AlertDelayJobService alertDelayJobService;
    @Autowired
    AlertRecordService alertRecordService;

    @XxlJob("alert-kuaishouDel")
    public ReturnT<?> kuaishouDel(String param){
        int errorMaxNum = 10;
        if(StringUtils.isNotBlank(param)){
            errorMaxNum = Integer.parseInt(param);
        }
        int delNum =  kuaishouDelMapper.queryKuaishouDel();
        XxlJobLogger.log("快手删除评论异常删除数为"+delNum);
        if(delNum<errorMaxNum){
            AlertRecord alertRecord = alertRecordService.insertAlertRecord("alert-kuaishouDel","快手删除评论",
                    AlertModel.OCPC,"快手删除评论异常删除数为"+delNum, AlertStatus.INIT, DingTailService.dset, AlertType.DINGDING);
            DingTailService.sendMarkdownMsg(alertRecord);
            VedioAlertService.sendVocMsg("OCPC","快手删除评论异常删除数为"+delNum);
        }
        return ReturnT.SUCCESS;
    }

    @XxlJob("cpa-bid-num-alert")
    public ReturnT<?> checkKuaishouTimer(String param){
        Date now = new Date();
        Date yes = DateUtils.addTime(now,-1000*60*60*24);
        String logday = DateUtils.formatDateForYMD(new Date());
        if(StringUtils.isNotBlank(param)){
            logday = param;
        }
        checkKuaishouBid(logday);
        return ReturnT.SUCCESS;
    }


    @XxlJob("kuaishou-cost-suporder-alert")
    public ReturnT<?> checkKuaishouCostSup(String param){
        Date now = new Date();
        Date yes = DateUtils.addTime(now,-1000*60*60*24);
        String logday = DateUtils.formatDateForYMD(yes);
        if(StringUtils.isNotBlank(param)){
            logday = param;
        }
        checkKuaishouCostBr(logday);
        return ReturnT.SUCCESS;
    }

    public void checkKuaishouBid(String logday){
        List<KuaishouErBean> filterBeanList = kuaishouDelMapper.queryErBid(logday);
        List<KuaishouErBean> callBackRateList = filterBeanList.stream()
                .filter(r -> alertDelayJobService.canSend(AlertJobDelayModel.CPA_BID_REALTIME.getJobId()+"_"+r.getSupplementOrderId()))
                .collect(Collectors.toList());
        if (callBackRateList.size() > 0){
            long pageSize = 4;
            for (int page = 0; page <= callBackRateList.size()/pageSize; page++){
                buildAndSend(callBackRateList.stream().skip(page *pageSize).limit(pageSize).collect(Collectors.toList()));
            }
        }
    }

    public void checkKuaishouCostBr(String logday){
        List<KuaishouDrCostBean> filterBeanList = clickHouseDwdMapper.quureCostEr(logday);
        List<KuaishouDrCostBean> callBackRateList = filterBeanList.stream()
                .filter(r -> alertDelayJobService.canSend(AlertJobDelayModel.CPA_BID_REALTIME.getJobId()+"_"+r.getProductName()))
                .collect(Collectors.toList());
        if (callBackRateList.size() > 0){
            long pageSize = 4;
            for (int page = 0; page <= callBackRateList.size()/pageSize; page++){
                buildAndSendCost(callBackRateList.stream().skip(page *pageSize).limit(pageSize).collect(Collectors.toList()));
            }
        }
    }

    private static String format(Double value){
        BigDecimal bd = new BigDecimal(value);
        bd = bd.setScale(2, RoundingMode.HALF_UP);
        return bd.toString();
    }

    private void buildAndSendCost(List<KuaishouDrCostBean> callBackRateList){
        if (callBackRateList == null || callBackRateList.size() == 0){
            return;
        }
        List<String> productList = callBackRateList.stream().map(KuaishouDrCostBean::getProductName).collect(Collectors.toList());
        String alert = callBackRateList.stream()
                .map(r -> String.format("日期:%s,产品:%s(%s)异常助推订单数 **%s**,消耗 **%s**,类型:**%s**,三方转化数:**%s**,转化成本:**%s**,出价:**%s**,转化成本/出价:**%s**",
                        r.getLogday(), r.getProductName(),r.getProductGroup(),r.getOrderCount(),format(r.getConsumeAmount()),r.getTfType(),r.getConversion()
                        ,format(r.getCpc()),format(r.getCapBid()),format(r.getGap())
                ))
                .collect(Collectors.joining(";\n\n _ _ _ \n "));
        Set<String> pushData = callBackRateList.stream().flatMap(r -> DingTailService.zengzhangPushMap.get(r.getProductGroup()).stream()).collect(Collectors.toSet());
        AlertRecord alertRecord = alertRecordService.insertAlertRecord(AlertJobDelayModel.CPA_BID_ZT.getJobId(),
                AlertJobDelayModel.CPA_BID_ZT.getJobName(), AlertModel.CPA_BID,alert
                , AlertStatus.INIT, pushData, AlertType.DINGDING);
        DingTailService.sendMarkdownMsgDelay(alertRecord, AlertRoute.ZENGZHANG.value,AlertJobDelayModel.CPA_BID_ZT,productList);
    }

    private void buildAndSend(List<KuaishouErBean> callBackRateList){
        if (callBackRateList == null || callBackRateList.size() == 0){
            return;
        }
        List<String> productList = callBackRateList.stream().map(KuaishouErBean::getSupplementOrderId).collect(Collectors.toList());
        String alert = callBackRateList.stream()
                .map(r -> String.format("任务:%s(订单 **%s** 助推订单 **%s**),当前出价 **%s**",
                        r.getTaskId(),r.getOrderId(),r.getSupplementOrderId(),r.getBid()
                ))
                .collect(Collectors.joining(";\n\n"));
        Set<String> pushData = DingTailService.zengzhangPushMap.values().stream().flatMap(Collection::stream).collect(Collectors.toSet());
        AlertRecord alertRecord = alertRecordService.insertAlertRecord(AlertJobDelayModel.CPA_BID_REALTIME.getJobId(),
                AlertJobDelayModel.CPA_BID_REALTIME.getJobName(), AlertModel.CPA_BID,alert
                , AlertStatus.INIT, pushData, AlertType.DINGDING);
        DingTailService.sendMarkdownMsgDelay(alertRecord, AlertRoute.ZENGZHANG.value,AlertJobDelayModel.CPA_BID_REALTIME,productList);
    }
}
