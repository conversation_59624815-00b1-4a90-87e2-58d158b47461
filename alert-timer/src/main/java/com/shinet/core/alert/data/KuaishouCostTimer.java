package com.shinet.core.alert.data;

import com.shinet.core.alert.clickhouse.entity.KuaishouZtCostBaen;
import com.shinet.core.alert.clickhouse.entity.OrderFeeBean;
import com.shinet.core.alert.clickhouse.mapper.ck1.ClickHouseDwdMapper;
import com.shinet.core.alert.clickhouse.service.KuaishouStarCostService;
import com.shinet.core.alert.coreservice.entity.AlertRecord;
import com.shinet.core.alert.coreservice.enums.*;
import com.shinet.core.alert.coreservice.service.AlertDelayJobService;
import com.shinet.core.alert.coreservice.service.AlertRecordService;
import com.shinet.core.alert.dingtalk.DingTailService;
import com.shinet.core.alert.dsp.entity.KsdrGapBean;
import com.shinet.core.alert.dsp.entity.KsdrGapSubBean;
import com.shinet.core.alert.dsp.mapper.KuaishouDelMapper;
import com.shinet.core.alert.util.DateUtils;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2023/10/30
 */
@Component
public class KuaishouCostTimer {

    @Resource
    private ClickHouseDwdMapper clickHouseDwdMapper;

    @Autowired
    private AlertDelayJobService alertDelayJobService;
    @Autowired
    AlertRecordService alertRecordService;


    @XxlJob("check-ksdrzt-cost-alert")
    public ReturnT<?> checkChannelCostTask(String param){
        checkZtCost();
        return ReturnT.SUCCESS;
    }


    @XxlJob("check-ksdrzt-realtime-cost-alert")
    public ReturnT<?> checkChannelRealtimeCostTask(String param){
        queryKuaishouZtRealtimeGap();
        return ReturnT.SUCCESS;
    }

    @XxlJob("check-ksdrzt-realtime-fee")
    public ReturnT<?> checkKsdrCostFee(String param){
        queryKsdrFee();
        return ReturnT.SUCCESS;
    }

    private void queryKsdrFee(){
        List<OrderFeeBean> orderFeeBeans = clickHouseDwdMapper.queryOrderFeeLimit();
        if (orderFeeBeans.size() > 0){
            String alert = orderFeeBeans.stream()
                    .map(r -> String.format("order:%s \n\n ks_zt_realtime 服务费累积:**%s**\n\n",
                            r.getOrderId(), format(r.getFee())))
                    .collect(Collectors.joining(";\n\n _ _ _ \n "));
            AlertRecord alertRecord = alertRecordService.insertAlertRecord(AlertJobDelayModel.KSDR_COST_FEE.getJobId(),
                    AlertJobDelayModel.KSDR_COST_FEE.getJobName(), AlertModel.KSDR_CHECK_FEE,alert
                    , AlertStatus.INIT, DingTailService.dsetData, AlertType.DINGDING);
            DingTailService.sendMarkdownMsg(alertRecord, AlertRoute.SJXT.value);
        }
    }

    private static String format(Double value){
        BigDecimal bd = new BigDecimal(value);
        bd = bd.setScale(2, RoundingMode.HALF_UP);
        return bd.toString();
    }

    public void queryKuaishouZtRealtimeGap(){
        List<KuaishouZtCostBaen> kuaishouZtCostBaens = clickHouseDwdMapper.queryKuaishouZtRealtimeGap();
        if (kuaishouZtCostBaens.size() > 0){
            String alert = kuaishouZtCostBaens.stream()
                    .map(r -> String.format("日期:%s \n\n ks_zt_realtime 消耗:**%s**\n\n ks_zt_realtime_hour消耗:**%s** \n\n gap:**%s**",
                            r.getLogday(), format(r.getCostDate()),format(r.getCostHour()),format(r.getGap())))
                    .collect(Collectors.joining(";\n\n _ _ _ \n "));
            AlertRecord alertRecord = alertRecordService.insertAlertRecord(AlertJobDelayModel.KSDR_COST.getJobId(),
                    AlertJobDelayModel.KSDR_COST.getJobName(), AlertModel.KSDR_CHECK,alert
                    , AlertStatus.INIT, DingTailService.dsetData, AlertType.DINGDING);
            DingTailService.sendMarkdownMsg(alertRecord, AlertRoute.SJXT.value);
        }
    }

    private void checkZtCost(){
        List<KuaishouZtCostBaen> ksdrGapSubBeanList =  clickHouseDwdMapper.queryKuaishouZtLogdayGap();
        if (ksdrGapSubBeanList.size() > 0){
            String alert = ksdrGapSubBeanList.stream()
                    .map(r -> String.format("日期:%s \n\n ks_zt_closing 消耗:**%s**\n\n ks_zt_realtime_date 消耗:**%s**\n\n ks_zt_realtime_hour消耗:**%s** \n\n gap1:**%s** gap2:**%s**",
                            r.getLogday(),format(r.getCostDate()),format(r.getCostReal()),format(r.getCostHour()),format(r.getGap()),format(r.getGap1())))
                    .collect(Collectors.joining(";\n\n _ _ _ \n "));
            AlertRecord alertRecord = alertRecordService.insertAlertRecord(AlertJobDelayModel.KSDR_COST.getJobId(),
                    AlertJobDelayModel.KSDR_COST.getJobName(), AlertModel.KSDR_CHECK,alert
                    , AlertStatus.INIT, DingTailService.dsetData, AlertType.DINGDING);
            DingTailService.sendMarkdownMsg(alertRecord, AlertRoute.SJXT.value);
        }

    }

    @Autowired
    KuaishouStarCostService kuaishouStarCostService;

    /**
     * 快手助推+视频消耗(星任务-视频消耗)预警
     * @param param
     * @return
     */
    @XxlJob("alter-kuaishou-star-video-cost")
    public ReturnT<?> alterKuaishouStarVideoCost(String param){
        kuaishouStarCostService.starCostAlert();
        return ReturnT.SUCCESS;
    }

    /**
     * 达人在新任务中创建订单预警
     * @param param
     * @return
     */
    @XxlJob("alter-daren-new-task-order-cost")
    public ReturnT<?> alterDarenNewTaskOrderCost(String param){
        kuaishouStarCostService.starTaskTf();
        return ReturnT.SUCCESS;
    }
}
