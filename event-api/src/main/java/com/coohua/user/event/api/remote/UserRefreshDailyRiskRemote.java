package com.coohua.user.event.api.remote;

import com.coohua.user.event.api.dto.UserDailyRiskResponse;
import com.coohua.user.event.api.remote.rpc.UserRefreshDailyRiskRpc;
import com.coohua.user.event.biz.ap.vo.UserRiskLimitVo;
import com.coohua.user.event.biz.ap.vo.UserRiskVo;
import com.coohua.user.event.biz.service.HBaseUserAdViewService;
import com.coohua.user.event.biz.service.UserRiskService;
import com.weibo.api.motan.config.springsupport.annotation.MotanService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @since 2021/9/3
 */
@Service
@MotanService(basicService = "user-eventBasicServiceConfigBean")
public class UserRefreshDailyRiskRemote implements UserRefreshDailyRiskRpc {

    @Autowired
    private UserRiskService userRiskService;
    @Autowired
    private HBaseUserAdViewService hBaseUserAdViewService;

    @Override
    public UserDailyRiskResponse queryUserRiskLevel(Integer appId, Long userId) {
        UserRiskVo result = userRiskService.queryUserDailyRiskInfo(appId,userId);
        UserRiskLimitVo vo = userRiskService.queryUserLimit(appId,userId);
        UserDailyRiskResponse response = new UserDailyRiskResponse();
        response.setAppId(result.getAppId());
        response.setUserId(result.getUserId());
        response.setLevel(result.getLevel());
        response.setDesc(result.getDesc());

        response.setRestrictUser(vo.getRestrictUser());
        if (vo.getRestrictUser()){
            response.setWithdrawRate(vo.getWithdrawRate());
            response.setRewardRate(vo.getRewardRate());
            response.setVideoLimit(vo.getVideoLimit());
        }
        return response;
    }

    @Override
    public UserDailyRiskResponse queryUserRiskLevel(Integer appId, Long userId, Boolean hsScore) {
        UserDailyRiskResponse response =  queryUserRiskLevel(appId, userId);
        if (hsScore){
            response.setHsScore(hBaseUserAdViewService.isHs300ExUserScore(appId,userId));
        }
        return response;
    }

    @Override
    public UserDailyRiskResponse refreshUserDailyRisk(Integer appId, Long userId, Integer userLevel, Boolean check) {
        UserRiskVo result =  userRiskService.refreshUserDailyRisk(appId, userId, userLevel,check);
        UserDailyRiskResponse response = new UserDailyRiskResponse();
        response.setAppId(result.getAppId());
        response.setUserId(result.getUserId());
        response.setLevel(result.getLevel());
        response.setDesc(result.getDesc());
        response.setRefreshRisk(result.getRefreshRisk());
        return response;
    }

}
