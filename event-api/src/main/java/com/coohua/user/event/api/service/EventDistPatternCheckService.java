package com.coohua.user.event.api.service;

import com.alibaba.fastjson.JSONObject;
import com.coohua.user.event.api.dto.CheckResult;
import com.coohua.user.event.biz.click.entity.ProductEntity;
import com.coohua.user.event.biz.service.bean.UserActive;
import com.coohua.user.event.biz.service.bean.UserEventInfoBean;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

/**
 * 埋点数据模式检查服务
 * 专门处理eventDistUserInfo和adDataEventDistUserInfo的模式检查
 * 
 * <AUTHOR>
 * @since 2025-01-23
 */
@Slf4j
@Service
public class EventDistPatternCheckService {

    /**
     * 校验近两日注册安卓自然量用户app_version与oaid/imei/channel/sdk_version的包含关系
     *
     * @param appId 应用ID
     * @param userId 用户ID
     * @param userActive 用户激活信息
     * @param productEntity 产品实体
     * @param eventDistUserInfo 第一条埋点信息JSON字符串
     * @param adDataEventDistUserInfo AdData埋点信息JSON字符串
     * @param checkResult 检查结果
     * @return true表示检测到风险需要拦截，false表示通过检查
     */
    public boolean checkAppVersionOaidPattern(Long appId, Long userId, UserActive userActive, 
            ProductEntity productEntity, String eventDistUserInfo, String adDataEventDistUserInfo, 
            CheckResult checkResult) {
        
        if (!userActive.isPastOneDaysUser()) {
            return false;
        }

        try {
            // 检查第一条埋点信息
            if (StringUtils.isNotBlank(eventDistUserInfo)) {
                if (checkEventDistForPattern(eventDistUserInfo, appId, userId, "第一条埋点", checkResult)) {
                    return true;
                }
            }

            // 检查AdData埋点信息
            if (StringUtils.isNotBlank(adDataEventDistUserInfo)) {
                if (checkEventDistForPattern(adDataEventDistUserInfo, appId, userId, "AdData埋点", checkResult)) {
                    return true;
                }
            }

        } catch (Exception e) {
            log.warn("解析埋点数据异常，用户 {} {}: {}", appId, userId, e.getMessage());
        }

        return false;
    }

    /**
     * 检查单个埋点数据中的字段包含关系模式
     *
     * @param eventDistInfo 埋点数据JSON字符串
     * @param appId 应用ID
     * @param userId 用户ID
     * @param dataSource 数据源描述
     * @param checkResult 检查结果
     * @return true表示检测到风险
     */
    private boolean checkEventDistForPattern(String eventDistInfo, Long appId, Long userId,
            String dataSource, CheckResult checkResult) {
        try {
            // 使用UserEventInfoBean解析埋点数据
            UserEventInfoBean eventInfoBean = JSONObject.parseObject(eventDistInfo, UserEventInfoBean.class);

            // 获取需要检查的字段
            String appVersion = eventInfoBean.getAppVersion();
            String oaid = eventInfoBean.getOaid();
            String imei = eventInfoBean.getImei();
            String channel = eventInfoBean.getChannel();
            String sdkVersion = eventInfoBean.getSdkVersion();

            if (checkStringContainment(appVersion, oaid)) {
                log.info("用户 {} {} 校验失败:规则41-拒绝提现 {}中app_version({})和oaid({})互为子字符串",
                    appId, userId, dataSource, appVersion, oaid);
                checkResult.setDirectRefund(false);
                checkResult.setReason("校验失败:规则41-拒绝提现");
                return true;
            }

            if (checkStringContainment(appVersion, imei)) {
                log.info("用户 {} {} 校验失败:规则41-拒绝提现 {}中app_version({})和imei({})互为子字符串",
                    appId, userId, dataSource, appVersion, imei);
                checkResult.setDirectRefund(false);
                checkResult.setReason("校验失败:规则41-拒绝提现");
                return true;
            }

            if (checkStringContainment(oaid, channel)) {
                log.info("用户 {} {} 校验失败:规则41-拒绝提现 {}中oaid({})和channel({})互为子字符串",
                    appId, userId, dataSource, oaid, channel);
                checkResult.setDirectRefund(false);
                checkResult.setReason("校验失败:规则41-拒绝提现");
                return true;
            }

            if (checkStringContainment(oaid, sdkVersion)) {
                log.info("用户 {} {} 校验失败:规则41-拒绝提现 {}中oaid({})和sdk_version({})互为子字符串",
                    appId, userId, dataSource, oaid, sdkVersion);
                checkResult.setDirectRefund(false);
                checkResult.setReason("校验失败:规则41-拒绝提现");
                return true;
            }

        } catch (Exception e) {
            log.warn("解析{}数据异常，用户 {} {}: {}", dataSource, appId, userId, e.getMessage());
        }

        return false;
    }

    /**
     * 检查两个字符串是否互为子字符串（A包含B 或 B包含A）
     *
     * @param str1 字符串1
     * @param str2 字符串2
     * @return true表示存在包含关系
     */
    private boolean checkStringContainment(String str1, String str2) {
        // 检查空值
        if (StringUtils.isBlank(str1) || StringUtils.isBlank(str2)) {
            return false;
        }

        if (StringUtils.equals(str1, str2)) {
            return true;
        }

        // 检查互为子字符串关系
        return str1.contains(str2) || str2.contains(str1);
    }
}
