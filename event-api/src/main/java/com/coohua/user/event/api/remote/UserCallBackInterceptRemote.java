package com.coohua.user.event.api.remote;

import com.alibaba.fastjson.JSONObject;
import com.coohua.user.event.api.dto.UserCallBackInterceptReq;
import com.coohua.user.event.api.remote.rpc.UserCallBackInterceptRpc;
import com.coohua.user.event.biz.dc.service.UserCallBackInterceptService;
import com.coohua.user.event.biz.util.RedisKeyConstants;
import com.weibo.api.motan.config.springsupport.annotation.MotanService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import top.zrbcool.pepper.boot.jediscluster.JedisClusterClient;
import top.zrbcool.pepper.boot.jediscluster.JedisClusterClientRefer;

import javax.annotation.Resource;


@Slf4j
@Service
@MotanService(basicService = "user-eventBasicServiceConfigBean")
public class UserCallBackInterceptRemote implements UserCallBackInterceptRpc {

    @JedisClusterClientRefer(namespace = "user-event")
    private JedisClusterClient jedisClusterClient;
    @Resource
    private UserCallBackInterceptService userCallBackInterceptService;

    @Override
    public boolean checkUserCallBackIntercept(UserCallBackInterceptReq req) {
        if (StringUtils.isBlank(req.getProduct())) {
            log.error("UserCallBackIntercept product is null :{}", req);
            return false;
        }
        if (StringUtils.isBlank(req.getOs())) {
            log.error("UserCallBackIntercept os is null :{}", req);
            return false;
        }
        if (StringUtils.isBlank(req.getUserId())) {
            log.error("UserCallBackIntercept userId is null :{}", req);
            return false;
        }
        if ("ios".equalsIgnoreCase(req.getOs())) {
            return false;
        }

        Boolean exists = jedisClusterClient.exists(RedisKeyConstants.getUserCallBackInterceptNonBaiduKey(req.getOs(), req.getProduct(), req.getUserId()));

        if (exists) {
            userCallBackInterceptService.saveSync(JSONObject.toJSONString(req));
        }

        // true代表该用户不需要拦截，可发送回调，反之回调需要拦截
        return !exists;
    }
}
