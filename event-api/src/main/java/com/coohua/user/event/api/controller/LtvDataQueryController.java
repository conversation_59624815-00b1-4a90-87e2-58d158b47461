package com.coohua.user.event.api.controller;

import com.coohua.user.event.biz.core.dto.rsp.ReturnResult;
import com.coohua.user.event.biz.dc.dto.LtvQueryRequest;
import com.coohua.user.event.biz.dc.dto.QueryRosRequest;
import com.coohua.user.event.biz.dc.service.LtvQueryService;
import org.apache.ibatis.annotations.Param;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;

/**
 * <AUTHOR>
 * @since 2021/7/7
 */
@RestController
@RequestMapping("ltv")
public class LtvDataQueryController {

    @Autowired
    private LtvQueryService ltvQueryService;

    @PostMapping(value = "queryRos",produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    public ReturnResult queryLtvRos(@RequestBody LtvQueryRequest request, HttpServletRequest servletRequest){
        ReturnResult returnResult = new ReturnResult();
        returnResult.setData(ltvQueryService.queryLtvInfo(request));
        return returnResult;
    }

    @PostMapping(value = "queryBaiduRos",produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    public ReturnResult queryBaiduRos(@RequestBody QueryRosRequest request, HttpServletRequest servletRequest){
        ReturnResult returnResult = new ReturnResult();
        returnResult.setData(ltvQueryService.queryBaiduFeedRos(request));
        return returnResult;
    }

    @PostMapping(value = "queryToutiaoRos",produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    public ReturnResult queryToutiaoRos(@RequestBody QueryRosRequest request, HttpServletRequest servletRequest){
        ReturnResult returnResult = new ReturnResult();
        returnResult.setData(ltvQueryService.queryToutiaoRos(request));
        return returnResult;
    }

    @GetMapping(value = "querySubLtv",produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    public ReturnResult queryLtvSub(@Param("logday") String logday, HttpServletRequest httpServletRequest){
        ReturnResult returnResult = new ReturnResult();
        returnResult.setData(ltvQueryService.queryLtvAllQueryResponse(logday));
        return returnResult;
    }
}
