package com.coohua.user.event.api.remote;

import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.coohua.user.event.api.dto.CheckResult;
import com.coohua.user.event.api.dto.QueryRequest;
import com.coohua.user.event.api.dto.QueryResponse;
import com.coohua.user.event.api.remote.rpc.UserAdEcpmRpc;
import com.coohua.user.event.api.service.EventDistPatternCheckService;
import com.coohua.user.event.api.service.NewNatureLimitService;
import com.coohua.user.event.biz.click.entity.ProductEntity;
import com.coohua.user.event.biz.click.mapper.ClickHouseOdsMapper;
import com.coohua.user.event.biz.core.entity.WithdrawNotSend;
import com.coohua.user.event.biz.core.service.WithdrawNotSendService;
import com.coohua.user.event.biz.dc.mapper.UserGrayIpMapper;
import com.coohua.user.event.biz.enums.GrayType;
import com.coohua.user.event.biz.service.*;
import com.coohua.user.event.biz.service.bean.UserActive;
import com.coohua.user.event.biz.service.bean.UserEventCountHBaseBean;
import com.coohua.user.event.biz.service.bean.UserEventInfoBean;
import com.coohua.user.event.biz.service.rsp.BatchQueryResponse;
import com.coohua.user.event.biz.service.rsp.CheckInnerRsp;
import com.coohua.user.event.biz.util.*;
import com.ctrip.framework.apollo.spring.annotation.ApolloJsonValue;
import com.weibo.api.motan.config.springsupport.annotation.MotanService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import top.zrbcool.pepper.boot.jediscluster.JedisClusterClient;
import top.zrbcool.pepper.boot.jediscluster.JedisClusterClientRefer;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.time.LocalTime;
import java.util.Objects;
import java.util.*;

/**
 * <AUTHOR>
 * @since 2021/6/29
 */
@Slf4j
@Service
@MotanService(basicService = "user-eventBasicServiceConfigBean")
public class UserAdEcpmRemote implements UserAdEcpmRpc {

    @Autowired
    private HBaseUserAdViewService hBaseUserAdViewService;
    @Autowired
    private HBaseAdInfoService hBaseAdInfoService;
    @Autowired
    private HBaseUserEventCountService hBaseUserEventCountService;

    @Autowired
    private UserAdExposureCheckService userAdExposureCheckService;
    @Autowired
    private UserDeviceCheckService userDeviceCheckService;
    @Autowired
    private UserGrayService userGrayService;
    @Autowired
    private UserInfoQueryService userInfoQueryService;
    @Autowired
    private TFUserService tfUserService;
    @Autowired
    private NewNatureLimitService newNatureLimitService;

    @ApolloJsonValue("${join.ad.filter.app:[]}")
    private List<Long> joinAdFilterCheckApp;

    @ApolloJsonValue("${join.hs.check.app:[]}")
    private List<Long> joinHuoShanApp;

    @ApolloJsonValue("${join.user.event.check.app:[]}")
    private List<Long> joinUserEventCountCheckApp;

    @ApolloJsonValue(("${dont.use.white.list:[]}"))
    private List<Long> dontUseWhiteList;

    @ApolloJsonValue(("${dont.use.black.pkgs:[\"com.xxdd.xwj\"]}"))
    private Set<String> blackPkgs;

    @JedisClusterClientRefer(namespace = "bp-user")
    private JedisClusterClient jedisClusterClient;

    @JedisClusterClientRefer(namespace = "user-event")
    private JedisClusterClient userEventJedisClusterClient;

    @Value("${limit.check.group7.amount:500}")
    private Integer checkLimitAmountGroup7;

    @ApolloJsonValue("${zt.check.app.list:[1215,1231]}")
    private List<Long> checkZtAppList;
    //自然量用户提现检测的appId
    @ApolloJsonValue("${zr.check.app.list:[1133,1142]}")
    private Set<Long> checkZrAppList;
    @ApolloJsonValue("${zr.team7.noCheck.app.list:[446,629,713,822,841,902,955,981,985,998,1064,1080,1095,1128,1147,1156,1157,1159,1187,1201,1225,1232,1251,1255,1259,1276]}")
    private Set<Long> noCheckTeam7AppList;

    @Value("${enable.continuous.withdraw.intercept:false}")
    private Boolean enableContinuousWithdraw;
    @Value("${enable.check.uinc.abnormal.nbd:false}")
    private Boolean enableCheckUincAbnormalNBd;
    @Value("${income.abnormal.nbd:100}")
    private Integer incAbnormalNonBdAmount;
    @ApolloJsonValue("${check.user.model.app.list:[]}")
    private Set<String> checkUserModelAppList;
    @ApolloJsonValue("${black.model.list:[]}")
    private Set<String> blackModelList;
    @ApolloJsonValue("${store.offloading.product.list:[]}")
    private Set<String> storeOffloadingProductList;
    @ApolloJsonValue("${group10.ziran.product.list:[]}")
    private Set<String> group10ZiranProductList;
    @ApolloJsonValue("${check.multi.user.group.count:{}}")
    private Map<String, Integer> checkMultiUserGroupCountMap;
    @ApolloJsonValue("${withdraw.product.map:{}}")
    private Map<String, Set<String>> withdrawProductMap;
    @ApolloJsonValue("${check.black.caid.list:[]}")
    private Set<String> checkBlackCaidList;
    @ApolloJsonValue("${check.ziran.amount.map:{}}")
    private Map<String, Map<String, String>> checkZiranAmountMap;
    @ApolloJsonValue("${check.ziran.amount.white.product.list:[]}")
    private Set<String> checkZiranAmountWhiteProductList;
    @ApolloJsonValue("${check.black.product.channel.map:{}}")
    private Map<String, Set<String>> checkBlackProductChannelMap;
    @ApolloJsonValue("${check.product.white.channel.list:[]}")
    private Set<String> checkProductWhiteChannelList;
    @Value("${enable.check.user.channel:false}")
    private Boolean enableCheckUserChannel;
    @Value("${enable.check.dist.model:false}")
    private Boolean enableCheckDistModel;
    @ApolloJsonValue("${check.suspicious.device.prefix.list:[\"e450d\"]}")
    private List<String> suspiciousDevicePrefixList;
    @ApolloJsonValue("${check.first.event.map:{}}")
    private Map<String, Map<String, List<String>>> checkFirstEventMap;

    @Autowired
    private WithdrawNotSendService withdrawNotSendService;
    @Autowired
    private ManufacturerModelCheckService manufacturerModelCheckService;
    @Autowired
    private WithdrawDelayUserService withdrawDelayUserService;
    @Autowired
    private ClickHouseOdsMapper clickHouseOdsMapper;
    @Resource
    private UserGrayIpMapper userGrayIpMapper;
    @Resource
    private EventDistPatternCheckService eventDistPatternCheckService;

//    @PostConstruct
    public void init(){
        long t = 100027303;
        for(;t <= 157000000; t += 1000000){
            List<Map<String, Object>> res = clickHouseOdsMapper.initSelect(String.valueOf(t), String.valueOf(t + 1000000));
            System.out.println(" res : " + res.size() + " t : " + t + " tend: " + (t + 1000000));
            userAdExposureCheckService.initRedis(res, "20240708");
        }
        for(;t <= 196000000; t += 100000){
            List<Map<String, Object>> res = clickHouseOdsMapper.initSelect(String.valueOf(t), String.valueOf(t + 100000));
            System.out.println(" res : " + res.size() + " t : " + t + " tend: " + (t + 100000));
            userAdExposureCheckService.initRedis(res, "20240708");
        }

        for(;t <= 999986498; t += 1000000){
            List<Map<String, Object>> res = clickHouseOdsMapper.initSelect(String.valueOf(t), String.valueOf(t + 1000000));
            System.out.println(" res : " + res.size() + " t : " + t + " tend: " + (t + 1000000));
            userAdExposureCheckService.initRedis(res, "20240708");
        }
    }

    @Override
    public String queryUserECPMBatch(QueryRequest request) {
        return JSON.toJSONString(hBaseUserAdViewService.batchQuery(request.getUserIdList(),request.getOs(),request.getAppId()));
    }

    @Override
    public Map<Long, QueryResponse> queryEcpmBatch(QueryRequest request) {
        Map<Long,BatchQueryResponse> batchQueryResponseMap = hBaseUserAdViewService.batchQuery(request.getUserIdList(),request.getOs(),request.getAppId());
        Map<Long,QueryResponse> responseMap = new HashMap<>();
        batchQueryResponseMap.forEach((k,v)->{
            responseMap.put(k,new QueryResponse(){{
                setEcpmSum(v.getEcpmSum());
                setUserId(v.getUserId());
                setVideoExposureCount(v.getVideoExposureCount());
            }});
        });

        return responseMap;
    }

    public boolean isWhiteUserId(String userId){
        try {
            String key = String.format("white:user:global:%s",userId);
            String result = jedisClusterClient.get(key);
            if (StringUtils.isNotEmpty(result)){
                return Boolean.parseBoolean(result);
            }
            return false;
        }catch (Exception e){
            log.error("==> 查询User白名单Redis 异常:{}",userId);
            return false;
        }
    }

    @Override
    public Boolean checkUserAd(Long appId, Long userId,Integer amount) {
        // 下线的Check
        return checkOrderBp(appId,userId,"",amount).getPass();
    }

    @Override
    public CheckResult checkOrderBp(Long appId, Long userId, String orderNo, Integer amount) {
        return checkOrderBp(appId, userId, orderNo, amount,false,null);
    }

    @Override
    public CheckResult checkOrderBp(Long appId, Long userId, String orderNo, Integer amount, Boolean delay, String delayReason) {
        CheckResult checkResult = new CheckResult();
        checkResult.setPass(true);
        checkResult.setDirectRefund(false);
        try {
            Long convert = AppDrawConf.appIdConvertMap.get(appId);
            if (convert != null){
                appId = convert;
            }
            log.info("===> 用户 {} {} 提现审核 中台策略进行核查",appId,userId);
            boolean flag = true;
            boolean teamFlag = true;
            boolean userBlackFlag = false;
            UserActive userActive = null;
            // 检测用户曝光与三方Reward 差异
            if ((userDeviceCheckService.isWhiteUser(userId) || isWhiteUserId(userId.toString())) && !dontUseWhiteList.contains(userId)){
                log.info("用户 {} {} 是白名单用户 直接通过",appId,userId);
            }else {
                if (delay){
                    log.info("用户 {} {} {} {} 被Delay发放 直接拒绝",appId,userId,orderNo,delayReason);
                //  项目组delay后中台继续判断
                //    flag = false;
                    teamFlag = false;
                    checkResult.setDirectRefund(false);
                    checkResult.setReason(delayReason);
                }
                // check提现曝光
                if (userAdExposureCheckService.isNtfExposureMoreThan500(appId,userId)){
                    log.info("非投放用户 {} {} 当日曝光超过500 拒绝提现",appId,userId);
                    flag = false;
                    checkResult.setDirectRefund(true);
                    checkResult.setReason("当日曝光超过500-拒绝提现");
                }
                // check 提现用户累积的设备是否已经处于黑名单 防止篡改
                if (flag){
                    try {
                        CheckInnerRsp checkInnerRsp = userDeviceCheckService.isAlreadyGrayDeviceUser(appId, userId);
                        if (checkInnerRsp.getReject()) {
                            log.info("用户 {} {} 关联设备处于黑名单 拒绝提现", appId, userId);
                            flag = false;
                            checkResult.setDirectRefund(true);
                            checkResult.setReason(checkInnerRsp.getReason());
                        }
                    } catch (Exception e) {
                        log.error("关联设备查询异常 {} {}", userId, e);
                    }
                }

                Integer totalAmount = userDeviceCheckService.getUserWithdraw(appId, userId, amount);
                if(flag) {
                    //这里边都是依赖 USER_ACTIVE 的所有判断
                    ProductEntity productEntity = AppConfig.appIdMap.get(appId);

                    if (productEntity == null) {
                        log.error("AppId {} {} 未查询到产品配置", appId, userId);
                    } else {
                        //        是否使用设备归因
                        if (tfUserService.isUseDeviceGui(productEntity.getProduct())) {
                            try {
                                userActive = tfUserService.queryUserDeviceGui(productEntity.getProduct(), String.valueOf(userId), appId);
                                if (tfUserService.isDeviceGuiLogEnabled) {
                                    log.info("UserAdEcpmRemote设备归因 {} {} {}", productEntity.getProduct(), userId, userActive);
                                }
                            } catch (Exception e) {
                                log.error("UserAdEcpmRemote查询用户 {} {} 设备归因异常", appId, userId, e);
                            }
                        } else {
                            userActive = tfUserService.queryUserActiveIfNoFundQueryRpc(productEntity.getProduct(), userId.toString(), appId);
                        }
                        if (userActive == null) {
                            log.info("未查询到 USER_ACTIVE {} {} 审核暂时不通过", appId, userId);
                            return checkResult;
//                            flag = false;
//                            checkResult.setDirectRefund(false);
//                            checkResult.setReason("未查询到 USER_ACTIVE-拒绝提现");
                        }
                    }


                    boolean isTodayUser = false;
                    if (userActive != null) {
                        isTodayUser = userActive.isTodayUser();
                    }

                    if (flag && "ios".equals(userActive.getOs())) {
                        recordMultiUser(userActive, checkResult, productEntity, appId, userId);

                        if (checkCaid(userActive, checkResult, appId, userId)) {
                            flag = false;
                            userBlackFlag = true;
                        }
                    }
                    String eventDistUserInfo = userEventJedisClusterClient.get(RedisKeyConstants.getEventDistInfoKey(productEntity.getProduct(), userId));
                    String adDataEventDistUserInfo = userEventJedisClusterClient.get(RedisKeyConstants.getAdDataEventDistInfoKey(productEntity.getProduct(), userId));

                    if (flag && userActive.isAndroid() && isTodayUser
                            && checkFirstEventDist(appId, userId, userActive, productEntity, eventDistUserInfo, adDataEventDistUserInfo, checkResult)) {
                        flag = false;
                    }

                    if (flag && userActive.isAndroid() && isTodayUser
                            && checkSuspiciousDevicePrefix(appId, userId, userActive, productEntity, eventDistUserInfo, adDataEventDistUserInfo, checkResult)) {
                        flag = false;
                    }

                    if (flag && userActive.isAndroid()
                            && eventDistPatternCheckService.checkAppVersionOaidPattern(appId, userId, userActive, productEntity, eventDistUserInfo, adDataEventDistUserInfo, checkResult)) {
                        flag = false;
                    }

                    if (flag && checkImeiConsistency(appId, userId, userActive, productEntity, eventDistUserInfo, adDataEventDistUserInfo, checkResult)) {
                        flag = false;
                    }
                    
                    if (flag && userActive.isAndroid() && isTodayUser
                            && checkAdDataDistManufacturer(appId, userId, userActive, productEntity, eventDistUserInfo, adDataEventDistUserInfo, checkResult)) {
                        flag = false;
                    }

                    if (flag && userActive.isAndroid() && isTodayUser
                            && checkRegisterAndDistInfo(appId, userId, userActive, productEntity, eventDistUserInfo, adDataEventDistUserInfo, checkResult)) {
                        flag = false;
                    }

                    if (flag && userActive.isAndroid() && isTodayUser
                            && checkEventDistUserInfo(appId, userId, userActive, productEntity, eventDistUserInfo, adDataEventDistUserInfo, checkResult)) {
                        flag = false;
                    }

                    if (flag && userActive.isAndroid() && isTodayUser
                            && checkOaidIpConsistency(appId, userId, userActive, productEntity, eventDistUserInfo, adDataEventDistUserInfo, checkResult)) {
                        flag = false;
                    }

                    if (flag && userActive.isAndroid() && checkDistManufacturer(appId, userId, userActive, productEntity, eventDistUserInfo, checkResult)) {
                        flag = false;
                    }

                    if (flag && "ios".equals(userActive.getOs())) {
                        if (checkBlackCaid(userActive, checkResult, appId, userId)) flag = false;
                    }

                    if (flag && "ios".equals(userActive.getOs()) && StringUtils.isNotBlank(userActive.getCaid())) {
                        if (checkMultiUser(userActive, checkResult, productEntity, appId, userId)) flag = false;
                    }

                    if (flag && "ios".equals(userActive.getOs()) && checkIdfa(userActive, checkResult, appId, userId)) {
                        flag = false;
                        userBlackFlag = true;
                    }

                    if (flag && checkOsBlackProduct(userActive.getOs(), productEntity.getProduct())) {
                        log.info("用户 {} {} 校验失败:规则20-拒绝提现", appId, userId);
                        flag = false;
                        checkResult.setDirectRefund(false);
                        checkResult.setReason("校验失败:规则20-拒绝提现");
                    }

                    if (flag && "项目十组".equals(productEntity.getProductGroup())) {
                        if (handler10Group(appId, userId, amount, totalAmount, productEntity, userActive, checkResult)) flag = false;
                    }

                    if (flag && checkGroupSgmEx(appId, userId, amount, totalAmount, productEntity, userActive, checkResult)) {
                        flag = false;
                    }

                    if (flag && checkBlackIp(userId, appId, productEntity, userActive, checkResult)) {
                        flag = false;
                    }

                    if (flag && checkZiranAmount(userId, appId, productEntity, userActive, amount, checkResult)) {
                        flag = false;
                    }

                    if (flag && checkExIp(userActive, appId, userId, productEntity, checkResult)) {
                        flag = false;
                    }

                    if (flag && checkOs(userActive, appId, userId, productEntity, checkResult)) {
                        flag = false;
                        userBlackFlag = true;
                    }

                    if (flag && checkBlackProductChannel(userActive, checkResult, appId, userId, productEntity)) {
                        flag = false;
                        userBlackFlag = true;
                    }

                    if (flag && checkUserChannel(userActive, checkResult, appId, userId, productEntity, amount, totalAmount)) {
                        flag = false;
                    }

                    if (flag && "android".equalsIgnoreCase(userActive.getOs()) && checkXiaoMiGdtGap(userActive, checkResult, appId, userId, productEntity, totalAmount)) {
                        flag = false;
                    }

                    // 下线
//                    if (flag && checkGroupSgmGdtEx(userActive, checkResult, appId, userId, productEntity, totalAmount)) {
//                        flag = false;
//                    }

//                    if (flag && checkProductChannel(userActive, checkResult, appId, userId, productEntity)) {
//                        flag = false;
//                        userBlackFlag = true;
//                    }

                    if (flag && "ios".equals(userActive.getOs())) {
                        if (userActive.getCreateTime().after(DateUtil.stringToDate("2025-04-15 00:00:00", DateUtil.COMMON_TIME_FORMAT))) {
                            if (storeOffloadingProductList.contains(productEntity.getProduct())) {
                                log.info("用户 {} {} 校验失败:规则90-拒绝提现", appId, userId);
                                checkResult.setDirectRefund(false);
                                checkResult.setReason("校验失败:规则90-拒绝提现");
                                flag = false;
                                userBlackFlag = true;
                            }
                        }
                    }

                    List<String> channelWhiteProductlist = Arrays.asList("hhfg","nxdxz","qzysdd");
                    if (flag && "ios".equals(userActive.getOs())
                            && StringUtils.isNotBlank(userActive.getChannel()) && !"AppStore".equals(userActive.getChannel())) {
                        if (!channelWhiteProductlist.contains(productEntity.getProduct())) {
                            log.info("用户 {} {} 校验失败:规则95-拒绝提现", appId, userId);
                            flag = false;
                            checkResult.setDirectRefund(false);
                            checkResult.setReason("校验失败:规则95-拒绝提现");
                            userBlackFlag = true;
                        }
                    }

                    String model = null;
                    if (userActive != null) {
                        model = userActive.getModel();
                    }
                    // 校验model是否为纯数字
                    if (flag && checkModel(model, productEntity.getProduct())) {
                        log.info("用户 {} {} 校验失败:规则301-拒绝提现", appId, userId);
                        flag = false;
                        checkResult.setDirectRefund(false);
                        checkResult.setReason("校验失败:规则301-拒绝提现");
                        userBlackFlag = true;
                    }
                    String oaid = null;
                    if (userActive != null) {
                        oaid = userActive.getOaid();
                    }
                    if (flag && "android".equalsIgnoreCase(userActive.getOs())
                            && StringUtils.isNotBlank(model) && StringUtils.isNotBlank(oaid)) {
                        // 校验oaid和model是否一致
                        if (StringUtils.equals(oaid, model)) {
                            log.info("用户 {} {} 校验失败:规则78-拒绝提现", appId, userId);
                            flag = false;
                            checkResult.setDirectRefund(false);
                            checkResult.setReason("校验失败:规则78-拒绝提现");
                            userBlackFlag = true;
                        }
                    }

                    String activeProduct = null;
                    if (userActive != null) {
                        activeProduct = userActive.getProduct();
                    }
                    if (flag && StringUtils.isNotBlank(activeProduct)) {
                        if (!StringUtils.equals(productEntity.getProduct(), activeProduct)) {
                            log.info("用户 {} {} 校验失败:规则80-拒绝提现", appId, userId);
                            flag = false;
                            checkResult.setDirectRefund(false);
                            checkResult.setReason("校验失败:规则80-拒绝提现");
                            userBlackFlag = true;
                        }
                    }

                    if (flag && checkBlackModel(model, productEntity)) {
                        log.info("用户 {} {} 校验失败:规则81-拒绝提现", appId, userId);
                        flag = false;
                        checkResult.setDirectRefund(false);
                        checkResult.setReason("校验失败:规则81-拒绝提现");
                        userBlackFlag = true;
                    }

                    if (flag && checkOaidAndImeiSimilarity(userActive, checkResult)) {
                        flag = false;
                        userBlackFlag = true;
                    }

                    if (flag
                            && "android".equalsIgnoreCase(userActive.getOs())
                            && checkKsdrHwGdtAbNormal(productEntity, userId, amount, totalAmount)) {
                        log.info("广点通收入占比异常-拦截提现 {} {}", appId, userId);
                        flag = false;
                        checkResult.setDirectRefund(false);
                        checkResult.setReason("广点通收入占比异常-拦截提现");
                    }

                    if (flag
                            && "android".equalsIgnoreCase(userActive.getOs())
                            && checkGdtRewardCallBackAbnormal(productEntity, userId, amount, totalAmount)) {
                        log.info("七组广点通服务端奖励回调异常-拦截提现 {} {}", appId, userId);
                        flag = false;
                        checkResult.setDirectRefund(false);
                        checkResult.setReason("七组广点通服务端奖励回调异常-拦截提现");
                    }

                    if (flag
                            && "android".equalsIgnoreCase(userActive.getOs())
                            && checkUserIncomeAbnormalNonBd(productEntity, userId, amount, totalAmount)) {
                        log.info("安卓非百度买量用户百度收入占比异常-拦截提现 {} {}", appId, userId);
                        flag = false;
                        checkResult.setDirectRefund(false);
                        checkResult.setReason("安卓非百度买量用户百度收入占比异常-拦截提现");

                        String xiaomiKey = RedisKeyConstants.getNonBaiduXiaomiKey("android", productEntity.getProduct(), String.valueOf(userId));
                        if (userEventJedisClusterClient.exists(xiaomiKey)) {
                            checkResult.setDirectRefund(true);
                            userGrayService.sendUserGrayWithdrawReason(appId.intValue(), userId.toString(), "安卓非百度买量用户百度收入占比异常-拦截提现");
                        }
                    }

                    if (flag
                            && !"android".equalsIgnoreCase(userActive.getOs())
                            && checkUserIncomeAbnormalIos(productEntity, userId, amount, totalAmount)) {
                        log.info("IOS百度收入占比异常-拦截提现 {} {}", appId, userId);
                        flag = false;
                        checkResult.setDirectRefund(false);
                        checkResult.setReason("IOS百度收入占比异常-拦截提现");
                    }

                    if (flag
                            && "android".equalsIgnoreCase(userActive.getOs())
                            && checkArpuIncAbnormalAndroid(productEntity, userId, amount, totalAmount)
                    ) {
                        log.info("十组安卓百度买量用户百度收入占比异常-拦截提现 {} {}", appId, userId);
                        flag = false;
                        checkResult.setDirectRefund(false);
                        checkResult.setReason("十组安卓百度买量用户百度收入占比异常-拦截提现");
                    }

                    if (flag
                            && "android".equalsIgnoreCase(userActive.getOs())
                            && checkGdtGapAbnormal(productEntity, userId, amount, totalAmount)
                    ) {
                        log.info("广点通收入占比异常且服务端奖励回调比例异常-拦截提现 {} {}", appId, userId);
                        flag = false;
                        checkResult.setDirectRefund(false);
                        checkResult.setReason("广点通收入占比异常且服务端奖励回调比例异常-拦截提现");
                    }

                    Date today = DateUtil.stringToDate(DateUtil.dateToString(new Date()) + " 00:00:00",DateUtil.COMMON_TIME_FORMAT);
                    String yesterdayBegin = DateUtil.dateToString(DateUtil.dateIncreaseByDay(new Date(), -1))+ " 00:00:00";
                    Date yesterday = DateUtil.stringToDate(yesterdayBegin,DateUtil.COMMON_TIME_FORMAT);
                    String beforeDayStr = DateUtil.dateToStringWithTime(DateUtil.dateIncreaseByDay(new Date(), -2));
                    Date beforeDay = DateUtil.stringToDate(beforeDayStr, DateUtil.COMMON_TIME_FORMAT);
                    if(flag && Objects.equals("项目五组", productEntity.getProductGroup()) && "android".equalsIgnoreCase(userActive.getOs())) {
                        //近2天用户
                        if(userActive.getCreateTime().after(beforeDay)){
                            if(amount >= 500 && userActive.isZiRan() && !checkAdCallBack(appId, userId, new Date[]{beforeDay, yesterday, today})){
                                log.info("五组单笔提现大于5/10元且无三方广告回调-延迟提现 product : {} userId : {} amount : {} ",productEntity.getProduct(),userId, amount);
                                flag = false;
                                checkResult.setDirectRefund(false);
                                checkResult.setReason("五组单笔提现大于5/10元且无三方广告回调-延迟提现");
                            }

                            if (amount >= 1000 && userActive.isOcpc() && !checkAdCallBack(appId, userId, new Date[]{beforeDay, yesterday, today})) {
                                log.info("五组单笔提现大于5/10元且无三方广告回调-延迟提现 product : {} userId : {} amount : {} ",productEntity.getProduct(),userId, amount);
                                flag = false;
                                checkResult.setDirectRefund(false);
                                checkResult.setReason("五组单笔提现大于5/10元且无三方广告回调-延迟提现");
                            }
                        }
                    }

                    String before5DayStr = DateUtil.dateToStringWithTime(DateUtil.dateIncreaseByDay(new Date(), -5));
                    Date before5Day = DateUtil.stringToDate(before5DayStr, DateUtil.COMMON_TIME_FORMAT);
                    if (flag
                            && "android".equalsIgnoreCase(userActive.getOs())
                            && checkUserIncomeAbnormalAndroid(productEntity, userId, amount, totalAmount)
                            && userActive.getCreateTime().after(before5Day)
                    ) {
                        log.info("ANDROID百度收入占比异常-拦截提现 {} {}", appId, userId);
                        flag = false;
                        checkResult.setDirectRefund(false);
                        checkResult.setReason("ANDROID百度收入占比异常-拦截提现");
                    }

                    // 判断当前时间段为1点至八点
                    // 获取当前时间
                    LocalTime now = LocalTime.now();

                    LocalTime start = LocalTime.of(1, 0); // 1点
                    LocalTime end = LocalTime.of(8, 0);   // 8点

                    // 获取今天的日期
                    Date todayStart = DateUtil.stringToDate(DateUtil.dateToString(new Date()) + " 00:00:00", DateUtil.COMMON_TIME_FORMAT);
                    Date tomorrowStart = DateUtil.stringToDate(DateUtil.dateToString(DateUtil.dateIncreaseByDay(new Date(), 1)) + " 00:00:00", DateUtil.COMMON_TIME_FORMAT);
                    // 判断当前时间是否在1点至8点之间
                    if (flag && now.isAfter(start) && now.isBefore(end)) {
                        if (Objects.equals("项目五组", productEntity.getProductGroup())
                                || (Objects.equals("项目十组", productEntity.getProductGroup()) && "cssb".equals(productEntity.getProduct()))) {

                            // 判断用户的创建时间是否在今天
                            if (userActive.getCreateTime().after(todayStart) && userActive.getCreateTime().before(tomorrowStart)) {
                                String highEcpmNum = userEventJedisClusterClient.get(RedisKeyConstants.getExposureHighEcpmKey(productEntity.getProduct(), String.valueOf(userId)));

                                if (totalAmount > 3000) {
                                    log.info("{} 新用户 {} {} 一点至八点累计提现超30元提现拦截", productEntity.getProductGroup(), appId, userId);
                                    flag = false;
                                    checkResult.setDirectRefund(false);
                                    checkResult.setReason(productEntity.getProductGroup() + " 新用户一点至八点累计提现超30元提现拦截");
                                }

                                if (checkNewUser1to8Ecpm(amount, totalAmount, userId, productEntity, highEcpmNum)) {
                                    log.info("{} 新用户 {} {} 一点至八点高ecpm提现拦截", productEntity.getProductGroup(), appId, userId);
                                    flag = false;
                                    checkResult.setDirectRefund(false);
                                    checkResult.setReason(productEntity.getProductGroup() + " 新用户一点至八点高ecpm提现拦截");
                                }
                            }

                        }
                    }

                    if (flag && Objects.nonNull(userActive)) {
                        // 中台默认提现Check
                        if (flag && userDeviceCheckService.checkSpecialChannel(userId,appId,amount,userActive)){
                            log.info("用户 {} {} 阿里云渠道 拒绝提现",appId,userId);
                            flag = false;
                            checkResult.setDirectRefund(false);
                            checkResult.setReason("阿里云渠道-拒绝提现");
                        }
                        //240709 排除亿万人生
//                        if (flag
//                                && "android".equalsIgnoreCase(userActive.getOs()) && !Objects.equals(appId, 810L)
//                                && userDeviceCheckService.checkOdsPoint(userId,appId,amount,userActive)
//                        ){
//                            log.info("用户 {} {} 安卓自然量用户埋点数检测未通过-拒绝提现-拒绝提现",appId,userId);
//                            flag = false;
//                            checkResult.setDirectRefund(false);
//                            checkResult.setReason("安卓自然量用户埋点数检测未通过-拒绝提现");
//                        }

                        if(flag && !"android".equalsIgnoreCase(userActive.getOs())//IOS
                                && userActive.isZiRan()//自然量
                        ){
                            if(userActive.getProduct() == null){
                                log.info("用户 {} {} iOS自然量用户注册信息错误-拦截提现", appId, userId);
//                                flag = false;
//                                checkResult.setDirectRefund(false);
//                                checkResult.setReason("iOS自然量用户无埋点上报-拦截提现");
                            }
//                            if(
//                                    checkIOSNoDist(appId, userId, amount, userActive, productEntity)
//                            ){
//                                log.info("用户 {} {} iOS自然量用户无埋点上报-拦截提现", appId, userId);
//                                flag = false;
//                                checkResult.setDirectRefund(false);
//                                checkResult.setReason("iOS自然量用户无埋点上报-拦截提现");
//                            }
                        }

                        if (flag && "android".equalsIgnoreCase(userActive.getOs()) && checkMultiIp(userId, productEntity)) {
                            log.info("用户 {} {} 校验失败:规则82-拒绝提现", appId, userId);
                            flag = false;
                            checkResult.setDirectRefund(false);
                            checkResult.setReason("校验失败:规则82-拒绝提现");
                            userBlackFlag = true;
                        }

                        //临时下线
//                        if (flag && userDeviceCheckService.checkRealtimeUserChannelNtf(userId,appId,amount,userActive)){
//                            log.info("用户 {} {} 处于拒绝渠道 拒绝提现",appId,userId);
//                            flag = false;
//                            checkResult.setDirectRefund(true);
//                            checkResult.setReason("处于拒绝渠道-非投放用户-拒绝提现");
//                        }
                        /*if (flag && "android".equalsIgnoreCase(userActive.getOs()) && userDeviceCheckService.checkUserIsInWaitCheckChannelAndModel(userId,appId,amount,userActive)) {
                            log.info("用户 {} {} 微信注册无埋点上报异常 拒绝提现",appId,userId);
                            flag = false;
                            checkResult.setDirectRefund(false);
                            checkResult.setReason("微信注册无埋点上报异常-拒绝提现");
                        }*/
                        if (flag && "android".equalsIgnoreCase(userActive.getOs()) && userInfoQueryService.checkUserBlackPkgs(userId,appId,amount,blackPkgs,userActive)) {
                            log.info("用户 {} {} 存在黑名单包名异常 拒绝提现",appId,userId);
                            flag = false;
                            checkResult.setDirectRefund(false);
                            checkResult.setReason("存在黑名单包名异常-拒绝提现");
                            userGrayService.grayUserAsync(GrayType.USER, Collections.singletonList(userId.toString()),
                                    "存在黑名单包名异常-提现拉黑",productEntity.getProduct(),"android","blackPkgs-withdraw");
                        }
                        /*if (flag && userDeviceCheckService.checkIosWithdrawCheckCaid(userId,appId,amount,userActive)) {
                            log.info("用户 {} {} ios检测caid用户异常 拒绝提现",appId,userId);
                            flag = false;
                            checkResult.setDirectRefund(false);
                            checkResult.setReason("ios检测caid用户异常-拒绝提现");
                        }*/

                        if (flag && manufacturerModelCheckService.isBlackModelAndManufacturer(userActive.getModel())) {
                            log.info("用户 {} {} 拉黑机型 拒绝提现",appId,userId);
                            flag = false;
                            checkResult.setDirectRefund(false);
                            checkResult.setReason("拉黑机型-拒绝提现");
                        }
                        /*if (flag && withdrawDelayUserService.isDelayUser(userActive.getProduct(),String.valueOf(userId))) {
                            log.info("用户 {} {} CSJ广告收入占比过低 拒绝提现",appId,userId);
                            flag = false;
                            checkResult.setDirectRefund(false);
                            checkResult.setReason("CSJ广告收入占比过低-拒绝提现");
                        }*/


                        // 1.49 触发渠道检测
                        if (totalAmount > 149){
//                            if (userDeviceCheckService.checkUserIsInWaitCheckChannel(userId, appId, amount, userActive)){
//                                log.info("用户 {} {} 处于提现过多处于拒绝渠道 拒绝提现",appId,userId);
//                                flag = false;
//                                checkResult.setDirectRefund(false);
//                                checkResult.setReason("处于拒绝渠道-拒绝提现");
//                            }
                            /*if (flag && userDeviceCheckService.checkUserIsBigHourNatureWithdrawCheckChannel(userId, appId, amount, userActive)) {
                                log.info("用户 {} {} 分时新增用户24h内提现异常 拒绝提现",appId,userId);
                                flag = false;
                                checkResult.setDirectRefund(false);
                                checkResult.setReason("分时新增自然量用户24h内提现异常-拒绝提现");
                            }*/
                            if (totalAmount > 400) {
                                if (flag && userDeviceCheckService.checkUserIsBigWithdrawCheckChannel(userId, appId, amount, userActive)) {
                                    log.info("用户 {} {} 处于提现过多处于大额提现异常渠道 拒绝提现",appId,userId);
                                    flag = false;
                                    checkResult.setDirectRefund(false);
                                    checkResult.setReason("新增用户大额提现异常渠道-拒绝提现");
                                }
                                if (
                                    flag
                                    && userDeviceCheckService.checkUserIsBigHourWithdrawCheckChannel(userId, appId, amount, userActive)
                                    && !StringUtils.equalsIgnoreCase("项目十组", productEntity.getProductGroup())
                                ) {
                                    log.info("用户 {} {} 分时新增用户24h内提现异常 拒绝提现",appId,userId);
                                    flag = false;
                                    checkResult.setDirectRefund(false);
                                    checkResult.setReason("分时新增用户24h内提现异常-拒绝提现");
                                }
                            }
                            if (totalAmount > 500 && checkZtAppList.contains(appId)) {
                                log.info("用户 {} {} 中台产品累计提现异常拦截 拒绝提现",appId,userId);
                                flag = false;
                                checkResult.setDirectRefund(false);
                                checkResult.setReason("中台产品累计提现异常拦截-拒绝提现");
                            }
                        }

                        if (flag && userActive.isZiRan() && "android".equalsIgnoreCase(userActive.getOs())) {
                            flag = handlerNature(appId, userId, totalAmount, userActive, flag, checkResult, productEntity, amount);
                        }

                        Integer yesterdayAmount = userDeviceCheckService.getUserYesterdayWithdraw(appId, userId);
//                        TODO 回调redis无内容临时屏蔽
//                        if(flag && totalAmount + yesterdayAmount > 500 && "android".equalsIgnoreCase(userActive.getOs())){
//                            String beforeDayStr = DateUtil.dateToStringWithTime(DateUtil.dateIncreaseByDay(new Date(), -2));
//                            Date beforeDay = DateUtil.stringToDate(beforeDayStr, DateUtil.COMMON_TIME_FORMAT);
//                            if(userActive.getCreateTime().after(beforeDay)){//近48小时 用户
//                                int callBackCount = getCallBakCount(productEntity.getProduct(), userId);
//                                if(totalAmount + yesterdayAmount >= 1500) {
//                                    if (callBackCount < 5){
//                                        flag = false;
//                                        log.info("服务回调次数过低校验 callBackCount getProduct : {} userId : {} appId : {} 小于 5 : {} 累计体现 : {}", productEntity.getProduct(), userId, appId, callBackCount, totalAmount + yesterdayAmount);
//                                    }
//                                } else if (totalAmount + yesterdayAmount >= 1000){
//                                    if (callBackCount < 3){
//                                        flag = false;
//                                        log.info("服务回调次数过低校验 callBackCount getProduct : {} userId : {} appId : {} 小于 3 : {} 累计体现 : {}", productEntity.getProduct(), userId, appId, callBackCount, totalAmount + yesterdayAmount);
//                                    }
//                                } else {//值大于5元
//                                    if (callBackCount < 1){
//                                        flag = false;
//                                        log.info("服务回调次数过低校验 callBackCount getProduct : {} userId : {} appId : {} 小于 1 : {} 累计体现 : {}", productEntity.getProduct(), userId, appId, callBackCount, totalAmount + yesterdayAmount);
//                                    }
//                                }
//                                if(flag){
//                                    log.info("服务回调次数过低校验 callBackCount 正常体现 回调次数 : {}  累计体现 : {} getProduct : {} userId : {} appId : {}", callBackCount, totalAmount + yesterdayAmount, productEntity.getProduct(), userId, appId);
//                                }else{
//                                    checkResult.setDirectRefund(false);
//                                    checkResult.setReason("服务回调次数过低拦截-拒绝提现");
//                                }
//                            }
//                        }

                        if (flag && shouldInterceptByContinuousWithdraw(appId, userId, amount, userActive.getOs())) {
                            log.info("大额提现间隔时间异常-拦截提现 {} {}",appId,userId);
                            flag = false;
                            checkResult.setDirectRefund(true);
                            checkResult.setReason("大额提现间隔时间异常-拦截提现");
                        }
                    }
                }

                // 10块触发设备校验
                if (totalAmount  > 1000) {
                    log.info("用户 {} {} 当日累计提现超过10元 当前金额 {} 本次提现进行设备-多userId校验",userId,appId,amount);
                    // 提现check设备是否对应多用户
                    if (flag && userDeviceCheckService.isUserDeviceHasManyWechat(appId, userId)) {
                        log.info("用户 {} {} 设备关联过多用户 拒绝提现", appId, userId);
                        flag = false;
                        checkResult.setDirectRefund(true);
                        checkResult.setReason("设备关联过多用户-拒绝提现");
                    }
                }

                // 默认5块校验
                if (totalAmount >= checkLimitAmountGroup7){
                    CheckInnerRsp checkInnerRsp = userAdExposureCheckService.checkInnerPull(appId,userId,amount,totalAmount);
                    if (flag && checkInnerRsp.getReject()){
                        log.info("用户 {} {} 内拉新检测异常 拒绝提现", appId, userId);
                        flag = false;
                        checkResult.setDirectRefund(false);
                        if (checkInnerRsp.getNotFundActive() != null && checkInnerRsp.getNotFundActive()){
                            checkResult.setReason(checkInnerRsp.getReason());
                        }else {
                            checkResult.setReason(String.format("内拉新检测异常-延迟提现 Arpu:%s,Withdraw:%s", checkInnerRsp.getArpu(), checkInnerRsp.getWithdraw()));
                        }
                    }
                }

                // 七组20直接拒绝
                if (totalAmount >= 2000){
                    if (userDeviceCheckService.newUserWithdrawLimit(userId,appId, totalAmount)){
                        log.info("用户 {} {} 用户累积当日提现超过20块,暂缓提现",appId,userId);
                        flag = false;
                        checkResult.setDirectRefund(false);
                        if(StringUtils.isNotBlank(checkResult.getReason())){
                            // 刷中台ecpm的用户可能会被解开，这个限制不允许解，找研发吧
                            checkResult.setReason(checkResult.getReason() + "|[中台拦截]累积提现过高-延缓提现");
                        }else {
                            checkResult.setReason("累积提现过高-延缓提现");
                        }
                    }
                }


                // 两块触发回调校验
                if (totalAmount > 500){
                    // 暂时加日志 检查我方-三方回调数据
                    if (flag && userAdExposureCheckService.isExposureGapThird(appId, userId)) {
                        log.info("用户 {} {} 三方回调异常 鉴定为刷量 拒绝提现",appId,userId);
                        userGrayService.sendUserGrayWithdrawReason(appId.intValue(),userId.toString(),"非投放用户检测三方回调GAP-拉灰");
                        flag = false;
                        checkResult.setDirectRefund(true);
                        checkResult.setReason("三方回调异常-鉴定为刷量-拒绝提现");
                    }
                }

//                if (userGrayIpMapper.countExist(null, GrayType.USER.getType(), String.valueOf(userId)) > 0) {
//                    flag = false;
//                    checkResult.setDirectRefund(true);
//                    checkResult.setReason("用户已被全局拉黑-拒绝提现");
//                }

            }
            if(StringUtils.isNotBlank(orderNo)) {
                // 审核未通过-直接修改结果
                if (!flag || !teamFlag) {
                    // 记录订单用户预警-发放 只要是拒绝审核都记录
                    withdrawNotSendService.createIfNotExist(appId, userId, amount, orderNo, checkResult.getReason(), checkResult.getDirectRefund());
                    // 查询订单是否可以无视中台规则直接发放
                    if (withdrawNotSendService.queryOrderCanSend(appId, userId, orderNo)) {
                        log.info("===> 用户 {} {} 提现审核 查到配置修改 将结果 {} -> true", appId, userId, flag);
                        flag = true;
                        teamFlag = true;
                    }
                    // userId全局拉黑
                    if (userBlackFlag && (!flag)) {
                        userGrayService.grayUserAsync(GrayType.USER, Collections.singletonList(userId.toString()),
                                checkResult.getReason(), null,userActive.getOs(),"blackPkgs-withdraw");
                    }
                }
                // 跨天老订单 Pending
                if (flag && teamFlag) {
                    WithdrawNotSend withdrawNotSend = withdrawNotSendService.queryOrderByOrderNo(appId, userId, orderNo);
                    if (withdrawNotSend != null && withdrawNotSend.getCanSend() == 0) {
                        log.info("===> 用户 {} {} 订单 {} 查询到过往拒绝提现记录 不予通过", appId, userId, orderNo);
                        flag = false;
                        checkResult.setDirectRefund(false);
                        checkResult.setReason("历史鉴定订单不发送");
                    }
                }

            }
            log.info("===> 用户 {} {} 提现审核 中台核查结果 {}   项目组核查结果 {}",appId, userId, flag, teamFlag);
            checkResult.setPass(flag && teamFlag);

            if(StringUtils.isNotBlank(orderNo)) {
                // 一个订单 只加一次
                if (flag && teamFlag) {
                    if (userDeviceCheckService.canAdd(orderNo)) {
                        userDeviceCheckService.incrTodayAmount(userId, appId, amount);

                        if (userActive != null) {
                            userDeviceCheckService.incrAppTodayAmount(userActive.getOs(), appId, amount);
                            if (userActive.isZiRan()) {
                                Calendar yesterCalendar = Calendar.getInstance();
                                yesterCalendar.set(
                                        yesterCalendar.get(Calendar.YEAR),
                                        yesterCalendar.get(Calendar.MONTH),
                                        yesterCalendar.get(Calendar.DAY_OF_MONTH) - 1,
                                        0,
                                        0,
                                        0
                                );
                                if (userActive.getCreateTime().compareTo(yesterCalendar.getTime()) >= 0) {
                                    userDeviceCheckService.incrZiranAppTodayAmount(userActive.getOs(), appId, amount);
                                }
                            }
                        }
                    }
                }
            }
            return checkResult;
        }catch (Exception e){
            log.error("Error: {}", e.getMessage(), e);
        }
        return  checkResult;
    }

    private boolean checkFirstEventDist(Long appId, Long userId, UserActive userActive, ProductEntity productEntity, String eventDistUserInfo, String adDataEventDistUserInfo, CheckResult checkResult) {
        if (userActive.isZiRan() && StringUtils.isNotBlank(eventDistUserInfo)) {
            UserEventInfoBean userEventInfoBean = JSONObject.parseObject(eventDistUserInfo, UserEventInfoBean.class);
            if (StringUtils.isBlank(userEventInfoBean.getEvent()) || StringUtils.isBlank(userEventInfoBean.getManufacturer())) {
                return false;
            }

            Map<String, List<String>> firstEventConfigMap = checkFirstEventMap.get(productEntity.getProduct());
            if (MapUtils.isEmpty(firstEventConfigMap)) {
                firstEventConfigMap = checkFirstEventMap.get("all");
                if (MapUtils.isEmpty(firstEventConfigMap)) {
                    return false;
                }
            }

            List<String> eventList = firstEventConfigMap.get("event");
            List<String> manufacturerList = firstEventConfigMap.get("manufacturer");

            if (CollectionUtils.isEmpty(eventList) ||  CollectionUtils.isEmpty(manufacturerList)) {
                return false;
            }

            if (eventList.contains(userEventInfoBean.getEvent()) && !manufacturerList.contains(userEventInfoBean.getManufacturer())) {
                log.info("用户 {} {} 校验失败:规则40-拒绝提现 event: {}",
                        appId, userId, userEventInfoBean);
                checkResult.setDirectRefund(false);
                checkResult.setReason("校验失败:规则40-拒绝提现");
                return true;
            }
        }

        return false;
    }

    private boolean checkImeiConsistency(Long appId, Long userId, UserActive userActive, ProductEntity productEntity, String eventDistUserInfo, String adDataEventDistUserInfo, CheckResult checkResult) {
        // 只检查当日注册的安卓用户
        if (!userActive.isTodayUser() || !userActive.isAndroid()) {
            return false;
        }

        // 检查注册时的imei
        String registerImei = userActive.getImei();
        if (StringUtils.isBlank(registerImei)) {
            return false;
        }

        // 检查注册imei是否符合格式要求：以e450d开头+纯数字
        if (!registerImei.startsWith("e450d") || !registerImei.substring(5).matches("\\d+")) {
            return false;
        }

        // 检查AdData埋点信息
        if (StringUtils.isBlank(adDataEventDistUserInfo)) {
            return false;
        }

        // 解析AdData埋点信息
        UserEventInfoBean adDataEventUserInfo = JSONObject.parseObject(adDataEventDistUserInfo, UserEventInfoBean.class);
        String adDataImei = adDataEventUserInfo.getImei();

        // 检查AdData埋点的imei是否为空
        if (StringUtils.isBlank(adDataImei)) {
            return false;
        }

        // 比较两个imei是否一致
        if (!StringUtils.equals(registerImei, adDataImei)) {
            log.info("用户 {} {} 校验失败:规则37-拒绝提现 注册imei: {}, AdData imei: {}",
                    appId, userId, registerImei, adDataImei);
            checkResult.setDirectRefund(false);
            checkResult.setReason("校验失败:规则37-拒绝提现");
            return true;
        }

        return false;
    }

    private boolean checkAdDataDistManufacturer(Long appId, Long userId, UserActive userActive, ProductEntity productEntity, String eventDistUserInfo, String adDataEventDistUserInfo, CheckResult checkResult) {
        if (StringUtils.isBlank(adDataEventDistUserInfo)) return false;

        UserEventInfoBean adDataEventUserInfo = JSONObject.parseObject(adDataEventDistUserInfo, UserEventInfoBean.class);

        // 检查 manufacturer 是否是 imei 或 oaid 的子字符串
        String adDataManufacturer = adDataEventUserInfo.getManufacturer();
        String adDataImei = adDataEventUserInfo.getImei();
        String adDataOaid = adDataEventUserInfo.getOaid();

        if (StringUtils.isNotBlank(adDataManufacturer)) {
            if ((StringUtils.isNotBlank(adDataImei) && adDataImei.contains(adDataManufacturer)) ||
                    (StringUtils.isNotBlank(adDataOaid) && adDataOaid.contains(adDataManufacturer))) {
                log.info("用户 {} {} 校验失败:规则34-拒绝提现 adData:{}", appId, userId, adDataEventDistUserInfo);
                checkResult.setDirectRefund(false);
                checkResult.setReason("校验失败:规则34-拒绝提现");
                return true;
            }
        }

        // 检查 model 是否是 oaid 的子字符串
        String adDataModel = adDataEventUserInfo.getModel();

        if (StringUtils.isNotBlank(adDataModel)) {
            if ((StringUtils.isNotBlank(adDataOaid) && adDataOaid.contains(adDataModel))) {
                log.info("用户 {} {} 校验失败:规则35-拒绝提现 adData:{}", appId, userId, adDataEventDistUserInfo);
                checkResult.setDirectRefund(false);
                checkResult.setReason("校验失败:规则35-拒绝提现");
                return true;
            }
        }

        // 检查 model 是否以小写字母e开头+纯数字，或者 model 以小写字母e开头且 manufacturer = model
        if (StringUtils.isNotBlank(adDataModel) && adDataModel.startsWith("e")) {
            // 检查是否是 e 开头加纯数字
            boolean isEPlusDigits = adDataModel.length() > 1 && adDataModel.substring(1).matches("\\d+");

            // 检查是否 model 以 e 开头且 manufacturer = model
            boolean isEAndEqualsManufacturer = StringUtils.isNotBlank(adDataManufacturer) &&
                    adDataModel.equals(adDataManufacturer);

            if (isEPlusDigits || isEAndEqualsManufacturer) {
                log.info("用户 {} {} 校验失败:规则36-拒绝提现 adData:{}", appId, userId, adDataEventDistUserInfo);
                checkResult.setDirectRefund(false);
                checkResult.setReason("校验失败:规则36-拒绝提现");
                return true;
            }
        }
        return false;
    }

    private boolean checkDistManufacturer(Long appId, Long userId, UserActive userActive, ProductEntity productEntity, String eventDistUserInfo, CheckResult checkResult) {

        if (!userActive.isPastOneDaysUser()) return false;

        if (StringUtils.isBlank(eventDistUserInfo)) return false;

        UserEventInfoBean userEventInfoBean = JSONObject.parseObject(eventDistUserInfo, UserEventInfoBean.class);

        String manufacturer = userEventInfoBean.getManufacturer();

        if (StringUtils.isBlank(manufacturer)) return false;

        if (manufacturer.matches("\\d+") && manufacturer.length() >= 6) {
            log.info("用户 {} {} 校验失败:规则33拒绝提现 distInfo:{}", appId, userId, eventDistUserInfo);
            checkResult.setDirectRefund(false);
            checkResult.setReason("校验失败:规则33-拒绝提现");
            return true;
        }

        return false;
    }

    /**
     * 校验用户埋点信息
     *
     * @param appId
     * @param userId
     * @param userActive
     * @param productEntity
     * @param eventDistUserInfo
     * @param adDataEventDistUserInfo
     * @param checkResult
     * @return
     */
    private boolean checkEventDistUserInfo(Long appId, Long userId, UserActive userActive, ProductEntity productEntity, String eventDistUserInfo, String adDataEventDistUserInfo, CheckResult checkResult) {
        if (!userActive.isZiRan()) return false;

        boolean hitRuleFlag = false;

        if (StringUtils.isNotBlank(eventDistUserInfo)) {
            UserEventInfoBean userEventInfoBean = JSONObject.parseObject(eventDistUserInfo, UserEventInfoBean.class);

            String manufacturer = userEventInfoBean.getManufacturer();
            String model = userEventInfoBean.getModel();

            hitRuleFlag = StringUtils.isNotBlank(manufacturer)
                    && StringUtils.isNotBlank(model)
                    && (StrUtil.equals(manufacturer, model) || manufacturer.contains(model))
                    && manufacturer.length() >= 8;
        }

        if (StringUtils.isNotBlank(adDataEventDistUserInfo)) {
            UserEventInfoBean adDataEventUserInfo = JSONObject.parseObject(adDataEventDistUserInfo, UserEventInfoBean.class);

            String adDataManufacturer = adDataEventUserInfo.getManufacturer();
            String adDataModel = adDataEventUserInfo.getModel();

            if (!hitRuleFlag
                    && StringUtils.isNotBlank(adDataManufacturer)
                    && StringUtils.isNotBlank(adDataModel)
                    && (StrUtil.equals(adDataManufacturer, adDataModel) || adDataManufacturer.contains(adDataModel))
                    && adDataManufacturer.length() >= 8) {
                hitRuleFlag = true;
            }

        }


        if (hitRuleFlag) {
            log.info("用户 {} {} 校验失败:规则32-拒绝提现 distInfo:{} adData:{}", appId, userId, eventDistUserInfo, adDataEventDistUserInfo);
            checkResult.setDirectRefund(false);
            checkResult.setReason("校验失败:规则32-拒绝提现");
            return true;
        }

        return false;

    }

    /**
     * 校验当日注册安卓用户oaid格式和IP一致性
     *
     * @param appId
     * @param userId
     * @param userActive
     * @param productEntity
     * @param eventDistUserInfo
     * @param adDataEventDistUserInfo
     * @param checkResult
     * @return
     */
    private boolean checkOaidIpConsistency(Long appId, Long userId, UserActive userActive, ProductEntity productEntity, String eventDistUserInfo, String adDataEventDistUserInfo, CheckResult checkResult) {
        // 检查两条埋点信息是否存在
        if (StringUtils.isBlank(eventDistUserInfo) || StringUtils.isBlank(adDataEventDistUserInfo)) {
            return false;
        }

        // 解析AdData埋点信息
        UserEventInfoBean adDataEventUserInfo = JSONObject.parseObject(adDataEventDistUserInfo, UserEventInfoBean.class);

        // 检查AdData埋点中的oaid格式：以"e450d"开头+纯数字
        String adDataOaid = adDataEventUserInfo.getOaid();
        if (StringUtils.isBlank(adDataOaid) || !adDataOaid.matches("e450d\\d+")) {
            return false;
        }

        // 解析第一条埋点信息
        UserEventInfoBean eventUserInfo = JSONObject.parseObject(eventDistUserInfo, UserEventInfoBean.class);

        // 获取两条埋点的IP
        String eventIp = eventUserInfo.getIp();
        String adDataIp = adDataEventUserInfo.getIp();

        // 检查IP是否为空
        if (StringUtils.isBlank(eventIp) || StringUtils.isBlank(adDataIp)) {
            return false;
        }

        // 比较两个IP是否不相等，不相等则拦截
        if (!StringUtils.equals(eventIp, adDataIp)) {
            log.info("用户 {} {} 校验失败:规则38-拒绝提现 第一条埋点IP: {}, AdData埋点IP: {}, AdData oaid: {}",
                    appId, userId, eventIp, adDataIp, adDataOaid);
            checkResult.setDirectRefund(false);
            checkResult.setReason("校验失败:规则38-拒绝提现");
            return true;
        }

        return false;
    }

    /**
     * 校验当日注册安卓用户可疑设备前缀
     * 检查用户注册信息和埋点数据中的OAID/IMEI是否以配置的可疑前缀开头
     *
     * @param appId
     * @param userId
     * @param userActive
     * @param productEntity
     * @param eventDistUserInfo
     * @param adDataEventDistUserInfo
     * @param checkResult
     * @return true表示检测到风险需要拦截，false表示通过检查
     */
    private boolean checkSuspiciousDevicePrefix(Long appId, Long userId, UserActive userActive, ProductEntity productEntity, String eventDistUserInfo, String adDataEventDistUserInfo, CheckResult checkResult) {
        // 检查配置是否为空
        if (CollectionUtils.isEmpty(suspiciousDevicePrefixList)) {
            return false;
        }

        // 检查注册信息中的OAID和IMEI
        if (checkDeviceIdWithPrefix(userActive.getOaid(), "注册OAID") ||
            checkDeviceIdWithPrefix(userActive.getImei(), "注册IMEI")) {
            log.info("用户 {} {} 校验失败:规则39-拒绝提现 userActive:{}", appId, userId, userActive);
            checkResult.setDirectRefund(false);
            checkResult.setReason("校验失败:规则39-拒绝提现");
            return true;
        }

        // 检查第一个埋点信息
        if (StringUtils.isNotBlank(eventDistUserInfo)) {
            try {
                UserEventInfoBean eventUserInfo = JSONObject.parseObject(eventDistUserInfo, UserEventInfoBean.class);
                if (checkDeviceIdWithPrefix(eventUserInfo.getOaid(), "第一个埋点OAID") ||
                    checkDeviceIdWithPrefix(eventUserInfo.getImei(), "第一个埋点IMEI")) {
                    log.info("用户 {} {} 校验失败:规则39-拒绝提现 event:{}", appId, userId, eventDistUserInfo);
                    checkResult.setDirectRefund(false);
                    checkResult.setReason("校验失败:规则39-拒绝提现");
                    return true;
                }
            } catch (Exception e) {
                log.warn("解析第一个埋点信息失败 {} {} : {}", appId, userId, e.getMessage());
            }
        }

        // 检查第二个埋点信息
        if (StringUtils.isNotBlank(adDataEventDistUserInfo)) {
            try {
                UserEventInfoBean adDataEventUserInfo = JSONObject.parseObject(adDataEventDistUserInfo, UserEventInfoBean.class);
                if (checkDeviceIdWithPrefix(adDataEventUserInfo.getOaid(), "第二个埋点OAID") ||
                    checkDeviceIdWithPrefix(adDataEventUserInfo.getImei(), "第二个埋点IMEI")) {
                    log.info("用户 {} {} 校验失败:规则39-拒绝提现 adEvent:{}", appId, userId, adDataEventDistUserInfo);
                    checkResult.setDirectRefund(false);
                    checkResult.setReason("校验失败:规则39-拒绝提现");
                    return true;
                }
            } catch (Exception e) {
                log.warn("解析第二个埋点信息失败 {} {} : {}", appId, userId, e.getMessage());
            }
        }

        return false;
    }

    /**
     * 检查设备ID是否以配置的可疑前缀开头
     *
     * @param deviceId 设备ID（OAID或IMEI）
     * @param deviceType 设备类型描述，用于日志
     * @return true表示检测到可疑前缀
     */
    private boolean checkDeviceIdWithPrefix(String deviceId, String deviceType) {
        if (StringUtils.isBlank(deviceId)) {
            return false;
        }

        for (String prefix : suspiciousDevicePrefixList) {
            if (StringUtils.isNotBlank(prefix) && deviceId.startsWith(prefix)) {
                log.debug("检测到可疑设备前缀: {} 以 {} 开头", deviceType, prefix);
                return true;
            }
        }

        return false;
    }

    /**
     * 注册和激活信息检测
     *
     * @param appId
     * @param userId
     * @param userActive
     * @param productEntity
     * @param eventDistUserInfo
     * @param adDataEventDistUserInfo
     * @param checkResult
     * @return
     */
    private boolean checkRegisterAndDistInfo(Long appId, Long userId, UserActive userActive, ProductEntity productEntity, String eventDistUserInfo, String adDataEventDistUserInfo, CheckResult checkResult) {
        if (!enableCheckDistModel) return false;

        if (!userActive.isZiRan()) return false;

        if (StringUtils.isBlank(userActive.getModel())) return false;

        if (StringUtils.isBlank(userActive.getChannel())) return false;

        boolean hitRuleFlag = false;

        if (StringUtils.isNotBlank(eventDistUserInfo)) {
            UserEventInfoBean userEventInfoBean = JSONObject.parseObject(eventDistUserInfo, UserEventInfoBean.class);

            if ((StringUtils.isNotBlank(userEventInfoBean.getModel())
                    && !StrUtil.equals(
                    userActive.getModel().replaceAll("\\s", "")
                    , userEventInfoBean.getModel().replaceAll("\\s", "")
            )
            )
                    || !StrUtil.equals(userActive.getChannel(), userEventInfoBean.getChannel())
            ) {
                hitRuleFlag = true;
            }
        }

        if (StringUtils.isNotBlank(adDataEventDistUserInfo)) {
            UserEventInfoBean adDataEventUserInfo = JSONObject.parseObject(adDataEventDistUserInfo, UserEventInfoBean.class);
            if ((StringUtils.isNotBlank(adDataEventUserInfo.getModel())
                    && !StrUtil.equals(
                    userActive.getModel().replaceAll("\\s", "")
                    , adDataEventUserInfo.getModel().replaceAll("\\s", ""))
            )
                    || !StrUtil.equals(userActive.getChannel(), adDataEventUserInfo.getChannel())) {
                hitRuleFlag = true;
            }
        }

        if (hitRuleFlag) {
            log.info("用户 {} {} 校验失败:规则31-拒绝提现 distInfo:{} active:{} adData: {}", appId, userId, eventDistUserInfo, JSONObject.toJSONString(userActive), adDataEventDistUserInfo);
            checkResult.setDirectRefund(false);
            checkResult.setReason("校验失败:规则31-拒绝提现");
            return true;
        }

        return false;
    }

//    @PostConstruct
    public void test() {

        String product = "qzzmms";
        Long userId = 1766914777L;
        Long appId = 1L;

        UserActive userActive = tfUserService.queryUserDeviceGui(product, String.valueOf(userId), appId);

        ProductEntity productEntity = new ProductEntity();
        productEntity.setProduct(product);

        CheckResult checkResult = new CheckResult();

//        String eventDistUserInfo = userEventJedisClusterClient.get(RedisKeyConstants.getEventDistInfoKey(productEntity.getProduct(), userId));
//        String adDataEventDistUserInfo = userEventJedisClusterClient.get(RedisKeyConstants.getAdDataEventDistInfoKey(productEntity.getProduct(), userId));
        if (checkOs(userActive, appId, userId, productEntity, checkResult)) {
            System.out.println(111);
        }
//        product = "zyxy";
//        userId = 2295349005L;
//        productEntity.setProduct(product);
//        userActive = tfUserService.queryUserDeviceGui(product, String.valueOf(userId), appId);
//        eventDistUserInfo = userEventJedisClusterClient.get(RedisKeyConstants.getEventDistInfoKey(productEntity.getProduct(), userId));
//        adDataEventDistUserInfo = userEventJedisClusterClient.get(RedisKeyConstants.getAdDataEventDistInfoKey(productEntity.getProduct(), userId));
//        if ( userActive.isAndroid()
//                && eventDistPatternCheckService.checkAppVersionOaidPattern(appId, userId, userActive, productEntity, eventDistUserInfo, adDataEventDistUserInfo, checkResult)) {
//            System.out.println(11);
//        }
    }

    private boolean checkXiaoMiGdtGap(UserActive userActive, CheckResult checkResult, Long appId, Long userId, ProductEntity productEntity, Integer totalAmount) {
        if ("项目十组".equals(productEntity.getProductGroup())) return false;

        if (totalAmount > 500 && userEventJedisClusterClient.exists(RedisKeyConstants.getGdtGapExKey(productEntity.getProduct(), String.valueOf(userId)))) {
            log.info("用户 {} {} 校验失败:规则30-拒绝提现", appId, userId);
            checkResult.setDirectRefund(true);
            checkResult.setReason("校验失败:规则30-拒绝提现");
            userGrayService.sendUserGrayWithdrawReason(appId.intValue(),userId.toString(),"校验失败:规则30-拒绝提现");
            return true;
        }

        return false;
    }

    private boolean checkGroupSgmGdtEx(UserActive userActive, CheckResult checkResult, Long appId, Long userId, ProductEntity productEntity, Integer totalAmount) {
        if (!"项目一组".equals(productEntity.getProductGroup())) return false;

        if ("android".equals(userActive.getOs())
                && userEventJedisClusterClient.exists(RedisKeyConstants.getSgmGdtGroupKey(productEntity.getProduct(), String.valueOf(userId)))) {
            log.info("用户 {} {} GDT&sigmob收入占比异常拦截", appId, userId);
            checkResult.setDirectRefund(true);
            checkResult.setReason("GDT&sigmob收入占比异常拦截");
            return true;
        }

        return false;
    }


    /**
     * 注册channel与埋点channel不一致校验
     *
     * @param userActive
     * @param checkResult
     * @param appId
     * @param userId
     * @param productEntity
     * @param amount
     * @param totalAmount
     * @return
     */
    private boolean checkUserChannel(UserActive userActive, CheckResult checkResult, Long appId, Long userId, ProductEntity productEntity, Integer amount, Integer totalAmount) {

        if (!enableCheckUserChannel) return false;

        if ("ios".equals(userActive.getOs())) return false;

        if (!userActive.isZiRan()) return false;

        Integer appZiranAmount = userDeviceCheckService.getAppZiranAmount(userActive.getOs(), appId, amount);

        if (appZiranAmount <= 5000) return false;

        Date todayStart = DateUtil.stringToDate(DateUtil.dateToString(new Date()) + " 00:00:00", DateUtil.COMMON_TIME_FORMAT);
        if (userActive.getCreateTime().before(todayStart)) {
            return false;
        }

        if (StringUtils.isBlank(userActive.getChannel())) return false;

        Set<String> smembers = userEventJedisClusterClient.smembers(RedisKeyConstants.getProductChannelKey(productEntity.getProduct()));

        if (CollectionUtils.isEmpty(smembers)) return false;

        if (!smembers.contains(userActive.getChannel())) {
            log.info("用户 {} {} 校验失败:规则29-拒绝提现", appId, userId);
            checkResult.setDirectRefund(false);
            checkResult.setReason("校验失败:规则29-拒绝提现");
            return true;
        }

        return false;
    }

    private boolean checkProductChannel(UserActive userActive, CheckResult checkResult, Long appId, Long userId, ProductEntity productEntity) {

        if (StringUtils.isBlank(userActive.getChannel())) return false;

        if ("ios".equals(userActive.getOs())) return false;

        if (checkProductWhiteChannelList.contains("all")) return false;

        for (String channel : checkProductWhiteChannelList) {
            if (userActive.getChannel().contains(channel)) return false;
        }

        String product = productEntity.getProduct();
        if ("项目七组".equals(productEntity.getProductGroup())) {
            product = product.replaceFirst("^qz", "");
        }

        product = product.replaceFirst("\\d$", "");

        if (!userActive.getChannel().contains(product)) {
            log.info("用户 {} {} 校验失败:规则28-拒绝提现", appId, userId);
            checkResult.setDirectRefund(true);
            checkResult.setReason("校验失败:规则28-拒绝提现");
            return true;
        }

        return false;
    }

    private boolean checkBlackProductChannel(UserActive userActive, CheckResult checkResult, Long appId, Long userId, ProductEntity productEntity) {
        if (StringUtils.isBlank(userActive.getChannel()) || !userActive.isZiRan()) {
            return false;
        }

        if (!checkBlackProductChannelMap.containsKey(productEntity.getProduct())) return false;

        Set<String> blackChannelSet = checkBlackProductChannelMap.get(productEntity.getProduct());

        if (blackChannelSet.contains(userActive.getChannel())) {
            log.info("用户 {} {} 校验失败:规则27-拒绝提现", appId, userId);
            checkResult.setDirectRefund(false);
            checkResult.setReason("校验失败:规则27-拒绝提现");
            return true;
        }

        return false;
    }

    private boolean checkOs(UserActive userActive, Long appId, Long userId, ProductEntity productEntity, CheckResult checkResult) {

        boolean osDeviceFlag = false;

        if ("android".equals(userActive.getOs()) && StringUtils.isNotBlank(userActive.getChannel())) {
            if ("AppStore".equals(userActive.getChannel()) || userActive.getChannel().contains("iPhone")) {
                log.info("用户 {} {} 校验失败:规则25-拒绝提现", appId, userId);
                checkResult.setDirectRefund(true);
                checkResult.setReason("校验失败:规则25-拒绝提现");
                return true;
            }
            if (StringUtils.isNotBlank(userActive.getCaid()) || StringUtils.isNotBlank(userActive.getIdfa())) {
                osDeviceFlag = true;
            }
        } else if ("ios".equals(userActive.getOs())) {
            if (isInvalidIosDevice(userActive)) {
                osDeviceFlag = true;
            }
        }

        if (osDeviceFlag) {
            log.info("用户 {} {} 校验失败:规则26-拒绝提现", appId, userId);
            checkResult.setDirectRefund(true);
            checkResult.setReason("校验失败:规则26-拒绝提现");
            return true;
        }


        return false;
    }

    private boolean isInvalidIosDevice(UserActive userActive) {
        if (StringUtils.isNotBlank(userActive.getOaid()) && !"undefined".equals(userActive.getOaid())) {
            return true;
        }

        if (StringUtils.isNotBlank(userActive.getImei()) && !"undefined".equals(userActive.getImei())) {
            return true;
        }

        if (StringUtils.isNotBlank(userActive.getIdfa())
                && userActive.getIdfa().matches("\\d+")
                && !isInvalidIdfa(userActive.getIdfa())) return true;

        return StringUtils.isNotBlank(userActive.getCaid()) && userActive.getCaid().matches("\\d+");
    }

    /**
     * 校验IDFA是否无效
     * @param idfa IDFA字符串
     * @return true表示无效IDFA
     */
    private boolean isInvalidIdfa(String idfa) {
        if (StringUtils.isBlank(idfa)) {
            return false;
        }

        // 检查是否全为0
        String normalizedIdfa = idfa.replace("-", "").toLowerCase();
        return normalizedIdfa.matches("^[0]+$");
    }

    private boolean checkExIp(UserActive userActive, Long appId, Long userId, ProductEntity productEntity, CheckResult checkResult) {
        if (StringUtils.isNotBlank(userActive.getIp())
                && userEventJedisClusterClient.exists(RedisKeyConstants.getExIpKey(userActive.getOs(), productEntity.getProduct(), userActive.getIp()))) {
            log.info("用户 {} {} 校验失败:规则23-拒绝提现", appId, userId);
            checkResult.setDirectRefund(true);
            checkResult.setReason("校验失败:规则23-拒绝提现");
            return true;
        }

        return false;
    }

    /**
     * oaid和imei相似度校验
     * @param userActive
     * @param checkResult
     * @return
     */
    private boolean checkOaidAndImeiSimilarity(UserActive userActive, CheckResult checkResult) {
        if ("ios".equals(userActive.getOs())) return false;

        if (StringUtils.isBlank(userActive.getImei()) || StringUtils.isBlank(userActive.getOaid())) return false;

        if (StringUtils.equals(userActive.getOaid(), userActive.getImei())) return false;

        String reverseOaid = new StringBuilder(userActive.getOaid()).reverse().toString();

        if (StringSimilarity.isSimilar(userActive.getOaid(), userActive.getImei(), 0.7)
                || StringSimilarity.isSimilar(reverseOaid, userActive.getImei(), 0.7)) {
            log.info("用户 {} {} 校验失败:规则101-拒绝提现", userActive.getProduct(), userActive.getUserId());
            checkResult.setDirectRefund(false);
            checkResult.setReason("校验失败:规则101-拒绝提现");
            return true;
        }

        return false;
    }

    /**
     * 校验自然量提现金额
     *
     * @param appId
     * @param productEntity
     * @param userActive
     * @param amount
     * @return
     */
    private boolean checkZiranAmount(Long userId, Long appId, ProductEntity productEntity, UserActive userActive, Integer amount, CheckResult checkResult) {

        if (!userActive.isZiRan()) return false;

        if (checkZiranAmountWhiteProductList.contains(productEntity.getProduct()) || checkZiranAmountWhiteProductList.contains("all")) return false;

        Calendar yesterCalendar = Calendar.getInstance();
        yesterCalendar.set(
                yesterCalendar.get(Calendar.YEAR),
                yesterCalendar.get(Calendar.MONTH),
                yesterCalendar.get(Calendar.DAY_OF_MONTH) - 1,
                0,
                0,
                0
        );
        if (userActive.getCreateTime().compareTo(yesterCalendar.getTime()) < 0) {
            return false;
        }

        Map<String, String> config = checkZiranAmountMap.get(productEntity.getProduct());
        if (MapUtils.isEmpty(config)) {
            config = checkZiranAmountMap.get(productEntity.getProductGroup());

            if (MapUtils.isEmpty(config)) return false;
        }


        Integer configTotalAmount = Integer.valueOf(config.get("totalAmount"));
        Integer configZiranProportion = Integer.valueOf(config.get("ziranProportion"));
        String timeRange = config.get("timeRange");
        String[] timeRangeArr = timeRange.split("-");
        Integer start = Integer.valueOf(timeRangeArr[0]);
        Integer end = Integer.valueOf(timeRangeArr[1]);
        Integer now = DateUtil.getHour(new Date());
        if (now >= start && now < end) {

            Integer appAmount = userDeviceCheckService.getAppAmount(userActive.getOs(), appId, amount);
            Integer appZiranAmount = userDeviceCheckService.getAppZiranAmount(userActive.getOs(), appId, amount);

            if (appZiranAmount > configTotalAmount * 100) {
                // 计算appZiranAmount占比appAmount
                Integer proportion = DoubleUtil.to45(Double.valueOf(appZiranAmount) / Double.valueOf(appAmount) * 100);
                if (proportion > configZiranProportion) {
                    log.info("用户 {} {} 校验失败:规则24-拒绝提现 appAmount:{} appZiranAmount:{} proportion:{} configTotalAmount:{} configZiranProportion:{}", appId, userId, appAmount, appZiranAmount, proportion, configTotalAmount, configZiranProportion);
                    checkResult.setDirectRefund(true);
                    checkResult.setReason("校验失败:规则24-拒绝提现");
                    return true;
                }
            }
        }
        return false;
    }



    /**
     * 校验黑名单ip
     * @param userId
     * @param appId
     * @param productEntity
     * @param userActive
     * @param checkResult
     * @return
     */
    private boolean checkBlackIp(Long userId, Long appId, ProductEntity productEntity, UserActive userActive, CheckResult checkResult) {
        String userIpKey = RedisKeyConstants.getUserIpKey(userActive.getOs(), productEntity.getProduct(), String.valueOf(userId));
        Set<String> userIpSet = userEventJedisClusterClient.smembers(userIpKey);

        if (CollectionUtils.isEmpty(userIpSet)) {
            return false;
        }

        String blackIpKey = RedisKeyConstants.BLACK_IP;
        Set<String> blackIpSet = userEventJedisClusterClient.smembers(blackIpKey);

        if (CollectionUtils.isEmpty(blackIpSet)) {
            return false;
        }

        for (String ip : userIpSet) {
            for (String blackIp : blackIpSet) {
                if (IpUtil.isIpMatched(ip, blackIp)) {
                    log.info("用户 {} {} 校验失败:规则22-拒绝提现", appId, userId);
                    checkResult.setDirectRefund(false);
                    checkResult.setReason("校验失败:规则22-拒绝提现");
                    return true;
                }
            }
        }

        return false;
    }

    /**
     * os产品提现黑名单
     * @param os
     * @param product
     * @return
     */
    private boolean checkOsBlackProduct(String os, String product) {
        Set<String> productList = withdrawProductMap.get(os);

        if (CollectionUtils.isNotEmpty(productList)) {
            return productList.contains(product);
        }

        return false;
    }


    /**
     * ios拦截相关
     * @param userId
     * @param amount
     * @param totalAmount
     * @param appId
     * @param productEntity
     * @param userActive
     * @param checkResult
     * @return
     */
//    private boolean handlerIos(Long userId, Integer amount, Integer totalAmount, Long appId, ProductEntity productEntity, UserActive userActive, CheckResult checkResult) {
//        boolean flag = false;
//
//
//
//        return flag;
//    }

    /**
     * 激活idfa校验
     * @param userActive
     * @param checkResult
     * @param appId
     * @param userId
     * @return
     */
    private boolean checkIdfa(UserActive userActive, CheckResult checkResult, Long appId, Long userId) {
        if (StringUtils.isNotBlank(userActive.getIdfa()) && "default".equals(userActive.getIdfa())) {
            log.info("用户 {} {} 校验失败:规则21-拒绝提现", appId, userId);
            checkResult.setReason("校验失败:规则21-拒绝提现");
            checkResult.setDirectRefund(false);
            return true;
        }

        return false;
    }

    private void recordMultiUser(UserActive userActive, CheckResult checkResult, ProductEntity productEntity, Long appId, Long userId) {
        if (StringUtils.isBlank(userActive.getCaid())) {
            return;
        }

        String key = RedisKeyConstants.getMultiUserKey(userActive.getOs(), userActive.getCaid(), productEntity.getProduct());

        userEventJedisClusterClient.sadd(key, String.valueOf(userId));
        userEventJedisClusterClient.expire(key, 60 * 60 * 24 * 180);
    }

    private boolean checkBlackCaid(UserActive userActive, CheckResult checkResult, Long appId, Long userId) {
        if (StringUtils.isNotBlank(userActive.getCaid())) {
            if (userEventJedisClusterClient.exists(RedisKeyConstants.getBlackCaidKey(userActive.getCaid()))) {
                log.info("用户 {} {} 校验失败:规则96-拒绝提现", appId, userId);
                checkResult.setDirectRefund(false);
                checkResult.setReason("校验失败:规则96-拒绝提现");
                return true;
            }
        }

        return false;
    }

    private boolean checkMultiUser(UserActive userActive, CheckResult checkResult, ProductEntity productEntity, Long appId, Long userId) {

        String key = RedisKeyConstants.getMultiUserKey(userActive.getOs(), userActive.getCaid(), productEntity.getProduct());

        Long scard = userEventJedisClusterClient.scard(key);

        Integer count = 5;
        Integer groupCount = checkMultiUserGroupCountMap.get(productEntity.getProductGroup());
        if (groupCount != null) {
            count = groupCount;
        }

        if (scard > count) {
            log.info("用户 {} {} 校验失败:规则97-拒绝提现", appId, userId);
            checkResult.setDirectRefund(false);
            checkResult.setReason("校验失败:规则97-拒绝提现");
            return true;
        }

        return false;
    }


    /**
     * 十组
     *
     * @param appId
     * @param userId
     * @param amount
     * @param totalAmount
     * @param productEntity
     * @param userActive
     * @param checkResult
     * @return
     */
    private boolean handler10Group(Long appId, Long userId, Integer amount, Integer totalAmount, ProductEntity productEntity, UserActive userActive, CheckResult checkResult) {
        String product = productEntity.getProduct();
//        if ("ios".equals(userActive.getOs())) {
//            Date todayStart = DateUtil.stringToDate(DateUtil.dateToString(new Date()) + " 00:00:00", DateUtil.COMMON_TIME_FORMAT);
//            Date tomorrowStart = DateUtil.stringToDate(DateUtil.dateToString(DateUtil.dateIncreaseByDay(new Date(), 1)) + " 00:00:00", DateUtil.COMMON_TIME_FORMAT);
//            if (userActive.isZiRan() && userActive.getCreateTime().after(todayStart) && userActive.getCreateTime().before(tomorrowStart)) {
//                if (group10ZiranProductList.contains(product)) {
//                    log.info("用户 {} {} 校验失败:规则91-拒绝提现", appId, userId);
//                    checkResult.setDirectRefund(false);
//                    checkResult.setReason("校验失败:规则91-拒绝提现");
//                    return true;
//                }
//            }
//        }

        if ("android".equals(userActive.getOs())
                && userEventJedisClusterClient.exists(RedisKeyConstants.getGdtGroup10Key(product, String.valueOf(userId)))
                && totalAmount > 500) {
            log.info("用户 {} {} 项目十组GDT收入占比异常拦截", appId, userId);
            checkResult.setDirectRefund(true);
            checkResult.setReason("项目十组GDT收入占比异常拦截");
            userGrayService.sendUserGrayWithdrawReason(appId.intValue(),userId.toString(),"项目十组GDT收入占比异常拦截-拉灰");
            return true;
        }
        return false;
    }

    List<String> SgmExGroupList = Arrays.asList("项目一组", "项目十组", "项目七组");
    private boolean checkGroupSgmEx(Long appId, Long userId, Integer amount, Integer totalAmount, ProductEntity productEntity, UserActive userActive, CheckResult checkResult) {
        String product = productEntity.getProduct();

        if (!SgmExGroupList.contains(productEntity.getProductGroup())) return false;

        if ("android".equals(userActive.getOs())
                && userEventJedisClusterClient.exists(RedisKeyConstants.getSgmGroup10Key(product, String.valueOf(userId)))
                && totalAmount > 100) {
            log.info("用户 {} {} 项目一或十组收入占比异常拦截", appId, userId);
            checkResult.setDirectRefund(true);
            checkResult.setReason("项目一或十组收入占比异常拦截");
            return true;
        }

        return false;
    }
    /**
     * caid篡改校验
     * @param userActive
     * @param checkResult
     */
    private boolean checkCaid(UserActive userActive, CheckResult checkResult, Long appId, Long userId) {
        String caid = userActive.getCaid();
        String gyCaid = userActive.getGyCaid();
        String idfa = userActive.getIdfa();
        if (StringUtils.isNotBlank(gyCaid) && StringUtils.isNotBlank(caid)) {
            if (!StringUtils.equals(gyCaid, caid)) {
                log.info("用户 {} {} 校验失败:规则87-拒绝提现", appId, userId);
                checkResult.setDirectRefund(false);
                checkResult.setReason("校验失败:规则87-拒绝提现");
                return true;
            }
        }
        if (StringUtils.isNotBlank(caid) && StringUtils.isNotBlank(idfa)) {
            String reverseCaid = new StringBuilder(caid).reverse().toString();
            if (StringSimilarity.isSimilar(caid, idfa, 0.7)
                    || StringSimilarity.isSimilar(reverseCaid, idfa, 0.7)) {
                log.info("用户 {} {} 校验失败:规则88-拒绝提现", appId, userId);
                checkResult.setDirectRefund(false);
                checkResult.setReason("校验失败:规则88-拒绝提现");
                return true;
            }
        }
//        if (StringUtils.isNotBlank(gyCaid) && StringUtils.isNotBlank(idfa)) {
//            String reverseGyCaid = new StringBuilder(gyCaid).reverse().toString();
//            if (StringSimilarity.isSimilar(gyCaid, idfa, 0.7)
//                    || StringSimilarity.isSimilar(reverseGyCaid, idfa, 0.7)) {
//                log.info("用户 {} {} 校验失败:规则89-拒绝提现", appId, userId);
//                checkResult.setDirectRefund(false);
//                checkResult.setReason("校验失败:规则89-拒绝提现");
//                return true;
//            }
//        }
        return false;
    }


    private boolean checkMultiIp(Long userId, ProductEntity productEntity) {
        String product = productEntity.getProduct();
        String ip = userEventJedisClusterClient.get(RedisKeyConstants.getMultiIpCountKey("android", product, String.valueOf(userId)));
        return StringUtils.isNotBlank(ip);
    }

    private boolean checkBlackModel(String model, ProductEntity productEntity) {
        if (StringUtils.isBlank(model)) {
            return false;
        }

        return blackModelList.contains(model);
    }

    private boolean checkModel(String model, String product) {

        if (checkUserModelAppList.contains(product) || checkUserModelAppList.contains("all")) {
            if (StringUtils.isBlank(model)) {
                return false;
            }

            if (model.matches("\\d+")) {
                if (model.startsWith("202") || model.startsWith("203")) {
                    return model.length() > 9;
                }
                return true;
            }

            return false;
        }

        return false;
    }


    /**
     * 广点通服务端奖励回调异常
     * @param productEntity
     * @param userId
     * @param amount
     * @param totalAmount
     * @return
     */
    private static final List<String> rewardCallBackAbnormalProducts = Arrays.asList("qzchgr");
    private boolean checkGdtRewardCallBackAbnormal(ProductEntity productEntity, Long userId, Integer amount, Integer totalAmount) {
        String product = productEntity.getProduct();
        String productGroup = productEntity.getProductGroup();
        if (totalAmount > 200 && "项目七组".equals(productGroup)) {
            if (rewardCallBackAbnormalProducts.contains(product)) {
                return userEventJedisClusterClient.exists(RedisKeyConstants.getGdtRewardCallBackAbnormalKey(product, String.valueOf(userId)));
            }
        }
        return false;
    }

    private boolean checkNewUser1to8Ecpm(Integer amount, Integer totalAmount, Long userId, ProductEntity productEntity, String ecpm1200Num) {
//        if (Objects.equals("项目五组", productEntity.getProductGroup())) {
            String ecpm30000Num = userEventJedisClusterClient.get(RedisKeyConstants.getExposureHigh3000EcpmKey(productEntity.getProduct(), String.valueOf(userId)));
            if (totalAmount >= 3000
                    && StringUtils.isNotBlank(ecpm30000Num)
                    && Integer.parseInt(ecpm30000Num) > 50) {
                return true;
            } else {
                return false;
            }
//        }

//        return totalAmount > 1000
//                && StringUtils.isNotBlank(ecpm1200Num)
//                && Integer.parseInt(ecpm1200Num) > 5;
    }

    /**
     * gdt Gap 异常
     * @param productEntity
     * @param userId
     * @param amount
     * @param totalAmount
     * @return
     */
    private boolean checkGdtGapAbnormal(ProductEntity productEntity, Long userId, Integer amount, Integer totalAmount) {
        String productGroup = productEntity.getProductGroup();
        String product = productEntity.getProduct();
        if (!"项目七组".equals(productGroup)) {
            return userEventJedisClusterClient.exists(RedisKeyConstants.getGdtGapKey(product, String.valueOf(userId)));
        }
        return false;
    }

    //    private static final List<String> arpuIncAbNormalProducts = Arrays.asList("dcdy","fycs", "cfdd");
    private boolean checkArpuIncAbnormalAndroid(ProductEntity productEntity, Long userId, Integer amount, Integer totalAmount) {
        String product = productEntity.getProduct();
        String productGroup = productEntity.getProductGroup();
        if ("项目十组".equals(productGroup)) {
            String s = userEventJedisClusterClient.get(RedisKeyConstants.getBdArpuIncAbnormalKey(product, String.valueOf(userId)));
            return StringUtils.isNotBlank(s);
        }

        return false;
    }

    /**
     * 快手达人渠道、huwei、广点通收入异常
     * @param productEntity
     * @param userId
     * @param amount
     * @param totalAmount
     * @return
     */
    private static final List<String> ksdrHwGdtAbNormalProducts = Arrays.asList("cffk","cyww2","blgls","dcdy","cfmm","qtdd","cssb","cyww","cfdd");
    private boolean checkKsdrHwGdtAbNormal(ProductEntity productEntity, Long userId, Integer amount, Integer totalAmount) {
        String product = productEntity.getProduct();
        Integer productId = productEntity.getId();
        if (ksdrHwGdtAbNormalProducts.contains(product)) {
            String s = userEventJedisClusterClient.get(RedisKeyConstants.getKsdrHwGdtIncomeAbnormalKey(product, String.valueOf(userId)));
            return StringUtils.isNotBlank(s);
        }

        return false;
    }

    private static final List<String> nodBdProducts = Arrays.asList("qzfgys", "qzfctj");
    private boolean checkUserIncomeAbnormalNonBd(ProductEntity productEntity, Long userId, Integer amount, Integer totalAmount) {
        String productGroup = productEntity.getProductGroup();
//        if ("项目七组".equals(productGroup)) return false;
//        if ("项目七组".equals(productGroup) && totalAmount > 200) {
//            if (userEventJedisClusterClient.exists(RedisKeyConstants.getNonBaiduIncomeAbnormalGeoup7Key("android", productEntity.getProduct(), String.valueOf(userId)))) {
//                return true;
//            }
//        }
        if (enableCheckUincAbnormalNBd) {

            //五组 安卓头部产品，非百度渠道买量用户
            if ("项目五组".equals(productGroup) && bdIncAbnormalProducts.contains(productEntity.getProduct()) && totalAmount > 2000) {
                return userEventJedisClusterClient.exists(RedisKeyConstants.getNonBaiduIncomeAbnormalGroup5Key("android", productEntity.getProduct(), String.valueOf(userId)));
            }

            if (totalAmount > incAbnormalNonBdAmount) {

//            if ("项目七组".equals(productGroup) && !nodBdProducts.contains(productEntity.getProduct())) return false;
                String s = userEventJedisClusterClient.get(RedisKeyConstants.getNonBaiduIncomeAbnormalKey("android", productEntity.getProduct(), String.valueOf(userId)));
                return StringUtils.isNotBlank(s);
            }
        }
        return false;
    }

    private static final List<String> bdIncAbnormalProducts = Arrays.asList("llds", "qgcz", "ddhj2", "jymt", "dfytl", "zgde", "zzdf", "wdqcz", "ylfc", "pzds", "fkfk", "fkyf", "kcynf", "zdydq", "jcdls", "qmyzc", "hhxbt", "hffk", "hhxbt2", "tqcy");

    private boolean checkUserIncomeAbnormalAndroid(ProductEntity productEntity, Long userId, Integer amount, Integer totalAmount) {
        String productGroup = productEntity.getProductGroup();
//        if ("项目七组".equals(productGroup) && !bdIncAbnormalProducts.contains(productEntity.getProduct())) return false;

        if (totalAmount > 1000) {
            if ("项目五组".equals(productGroup) && bdIncAbnormalProducts.contains(productEntity.getProduct())) {
                String s = userEventJedisClusterClient.get(RedisKeyConstants.getBaiduIncomeAbnormalGroup5Key("android", productEntity.getProduct(), String.valueOf(userId)));
                return StringUtils.isNotBlank(s);
            }
            String s = userEventJedisClusterClient.get(RedisKeyConstants.getUserBaiduIncomeAbnormalKey("android", productEntity.getProduct(), String.valueOf(userId)));
            return StringUtils.isNotBlank(s);
        }
        return false;
    }

    /**
     * ios百度收入占比异常-拦截提现
     * @param productEntity
     * @param userId
     * @param amount
     * @param totalAmount
     * @return
     */
    private boolean checkUserIncomeAbnormalIos(ProductEntity productEntity, Long userId, Integer amount, Integer totalAmount) {
        boolean groupFlag = "项目五组".equals(productEntity.getProductGroup())
                || "项目七组".equals(productEntity.getProductGroup())
                || "项目一组".equals(productEntity.getProductGroup());
        if (groupFlag && amount > 200) {
            String s = userEventJedisClusterClient.get(RedisKeyConstants.getUserIncomeAbnormalKey("ios", productEntity.getProduct(), String.valueOf(userId)));
            return StringUtils.isNotBlank(s);
        }
        return false;
    }

    /**
     * 大额提现间隔时间异常-拦截提现
     *
     * @param appId
     * @param userId
     * @param amount
     * @param os
     * @return
     */
    private boolean shouldInterceptByContinuousWithdraw(Long appId, Long userId, Integer amount, String os) {
        // 规则是否启用
        if (!enableContinuousWithdraw) return false;

        ProductEntity productEntity = AppConfig.appIdMap.get(appId);
        if (productEntity == null) {
            log.error("AppId {} {} 未查询到产品配置", appId, userId);
            return false;
        }
        String product = productEntity.getProduct();
        String productGroup = productEntity.getProductGroup();

        boolean check5And7Group = Objects.equals("项目五组", productEntity.getProductGroup());
        // redis key
        String key;
        // 间隔时间阈值
        int timeThreshold;
        // 提现金额阈值
        int amountThreshold;
        // 提现次数阈值
        int countThreshold;

//        if (check5And7Group) {
//            if (amount > 1000) {
//                key = RedisKeyConstants.getContinuousWithdraw1000amountKey(product, os, userId);
//                timeThreshold = 30 * 60; // 30分钟
//                amountThreshold = 1000;
//                countThreshold = 2;
//            } else if (amount > 500) {
//                key = RedisKeyConstants.getContinuousWithdraw500amountKey(product, os, userId);
//                timeThreshold = 10 * 60; // 10分钟
//                amountThreshold = 500;
//                countThreshold = 2;
//            } else {
//                return false;
//            }
//
//            // 五组改为3次
//            if (Objects.equals("项目五组", productEntity.getProductGroup())) countThreshold = 3;
//
//        } else

        if (Objects.equals("项目十组", productGroup) || Objects.equals("项目七组", productGroup)
                || Objects.equals("项目五组", productEntity.getProductGroup())) {
            if (amount > 1000) {
                key = RedisKeyConstants.getContinuousWithdraw1000amountKey(product, os, userId);
                timeThreshold = 2 * 60; // 2分钟
                amountThreshold = 1000;
                countThreshold = 3;
            } else {
                return false;
            }
        } else {
            return false;
        }

        long currentTime = System.currentTimeMillis();
        // 先查redis里是否存在记录
        Long existNum = userEventJedisClusterClient.zcard(key);
        if (existNum == null || existNum == 0) {
            userEventJedisClusterClient.zadd(key, currentTime, amount + ":" + currentTime);
            // 设置过期时间
            userEventJedisClusterClient.expire(key, timeThreshold);
        } else {
            userEventJedisClusterClient.zadd(key, currentTime, amount + ":" + currentTime);
        }

        // 获取指定时间范围内的记录
        Set<String> records = userEventJedisClusterClient.zrangeByScore(key, currentTime - timeThreshold * 1000, currentTime);
        if (records == null || records.size() < countThreshold) {
            return false;
        }
        log.info("用户提现审核 满足提现次数阈值&时间范围内的记录 {} {}",key , records);
        int count = 0;
        for (String record : records) {
            int recordAmount = Integer.parseInt(record.split(":")[0]);
            if (recordAmount >= amountThreshold) {
                count++;
                if (count >= countThreshold) {
                    return true;
                }
            }
        }

        return false;
    }

    private boolean checkIOSNoDist(Long appId, Long userId, Integer amount, UserActive userActive, ProductEntity productEntity) {
        Calendar yesterCalendar = Calendar.getInstance();
        yesterCalendar.set(
                yesterCalendar.get(Calendar.YEAR),
                yesterCalendar.get(Calendar.MONTH),
                yesterCalendar.get(Calendar.DAY_OF_MONTH) - 1,
                0,
                0,
                0
        );
        if (userActive.getCreateTime().compareTo(yesterCalendar.getTime()) >= 0 //近两个自然日新增
                && productEntity != null){
            if(userDeviceCheckService.checkNoDist(userId, appId, amount, productEntity, "ios")){
                return true;
            }
        }
        return false;
    }

    private boolean handlerNature(Long appId, Long userId, Integer totalAmount, UserActive userActive, boolean flag, CheckResult checkResult, ProductEntity productEntity, Integer amount) {
        if(flag && "android".equalsIgnoreCase(userActive.getOs()) && newNatureLimitService.isChannelLimit(appId, userActive.getChannel())) {
            log.info("用户 {} {} 中台产品自然量提现channel受限 拒绝提现 {}",appId,userId,userActive.getChannel());
            flag = false;
            checkResult.setDirectRefund(false);
            checkResult.setReason("中台产品自然量提现channel异常拦截-拒绝提现");
            return flag;
        }

        String yesterdayBegin = DateUtil.dateToString(DateUtil.dateIncreaseByDay(new Date(), -1))+ " 00:00:00";
        Date yesterday = DateUtil.stringToDate(yesterdayBegin,DateUtil.COMMON_TIME_FORMAT);
        Integer yesterdayAmount = userDeviceCheckService.getUserYesterdayWithdraw(appId, userId);
        if ((totalAmount + yesterdayAmount) > 1000 && userActive.getCreateTime().after(yesterday)) {
            if(checkEX(appId, userId, totalAmount, yesterdayAmount, productEntity)){
                log.info("用户 {} {} 自然量新增累计提现过高 拒绝提现", appId, userId);
                flag = false;
                checkResult.setDirectRefund(false);
                checkResult.setReason("自然量新增累计提现过高-拒绝提现");
                return flag;
            }
        }

        boolean checkGroup7Nature = Objects.equals("项目七组", productEntity.getProductGroup());
//        if (checkGroup7Nature &&  totalAmount > 100) {
//            String highEcpmFlag = userEventJedisClusterClient.get(RedisKeyConstants.getExposureHigh2000EcpmKey(productEntity.getProduct(), String.valueOf(userId)));
//            if (StringUtils.isNotBlank(highEcpmFlag) && Integer.valueOf(highEcpmFlag) > 3) {
//                log.info("用户 {} {} 七组活跃用户高ecpm自然量提现 拒绝提现", appId, userId);
//                flag = false;
//                checkResult.setDirectRefund(false);
//                checkResult.setReason("七组活跃用户高ecpm自然量提现-拒绝提现");
//                return flag;
//            }
//        }

        if (Objects.equals("项目五组", productEntity.getProductGroup())) {
            flag = handler5Group(appId, userId, totalAmount, flag, checkResult, productEntity);
            if(!flag){
                return flag;
            }
        }

        Date today = DateUtil.stringToDate(DateUtil.dateToString(new Date()) + " 00:00:00",DateUtil.COMMON_TIME_FORMAT);

        //累计体现大于1
        try{
//            boolean group5ForZiranCallBackFlag = amount > 200 && Objects.equals("项目五组", productEntity.getProductGroup());
            if (totalAmount + yesterdayAmount > 100 && !"项目五组".equals(productEntity.getProductGroup())) {
                String beforeDayStr = DateUtil.dateToStringWithTime(DateUtil.dateIncreaseByDay(new Date(), -2));
                Date beforeDay = DateUtil.stringToDate(beforeDayStr, DateUtil.COMMON_TIME_FORMAT);
                //近2天用户
                if(userActive.getCreateTime().after(beforeDay)){
                    //没有第三方广告回调
                    if(!checkAdCallBack(appId, userId, new Date[]{beforeDay, yesterday, today})){
                        log.info("[中台拦截]测试 近2天的安卓新增自然量用户近2天无第三方广告回调 延迟提现 appId：{} userId : {}",appId,userId);
                        flag = false;
                        checkResult.setDirectRefund(false);
                        checkResult.setReason("[中台拦截]安卓新增自然量用户提现1元无广告回调-延迟提现");
//                        if (group5ForZiranCallBackFlag) {
//                            checkResult.setReason("[中台拦截]五组安卓新增自然量用户提现2元无广告回调-延迟提现");
//                        }
                        return flag;
                    }
                }
            }
        }catch (Exception e){
            log.error("近2天的安卓新增自然量用户近2天无第三方广告回调 异常 appId：{} userId : {} e {}" ,appId, userId, e );
        }

        //累计体现大于0.8
        if (totalAmount + yesterdayAmount > 80) {
            try{
                String beforeDayStr = DateUtil.dateToStringWithTime(DateUtil.dateIncreaseByDay(new Date(), -3));
                Date beforeDay = DateUtil.stringToDate(beforeDayStr, DateUtil.COMMON_TIME_FORMAT);
                //近3天用户
                if(userActive.getCreateTime().after(beforeDay)){
                    //没有第三方广告回调
                    if(checkNoReward(productEntity.getProduct(), userId)){
                        if(checkNoHuoShan(productEntity.getProduct(), userId)){
                            log.info("[中台拦截]测试 近3天的安卓新增自然量用户近3天无reward事件无火山 延迟提现 product : {} userId : {}",productEntity.getProduct(),userId);
                        }
                    }
                }
            }catch (Exception e){
                log.error("近3天的安卓新增自然量用户近3天无reward事件无火山  异常 product : {} userId : {} e {}",productEntity.getProduct(), userId , e );
            }
        }
        //首笔提现大于0.8
        boolean checkGroup5FirstAdCallBackFlag = amount >= 2000 && amount.equals(totalAmount) && yesterdayAmount == 0 && Objects.equals("项目五组", productEntity.getProductGroup());
        if((amount >= 80 && amount == totalAmount && yesterdayAmount == 0) || checkGroup5FirstAdCallBackFlag){
            String beforeDayStr = DateUtil.dateToStringWithTime(DateUtil.dateIncreaseByDay(new Date(), -2));
            Date beforeDay = DateUtil.stringToDate(beforeDayStr, DateUtil.COMMON_TIME_FORMAT);
            //近2天用户
            if(userActive.getCreateTime().after(beforeDay)){
                if(!checkAdCallBack(appId, userId, new Date[]{beforeDay, yesterday, today})){
                    log.info("[中台拦截]测试 近两天安卓新增自然量用户首次提现超过0.8元且无第三方广告回调 延迟提现 product : {} userId : {} amount : {} ",productEntity.getProduct(),userId, amount);
                    flag = false;
                    checkResult.setDirectRefund(false);
                    checkResult.setReason("[中台拦截]新增自然量首次提现超过0.8且无三方回调-延迟提现");
                    if (checkGroup5FirstAdCallBackFlag) {
                        checkResult.setReason("[中台拦截]五组新增自然量首次提现超过2元且无三方回调-延迟提现");
                    }
                    return flag;
                }
            }
        }

        //当日用户
        if(!userActive.getCreateTime().after(today)){
            return true;
        }


        String reason = "";
//        if (checkGroup7Nature) {
//            //指定的app加了游戏校验，不走中台校验
//            if (totalAmount > 200) {
//                String highEcpmNum = userEventJedisClusterClient.get(RedisKeyConstants.getExposureHighEcpmKey(productEntity.getProduct(), String.valueOf(userId)));
//                if(StringUtils.isNotBlank(highEcpmNum) && Integer.valueOf(highEcpmNum) > 5){
//                    log.info("用户 {} {} 七组全部安卓产品新增自然量首日提现2元拦截 pv大于5", appId, userId);
//                    reason = "七组全部安卓产品新增自然量首日提现2元拦截";
//                }else{
//                    log.info("用户 {} {} 七组全部安卓产品新增自然量首日提现2元拦截 正常提现", appId, userId);
//                }
//            }
//            if (totalAmount > 100) {
//                String highEcpmNum = userEventJedisClusterClient.get(RedisKeyConstants.getExposureHighEcpmKey(productEntity.getProduct(), String.valueOf(userId)));
//                if (StringUtils.isNotBlank(highEcpmNum) && Integer.valueOf(highEcpmNum) > 3) {
//                    reason = "七组高ecpm自然量提现";
//                }
//            }
//        }
//        else if (checkZrAppList.contains(appId)) {
//            reason = "五组部分安卓产品新增自然量首日提现2元拦截";
//        }
//        if (Objects.equals("项目五组", productEntity.getProductGroup()) &&  totalAmount > 200) {
//            if(
//                appId == 1360L || StringUtils.equals(productEntity.getProduct(),"dfdg")
//            || appId == 1367L || StringUtils.equals(productEntity.getProduct(),"ylfc")
//            || appId == 1377L || StringUtils.equals(productEntity.getProduct(),"asqc")
//            || appId == 1309L || StringUtils.equals(productEntity.getProduct(),"zcjb")
//            || appId == 1351L || StringUtils.equals(productEntity.getProduct(),"zcjb2")
//            || appId == 810L || StringUtils.equals(productEntity.getProduct(),"ywrs")
//            || appId == 1308L || StringUtils.equals(productEntity.getProduct(),"qyjs")
//            ){
//                String high2000EcpmNum = userEventJedisClusterClient.get(RedisKeyConstants.getExposureHigh2000EcpmKey(productEntity.getProduct(), String.valueOf(userId)));
//                if (totalAmount > 500 && StringUtils.isNotBlank(high2000EcpmNum) && Integer.valueOf(high2000EcpmNum) > 10) {
//                    log.info("五组高ecpm自然量提现 特殊 productEntity : {} userId : {}", productEntity, userId);
//                    reason = "五组高ecpm自然量提现";
//                }
//            }else{
//                String highEcpmNum = userEventJedisClusterClient.get(RedisKeyConstants.getExposureHighEcpmKey(productEntity.getProduct(), String.valueOf(userId)));
//                if (StringUtils.isNotBlank(highEcpmNum) && Integer.valueOf(highEcpmNum) >= 5) {
//                    reason = "五组高ecpm自然量提现";
//                }
//            }
//        }

        String high2000EcpmNum = userEventJedisClusterClient.get(RedisKeyConstants.getExposureHigh2000EcpmKey(productEntity.getProduct(), String.valueOf(userId)));
        if (Objects.equals("项目五组", productEntity.getProductGroup())) {
            if (totalAmount > 500 && StringUtils.isNotBlank(high2000EcpmNum) && Integer.valueOf(high2000EcpmNum) > 10) {
                log.info("五组高ecpm自然量提现 特殊 productEntity : {} userId : {}", productEntity, userId);
                reason = "五组高ecpm自然量提现";
            }
        }

        if (StringUtils.isNotBlank(reason)) {
            log.info("用户 {} {} {} 拒绝提现", appId, userId,reason);
            flag = false;
            checkResult.setDirectRefund(false);
            checkResult.setReason(reason+"-拒绝提现");
            return flag;
        }

        if (newNatureLimitService.check(appId, totalAmount)) {
            reason = "产品配置新增自然量提现拦截";
            log.info("用户 {} {} {} 拒绝提现",appId,userId,reason);
            flag = false;
            checkResult.setDirectRefund(false);
            checkResult.setReason("[中台拦截]产品配置新增自然量提现拦截-拒绝提现");
            return flag;
        }


        return flag;
    }

    static List<Integer> checkAppList = Arrays.asList(1528,1486,1375,1524);
    private static boolean checkEX(Long appId, Long userId, Integer totalAmount, Integer yesterdayAmount, ProductEntity productEntity) {

        if (Objects.equals("项目五组", productEntity.getProductGroup()) && (totalAmount + yesterdayAmount) < 5000) {
            log.info("用户 {} {} 五组自然量新增累计提现过高 特殊处理", appId, userId);
            return false;
        }

        if (checkAppList.contains(appId.intValue()) && (totalAmount + yesterdayAmount) < 4000) {
            log.info("用户 {} {} 五组多福一条龙，金玉满堂，连连大顺，我是首富自然量新增累计提现过高 拒绝提现", appId, userId);
            return false;
        }


        if(appId == 1309
            || appId == 1351
            || appId == 1360
        ){
            if((totalAmount + yesterdayAmount) <= 2500){
                log.info("用户 {} {} 自然量新增累计提现过高 招财进宝 特殊处理", appId, userId);
                return false;
            }
        }
        if(appId == 810
            || appId == 1339
            || appId == 1367
        ){
            if((totalAmount + yesterdayAmount) <= 2000){
                log.info("用户 {} {} 自然量新增累计提现过高 招财进宝 特殊处理", appId, userId);
                return false;
            }
        }
        // 七组八方来财 麻友解压馆20块拦截
        if (appId == 1386 || appId == 1080) {
            if((totalAmount + yesterdayAmount) < 2000){
                log.info("用户 {} {} 自然量新增累计提现过高 特殊处理", appId, userId);
                return false;
            }
        }
        if(Objects.equals("项目十组", productEntity.getProductGroup()) || Objects.equals("项目七组", productEntity.getProductGroup())){
            if((totalAmount + yesterdayAmount) <= 10000){
                log.info("用户 {} {} 十组自然量新增累计提现过高 特殊处理", appId, userId);
                return false;
            }
        }
        return true;
    }

    private boolean handler5Group(Long appId, Long userId, Integer totalAmount, boolean flag, CheckResult checkResult, ProductEntity productEntity) {
//        if (appId == 1448L || StringUtils.equals(productEntity.getProduct(), "gbzw")
//                || appId == 1426L || StringUtils.equals(productEntity.getProduct(),"hhyb")
//                || appId == 1435L || StringUtils.equals(productEntity.getProduct(),"ddhj2")
//                || appId == 1431L || StringUtils.equals(productEntity.getProduct(),"hffk")
//                || appId == 1324L || StringUtils.equals(productEntity.getProduct(),"jfcx")
//                || appId == 1405L || StringUtils.equals(productEntity.getProduct(),"pzds")
//                || appId == 1449L || StringUtils.equals(productEntity.getProduct(),"yszbc")
//                || appId == 1285L || StringUtils.equals(productEntity.getProduct(),"wdqcz")
//
//        ) {
//            String highEcpmFlag2000 = userEventJedisClusterClient.get(
//                    RedisKeyConstants.getExposureHigh2000EcpmKey(productEntity.getProduct(), String.valueOf(userId))
//            );
//            if (totalAmount > 500
//                    && StringUtils.isNotBlank(highEcpmFlag2000)
//                    && Integer.parseInt(highEcpmFlag2000) > 10) {
//                log.info("用户 {} {} 五组活跃用户高ecpm自然量提现 拒绝提现 特殊处理 pv大于10,超过5块 ", appId, userId);
//                flag = false;
//                checkResult.setDirectRefund(false);
//                checkResult.setReason("五组活跃用户高ecpm自然量提现-拒绝提现");
//            }
//            return flag;
//        }
//
//        if(totalAmount > 100){
//            String highEcpmNum = userEventJedisClusterClient.get(RedisKeyConstants.getExposureHighEcpmKey(productEntity.getProduct(), String.valueOf(userId)));
//            if(
//                    appId == 1309L || StringUtils.equals(productEntity.getProduct(),"zcjb")
//                || appId == 1360L || StringUtils.equals(productEntity.getProduct(),"dfdg")
//                || appId == 1367L || StringUtils.equals(productEntity.getProduct(),"ylfc")
//                || appId == 1351L || StringUtils.equals(productEntity.getProduct(),"zcjb2")
//                || appId == 810L || StringUtils.equals(productEntity.getProduct(),"ywrs")
//                || appId == 1308L || StringUtils.equals(productEntity.getProduct(),"qyjs")
//                || appId == 1377L || StringUtils.equals(productEntity.getProduct(),"asqc")
//                || appId == 1409L || StringUtils.equals(productEntity.getProduct(),"hhyc")
//                || appId == 1411L || StringUtils.equals(productEntity.getProduct(),"hhfg")
//                || appId == 1422L || StringUtils.equals(productEntity.getProduct(),"hqsc")
//                || appId == 1423L || StringUtils.equals(productEntity.getProduct(),"rssf")
//            ){//特殊处理
//                if(totalAmount > 300){
//                    String highEcpmFlag2000 = userEventJedisClusterClient.get(
//                            RedisKeyConstants.getExposureHigh2000EcpmKey(productEntity.getProduct(), String.valueOf(userId))
//                    );
//                    if(
//                        appId == 1309L || StringUtils.equals(productEntity.getProduct(),"zcjb")
//                        || appId == 1351L || StringUtils.equals(productEntity.getProduct(),"zcjb2")
//                        || appId == 1367L || StringUtils.equals(productEntity.getProduct(),"ylfc")
//                        || appId == 1360L || StringUtils.equals(productEntity.getProduct(),"dfdg")
//                            || appId == 1409L || StringUtils.equals(productEntity.getProduct(),"hhyc")
//                            || appId == 1411L || StringUtils.equals(productEntity.getProduct(),"hhfg")
//                            || appId == 1422L || StringUtils.equals(productEntity.getProduct(),"hqsc")
//                            || appId == 1423L || StringUtils.equals(productEntity.getProduct(),"rssf")
//                    ){
//                        if (
//                                totalAmount > 2000
//                            && StringUtils.isNotBlank(highEcpmFlag2000)
//                            && Integer.valueOf(highEcpmFlag2000) > 10
//                        ){
//                            log.info("用户 {} {} 五组活跃用户高ecpm自然量提现 拒绝提现 特殊处理 pv大于10", appId, userId);
//                            flag = false;
//                            checkResult.setDirectRefund(false);
//                            checkResult.setReason("五组活跃用户高ecpm自然量提现-拒绝提现");
//                        }
//                    }
//                    else if (
//                        StringUtils.isNotBlank(highEcpmFlag2000)
//                        && Integer.valueOf(highEcpmFlag2000) > 3
//                    ) {
//                        log.info("用户 {} {} 五组活跃用户高ecpm自然量提现 拒绝提现 特殊处理 pv大于3", appId, userId);
//                        flag = false;
//                        checkResult.setDirectRefund(false);
//                        checkResult.setReason("五组活跃用户高ecpm自然量提现-拒绝提现");
//                    }
//                }
//            }
//        }

        // 5组高ecpm自然量提现
        String highEcpmFlag2000 = userEventJedisClusterClient.get(RedisKeyConstants.getExposureHigh2000EcpmKey(productEntity.getProduct(), String.valueOf(userId)));
       if (StringUtils.isNotBlank(highEcpmFlag2000) && Integer.parseInt(highEcpmFlag2000) > 10 && totalAmount > 1500) {
            log.info("用户 {} {} 五组活跃用户高ecpm自然量提现 拒绝提现", appId, userId);
            flag = false;
            checkResult.setDirectRefund(false);
            checkResult.setReason("五组活跃用户高ecpm自然量提现-拒绝提现");
        }

        return flag;
    }

    private boolean checkNoHuoShan(String product, Long userId) {
        String postUrl = "http://sapi.shinet-inc.com/sf/rk/checkExists";
        Map<String,Object> req = new HashMap<>();
        req.put("product", product);
        req.put("dayCount", 4);
        req.put("userId", userId);
        String result = HttpClients.POST(postUrl, req);
        log.info(" boolean checkNoHuoShan(String product, Long userId) req {}: , res {}", req, result);
        return !JSONObject.parseObject(result).getBoolean("data");
    }

    public boolean checkNoReward(String product, Long userId) {
        return !userEventJedisClusterClient.exists("{risk:reward}:"+ userId + ":" + product);
    }

    public int getCallBakCount(String product, Long userId) {
        String co = userEventJedisClusterClient.hget("{risk:reward}:" + userId + ":" + product, "co");
        if(!StringUtils.isNumeric(co)){
            return 0;
        }
        return Integer.valueOf(co);
    }

    private boolean checkAdCallBack(Long appId, Long userId, Date[] dates) {
        try{
            return userAdExposureCheckService.checkExitsThirdCallBack(appId, userId, dates);
        }catch (Exception e){
            log.error(" userAdExposureCheckService.checkThirdCallBack error {}", e);
            return true;
        }
    }

    @Override
    public Boolean passDelayOrder(String oderNo,String op) {
        log.info("OP {} 操作订单 {} 为可发放状态",op,oderNo);
        return withdrawNotSendService.updateOrderToCanSend(oderNo, false);
    }

    @Override
    public Long queryUserEventCount(Long appId, Long userId) {
        UserEventCountHBaseBean userEventCountHBaseBean = hBaseUserEventCountService.queryUserEventCount(appId,userId);
        return userEventCountHBaseBean.getEventCount();
    }
}
