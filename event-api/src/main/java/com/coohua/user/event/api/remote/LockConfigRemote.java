package com.coohua.user.event.api.remote;

import com.alibaba.fastjson.JSON;
import com.coohua.user.event.api.dto.UserActiveResp;
import com.coohua.user.event.api.remote.rpc.LockConfigRpc;
import com.coohua.user.event.biz.click.service.ClickHouseService;
import com.coohua.user.event.biz.service.TFUserService;
import com.coohua.user.event.biz.util.Strings;
import com.weibo.api.motan.config.springsupport.annotation.MotanService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2023/8/31
 */
@Slf4j
@Service
@MotanService(basicService = "user-eventBasicServiceConfigBean")
public class LockConfigRemote implements LockConfigRpc {

    @Autowired
    private ClickHouseService clickHouseService;
    @Autowired
    private TFUserService tfUserService;

    @Override
    public List<Integer> queryNeedCheckAreaConfig() {
        return clickHouseService.queryConfigAreaProduct();
    }

    @Override
    public UserActiveResp queryActiveByCaid(String product, String caid) {
        try {
            String result = tfUserService.queryByCaid(product,caid);
            if (Strings.noEmpty(result)){
                return JSON.parseObject(result,UserActiveResp.class);
            }
        }catch (Exception e){
            log.error("Query UserActive Er:",e);
        }
        return null;
    }
}
