package com.coohua.user.event.api.controller;

import com.coohua.user.event.biz.core.dto.rsp.ReturnResult;
import com.coohua.user.event.biz.toufang.service.ProductLockService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 * @since 2022/2/16
 */
@Slf4j
@RequestMapping("area")
@RestController
public class LockAreaConfigController {

    @Autowired
    private ProductLockService productLockService;

    @GetMapping(value = "getConfig",produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    public ReturnResult queryRunConfigs(){
        ReturnResult returnResult = new ReturnResult();
        returnResult.setData(productLockService.queryProductConfig());
        return returnResult;
    }
}
