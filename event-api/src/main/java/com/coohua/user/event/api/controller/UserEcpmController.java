package com.coohua.user.event.api.controller;

import com.coohua.user.event.api.dto.QueryRequest;
import com.coohua.user.event.biz.core.dto.rsp.ReturnResult;
import com.coohua.user.event.biz.service.HBaseUserAdViewService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

/**
 * <AUTHOR>
 * @since 2021/6/25
 */
@Slf4j
@RestController
@RequestMapping("ad")
public class UserEcpmController {

    @Autowired
    private HBaseUserAdViewService hBaseUserAdViewService;

    @PostMapping("ecpm/queryBatch")
    public ReturnResult queryBatch(@RequestBody QueryRequest request){
        return new ReturnResult(hBaseUserAdViewService.batchQuery(request.getUserIdList(),request.getOs(),request.getAppId()));
    }


}
