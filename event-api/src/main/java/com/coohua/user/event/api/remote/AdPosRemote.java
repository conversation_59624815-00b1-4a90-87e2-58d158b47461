package com.coohua.user.event.api.remote;

import com.coohua.user.event.api.dto.AdFxConfigResponse;
import com.coohua.user.event.api.dto.ExceptionAdPos;
import com.coohua.user.event.api.dto.ProductEntity;
import com.coohua.user.event.api.remote.rpc.AdPosRpc;
import com.coohua.user.event.biz.click.entity.ExceptionAdPosEntity;
import com.coohua.user.event.biz.click.service.ClickHouseService;
import com.coohua.user.event.biz.core.entity.AdFxConfig;
import com.coohua.user.event.biz.core.service.AdFxConfigService;
import com.weibo.api.motan.config.springsupport.annotation.MotanService;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2021/9/22
 */
@Service
@MotanService(basicService = "user-eventBasicServiceConfigBean")
public class AdPosRemote implements AdPosRpc {

    @Autowired
    private ClickHouseService clickHouseService;
    @Autowired
    private AdFxConfigService adFxConfigService;

    @Override
    public List<ProductEntity> queryProductDict() {
        return clickHouseService.queryAllProduct().stream().map(r ->{
            ProductEntity productEntity = new ProductEntity();
            productEntity.setId(r.getId());
            productEntity.setProduct(r.getProduct());
            productEntity.setProductName(r.getProductName());
            productEntity.setProductGroup(r.getProductGroup());
            return productEntity;
        }).collect(Collectors.toList());
    }

    @Override
    public List<ExceptionAdPos> queryExceptionAds(String queryDate, String typeName, Integer lowPrice,Integer rate) {
        List<ExceptionAdPosEntity> exceptionAdPosEntities = clickHouseService.queryExceptionList(queryDate,typeName,lowPrice,rate);
        return exceptionAdPosEntities.stream().map(this::convertAd).collect(Collectors.toList());
    }

    @Override
    public List<ExceptionAdPos> queryExceptionAdsSumIncomeLimit(String queryDate, String typeName, Integer limitIncome) {
        List<ExceptionAdPosEntity> exceptionAdPosEntities = clickHouseService.queryExceptionList(queryDate,typeName,limitIncome);
        return exceptionAdPosEntities.stream().map(this::convertAd).collect(Collectors.toList());
    }

    @Override
    public List<AdFxConfigResponse> queryFxAdConfigAll() {
        List<AdFxConfig> results = adFxConfigService.queryStateInitAll();
        return results.stream().map(this::convert).collect(Collectors.toList());
    }

    private AdFxConfigResponse convert(AdFxConfig adFxConfig){
        AdFxConfigResponse adFxConfigResponse = new AdFxConfigResponse();
        BeanUtils.copyProperties(adFxConfig,adFxConfigResponse);
        return adFxConfigResponse;
    }

    private ExceptionAdPos convertAd(ExceptionAdPosEntity exceptionAdPosEntity){
        ExceptionAdPos exceptionAdPos = new ExceptionAdPos();
        exceptionAdPos.setAdId(exceptionAdPosEntity.getAdId());
        exceptionAdPos.setAdType(exceptionAdPosEntity.getAdType());
        exceptionAdPos.setAppId(exceptionAdPosEntity.getAppId());
        exceptionAdPos.setPosId(exceptionAdPosEntity.getPosId());
        exceptionAdPos.setPosName(exceptionAdPosEntity.getPosName());
        exceptionAdPos.setProduct(exceptionAdPosEntity.getProduct());
        exceptionAdPos.setProductName(exceptionAdPosEntity.getProductName());
        exceptionAdPos.setTypeName(exceptionAdPosEntity.getTypeName());
        exceptionAdPos.setRate(exceptionAdPosEntity.getRate());
        exceptionAdPos.setIncome(exceptionAdPosEntity.getIncome());
        exceptionAdPos.setOs(exceptionAdPosEntity.getOs());
        exceptionAdPos.setLogday(exceptionAdPosEntity.getLogday());
        exceptionAdPos.setSourceName(exceptionAdPosEntity.getSourceName());
        return exceptionAdPos;
    }
}
