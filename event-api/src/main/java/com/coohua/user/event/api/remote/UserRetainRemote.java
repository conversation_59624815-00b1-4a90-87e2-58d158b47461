package com.coohua.user.event.api.remote;

import com.coohua.user.event.api.remote.rpc.UserRetainRpc;
import com.coohua.user.event.biz.click.service.ClickHouseService;
import com.coohua.user.event.biz.util.Lists;
import com.weibo.api.motan.config.springsupport.annotation.MotanService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2021/12/28
 */
@Service
@MotanService(basicService = "user-eventBasicServiceConfigBean")
public class UserRetainRemote implements UserRetainRpc {

    @Autowired
    private ClickHouseService clickHouseService;

    @Override
    public Integer countRetainUser(List<Long> userIdList, Integer appId) {
        if (Lists.isEmpty(userIdList) || appId == null){
            return 0;
        }
        return clickHouseService.queryRetainRelation(userIdList,appId);
    }
}
