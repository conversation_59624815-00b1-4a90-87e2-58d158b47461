package com.coohua.user.event.api.remote;

import com.alibaba.fastjson.JSON;
import com.coohua.user.event.api.dto.PfQueryRequest;
import com.coohua.user.event.api.dto.PfResponse;
import com.coohua.user.event.api.remote.rpc.TtPfRpc;
import com.coohua.user.event.biz.service.TfPfService;
import com.coohua.user.event.biz.service.bean.TfPfAdBean;
import com.coohua.user.event.biz.service.bean.TfPfUserBean;
import com.coohua.user.event.biz.util.Strings;
import com.weibo.api.motan.config.springsupport.annotation.MotanService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2024/2/2
 */
@Slf4j
@Service
@MotanService(basicService = "user-eventBasicServiceConfigBean")
public class PfQueryRemote implements TtPfRpc {

    @Autowired
    private TfPfService tfPfService;

    @Override
    public List<PfResponse> queryPfAd(PfQueryRequest request) {
        log.info("Query TfPf Request {}", JSON.toJSONString(request));
        if (Strings.isEmpty(request.getLogday())){
            throw new RuntimeException("日期不能为空");
        }

        // 查询待暂停计划
        List<TfPfAdBean> tfPfAdBeans = tfPfService.queryWaitPfAd(request.getLogday(),request.getProduct());
        if (tfPfAdBeans == null || tfPfAdBeans.size() == 0){
            return new ArrayList<>();
        }
        // 查人群
        List<String> adIds = tfPfAdBeans.stream().map(r-> r.getAdId().toString()).collect(Collectors.toList());
        List<TfPfUserBean> tfPfUserBeans = tfPfService.queryWaitPfUser(adIds,request.getLogday());
        if (tfPfUserBeans == null || tfPfUserBeans.size() == 0){
            return new ArrayList<>();
        }

        Map<Long,List<TfPfUserBean>> trMap = tfPfUserBeans.stream().collect(Collectors.groupingBy(TfPfUserBean::getAdId));
        List<PfResponse> responses =  tfPfAdBeans.stream().map(tfPfAdBean -> {
            PfResponse pfResponse = new PfResponse();
            pfResponse.setAdId(tfPfAdBean.getAdId());
            pfResponse.setProductName(tfPfAdBean.getProductName());
            pfResponse.setProduct(tfPfAdBean.getProduct());
            pfResponse.setAdvertiserId(tfPfAdBean.getAdvertiserId());

            Map<Long, PfResponse.UserPv> rsMap = new HashMap<>();
            List<TfPfUserBean> tfPfUserBeanList = trMap.get(tfPfAdBean.getAdId());
            // Arpu 倒序
            tfPfUserBeanList = tfPfUserBeanList.stream()
                    .sorted((r1,r2) -> r2.getArpu().compareTo(r1.getArpu()))
                    .collect(Collectors.toList());
            for (TfPfUserBean userBean:tfPfUserBeanList){
                // 符合人群截断
                if (rsMap.size() < tfPfAdBean.getNeedReturnUser()) {
                    PfResponse.UserPv pv = new PfResponse.UserPv();
                    pv.setArpu(userBean.getArpu());
                    pv.setPv(userBean.getSpPv());
                    rsMap.put(userBean.getUserId(), pv);
                }
            }
            pfResponse.setNeedSendUserMap(rsMap);
            return pfResponse;
        }).collect(Collectors.toList());

        log.info("Get PF Result Size {}",responses.size());
        return responses;
    }
}
