package com.coohua.user.event.api.controller;

import com.alibaba.fastjson.JSONObject;
import com.coohua.bp.user.remote.api.UserRPC;
import com.coohua.bp.user.remote.dto.CommonHeaderDTO;
import com.coohua.bp.user.remote.dto.UserDTO;
import com.coohua.user.event.api.req.CheckUserLogReq;
import com.coohua.user.event.biz.click.service.ClickHouseService;
import com.coohua.user.event.biz.core.dto.req.UploadEventReq;
import com.coohua.user.event.biz.core.dto.rsp.ReturnResult;
import com.coohua.user.event.biz.core.mapper.UserEventCountMapper;
import com.coohua.user.event.biz.core.service.UserEventLogService;
import com.coohua.user.event.biz.core.service.UserEventService;
import com.coohua.user.event.biz.service.DailyResultService;
import com.coohua.user.event.biz.service.HBaseUserEventCountService;
import com.coohua.user.event.biz.util.AppDrawConf;
import com.coohua.user.event.biz.util.Objects;
import com.coohua.user.event.biz.util.Strings;
import com.weibo.api.motan.config.springsupport.annotation.MotanReferer;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.MediaType;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;

@Slf4j
@RestController
@RequestMapping("/event/")
public class CoreEventController {

    @Autowired
    private UserEventService userEventService;
    @Autowired
    private UserEventLogService userEventLogService;
    @Autowired
    private DailyResultService dailyResultService;
    @Autowired
    private ClickHouseService clickHouseService;
    @Resource
    private UserEventCountMapper userEventCountMapper;
    @MotanReferer(basicReferer = "bp-userBasicRefererConfigBean")
    private UserRPC userRPC;
    @Autowired
    private HBaseUserEventCountService hBaseUserEventCountService;
    @Value("${use.hbase.query.switch:false}")
    private Boolean useHBaseQuery;


    @PostMapping(value = "uploadEvent",produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    @ResponseBody
    public ReturnResult uploadUserInfo(UploadEventReq req){
        ReturnResult returnResult = new ReturnResult();
        if (Objects.isNull(req)){
            returnResult.setMessage("参数非法");
            returnResult.setStatus(400);
            return returnResult;
        }
        if (Strings.isEmpty(req.getAppId()) || Strings.isEmpty(req.getDeviceId()) || Strings.isEmpty(req.getOs())){
            returnResult.setMessage("参数非法");
            returnResult.setStatus(400);
            return returnResult;
        }
        CommonHeaderDTO commonHeaderDTO = new CommonHeaderDTO();
        BeanUtils.copyProperties(req, commonHeaderDTO);
        UserDTO userDTO = null;
        try {
            userDTO = userRPC.getUserInfo(commonHeaderDTO, req.getUserId());
        }catch (Exception e){
            log.error("Request User Ex：", e);
            userDTO = userRPC.findByAppIdAndUserId(Long.valueOf(req.getAppId()),req.getUserId());
        }
        userEventService.solveUserEvent(userDTO, req);
        return returnResult;
    }


    /**
     * 已废弃
     * @param commonHeaderDTO
     * @return
     */
    @PostMapping(value = "userLog",produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    @ResponseBody
    public ReturnResult uploadUserInfo(@RequestBody CommonHeaderDTO commonHeaderDTO){
        ReturnResult returnResult = new ReturnResult();
        userEventLogService.saveUserLogByCommonHeader(commonHeaderDTO);
        return returnResult;
    }

    @RequestMapping(value = "userLogForCheckAuth",produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    @ResponseBody
    public ReturnResult userLogForCheckAuth(String userId,String appId){
        ReturnResult returnResult = new ReturnResult();
        userEventLogService.saveUserLogForCheckAuth(userId,appId);
        return returnResult;
    }

    @GetMapping(value = "queryAmount",produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    @ResponseBody
    public ReturnResult queryAmount(String logDay){
        ReturnResult returnResult = new ReturnResult();
        returnResult.setData(dailyResultService.queryDailyAmount(logDay));
        return returnResult;
    }

    @PostMapping(value = "checkUserListEvent",produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    @ResponseBody
    public ReturnResult checkUserListEvent(@RequestBody CheckUserLogReq checkUserLogReq){
        log.info("checkUserListEvent req = {}",JSONObject.toJSONString(checkUserLogReq));
        Long appId = AppDrawConf.appIdConvertMap.get(checkUserLogReq.getAppId());
        if(appId == null){
            appId = checkUserLogReq.getAppId();
        }
        ReturnResult returnResult = new ReturnResult();
        if (checkUserLogReq.getCountLog() == 0){
            returnResult.setData(0);
            return returnResult;
        }
        if(CollectionUtils.isEmpty(checkUserLogReq.getUserIdList())){
	        returnResult.setData(0);
        }else{
	        returnResult.setData(checkUserLogReq.getUserIdList().size());
        }
        if(CollectionUtils.isEmpty(checkUserLogReq.getUserIdList())){
            return returnResult;
        }
        try{

            Integer rs = hBaseUserEventCountService.filterUserMoreThanRedis(appId, checkUserLogReq.getUserIdList(), checkUserLogReq.getCountLog());
            if (useHBaseQuery) {
                returnResult.setData(checkUserLogReq.getUserIdList().size() - rs);
                return returnResult;
            }

            List<Map> maps = userEventCountMapper.listCountByUserAndApp(checkUserLogReq.getUserIdList(), appId, checkUserLogReq.getCountLog());
            if(!CollectionUtils.isEmpty(maps)) {
                log.info("checkUserListEvent rsp = {} ", JSONObject.toJSONString(maps));
                returnResult.setData(checkUserLogReq.getUserIdList().size() - maps.size());
            }
            log.info("ResultEx Rsp1= {} ,Rsp2= {}",checkUserLogReq.getUserIdList().size() - maps.size(),checkUserLogReq.getUserIdList().size() - rs);
        }catch (Exception e){
            log.error("checkUserListEvent exception ",e);
            returnResult.setData(0);
        }

        return returnResult;
    }

    @GetMapping(value = "queryTop40")
    public ReturnResult queryTopDevice40(){
        ReturnResult  returnResult = new ReturnResult();
        returnResult.setData(clickHouseService.queryTop40());
        return returnResult;
    }
}
