package com.coohua.user.event.api.controller;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.aliyun.openservices.ons.api.Message;
import com.aliyun.openservices.ons.api.bean.ProducerBean;
import com.aliyun.openservices.shade.com.google.common.base.Charsets;
import com.coohua.user.event.api.remote.UserAdEcpmRemote;
import com.coohua.user.event.biz.ap.service.BpApService;
import com.coohua.user.event.biz.click.entity.VideoAdReportEntitiy;
import com.coohua.user.event.biz.config.MqConfig;
import com.coohua.user.event.biz.core.dto.rsp.ValidResponse;
import com.coohua.user.event.biz.ecp.entity.AppKey;
import com.coohua.user.event.biz.ecp.service.AppKeyService;
import com.ctrip.framework.apollo.spring.annotation.ApolloJsonValue;
import com.google.common.hash.Hashing;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.sql.Timestamp;
import java.util.*;

/**
 * <AUTHOR>
 * @since 2021/2/9
 */
@Slf4j
@RestController
@RequestMapping("ad")
public class AdController {

    @Resource(name = "MqProducer")
    private ProducerBean producerBean;
    @Autowired
    private MqConfig mqConfig;
    @ApolloJsonValue("${old_tag_app_list}")
    private List<Integer> useOldTagAppList;

    @Autowired
    private BpApService bpApService;

    @Autowired
    private UserAdEcpmRemote userAdEcpmRemote;


    @RequestMapping(value = "checkNoReward/{product}/{userId}")
    public ValidResponse checkNoReward(@PathVariable Long userId, @PathVariable String product){
        return new ValidResponse(userAdEcpmRemote.checkNoReward(product, userId));
    }

    @RequestMapping(value = "point/{dsp}/{price}")
    public ValidResponse point(@PathVariable String dsp, @PathVariable String price,String trans_id,HttpServletRequest request){
        VideoAdReportEntitiy entity = new VideoAdReportEntitiy();
        entity.setDsp(dsp);
        entity.setPrice(price);
        Date now = new Date();
        entity.setCallTime(new Timestamp(now.getTime()));
        entity.setTimestamp(now.getTime());
        entity.setCkLogDay(new java.sql.Date(now.getTime()));
        entity.setUri(solveUrl(request));
        if(entity.getUri().contains("extra=&") || entity.getUri().contains("extrainfo=&")){
            return new ValidResponse();
        }
        String json = entity.getUri().substring( entity.getUri().indexOf("{"),entity.getUri().indexOf("}")+1);
        JSONObject jsonObject = JSONObject.parseObject(json);
        Integer appId = jsonObject.getInteger("appId");
        entity.setAppId(appId);
        entity.setUserId(jsonObject.getString("userId"));
        entity.setDeviceId(jsonObject.getString("deviceId") == null ? "null":jsonObject.getString("deviceId"));
        entity.setAd_id(jsonObject.getString("adId"));
        entity.setOs(jsonObject.getString("os"));
        entity.setChannel(jsonObject.getString("channel"));

        log.info(JSON.toJSONString(entity));

        if (bpApService.isSendToKafka(appId)){
            entity.setAdType(bpApService.getAdType(Integer.valueOf(entity.getAd_id())));
        }else {
            this.sendMessage(entity, appId);
        }

        if ("chuanshanjia".equals(entity.getDsp())){
            String key = AppKeyService.appKeyMap.getOrDefault(Long.valueOf(entity.getAd_id()),new AppKey()).getKeySr();
            if (StringUtils.isEmpty(key)){
                return new ValidResponse(Boolean.TRUE);
            }
            String sign = Hashing.sha256()
                    .newHasher()
                    .putString(String.format("%s:%s",trans_id,key), Charsets.UTF_8)
                    .hash()
                    .toString();
            return new ValidResponse(Boolean.TRUE,sign);
        }else {
            return new ValidResponse(Boolean.TRUE);
        }
    }

    private String solveUrl(HttpServletRequest request){
        StringBuilder stringBuilder = new StringBuilder();
        Set<String> keyset = request.getParameterMap().keySet();
        for (String key : keyset) {
            String[] values = request.getParameterValues(key);
            for (String value : values) {
                stringBuilder.append(key).append("=").append(value).append("&");
            }
        }

        return request.getRequestURI() + "?" + stringBuilder.toString();
    }

    private void sendMessage(Object o,Integer appId){
        String tag = mqConfig.getTag() + "_" + appId;
        if (useOldTagAppList.contains(appId)){
            tag = mqConfig.getTag();
        }
        Message msg = new Message(mqConfig.getTopic(),tag, JSON.toJSONString(o).getBytes());
        try {
            producerBean.send(msg);
        }catch (Exception e){
            log.error("SEND ERROR",e);
        }
    }

}
