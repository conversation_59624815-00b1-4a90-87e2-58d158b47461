package com.coohua.user.event.api.remote;

import com.alibaba.fastjson.JSONObject;
import com.coohua.user.event.api.remote.rpc.UserTfRpc;
import com.coohua.user.event.biz.click.entity.ProductEntity;
import com.coohua.user.event.biz.click.service.ClickHouseService;
import com.coohua.user.event.biz.service.TFUserService;
import com.coohua.user.event.biz.service.UserInfoQueryService;
import com.coohua.user.event.biz.service.bean.UserActive;
import com.coohua.user.event.biz.user.entity.UserMeta;
import com.coohua.user.event.biz.user.mapper.BpUserMapper;
import com.coohua.user.event.biz.util.AppConfig;
import com.coohua.user.event.biz.util.HBaseUtils;
import com.weibo.api.motan.config.springsupport.annotation.MotanService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.hadoop.hbase.client.Connection;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.Map;

@Slf4j
@Service
@MotanService(basicService = "user-eventBasicServiceConfigBean")
public class UserTfRemote implements UserTfRpc {

    @Autowired
    private TFUserService tfUserService;
    @Resource(name = "linDormConnection")
    private Connection ocpcHadoopConnection;
    @Autowired
    private ClickHouseService clickHouseService;
    @Resource
    private BpUserMapper bpUserMapper;

    @Override
    public String getDsp(Long appId, Long userId, String deviceId, String androidId, String imei, String oaid, String caid) {
        ProductEntity productEntity = AppConfig.appIdMap.get(appId);
        String dsp;
        if (tfUserService.isUseDeviceGui(productEntity.getProduct())) {
            UserActive userActive = tfUserService.queryUserDeviceGui(productEntity.getProduct(), String.valueOf(userId), appId);
            dsp = userActive.getSource();
        } else {
            Map<String,String> deviceMap = new HashMap<>();
            if(StringUtils.isNotBlank(deviceId)) {
                deviceMap.put("DEVICE_ID", deviceId);
            }
            if(StringUtils.isNotBlank(androidId)) {
                deviceMap.put("ANDROID_ID", androidId);
            }
            if(StringUtils.isNotBlank(imei)) {
                deviceMap.put("IMEI", imei);
            }
            if(StringUtils.isNotBlank(oaid)) {
                deviceMap.put("OAID", oaid);
            }
            if(StringUtils.isNotBlank(caid)) {
                deviceMap.put("CAID", caid);
            }

            if (deviceMap.isEmpty()){
                // 若没查到日志中的设备取埋点BackUp
                deviceMap = clickHouseService.queryDeviceIdOnlyThreeDays(productEntity.getProduct(),userId);
            }
            if (deviceMap.isEmpty()){
                UserMeta userMeta = bpUserMapper.selectByUserIdAndPkgId(userId,appId.intValue());
                deviceMap.put("DEVICE_ID", userMeta.getDeviceId());
            }
            // 查SDC-HBase
            String key = String.format("%s@%s",userId,productEntity.getProduct());
            byte[] result = HBaseUtils.searchDataFromHadoop(ocpcHadoopConnection, "userAcitveByUser", key);
            if (result != null){
                UserInfoQueryService.OcpcClick click = JSONObject.parseObject(result, UserInfoQueryService.OcpcClick.class);
                dsp = click.getSource();
            }else {
                dsp = "";
            }
            // 查 GUI_DSP
            dsp = (StringUtils.isNotBlank(dsp) ? (dsp + ",") : dsp) + UserInfoQueryService.userDsp(deviceMap,productEntity.getId(),productEntity.getProduct());
        }
        return dsp;
    }
}
