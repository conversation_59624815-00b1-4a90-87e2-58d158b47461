package com.coohua.user.event.api.remote;

import com.coohua.user.event.api.dto.UserRiskResponse;
import com.coohua.user.event.api.remote.rpc.UserClickRiskRpc;
import com.coohua.user.event.biz.ap.vo.UserRiskLimitVo;
import com.coohua.user.event.biz.ap.vo.UserRiskVo;
import com.coohua.user.event.biz.service.InnerPullService;
import com.coohua.user.event.biz.service.UserRiskService;
import com.weibo.api.motan.config.springsupport.annotation.MotanService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @since 2021/9/3
 */
@Service
@MotanService(basicService = "user-eventBasicServiceConfigBean")
public class UserRiskQueryRemote implements UserClickRiskRpc {

    @Autowired
    private UserRiskService userRiskService;
    @Autowired
    private InnerPullService innerPullService;

    @Override
    public UserRiskResponse queryUserRiskLevel(Integer appId, Long userId) {
        UserRiskVo result = userRiskService.queryUserRiskInfo(appId,userId);
        UserRiskLimitVo vo = userRiskService.queryUserLimit(appId,userId);
        UserRiskResponse response = new UserRiskResponse();
        response.setUserId(result.getUserId());
        response.setLevel(result.getLevel());
        response.setDesc(result.getDesc());

        response.setRestrictUser(vo.getRestrictUser());
        if (vo.getRestrictUser()){
            response.setWithdrawRate(vo.getWithdrawRate());
            response.setRewardRate(vo.getRewardRate());
            response.setVideoLimit(vo.getVideoLimit());
        }
        return response;
    }

    @Override
    public Boolean switchUserAd(Integer appId, Long userId, Boolean switchAd) {
        return userRiskService.switchUserAd(appId, userId, switchAd);
    }

    @Override
    public Boolean isInnerPullUser(Integer appId, Long userId) {
        return innerPullService.queryIsInnerUser(appId,userId);
    }
}
