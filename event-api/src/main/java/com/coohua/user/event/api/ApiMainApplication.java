package com.coohua.user.event.api;

import com.coohua.caf.core.apollo.EnableAutoChangeApolloConfig;
import com.ctrip.framework.apollo.spring.annotation.EnableApolloConfig;
import com.github.xiaoymin.swaggerbootstrapui.annotations.EnableSwaggerBootstrapUI;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.cache.annotation.EnableCaching;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.scheduling.annotation.EnableScheduling;
import springfox.documentation.swagger2.annotations.EnableSwagger2;
import top.zrbcool.pepper.boot.jediscluster.EnableJedisClusterClient;
import top.zrbcool.pepper.boot.motan.EnableMotan;


@EnableApolloConfig(value = {
        "application",
        "caf.log.level",
        "caf.base.registry",
        "ad.base.health",
        "bp.user.rpc.referer",
        "bp.mall.rpc.referer",
        "bp.account.rpc.referer",
        "user-event-sub-db",
        "ad.user.event.rpc.service",
        "ad.user-event.redis",
        "ap.redis.cluster",
        "bp.redis.user",
        "withdraw.rule.config"
})
@EnableAutoChangeApolloConfig
@SpringBootApplication
@EnableScheduling
@EnableCaching
@EnableSwagger2
@EnableSwaggerBootstrapUI
@EnableJedisClusterClient(namespace = "user-event")
@EnableJedisClusterClient(namespace = "ap-cluster")
@EnableJedisClusterClient(namespace = "bp-user")
@EnableMotan(namespace = "bp-user")
@EnableMotan(namespace = "bp-account")
@EnableMotan(namespace = "user-event")
@EnableMotan(namespace = "bp-mall")
@ComponentScan(basePackages = {"com.coohua.user.event.api","com.coohua.user.event.biz"})
public class ApiMainApplication {

    public static void main(String[] args) {
        SpringApplication.run(ApiMainApplication.class, args);
    }

}
