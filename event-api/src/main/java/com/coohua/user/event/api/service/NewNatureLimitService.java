package com.coohua.user.event.api.service;

import com.coohua.bp.mall.remote.api.MallRPC;
import com.coohua.bp.mall.remote.dto.WithdrawLimitConfDto;
import com.weibo.api.motan.config.springsupport.annotation.MotanReferer;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 新增 自然量 用户 提现限制 服务
 */
@Slf4j
@Service
public class NewNatureLimitService {

    //新增自然量用户当日提现额度上限
    private Map<Long,Integer> newNatureLimitMap;
    private Map<Long, Set<String>> channelLimitMap;
    @MotanReferer(basicReferer = "bp-mallBasicRefererConfigBean")
    private MallRPC mallRPC;

    @Scheduled(cron = "0 */3 * * * ? ")
    @PostConstruct
    public void initNewNatureLimitMap(){
        List<WithdrawLimitConfDto> confDtoList = mallRPC.queryWithdrawLimitList();
        Map<Long, Integer> res = confDtoList.stream()
                .filter(e -> {
                    return e.getNatureEveryLimitAmount() != null && e.getNatureEveryLimitAmount() > 0;
                })
                .collect(Collectors.toMap(WithdrawLimitConfDto::getAppId, WithdrawLimitConfDto::getNatureEveryLimitAmount, (r1, r2) -> r1));
        this.newNatureLimitMap = res;
        Map<Long, Set<String>> map = new HashMap<>();
        confDtoList.forEach(o -> {
            map.put(o.getAppId(), StringUtils.isNotBlank(o.getChannelLimit()) ? Arrays.stream(o.getChannelLimit().split(",")).collect(Collectors.toSet()) : new HashSet<>(0));
        });
        this.channelLimitMap = map;
    }

    public boolean check(Long appId, Integer totalAmount) {
        return newNatureLimitMap.containsKey(appId)
                        && totalAmount > newNatureLimitMap.get(appId);
    }

    /**
     * 是否为channel受限
     * @param appId
     * @param channel
     * @return
     */
    public boolean isChannelLimit(Long appId, String channel) {
        Set<String> channelSet = channelLimitMap.get(appId);
        if(CollectionUtils.isEmpty(channelSet)) {
            //没有配置渠道
            return false;
        }
        //有渠道配置，但是用户没有channel
        if(StringUtils.isBlank(channel)) {
            return true;
        }
        //配置的渠道不包含用户channel
        return !channelSet.contains(channel);
    }
}
