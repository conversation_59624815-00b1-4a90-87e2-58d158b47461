package com.coohua.user.event.api.remote;

import com.coohua.bp.user.remote.api.UserRPC;
import com.coohua.user.event.api.remote.rpc.UserGrayRpc;
import com.coohua.user.event.biz.service.UserGrayService;
import com.weibo.api.motan.config.springsupport.annotation.MotanReferer;
import com.weibo.api.motan.config.springsupport.annotation.MotanService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @since 2021/9/25
 */
@Service
@MotanService(basicService = "user-eventBasicServiceConfigBean")
public class UserGrayRemote implements UserGrayRpc {

    @MotanReferer(basicReferer = "bp-userBasicRefererConfigBean")
    private UserRPC userRPC;
    @Autowired
    private UserGrayService userGrayService;

    @Override
    public Integer signOutWithReason(Long appId, String accessKey, String remark) {
        userGrayService.sycnInsert(appId,remark,accessKey);
        return userRPC.signOutUser(appId,accessKey);
    }

}
