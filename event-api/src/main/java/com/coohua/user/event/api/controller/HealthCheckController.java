package com.coohua.user.event.api.controller;

import com.coohua.user.event.biz.core.dto.rsp.ReturnResult;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 * @since 2021/7/31
 */
@RestController
@RequestMapping("/health")
public class HealthCheckController {

    @GetMapping("info")
    public ReturnResult healthCheck(){
        return new ReturnResult();
    }

}
